{"version": 3, "sources": ["../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/preview/plugin.js", "../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/preview/index.js"], "sourcesContent": ["/**\n * TinyMCE version 6.6.2 (2023-08-09)\n */\n\n(function () {\n    'use strict';\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.Env');\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const option = name => editor => editor.options.get(name);\n    const getContentStyle = option('content_style');\n    const shouldUseContentCssCors = option('content_css_cors');\n    const getBodyClass = option('body_class');\n    const getBodyId = option('body_id');\n\n    const getPreviewHtml = editor => {\n      var _a;\n      let headHtml = '';\n      const encode = editor.dom.encode;\n      const contentStyle = (_a = getContentStyle(editor)) !== null && _a !== void 0 ? _a : '';\n      headHtml += '<base href=\"' + encode(editor.documentBaseURI.getURI()) + '\">';\n      const cors = shouldUseContentCssCors(editor) ? ' crossorigin=\"anonymous\"' : '';\n      global.each(editor.contentCSS, url => {\n        headHtml += '<link type=\"text/css\" rel=\"stylesheet\" href=\"' + encode(editor.documentBaseURI.toAbsolute(url)) + '\"' + cors + '>';\n      });\n      if (contentStyle) {\n        headHtml += '<style type=\"text/css\">' + contentStyle + '</style>';\n      }\n      const bodyId = getBodyId(editor);\n      const bodyClass = getBodyClass(editor);\n      const isMetaKeyPressed = global$1.os.isMacOS() || global$1.os.isiOS() ? 'e.metaKey' : 'e.ctrlKey && !e.altKey';\n      const preventClicksOnLinksScript = '<script>' + 'document.addEventListener && document.addEventListener(\"click\", function(e) {' + 'for (var elm = e.target; elm; elm = elm.parentNode) {' + 'if (elm.nodeName === \"A\" && !(' + isMetaKeyPressed + ')) {' + 'e.preventDefault();' + '}' + '}' + '}, false);' + '</script> ';\n      const directionality = editor.getBody().dir;\n      const dirAttr = directionality ? ' dir=\"' + encode(directionality) + '\"' : '';\n      const previewHtml = '<!DOCTYPE html>' + '<html>' + '<head>' + headHtml + '</head>' + '<body id=\"' + encode(bodyId) + '\" class=\"mce-content-body ' + encode(bodyClass) + '\"' + dirAttr + '>' + editor.getContent() + preventClicksOnLinksScript + '</body>' + '</html>';\n      return previewHtml;\n    };\n\n    const open = editor => {\n      const content = getPreviewHtml(editor);\n      const dataApi = editor.windowManager.open({\n        title: 'Preview',\n        size: 'large',\n        body: {\n          type: 'panel',\n          items: [{\n              name: 'preview',\n              type: 'iframe',\n              sandboxed: true,\n              transparent: false\n            }]\n        },\n        buttons: [{\n            type: 'cancel',\n            name: 'close',\n            text: 'Close',\n            primary: true\n          }],\n        initialData: { preview: content }\n      });\n      dataApi.focus('close');\n    };\n\n    const register$1 = editor => {\n      editor.addCommand('mcePreview', () => {\n        open(editor);\n      });\n    };\n\n    const register = editor => {\n      const onAction = () => editor.execCommand('mcePreview');\n      editor.ui.registry.addButton('preview', {\n        icon: 'preview',\n        tooltip: 'Preview',\n        onAction\n      });\n      editor.ui.registry.addMenuItem('preview', {\n        icon: 'preview',\n        text: 'Preview',\n        onAction\n      });\n    };\n\n    var Plugin = () => {\n      global$2.add('preview', editor => {\n        register$1(editor);\n        register(editor);\n      });\n    };\n\n    Plugin();\n\n})();\n", "// Exports the \"preview\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/preview')\n//   ES2015:\n//     import 'tinymce/plugins/preview'\nrequire('./plugin.js');"], "mappings": ";;;;;AAAA;AAAA;AAIA,KAAC,WAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,aAAa;AAEvD,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,oBAAoB;AAE5D,YAAM,SAAS,UAAQ,YAAU,OAAO,QAAQ,IAAI,IAAI;AACxD,YAAM,kBAAkB,OAAO,eAAe;AAC9C,YAAM,0BAA0B,OAAO,kBAAkB;AACzD,YAAM,eAAe,OAAO,YAAY;AACxC,YAAM,YAAY,OAAO,SAAS;AAElC,YAAM,iBAAiB,YAAU;AAC/B,YAAI;AACJ,YAAI,WAAW;AACf,cAAM,SAAS,OAAO,IAAI;AAC1B,cAAM,gBAAgB,KAAK,gBAAgB,MAAM,OAAO,QAAQ,OAAO,SAAS,KAAK;AACrF,oBAAY,iBAAiB,OAAO,OAAO,gBAAgB,OAAO,CAAC,IAAI;AACvE,cAAM,OAAO,wBAAwB,MAAM,IAAI,6BAA6B;AAC5E,eAAO,KAAK,OAAO,YAAY,SAAO;AACpC,sBAAY,kDAAkD,OAAO,OAAO,gBAAgB,WAAW,GAAG,CAAC,IAAI,MAAM,OAAO;AAAA,QAC9H,CAAC;AACD,YAAI,cAAc;AAChB,sBAAY,4BAA4B,eAAe;AAAA,QACzD;AACA,cAAM,SAAS,UAAU,MAAM;AAC/B,cAAM,YAAY,aAAa,MAAM;AACrC,cAAM,mBAAmB,SAAS,GAAG,QAAQ,KAAK,SAAS,GAAG,MAAM,IAAI,cAAc;AACtF,cAAM,6BAA6B,6KAA4L,mBAAmB;AAClP,cAAM,iBAAiB,OAAO,QAAQ,EAAE;AACxC,cAAM,UAAU,iBAAiB,WAAW,OAAO,cAAc,IAAI,MAAM;AAC3E,cAAM,cAAc,gCAA0C,WAAW,sBAA2B,OAAO,MAAM,IAAI,+BAA+B,OAAO,SAAS,IAAI,MAAM,UAAU,MAAM,OAAO,WAAW,IAAI,6BAA6B;AACjP,eAAO;AAAA,MACT;AAEA,YAAM,OAAO,YAAU;AACrB,cAAM,UAAU,eAAe,MAAM;AACrC,cAAM,UAAU,OAAO,cAAc,KAAK;AAAA,UACxC,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,YACJ,MAAM;AAAA,YACN,OAAO,CAAC;AAAA,cACJ,MAAM;AAAA,cACN,MAAM;AAAA,cACN,WAAW;AAAA,cACX,aAAa;AAAA,YACf,CAAC;AAAA,UACL;AAAA,UACA,SAAS,CAAC;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,UACX,CAAC;AAAA,UACH,aAAa,EAAE,SAAS,QAAQ;AAAA,QAClC,CAAC;AACD,gBAAQ,MAAM,OAAO;AAAA,MACvB;AAEA,YAAM,aAAa,YAAU;AAC3B,eAAO,WAAW,cAAc,MAAM;AACpC,eAAK,MAAM;AAAA,QACb,CAAC;AAAA,MACH;AAEA,YAAM,WAAW,YAAU;AACzB,cAAM,WAAW,MAAM,OAAO,YAAY,YAAY;AACtD,eAAO,GAAG,SAAS,UAAU,WAAW;AAAA,UACtC,MAAM;AAAA,UACN,SAAS;AAAA,UACT;AAAA,QACF,CAAC;AACD,eAAO,GAAG,SAAS,YAAY,WAAW;AAAA,UACxC,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA,QACF,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,MAAM;AACjB,iBAAS,IAAI,WAAW,YAAU;AAChC,qBAAW,MAAM;AACjB,mBAAS,MAAM;AAAA,QACjB,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IAEX,GAAG;AAAA;AAAA;;;AC1FH;", "names": []}