{"version": 3, "sources": ["../../.pnpm/cssfilter@0.0.10/node_modules/cssfilter/lib/default.js", "../../.pnpm/cssfilter@0.0.10/node_modules/cssfilter/lib/util.js", "../../.pnpm/cssfilter@0.0.10/node_modules/cssfilter/lib/parser.js", "../../.pnpm/cssfilter@0.0.10/node_modules/cssfilter/lib/css.js", "../../.pnpm/cssfilter@0.0.10/node_modules/cssfilter/lib/index.js", "../../.pnpm/xss@1.0.15/node_modules/xss/lib/util.js", "../../.pnpm/xss@1.0.15/node_modules/xss/lib/default.js", "../../.pnpm/xss@1.0.15/node_modules/xss/lib/parser.js", "../../.pnpm/xss@1.0.15/node_modules/xss/lib/xss.js", "../../.pnpm/xss@1.0.15/node_modules/xss/lib/index.js"], "sourcesContent": ["/**\n * cssfilter\n *\n * <AUTHOR>\n */\n\nfunction getDefaultWhiteList () {\n  // 白名单值说明：\n  // true: 允许该属性\n  // Function: function (val) { } 返回true表示允许该属性，其他值均表示不允许\n  // RegExp: regexp.test(val) 返回true表示允许该属性，其他值均表示不允许\n  // 除上面列出的值外均表示不允许\n  var whiteList = {};\n\n  whiteList['align-content'] = false; // default: auto\n  whiteList['align-items'] = false; // default: auto\n  whiteList['align-self'] = false; // default: auto\n  whiteList['alignment-adjust'] = false; // default: auto\n  whiteList['alignment-baseline'] = false; // default: baseline\n  whiteList['all'] = false; // default: depending on individual properties\n  whiteList['anchor-point'] = false; // default: none\n  whiteList['animation'] = false; // default: depending on individual properties\n  whiteList['animation-delay'] = false; // default: 0\n  whiteList['animation-direction'] = false; // default: normal\n  whiteList['animation-duration'] = false; // default: 0\n  whiteList['animation-fill-mode'] = false; // default: none\n  whiteList['animation-iteration-count'] = false; // default: 1\n  whiteList['animation-name'] = false; // default: none\n  whiteList['animation-play-state'] = false; // default: running\n  whiteList['animation-timing-function'] = false; // default: ease\n  whiteList['azimuth'] = false; // default: center\n  whiteList['backface-visibility'] = false; // default: visible\n  whiteList['background'] = true; // default: depending on individual properties\n  whiteList['background-attachment'] = true; // default: scroll\n  whiteList['background-clip'] = true; // default: border-box\n  whiteList['background-color'] = true; // default: transparent\n  whiteList['background-image'] = true; // default: none\n  whiteList['background-origin'] = true; // default: padding-box\n  whiteList['background-position'] = true; // default: 0% 0%\n  whiteList['background-repeat'] = true; // default: repeat\n  whiteList['background-size'] = true; // default: auto\n  whiteList['baseline-shift'] = false; // default: baseline\n  whiteList['binding'] = false; // default: none\n  whiteList['bleed'] = false; // default: 6pt\n  whiteList['bookmark-label'] = false; // default: content()\n  whiteList['bookmark-level'] = false; // default: none\n  whiteList['bookmark-state'] = false; // default: open\n  whiteList['border'] = true; // default: depending on individual properties\n  whiteList['border-bottom'] = true; // default: depending on individual properties\n  whiteList['border-bottom-color'] = true; // default: current color\n  whiteList['border-bottom-left-radius'] = true; // default: 0\n  whiteList['border-bottom-right-radius'] = true; // default: 0\n  whiteList['border-bottom-style'] = true; // default: none\n  whiteList['border-bottom-width'] = true; // default: medium\n  whiteList['border-collapse'] = true; // default: separate\n  whiteList['border-color'] = true; // default: depending on individual properties\n  whiteList['border-image'] = true; // default: none\n  whiteList['border-image-outset'] = true; // default: 0\n  whiteList['border-image-repeat'] = true; // default: stretch\n  whiteList['border-image-slice'] = true; // default: 100%\n  whiteList['border-image-source'] = true; // default: none\n  whiteList['border-image-width'] = true; // default: 1\n  whiteList['border-left'] = true; // default: depending on individual properties\n  whiteList['border-left-color'] = true; // default: current color\n  whiteList['border-left-style'] = true; // default: none\n  whiteList['border-left-width'] = true; // default: medium\n  whiteList['border-radius'] = true; // default: 0\n  whiteList['border-right'] = true; // default: depending on individual properties\n  whiteList['border-right-color'] = true; // default: current color\n  whiteList['border-right-style'] = true; // default: none\n  whiteList['border-right-width'] = true; // default: medium\n  whiteList['border-spacing'] = true; // default: 0\n  whiteList['border-style'] = true; // default: depending on individual properties\n  whiteList['border-top'] = true; // default: depending on individual properties\n  whiteList['border-top-color'] = true; // default: current color\n  whiteList['border-top-left-radius'] = true; // default: 0\n  whiteList['border-top-right-radius'] = true; // default: 0\n  whiteList['border-top-style'] = true; // default: none\n  whiteList['border-top-width'] = true; // default: medium\n  whiteList['border-width'] = true; // default: depending on individual properties\n  whiteList['bottom'] = false; // default: auto\n  whiteList['box-decoration-break'] = true; // default: slice\n  whiteList['box-shadow'] = true; // default: none\n  whiteList['box-sizing'] = true; // default: content-box\n  whiteList['box-snap'] = true; // default: none\n  whiteList['box-suppress'] = true; // default: show\n  whiteList['break-after'] = true; // default: auto\n  whiteList['break-before'] = true; // default: auto\n  whiteList['break-inside'] = true; // default: auto\n  whiteList['caption-side'] = false; // default: top\n  whiteList['chains'] = false; // default: none\n  whiteList['clear'] = true; // default: none\n  whiteList['clip'] = false; // default: auto\n  whiteList['clip-path'] = false; // default: none\n  whiteList['clip-rule'] = false; // default: nonzero\n  whiteList['color'] = true; // default: implementation dependent\n  whiteList['color-interpolation-filters'] = true; // default: auto\n  whiteList['column-count'] = false; // default: auto\n  whiteList['column-fill'] = false; // default: balance\n  whiteList['column-gap'] = false; // default: normal\n  whiteList['column-rule'] = false; // default: depending on individual properties\n  whiteList['column-rule-color'] = false; // default: current color\n  whiteList['column-rule-style'] = false; // default: medium\n  whiteList['column-rule-width'] = false; // default: medium\n  whiteList['column-span'] = false; // default: none\n  whiteList['column-width'] = false; // default: auto\n  whiteList['columns'] = false; // default: depending on individual properties\n  whiteList['contain'] = false; // default: none\n  whiteList['content'] = false; // default: normal\n  whiteList['counter-increment'] = false; // default: none\n  whiteList['counter-reset'] = false; // default: none\n  whiteList['counter-set'] = false; // default: none\n  whiteList['crop'] = false; // default: auto\n  whiteList['cue'] = false; // default: depending on individual properties\n  whiteList['cue-after'] = false; // default: none\n  whiteList['cue-before'] = false; // default: none\n  whiteList['cursor'] = false; // default: auto\n  whiteList['direction'] = false; // default: ltr\n  whiteList['display'] = true; // default: depending on individual properties\n  whiteList['display-inside'] = true; // default: auto\n  whiteList['display-list'] = true; // default: none\n  whiteList['display-outside'] = true; // default: inline-level\n  whiteList['dominant-baseline'] = false; // default: auto\n  whiteList['elevation'] = false; // default: level\n  whiteList['empty-cells'] = false; // default: show\n  whiteList['filter'] = false; // default: none\n  whiteList['flex'] = false; // default: depending on individual properties\n  whiteList['flex-basis'] = false; // default: auto\n  whiteList['flex-direction'] = false; // default: row\n  whiteList['flex-flow'] = false; // default: depending on individual properties\n  whiteList['flex-grow'] = false; // default: 0\n  whiteList['flex-shrink'] = false; // default: 1\n  whiteList['flex-wrap'] = false; // default: nowrap\n  whiteList['float'] = false; // default: none\n  whiteList['float-offset'] = false; // default: 0 0\n  whiteList['flood-color'] = false; // default: black\n  whiteList['flood-opacity'] = false; // default: 1\n  whiteList['flow-from'] = false; // default: none\n  whiteList['flow-into'] = false; // default: none\n  whiteList['font'] = true; // default: depending on individual properties\n  whiteList['font-family'] = true; // default: implementation dependent\n  whiteList['font-feature-settings'] = true; // default: normal\n  whiteList['font-kerning'] = true; // default: auto\n  whiteList['font-language-override'] = true; // default: normal\n  whiteList['font-size'] = true; // default: medium\n  whiteList['font-size-adjust'] = true; // default: none\n  whiteList['font-stretch'] = true; // default: normal\n  whiteList['font-style'] = true; // default: normal\n  whiteList['font-synthesis'] = true; // default: weight style\n  whiteList['font-variant'] = true; // default: normal\n  whiteList['font-variant-alternates'] = true; // default: normal\n  whiteList['font-variant-caps'] = true; // default: normal\n  whiteList['font-variant-east-asian'] = true; // default: normal\n  whiteList['font-variant-ligatures'] = true; // default: normal\n  whiteList['font-variant-numeric'] = true; // default: normal\n  whiteList['font-variant-position'] = true; // default: normal\n  whiteList['font-weight'] = true; // default: normal\n  whiteList['grid'] = false; // default: depending on individual properties\n  whiteList['grid-area'] = false; // default: depending on individual properties\n  whiteList['grid-auto-columns'] = false; // default: auto\n  whiteList['grid-auto-flow'] = false; // default: none\n  whiteList['grid-auto-rows'] = false; // default: auto\n  whiteList['grid-column'] = false; // default: depending on individual properties\n  whiteList['grid-column-end'] = false; // default: auto\n  whiteList['grid-column-start'] = false; // default: auto\n  whiteList['grid-row'] = false; // default: depending on individual properties\n  whiteList['grid-row-end'] = false; // default: auto\n  whiteList['grid-row-start'] = false; // default: auto\n  whiteList['grid-template'] = false; // default: depending on individual properties\n  whiteList['grid-template-areas'] = false; // default: none\n  whiteList['grid-template-columns'] = false; // default: none\n  whiteList['grid-template-rows'] = false; // default: none\n  whiteList['hanging-punctuation'] = false; // default: none\n  whiteList['height'] = true; // default: auto\n  whiteList['hyphens'] = false; // default: manual\n  whiteList['icon'] = false; // default: auto\n  whiteList['image-orientation'] = false; // default: auto\n  whiteList['image-resolution'] = false; // default: normal\n  whiteList['ime-mode'] = false; // default: auto\n  whiteList['initial-letters'] = false; // default: normal\n  whiteList['inline-box-align'] = false; // default: last\n  whiteList['justify-content'] = false; // default: auto\n  whiteList['justify-items'] = false; // default: auto\n  whiteList['justify-self'] = false; // default: auto\n  whiteList['left'] = false; // default: auto\n  whiteList['letter-spacing'] = true; // default: normal\n  whiteList['lighting-color'] = true; // default: white\n  whiteList['line-box-contain'] = false; // default: block inline replaced\n  whiteList['line-break'] = false; // default: auto\n  whiteList['line-grid'] = false; // default: match-parent\n  whiteList['line-height'] = false; // default: normal\n  whiteList['line-snap'] = false; // default: none\n  whiteList['line-stacking'] = false; // default: depending on individual properties\n  whiteList['line-stacking-ruby'] = false; // default: exclude-ruby\n  whiteList['line-stacking-shift'] = false; // default: consider-shifts\n  whiteList['line-stacking-strategy'] = false; // default: inline-line-height\n  whiteList['list-style'] = true; // default: depending on individual properties\n  whiteList['list-style-image'] = true; // default: none\n  whiteList['list-style-position'] = true; // default: outside\n  whiteList['list-style-type'] = true; // default: disc\n  whiteList['margin'] = true; // default: depending on individual properties\n  whiteList['margin-bottom'] = true; // default: 0\n  whiteList['margin-left'] = true; // default: 0\n  whiteList['margin-right'] = true; // default: 0\n  whiteList['margin-top'] = true; // default: 0\n  whiteList['marker-offset'] = false; // default: auto\n  whiteList['marker-side'] = false; // default: list-item\n  whiteList['marks'] = false; // default: none\n  whiteList['mask'] = false; // default: border-box\n  whiteList['mask-box'] = false; // default: see individual properties\n  whiteList['mask-box-outset'] = false; // default: 0\n  whiteList['mask-box-repeat'] = false; // default: stretch\n  whiteList['mask-box-slice'] = false; // default: 0 fill\n  whiteList['mask-box-source'] = false; // default: none\n  whiteList['mask-box-width'] = false; // default: auto\n  whiteList['mask-clip'] = false; // default: border-box\n  whiteList['mask-image'] = false; // default: none\n  whiteList['mask-origin'] = false; // default: border-box\n  whiteList['mask-position'] = false; // default: center\n  whiteList['mask-repeat'] = false; // default: no-repeat\n  whiteList['mask-size'] = false; // default: border-box\n  whiteList['mask-source-type'] = false; // default: auto\n  whiteList['mask-type'] = false; // default: luminance\n  whiteList['max-height'] = true; // default: none\n  whiteList['max-lines'] = false; // default: none\n  whiteList['max-width'] = true; // default: none\n  whiteList['min-height'] = true; // default: 0\n  whiteList['min-width'] = true; // default: 0\n  whiteList['move-to'] = false; // default: normal\n  whiteList['nav-down'] = false; // default: auto\n  whiteList['nav-index'] = false; // default: auto\n  whiteList['nav-left'] = false; // default: auto\n  whiteList['nav-right'] = false; // default: auto\n  whiteList['nav-up'] = false; // default: auto\n  whiteList['object-fit'] = false; // default: fill\n  whiteList['object-position'] = false; // default: 50% 50%\n  whiteList['opacity'] = false; // default: 1\n  whiteList['order'] = false; // default: 0\n  whiteList['orphans'] = false; // default: 2\n  whiteList['outline'] = false; // default: depending on individual properties\n  whiteList['outline-color'] = false; // default: invert\n  whiteList['outline-offset'] = false; // default: 0\n  whiteList['outline-style'] = false; // default: none\n  whiteList['outline-width'] = false; // default: medium\n  whiteList['overflow'] = false; // default: depending on individual properties\n  whiteList['overflow-wrap'] = false; // default: normal\n  whiteList['overflow-x'] = false; // default: visible\n  whiteList['overflow-y'] = false; // default: visible\n  whiteList['padding'] = true; // default: depending on individual properties\n  whiteList['padding-bottom'] = true; // default: 0\n  whiteList['padding-left'] = true; // default: 0\n  whiteList['padding-right'] = true; // default: 0\n  whiteList['padding-top'] = true; // default: 0\n  whiteList['page'] = false; // default: auto\n  whiteList['page-break-after'] = false; // default: auto\n  whiteList['page-break-before'] = false; // default: auto\n  whiteList['page-break-inside'] = false; // default: auto\n  whiteList['page-policy'] = false; // default: start\n  whiteList['pause'] = false; // default: implementation dependent\n  whiteList['pause-after'] = false; // default: implementation dependent\n  whiteList['pause-before'] = false; // default: implementation dependent\n  whiteList['perspective'] = false; // default: none\n  whiteList['perspective-origin'] = false; // default: 50% 50%\n  whiteList['pitch'] = false; // default: medium\n  whiteList['pitch-range'] = false; // default: 50\n  whiteList['play-during'] = false; // default: auto\n  whiteList['position'] = false; // default: static\n  whiteList['presentation-level'] = false; // default: 0\n  whiteList['quotes'] = false; // default: text\n  whiteList['region-fragment'] = false; // default: auto\n  whiteList['resize'] = false; // default: none\n  whiteList['rest'] = false; // default: depending on individual properties\n  whiteList['rest-after'] = false; // default: none\n  whiteList['rest-before'] = false; // default: none\n  whiteList['richness'] = false; // default: 50\n  whiteList['right'] = false; // default: auto\n  whiteList['rotation'] = false; // default: 0\n  whiteList['rotation-point'] = false; // default: 50% 50%\n  whiteList['ruby-align'] = false; // default: auto\n  whiteList['ruby-merge'] = false; // default: separate\n  whiteList['ruby-position'] = false; // default: before\n  whiteList['shape-image-threshold'] = false; // default: 0.0\n  whiteList['shape-outside'] = false; // default: none\n  whiteList['shape-margin'] = false; // default: 0\n  whiteList['size'] = false; // default: auto\n  whiteList['speak'] = false; // default: auto\n  whiteList['speak-as'] = false; // default: normal\n  whiteList['speak-header'] = false; // default: once\n  whiteList['speak-numeral'] = false; // default: continuous\n  whiteList['speak-punctuation'] = false; // default: none\n  whiteList['speech-rate'] = false; // default: medium\n  whiteList['stress'] = false; // default: 50\n  whiteList['string-set'] = false; // default: none\n  whiteList['tab-size'] = false; // default: 8\n  whiteList['table-layout'] = false; // default: auto\n  whiteList['text-align'] = true; // default: start\n  whiteList['text-align-last'] = true; // default: auto\n  whiteList['text-combine-upright'] = true; // default: none\n  whiteList['text-decoration'] = true; // default: none\n  whiteList['text-decoration-color'] = true; // default: currentColor\n  whiteList['text-decoration-line'] = true; // default: none\n  whiteList['text-decoration-skip'] = true; // default: objects\n  whiteList['text-decoration-style'] = true; // default: solid\n  whiteList['text-emphasis'] = true; // default: depending on individual properties\n  whiteList['text-emphasis-color'] = true; // default: currentColor\n  whiteList['text-emphasis-position'] = true; // default: over right\n  whiteList['text-emphasis-style'] = true; // default: none\n  whiteList['text-height'] = true; // default: auto\n  whiteList['text-indent'] = true; // default: 0\n  whiteList['text-justify'] = true; // default: auto\n  whiteList['text-orientation'] = true; // default: mixed\n  whiteList['text-overflow'] = true; // default: clip\n  whiteList['text-shadow'] = true; // default: none\n  whiteList['text-space-collapse'] = true; // default: collapse\n  whiteList['text-transform'] = true; // default: none\n  whiteList['text-underline-position'] = true; // default: auto\n  whiteList['text-wrap'] = true; // default: normal\n  whiteList['top'] = false; // default: auto\n  whiteList['transform'] = false; // default: none\n  whiteList['transform-origin'] = false; // default: 50% 50% 0\n  whiteList['transform-style'] = false; // default: flat\n  whiteList['transition'] = false; // default: depending on individual properties\n  whiteList['transition-delay'] = false; // default: 0s\n  whiteList['transition-duration'] = false; // default: 0s\n  whiteList['transition-property'] = false; // default: all\n  whiteList['transition-timing-function'] = false; // default: ease\n  whiteList['unicode-bidi'] = false; // default: normal\n  whiteList['vertical-align'] = false; // default: baseline\n  whiteList['visibility'] = false; // default: visible\n  whiteList['voice-balance'] = false; // default: center\n  whiteList['voice-duration'] = false; // default: auto\n  whiteList['voice-family'] = false; // default: implementation dependent\n  whiteList['voice-pitch'] = false; // default: medium\n  whiteList['voice-range'] = false; // default: medium\n  whiteList['voice-rate'] = false; // default: normal\n  whiteList['voice-stress'] = false; // default: normal\n  whiteList['voice-volume'] = false; // default: medium\n  whiteList['volume'] = false; // default: medium\n  whiteList['white-space'] = false; // default: normal\n  whiteList['widows'] = false; // default: 2\n  whiteList['width'] = true; // default: auto\n  whiteList['will-change'] = false; // default: auto\n  whiteList['word-break'] = true; // default: normal\n  whiteList['word-spacing'] = true; // default: normal\n  whiteList['word-wrap'] = true; // default: normal\n  whiteList['wrap-flow'] = false; // default: auto\n  whiteList['wrap-through'] = false; // default: wrap\n  whiteList['writing-mode'] = false; // default: horizontal-tb\n  whiteList['z-index'] = false; // default: auto\n\n  return whiteList;\n}\n\n\n/**\n * 匹配到白名单上的一个属性时\n *\n * @param {String} name\n * @param {String} value\n * @param {Object} options\n * @return {String}\n */\nfunction onAttr (name, value, options) {\n  // do nothing\n}\n\n/**\n * 匹配到不在白名单上的一个属性时\n *\n * @param {String} name\n * @param {String} value\n * @param {Object} options\n * @return {String}\n */\nfunction onIgnoreAttr (name, value, options) {\n  // do nothing\n}\n\nvar REGEXP_URL_JAVASCRIPT = /javascript\\s*\\:/img;\n\n/**\n * 过滤属性值\n *\n * @param {String} name\n * @param {String} value\n * @return {String}\n */\nfunction safeAttrValue(name, value) {\n  if (REGEXP_URL_JAVASCRIPT.test(value)) return '';\n  return value;\n}\n\n\nexports.whiteList = getDefaultWhiteList();\nexports.getDefaultWhiteList = getDefaultWhiteList;\nexports.onAttr = onAttr;\nexports.onIgnoreAttr = onIgnoreAttr;\nexports.safeAttrValue = safeAttrValue;\n", "module.exports = {\n  indexOf: function (arr, item) {\n    var i, j;\n    if (Array.prototype.indexOf) {\n      return arr.indexOf(item);\n    }\n    for (i = 0, j = arr.length; i < j; i++) {\n      if (arr[i] === item) {\n        return i;\n      }\n    }\n    return -1;\n  },\n  forEach: function (arr, fn, scope) {\n    var i, j;\n    if (Array.prototype.forEach) {\n      return arr.forEach(fn, scope);\n    }\n    for (i = 0, j = arr.length; i < j; i++) {\n      fn.call(scope, arr[i], i, arr);\n    }\n  },\n  trim: function (str) {\n    if (String.prototype.trim) {\n      return str.trim();\n    }\n    return str.replace(/(^\\s*)|(\\s*$)/g, '');\n  },\n  trimRight: function (str) {\n    if (String.prototype.trimRight) {\n      return str.trimRight();\n    }\n    return str.replace(/(\\s*$)/g, '');\n  }\n};\n", "/**\n * cssfilter\n *\n * <AUTHOR>\n */\n\nvar _ = require('./util');\n\n\n/**\n * 解析style\n *\n * @param {String} css\n * @param {Function} onAttr 处理属性的函数\n *   参数格式： function (sourcePosition, position, name, value, source)\n * @return {String}\n */\nfunction parseStyle (css, onAttr) {\n  css = _.trimRight(css);\n  if (css[css.length - 1] !== ';') css += ';';\n  var cssLength = css.length;\n  var isParenthesisOpen = false;\n  var lastPos = 0;\n  var i = 0;\n  var retCSS = '';\n\n  function addNewAttr () {\n    // 如果没有正常的闭合圆括号，则直接忽略当前属性\n    if (!isParenthesisOpen) {\n      var source = _.trim(css.slice(lastPos, i));\n      var j = source.indexOf(':');\n      if (j !== -1) {\n        var name = _.trim(source.slice(0, j));\n        var value = _.trim(source.slice(j + 1));\n        // 必须有属性名称\n        if (name) {\n          var ret = onAttr(lastPos, retCSS.length, name, value, source);\n          if (ret) retCSS += ret + '; ';\n        }\n      }\n    }\n    lastPos = i + 1;\n  }\n\n  for (; i < cssLength; i++) {\n    var c = css[i];\n    if (c === '/' && css[i + 1] === '*') {\n      // 备注开始\n      var j = css.indexOf('*/', i + 2);\n      // 如果没有正常的备注结束，则后面的部分全部跳过\n      if (j === -1) break;\n      // 直接将当前位置调到备注结尾，并且初始化状态\n      i = j + 1;\n      lastPos = i + 1;\n      isParenthesisOpen = false;\n    } else if (c === '(') {\n      isParenthesisOpen = true;\n    } else if (c === ')') {\n      isParenthesisOpen = false;\n    } else if (c === ';') {\n      if (isParenthesisOpen) {\n        // 在圆括号里面，忽略\n      } else {\n        addNewAttr();\n      }\n    } else if (c === '\\n') {\n      addNewAttr();\n    }\n  }\n\n  return _.trim(retCSS);\n}\n\nmodule.exports = parseStyle;\n", "/**\n * cssfilter\n *\n * <AUTHOR>\n */\n\nvar DEFAULT = require('./default');\nvar parseStyle = require('./parser');\nvar _ = require('./util');\n\n\n/**\n * 返回值是否为空\n *\n * @param {Object} obj\n * @return {Boolean}\n */\nfunction isNull (obj) {\n  return (obj === undefined || obj === null);\n}\n\n/**\n * 浅拷贝对象\n *\n * @param {Object} obj\n * @return {Object}\n */\nfunction shallowCopyObject (obj) {\n  var ret = {};\n  for (var i in obj) {\n    ret[i] = obj[i];\n  }\n  return ret;\n}\n\n/**\n * 创建CSS过滤器\n *\n * @param {Object} options\n *   - {Object} whiteList\n *   - {Function} onAttr\n *   - {Function} onIgnoreAttr\n *   - {Function} safeAttrValue\n */\nfunction FilterCSS (options) {\n  options = shallowCopyObject(options || {});\n  options.whiteList = options.whiteList || DEFAULT.whiteList;\n  options.onAttr = options.onAttr || DEFAULT.onAttr;\n  options.onIgnoreAttr = options.onIgnoreAttr || DEFAULT.onIgnoreAttr;\n  options.safeAttrValue = options.safeAttrValue || DEFAULT.safeAttrValue;\n  this.options = options;\n}\n\nFilterCSS.prototype.process = function (css) {\n  // 兼容各种奇葩输入\n  css = css || '';\n  css = css.toString();\n  if (!css) return '';\n\n  var me = this;\n  var options = me.options;\n  var whiteList = options.whiteList;\n  var onAttr = options.onAttr;\n  var onIgnoreAttr = options.onIgnoreAttr;\n  var safeAttrValue = options.safeAttrValue;\n\n  var retCSS = parseStyle(css, function (sourcePosition, position, name, value, source) {\n\n    var check = whiteList[name];\n    var isWhite = false;\n    if (check === true) isWhite = check;\n    else if (typeof check === 'function') isWhite = check(value);\n    else if (check instanceof RegExp) isWhite = check.test(value);\n    if (isWhite !== true) isWhite = false;\n\n    // 如果过滤后 value 为空则直接忽略\n    value = safeAttrValue(name, value);\n    if (!value) return;\n\n    var opts = {\n      position: position,\n      sourcePosition: sourcePosition,\n      source: source,\n      isWhite: isWhite\n    };\n\n    if (isWhite) {\n\n      var ret = onAttr(name, value, opts);\n      if (isNull(ret)) {\n        return name + ':' + value;\n      } else {\n        return ret;\n      }\n\n    } else {\n\n      var ret = onIgnoreAttr(name, value, opts);\n      if (!isNull(ret)) {\n        return ret;\n      }\n\n    }\n  });\n\n  return retCSS;\n};\n\n\nmodule.exports = FilterCSS;\n", "/**\n * cssfilter\n *\n * <AUTHOR>\n */\n\nvar DEFAULT = require('./default');\nvar FilterCSS = require('./css');\n\n\n/**\n * XSS过滤\n *\n * @param {String} css 要过滤的CSS代码\n * @param {Object} options 选项：whiteList, onAttr, onIgnoreAttr\n * @return {String}\n */\nfunction filterCSS (html, options) {\n  var xss = new FilterCSS(options);\n  return xss.process(html);\n}\n\n\n// 输出\nexports = module.exports = filterCSS;\nexports.FilterCSS = FilterCSS;\nfor (var i in DEFAULT) exports[i] = DEFAULT[i];\n\n// 在浏览器端使用\nif (typeof window !== 'undefined') {\n  window.filterCSS = module.exports;\n}\n", "module.exports = {\n  indexOf: function (arr, item) {\n    var i, j;\n    if (Array.prototype.indexOf) {\n      return arr.indexOf(item);\n    }\n    for (i = 0, j = arr.length; i < j; i++) {\n      if (arr[i] === item) {\n        return i;\n      }\n    }\n    return -1;\n  },\n  forEach: function (arr, fn, scope) {\n    var i, j;\n    if (Array.prototype.forEach) {\n      return arr.forEach(fn, scope);\n    }\n    for (i = 0, j = arr.length; i < j; i++) {\n      fn.call(scope, arr[i], i, arr);\n    }\n  },\n  trim: function (str) {\n    if (String.prototype.trim) {\n      return str.trim();\n    }\n    return str.replace(/(^\\s*)|(\\s*$)/g, \"\");\n  },\n  spaceIndex: function (str) {\n    var reg = /\\s|\\n|\\t/;\n    var match = reg.exec(str);\n    return match ? match.index : -1;\n  },\n};\n", "/**\n * default settings\n *\n * <AUTHOR>\n */\n\nvar FilterCSS = require(\"cssfilter\").FilterCSS;\nvar getDefaultCSSWhiteList = require(\"cssfilter\").getDefaultWhiteList;\nvar _ = require(\"./util\");\n\nfunction getDefaultWhiteList() {\n  return {\n    a: [\"target\", \"href\", \"title\"],\n    abbr: [\"title\"],\n    address: [],\n    area: [\"shape\", \"coords\", \"href\", \"alt\"],\n    article: [],\n    aside: [],\n    audio: [\n      \"autoplay\",\n      \"controls\",\n      \"crossorigin\",\n      \"loop\",\n      \"muted\",\n      \"preload\",\n      \"src\",\n    ],\n    b: [],\n    bdi: [\"dir\"],\n    bdo: [\"dir\"],\n    big: [],\n    blockquote: [\"cite\"],\n    br: [],\n    caption: [],\n    center: [],\n    cite: [],\n    code: [],\n    col: [\"align\", \"valign\", \"span\", \"width\"],\n    colgroup: [\"align\", \"valign\", \"span\", \"width\"],\n    dd: [],\n    del: [\"datetime\"],\n    details: [\"open\"],\n    div: [],\n    dl: [],\n    dt: [],\n    em: [],\n    figcaption: [],\n    figure: [],\n    font: [\"color\", \"size\", \"face\"],\n    footer: [],\n    h1: [],\n    h2: [],\n    h3: [],\n    h4: [],\n    h5: [],\n    h6: [],\n    header: [],\n    hr: [],\n    i: [],\n    img: [\"src\", \"alt\", \"title\", \"width\", \"height\", \"loading\"],\n    ins: [\"datetime\"],\n    kbd: [],\n    li: [],\n    mark: [],\n    nav: [],\n    ol: [],\n    p: [],\n    pre: [],\n    s: [],\n    section: [],\n    small: [],\n    span: [],\n    sub: [],\n    summary: [],\n    sup: [],\n    strong: [],\n    strike: [],\n    table: [\"width\", \"border\", \"align\", \"valign\"],\n    tbody: [\"align\", \"valign\"],\n    td: [\"width\", \"rowspan\", \"colspan\", \"align\", \"valign\"],\n    tfoot: [\"align\", \"valign\"],\n    th: [\"width\", \"rowspan\", \"colspan\", \"align\", \"valign\"],\n    thead: [\"align\", \"valign\"],\n    tr: [\"rowspan\", \"align\", \"valign\"],\n    tt: [],\n    u: [],\n    ul: [],\n    video: [\n      \"autoplay\",\n      \"controls\",\n      \"crossorigin\",\n      \"loop\",\n      \"muted\",\n      \"playsinline\",\n      \"poster\",\n      \"preload\",\n      \"src\",\n      \"height\",\n      \"width\",\n    ],\n  };\n}\n\nvar defaultCSSFilter = new FilterCSS();\n\n/**\n * default onTag function\n *\n * @param {String} tag\n * @param {String} html\n * @param {Object} options\n * @return {String}\n */\nfunction onTag(tag, html, options) {\n  // do nothing\n}\n\n/**\n * default onIgnoreTag function\n *\n * @param {String} tag\n * @param {String} html\n * @param {Object} options\n * @return {String}\n */\nfunction onIgnoreTag(tag, html, options) {\n  // do nothing\n}\n\n/**\n * default onTagAttr function\n *\n * @param {String} tag\n * @param {String} name\n * @param {String} value\n * @return {String}\n */\nfunction onTagAttr(tag, name, value) {\n  // do nothing\n}\n\n/**\n * default onIgnoreTagAttr function\n *\n * @param {String} tag\n * @param {String} name\n * @param {String} value\n * @return {String}\n */\nfunction onIgnoreTagAttr(tag, name, value) {\n  // do nothing\n}\n\n/**\n * default escapeHtml function\n *\n * @param {String} html\n */\nfunction escapeHtml(html) {\n  return html.replace(REGEXP_LT, \"&lt;\").replace(REGEXP_GT, \"&gt;\");\n}\n\n/**\n * default safeAttrValue function\n *\n * @param {String} tag\n * @param {String} name\n * @param {String} value\n * @param {Object} cssFilter\n * @return {String}\n */\nfunction safeAttrValue(tag, name, value, cssFilter) {\n  // unescape attribute value firstly\n  value = friendlyAttrValue(value);\n\n  if (name === \"href\" || name === \"src\") {\n    // filter `href` and `src` attribute\n    // only allow the value that starts with `http://` | `https://` | `mailto:` | `/` | `#`\n    value = _.trim(value);\n    if (value === \"#\") return \"#\";\n    if (\n      !(\n        value.substr(0, 7) === \"http://\" ||\n        value.substr(0, 8) === \"https://\" ||\n        value.substr(0, 7) === \"mailto:\" ||\n        value.substr(0, 4) === \"tel:\" ||\n        value.substr(0, 11) === \"data:image/\" ||\n        value.substr(0, 6) === \"ftp://\" ||\n        value.substr(0, 2) === \"./\" ||\n        value.substr(0, 3) === \"../\" ||\n        value[0] === \"#\" ||\n        value[0] === \"/\"\n      )\n    ) {\n      return \"\";\n    }\n  } else if (name === \"background\") {\n    // filter `background` attribute (maybe no use)\n    // `javascript:`\n    REGEXP_DEFAULT_ON_TAG_ATTR_4.lastIndex = 0;\n    if (REGEXP_DEFAULT_ON_TAG_ATTR_4.test(value)) {\n      return \"\";\n    }\n  } else if (name === \"style\") {\n    // `expression()`\n    REGEXP_DEFAULT_ON_TAG_ATTR_7.lastIndex = 0;\n    if (REGEXP_DEFAULT_ON_TAG_ATTR_7.test(value)) {\n      return \"\";\n    }\n    // `url()`\n    REGEXP_DEFAULT_ON_TAG_ATTR_8.lastIndex = 0;\n    if (REGEXP_DEFAULT_ON_TAG_ATTR_8.test(value)) {\n      REGEXP_DEFAULT_ON_TAG_ATTR_4.lastIndex = 0;\n      if (REGEXP_DEFAULT_ON_TAG_ATTR_4.test(value)) {\n        return \"\";\n      }\n    }\n    if (cssFilter !== false) {\n      cssFilter = cssFilter || defaultCSSFilter;\n      value = cssFilter.process(value);\n    }\n  }\n\n  // escape `<>\"` before returns\n  value = escapeAttrValue(value);\n  return value;\n}\n\n// RegExp list\nvar REGEXP_LT = /</g;\nvar REGEXP_GT = />/g;\nvar REGEXP_QUOTE = /\"/g;\nvar REGEXP_QUOTE_2 = /&quot;/g;\nvar REGEXP_ATTR_VALUE_1 = /&#([a-zA-Z0-9]*);?/gim;\nvar REGEXP_ATTR_VALUE_COLON = /&colon;?/gim;\nvar REGEXP_ATTR_VALUE_NEWLINE = /&newline;?/gim;\n// var REGEXP_DEFAULT_ON_TAG_ATTR_3 = /\\/\\*|\\*\\//gm;\nvar REGEXP_DEFAULT_ON_TAG_ATTR_4 =\n  /((j\\s*a\\s*v\\s*a|v\\s*b|l\\s*i\\s*v\\s*e)\\s*s\\s*c\\s*r\\s*i\\s*p\\s*t\\s*|m\\s*o\\s*c\\s*h\\s*a):/gi;\n// var REGEXP_DEFAULT_ON_TAG_ATTR_5 = /^[\\s\"'`]*(d\\s*a\\s*t\\s*a\\s*)\\:/gi;\n// var REGEXP_DEFAULT_ON_TAG_ATTR_6 = /^[\\s\"'`]*(d\\s*a\\s*t\\s*a\\s*)\\:\\s*image\\//gi;\nvar REGEXP_DEFAULT_ON_TAG_ATTR_7 =\n  /e\\s*x\\s*p\\s*r\\s*e\\s*s\\s*s\\s*i\\s*o\\s*n\\s*\\(.*/gi;\nvar REGEXP_DEFAULT_ON_TAG_ATTR_8 = /u\\s*r\\s*l\\s*\\(.*/gi;\n\n/**\n * escape double quote\n *\n * @param {String} str\n * @return {String} str\n */\nfunction escapeQuote(str) {\n  return str.replace(REGEXP_QUOTE, \"&quot;\");\n}\n\n/**\n * unescape double quote\n *\n * @param {String} str\n * @return {String} str\n */\nfunction unescapeQuote(str) {\n  return str.replace(REGEXP_QUOTE_2, '\"');\n}\n\n/**\n * escape html entities\n *\n * @param {String} str\n * @return {String}\n */\nfunction escapeHtmlEntities(str) {\n  return str.replace(REGEXP_ATTR_VALUE_1, function replaceUnicode(str, code) {\n    return code[0] === \"x\" || code[0] === \"X\"\n      ? String.fromCharCode(parseInt(code.substr(1), 16))\n      : String.fromCharCode(parseInt(code, 10));\n  });\n}\n\n/**\n * escape html5 new danger entities\n *\n * @param {String} str\n * @return {String}\n */\nfunction escapeDangerHtml5Entities(str) {\n  return str\n    .replace(REGEXP_ATTR_VALUE_COLON, \":\")\n    .replace(REGEXP_ATTR_VALUE_NEWLINE, \" \");\n}\n\n/**\n * clear nonprintable characters\n *\n * @param {String} str\n * @return {String}\n */\nfunction clearNonPrintableCharacter(str) {\n  var str2 = \"\";\n  for (var i = 0, len = str.length; i < len; i++) {\n    str2 += str.charCodeAt(i) < 32 ? \" \" : str.charAt(i);\n  }\n  return _.trim(str2);\n}\n\n/**\n * get friendly attribute value\n *\n * @param {String} str\n * @return {String}\n */\nfunction friendlyAttrValue(str) {\n  str = unescapeQuote(str);\n  str = escapeHtmlEntities(str);\n  str = escapeDangerHtml5Entities(str);\n  str = clearNonPrintableCharacter(str);\n  return str;\n}\n\n/**\n * unescape attribute value\n *\n * @param {String} str\n * @return {String}\n */\nfunction escapeAttrValue(str) {\n  str = escapeQuote(str);\n  str = escapeHtml(str);\n  return str;\n}\n\n/**\n * `onIgnoreTag` function for removing all the tags that are not in whitelist\n */\nfunction onIgnoreTagStripAll() {\n  return \"\";\n}\n\n/**\n * remove tag body\n * specify a `tags` list, if the tag is not in the `tags` list then process by the specify function (optional)\n *\n * @param {array} tags\n * @param {function} next\n */\nfunction StripTagBody(tags, next) {\n  if (typeof next !== \"function\") {\n    next = function () {};\n  }\n\n  var isRemoveAllTag = !Array.isArray(tags);\n  function isRemoveTag(tag) {\n    if (isRemoveAllTag) return true;\n    return _.indexOf(tags, tag) !== -1;\n  }\n\n  var removeList = [];\n  var posStart = false;\n\n  return {\n    onIgnoreTag: function (tag, html, options) {\n      if (isRemoveTag(tag)) {\n        if (options.isClosing) {\n          var ret = \"[/removed]\";\n          var end = options.position + ret.length;\n          removeList.push([\n            posStart !== false ? posStart : options.position,\n            end,\n          ]);\n          posStart = false;\n          return ret;\n        } else {\n          if (!posStart) {\n            posStart = options.position;\n          }\n          return \"[removed]\";\n        }\n      } else {\n        return next(tag, html, options);\n      }\n    },\n    remove: function (html) {\n      var rethtml = \"\";\n      var lastPos = 0;\n      _.forEach(removeList, function (pos) {\n        rethtml += html.slice(lastPos, pos[0]);\n        lastPos = pos[1];\n      });\n      rethtml += html.slice(lastPos);\n      return rethtml;\n    },\n  };\n}\n\n/**\n * remove html comments\n *\n * @param {String} html\n * @return {String}\n */\nfunction stripCommentTag(html) {\n  var retHtml = \"\";\n  var lastPos = 0;\n  while (lastPos < html.length) {\n    var i = html.indexOf(\"<!--\", lastPos);\n    if (i === -1) {\n      retHtml += html.slice(lastPos);\n      break;\n    }\n    retHtml += html.slice(lastPos, i);\n    var j = html.indexOf(\"-->\", i);\n    if (j === -1) {\n      break;\n    }\n    lastPos = j + 3;\n  }\n  return retHtml;\n}\n\n/**\n * remove invisible characters\n *\n * @param {String} html\n * @return {String}\n */\nfunction stripBlankChar(html) {\n  var chars = html.split(\"\");\n  chars = chars.filter(function (char) {\n    var c = char.charCodeAt(0);\n    if (c === 127) return false;\n    if (c <= 31) {\n      if (c === 10 || c === 13) return true;\n      return false;\n    }\n    return true;\n  });\n  return chars.join(\"\");\n}\n\nexports.whiteList = getDefaultWhiteList();\nexports.getDefaultWhiteList = getDefaultWhiteList;\nexports.onTag = onTag;\nexports.onIgnoreTag = onIgnoreTag;\nexports.onTagAttr = onTagAttr;\nexports.onIgnoreTagAttr = onIgnoreTagAttr;\nexports.safeAttrValue = safeAttrValue;\nexports.escapeHtml = escapeHtml;\nexports.escapeQuote = escapeQuote;\nexports.unescapeQuote = unescapeQuote;\nexports.escapeHtmlEntities = escapeHtmlEntities;\nexports.escapeDangerHtml5Entities = escapeDangerHtml5Entities;\nexports.clearNonPrintableCharacter = clearNonPrintableCharacter;\nexports.friendlyAttrValue = friendlyAttrValue;\nexports.escapeAttrValue = escapeAttrValue;\nexports.onIgnoreTagStripAll = onIgnoreTagStripAll;\nexports.StripTagBody = StripTagBody;\nexports.stripCommentTag = stripCommentTag;\nexports.stripBlankChar = stripBlankChar;\nexports.attributeWrapSign = '\"';\nexports.cssFilter = defaultCSSFilter;\nexports.getDefaultCSSWhiteList = getDefaultCSSWhiteList;\n", "/**\n * Simple HTML Parser\n *\n * <AUTHOR>\n */\n\nvar _ = require(\"./util\");\n\n/**\n * get tag name\n *\n * @param {String} html e.g. '<a hef=\"#\">'\n * @return {String}\n */\nfunction getTagName(html) {\n  var i = _.spaceIndex(html);\n  var tagName;\n  if (i === -1) {\n    tagName = html.slice(1, -1);\n  } else {\n    tagName = html.slice(1, i + 1);\n  }\n  tagName = _.trim(tagName).toLowerCase();\n  if (tagName.slice(0, 1) === \"/\") tagName = tagName.slice(1);\n  if (tagName.slice(-1) === \"/\") tagName = tagName.slice(0, -1);\n  return tagName;\n}\n\n/**\n * is close tag?\n *\n * @param {String} html 如：'<a hef=\"#\">'\n * @return {Boolean}\n */\nfunction isClosing(html) {\n  return html.slice(0, 2) === \"</\";\n}\n\n/**\n * parse input html and returns processed html\n *\n * @param {String} html\n * @param {Function} onTag e.g. function (sourcePosition, position, tag, html, isClosing)\n * @param {Function} escapeHtml\n * @return {String}\n */\nfunction parseTag(html, onTag, escapeHtml) {\n  \"use strict\";\n\n  var rethtml = \"\";\n  var lastPos = 0;\n  var tagStart = false;\n  var quoteStart = false;\n  var currentPos = 0;\n  var len = html.length;\n  var currentTagName = \"\";\n  var currentHtml = \"\";\n\n  chariterator: for (currentPos = 0; currentPos < len; currentPos++) {\n    var c = html.charAt(currentPos);\n    if (tagStart === false) {\n      if (c === \"<\") {\n        tagStart = currentPos;\n        continue;\n      }\n    } else {\n      if (quoteStart === false) {\n        if (c === \"<\") {\n          rethtml += escapeHtml(html.slice(lastPos, currentPos));\n          tagStart = currentPos;\n          lastPos = currentPos;\n          continue;\n        }\n        if (c === \">\" || currentPos === len - 1) {\n          rethtml += escapeHtml(html.slice(lastPos, tagStart));\n          currentHtml = html.slice(tagStart, currentPos + 1);\n          currentTagName = getTagName(currentHtml);\n          rethtml += onTag(\n            tagStart,\n            rethtml.length,\n            currentTagName,\n            currentHtml,\n            isClosing(currentHtml)\n          );\n          lastPos = currentPos + 1;\n          tagStart = false;\n          continue;\n        }\n        if (c === '\"' || c === \"'\") {\n          var i = 1;\n          var ic = html.charAt(currentPos - i);\n\n          while (ic.trim() === \"\" || ic === \"=\") {\n            if (ic === \"=\") {\n              quoteStart = c;\n              continue chariterator;\n            }\n            ic = html.charAt(currentPos - ++i);\n          }\n        }\n      } else {\n        if (c === quoteStart) {\n          quoteStart = false;\n          continue;\n        }\n      }\n    }\n  }\n  if (lastPos < len) {\n    rethtml += escapeHtml(html.substr(lastPos));\n  }\n\n  return rethtml;\n}\n\nvar REGEXP_ILLEGAL_ATTR_NAME = /[^a-zA-Z0-9\\\\_:.-]/gim;\n\n/**\n * parse input attributes and returns processed attributes\n *\n * @param {String} html e.g. `href=\"#\" target=\"_blank\"`\n * @param {Function} onAttr e.g. `function (name, value)`\n * @return {String}\n */\nfunction parseAttr(html, onAttr) {\n  \"use strict\";\n\n  var lastPos = 0;\n  var lastMarkPos = 0;\n  var retAttrs = [];\n  var tmpName = false;\n  var len = html.length;\n\n  function addAttr(name, value) {\n    name = _.trim(name);\n    name = name.replace(REGEXP_ILLEGAL_ATTR_NAME, \"\").toLowerCase();\n    if (name.length < 1) return;\n    var ret = onAttr(name, value || \"\");\n    if (ret) retAttrs.push(ret);\n  }\n\n  // 逐个分析字符\n  for (var i = 0; i < len; i++) {\n    var c = html.charAt(i);\n    var v, j;\n    if (tmpName === false && c === \"=\") {\n      tmpName = html.slice(lastPos, i);\n      lastPos = i + 1;\n      lastMarkPos = html.charAt(lastPos) === '\"' || html.charAt(lastPos) === \"'\" ? lastPos : findNextQuotationMark(html, i + 1);\n      continue;\n    }\n    if (tmpName !== false) {\n      if (\n        i === lastMarkPos\n      ) {\n        j = html.indexOf(c, i + 1);\n        if (j === -1) {\n          break;\n        } else {\n          v = _.trim(html.slice(lastMarkPos + 1, j));\n          addAttr(tmpName, v);\n          tmpName = false;\n          i = j;\n          lastPos = i + 1;\n          continue;\n        }\n      }\n    }\n    if (/\\s|\\n|\\t/.test(c)) {\n      html = html.replace(/\\s|\\n|\\t/g, \" \");\n      if (tmpName === false) {\n        j = findNextEqual(html, i);\n        if (j === -1) {\n          v = _.trim(html.slice(lastPos, i));\n          addAttr(v);\n          tmpName = false;\n          lastPos = i + 1;\n          continue;\n        } else {\n          i = j - 1;\n          continue;\n        }\n      } else {\n        j = findBeforeEqual(html, i - 1);\n        if (j === -1) {\n          v = _.trim(html.slice(lastPos, i));\n          v = stripQuoteWrap(v);\n          addAttr(tmpName, v);\n          tmpName = false;\n          lastPos = i + 1;\n          continue;\n        } else {\n          continue;\n        }\n      }\n    }\n  }\n\n  if (lastPos < html.length) {\n    if (tmpName === false) {\n      addAttr(html.slice(lastPos));\n    } else {\n      addAttr(tmpName, stripQuoteWrap(_.trim(html.slice(lastPos))));\n    }\n  }\n\n  return _.trim(retAttrs.join(\" \"));\n}\n\nfunction findNextEqual(str, i) {\n  for (; i < str.length; i++) {\n    var c = str[i];\n    if (c === \" \") continue;\n    if (c === \"=\") return i;\n    return -1;\n  }\n}\n\nfunction findNextQuotationMark(str, i) {\n  for (; i < str.length; i++) {\n    var c = str[i];\n    if (c === \" \") continue;\n    if (c === \"'\" || c === '\"') return i;\n    return -1;\n  }\n}\n\nfunction findBeforeEqual(str, i) {\n  for (; i > 0; i--) {\n    var c = str[i];\n    if (c === \" \") continue;\n    if (c === \"=\") return i;\n    return -1;\n  }\n}\n\nfunction isQuoteWrapString(text) {\n  if (\n    (text[0] === '\"' && text[text.length - 1] === '\"') ||\n    (text[0] === \"'\" && text[text.length - 1] === \"'\")\n  ) {\n    return true;\n  } else {\n    return false;\n  }\n}\n\nfunction stripQuoteWrap(text) {\n  if (isQuoteWrapString(text)) {\n    return text.substr(1, text.length - 2);\n  } else {\n    return text;\n  }\n}\n\nexports.parseTag = parseTag;\nexports.parseAttr = parseAttr;\n", "/**\n * filter xss\n *\n * <AUTHOR>\n */\n\nvar FilterCSS = require(\"cssfilter\").FilterCSS;\nvar DEFAULT = require(\"./default\");\nvar parser = require(\"./parser\");\nvar parseTag = parser.parseTag;\nvar parseAttr = parser.parseAttr;\nvar _ = require(\"./util\");\n\n/**\n * returns `true` if the input value is `undefined` or `null`\n *\n * @param {Object} obj\n * @return {Boolean}\n */\nfunction isNull(obj) {\n  return obj === undefined || obj === null;\n}\n\n/**\n * get attributes for a tag\n *\n * @param {String} html\n * @return {Object}\n *   - {String} html\n *   - {Boolean} closing\n */\nfunction getAttrs(html) {\n  var i = _.spaceIndex(html);\n  if (i === -1) {\n    return {\n      html: \"\",\n      closing: html[html.length - 2] === \"/\",\n    };\n  }\n  html = _.trim(html.slice(i + 1, -1));\n  var isClosing = html[html.length - 1] === \"/\";\n  if (isClosing) html = _.trim(html.slice(0, -1));\n  return {\n    html: html,\n    closing: isClosing,\n  };\n}\n\n/**\n * shallow copy\n *\n * @param {Object} obj\n * @return {Object}\n */\nfunction shallowCopyObject(obj) {\n  var ret = {};\n  for (var i in obj) {\n    ret[i] = obj[i];\n  }\n  return ret;\n}\n\nfunction keysToLowerCase(obj) {\n  var ret = {};\n  for (var i in obj) {\n    if (Array.isArray(obj[i])) {\n      ret[i.toLowerCase()] = obj[i].map(function (item) {\n        return item.toLowerCase();\n      });\n    } else {\n      ret[i.toLowerCase()] = obj[i];\n    }\n  }\n  return ret;\n}\n\n/**\n * FilterXSS class\n *\n * @param {Object} options\n *        whiteList (or allowList), onTag, onTagAttr, onIgnoreTag,\n *        onIgnoreTagAttr, safeAttrValue, escapeHtml\n *        stripIgnoreTagBody, allowCommentTag, stripBlankChar\n *        css{whiteList, onAttr, onIgnoreAttr} `css=false` means don't use `cssfilter`\n */\nfunction FilterXSS(options) {\n  options = shallowCopyObject(options || {});\n\n  if (options.stripIgnoreTag) {\n    if (options.onIgnoreTag) {\n      console.error(\n        'Notes: cannot use these two options \"stripIgnoreTag\" and \"onIgnoreTag\" at the same time'\n      );\n    }\n    options.onIgnoreTag = DEFAULT.onIgnoreTagStripAll;\n  }\n  if (options.whiteList || options.allowList) {\n    options.whiteList = keysToLowerCase(options.whiteList || options.allowList);\n  } else {\n    options.whiteList = DEFAULT.whiteList;\n  }\n\n  this.attributeWrapSign = options.singleQuotedAttributeValue === true ? \"'\" : DEFAULT.attributeWrapSign;\n\n  options.onTag = options.onTag || DEFAULT.onTag;\n  options.onTagAttr = options.onTagAttr || DEFAULT.onTagAttr;\n  options.onIgnoreTag = options.onIgnoreTag || DEFAULT.onIgnoreTag;\n  options.onIgnoreTagAttr = options.onIgnoreTagAttr || DEFAULT.onIgnoreTagAttr;\n  options.safeAttrValue = options.safeAttrValue || DEFAULT.safeAttrValue;\n  options.escapeHtml = options.escapeHtml || DEFAULT.escapeHtml;\n  this.options = options;\n\n  if (options.css === false) {\n    this.cssFilter = false;\n  } else {\n    options.css = options.css || {};\n    this.cssFilter = new FilterCSS(options.css);\n  }\n}\n\n/**\n * start process and returns result\n *\n * @param {String} html\n * @return {String}\n */\nFilterXSS.prototype.process = function (html) {\n  // compatible with the input\n  html = html || \"\";\n  html = html.toString();\n  if (!html) return \"\";\n\n  var me = this;\n  var options = me.options;\n  var whiteList = options.whiteList;\n  var onTag = options.onTag;\n  var onIgnoreTag = options.onIgnoreTag;\n  var onTagAttr = options.onTagAttr;\n  var onIgnoreTagAttr = options.onIgnoreTagAttr;\n  var safeAttrValue = options.safeAttrValue;\n  var escapeHtml = options.escapeHtml;\n  var attributeWrapSign = me.attributeWrapSign;\n  var cssFilter = me.cssFilter;\n\n  // remove invisible characters\n  if (options.stripBlankChar) {\n    html = DEFAULT.stripBlankChar(html);\n  }\n\n  // remove html comments\n  if (!options.allowCommentTag) {\n    html = DEFAULT.stripCommentTag(html);\n  }\n\n  // if enable stripIgnoreTagBody\n  var stripIgnoreTagBody = false;\n  if (options.stripIgnoreTagBody) {\n    stripIgnoreTagBody = DEFAULT.StripTagBody(\n      options.stripIgnoreTagBody,\n      onIgnoreTag\n    );\n    onIgnoreTag = stripIgnoreTagBody.onIgnoreTag;\n  }\n\n  var retHtml = parseTag(\n    html,\n    function (sourcePosition, position, tag, html, isClosing) {\n      var info = {\n        sourcePosition: sourcePosition,\n        position: position,\n        isClosing: isClosing,\n        isWhite: Object.prototype.hasOwnProperty.call(whiteList, tag),\n      };\n\n      // call `onTag()`\n      var ret = onTag(tag, html, info);\n      if (!isNull(ret)) return ret;\n\n      if (info.isWhite) {\n        if (info.isClosing) {\n          return \"</\" + tag + \">\";\n        }\n\n        var attrs = getAttrs(html);\n        var whiteAttrList = whiteList[tag];\n        var attrsHtml = parseAttr(attrs.html, function (name, value) {\n          // call `onTagAttr()`\n          var isWhiteAttr = _.indexOf(whiteAttrList, name) !== -1;\n          var ret = onTagAttr(tag, name, value, isWhiteAttr);\n          if (!isNull(ret)) return ret;\n\n          if (isWhiteAttr) {\n            // call `safeAttrValue()`\n            value = safeAttrValue(tag, name, value, cssFilter);\n            if (value) {\n              return name + '=' + attributeWrapSign + value + attributeWrapSign;\n            } else {\n              return name;\n            }\n          } else {\n            // call `onIgnoreTagAttr()`\n            ret = onIgnoreTagAttr(tag, name, value, isWhiteAttr);\n            if (!isNull(ret)) return ret;\n            return;\n          }\n        });\n\n        // build new tag html\n        html = \"<\" + tag;\n        if (attrsHtml) html += \" \" + attrsHtml;\n        if (attrs.closing) html += \" /\";\n        html += \">\";\n        return html;\n      } else {\n        // call `onIgnoreTag()`\n        ret = onIgnoreTag(tag, html, info);\n        if (!isNull(ret)) return ret;\n        return escapeHtml(html);\n      }\n    },\n    escapeHtml\n  );\n\n  // if enable stripIgnoreTagBody\n  if (stripIgnoreTagBody) {\n    retHtml = stripIgnoreTagBody.remove(retHtml);\n  }\n\n  return retHtml;\n};\n\nmodule.exports = FilterXSS;\n", "/**\n * xss\n *\n * <AUTHOR>\n */\n\nvar DEFAULT = require(\"./default\");\nvar parser = require(\"./parser\");\nvar FilterXSS = require(\"./xss\");\n\n/**\n * filter xss function\n *\n * @param {String} html\n * @param {Object} options { whiteList, onTag, onTagAttr, onIgnoreTag, onIgnoreTagAttr, safeAttrValue, escapeHtml }\n * @return {String}\n */\nfunction filterXSS(html, options) {\n  var xss = new FilterXSS(options);\n  return xss.process(html);\n}\n\nexports = module.exports = filterXSS;\nexports.filterXSS = filterXSS;\nexports.FilterXSS = FilterXSS;\n\n(function () {\n  for (var i in DEFAULT) {\n    exports[i] = DEFAULT[i];\n  }\n  for (var j in parser) {\n    exports[j] = parser[j];\n  }\n})();\n\n// using `xss` on the browser, output `filterXSS` to the globals\nif (typeof window !== \"undefined\") {\n  window.filterXSS = module.exports;\n}\n\n// using `xss` on the WebWorker, output `filterXSS` to the globals\nfunction isWorkerEnv() {\n  return (\n    typeof self !== \"undefined\" &&\n    typeof DedicatedWorkerGlobalScope !== \"undefined\" &&\n    self instanceof DedicatedWorkerGlobalScope\n  );\n}\nif (isWorkerEnv()) {\n  self.filterXSS = module.exports;\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAMA,aAAS,sBAAuB;AAM9B,UAAI,YAAY,CAAC;AAEjB,gBAAU,eAAe,IAAI;AAC7B,gBAAU,aAAa,IAAI;AAC3B,gBAAU,YAAY,IAAI;AAC1B,gBAAU,kBAAkB,IAAI;AAChC,gBAAU,oBAAoB,IAAI;AAClC,gBAAU,KAAK,IAAI;AACnB,gBAAU,cAAc,IAAI;AAC5B,gBAAU,WAAW,IAAI;AACzB,gBAAU,iBAAiB,IAAI;AAC/B,gBAAU,qBAAqB,IAAI;AACnC,gBAAU,oBAAoB,IAAI;AAClC,gBAAU,qBAAqB,IAAI;AACnC,gBAAU,2BAA2B,IAAI;AACzC,gBAAU,gBAAgB,IAAI;AAC9B,gBAAU,sBAAsB,IAAI;AACpC,gBAAU,2BAA2B,IAAI;AACzC,gBAAU,SAAS,IAAI;AACvB,gBAAU,qBAAqB,IAAI;AACnC,gBAAU,YAAY,IAAI;AAC1B,gBAAU,uBAAuB,IAAI;AACrC,gBAAU,iBAAiB,IAAI;AAC/B,gBAAU,kBAAkB,IAAI;AAChC,gBAAU,kBAAkB,IAAI;AAChC,gBAAU,mBAAmB,IAAI;AACjC,gBAAU,qBAAqB,IAAI;AACnC,gBAAU,mBAAmB,IAAI;AACjC,gBAAU,iBAAiB,IAAI;AAC/B,gBAAU,gBAAgB,IAAI;AAC9B,gBAAU,SAAS,IAAI;AACvB,gBAAU,OAAO,IAAI;AACrB,gBAAU,gBAAgB,IAAI;AAC9B,gBAAU,gBAAgB,IAAI;AAC9B,gBAAU,gBAAgB,IAAI;AAC9B,gBAAU,QAAQ,IAAI;AACtB,gBAAU,eAAe,IAAI;AAC7B,gBAAU,qBAAqB,IAAI;AACnC,gBAAU,2BAA2B,IAAI;AACzC,gBAAU,4BAA4B,IAAI;AAC1C,gBAAU,qBAAqB,IAAI;AACnC,gBAAU,qBAAqB,IAAI;AACnC,gBAAU,iBAAiB,IAAI;AAC/B,gBAAU,cAAc,IAAI;AAC5B,gBAAU,cAAc,IAAI;AAC5B,gBAAU,qBAAqB,IAAI;AACnC,gBAAU,qBAAqB,IAAI;AACnC,gBAAU,oBAAoB,IAAI;AAClC,gBAAU,qBAAqB,IAAI;AACnC,gBAAU,oBAAoB,IAAI;AAClC,gBAAU,aAAa,IAAI;AAC3B,gBAAU,mBAAmB,IAAI;AACjC,gBAAU,mBAAmB,IAAI;AACjC,gBAAU,mBAAmB,IAAI;AACjC,gBAAU,eAAe,IAAI;AAC7B,gBAAU,cAAc,IAAI;AAC5B,gBAAU,oBAAoB,IAAI;AAClC,gBAAU,oBAAoB,IAAI;AAClC,gBAAU,oBAAoB,IAAI;AAClC,gBAAU,gBAAgB,IAAI;AAC9B,gBAAU,cAAc,IAAI;AAC5B,gBAAU,YAAY,IAAI;AAC1B,gBAAU,kBAAkB,IAAI;AAChC,gBAAU,wBAAwB,IAAI;AACtC,gBAAU,yBAAyB,IAAI;AACvC,gBAAU,kBAAkB,IAAI;AAChC,gBAAU,kBAAkB,IAAI;AAChC,gBAAU,cAAc,IAAI;AAC5B,gBAAU,QAAQ,IAAI;AACtB,gBAAU,sBAAsB,IAAI;AACpC,gBAAU,YAAY,IAAI;AAC1B,gBAAU,YAAY,IAAI;AAC1B,gBAAU,UAAU,IAAI;AACxB,gBAAU,cAAc,IAAI;AAC5B,gBAAU,aAAa,IAAI;AAC3B,gBAAU,cAAc,IAAI;AAC5B,gBAAU,cAAc,IAAI;AAC5B,gBAAU,cAAc,IAAI;AAC5B,gBAAU,QAAQ,IAAI;AACtB,gBAAU,OAAO,IAAI;AACrB,gBAAU,MAAM,IAAI;AACpB,gBAAU,WAAW,IAAI;AACzB,gBAAU,WAAW,IAAI;AACzB,gBAAU,OAAO,IAAI;AACrB,gBAAU,6BAA6B,IAAI;AAC3C,gBAAU,cAAc,IAAI;AAC5B,gBAAU,aAAa,IAAI;AAC3B,gBAAU,YAAY,IAAI;AAC1B,gBAAU,aAAa,IAAI;AAC3B,gBAAU,mBAAmB,IAAI;AACjC,gBAAU,mBAAmB,IAAI;AACjC,gBAAU,mBAAmB,IAAI;AACjC,gBAAU,aAAa,IAAI;AAC3B,gBAAU,cAAc,IAAI;AAC5B,gBAAU,SAAS,IAAI;AACvB,gBAAU,SAAS,IAAI;AACvB,gBAAU,SAAS,IAAI;AACvB,gBAAU,mBAAmB,IAAI;AACjC,gBAAU,eAAe,IAAI;AAC7B,gBAAU,aAAa,IAAI;AAC3B,gBAAU,MAAM,IAAI;AACpB,gBAAU,KAAK,IAAI;AACnB,gBAAU,WAAW,IAAI;AACzB,gBAAU,YAAY,IAAI;AAC1B,gBAAU,QAAQ,IAAI;AACtB,gBAAU,WAAW,IAAI;AACzB,gBAAU,SAAS,IAAI;AACvB,gBAAU,gBAAgB,IAAI;AAC9B,gBAAU,cAAc,IAAI;AAC5B,gBAAU,iBAAiB,IAAI;AAC/B,gBAAU,mBAAmB,IAAI;AACjC,gBAAU,WAAW,IAAI;AACzB,gBAAU,aAAa,IAAI;AAC3B,gBAAU,QAAQ,IAAI;AACtB,gBAAU,MAAM,IAAI;AACpB,gBAAU,YAAY,IAAI;AAC1B,gBAAU,gBAAgB,IAAI;AAC9B,gBAAU,WAAW,IAAI;AACzB,gBAAU,WAAW,IAAI;AACzB,gBAAU,aAAa,IAAI;AAC3B,gBAAU,WAAW,IAAI;AACzB,gBAAU,OAAO,IAAI;AACrB,gBAAU,cAAc,IAAI;AAC5B,gBAAU,aAAa,IAAI;AAC3B,gBAAU,eAAe,IAAI;AAC7B,gBAAU,WAAW,IAAI;AACzB,gBAAU,WAAW,IAAI;AACzB,gBAAU,MAAM,IAAI;AACpB,gBAAU,aAAa,IAAI;AAC3B,gBAAU,uBAAuB,IAAI;AACrC,gBAAU,cAAc,IAAI;AAC5B,gBAAU,wBAAwB,IAAI;AACtC,gBAAU,WAAW,IAAI;AACzB,gBAAU,kBAAkB,IAAI;AAChC,gBAAU,cAAc,IAAI;AAC5B,gBAAU,YAAY,IAAI;AAC1B,gBAAU,gBAAgB,IAAI;AAC9B,gBAAU,cAAc,IAAI;AAC5B,gBAAU,yBAAyB,IAAI;AACvC,gBAAU,mBAAmB,IAAI;AACjC,gBAAU,yBAAyB,IAAI;AACvC,gBAAU,wBAAwB,IAAI;AACtC,gBAAU,sBAAsB,IAAI;AACpC,gBAAU,uBAAuB,IAAI;AACrC,gBAAU,aAAa,IAAI;AAC3B,gBAAU,MAAM,IAAI;AACpB,gBAAU,WAAW,IAAI;AACzB,gBAAU,mBAAmB,IAAI;AACjC,gBAAU,gBAAgB,IAAI;AAC9B,gBAAU,gBAAgB,IAAI;AAC9B,gBAAU,aAAa,IAAI;AAC3B,gBAAU,iBAAiB,IAAI;AAC/B,gBAAU,mBAAmB,IAAI;AACjC,gBAAU,UAAU,IAAI;AACxB,gBAAU,cAAc,IAAI;AAC5B,gBAAU,gBAAgB,IAAI;AAC9B,gBAAU,eAAe,IAAI;AAC7B,gBAAU,qBAAqB,IAAI;AACnC,gBAAU,uBAAuB,IAAI;AACrC,gBAAU,oBAAoB,IAAI;AAClC,gBAAU,qBAAqB,IAAI;AACnC,gBAAU,QAAQ,IAAI;AACtB,gBAAU,SAAS,IAAI;AACvB,gBAAU,MAAM,IAAI;AACpB,gBAAU,mBAAmB,IAAI;AACjC,gBAAU,kBAAkB,IAAI;AAChC,gBAAU,UAAU,IAAI;AACxB,gBAAU,iBAAiB,IAAI;AAC/B,gBAAU,kBAAkB,IAAI;AAChC,gBAAU,iBAAiB,IAAI;AAC/B,gBAAU,eAAe,IAAI;AAC7B,gBAAU,cAAc,IAAI;AAC5B,gBAAU,MAAM,IAAI;AACpB,gBAAU,gBAAgB,IAAI;AAC9B,gBAAU,gBAAgB,IAAI;AAC9B,gBAAU,kBAAkB,IAAI;AAChC,gBAAU,YAAY,IAAI;AAC1B,gBAAU,WAAW,IAAI;AACzB,gBAAU,aAAa,IAAI;AAC3B,gBAAU,WAAW,IAAI;AACzB,gBAAU,eAAe,IAAI;AAC7B,gBAAU,oBAAoB,IAAI;AAClC,gBAAU,qBAAqB,IAAI;AACnC,gBAAU,wBAAwB,IAAI;AACtC,gBAAU,YAAY,IAAI;AAC1B,gBAAU,kBAAkB,IAAI;AAChC,gBAAU,qBAAqB,IAAI;AACnC,gBAAU,iBAAiB,IAAI;AAC/B,gBAAU,QAAQ,IAAI;AACtB,gBAAU,eAAe,IAAI;AAC7B,gBAAU,aAAa,IAAI;AAC3B,gBAAU,cAAc,IAAI;AAC5B,gBAAU,YAAY,IAAI;AAC1B,gBAAU,eAAe,IAAI;AAC7B,gBAAU,aAAa,IAAI;AAC3B,gBAAU,OAAO,IAAI;AACrB,gBAAU,MAAM,IAAI;AACpB,gBAAU,UAAU,IAAI;AACxB,gBAAU,iBAAiB,IAAI;AAC/B,gBAAU,iBAAiB,IAAI;AAC/B,gBAAU,gBAAgB,IAAI;AAC9B,gBAAU,iBAAiB,IAAI;AAC/B,gBAAU,gBAAgB,IAAI;AAC9B,gBAAU,WAAW,IAAI;AACzB,gBAAU,YAAY,IAAI;AAC1B,gBAAU,aAAa,IAAI;AAC3B,gBAAU,eAAe,IAAI;AAC7B,gBAAU,aAAa,IAAI;AAC3B,gBAAU,WAAW,IAAI;AACzB,gBAAU,kBAAkB,IAAI;AAChC,gBAAU,WAAW,IAAI;AACzB,gBAAU,YAAY,IAAI;AAC1B,gBAAU,WAAW,IAAI;AACzB,gBAAU,WAAW,IAAI;AACzB,gBAAU,YAAY,IAAI;AAC1B,gBAAU,WAAW,IAAI;AACzB,gBAAU,SAAS,IAAI;AACvB,gBAAU,UAAU,IAAI;AACxB,gBAAU,WAAW,IAAI;AACzB,gBAAU,UAAU,IAAI;AACxB,gBAAU,WAAW,IAAI;AACzB,gBAAU,QAAQ,IAAI;AACtB,gBAAU,YAAY,IAAI;AAC1B,gBAAU,iBAAiB,IAAI;AAC/B,gBAAU,SAAS,IAAI;AACvB,gBAAU,OAAO,IAAI;AACrB,gBAAU,SAAS,IAAI;AACvB,gBAAU,SAAS,IAAI;AACvB,gBAAU,eAAe,IAAI;AAC7B,gBAAU,gBAAgB,IAAI;AAC9B,gBAAU,eAAe,IAAI;AAC7B,gBAAU,eAAe,IAAI;AAC7B,gBAAU,UAAU,IAAI;AACxB,gBAAU,eAAe,IAAI;AAC7B,gBAAU,YAAY,IAAI;AAC1B,gBAAU,YAAY,IAAI;AAC1B,gBAAU,SAAS,IAAI;AACvB,gBAAU,gBAAgB,IAAI;AAC9B,gBAAU,cAAc,IAAI;AAC5B,gBAAU,eAAe,IAAI;AAC7B,gBAAU,aAAa,IAAI;AAC3B,gBAAU,MAAM,IAAI;AACpB,gBAAU,kBAAkB,IAAI;AAChC,gBAAU,mBAAmB,IAAI;AACjC,gBAAU,mBAAmB,IAAI;AACjC,gBAAU,aAAa,IAAI;AAC3B,gBAAU,OAAO,IAAI;AACrB,gBAAU,aAAa,IAAI;AAC3B,gBAAU,cAAc,IAAI;AAC5B,gBAAU,aAAa,IAAI;AAC3B,gBAAU,oBAAoB,IAAI;AAClC,gBAAU,OAAO,IAAI;AACrB,gBAAU,aAAa,IAAI;AAC3B,gBAAU,aAAa,IAAI;AAC3B,gBAAU,UAAU,IAAI;AACxB,gBAAU,oBAAoB,IAAI;AAClC,gBAAU,QAAQ,IAAI;AACtB,gBAAU,iBAAiB,IAAI;AAC/B,gBAAU,QAAQ,IAAI;AACtB,gBAAU,MAAM,IAAI;AACpB,gBAAU,YAAY,IAAI;AAC1B,gBAAU,aAAa,IAAI;AAC3B,gBAAU,UAAU,IAAI;AACxB,gBAAU,OAAO,IAAI;AACrB,gBAAU,UAAU,IAAI;AACxB,gBAAU,gBAAgB,IAAI;AAC9B,gBAAU,YAAY,IAAI;AAC1B,gBAAU,YAAY,IAAI;AAC1B,gBAAU,eAAe,IAAI;AAC7B,gBAAU,uBAAuB,IAAI;AACrC,gBAAU,eAAe,IAAI;AAC7B,gBAAU,cAAc,IAAI;AAC5B,gBAAU,MAAM,IAAI;AACpB,gBAAU,OAAO,IAAI;AACrB,gBAAU,UAAU,IAAI;AACxB,gBAAU,cAAc,IAAI;AAC5B,gBAAU,eAAe,IAAI;AAC7B,gBAAU,mBAAmB,IAAI;AACjC,gBAAU,aAAa,IAAI;AAC3B,gBAAU,QAAQ,IAAI;AACtB,gBAAU,YAAY,IAAI;AAC1B,gBAAU,UAAU,IAAI;AACxB,gBAAU,cAAc,IAAI;AAC5B,gBAAU,YAAY,IAAI;AAC1B,gBAAU,iBAAiB,IAAI;AAC/B,gBAAU,sBAAsB,IAAI;AACpC,gBAAU,iBAAiB,IAAI;AAC/B,gBAAU,uBAAuB,IAAI;AACrC,gBAAU,sBAAsB,IAAI;AACpC,gBAAU,sBAAsB,IAAI;AACpC,gBAAU,uBAAuB,IAAI;AACrC,gBAAU,eAAe,IAAI;AAC7B,gBAAU,qBAAqB,IAAI;AACnC,gBAAU,wBAAwB,IAAI;AACtC,gBAAU,qBAAqB,IAAI;AACnC,gBAAU,aAAa,IAAI;AAC3B,gBAAU,aAAa,IAAI;AAC3B,gBAAU,cAAc,IAAI;AAC5B,gBAAU,kBAAkB,IAAI;AAChC,gBAAU,eAAe,IAAI;AAC7B,gBAAU,aAAa,IAAI;AAC3B,gBAAU,qBAAqB,IAAI;AACnC,gBAAU,gBAAgB,IAAI;AAC9B,gBAAU,yBAAyB,IAAI;AACvC,gBAAU,WAAW,IAAI;AACzB,gBAAU,KAAK,IAAI;AACnB,gBAAU,WAAW,IAAI;AACzB,gBAAU,kBAAkB,IAAI;AAChC,gBAAU,iBAAiB,IAAI;AAC/B,gBAAU,YAAY,IAAI;AAC1B,gBAAU,kBAAkB,IAAI;AAChC,gBAAU,qBAAqB,IAAI;AACnC,gBAAU,qBAAqB,IAAI;AACnC,gBAAU,4BAA4B,IAAI;AAC1C,gBAAU,cAAc,IAAI;AAC5B,gBAAU,gBAAgB,IAAI;AAC9B,gBAAU,YAAY,IAAI;AAC1B,gBAAU,eAAe,IAAI;AAC7B,gBAAU,gBAAgB,IAAI;AAC9B,gBAAU,cAAc,IAAI;AAC5B,gBAAU,aAAa,IAAI;AAC3B,gBAAU,aAAa,IAAI;AAC3B,gBAAU,YAAY,IAAI;AAC1B,gBAAU,cAAc,IAAI;AAC5B,gBAAU,cAAc,IAAI;AAC5B,gBAAU,QAAQ,IAAI;AACtB,gBAAU,aAAa,IAAI;AAC3B,gBAAU,QAAQ,IAAI;AACtB,gBAAU,OAAO,IAAI;AACrB,gBAAU,aAAa,IAAI;AAC3B,gBAAU,YAAY,IAAI;AAC1B,gBAAU,cAAc,IAAI;AAC5B,gBAAU,WAAW,IAAI;AACzB,gBAAU,WAAW,IAAI;AACzB,gBAAU,cAAc,IAAI;AAC5B,gBAAU,cAAc,IAAI;AAC5B,gBAAU,SAAS,IAAI;AAEvB,aAAO;AAAA,IACT;AAWA,aAAS,OAAQ,MAAM,OAAO,SAAS;AAAA,IAEvC;AAUA,aAAS,aAAc,MAAM,OAAO,SAAS;AAAA,IAE7C;AAEA,QAAI,wBAAwB;AAS5B,aAAS,cAAc,MAAM,OAAO;AAClC,UAAI,sBAAsB,KAAK,KAAK,EAAG,QAAO;AAC9C,aAAO;AAAA,IACT;AAGA,YAAQ,YAAY,oBAAoB;AACxC,YAAQ,sBAAsB;AAC9B,YAAQ,SAAS;AACjB,YAAQ,eAAe;AACvB,YAAQ,gBAAgB;AAAA;AAAA;;;AC7YxB;AAAA;AAAA,WAAO,UAAU;AAAA,MACf,SAAS,SAAU,KAAK,MAAM;AAC5B,YAAI,GAAG;AACP,YAAI,MAAM,UAAU,SAAS;AAC3B,iBAAO,IAAI,QAAQ,IAAI;AAAA,QACzB;AACA,aAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,KAAK;AACtC,cAAI,IAAI,CAAC,MAAM,MAAM;AACnB,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,MACA,SAAS,SAAU,KAAK,IAAI,OAAO;AACjC,YAAI,GAAG;AACP,YAAI,MAAM,UAAU,SAAS;AAC3B,iBAAO,IAAI,QAAQ,IAAI,KAAK;AAAA,QAC9B;AACA,aAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,KAAK;AACtC,aAAG,KAAK,OAAO,IAAI,CAAC,GAAG,GAAG,GAAG;AAAA,QAC/B;AAAA,MACF;AAAA,MACA,MAAM,SAAU,KAAK;AACnB,YAAI,OAAO,UAAU,MAAM;AACzB,iBAAO,IAAI,KAAK;AAAA,QAClB;AACA,eAAO,IAAI,QAAQ,kBAAkB,EAAE;AAAA,MACzC;AAAA,MACA,WAAW,SAAU,KAAK;AACxB,YAAI,OAAO,UAAU,WAAW;AAC9B,iBAAO,IAAI,UAAU;AAAA,QACvB;AACA,eAAO,IAAI,QAAQ,WAAW,EAAE;AAAA,MAClC;AAAA,IACF;AAAA;AAAA;;;AClCA;AAAA;AAMA,QAAI,IAAI;AAWR,aAAS,WAAY,KAAK,QAAQ;AAChC,YAAM,EAAE,UAAU,GAAG;AACrB,UAAI,IAAI,IAAI,SAAS,CAAC,MAAM,IAAK,QAAO;AACxC,UAAI,YAAY,IAAI;AACpB,UAAI,oBAAoB;AACxB,UAAI,UAAU;AACd,UAAI,IAAI;AACR,UAAI,SAAS;AAEb,eAAS,aAAc;AAErB,YAAI,CAAC,mBAAmB;AACtB,cAAI,SAAS,EAAE,KAAK,IAAI,MAAM,SAAS,CAAC,CAAC;AACzC,cAAIA,KAAI,OAAO,QAAQ,GAAG;AAC1B,cAAIA,OAAM,IAAI;AACZ,gBAAI,OAAO,EAAE,KAAK,OAAO,MAAM,GAAGA,EAAC,CAAC;AACpC,gBAAI,QAAQ,EAAE,KAAK,OAAO,MAAMA,KAAI,CAAC,CAAC;AAEtC,gBAAI,MAAM;AACR,kBAAI,MAAM,OAAO,SAAS,OAAO,QAAQ,MAAM,OAAO,MAAM;AAC5D,kBAAI,IAAK,WAAU,MAAM;AAAA,YAC3B;AAAA,UACF;AAAA,QACF;AACA,kBAAU,IAAI;AAAA,MAChB;AAEA,aAAO,IAAI,WAAW,KAAK;AACzB,YAAI,IAAI,IAAI,CAAC;AACb,YAAI,MAAM,OAAO,IAAI,IAAI,CAAC,MAAM,KAAK;AAEnC,cAAI,IAAI,IAAI,QAAQ,MAAM,IAAI,CAAC;AAE/B,cAAI,MAAM,GAAI;AAEd,cAAI,IAAI;AACR,oBAAU,IAAI;AACd,8BAAoB;AAAA,QACtB,WAAW,MAAM,KAAK;AACpB,8BAAoB;AAAA,QACtB,WAAW,MAAM,KAAK;AACpB,8BAAoB;AAAA,QACtB,WAAW,MAAM,KAAK;AACpB,cAAI,mBAAmB;AAAA,UAEvB,OAAO;AACL,uBAAW;AAAA,UACb;AAAA,QACF,WAAW,MAAM,MAAM;AACrB,qBAAW;AAAA,QACb;AAAA,MACF;AAEA,aAAO,EAAE,KAAK,MAAM;AAAA,IACtB;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACzEjB;AAAA;AAMA,QAAI,UAAU;AACd,QAAI,aAAa;AACjB,QAAI,IAAI;AASR,aAAS,OAAQ,KAAK;AACpB,aAAQ,QAAQ,UAAa,QAAQ;AAAA,IACvC;AAQA,aAAS,kBAAmB,KAAK;AAC/B,UAAI,MAAM,CAAC;AACX,eAAS,KAAK,KAAK;AACjB,YAAI,CAAC,IAAI,IAAI,CAAC;AAAA,MAChB;AACA,aAAO;AAAA,IACT;AAWA,aAAS,UAAW,SAAS;AAC3B,gBAAU,kBAAkB,WAAW,CAAC,CAAC;AACzC,cAAQ,YAAY,QAAQ,aAAa,QAAQ;AACjD,cAAQ,SAAS,QAAQ,UAAU,QAAQ;AAC3C,cAAQ,eAAe,QAAQ,gBAAgB,QAAQ;AACvD,cAAQ,gBAAgB,QAAQ,iBAAiB,QAAQ;AACzD,WAAK,UAAU;AAAA,IACjB;AAEA,cAAU,UAAU,UAAU,SAAU,KAAK;AAE3C,YAAM,OAAO;AACb,YAAM,IAAI,SAAS;AACnB,UAAI,CAAC,IAAK,QAAO;AAEjB,UAAI,KAAK;AACT,UAAI,UAAU,GAAG;AACjB,UAAI,YAAY,QAAQ;AACxB,UAAI,SAAS,QAAQ;AACrB,UAAI,eAAe,QAAQ;AAC3B,UAAI,gBAAgB,QAAQ;AAE5B,UAAI,SAAS,WAAW,KAAK,SAAU,gBAAgB,UAAU,MAAM,OAAO,QAAQ;AAEpF,YAAI,QAAQ,UAAU,IAAI;AAC1B,YAAI,UAAU;AACd,YAAI,UAAU,KAAM,WAAU;AAAA,iBACrB,OAAO,UAAU,WAAY,WAAU,MAAM,KAAK;AAAA,iBAClD,iBAAiB,OAAQ,WAAU,MAAM,KAAK,KAAK;AAC5D,YAAI,YAAY,KAAM,WAAU;AAGhC,gBAAQ,cAAc,MAAM,KAAK;AACjC,YAAI,CAAC,MAAO;AAEZ,YAAI,OAAO;AAAA,UACT;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAEA,YAAI,SAAS;AAEX,cAAI,MAAM,OAAO,MAAM,OAAO,IAAI;AAClC,cAAI,OAAO,GAAG,GAAG;AACf,mBAAO,OAAO,MAAM;AAAA,UACtB,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QAEF,OAAO;AAEL,cAAI,MAAM,aAAa,MAAM,OAAO,IAAI;AACxC,cAAI,CAAC,OAAO,GAAG,GAAG;AAChB,mBAAO;AAAA,UACT;AAAA,QAEF;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAGA,WAAO,UAAU;AAAA;AAAA;;;AC7GjB;AAAA;AAMA,QAAI,UAAU;AACd,QAAI,YAAY;AAUhB,aAAS,UAAW,MAAM,SAAS;AACjC,UAAI,MAAM,IAAI,UAAU,OAAO;AAC/B,aAAO,IAAI,QAAQ,IAAI;AAAA,IACzB;AAIA,cAAU,OAAO,UAAU;AAC3B,YAAQ,YAAY;AACpB,SAAS,KAAK,QAAS,SAAQ,CAAC,IAAI,QAAQ,CAAC;AAApC;AAGT,QAAI,OAAO,WAAW,aAAa;AACjC,aAAO,YAAY,OAAO;AAAA,IAC5B;AAAA;AAAA;;;AC/BA,IAAAC,gBAAA;AAAA;AAAA,WAAO,UAAU;AAAA,MACf,SAAS,SAAU,KAAK,MAAM;AAC5B,YAAI,GAAG;AACP,YAAI,MAAM,UAAU,SAAS;AAC3B,iBAAO,IAAI,QAAQ,IAAI;AAAA,QACzB;AACA,aAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,KAAK;AACtC,cAAI,IAAI,CAAC,MAAM,MAAM;AACnB,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,MACA,SAAS,SAAU,KAAK,IAAI,OAAO;AACjC,YAAI,GAAG;AACP,YAAI,MAAM,UAAU,SAAS;AAC3B,iBAAO,IAAI,QAAQ,IAAI,KAAK;AAAA,QAC9B;AACA,aAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,KAAK;AACtC,aAAG,KAAK,OAAO,IAAI,CAAC,GAAG,GAAG,GAAG;AAAA,QAC/B;AAAA,MACF;AAAA,MACA,MAAM,SAAU,KAAK;AACnB,YAAI,OAAO,UAAU,MAAM;AACzB,iBAAO,IAAI,KAAK;AAAA,QAClB;AACA,eAAO,IAAI,QAAQ,kBAAkB,EAAE;AAAA,MACzC;AAAA,MACA,YAAY,SAAU,KAAK;AACzB,YAAI,MAAM;AACV,YAAI,QAAQ,IAAI,KAAK,GAAG;AACxB,eAAO,QAAQ,MAAM,QAAQ;AAAA,MAC/B;AAAA,IACF;AAAA;AAAA;;;ACjCA,IAAAC,mBAAA;AAAA;AAMA,QAAI,YAAY,cAAqB;AACrC,QAAI,yBAAyB,cAAqB;AAClD,QAAI,IAAI;AAER,aAAS,sBAAsB;AAC7B,aAAO;AAAA,QACL,GAAG,CAAC,UAAU,QAAQ,OAAO;AAAA,QAC7B,MAAM,CAAC,OAAO;AAAA,QACd,SAAS,CAAC;AAAA,QACV,MAAM,CAAC,SAAS,UAAU,QAAQ,KAAK;AAAA,QACvC,SAAS,CAAC;AAAA,QACV,OAAO,CAAC;AAAA,QACR,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,GAAG,CAAC;AAAA,QACJ,KAAK,CAAC,KAAK;AAAA,QACX,KAAK,CAAC,KAAK;AAAA,QACX,KAAK,CAAC;AAAA,QACN,YAAY,CAAC,MAAM;AAAA,QACnB,IAAI,CAAC;AAAA,QACL,SAAS,CAAC;AAAA,QACV,QAAQ,CAAC;AAAA,QACT,MAAM,CAAC;AAAA,QACP,MAAM,CAAC;AAAA,QACP,KAAK,CAAC,SAAS,UAAU,QAAQ,OAAO;AAAA,QACxC,UAAU,CAAC,SAAS,UAAU,QAAQ,OAAO;AAAA,QAC7C,IAAI,CAAC;AAAA,QACL,KAAK,CAAC,UAAU;AAAA,QAChB,SAAS,CAAC,MAAM;AAAA,QAChB,KAAK,CAAC;AAAA,QACN,IAAI,CAAC;AAAA,QACL,IAAI,CAAC;AAAA,QACL,IAAI,CAAC;AAAA,QACL,YAAY,CAAC;AAAA,QACb,QAAQ,CAAC;AAAA,QACT,MAAM,CAAC,SAAS,QAAQ,MAAM;AAAA,QAC9B,QAAQ,CAAC;AAAA,QACT,IAAI,CAAC;AAAA,QACL,IAAI,CAAC;AAAA,QACL,IAAI,CAAC;AAAA,QACL,IAAI,CAAC;AAAA,QACL,IAAI,CAAC;AAAA,QACL,IAAI,CAAC;AAAA,QACL,QAAQ,CAAC;AAAA,QACT,IAAI,CAAC;AAAA,QACL,GAAG,CAAC;AAAA,QACJ,KAAK,CAAC,OAAO,OAAO,SAAS,SAAS,UAAU,SAAS;AAAA,QACzD,KAAK,CAAC,UAAU;AAAA,QAChB,KAAK,CAAC;AAAA,QACN,IAAI,CAAC;AAAA,QACL,MAAM,CAAC;AAAA,QACP,KAAK,CAAC;AAAA,QACN,IAAI,CAAC;AAAA,QACL,GAAG,CAAC;AAAA,QACJ,KAAK,CAAC;AAAA,QACN,GAAG,CAAC;AAAA,QACJ,SAAS,CAAC;AAAA,QACV,OAAO,CAAC;AAAA,QACR,MAAM,CAAC;AAAA,QACP,KAAK,CAAC;AAAA,QACN,SAAS,CAAC;AAAA,QACV,KAAK,CAAC;AAAA,QACN,QAAQ,CAAC;AAAA,QACT,QAAQ,CAAC;AAAA,QACT,OAAO,CAAC,SAAS,UAAU,SAAS,QAAQ;AAAA,QAC5C,OAAO,CAAC,SAAS,QAAQ;AAAA,QACzB,IAAI,CAAC,SAAS,WAAW,WAAW,SAAS,QAAQ;AAAA,QACrD,OAAO,CAAC,SAAS,QAAQ;AAAA,QACzB,IAAI,CAAC,SAAS,WAAW,WAAW,SAAS,QAAQ;AAAA,QACrD,OAAO,CAAC,SAAS,QAAQ;AAAA,QACzB,IAAI,CAAC,WAAW,SAAS,QAAQ;AAAA,QACjC,IAAI,CAAC;AAAA,QACL,GAAG,CAAC;AAAA,QACJ,IAAI,CAAC;AAAA,QACL,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,QAAI,mBAAmB,IAAI,UAAU;AAUrC,aAAS,MAAM,KAAK,MAAM,SAAS;AAAA,IAEnC;AAUA,aAAS,YAAY,KAAK,MAAM,SAAS;AAAA,IAEzC;AAUA,aAAS,UAAU,KAAK,MAAM,OAAO;AAAA,IAErC;AAUA,aAAS,gBAAgB,KAAK,MAAM,OAAO;AAAA,IAE3C;AAOA,aAAS,WAAW,MAAM;AACxB,aAAO,KAAK,QAAQ,WAAW,MAAM,EAAE,QAAQ,WAAW,MAAM;AAAA,IAClE;AAWA,aAAS,cAAc,KAAK,MAAM,OAAO,WAAW;AAElD,cAAQ,kBAAkB,KAAK;AAE/B,UAAI,SAAS,UAAU,SAAS,OAAO;AAGrC,gBAAQ,EAAE,KAAK,KAAK;AACpB,YAAI,UAAU,IAAK,QAAO;AAC1B,YACE,EACE,MAAM,OAAO,GAAG,CAAC,MAAM,aACvB,MAAM,OAAO,GAAG,CAAC,MAAM,cACvB,MAAM,OAAO,GAAG,CAAC,MAAM,aACvB,MAAM,OAAO,GAAG,CAAC,MAAM,UACvB,MAAM,OAAO,GAAG,EAAE,MAAM,iBACxB,MAAM,OAAO,GAAG,CAAC,MAAM,YACvB,MAAM,OAAO,GAAG,CAAC,MAAM,QACvB,MAAM,OAAO,GAAG,CAAC,MAAM,SACvB,MAAM,CAAC,MAAM,OACb,MAAM,CAAC,MAAM,MAEf;AACA,iBAAO;AAAA,QACT;AAAA,MACF,WAAW,SAAS,cAAc;AAGhC,qCAA6B,YAAY;AACzC,YAAI,6BAA6B,KAAK,KAAK,GAAG;AAC5C,iBAAO;AAAA,QACT;AAAA,MACF,WAAW,SAAS,SAAS;AAE3B,qCAA6B,YAAY;AACzC,YAAI,6BAA6B,KAAK,KAAK,GAAG;AAC5C,iBAAO;AAAA,QACT;AAEA,qCAA6B,YAAY;AACzC,YAAI,6BAA6B,KAAK,KAAK,GAAG;AAC5C,uCAA6B,YAAY;AACzC,cAAI,6BAA6B,KAAK,KAAK,GAAG;AAC5C,mBAAO;AAAA,UACT;AAAA,QACF;AACA,YAAI,cAAc,OAAO;AACvB,sBAAY,aAAa;AACzB,kBAAQ,UAAU,QAAQ,KAAK;AAAA,QACjC;AAAA,MACF;AAGA,cAAQ,gBAAgB,KAAK;AAC7B,aAAO;AAAA,IACT;AAGA,QAAI,YAAY;AAChB,QAAI,YAAY;AAChB,QAAI,eAAe;AACnB,QAAI,iBAAiB;AACrB,QAAI,sBAAsB;AAC1B,QAAI,0BAA0B;AAC9B,QAAI,4BAA4B;AAEhC,QAAI,+BACF;AAGF,QAAI,+BACF;AACF,QAAI,+BAA+B;AAQnC,aAAS,YAAY,KAAK;AACxB,aAAO,IAAI,QAAQ,cAAc,QAAQ;AAAA,IAC3C;AAQA,aAAS,cAAc,KAAK;AAC1B,aAAO,IAAI,QAAQ,gBAAgB,GAAG;AAAA,IACxC;AAQA,aAAS,mBAAmB,KAAK;AAC/B,aAAO,IAAI,QAAQ,qBAAqB,SAAS,eAAeC,MAAK,MAAM;AACzE,eAAO,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM,MAClC,OAAO,aAAa,SAAS,KAAK,OAAO,CAAC,GAAG,EAAE,CAAC,IAChD,OAAO,aAAa,SAAS,MAAM,EAAE,CAAC;AAAA,MAC5C,CAAC;AAAA,IACH;AAQA,aAAS,0BAA0B,KAAK;AACtC,aAAO,IACJ,QAAQ,yBAAyB,GAAG,EACpC,QAAQ,2BAA2B,GAAG;AAAA,IAC3C;AAQA,aAAS,2BAA2B,KAAK;AACvC,UAAI,OAAO;AACX,eAAS,IAAI,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK;AAC9C,gBAAQ,IAAI,WAAW,CAAC,IAAI,KAAK,MAAM,IAAI,OAAO,CAAC;AAAA,MACrD;AACA,aAAO,EAAE,KAAK,IAAI;AAAA,IACpB;AAQA,aAAS,kBAAkB,KAAK;AAC9B,YAAM,cAAc,GAAG;AACvB,YAAM,mBAAmB,GAAG;AAC5B,YAAM,0BAA0B,GAAG;AACnC,YAAM,2BAA2B,GAAG;AACpC,aAAO;AAAA,IACT;AAQA,aAAS,gBAAgB,KAAK;AAC5B,YAAM,YAAY,GAAG;AACrB,YAAM,WAAW,GAAG;AACpB,aAAO;AAAA,IACT;AAKA,aAAS,sBAAsB;AAC7B,aAAO;AAAA,IACT;AASA,aAAS,aAAa,MAAM,MAAM;AAChC,UAAI,OAAO,SAAS,YAAY;AAC9B,eAAO,WAAY;AAAA,QAAC;AAAA,MACtB;AAEA,UAAI,iBAAiB,CAAC,MAAM,QAAQ,IAAI;AACxC,eAAS,YAAY,KAAK;AACxB,YAAI,eAAgB,QAAO;AAC3B,eAAO,EAAE,QAAQ,MAAM,GAAG,MAAM;AAAA,MAClC;AAEA,UAAI,aAAa,CAAC;AAClB,UAAI,WAAW;AAEf,aAAO;AAAA,QACL,aAAa,SAAU,KAAK,MAAM,SAAS;AACzC,cAAI,YAAY,GAAG,GAAG;AACpB,gBAAI,QAAQ,WAAW;AACrB,kBAAI,MAAM;AACV,kBAAI,MAAM,QAAQ,WAAW,IAAI;AACjC,yBAAW,KAAK;AAAA,gBACd,aAAa,QAAQ,WAAW,QAAQ;AAAA,gBACxC;AAAA,cACF,CAAC;AACD,yBAAW;AACX,qBAAO;AAAA,YACT,OAAO;AACL,kBAAI,CAAC,UAAU;AACb,2BAAW,QAAQ;AAAA,cACrB;AACA,qBAAO;AAAA,YACT;AAAA,UACF,OAAO;AACL,mBAAO,KAAK,KAAK,MAAM,OAAO;AAAA,UAChC;AAAA,QACF;AAAA,QACA,QAAQ,SAAU,MAAM;AACtB,cAAI,UAAU;AACd,cAAI,UAAU;AACd,YAAE,QAAQ,YAAY,SAAU,KAAK;AACnC,uBAAW,KAAK,MAAM,SAAS,IAAI,CAAC,CAAC;AACrC,sBAAU,IAAI,CAAC;AAAA,UACjB,CAAC;AACD,qBAAW,KAAK,MAAM,OAAO;AAC7B,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAQA,aAAS,gBAAgB,MAAM;AAC7B,UAAI,UAAU;AACd,UAAI,UAAU;AACd,aAAO,UAAU,KAAK,QAAQ;AAC5B,YAAI,IAAI,KAAK,QAAQ,QAAQ,OAAO;AACpC,YAAI,MAAM,IAAI;AACZ,qBAAW,KAAK,MAAM,OAAO;AAC7B;AAAA,QACF;AACA,mBAAW,KAAK,MAAM,SAAS,CAAC;AAChC,YAAI,IAAI,KAAK,QAAQ,OAAO,CAAC;AAC7B,YAAI,MAAM,IAAI;AACZ;AAAA,QACF;AACA,kBAAU,IAAI;AAAA,MAChB;AACA,aAAO;AAAA,IACT;AAQA,aAAS,eAAe,MAAM;AAC5B,UAAI,QAAQ,KAAK,MAAM,EAAE;AACzB,cAAQ,MAAM,OAAO,SAAU,MAAM;AACnC,YAAI,IAAI,KAAK,WAAW,CAAC;AACzB,YAAI,MAAM,IAAK,QAAO;AACtB,YAAI,KAAK,IAAI;AACX,cAAI,MAAM,MAAM,MAAM,GAAI,QAAO;AACjC,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,CAAC;AACD,aAAO,MAAM,KAAK,EAAE;AAAA,IACtB;AAEA,YAAQ,YAAY,oBAAoB;AACxC,YAAQ,sBAAsB;AAC9B,YAAQ,QAAQ;AAChB,YAAQ,cAAc;AACtB,YAAQ,YAAY;AACpB,YAAQ,kBAAkB;AAC1B,YAAQ,gBAAgB;AACxB,YAAQ,aAAa;AACrB,YAAQ,cAAc;AACtB,YAAQ,gBAAgB;AACxB,YAAQ,qBAAqB;AAC7B,YAAQ,4BAA4B;AACpC,YAAQ,6BAA6B;AACrC,YAAQ,oBAAoB;AAC5B,YAAQ,kBAAkB;AAC1B,YAAQ,sBAAsB;AAC9B,YAAQ,eAAe;AACvB,YAAQ,kBAAkB;AAC1B,YAAQ,iBAAiB;AACzB,YAAQ,oBAAoB;AAC5B,YAAQ,YAAY;AACpB,YAAQ,yBAAyB;AAAA;AAAA;;;AC5cjC,IAAAC,kBAAA;AAAA;AAMA,QAAI,IAAI;AAQR,aAAS,WAAW,MAAM;AACxB,UAAI,IAAI,EAAE,WAAW,IAAI;AACzB,UAAI;AACJ,UAAI,MAAM,IAAI;AACZ,kBAAU,KAAK,MAAM,GAAG,EAAE;AAAA,MAC5B,OAAO;AACL,kBAAU,KAAK,MAAM,GAAG,IAAI,CAAC;AAAA,MAC/B;AACA,gBAAU,EAAE,KAAK,OAAO,EAAE,YAAY;AACtC,UAAI,QAAQ,MAAM,GAAG,CAAC,MAAM,IAAK,WAAU,QAAQ,MAAM,CAAC;AAC1D,UAAI,QAAQ,MAAM,EAAE,MAAM,IAAK,WAAU,QAAQ,MAAM,GAAG,EAAE;AAC5D,aAAO;AAAA,IACT;AAQA,aAAS,UAAU,MAAM;AACvB,aAAO,KAAK,MAAM,GAAG,CAAC,MAAM;AAAA,IAC9B;AAUA,aAAS,SAAS,MAAM,OAAO,YAAY;AACzC;AAEA,UAAI,UAAU;AACd,UAAI,UAAU;AACd,UAAI,WAAW;AACf,UAAI,aAAa;AACjB,UAAI,aAAa;AACjB,UAAI,MAAM,KAAK;AACf,UAAI,iBAAiB;AACrB,UAAI,cAAc;AAElB,mBAAc,MAAK,aAAa,GAAG,aAAa,KAAK,cAAc;AACjE,YAAI,IAAI,KAAK,OAAO,UAAU;AAC9B,YAAI,aAAa,OAAO;AACtB,cAAI,MAAM,KAAK;AACb,uBAAW;AACX;AAAA,UACF;AAAA,QACF,OAAO;AACL,cAAI,eAAe,OAAO;AACxB,gBAAI,MAAM,KAAK;AACb,yBAAW,WAAW,KAAK,MAAM,SAAS,UAAU,CAAC;AACrD,yBAAW;AACX,wBAAU;AACV;AAAA,YACF;AACA,gBAAI,MAAM,OAAO,eAAe,MAAM,GAAG;AACvC,yBAAW,WAAW,KAAK,MAAM,SAAS,QAAQ,CAAC;AACnD,4BAAc,KAAK,MAAM,UAAU,aAAa,CAAC;AACjD,+BAAiB,WAAW,WAAW;AACvC,yBAAW;AAAA,gBACT;AAAA,gBACA,QAAQ;AAAA,gBACR;AAAA,gBACA;AAAA,gBACA,UAAU,WAAW;AAAA,cACvB;AACA,wBAAU,aAAa;AACvB,yBAAW;AACX;AAAA,YACF;AACA,gBAAI,MAAM,OAAO,MAAM,KAAK;AAC1B,kBAAI,IAAI;AACR,kBAAI,KAAK,KAAK,OAAO,aAAa,CAAC;AAEnC,qBAAO,GAAG,KAAK,MAAM,MAAM,OAAO,KAAK;AACrC,oBAAI,OAAO,KAAK;AACd,+BAAa;AACb,2BAAS;AAAA,gBACX;AACA,qBAAK,KAAK,OAAO,aAAa,EAAE,CAAC;AAAA,cACnC;AAAA,YACF;AAAA,UACF,OAAO;AACL,gBAAI,MAAM,YAAY;AACpB,2BAAa;AACb;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI,UAAU,KAAK;AACjB,mBAAW,WAAW,KAAK,OAAO,OAAO,CAAC;AAAA,MAC5C;AAEA,aAAO;AAAA,IACT;AAEA,QAAI,2BAA2B;AAS/B,aAAS,UAAU,MAAM,QAAQ;AAC/B;AAEA,UAAI,UAAU;AACd,UAAI,cAAc;AAClB,UAAI,WAAW,CAAC;AAChB,UAAI,UAAU;AACd,UAAI,MAAM,KAAK;AAEf,eAAS,QAAQ,MAAM,OAAO;AAC5B,eAAO,EAAE,KAAK,IAAI;AAClB,eAAO,KAAK,QAAQ,0BAA0B,EAAE,EAAE,YAAY;AAC9D,YAAI,KAAK,SAAS,EAAG;AACrB,YAAI,MAAM,OAAO,MAAM,SAAS,EAAE;AAClC,YAAI,IAAK,UAAS,KAAK,GAAG;AAAA,MAC5B;AAGA,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,YAAI,IAAI,KAAK,OAAO,CAAC;AACrB,YAAI,GAAG;AACP,YAAI,YAAY,SAAS,MAAM,KAAK;AAClC,oBAAU,KAAK,MAAM,SAAS,CAAC;AAC/B,oBAAU,IAAI;AACd,wBAAc,KAAK,OAAO,OAAO,MAAM,OAAO,KAAK,OAAO,OAAO,MAAM,MAAM,UAAU,sBAAsB,MAAM,IAAI,CAAC;AACxH;AAAA,QACF;AACA,YAAI,YAAY,OAAO;AACrB,cACE,MAAM,aACN;AACA,gBAAI,KAAK,QAAQ,GAAG,IAAI,CAAC;AACzB,gBAAI,MAAM,IAAI;AACZ;AAAA,YACF,OAAO;AACL,kBAAI,EAAE,KAAK,KAAK,MAAM,cAAc,GAAG,CAAC,CAAC;AACzC,sBAAQ,SAAS,CAAC;AAClB,wBAAU;AACV,kBAAI;AACJ,wBAAU,IAAI;AACd;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,WAAW,KAAK,CAAC,GAAG;AACtB,iBAAO,KAAK,QAAQ,aAAa,GAAG;AACpC,cAAI,YAAY,OAAO;AACrB,gBAAI,cAAc,MAAM,CAAC;AACzB,gBAAI,MAAM,IAAI;AACZ,kBAAI,EAAE,KAAK,KAAK,MAAM,SAAS,CAAC,CAAC;AACjC,sBAAQ,CAAC;AACT,wBAAU;AACV,wBAAU,IAAI;AACd;AAAA,YACF,OAAO;AACL,kBAAI,IAAI;AACR;AAAA,YACF;AAAA,UACF,OAAO;AACL,gBAAI,gBAAgB,MAAM,IAAI,CAAC;AAC/B,gBAAI,MAAM,IAAI;AACZ,kBAAI,EAAE,KAAK,KAAK,MAAM,SAAS,CAAC,CAAC;AACjC,kBAAI,eAAe,CAAC;AACpB,sBAAQ,SAAS,CAAC;AAClB,wBAAU;AACV,wBAAU,IAAI;AACd;AAAA,YACF,OAAO;AACL;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,UAAI,UAAU,KAAK,QAAQ;AACzB,YAAI,YAAY,OAAO;AACrB,kBAAQ,KAAK,MAAM,OAAO,CAAC;AAAA,QAC7B,OAAO;AACL,kBAAQ,SAAS,eAAe,EAAE,KAAK,KAAK,MAAM,OAAO,CAAC,CAAC,CAAC;AAAA,QAC9D;AAAA,MACF;AAEA,aAAO,EAAE,KAAK,SAAS,KAAK,GAAG,CAAC;AAAA,IAClC;AAEA,aAAS,cAAc,KAAK,GAAG;AAC7B,aAAO,IAAI,IAAI,QAAQ,KAAK;AAC1B,YAAI,IAAI,IAAI,CAAC;AACb,YAAI,MAAM,IAAK;AACf,YAAI,MAAM,IAAK,QAAO;AACtB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,sBAAsB,KAAK,GAAG;AACrC,aAAO,IAAI,IAAI,QAAQ,KAAK;AAC1B,YAAI,IAAI,IAAI,CAAC;AACb,YAAI,MAAM,IAAK;AACf,YAAI,MAAM,OAAO,MAAM,IAAK,QAAO;AACnC,eAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,gBAAgB,KAAK,GAAG;AAC/B,aAAO,IAAI,GAAG,KAAK;AACjB,YAAI,IAAI,IAAI,CAAC;AACb,YAAI,MAAM,IAAK;AACf,YAAI,MAAM,IAAK,QAAO;AACtB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,kBAAkB,MAAM;AAC/B,UACG,KAAK,CAAC,MAAM,OAAO,KAAK,KAAK,SAAS,CAAC,MAAM,OAC7C,KAAK,CAAC,MAAM,OAAO,KAAK,KAAK,SAAS,CAAC,MAAM,KAC9C;AACA,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,eAAe,MAAM;AAC5B,UAAI,kBAAkB,IAAI,GAAG;AAC3B,eAAO,KAAK,OAAO,GAAG,KAAK,SAAS,CAAC;AAAA,MACvC,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAEA,YAAQ,WAAW;AACnB,YAAQ,YAAY;AAAA;AAAA;;;AChQpB;AAAA;AAMA,QAAI,YAAY,cAAqB;AACrC,QAAI,UAAU;AACd,QAAI,SAAS;AACb,QAAI,WAAW,OAAO;AACtB,QAAI,YAAY,OAAO;AACvB,QAAI,IAAI;AAQR,aAAS,OAAO,KAAK;AACnB,aAAO,QAAQ,UAAa,QAAQ;AAAA,IACtC;AAUA,aAAS,SAAS,MAAM;AACtB,UAAI,IAAI,EAAE,WAAW,IAAI;AACzB,UAAI,MAAM,IAAI;AACZ,eAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS,KAAK,KAAK,SAAS,CAAC,MAAM;AAAA,QACrC;AAAA,MACF;AACA,aAAO,EAAE,KAAK,KAAK,MAAM,IAAI,GAAG,EAAE,CAAC;AACnC,UAAI,YAAY,KAAK,KAAK,SAAS,CAAC,MAAM;AAC1C,UAAI,UAAW,QAAO,EAAE,KAAK,KAAK,MAAM,GAAG,EAAE,CAAC;AAC9C,aAAO;AAAA,QACL;AAAA,QACA,SAAS;AAAA,MACX;AAAA,IACF;AAQA,aAAS,kBAAkB,KAAK;AAC9B,UAAI,MAAM,CAAC;AACX,eAAS,KAAK,KAAK;AACjB,YAAI,CAAC,IAAI,IAAI,CAAC;AAAA,MAChB;AACA,aAAO;AAAA,IACT;AAEA,aAAS,gBAAgB,KAAK;AAC5B,UAAI,MAAM,CAAC;AACX,eAAS,KAAK,KAAK;AACjB,YAAI,MAAM,QAAQ,IAAI,CAAC,CAAC,GAAG;AACzB,cAAI,EAAE,YAAY,CAAC,IAAI,IAAI,CAAC,EAAE,IAAI,SAAU,MAAM;AAChD,mBAAO,KAAK,YAAY;AAAA,UAC1B,CAAC;AAAA,QACH,OAAO;AACL,cAAI,EAAE,YAAY,CAAC,IAAI,IAAI,CAAC;AAAA,QAC9B;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAWA,aAAS,UAAU,SAAS;AAC1B,gBAAU,kBAAkB,WAAW,CAAC,CAAC;AAEzC,UAAI,QAAQ,gBAAgB;AAC1B,YAAI,QAAQ,aAAa;AACvB,kBAAQ;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,gBAAQ,cAAc,QAAQ;AAAA,MAChC;AACA,UAAI,QAAQ,aAAa,QAAQ,WAAW;AAC1C,gBAAQ,YAAY,gBAAgB,QAAQ,aAAa,QAAQ,SAAS;AAAA,MAC5E,OAAO;AACL,gBAAQ,YAAY,QAAQ;AAAA,MAC9B;AAEA,WAAK,oBAAoB,QAAQ,+BAA+B,OAAO,MAAM,QAAQ;AAErF,cAAQ,QAAQ,QAAQ,SAAS,QAAQ;AACzC,cAAQ,YAAY,QAAQ,aAAa,QAAQ;AACjD,cAAQ,cAAc,QAAQ,eAAe,QAAQ;AACrD,cAAQ,kBAAkB,QAAQ,mBAAmB,QAAQ;AAC7D,cAAQ,gBAAgB,QAAQ,iBAAiB,QAAQ;AACzD,cAAQ,aAAa,QAAQ,cAAc,QAAQ;AACnD,WAAK,UAAU;AAEf,UAAI,QAAQ,QAAQ,OAAO;AACzB,aAAK,YAAY;AAAA,MACnB,OAAO;AACL,gBAAQ,MAAM,QAAQ,OAAO,CAAC;AAC9B,aAAK,YAAY,IAAI,UAAU,QAAQ,GAAG;AAAA,MAC5C;AAAA,IACF;AAQA,cAAU,UAAU,UAAU,SAAU,MAAM;AAE5C,aAAO,QAAQ;AACf,aAAO,KAAK,SAAS;AACrB,UAAI,CAAC,KAAM,QAAO;AAElB,UAAI,KAAK;AACT,UAAI,UAAU,GAAG;AACjB,UAAI,YAAY,QAAQ;AACxB,UAAI,QAAQ,QAAQ;AACpB,UAAI,cAAc,QAAQ;AAC1B,UAAI,YAAY,QAAQ;AACxB,UAAI,kBAAkB,QAAQ;AAC9B,UAAI,gBAAgB,QAAQ;AAC5B,UAAI,aAAa,QAAQ;AACzB,UAAI,oBAAoB,GAAG;AAC3B,UAAI,YAAY,GAAG;AAGnB,UAAI,QAAQ,gBAAgB;AAC1B,eAAO,QAAQ,eAAe,IAAI;AAAA,MACpC;AAGA,UAAI,CAAC,QAAQ,iBAAiB;AAC5B,eAAO,QAAQ,gBAAgB,IAAI;AAAA,MACrC;AAGA,UAAI,qBAAqB;AACzB,UAAI,QAAQ,oBAAoB;AAC9B,6BAAqB,QAAQ;AAAA,UAC3B,QAAQ;AAAA,UACR;AAAA,QACF;AACA,sBAAc,mBAAmB;AAAA,MACnC;AAEA,UAAI,UAAU;AAAA,QACZ;AAAA,QACA,SAAU,gBAAgB,UAAU,KAAKC,OAAM,WAAW;AACxD,cAAI,OAAO;AAAA,YACT;AAAA,YACA;AAAA,YACA;AAAA,YACA,SAAS,OAAO,UAAU,eAAe,KAAK,WAAW,GAAG;AAAA,UAC9D;AAGA,cAAI,MAAM,MAAM,KAAKA,OAAM,IAAI;AAC/B,cAAI,CAAC,OAAO,GAAG,EAAG,QAAO;AAEzB,cAAI,KAAK,SAAS;AAChB,gBAAI,KAAK,WAAW;AAClB,qBAAO,OAAO,MAAM;AAAA,YACtB;AAEA,gBAAI,QAAQ,SAASA,KAAI;AACzB,gBAAI,gBAAgB,UAAU,GAAG;AACjC,gBAAI,YAAY,UAAU,MAAM,MAAM,SAAU,MAAM,OAAO;AAE3D,kBAAI,cAAc,EAAE,QAAQ,eAAe,IAAI,MAAM;AACrD,kBAAIC,OAAM,UAAU,KAAK,MAAM,OAAO,WAAW;AACjD,kBAAI,CAAC,OAAOA,IAAG,EAAG,QAAOA;AAEzB,kBAAI,aAAa;AAEf,wBAAQ,cAAc,KAAK,MAAM,OAAO,SAAS;AACjD,oBAAI,OAAO;AACT,yBAAO,OAAO,MAAM,oBAAoB,QAAQ;AAAA,gBAClD,OAAO;AACL,yBAAO;AAAA,gBACT;AAAA,cACF,OAAO;AAEL,gBAAAA,OAAM,gBAAgB,KAAK,MAAM,OAAO,WAAW;AACnD,oBAAI,CAAC,OAAOA,IAAG,EAAG,QAAOA;AACzB;AAAA,cACF;AAAA,YACF,CAAC;AAGD,YAAAD,QAAO,MAAM;AACb,gBAAI,UAAW,CAAAA,SAAQ,MAAM;AAC7B,gBAAI,MAAM,QAAS,CAAAA,SAAQ;AAC3B,YAAAA,SAAQ;AACR,mBAAOA;AAAA,UACT,OAAO;AAEL,kBAAM,YAAY,KAAKA,OAAM,IAAI;AACjC,gBAAI,CAAC,OAAO,GAAG,EAAG,QAAO;AACzB,mBAAO,WAAWA,KAAI;AAAA,UACxB;AAAA,QACF;AAAA,QACA;AAAA,MACF;AAGA,UAAI,oBAAoB;AACtB,kBAAU,mBAAmB,OAAO,OAAO;AAAA,MAC7C;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACvOjB,IAAAE,eAAA;AAAA;AAMA,QAAI,UAAU;AACd,QAAI,SAAS;AACb,QAAI,YAAY;AAShB,aAAS,UAAU,MAAM,SAAS;AAChC,UAAI,MAAM,IAAI,UAAU,OAAO;AAC/B,aAAO,IAAI,QAAQ,IAAI;AAAA,IACzB;AAEA,cAAU,OAAO,UAAU;AAC3B,YAAQ,YAAY;AACpB,YAAQ,YAAY;AAEpB,KAAC,WAAY;AACX,eAAS,KAAK,SAAS;AACrB,gBAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,MACxB;AACA,eAAS,KAAK,QAAQ;AACpB,gBAAQ,CAAC,IAAI,OAAO,CAAC;AAAA,MACvB;AAAA,IACF,GAAG;AAGH,QAAI,OAAO,WAAW,aAAa;AACjC,aAAO,YAAY,OAAO;AAAA,IAC5B;AAGA,aAAS,cAAc;AACrB,aACE,OAAO,SAAS,eAChB,OAAO,+BAA+B,eACtC,gBAAgB;AAAA,IAEpB;AACA,QAAI,YAAY,GAAG;AACjB,WAAK,YAAY,OAAO;AAAA,IAC1B;AAAA;AAAA;", "names": ["j", "require_util", "require_default", "str", "require_parser", "html", "ret", "require_lib"]}