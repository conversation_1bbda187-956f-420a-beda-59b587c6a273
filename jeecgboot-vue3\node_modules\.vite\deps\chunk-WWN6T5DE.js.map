{"version": 3, "sources": ["../../.pnpm/codemirror@5.65.18/node_modules/codemirror/mode/xml/xml.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n\"use strict\";\n\nvar htmlConfig = {\n  autoSelfClosers: {'area': true, 'base': true, 'br': true, 'col': true, 'command': true,\n                    'embed': true, 'frame': true, 'hr': true, 'img': true, 'input': true,\n                    'keygen': true, 'link': true, 'meta': true, 'param': true, 'source': true,\n                    'track': true, 'wbr': true, 'menuitem': true},\n  implicitlyClosed: {'dd': true, 'li': true, 'optgroup': true, 'option': true, 'p': true,\n                     'rp': true, 'rt': true, 'tbody': true, 'td': true, 'tfoot': true,\n                     'th': true, 'tr': true},\n  contextGrabbers: {\n    'dd': {'dd': true, 'dt': true},\n    'dt': {'dd': true, 'dt': true},\n    'li': {'li': true},\n    'option': {'option': true, 'optgroup': true},\n    'optgroup': {'optgroup': true},\n    'p': {'address': true, 'article': true, 'aside': true, 'blockquote': true, 'dir': true,\n          'div': true, 'dl': true, 'fieldset': true, 'footer': true, 'form': true,\n          'h1': true, 'h2': true, 'h3': true, 'h4': true, 'h5': true, 'h6': true,\n          'header': true, 'hgroup': true, 'hr': true, 'menu': true, 'nav': true, 'ol': true,\n          'p': true, 'pre': true, 'section': true, 'table': true, 'ul': true},\n    'rp': {'rp': true, 'rt': true},\n    'rt': {'rp': true, 'rt': true},\n    'tbody': {'tbody': true, 'tfoot': true},\n    'td': {'td': true, 'th': true},\n    'tfoot': {'tbody': true},\n    'th': {'td': true, 'th': true},\n    'thead': {'tbody': true, 'tfoot': true},\n    'tr': {'tr': true}\n  },\n  doNotIndent: {\"pre\": true},\n  allowUnquoted: true,\n  allowMissing: true,\n  caseFold: true\n}\n\nvar xmlConfig = {\n  autoSelfClosers: {},\n  implicitlyClosed: {},\n  contextGrabbers: {},\n  doNotIndent: {},\n  allowUnquoted: false,\n  allowMissing: false,\n  allowMissingTagName: false,\n  caseFold: false\n}\n\nCodeMirror.defineMode(\"xml\", function(editorConf, config_) {\n  var indentUnit = editorConf.indentUnit\n  var config = {}\n  var defaults = config_.htmlMode ? htmlConfig : xmlConfig\n  for (var prop in defaults) config[prop] = defaults[prop]\n  for (var prop in config_) config[prop] = config_[prop]\n\n  // Return variables for tokenizers\n  var type, setStyle;\n\n  function inText(stream, state) {\n    function chain(parser) {\n      state.tokenize = parser;\n      return parser(stream, state);\n    }\n\n    var ch = stream.next();\n    if (ch == \"<\") {\n      if (stream.eat(\"!\")) {\n        if (stream.eat(\"[\")) {\n          if (stream.match(\"CDATA[\")) return chain(inBlock(\"atom\", \"]]>\"));\n          else return null;\n        } else if (stream.match(\"--\")) {\n          return chain(inBlock(\"comment\", \"-->\"));\n        } else if (stream.match(\"DOCTYPE\", true, true)) {\n          stream.eatWhile(/[\\w\\._\\-]/);\n          return chain(doctype(1));\n        } else {\n          return null;\n        }\n      } else if (stream.eat(\"?\")) {\n        stream.eatWhile(/[\\w\\._\\-]/);\n        state.tokenize = inBlock(\"meta\", \"?>\");\n        return \"meta\";\n      } else {\n        type = stream.eat(\"/\") ? \"closeTag\" : \"openTag\";\n        state.tokenize = inTag;\n        return \"tag bracket\";\n      }\n    } else if (ch == \"&\") {\n      var ok;\n      if (stream.eat(\"#\")) {\n        if (stream.eat(\"x\")) {\n          ok = stream.eatWhile(/[a-fA-F\\d]/) && stream.eat(\";\");\n        } else {\n          ok = stream.eatWhile(/[\\d]/) && stream.eat(\";\");\n        }\n      } else {\n        ok = stream.eatWhile(/[\\w\\.\\-:]/) && stream.eat(\";\");\n      }\n      return ok ? \"atom\" : \"error\";\n    } else {\n      stream.eatWhile(/[^&<]/);\n      return null;\n    }\n  }\n  inText.isInText = true;\n\n  function inTag(stream, state) {\n    var ch = stream.next();\n    if (ch == \">\" || (ch == \"/\" && stream.eat(\">\"))) {\n      state.tokenize = inText;\n      type = ch == \">\" ? \"endTag\" : \"selfcloseTag\";\n      return \"tag bracket\";\n    } else if (ch == \"=\") {\n      type = \"equals\";\n      return null;\n    } else if (ch == \"<\") {\n      state.tokenize = inText;\n      state.state = baseState;\n      state.tagName = state.tagStart = null;\n      var next = state.tokenize(stream, state);\n      return next ? next + \" tag error\" : \"tag error\";\n    } else if (/[\\'\\\"]/.test(ch)) {\n      state.tokenize = inAttribute(ch);\n      state.stringStartCol = stream.column();\n      return state.tokenize(stream, state);\n    } else {\n      stream.match(/^[^\\s\\u00a0=<>\\\"\\']*[^\\s\\u00a0=<>\\\"\\'\\/]/);\n      return \"word\";\n    }\n  }\n\n  function inAttribute(quote) {\n    var closure = function(stream, state) {\n      while (!stream.eol()) {\n        if (stream.next() == quote) {\n          state.tokenize = inTag;\n          break;\n        }\n      }\n      return \"string\";\n    };\n    closure.isInAttribute = true;\n    return closure;\n  }\n\n  function inBlock(style, terminator) {\n    return function(stream, state) {\n      while (!stream.eol()) {\n        if (stream.match(terminator)) {\n          state.tokenize = inText;\n          break;\n        }\n        stream.next();\n      }\n      return style;\n    }\n  }\n\n  function doctype(depth) {\n    return function(stream, state) {\n      var ch;\n      while ((ch = stream.next()) != null) {\n        if (ch == \"<\") {\n          state.tokenize = doctype(depth + 1);\n          return state.tokenize(stream, state);\n        } else if (ch == \">\") {\n          if (depth == 1) {\n            state.tokenize = inText;\n            break;\n          } else {\n            state.tokenize = doctype(depth - 1);\n            return state.tokenize(stream, state);\n          }\n        }\n      }\n      return \"meta\";\n    };\n  }\n\n  function lower(tagName) {\n    return tagName && tagName.toLowerCase();\n  }\n\n  function Context(state, tagName, startOfLine) {\n    this.prev = state.context;\n    this.tagName = tagName || \"\";\n    this.indent = state.indented;\n    this.startOfLine = startOfLine;\n    if (config.doNotIndent.hasOwnProperty(tagName) || (state.context && state.context.noIndent))\n      this.noIndent = true;\n  }\n  function popContext(state) {\n    if (state.context) state.context = state.context.prev;\n  }\n  function maybePopContext(state, nextTagName) {\n    var parentTagName;\n    while (true) {\n      if (!state.context) {\n        return;\n      }\n      parentTagName = state.context.tagName;\n      if (!config.contextGrabbers.hasOwnProperty(lower(parentTagName)) ||\n          !config.contextGrabbers[lower(parentTagName)].hasOwnProperty(lower(nextTagName))) {\n        return;\n      }\n      popContext(state);\n    }\n  }\n\n  function baseState(type, stream, state) {\n    if (type == \"openTag\") {\n      state.tagStart = stream.column();\n      return tagNameState;\n    } else if (type == \"closeTag\") {\n      return closeTagNameState;\n    } else {\n      return baseState;\n    }\n  }\n  function tagNameState(type, stream, state) {\n    if (type == \"word\") {\n      state.tagName = stream.current();\n      setStyle = \"tag\";\n      return attrState;\n    } else if (config.allowMissingTagName && type == \"endTag\") {\n      setStyle = \"tag bracket\";\n      return attrState(type, stream, state);\n    } else {\n      setStyle = \"error\";\n      return tagNameState;\n    }\n  }\n  function closeTagNameState(type, stream, state) {\n    if (type == \"word\") {\n      var tagName = stream.current();\n      if (state.context && state.context.tagName != tagName &&\n          config.implicitlyClosed.hasOwnProperty(lower(state.context.tagName)))\n        popContext(state);\n      if ((state.context && state.context.tagName == tagName) || config.matchClosing === false) {\n        setStyle = \"tag\";\n        return closeState;\n      } else {\n        setStyle = \"tag error\";\n        return closeStateErr;\n      }\n    } else if (config.allowMissingTagName && type == \"endTag\") {\n      setStyle = \"tag bracket\";\n      return closeState(type, stream, state);\n    } else {\n      setStyle = \"error\";\n      return closeStateErr;\n    }\n  }\n\n  function closeState(type, _stream, state) {\n    if (type != \"endTag\") {\n      setStyle = \"error\";\n      return closeState;\n    }\n    popContext(state);\n    return baseState;\n  }\n  function closeStateErr(type, stream, state) {\n    setStyle = \"error\";\n    return closeState(type, stream, state);\n  }\n\n  function attrState(type, _stream, state) {\n    if (type == \"word\") {\n      setStyle = \"attribute\";\n      return attrEqState;\n    } else if (type == \"endTag\" || type == \"selfcloseTag\") {\n      var tagName = state.tagName, tagStart = state.tagStart;\n      state.tagName = state.tagStart = null;\n      if (type == \"selfcloseTag\" ||\n          config.autoSelfClosers.hasOwnProperty(lower(tagName))) {\n        maybePopContext(state, tagName);\n      } else {\n        maybePopContext(state, tagName);\n        state.context = new Context(state, tagName, tagStart == state.indented);\n      }\n      return baseState;\n    }\n    setStyle = \"error\";\n    return attrState;\n  }\n  function attrEqState(type, stream, state) {\n    if (type == \"equals\") return attrValueState;\n    if (!config.allowMissing) setStyle = \"error\";\n    return attrState(type, stream, state);\n  }\n  function attrValueState(type, stream, state) {\n    if (type == \"string\") return attrContinuedState;\n    if (type == \"word\" && config.allowUnquoted) {setStyle = \"string\"; return attrState;}\n    setStyle = \"error\";\n    return attrState(type, stream, state);\n  }\n  function attrContinuedState(type, stream, state) {\n    if (type == \"string\") return attrContinuedState;\n    return attrState(type, stream, state);\n  }\n\n  return {\n    startState: function(baseIndent) {\n      var state = {tokenize: inText,\n                   state: baseState,\n                   indented: baseIndent || 0,\n                   tagName: null, tagStart: null,\n                   context: null}\n      if (baseIndent != null) state.baseIndent = baseIndent\n      return state\n    },\n\n    token: function(stream, state) {\n      if (!state.tagName && stream.sol())\n        state.indented = stream.indentation();\n\n      if (stream.eatSpace()) return null;\n      type = null;\n      var style = state.tokenize(stream, state);\n      if ((style || type) && style != \"comment\") {\n        setStyle = null;\n        state.state = state.state(type || style, stream, state);\n        if (setStyle)\n          style = setStyle == \"error\" ? style + \" error\" : setStyle;\n      }\n      return style;\n    },\n\n    indent: function(state, textAfter, fullLine) {\n      var context = state.context;\n      // Indent multi-line strings (e.g. css).\n      if (state.tokenize.isInAttribute) {\n        if (state.tagStart == state.indented)\n          return state.stringStartCol + 1;\n        else\n          return state.indented + indentUnit;\n      }\n      if (context && context.noIndent) return CodeMirror.Pass;\n      if (state.tokenize != inTag && state.tokenize != inText)\n        return fullLine ? fullLine.match(/^(\\s*)/)[0].length : 0;\n      // Indent the starts of attribute names.\n      if (state.tagName) {\n        if (config.multilineTagIndentPastTag !== false)\n          return state.tagStart + state.tagName.length + 2;\n        else\n          return state.tagStart + indentUnit * (config.multilineTagIndentFactor || 1);\n      }\n      if (config.alignCDATA && /<!\\[CDATA\\[/.test(textAfter)) return 0;\n      var tagAfter = textAfter && /^<(\\/)?([\\w_:\\.-]*)/.exec(textAfter);\n      if (tagAfter && tagAfter[1]) { // Closing tag spotted\n        while (context) {\n          if (context.tagName == tagAfter[2]) {\n            context = context.prev;\n            break;\n          } else if (config.implicitlyClosed.hasOwnProperty(lower(context.tagName))) {\n            context = context.prev;\n          } else {\n            break;\n          }\n        }\n      } else if (tagAfter) { // Opening tag spotted\n        while (context) {\n          var grabbers = config.contextGrabbers[lower(context.tagName)];\n          if (grabbers && grabbers.hasOwnProperty(lower(tagAfter[2])))\n            context = context.prev;\n          else\n            break;\n        }\n      }\n      while (context && context.prev && !context.startOfLine)\n        context = context.prev;\n      if (context) return context.indent + indentUnit;\n      else return state.baseIndent || 0;\n    },\n\n    electricInput: /<\\/[\\s\\w:]+>$/,\n    blockCommentStart: \"<!--\",\n    blockCommentEnd: \"-->\",\n\n    configuration: config.htmlMode ? \"html\" : \"xml\",\n    helperType: config.htmlMode ? \"html\" : \"xml\",\n\n    skipAttribute: function(state) {\n      if (state.state == attrValueState)\n        state.state = attrState\n    },\n\n    xmlCurrentTag: function(state) {\n      return state.tagName ? {name: state.tagName, close: state.type == \"closeTag\"} : null\n    },\n\n    xmlCurrentContext: function(state) {\n      var context = []\n      for (var cx = state.context; cx; cx = cx.prev)\n        context.push(cx.tagName)\n      return context.reverse()\n    }\n  };\n});\n\nCodeMirror.defineMIME(\"text/xml\", \"xml\");\nCodeMirror.defineMIME(\"application/xml\", \"xml\");\nif (!CodeMirror.mimeModes.hasOwnProperty(\"text/html\"))\n  CodeMirror.defineMIME(\"text/html\", {name: \"xml\", htmlMode: true});\n\n});\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACxB;AAEA,UAAI,aAAa;AAAA,QACf,iBAAiB;AAAA,UAAC,QAAQ;AAAA,UAAM,QAAQ;AAAA,UAAM,MAAM;AAAA,UAAM,OAAO;AAAA,UAAM,WAAW;AAAA,UAChE,SAAS;AAAA,UAAM,SAAS;AAAA,UAAM,MAAM;AAAA,UAAM,OAAO;AAAA,UAAM,SAAS;AAAA,UAChE,UAAU;AAAA,UAAM,QAAQ;AAAA,UAAM,QAAQ;AAAA,UAAM,SAAS;AAAA,UAAM,UAAU;AAAA,UACrE,SAAS;AAAA,UAAM,OAAO;AAAA,UAAM,YAAY;AAAA,QAAI;AAAA,QAC9D,kBAAkB;AAAA,UAAC,MAAM;AAAA,UAAM,MAAM;AAAA,UAAM,YAAY;AAAA,UAAM,UAAU;AAAA,UAAM,KAAK;AAAA,UAC/D,MAAM;AAAA,UAAM,MAAM;AAAA,UAAM,SAAS;AAAA,UAAM,MAAM;AAAA,UAAM,SAAS;AAAA,UAC5D,MAAM;AAAA,UAAM,MAAM;AAAA,QAAI;AAAA,QACzC,iBAAiB;AAAA,UACf,MAAM,EAAC,MAAM,MAAM,MAAM,KAAI;AAAA,UAC7B,MAAM,EAAC,MAAM,MAAM,MAAM,KAAI;AAAA,UAC7B,MAAM,EAAC,MAAM,KAAI;AAAA,UACjB,UAAU,EAAC,UAAU,MAAM,YAAY,KAAI;AAAA,UAC3C,YAAY,EAAC,YAAY,KAAI;AAAA,UAC7B,KAAK;AAAA,YAAC,WAAW;AAAA,YAAM,WAAW;AAAA,YAAM,SAAS;AAAA,YAAM,cAAc;AAAA,YAAM,OAAO;AAAA,YAC5E,OAAO;AAAA,YAAM,MAAM;AAAA,YAAM,YAAY;AAAA,YAAM,UAAU;AAAA,YAAM,QAAQ;AAAA,YACnE,MAAM;AAAA,YAAM,MAAM;AAAA,YAAM,MAAM;AAAA,YAAM,MAAM;AAAA,YAAM,MAAM;AAAA,YAAM,MAAM;AAAA,YAClE,UAAU;AAAA,YAAM,UAAU;AAAA,YAAM,MAAM;AAAA,YAAM,QAAQ;AAAA,YAAM,OAAO;AAAA,YAAM,MAAM;AAAA,YAC7E,KAAK;AAAA,YAAM,OAAO;AAAA,YAAM,WAAW;AAAA,YAAM,SAAS;AAAA,YAAM,MAAM;AAAA,UAAI;AAAA,UACxE,MAAM,EAAC,MAAM,MAAM,MAAM,KAAI;AAAA,UAC7B,MAAM,EAAC,MAAM,MAAM,MAAM,KAAI;AAAA,UAC7B,SAAS,EAAC,SAAS,MAAM,SAAS,KAAI;AAAA,UACtC,MAAM,EAAC,MAAM,MAAM,MAAM,KAAI;AAAA,UAC7B,SAAS,EAAC,SAAS,KAAI;AAAA,UACvB,MAAM,EAAC,MAAM,MAAM,MAAM,KAAI;AAAA,UAC7B,SAAS,EAAC,SAAS,MAAM,SAAS,KAAI;AAAA,UACtC,MAAM,EAAC,MAAM,KAAI;AAAA,QACnB;AAAA,QACA,aAAa,EAAC,OAAO,KAAI;AAAA,QACzB,eAAe;AAAA,QACf,cAAc;AAAA,QACd,UAAU;AAAA,MACZ;AAEA,UAAI,YAAY;AAAA,QACd,iBAAiB,CAAC;AAAA,QAClB,kBAAkB,CAAC;AAAA,QACnB,iBAAiB,CAAC;AAAA,QAClB,aAAa,CAAC;AAAA,QACd,eAAe;AAAA,QACf,cAAc;AAAA,QACd,qBAAqB;AAAA,QACrB,UAAU;AAAA,MACZ;AAEA,MAAAA,YAAW,WAAW,OAAO,SAAS,YAAY,SAAS;AACzD,YAAI,aAAa,WAAW;AAC5B,YAAI,SAAS,CAAC;AACd,YAAI,WAAW,QAAQ,WAAW,aAAa;AAC/C,iBAAS,QAAQ,SAAU,QAAO,IAAI,IAAI,SAAS,IAAI;AACvD,iBAAS,QAAQ,QAAS,QAAO,IAAI,IAAI,QAAQ,IAAI;AAGrD,YAAI,MAAM;AAEV,iBAAS,OAAO,QAAQ,OAAO;AAC7B,mBAAS,MAAM,QAAQ;AACrB,kBAAM,WAAW;AACjB,mBAAO,OAAO,QAAQ,KAAK;AAAA,UAC7B;AAEA,cAAI,KAAK,OAAO,KAAK;AACrB,cAAI,MAAM,KAAK;AACb,gBAAI,OAAO,IAAI,GAAG,GAAG;AACnB,kBAAI,OAAO,IAAI,GAAG,GAAG;AACnB,oBAAI,OAAO,MAAM,QAAQ,EAAG,QAAO,MAAM,QAAQ,QAAQ,KAAK,CAAC;AAAA,oBAC1D,QAAO;AAAA,cACd,WAAW,OAAO,MAAM,IAAI,GAAG;AAC7B,uBAAO,MAAM,QAAQ,WAAW,KAAK,CAAC;AAAA,cACxC,WAAW,OAAO,MAAM,WAAW,MAAM,IAAI,GAAG;AAC9C,uBAAO,SAAS,WAAW;AAC3B,uBAAO,MAAM,QAAQ,CAAC,CAAC;AAAA,cACzB,OAAO;AACL,uBAAO;AAAA,cACT;AAAA,YACF,WAAW,OAAO,IAAI,GAAG,GAAG;AAC1B,qBAAO,SAAS,WAAW;AAC3B,oBAAM,WAAW,QAAQ,QAAQ,IAAI;AACrC,qBAAO;AAAA,YACT,OAAO;AACL,qBAAO,OAAO,IAAI,GAAG,IAAI,aAAa;AACtC,oBAAM,WAAW;AACjB,qBAAO;AAAA,YACT;AAAA,UACF,WAAW,MAAM,KAAK;AACpB,gBAAI;AACJ,gBAAI,OAAO,IAAI,GAAG,GAAG;AACnB,kBAAI,OAAO,IAAI,GAAG,GAAG;AACnB,qBAAK,OAAO,SAAS,YAAY,KAAK,OAAO,IAAI,GAAG;AAAA,cACtD,OAAO;AACL,qBAAK,OAAO,SAAS,MAAM,KAAK,OAAO,IAAI,GAAG;AAAA,cAChD;AAAA,YACF,OAAO;AACL,mBAAK,OAAO,SAAS,WAAW,KAAK,OAAO,IAAI,GAAG;AAAA,YACrD;AACA,mBAAO,KAAK,SAAS;AAAA,UACvB,OAAO;AACL,mBAAO,SAAS,OAAO;AACvB,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO,WAAW;AAElB,iBAAS,MAAM,QAAQ,OAAO;AAC5B,cAAI,KAAK,OAAO,KAAK;AACrB,cAAI,MAAM,OAAQ,MAAM,OAAO,OAAO,IAAI,GAAG,GAAI;AAC/C,kBAAM,WAAW;AACjB,mBAAO,MAAM,MAAM,WAAW;AAC9B,mBAAO;AAAA,UACT,WAAW,MAAM,KAAK;AACpB,mBAAO;AACP,mBAAO;AAAA,UACT,WAAW,MAAM,KAAK;AACpB,kBAAM,WAAW;AACjB,kBAAM,QAAQ;AACd,kBAAM,UAAU,MAAM,WAAW;AACjC,gBAAI,OAAO,MAAM,SAAS,QAAQ,KAAK;AACvC,mBAAO,OAAO,OAAO,eAAe;AAAA,UACtC,WAAW,SAAS,KAAK,EAAE,GAAG;AAC5B,kBAAM,WAAW,YAAY,EAAE;AAC/B,kBAAM,iBAAiB,OAAO,OAAO;AACrC,mBAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,UACrC,OAAO;AACL,mBAAO,MAAM,0CAA0C;AACvD,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,iBAAS,YAAY,OAAO;AAC1B,cAAI,UAAU,SAAS,QAAQ,OAAO;AACpC,mBAAO,CAAC,OAAO,IAAI,GAAG;AACpB,kBAAI,OAAO,KAAK,KAAK,OAAO;AAC1B,sBAAM,WAAW;AACjB;AAAA,cACF;AAAA,YACF;AACA,mBAAO;AAAA,UACT;AACA,kBAAQ,gBAAgB;AACxB,iBAAO;AAAA,QACT;AAEA,iBAAS,QAAQ,OAAO,YAAY;AAClC,iBAAO,SAAS,QAAQ,OAAO;AAC7B,mBAAO,CAAC,OAAO,IAAI,GAAG;AACpB,kBAAI,OAAO,MAAM,UAAU,GAAG;AAC5B,sBAAM,WAAW;AACjB;AAAA,cACF;AACA,qBAAO,KAAK;AAAA,YACd;AACA,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,iBAAS,QAAQ,OAAO;AACtB,iBAAO,SAAS,QAAQ,OAAO;AAC7B,gBAAI;AACJ,oBAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,kBAAI,MAAM,KAAK;AACb,sBAAM,WAAW,QAAQ,QAAQ,CAAC;AAClC,uBAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,cACrC,WAAW,MAAM,KAAK;AACpB,oBAAI,SAAS,GAAG;AACd,wBAAM,WAAW;AACjB;AAAA,gBACF,OAAO;AACL,wBAAM,WAAW,QAAQ,QAAQ,CAAC;AAClC,yBAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,gBACrC;AAAA,cACF;AAAA,YACF;AACA,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,iBAAS,MAAM,SAAS;AACtB,iBAAO,WAAW,QAAQ,YAAY;AAAA,QACxC;AAEA,iBAAS,QAAQ,OAAO,SAAS,aAAa;AAC5C,eAAK,OAAO,MAAM;AAClB,eAAK,UAAU,WAAW;AAC1B,eAAK,SAAS,MAAM;AACpB,eAAK,cAAc;AACnB,cAAI,OAAO,YAAY,eAAe,OAAO,KAAM,MAAM,WAAW,MAAM,QAAQ;AAChF,iBAAK,WAAW;AAAA,QACpB;AACA,iBAAS,WAAW,OAAO;AACzB,cAAI,MAAM,QAAS,OAAM,UAAU,MAAM,QAAQ;AAAA,QACnD;AACA,iBAAS,gBAAgB,OAAO,aAAa;AAC3C,cAAI;AACJ,iBAAO,MAAM;AACX,gBAAI,CAAC,MAAM,SAAS;AAClB;AAAA,YACF;AACA,4BAAgB,MAAM,QAAQ;AAC9B,gBAAI,CAAC,OAAO,gBAAgB,eAAe,MAAM,aAAa,CAAC,KAC3D,CAAC,OAAO,gBAAgB,MAAM,aAAa,CAAC,EAAE,eAAe,MAAM,WAAW,CAAC,GAAG;AACpF;AAAA,YACF;AACA,uBAAW,KAAK;AAAA,UAClB;AAAA,QACF;AAEA,iBAAS,UAAUC,OAAM,QAAQ,OAAO;AACtC,cAAIA,SAAQ,WAAW;AACrB,kBAAM,WAAW,OAAO,OAAO;AAC/B,mBAAO;AAAA,UACT,WAAWA,SAAQ,YAAY;AAC7B,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF;AACA,iBAAS,aAAaA,OAAM,QAAQ,OAAO;AACzC,cAAIA,SAAQ,QAAQ;AAClB,kBAAM,UAAU,OAAO,QAAQ;AAC/B,uBAAW;AACX,mBAAO;AAAA,UACT,WAAW,OAAO,uBAAuBA,SAAQ,UAAU;AACzD,uBAAW;AACX,mBAAO,UAAUA,OAAM,QAAQ,KAAK;AAAA,UACtC,OAAO;AACL,uBAAW;AACX,mBAAO;AAAA,UACT;AAAA,QACF;AACA,iBAAS,kBAAkBA,OAAM,QAAQ,OAAO;AAC9C,cAAIA,SAAQ,QAAQ;AAClB,gBAAI,UAAU,OAAO,QAAQ;AAC7B,gBAAI,MAAM,WAAW,MAAM,QAAQ,WAAW,WAC1C,OAAO,iBAAiB,eAAe,MAAM,MAAM,QAAQ,OAAO,CAAC;AACrE,yBAAW,KAAK;AAClB,gBAAK,MAAM,WAAW,MAAM,QAAQ,WAAW,WAAY,OAAO,iBAAiB,OAAO;AACxF,yBAAW;AACX,qBAAO;AAAA,YACT,OAAO;AACL,yBAAW;AACX,qBAAO;AAAA,YACT;AAAA,UACF,WAAW,OAAO,uBAAuBA,SAAQ,UAAU;AACzD,uBAAW;AACX,mBAAO,WAAWA,OAAM,QAAQ,KAAK;AAAA,UACvC,OAAO;AACL,uBAAW;AACX,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,iBAAS,WAAWA,OAAM,SAAS,OAAO;AACxC,cAAIA,SAAQ,UAAU;AACpB,uBAAW;AACX,mBAAO;AAAA,UACT;AACA,qBAAW,KAAK;AAChB,iBAAO;AAAA,QACT;AACA,iBAAS,cAAcA,OAAM,QAAQ,OAAO;AAC1C,qBAAW;AACX,iBAAO,WAAWA,OAAM,QAAQ,KAAK;AAAA,QACvC;AAEA,iBAAS,UAAUA,OAAM,SAAS,OAAO;AACvC,cAAIA,SAAQ,QAAQ;AAClB,uBAAW;AACX,mBAAO;AAAA,UACT,WAAWA,SAAQ,YAAYA,SAAQ,gBAAgB;AACrD,gBAAI,UAAU,MAAM,SAAS,WAAW,MAAM;AAC9C,kBAAM,UAAU,MAAM,WAAW;AACjC,gBAAIA,SAAQ,kBACR,OAAO,gBAAgB,eAAe,MAAM,OAAO,CAAC,GAAG;AACzD,8BAAgB,OAAO,OAAO;AAAA,YAChC,OAAO;AACL,8BAAgB,OAAO,OAAO;AAC9B,oBAAM,UAAU,IAAI,QAAQ,OAAO,SAAS,YAAY,MAAM,QAAQ;AAAA,YACxE;AACA,mBAAO;AAAA,UACT;AACA,qBAAW;AACX,iBAAO;AAAA,QACT;AACA,iBAAS,YAAYA,OAAM,QAAQ,OAAO;AACxC,cAAIA,SAAQ,SAAU,QAAO;AAC7B,cAAI,CAAC,OAAO,aAAc,YAAW;AACrC,iBAAO,UAAUA,OAAM,QAAQ,KAAK;AAAA,QACtC;AACA,iBAAS,eAAeA,OAAM,QAAQ,OAAO;AAC3C,cAAIA,SAAQ,SAAU,QAAO;AAC7B,cAAIA,SAAQ,UAAU,OAAO,eAAe;AAAC,uBAAW;AAAU,mBAAO;AAAA,UAAU;AACnF,qBAAW;AACX,iBAAO,UAAUA,OAAM,QAAQ,KAAK;AAAA,QACtC;AACA,iBAAS,mBAAmBA,OAAM,QAAQ,OAAO;AAC/C,cAAIA,SAAQ,SAAU,QAAO;AAC7B,iBAAO,UAAUA,OAAM,QAAQ,KAAK;AAAA,QACtC;AAEA,eAAO;AAAA,UACL,YAAY,SAAS,YAAY;AAC/B,gBAAI,QAAQ;AAAA,cAAC,UAAU;AAAA,cACV,OAAO;AAAA,cACP,UAAU,cAAc;AAAA,cACxB,SAAS;AAAA,cAAM,UAAU;AAAA,cACzB,SAAS;AAAA,YAAI;AAC1B,gBAAI,cAAc,KAAM,OAAM,aAAa;AAC3C,mBAAO;AAAA,UACT;AAAA,UAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,gBAAI,CAAC,MAAM,WAAW,OAAO,IAAI;AAC/B,oBAAM,WAAW,OAAO,YAAY;AAEtC,gBAAI,OAAO,SAAS,EAAG,QAAO;AAC9B,mBAAO;AACP,gBAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AACxC,iBAAK,SAAS,SAAS,SAAS,WAAW;AACzC,yBAAW;AACX,oBAAM,QAAQ,MAAM,MAAM,QAAQ,OAAO,QAAQ,KAAK;AACtD,kBAAI;AACF,wBAAQ,YAAY,UAAU,QAAQ,WAAW;AAAA,YACrD;AACA,mBAAO;AAAA,UACT;AAAA,UAEA,QAAQ,SAAS,OAAO,WAAW,UAAU;AAC3C,gBAAI,UAAU,MAAM;AAEpB,gBAAI,MAAM,SAAS,eAAe;AAChC,kBAAI,MAAM,YAAY,MAAM;AAC1B,uBAAO,MAAM,iBAAiB;AAAA;AAE9B,uBAAO,MAAM,WAAW;AAAA,YAC5B;AACA,gBAAI,WAAW,QAAQ,SAAU,QAAOD,YAAW;AACnD,gBAAI,MAAM,YAAY,SAAS,MAAM,YAAY;AAC/C,qBAAO,WAAW,SAAS,MAAM,QAAQ,EAAE,CAAC,EAAE,SAAS;AAEzD,gBAAI,MAAM,SAAS;AACjB,kBAAI,OAAO,8BAA8B;AACvC,uBAAO,MAAM,WAAW,MAAM,QAAQ,SAAS;AAAA;AAE/C,uBAAO,MAAM,WAAW,cAAc,OAAO,4BAA4B;AAAA,YAC7E;AACA,gBAAI,OAAO,cAAc,cAAc,KAAK,SAAS,EAAG,QAAO;AAC/D,gBAAI,WAAW,aAAa,sBAAsB,KAAK,SAAS;AAChE,gBAAI,YAAY,SAAS,CAAC,GAAG;AAC3B,qBAAO,SAAS;AACd,oBAAI,QAAQ,WAAW,SAAS,CAAC,GAAG;AAClC,4BAAU,QAAQ;AAClB;AAAA,gBACF,WAAW,OAAO,iBAAiB,eAAe,MAAM,QAAQ,OAAO,CAAC,GAAG;AACzE,4BAAU,QAAQ;AAAA,gBACpB,OAAO;AACL;AAAA,gBACF;AAAA,cACF;AAAA,YACF,WAAW,UAAU;AACnB,qBAAO,SAAS;AACd,oBAAI,WAAW,OAAO,gBAAgB,MAAM,QAAQ,OAAO,CAAC;AAC5D,oBAAI,YAAY,SAAS,eAAe,MAAM,SAAS,CAAC,CAAC,CAAC;AACxD,4BAAU,QAAQ;AAAA;AAElB;AAAA,cACJ;AAAA,YACF;AACA,mBAAO,WAAW,QAAQ,QAAQ,CAAC,QAAQ;AACzC,wBAAU,QAAQ;AACpB,gBAAI,QAAS,QAAO,QAAQ,SAAS;AAAA,gBAChC,QAAO,MAAM,cAAc;AAAA,UAClC;AAAA,UAEA,eAAe;AAAA,UACf,mBAAmB;AAAA,UACnB,iBAAiB;AAAA,UAEjB,eAAe,OAAO,WAAW,SAAS;AAAA,UAC1C,YAAY,OAAO,WAAW,SAAS;AAAA,UAEvC,eAAe,SAAS,OAAO;AAC7B,gBAAI,MAAM,SAAS;AACjB,oBAAM,QAAQ;AAAA,UAClB;AAAA,UAEA,eAAe,SAAS,OAAO;AAC7B,mBAAO,MAAM,UAAU,EAAC,MAAM,MAAM,SAAS,OAAO,MAAM,QAAQ,WAAU,IAAI;AAAA,UAClF;AAAA,UAEA,mBAAmB,SAAS,OAAO;AACjC,gBAAI,UAAU,CAAC;AACf,qBAAS,KAAK,MAAM,SAAS,IAAI,KAAK,GAAG;AACvC,sBAAQ,KAAK,GAAG,OAAO;AACzB,mBAAO,QAAQ,QAAQ;AAAA,UACzB;AAAA,QACF;AAAA,MACF,CAAC;AAED,MAAAA,YAAW,WAAW,YAAY,KAAK;AACvC,MAAAA,YAAW,WAAW,mBAAmB,KAAK;AAC9C,UAAI,CAACA,YAAW,UAAU,eAAe,WAAW;AAClD,QAAAA,YAAW,WAAW,aAAa,EAAC,MAAM,OAAO,UAAU,KAAI,CAAC;AAAA,IAElE,CAAC;AAAA;AAAA;", "names": ["CodeMirror", "type"]}