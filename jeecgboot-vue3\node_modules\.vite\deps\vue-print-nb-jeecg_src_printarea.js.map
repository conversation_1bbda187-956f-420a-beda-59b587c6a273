{"version": 3, "sources": ["../../.pnpm/vue-print-nb-jeecg@1.0.12/node_modules/vue-print-nb-jeecg/src/printarea.js"], "sourcesContent": ["export default class {\r\n  constructor(option) {\r\n    this.standards = {\r\n      strict: 'strict',\r\n      loose: 'loose',\r\n      html5: 'html5'\r\n    };\r\n    this.counter = 0;\r\n    this.settings = {\r\n      standard: this.standards.html5,\r\n      extraHead: '', // 附加在head标签上的额外元素,使用逗号分隔\r\n      extraCss: '', // 额外的css逗号分隔\r\n      popTitle: '', // 标题\r\n      endCallback: null, // 成功打开后的回调函数\r\n      el: '' // 局部打印的id\r\n    };\r\n    Object.assign(this.settings, option);\r\n    this.init();\r\n  };\r\n  init() {\r\n    this.counter++;\r\n    this.settings.id = `printArea_${this.counter}`;\r\n    let box = document.getElementById(this.settings.id);\r\n    if (box) {\r\n      box.parentNode.removeChild(box);\r\n    }\r\n    let PrintAreaWindow = this.getPrintWindow(); // 创建iframe\r\n    this.write(PrintAreaWindow.doc); // 写入内容\r\n    //this.print(PrintAreaWindow);\r\n    this.settings.endCallback();\r\n  };\r\n  print(PAWindow) {\r\n    let paWindow = PAWindow;\r\n\tconsole.log('---调用打印 focus-----');\r\n\tpaWindow.focus();\r\n\tpaWindow.print();\r\n\tconsole.log('---调用打印 print-----');\r\n  };\r\n  write(PADocument, $ele) {\r\n    PADocument.open();\r\n    PADocument.write(`${this.docType()}<html>${this.getHead()}${this.getBody()}</html>`);\r\n    PADocument.close();\r\n  };\r\n  docType() {\r\n    if (this.settings.standard === this.standards.html5) {\r\n      return '<!DOCTYPE html>';\r\n    }\r\n    var transitional = this.settings.standard === this.standards.loose ? ' Transitional' : '';\r\n    var dtd = this.settings.standard === this.standards.loose ? 'loose' : 'strict';\r\n\r\n    return `<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01${transitional}//EN\" \"http://www.w3.org/TR/html4/${dtd}.dtd\">`;\r\n  };\r\n  getHead() {\r\n    let extraHead = '';\r\n    let links = '';\r\n    let style = '';\r\n    if (this.settings.extraHead) {\r\n      this.settings.extraHead.replace(/([^,]+)/g, function(m) {\r\n        extraHead += m;\r\n      });\r\n    }\r\n\t[].forEach.call(document.querySelectorAll('link'), function (item, i) {\r\n        if (item.href.indexOf('.css') >= 0) {\r\n          links += '<link type=\"text/css\" rel=\"stylesheet\" href=\"' + item.href + '\" >';\r\n        }\r\n    });\r\n   \r\n    for (let i = 0 ; i < document.styleSheets.length; i++) {\r\n      if (document.styleSheets[i].cssRules || document.styleSheets[i].rules) {\r\n        let rules = document.styleSheets[i].cssRules || document.styleSheets[i].rules;\r\n        for (let b = 0 ; b < rules.length; b++) {\r\n\t\t  try {\r\n              style += rules[b].cssText;\r\n            } catch (err) {}\r\n        }\r\n      }\r\n    }\r\n\r\n    if (this.settings.extraCss) {\r\n      this.settings.extraCss.replace(/([^,\\s]+)/g, function(m) {\r\n        links += `<link type=\"text/css\" rel=\"stylesheet\" href=\"${m}\">`;\r\n      });\r\n    }\r\n\r\n    return `<head><title>${this.settings.popTitle}</title>${extraHead}${links}<style type=\"text/css\">${style}</style></head>`;\r\n  };\r\n  getBody() {\r\n    let ele = this.getFormData(document.querySelector(this.settings.el));\r\n    let htm = ele.outerHTML;\r\n    console.log('htm', htm);\r\n    return '<body>' + htm + '</body>';\r\n  };\r\n  // 处理form表单的默认状态\r\n  getFormData(ele) {\r\n    let that = this\r\n    let copy = ele.cloneNode(true);\r\n\r\n    //update-begin--Author:sunjianlei  Date:20190510 for：支持忽略打印的标签----------------------\r\n    let allElements = copy.querySelectorAll('*');\r\n\t[].forEach.call(allElements, function (item) {\r\n\t\tlet attr = item.getAttribute('ignore-print');\r\n        attr = (attr == null ? item.getAttribute('ignoreprint') : attr);\r\n        if (attr != null && attr.toString() === 'true') {\r\n            item.outerHTML = ''\r\n        }\r\n\t});\r\n    //update-end--Author:sunjianlei  Date:20190510 for：支持忽略打印的标签----------------------\r\n\r\n    let copiedInputs = copy.querySelectorAll('input,select,textarea');\r\n\t[].forEach.call(copiedInputs, function (item, i) {\r\n\t  let typeInput = item.getAttribute('type');\r\n      let copiedInput = copiedInputs[i];\r\n      // update-begin--Author:sunjianlei  Date:20191101 for：优化赋值逻辑方式 ----------------------\r\n      if (typeInput == null) {\r\n        typeInput = item.tagName === 'SELECT' ? 'select' : item.tagName === 'TEXTAREA' ? 'textarea' : '';\r\n      }\r\n      if (typeInput === 'radio' || typeInput === 'checkbox') {\r\n\r\n        item.checked && copiedInput.setAttribute('checked', item.checked);\r\n\r\n      } else if (typeInput === 'select') {\r\n\t\t[].forEach.call(copiedInput.querySelectorAll('option'), function (op, b) {\r\n\t\t\tif (op.selected) {\r\n            op.setAttribute('selected', true);\r\n          }\r\n\t\t});\r\n      } else if (typeInput === 'textarea') {\r\n        // update-begin--Author:sunjianlei  Date:20220302 for：修复textarea换行会出现<br>的问题 ----------------------\r\n        copiedInput.innerHTML = item.value\r\n        // update-end----Author:sunjianlei  Date:20220302 for：修复textarea换行会出现<br>的问题 ----------------------\r\n      } else {\r\n        copiedInput.value = item.value;\r\n        copiedInput.setAttribute('value', item.value);\r\n      }\r\n      //update-end--Author:sunjianlei  Date:20191101 for：优化赋值逻辑方式 ----------------------\r\n\t});\r\n\r\n    //update-begin--Author:jianlei  Date:20190507 for：支持Canvas打印--------------------\r\n    var sourceCanvas = ele.querySelectorAll('canvas');\r\n    var copyCanvas = copy.querySelectorAll('canvas');\r\n\r\n\t[].forEach.call(copyCanvas, function (item, i) {\r\n    // update-begin--author:sunjianlei date:20220407 for：echarts canvas宽度自适应 ---------\r\n    if (that.isECharts(item)) {\r\n      if (item.parentElement.style.width) {\r\n        item.parentElement.style.width = '100%'\r\n        item.parentElement.style.height = 'auto'\r\n      }\r\n      if (item.parentElement.parentElement.style.width) {\r\n        item.parentElement.parentElement.style.width = '100%'\r\n        item.parentElement.parentElement.style.height = 'auto'\r\n      }\r\n    }\r\n    // update-end--author:sunjianlei date:20220407 for：echarts canvas宽度自适应 ---------\r\n\r\n      var url = sourceCanvas[i].toDataURL()\r\n        //update-begin--Author:sunjianlei  Date:20190510 for：canvas宽度自适应----------------------\r\n        item.outerHTML = '<img src=\"' + url + '\" style=\"width:100%;\"/>'\r\n        //update-end--Author:sunjianlei  Date:20190510 for：canvas宽度自适应----------------------\r\n    });\r\n      //update-end--Author:jianlei  Date:20190507 for：支持Canvas打印----------------------\r\n\r\n    return copy;\r\n  };\r\n\r\n  /**\r\n   * 判断是否是 ECharts 的 Canvas\r\n   *\r\n   * @param item canvas\r\n   * @time 2022-4-7\r\n   * <AUTHOR>\r\n   */\r\n  isECharts(item) {\r\n    let attrName = '_echarts_instance_'\r\n    let parent = item.parentElement\r\n    if (parent.getAttribute(attrName) != null) {\r\n      return true\r\n    }\r\n    if (parent.parentElement) {\r\n      return parent.parentElement.getAttribute(attrName) != null\r\n    }\r\n    return false\r\n  };\r\n\r\n  getPrintWindow() {\r\n    var f = this.Iframe();\r\n    return {\r\n      win: f.contentWindow || f,\r\n      doc: f.doc\r\n    };\r\n  };\r\n  Iframe() {\r\n    let frameId = this.settings.id;\r\n    let iframe;\r\n\tvar that = this;\r\n    try {\r\n      iframe = document.createElement('iframe');\r\n      document.body.appendChild(iframe);\r\n      iframe.style.border = '0px';\r\n      iframe.style.position = 'absolute';\r\n      iframe.style.width = '0px';\r\n      iframe.style.height = '0px';\r\n      iframe.style.right = '0px';\r\n      iframe.style.top = '0px';\r\n      iframe.setAttribute('id', frameId);\r\n      iframe.setAttribute('src', new Date().getTime());\r\n      iframe.doc = null;\r\n\t  iframe.onload = function () {\r\n\t\tvar win = iframe.contentWindow || iframe;\r\n\t\tthat.print(win);\r\n\t  }\r\n      iframe.doc = iframe.contentDocument ? iframe.contentDocument : (iframe.contentWindow ? iframe.contentWindow.document : iframe.document);\r\n    } catch (e) {\r\n      throw new Error(e + '. iframes may not be supported in this browser.');\r\n    }\r\n\r\n    if (iframe.doc == null) {\r\n      throw new Error('Cannot find document.');\r\n    }\r\n\r\n    return iframe;\r\n  };\r\n};"], "mappings": ";;;AAAA,IAAO,oBAAP,MAAqB;AAAA,EACnB,YAAY,QAAQ;AAClB,SAAK,YAAY;AAAA,MACf,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AACA,SAAK,UAAU;AACf,SAAK,WAAW;AAAA,MACd,UAAU,KAAK,UAAU;AAAA,MACzB,WAAW;AAAA;AAAA,MACX,UAAU;AAAA;AAAA,MACV,UAAU;AAAA;AAAA,MACV,aAAa;AAAA;AAAA,MACb,IAAI;AAAA;AAAA,IACN;AACA,WAAO,OAAO,KAAK,UAAU,MAAM;AACnC,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,OAAO;AACL,SAAK;AACL,SAAK,SAAS,KAAK,aAAa,KAAK,OAAO;AAC5C,QAAI,MAAM,SAAS,eAAe,KAAK,SAAS,EAAE;AAClD,QAAI,KAAK;AACP,UAAI,WAAW,YAAY,GAAG;AAAA,IAChC;AACA,QAAI,kBAAkB,KAAK,eAAe;AAC1C,SAAK,MAAM,gBAAgB,GAAG;AAE9B,SAAK,SAAS,YAAY;AAAA,EAC5B;AAAA,EACA,MAAM,UAAU;AACd,QAAI,WAAW;AAClB,YAAQ,IAAI,oBAAoB;AAChC,aAAS,MAAM;AACf,aAAS,MAAM;AACf,YAAQ,IAAI,oBAAoB;AAAA,EAC/B;AAAA,EACA,MAAM,YAAY,MAAM;AACtB,eAAW,KAAK;AAChB,eAAW,MAAM,GAAG,KAAK,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC,GAAG,KAAK,QAAQ,CAAC,SAAS;AACnF,eAAW,MAAM;AAAA,EACnB;AAAA,EACA,UAAU;AACR,QAAI,KAAK,SAAS,aAAa,KAAK,UAAU,OAAO;AACnD,aAAO;AAAA,IACT;AACA,QAAI,eAAe,KAAK,SAAS,aAAa,KAAK,UAAU,QAAQ,kBAAkB;AACvF,QAAI,MAAM,KAAK,SAAS,aAAa,KAAK,UAAU,QAAQ,UAAU;AAEtE,WAAO,+CAA+C,YAAY,qCAAqC,GAAG;AAAA,EAC5G;AAAA,EACA,UAAU;AACR,QAAI,YAAY;AAChB,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,QAAI,KAAK,SAAS,WAAW;AAC3B,WAAK,SAAS,UAAU,QAAQ,YAAY,SAAS,GAAG;AACtD,qBAAa;AAAA,MACf,CAAC;AAAA,IACH;AACH,KAAC,EAAE,QAAQ,KAAK,SAAS,iBAAiB,MAAM,GAAG,SAAU,MAAM,GAAG;AAC/D,UAAI,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;AAClC,iBAAS,kDAAkD,KAAK,OAAO;AAAA,MACzE;AAAA,IACJ,CAAC;AAED,aAAS,IAAI,GAAI,IAAI,SAAS,YAAY,QAAQ,KAAK;AACrD,UAAI,SAAS,YAAY,CAAC,EAAE,YAAY,SAAS,YAAY,CAAC,EAAE,OAAO;AACrE,YAAI,QAAQ,SAAS,YAAY,CAAC,EAAE,YAAY,SAAS,YAAY,CAAC,EAAE;AACxE,iBAAS,IAAI,GAAI,IAAI,MAAM,QAAQ,KAAK;AAC5C,cAAI;AACM,qBAAS,MAAM,CAAC,EAAE;AAAA,UACpB,SAAS,KAAK;AAAA,UAAC;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAEA,QAAI,KAAK,SAAS,UAAU;AAC1B,WAAK,SAAS,SAAS,QAAQ,cAAc,SAAS,GAAG;AACvD,iBAAS,gDAAgD,CAAC;AAAA,MAC5D,CAAC;AAAA,IACH;AAEA,WAAO,gBAAgB,KAAK,SAAS,QAAQ,WAAW,SAAS,GAAG,KAAK,0BAA0B,KAAK;AAAA,EAC1G;AAAA,EACA,UAAU;AACR,QAAI,MAAM,KAAK,YAAY,SAAS,cAAc,KAAK,SAAS,EAAE,CAAC;AACnE,QAAI,MAAM,IAAI;AACd,YAAQ,IAAI,OAAO,GAAG;AACtB,WAAO,WAAW,MAAM;AAAA,EAC1B;AAAA;AAAA,EAEA,YAAY,KAAK;AACf,QAAI,OAAO;AACX,QAAI,OAAO,IAAI,UAAU,IAAI;AAG7B,QAAI,cAAc,KAAK,iBAAiB,GAAG;AAC9C,KAAC,EAAE,QAAQ,KAAK,aAAa,SAAU,MAAM;AAC5C,UAAI,OAAO,KAAK,aAAa,cAAc;AACrC,aAAQ,QAAQ,OAAO,KAAK,aAAa,aAAa,IAAI;AAC1D,UAAI,QAAQ,QAAQ,KAAK,SAAS,MAAM,QAAQ;AAC5C,aAAK,YAAY;AAAA,MACrB;AAAA,IACP,CAAC;AAGE,QAAI,eAAe,KAAK,iBAAiB,uBAAuB;AACnE,KAAC,EAAE,QAAQ,KAAK,cAAc,SAAU,MAAM,GAAG;AAC/C,UAAI,YAAY,KAAK,aAAa,MAAM;AACrC,UAAI,cAAc,aAAa,CAAC;AAEhC,UAAI,aAAa,MAAM;AACrB,oBAAY,KAAK,YAAY,WAAW,WAAW,KAAK,YAAY,aAAa,aAAa;AAAA,MAChG;AACA,UAAI,cAAc,WAAW,cAAc,YAAY;AAErD,aAAK,WAAW,YAAY,aAAa,WAAW,KAAK,OAAO;AAAA,MAElE,WAAW,cAAc,UAAU;AACvC,SAAC,EAAE,QAAQ,KAAK,YAAY,iBAAiB,QAAQ,GAAG,SAAU,IAAI,GAAG;AACxE,cAAI,GAAG,UAAU;AACR,eAAG,aAAa,YAAY,IAAI;AAAA,UAClC;AAAA,QACR,CAAC;AAAA,MACG,WAAW,cAAc,YAAY;AAEnC,oBAAY,YAAY,KAAK;AAAA,MAE/B,OAAO;AACL,oBAAY,QAAQ,KAAK;AACzB,oBAAY,aAAa,SAAS,KAAK,KAAK;AAAA,MAC9C;AAAA,IAEL,CAAC;AAGE,QAAI,eAAe,IAAI,iBAAiB,QAAQ;AAChD,QAAI,aAAa,KAAK,iBAAiB,QAAQ;AAElD,KAAC,EAAE,QAAQ,KAAK,YAAY,SAAU,MAAM,GAAG;AAE5C,UAAI,KAAK,UAAU,IAAI,GAAG;AACxB,YAAI,KAAK,cAAc,MAAM,OAAO;AAClC,eAAK,cAAc,MAAM,QAAQ;AACjC,eAAK,cAAc,MAAM,SAAS;AAAA,QACpC;AACA,YAAI,KAAK,cAAc,cAAc,MAAM,OAAO;AAChD,eAAK,cAAc,cAAc,MAAM,QAAQ;AAC/C,eAAK,cAAc,cAAc,MAAM,SAAS;AAAA,QAClD;AAAA,MACF;AAGE,UAAI,MAAM,aAAa,CAAC,EAAE,UAAU;AAElC,WAAK,YAAY,eAAe,MAAM;AAAA,IAE1C,CAAC;AAGD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,MAAM;AACd,QAAI,WAAW;AACf,QAAI,SAAS,KAAK;AAClB,QAAI,OAAO,aAAa,QAAQ,KAAK,MAAM;AACzC,aAAO;AAAA,IACT;AACA,QAAI,OAAO,eAAe;AACxB,aAAO,OAAO,cAAc,aAAa,QAAQ,KAAK;AAAA,IACxD;AACA,WAAO;AAAA,EACT;AAAA,EAEA,iBAAiB;AACf,QAAI,IAAI,KAAK,OAAO;AACpB,WAAO;AAAA,MACL,KAAK,EAAE,iBAAiB;AAAA,MACxB,KAAK,EAAE;AAAA,IACT;AAAA,EACF;AAAA,EACA,SAAS;AACP,QAAI,UAAU,KAAK,SAAS;AAC5B,QAAI;AACP,QAAI,OAAO;AACR,QAAI;AACF,eAAS,SAAS,cAAc,QAAQ;AACxC,eAAS,KAAK,YAAY,MAAM;AAChC,aAAO,MAAM,SAAS;AACtB,aAAO,MAAM,WAAW;AACxB,aAAO,MAAM,QAAQ;AACrB,aAAO,MAAM,SAAS;AACtB,aAAO,MAAM,QAAQ;AACrB,aAAO,MAAM,MAAM;AACnB,aAAO,aAAa,MAAM,OAAO;AACjC,aAAO,aAAa,QAAO,oBAAI,KAAK,GAAE,QAAQ,CAAC;AAC/C,aAAO,MAAM;AAChB,aAAO,SAAS,WAAY;AAC7B,YAAI,MAAM,OAAO,iBAAiB;AAClC,aAAK,MAAM,GAAG;AAAA,MACb;AACG,aAAO,MAAM,OAAO,kBAAkB,OAAO,kBAAmB,OAAO,gBAAgB,OAAO,cAAc,WAAW,OAAO;AAAA,IAChI,SAAS,GAAG;AACV,YAAM,IAAI,MAAM,IAAI,iDAAiD;AAAA,IACvE;AAEA,QAAI,OAAO,OAAO,MAAM;AACtB,YAAM,IAAI,MAAM,uBAAuB;AAAA,IACzC;AAEA,WAAO;AAAA,EACT;AACF;", "names": []}