{"version": 3, "sources": ["../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/link/plugin.js", "../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/link/index.js"], "sourcesContent": ["/**\n * TinyMCE version 6.6.2 (2023-08-09)\n */\n\n(function () {\n    'use strict';\n\n    var global$5 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const hasProto = (v, constructor, predicate) => {\n      var _a;\n      if (predicate(v, constructor.prototype)) {\n        return true;\n      } else {\n        return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n      }\n    };\n    const typeOf = x => {\n      const t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && Array.isArray(x)) {\n        return 'array';\n      } else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    const isType = type => value => typeOf(value) === type;\n    const isSimpleType = type => value => typeof value === type;\n    const eq = t => a => t === a;\n    const isString = isType('string');\n    const isObject = isType('object');\n    const isArray = isType('array');\n    const isNull = eq(null);\n    const isBoolean = isSimpleType('boolean');\n    const isNullable = a => a === null || a === undefined;\n    const isNonNullable = a => !isNullable(a);\n    const isFunction = isSimpleType('function');\n    const isArrayOf = (value, pred) => {\n      if (isArray(value)) {\n        for (let i = 0, len = value.length; i < len; ++i) {\n          if (!pred(value[i])) {\n            return false;\n          }\n        }\n        return true;\n      }\n      return false;\n    };\n\n    const noop = () => {\n    };\n    const constant = value => {\n      return () => {\n        return value;\n      };\n    };\n    const tripleEquals = (a, b) => {\n      return a === b;\n    };\n\n    class Optional {\n      constructor(tag, value) {\n        this.tag = tag;\n        this.value = value;\n      }\n      static some(value) {\n        return new Optional(true, value);\n      }\n      static none() {\n        return Optional.singletonNone;\n      }\n      fold(onNone, onSome) {\n        if (this.tag) {\n          return onSome(this.value);\n        } else {\n          return onNone();\n        }\n      }\n      isSome() {\n        return this.tag;\n      }\n      isNone() {\n        return !this.tag;\n      }\n      map(mapper) {\n        if (this.tag) {\n          return Optional.some(mapper(this.value));\n        } else {\n          return Optional.none();\n        }\n      }\n      bind(binder) {\n        if (this.tag) {\n          return binder(this.value);\n        } else {\n          return Optional.none();\n        }\n      }\n      exists(predicate) {\n        return this.tag && predicate(this.value);\n      }\n      forall(predicate) {\n        return !this.tag || predicate(this.value);\n      }\n      filter(predicate) {\n        if (!this.tag || predicate(this.value)) {\n          return this;\n        } else {\n          return Optional.none();\n        }\n      }\n      getOr(replacement) {\n        return this.tag ? this.value : replacement;\n      }\n      or(replacement) {\n        return this.tag ? this : replacement;\n      }\n      getOrThunk(thunk) {\n        return this.tag ? this.value : thunk();\n      }\n      orThunk(thunk) {\n        return this.tag ? this : thunk();\n      }\n      getOrDie(message) {\n        if (!this.tag) {\n          throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n        } else {\n          return this.value;\n        }\n      }\n      static from(value) {\n        return isNonNullable(value) ? Optional.some(value) : Optional.none();\n      }\n      getOrNull() {\n        return this.tag ? this.value : null;\n      }\n      getOrUndefined() {\n        return this.value;\n      }\n      each(worker) {\n        if (this.tag) {\n          worker(this.value);\n        }\n      }\n      toArray() {\n        return this.tag ? [this.value] : [];\n      }\n      toString() {\n        return this.tag ? `some(${ this.value })` : 'none()';\n      }\n    }\n    Optional.singletonNone = new Optional(false);\n\n    const nativeIndexOf = Array.prototype.indexOf;\n    const nativePush = Array.prototype.push;\n    const rawIndexOf = (ts, t) => nativeIndexOf.call(ts, t);\n    const contains = (xs, x) => rawIndexOf(xs, x) > -1;\n    const map = (xs, f) => {\n      const len = xs.length;\n      const r = new Array(len);\n      for (let i = 0; i < len; i++) {\n        const x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    const each$1 = (xs, f) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        f(x, i);\n      }\n    };\n    const foldl = (xs, f, acc) => {\n      each$1(xs, (x, i) => {\n        acc = f(acc, x, i);\n      });\n      return acc;\n    };\n    const flatten = xs => {\n      const r = [];\n      for (let i = 0, len = xs.length; i < len; ++i) {\n        if (!isArray(xs[i])) {\n          throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n        }\n        nativePush.apply(r, xs[i]);\n      }\n      return r;\n    };\n    const bind = (xs, f) => flatten(map(xs, f));\n    const findMap = (arr, f) => {\n      for (let i = 0; i < arr.length; i++) {\n        const r = f(arr[i], i);\n        if (r.isSome()) {\n          return r;\n        }\n      }\n      return Optional.none();\n    };\n\n    const is = (lhs, rhs, comparator = tripleEquals) => lhs.exists(left => comparator(left, rhs));\n    const cat = arr => {\n      const r = [];\n      const push = x => {\n        r.push(x);\n      };\n      for (let i = 0; i < arr.length; i++) {\n        arr[i].each(push);\n      }\n      return r;\n    };\n    const someIf = (b, a) => b ? Optional.some(a) : Optional.none();\n\n    const option = name => editor => editor.options.get(name);\n    const register$1 = editor => {\n      const registerOption = editor.options.register;\n      registerOption('link_assume_external_targets', {\n        processor: value => {\n          const valid = isString(value) || isBoolean(value);\n          if (valid) {\n            if (value === true) {\n              return {\n                value: 1,\n                valid\n              };\n            } else if (value === 'http' || value === 'https') {\n              return {\n                value,\n                valid\n              };\n            } else {\n              return {\n                value: 0,\n                valid\n              };\n            }\n          } else {\n            return {\n              valid: false,\n              message: 'Must be a string or a boolean.'\n            };\n          }\n        },\n        default: false\n      });\n      registerOption('link_context_toolbar', {\n        processor: 'boolean',\n        default: false\n      });\n      registerOption('link_list', { processor: value => isString(value) || isFunction(value) || isArrayOf(value, isObject) });\n      registerOption('link_default_target', { processor: 'string' });\n      registerOption('link_default_protocol', {\n        processor: 'string',\n        default: 'https'\n      });\n      registerOption('link_target_list', {\n        processor: value => isBoolean(value) || isArrayOf(value, isObject),\n        default: true\n      });\n      registerOption('link_rel_list', {\n        processor: 'object[]',\n        default: []\n      });\n      registerOption('link_class_list', {\n        processor: 'object[]',\n        default: []\n      });\n      registerOption('link_title', {\n        processor: 'boolean',\n        default: true\n      });\n      registerOption('allow_unsafe_link_target', {\n        processor: 'boolean',\n        default: false\n      });\n      registerOption('link_quicklink', {\n        processor: 'boolean',\n        default: false\n      });\n    };\n    const assumeExternalTargets = option('link_assume_external_targets');\n    const hasContextToolbar = option('link_context_toolbar');\n    const getLinkList = option('link_list');\n    const getDefaultLinkTarget = option('link_default_target');\n    const getDefaultLinkProtocol = option('link_default_protocol');\n    const getTargetList = option('link_target_list');\n    const getRelList = option('link_rel_list');\n    const getLinkClassList = option('link_class_list');\n    const shouldShowLinkTitle = option('link_title');\n    const allowUnsafeLinkTarget = option('allow_unsafe_link_target');\n    const useQuickLink = option('link_quicklink');\n\n    var global$4 = tinymce.util.Tools.resolve('tinymce.util.Tools');\n\n    const getValue = item => isString(item.value) ? item.value : '';\n    const getText = item => {\n      if (isString(item.text)) {\n        return item.text;\n      } else if (isString(item.title)) {\n        return item.title;\n      } else {\n        return '';\n      }\n    };\n    const sanitizeList = (list, extractValue) => {\n      const out = [];\n      global$4.each(list, item => {\n        const text = getText(item);\n        if (item.menu !== undefined) {\n          const items = sanitizeList(item.menu, extractValue);\n          out.push({\n            text,\n            items\n          });\n        } else {\n          const value = extractValue(item);\n          out.push({\n            text,\n            value\n          });\n        }\n      });\n      return out;\n    };\n    const sanitizeWith = (extracter = getValue) => list => Optional.from(list).map(list => sanitizeList(list, extracter));\n    const sanitize = list => sanitizeWith(getValue)(list);\n    const createUi = (name, label) => items => ({\n      name,\n      type: 'listbox',\n      label,\n      items\n    });\n    const ListOptions = {\n      sanitize,\n      sanitizeWith,\n      createUi,\n      getValue\n    };\n\n    const keys = Object.keys;\n    const hasOwnProperty = Object.hasOwnProperty;\n    const each = (obj, f) => {\n      const props = keys(obj);\n      for (let k = 0, len = props.length; k < len; k++) {\n        const i = props[k];\n        const x = obj[i];\n        f(x, i);\n      }\n    };\n    const objAcc = r => (x, i) => {\n      r[i] = x;\n    };\n    const internalFilter = (obj, pred, onTrue, onFalse) => {\n      each(obj, (x, i) => {\n        (pred(x, i) ? onTrue : onFalse)(x, i);\n      });\n    };\n    const filter = (obj, pred) => {\n      const t = {};\n      internalFilter(obj, pred, objAcc(t), noop);\n      return t;\n    };\n    const has = (obj, key) => hasOwnProperty.call(obj, key);\n    const hasNonNullableKey = (obj, key) => has(obj, key) && obj[key] !== undefined && obj[key] !== null;\n\n    var global$3 = tinymce.util.Tools.resolve('tinymce.dom.TreeWalker');\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.util.URI');\n\n    const isAnchor = elm => isNonNullable(elm) && elm.nodeName.toLowerCase() === 'a';\n    const isLink = elm => isAnchor(elm) && !!getHref(elm);\n    const collectNodesInRange = (rng, predicate) => {\n      if (rng.collapsed) {\n        return [];\n      } else {\n        const contents = rng.cloneContents();\n        const firstChild = contents.firstChild;\n        const walker = new global$3(firstChild, contents);\n        const elements = [];\n        let current = firstChild;\n        do {\n          if (predicate(current)) {\n            elements.push(current);\n          }\n        } while (current = walker.next());\n        return elements;\n      }\n    };\n    const hasProtocol = url => /^\\w+:/i.test(url);\n    const getHref = elm => {\n      var _a, _b;\n      return (_b = (_a = elm.getAttribute('data-mce-href')) !== null && _a !== void 0 ? _a : elm.getAttribute('href')) !== null && _b !== void 0 ? _b : '';\n    };\n    const applyRelTargetRules = (rel, isUnsafe) => {\n      const rules = ['noopener'];\n      const rels = rel ? rel.split(/\\s+/) : [];\n      const toString = rels => global$4.trim(rels.sort().join(' '));\n      const addTargetRules = rels => {\n        rels = removeTargetRules(rels);\n        return rels.length > 0 ? rels.concat(rules) : rules;\n      };\n      const removeTargetRules = rels => rels.filter(val => global$4.inArray(rules, val) === -1);\n      const newRels = isUnsafe ? addTargetRules(rels) : removeTargetRules(rels);\n      return newRels.length > 0 ? toString(newRels) : '';\n    };\n    const trimCaretContainers = text => text.replace(/\\uFEFF/g, '');\n    const getAnchorElement = (editor, selectedElm) => {\n      selectedElm = selectedElm || getLinksInSelection(editor.selection.getRng())[0] || editor.selection.getNode();\n      if (isImageFigure(selectedElm)) {\n        return Optional.from(editor.dom.select('a[href]', selectedElm)[0]);\n      } else {\n        return Optional.from(editor.dom.getParent(selectedElm, 'a[href]'));\n      }\n    };\n    const isInAnchor = (editor, selectedElm) => getAnchorElement(editor, selectedElm).isSome();\n    const getAnchorText = (selection, anchorElm) => {\n      const text = anchorElm.fold(() => selection.getContent({ format: 'text' }), anchorElm => anchorElm.innerText || anchorElm.textContent || '');\n      return trimCaretContainers(text);\n    };\n    const getLinksInSelection = rng => collectNodesInRange(rng, isLink);\n    const getLinks$1 = elements => global$4.grep(elements, isLink);\n    const hasLinks = elements => getLinks$1(elements).length > 0;\n    const hasLinksInSelection = rng => getLinksInSelection(rng).length > 0;\n    const isOnlyTextSelected = editor => {\n      const inlineTextElements = editor.schema.getTextInlineElements();\n      const isElement = elm => elm.nodeType === 1 && !isAnchor(elm) && !has(inlineTextElements, elm.nodeName.toLowerCase());\n      const isInBlockAnchor = getAnchorElement(editor).exists(anchor => anchor.hasAttribute('data-mce-block'));\n      if (isInBlockAnchor) {\n        return false;\n      }\n      const rng = editor.selection.getRng();\n      if (!rng.collapsed) {\n        const elements = collectNodesInRange(rng, isElement);\n        return elements.length === 0;\n      } else {\n        return true;\n      }\n    };\n    const isImageFigure = elm => isNonNullable(elm) && elm.nodeName === 'FIGURE' && /\\bimage\\b/i.test(elm.className);\n    const getLinkAttrs = data => {\n      const attrs = [\n        'title',\n        'rel',\n        'class',\n        'target'\n      ];\n      return foldl(attrs, (acc, key) => {\n        data[key].each(value => {\n          acc[key] = value.length > 0 ? value : null;\n        });\n        return acc;\n      }, { href: data.href });\n    };\n    const handleExternalTargets = (href, assumeExternalTargets) => {\n      if ((assumeExternalTargets === 'http' || assumeExternalTargets === 'https') && !hasProtocol(href)) {\n        return assumeExternalTargets + '://' + href;\n      }\n      return href;\n    };\n    const applyLinkOverrides = (editor, linkAttrs) => {\n      const newLinkAttrs = { ...linkAttrs };\n      if (getRelList(editor).length === 0 && !allowUnsafeLinkTarget(editor)) {\n        const newRel = applyRelTargetRules(newLinkAttrs.rel, newLinkAttrs.target === '_blank');\n        newLinkAttrs.rel = newRel ? newRel : null;\n      }\n      if (Optional.from(newLinkAttrs.target).isNone() && getTargetList(editor) === false) {\n        newLinkAttrs.target = getDefaultLinkTarget(editor);\n      }\n      newLinkAttrs.href = handleExternalTargets(newLinkAttrs.href, assumeExternalTargets(editor));\n      return newLinkAttrs;\n    };\n    const updateLink = (editor, anchorElm, text, linkAttrs) => {\n      text.each(text => {\n        if (has(anchorElm, 'innerText')) {\n          anchorElm.innerText = text;\n        } else {\n          anchorElm.textContent = text;\n        }\n      });\n      editor.dom.setAttribs(anchorElm, linkAttrs);\n      editor.selection.select(anchorElm);\n    };\n    const createLink = (editor, selectedElm, text, linkAttrs) => {\n      const dom = editor.dom;\n      if (isImageFigure(selectedElm)) {\n        linkImageFigure(dom, selectedElm, linkAttrs);\n      } else {\n        text.fold(() => {\n          editor.execCommand('mceInsertLink', false, linkAttrs);\n        }, text => {\n          editor.insertContent(dom.createHTML('a', linkAttrs, dom.encode(text)));\n        });\n      }\n    };\n    const linkDomMutation = (editor, attachState, data) => {\n      const selectedElm = editor.selection.getNode();\n      const anchorElm = getAnchorElement(editor, selectedElm);\n      const linkAttrs = applyLinkOverrides(editor, getLinkAttrs(data));\n      editor.undoManager.transact(() => {\n        if (data.href === attachState.href) {\n          attachState.attach();\n        }\n        anchorElm.fold(() => {\n          createLink(editor, selectedElm, data.text, linkAttrs);\n        }, elm => {\n          editor.focus();\n          updateLink(editor, elm, data.text, linkAttrs);\n        });\n      });\n    };\n    const unlinkSelection = editor => {\n      const dom = editor.dom, selection = editor.selection;\n      const bookmark = selection.getBookmark();\n      const rng = selection.getRng().cloneRange();\n      const startAnchorElm = dom.getParent(rng.startContainer, 'a[href]', editor.getBody());\n      const endAnchorElm = dom.getParent(rng.endContainer, 'a[href]', editor.getBody());\n      if (startAnchorElm) {\n        rng.setStartBefore(startAnchorElm);\n      }\n      if (endAnchorElm) {\n        rng.setEndAfter(endAnchorElm);\n      }\n      selection.setRng(rng);\n      editor.execCommand('unlink');\n      selection.moveToBookmark(bookmark);\n    };\n    const unlinkDomMutation = editor => {\n      editor.undoManager.transact(() => {\n        const node = editor.selection.getNode();\n        if (isImageFigure(node)) {\n          unlinkImageFigure(editor, node);\n        } else {\n          unlinkSelection(editor);\n        }\n        editor.focus();\n      });\n    };\n    const unwrapOptions = data => {\n      const {\n        class: cls,\n        href,\n        rel,\n        target,\n        text,\n        title\n      } = data;\n      return filter({\n        class: cls.getOrNull(),\n        href,\n        rel: rel.getOrNull(),\n        target: target.getOrNull(),\n        text: text.getOrNull(),\n        title: title.getOrNull()\n      }, (v, _k) => isNull(v) === false);\n    };\n    const sanitizeData = (editor, data) => {\n      const getOption = editor.options.get;\n      const uriOptions = {\n        allow_html_data_urls: getOption('allow_html_data_urls'),\n        allow_script_urls: getOption('allow_script_urls'),\n        allow_svg_data_urls: getOption('allow_svg_data_urls')\n      };\n      const href = data.href;\n      return {\n        ...data,\n        href: global$2.isDomSafe(href, 'a', uriOptions) ? href : ''\n      };\n    };\n    const link = (editor, attachState, data) => {\n      const sanitizedData = sanitizeData(editor, data);\n      editor.hasPlugin('rtc', true) ? editor.execCommand('createlink', false, unwrapOptions(sanitizedData)) : linkDomMutation(editor, attachState, sanitizedData);\n    };\n    const unlink = editor => {\n      editor.hasPlugin('rtc', true) ? editor.execCommand('unlink') : unlinkDomMutation(editor);\n    };\n    const unlinkImageFigure = (editor, fig) => {\n      var _a;\n      const img = editor.dom.select('img', fig)[0];\n      if (img) {\n        const a = editor.dom.getParents(img, 'a[href]', fig)[0];\n        if (a) {\n          (_a = a.parentNode) === null || _a === void 0 ? void 0 : _a.insertBefore(img, a);\n          editor.dom.remove(a);\n        }\n      }\n    };\n    const linkImageFigure = (dom, fig, attrs) => {\n      var _a;\n      const img = dom.select('img', fig)[0];\n      if (img) {\n        const a = dom.create('a', attrs);\n        (_a = img.parentNode) === null || _a === void 0 ? void 0 : _a.insertBefore(a, img);\n        a.appendChild(img);\n      }\n    };\n\n    const isListGroup = item => hasNonNullableKey(item, 'items');\n    const findTextByValue = (value, catalog) => findMap(catalog, item => {\n      if (isListGroup(item)) {\n        return findTextByValue(value, item.items);\n      } else {\n        return someIf(item.value === value, item);\n      }\n    });\n    const getDelta = (persistentText, fieldName, catalog, data) => {\n      const value = data[fieldName];\n      const hasPersistentText = persistentText.length > 0;\n      return value !== undefined ? findTextByValue(value, catalog).map(i => ({\n        url: {\n          value: i.value,\n          meta: {\n            text: hasPersistentText ? persistentText : i.text,\n            attach: noop\n          }\n        },\n        text: hasPersistentText ? persistentText : i.text\n      })) : Optional.none();\n    };\n    const findCatalog = (catalogs, fieldName) => {\n      if (fieldName === 'link') {\n        return catalogs.link;\n      } else if (fieldName === 'anchor') {\n        return catalogs.anchor;\n      } else {\n        return Optional.none();\n      }\n    };\n    const init = (initialData, linkCatalog) => {\n      const persistentData = {\n        text: initialData.text,\n        title: initialData.title\n      };\n      const getTitleFromUrlChange = url => {\n        var _a;\n        return someIf(persistentData.title.length <= 0, Optional.from((_a = url.meta) === null || _a === void 0 ? void 0 : _a.title).getOr(''));\n      };\n      const getTextFromUrlChange = url => {\n        var _a;\n        return someIf(persistentData.text.length <= 0, Optional.from((_a = url.meta) === null || _a === void 0 ? void 0 : _a.text).getOr(url.value));\n      };\n      const onUrlChange = data => {\n        const text = getTextFromUrlChange(data.url);\n        const title = getTitleFromUrlChange(data.url);\n        if (text.isSome() || title.isSome()) {\n          return Optional.some({\n            ...text.map(text => ({ text })).getOr({}),\n            ...title.map(title => ({ title })).getOr({})\n          });\n        } else {\n          return Optional.none();\n        }\n      };\n      const onCatalogChange = (data, change) => {\n        const catalog = findCatalog(linkCatalog, change).getOr([]);\n        return getDelta(persistentData.text, change, catalog, data);\n      };\n      const onChange = (getData, change) => {\n        const name = change.name;\n        if (name === 'url') {\n          return onUrlChange(getData());\n        } else if (contains([\n            'anchor',\n            'link'\n          ], name)) {\n          return onCatalogChange(getData(), name);\n        } else if (name === 'text' || name === 'title') {\n          persistentData[name] = getData()[name];\n          return Optional.none();\n        } else {\n          return Optional.none();\n        }\n      };\n      return { onChange };\n    };\n    const DialogChanges = {\n      init,\n      getDelta\n    };\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.util.Delay');\n\n    const delayedConfirm = (editor, message, callback) => {\n      const rng = editor.selection.getRng();\n      global$1.setEditorTimeout(editor, () => {\n        editor.windowManager.confirm(message, state => {\n          editor.selection.setRng(rng);\n          callback(state);\n        });\n      });\n    };\n    const tryEmailTransform = data => {\n      const url = data.href;\n      const suggestMailTo = url.indexOf('@') > 0 && url.indexOf('/') === -1 && url.indexOf('mailto:') === -1;\n      return suggestMailTo ? Optional.some({\n        message: 'The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?',\n        preprocess: oldData => ({\n          ...oldData,\n          href: 'mailto:' + url\n        })\n      }) : Optional.none();\n    };\n    const tryProtocolTransform = (assumeExternalTargets, defaultLinkProtocol) => data => {\n      const url = data.href;\n      const suggestProtocol = assumeExternalTargets === 1 && !hasProtocol(url) || assumeExternalTargets === 0 && /^\\s*www(\\.|\\d\\.)/i.test(url);\n      return suggestProtocol ? Optional.some({\n        message: `The URL you entered seems to be an external link. Do you want to add the required ${ defaultLinkProtocol }:// prefix?`,\n        preprocess: oldData => ({\n          ...oldData,\n          href: defaultLinkProtocol + '://' + url\n        })\n      }) : Optional.none();\n    };\n    const preprocess = (editor, data) => findMap([\n      tryEmailTransform,\n      tryProtocolTransform(assumeExternalTargets(editor), getDefaultLinkProtocol(editor))\n    ], f => f(data)).fold(() => Promise.resolve(data), transform => new Promise(callback => {\n      delayedConfirm(editor, transform.message, state => {\n        callback(state ? transform.preprocess(data) : data);\n      });\n    }));\n    const DialogConfirms = { preprocess };\n\n    const getAnchors = editor => {\n      const anchorNodes = editor.dom.select('a:not([href])');\n      const anchors = bind(anchorNodes, anchor => {\n        const id = anchor.name || anchor.id;\n        return id ? [{\n            text: id,\n            value: '#' + id\n          }] : [];\n      });\n      return anchors.length > 0 ? Optional.some([{\n          text: 'None',\n          value: ''\n        }].concat(anchors)) : Optional.none();\n    };\n    const AnchorListOptions = { getAnchors };\n\n    const getClasses = editor => {\n      const list = getLinkClassList(editor);\n      if (list.length > 0) {\n        return ListOptions.sanitize(list);\n      }\n      return Optional.none();\n    };\n    const ClassListOptions = { getClasses };\n\n    const parseJson = text => {\n      try {\n        return Optional.some(JSON.parse(text));\n      } catch (err) {\n        return Optional.none();\n      }\n    };\n    const getLinks = editor => {\n      const extractor = item => editor.convertURL(item.value || item.url || '', 'href');\n      const linkList = getLinkList(editor);\n      return new Promise(resolve => {\n        if (isString(linkList)) {\n          fetch(linkList).then(res => res.ok ? res.text().then(parseJson) : Promise.reject()).then(resolve, () => resolve(Optional.none()));\n        } else if (isFunction(linkList)) {\n          linkList(output => resolve(Optional.some(output)));\n        } else {\n          resolve(Optional.from(linkList));\n        }\n      }).then(optItems => optItems.bind(ListOptions.sanitizeWith(extractor)).map(items => {\n        if (items.length > 0) {\n          const noneItem = [{\n              text: 'None',\n              value: ''\n            }];\n          return noneItem.concat(items);\n        } else {\n          return items;\n        }\n      }));\n    };\n    const LinkListOptions = { getLinks };\n\n    const getRels = (editor, initialTarget) => {\n      const list = getRelList(editor);\n      if (list.length > 0) {\n        const isTargetBlank = is(initialTarget, '_blank');\n        const enforceSafe = allowUnsafeLinkTarget(editor) === false;\n        const safeRelExtractor = item => applyRelTargetRules(ListOptions.getValue(item), isTargetBlank);\n        const sanitizer = enforceSafe ? ListOptions.sanitizeWith(safeRelExtractor) : ListOptions.sanitize;\n        return sanitizer(list);\n      }\n      return Optional.none();\n    };\n    const RelOptions = { getRels };\n\n    const fallbacks = [\n      {\n        text: 'Current window',\n        value: ''\n      },\n      {\n        text: 'New window',\n        value: '_blank'\n      }\n    ];\n    const getTargets = editor => {\n      const list = getTargetList(editor);\n      if (isArray(list)) {\n        return ListOptions.sanitize(list).orThunk(() => Optional.some(fallbacks));\n      } else if (list === false) {\n        return Optional.none();\n      }\n      return Optional.some(fallbacks);\n    };\n    const TargetOptions = { getTargets };\n\n    const nonEmptyAttr = (dom, elem, name) => {\n      const val = dom.getAttrib(elem, name);\n      return val !== null && val.length > 0 ? Optional.some(val) : Optional.none();\n    };\n    const extractFromAnchor = (editor, anchor) => {\n      const dom = editor.dom;\n      const onlyText = isOnlyTextSelected(editor);\n      const text = onlyText ? Optional.some(getAnchorText(editor.selection, anchor)) : Optional.none();\n      const url = anchor.bind(anchorElm => Optional.from(dom.getAttrib(anchorElm, 'href')));\n      const target = anchor.bind(anchorElm => Optional.from(dom.getAttrib(anchorElm, 'target')));\n      const rel = anchor.bind(anchorElm => nonEmptyAttr(dom, anchorElm, 'rel'));\n      const linkClass = anchor.bind(anchorElm => nonEmptyAttr(dom, anchorElm, 'class'));\n      const title = anchor.bind(anchorElm => nonEmptyAttr(dom, anchorElm, 'title'));\n      return {\n        url,\n        text,\n        title,\n        target,\n        rel,\n        linkClass\n      };\n    };\n    const collect = (editor, linkNode) => LinkListOptions.getLinks(editor).then(links => {\n      const anchor = extractFromAnchor(editor, linkNode);\n      return {\n        anchor,\n        catalogs: {\n          targets: TargetOptions.getTargets(editor),\n          rels: RelOptions.getRels(editor, anchor.target),\n          classes: ClassListOptions.getClasses(editor),\n          anchor: AnchorListOptions.getAnchors(editor),\n          link: links\n        },\n        optNode: linkNode,\n        flags: { titleEnabled: shouldShowLinkTitle(editor) }\n      };\n    });\n    const DialogInfo = { collect };\n\n    const handleSubmit = (editor, info) => api => {\n      const data = api.getData();\n      if (!data.url.value) {\n        unlink(editor);\n        api.close();\n        return;\n      }\n      const getChangedValue = key => Optional.from(data[key]).filter(value => !is(info.anchor[key], value));\n      const changedData = {\n        href: data.url.value,\n        text: getChangedValue('text'),\n        target: getChangedValue('target'),\n        rel: getChangedValue('rel'),\n        class: getChangedValue('linkClass'),\n        title: getChangedValue('title')\n      };\n      const attachState = {\n        href: data.url.value,\n        attach: data.url.meta !== undefined && data.url.meta.attach ? data.url.meta.attach : noop\n      };\n      DialogConfirms.preprocess(editor, changedData).then(pData => {\n        link(editor, attachState, pData);\n      });\n      api.close();\n    };\n    const collectData = editor => {\n      const anchorNode = getAnchorElement(editor);\n      return DialogInfo.collect(editor, anchorNode);\n    };\n    const getInitialData = (info, defaultTarget) => {\n      const anchor = info.anchor;\n      const url = anchor.url.getOr('');\n      return {\n        url: {\n          value: url,\n          meta: { original: { value: url } }\n        },\n        text: anchor.text.getOr(''),\n        title: anchor.title.getOr(''),\n        anchor: url,\n        link: url,\n        rel: anchor.rel.getOr(''),\n        target: anchor.target.or(defaultTarget).getOr(''),\n        linkClass: anchor.linkClass.getOr('')\n      };\n    };\n    const makeDialog = (settings, onSubmit, editor) => {\n      const urlInput = [{\n          name: 'url',\n          type: 'urlinput',\n          filetype: 'file',\n          label: 'URL'\n        }];\n      const displayText = settings.anchor.text.map(() => ({\n        name: 'text',\n        type: 'input',\n        label: 'Text to display'\n      })).toArray();\n      const titleText = settings.flags.titleEnabled ? [{\n          name: 'title',\n          type: 'input',\n          label: 'Title'\n        }] : [];\n      const defaultTarget = Optional.from(getDefaultLinkTarget(editor));\n      const initialData = getInitialData(settings, defaultTarget);\n      const catalogs = settings.catalogs;\n      const dialogDelta = DialogChanges.init(initialData, catalogs);\n      const body = {\n        type: 'panel',\n        items: flatten([\n          urlInput,\n          displayText,\n          titleText,\n          cat([\n            catalogs.anchor.map(ListOptions.createUi('anchor', 'Anchors')),\n            catalogs.rels.map(ListOptions.createUi('rel', 'Rel')),\n            catalogs.targets.map(ListOptions.createUi('target', 'Open link in...')),\n            catalogs.link.map(ListOptions.createUi('link', 'Link list')),\n            catalogs.classes.map(ListOptions.createUi('linkClass', 'Class'))\n          ])\n        ])\n      };\n      return {\n        title: 'Insert/Edit Link',\n        size: 'normal',\n        body,\n        buttons: [\n          {\n            type: 'cancel',\n            name: 'cancel',\n            text: 'Cancel'\n          },\n          {\n            type: 'submit',\n            name: 'save',\n            text: 'Save',\n            primary: true\n          }\n        ],\n        initialData,\n        onChange: (api, {name}) => {\n          dialogDelta.onChange(api.getData, { name }).each(newData => {\n            api.setData(newData);\n          });\n        },\n        onSubmit\n      };\n    };\n    const open$1 = editor => {\n      const data = collectData(editor);\n      data.then(info => {\n        const onSubmit = handleSubmit(editor, info);\n        return makeDialog(info, onSubmit, editor);\n      }).then(spec => {\n        editor.windowManager.open(spec);\n      });\n    };\n\n    const register = editor => {\n      editor.addCommand('mceLink', (_ui, value) => {\n        if ((value === null || value === void 0 ? void 0 : value.dialog) === true || !useQuickLink(editor)) {\n          open$1(editor);\n        } else {\n          editor.dispatch('contexttoolbar-show', { toolbarKey: 'quicklink' });\n        }\n      });\n    };\n\n    var global = tinymce.util.Tools.resolve('tinymce.util.VK');\n\n    const appendClickRemove = (link, evt) => {\n      document.body.appendChild(link);\n      link.dispatchEvent(evt);\n      document.body.removeChild(link);\n    };\n    const open = url => {\n      const link = document.createElement('a');\n      link.target = '_blank';\n      link.href = url;\n      link.rel = 'noreferrer noopener';\n      const evt = document.createEvent('MouseEvents');\n      evt.initMouseEvent('click', true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);\n      appendClickRemove(link, evt);\n    };\n\n    const getLink = (editor, elm) => editor.dom.getParent(elm, 'a[href]');\n    const getSelectedLink = editor => getLink(editor, editor.selection.getStart());\n    const hasOnlyAltModifier = e => {\n      return e.altKey === true && e.shiftKey === false && e.ctrlKey === false && e.metaKey === false;\n    };\n    const gotoLink = (editor, a) => {\n      if (a) {\n        const href = getHref(a);\n        if (/^#/.test(href)) {\n          const targetEl = editor.dom.select(href);\n          if (targetEl.length) {\n            editor.selection.scrollIntoView(targetEl[0], true);\n          }\n        } else {\n          open(a.href);\n        }\n      }\n    };\n    const openDialog = editor => () => {\n      editor.execCommand('mceLink', false, { dialog: true });\n    };\n    const gotoSelectedLink = editor => () => {\n      gotoLink(editor, getSelectedLink(editor));\n    };\n    const setupGotoLinks = editor => {\n      editor.on('click', e => {\n        const link = getLink(editor, e.target);\n        if (link && global.metaKeyPressed(e)) {\n          e.preventDefault();\n          gotoLink(editor, link);\n        }\n      });\n      editor.on('keydown', e => {\n        if (!e.isDefaultPrevented() && e.keyCode === 13 && hasOnlyAltModifier(e)) {\n          const link = getSelectedLink(editor);\n          if (link) {\n            e.preventDefault();\n            gotoLink(editor, link);\n          }\n        }\n      });\n    };\n    const toggleState = (editor, toggler) => {\n      editor.on('NodeChange', toggler);\n      return () => editor.off('NodeChange', toggler);\n    };\n    const toggleLinkState = editor => api => {\n      const updateState = () => {\n        api.setActive(!editor.mode.isReadOnly() && isInAnchor(editor, editor.selection.getNode()));\n        api.setEnabled(editor.selection.isEditable());\n      };\n      updateState();\n      return toggleState(editor, updateState);\n    };\n    const toggleLinkMenuState = editor => api => {\n      const updateState = () => {\n        api.setEnabled(editor.selection.isEditable());\n      };\n      updateState();\n      return toggleState(editor, updateState);\n    };\n    const hasExactlyOneLinkInSelection = editor => {\n      const links = editor.selection.isCollapsed() ? getLinks$1(editor.dom.getParents(editor.selection.getStart())) : getLinksInSelection(editor.selection.getRng());\n      return links.length === 1;\n    };\n    const toggleGotoLinkState = editor => api => {\n      const updateState = () => api.setEnabled(hasExactlyOneLinkInSelection(editor));\n      updateState();\n      return toggleState(editor, updateState);\n    };\n    const toggleUnlinkState = editor => api => {\n      const hasLinks$1 = parents => hasLinks(parents) || hasLinksInSelection(editor.selection.getRng());\n      const parents = editor.dom.getParents(editor.selection.getStart());\n      const updateEnabled = parents => {\n        api.setEnabled(hasLinks$1(parents) && editor.selection.isEditable());\n      };\n      updateEnabled(parents);\n      return toggleState(editor, e => updateEnabled(e.parents));\n    };\n\n    const setup = editor => {\n      editor.addShortcut('Meta+K', '', () => {\n        editor.execCommand('mceLink');\n      });\n    };\n\n    const setupButtons = editor => {\n      editor.ui.registry.addToggleButton('link', {\n        icon: 'link',\n        tooltip: 'Insert/edit link',\n        onAction: openDialog(editor),\n        onSetup: toggleLinkState(editor)\n      });\n      editor.ui.registry.addButton('openlink', {\n        icon: 'new-tab',\n        tooltip: 'Open link',\n        onAction: gotoSelectedLink(editor),\n        onSetup: toggleGotoLinkState(editor)\n      });\n      editor.ui.registry.addButton('unlink', {\n        icon: 'unlink',\n        tooltip: 'Remove link',\n        onAction: () => unlink(editor),\n        onSetup: toggleUnlinkState(editor)\n      });\n    };\n    const setupMenuItems = editor => {\n      editor.ui.registry.addMenuItem('openlink', {\n        text: 'Open link',\n        icon: 'new-tab',\n        onAction: gotoSelectedLink(editor),\n        onSetup: toggleGotoLinkState(editor)\n      });\n      editor.ui.registry.addMenuItem('link', {\n        icon: 'link',\n        text: 'Link...',\n        shortcut: 'Meta+K',\n        onSetup: toggleLinkMenuState(editor),\n        onAction: openDialog(editor)\n      });\n      editor.ui.registry.addMenuItem('unlink', {\n        icon: 'unlink',\n        text: 'Remove link',\n        onAction: () => unlink(editor),\n        onSetup: toggleUnlinkState(editor)\n      });\n    };\n    const setupContextMenu = editor => {\n      const inLink = 'link unlink openlink';\n      const noLink = 'link';\n      editor.ui.registry.addContextMenu('link', {\n        update: element => {\n          const isEditable = editor.dom.isEditable(element);\n          if (!isEditable) {\n            return '';\n          }\n          return hasLinks(editor.dom.getParents(element, 'a')) ? inLink : noLink;\n        }\n      });\n    };\n    const setupContextToolbars = editor => {\n      const collapseSelectionToEnd = editor => {\n        editor.selection.collapse(false);\n      };\n      const onSetupLink = buttonApi => {\n        const node = editor.selection.getNode();\n        buttonApi.setEnabled(isInAnchor(editor, node));\n        return noop;\n      };\n      const getLinkText = value => {\n        const anchor = getAnchorElement(editor);\n        const onlyText = isOnlyTextSelected(editor);\n        if (anchor.isNone() && onlyText) {\n          const text = getAnchorText(editor.selection, anchor);\n          return someIf(text.length === 0, value);\n        } else {\n          return Optional.none();\n        }\n      };\n      editor.ui.registry.addContextForm('quicklink', {\n        launch: {\n          type: 'contextformtogglebutton',\n          icon: 'link',\n          tooltip: 'Link',\n          onSetup: toggleLinkState(editor)\n        },\n        label: 'Link',\n        predicate: node => hasContextToolbar(editor) && isInAnchor(editor, node),\n        initValue: () => {\n          const elm = getAnchorElement(editor);\n          return elm.fold(constant(''), getHref);\n        },\n        commands: [\n          {\n            type: 'contextformtogglebutton',\n            icon: 'link',\n            tooltip: 'Link',\n            primary: true,\n            onSetup: buttonApi => {\n              const node = editor.selection.getNode();\n              buttonApi.setActive(isInAnchor(editor, node));\n              return toggleLinkState(editor)(buttonApi);\n            },\n            onAction: formApi => {\n              const value = formApi.getValue();\n              const text = getLinkText(value);\n              const attachState = {\n                href: value,\n                attach: noop\n              };\n              link(editor, attachState, {\n                href: value,\n                text,\n                title: Optional.none(),\n                rel: Optional.none(),\n                target: Optional.none(),\n                class: Optional.none()\n              });\n              collapseSelectionToEnd(editor);\n              formApi.hide();\n            }\n          },\n          {\n            type: 'contextformbutton',\n            icon: 'unlink',\n            tooltip: 'Remove link',\n            onSetup: onSetupLink,\n            onAction: formApi => {\n              unlink(editor);\n              formApi.hide();\n            }\n          },\n          {\n            type: 'contextformbutton',\n            icon: 'new-tab',\n            tooltip: 'Open link',\n            onSetup: onSetupLink,\n            onAction: formApi => {\n              gotoSelectedLink(editor)();\n              formApi.hide();\n            }\n          }\n        ]\n      });\n    };\n\n    var Plugin = () => {\n      global$5.add('link', editor => {\n        register$1(editor);\n        setupButtons(editor);\n        setupMenuItems(editor);\n        setupContextMenu(editor);\n        setupContextToolbars(editor);\n        setupGotoLinks(editor);\n        register(editor);\n        setup(editor);\n      });\n    };\n\n    Plugin();\n\n})();\n", "// Exports the \"link\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/link')\n//   ES2015:\n//     import 'tinymce/plugins/link'\nrequire('./plugin.js');"], "mappings": ";;;;;AAAA;AAAA;AAIA,KAAC,WAAY;AACT;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,YAAM,WAAW,CAAC,GAAG,aAAa,cAAc;AAC9C,YAAI;AACJ,YAAI,UAAU,GAAG,YAAY,SAAS,GAAG;AACvC,iBAAO;AAAA,QACT,OAAO;AACL,mBAAS,KAAK,EAAE,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,YAAY;AAAA,QAC7F;AAAA,MACF;AACA,YAAM,SAAS,OAAK;AAClB,cAAM,IAAI,OAAO;AACjB,YAAI,MAAM,MAAM;AACd,iBAAO;AAAA,QACT,WAAW,MAAM,YAAY,MAAM,QAAQ,CAAC,GAAG;AAC7C,iBAAO;AAAA,QACT,WAAW,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,UAAU,MAAM,cAAc,CAAC,CAAC,GAAG;AACtF,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,SAAS,UAAQ,WAAS,OAAO,KAAK,MAAM;AAClD,YAAM,eAAe,UAAQ,WAAS,OAAO,UAAU;AACvD,YAAM,KAAK,OAAK,OAAK,MAAM;AAC3B,YAAM,WAAW,OAAO,QAAQ;AAChC,YAAM,WAAW,OAAO,QAAQ;AAChC,YAAM,UAAU,OAAO,OAAO;AAC9B,YAAM,SAAS,GAAG,IAAI;AACtB,YAAM,YAAY,aAAa,SAAS;AACxC,YAAM,aAAa,OAAK,MAAM,QAAQ,MAAM;AAC5C,YAAM,gBAAgB,OAAK,CAAC,WAAW,CAAC;AACxC,YAAM,aAAa,aAAa,UAAU;AAC1C,YAAM,YAAY,CAAC,OAAO,SAAS;AACjC,YAAI,QAAQ,KAAK,GAAG;AAClB,mBAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,EAAE,GAAG;AAChD,gBAAI,CAAC,KAAK,MAAM,CAAC,CAAC,GAAG;AACnB,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAEA,YAAM,OAAO,MAAM;AAAA,MACnB;AACA,YAAM,WAAW,WAAS;AACxB,eAAO,MAAM;AACX,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,eAAe,CAAC,GAAG,MAAM;AAC7B,eAAO,MAAM;AAAA,MACf;AAAA,MAEA,MAAM,SAAS;AAAA,QACb,YAAY,KAAK,OAAO;AACtB,eAAK,MAAM;AACX,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,OAAO,KAAK,OAAO;AACjB,iBAAO,IAAI,SAAS,MAAM,KAAK;AAAA,QACjC;AAAA,QACA,OAAO,OAAO;AACZ,iBAAO,SAAS;AAAA,QAClB;AAAA,QACA,KAAK,QAAQ,QAAQ;AACnB,cAAI,KAAK,KAAK;AACZ,mBAAO,OAAO,KAAK,KAAK;AAAA,UAC1B,OAAO;AACL,mBAAO,OAAO;AAAA,UAChB;AAAA,QACF;AAAA,QACA,SAAS;AACP,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,SAAS;AACP,iBAAO,CAAC,KAAK;AAAA,QACf;AAAA,QACA,IAAI,QAAQ;AACV,cAAI,KAAK,KAAK;AACZ,mBAAO,SAAS,KAAK,OAAO,KAAK,KAAK,CAAC;AAAA,UACzC,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,KAAK,KAAK;AACZ,mBAAO,OAAO,KAAK,KAAK;AAAA,UAC1B,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,OAAO,WAAW;AAChB,iBAAO,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,QACzC;AAAA,QACA,OAAO,WAAW;AAChB,iBAAO,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,QAC1C;AAAA,QACA,OAAO,WAAW;AAChB,cAAI,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK,GAAG;AACtC,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,MAAM,aAAa;AACjB,iBAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,QACjC;AAAA,QACA,GAAG,aAAa;AACd,iBAAO,KAAK,MAAM,OAAO;AAAA,QAC3B;AAAA,QACA,WAAW,OAAO;AAChB,iBAAO,KAAK,MAAM,KAAK,QAAQ,MAAM;AAAA,QACvC;AAAA,QACA,QAAQ,OAAO;AACb,iBAAO,KAAK,MAAM,OAAO,MAAM;AAAA,QACjC;AAAA,QACA,SAAS,SAAS;AAChB,cAAI,CAAC,KAAK,KAAK;AACb,kBAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,SAAS,UAAU,yBAAyB;AAAA,UAC9F,OAAO;AACL,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAAA,QACA,OAAO,KAAK,OAAO;AACjB,iBAAO,cAAc,KAAK,IAAI,SAAS,KAAK,KAAK,IAAI,SAAS,KAAK;AAAA,QACrE;AAAA,QACA,YAAY;AACV,iBAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,QACjC;AAAA,QACA,iBAAiB;AACf,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,KAAK,KAAK;AACZ,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF;AAAA,QACA,UAAU;AACR,iBAAO,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,QACpC;AAAA,QACA,WAAW;AACT,iBAAO,KAAK,MAAM,QAAS,KAAK,KAAM,MAAM;AAAA,QAC9C;AAAA,MACF;AACA,eAAS,gBAAgB,IAAI,SAAS,KAAK;AAE3C,YAAM,gBAAgB,MAAM,UAAU;AACtC,YAAM,aAAa,MAAM,UAAU;AACnC,YAAM,aAAa,CAAC,IAAI,MAAM,cAAc,KAAK,IAAI,CAAC;AACtD,YAAM,WAAW,CAAC,IAAI,MAAM,WAAW,IAAI,CAAC,IAAI;AAChD,YAAM,MAAM,CAAC,IAAI,MAAM;AACrB,cAAM,MAAM,GAAG;AACf,cAAM,IAAI,IAAI,MAAM,GAAG;AACvB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,gBAAM,IAAI,GAAG,CAAC;AACd,YAAE,CAAC,IAAI,EAAE,GAAG,CAAC;AAAA,QACf;AACA,eAAO;AAAA,MACT;AACA,YAAM,SAAS,CAAC,IAAI,MAAM;AACxB,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG,CAAC;AACd,YAAE,GAAG,CAAC;AAAA,QACR;AAAA,MACF;AACA,YAAM,QAAQ,CAAC,IAAI,GAAG,QAAQ;AAC5B,eAAO,IAAI,CAAC,GAAG,MAAM;AACnB,gBAAM,EAAE,KAAK,GAAG,CAAC;AAAA,QACnB,CAAC;AACD,eAAO;AAAA,MACT;AACA,YAAM,UAAU,QAAM;AACpB,cAAM,IAAI,CAAC;AACX,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC7C,cAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG;AACnB,kBAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B,EAAE;AAAA,UAC7E;AACA,qBAAW,MAAM,GAAG,GAAG,CAAC,CAAC;AAAA,QAC3B;AACA,eAAO;AAAA,MACT;AACA,YAAM,OAAO,CAAC,IAAI,MAAM,QAAQ,IAAI,IAAI,CAAC,CAAC;AAC1C,YAAM,UAAU,CAAC,KAAK,MAAM;AAC1B,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,gBAAM,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC;AACrB,cAAI,EAAE,OAAO,GAAG;AACd,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO,SAAS,KAAK;AAAA,MACvB;AAEA,YAAM,KAAK,CAAC,KAAK,KAAK,aAAa,iBAAiB,IAAI,OAAO,UAAQ,WAAW,MAAM,GAAG,CAAC;AAC5F,YAAM,MAAM,SAAO;AACjB,cAAM,IAAI,CAAC;AACX,cAAM,OAAO,OAAK;AAChB,YAAE,KAAK,CAAC;AAAA,QACV;AACA,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAI,CAAC,EAAE,KAAK,IAAI;AAAA,QAClB;AACA,eAAO;AAAA,MACT;AACA,YAAM,SAAS,CAAC,GAAG,MAAM,IAAI,SAAS,KAAK,CAAC,IAAI,SAAS,KAAK;AAE9D,YAAM,SAAS,UAAQ,YAAU,OAAO,QAAQ,IAAI,IAAI;AACxD,YAAM,aAAa,YAAU;AAC3B,cAAM,iBAAiB,OAAO,QAAQ;AACtC,uBAAe,gCAAgC;AAAA,UAC7C,WAAW,WAAS;AAClB,kBAAM,QAAQ,SAAS,KAAK,KAAK,UAAU,KAAK;AAChD,gBAAI,OAAO;AACT,kBAAI,UAAU,MAAM;AAClB,uBAAO;AAAA,kBACL,OAAO;AAAA,kBACP;AAAA,gBACF;AAAA,cACF,WAAW,UAAU,UAAU,UAAU,SAAS;AAChD,uBAAO;AAAA,kBACL;AAAA,kBACA;AAAA,gBACF;AAAA,cACF,OAAO;AACL,uBAAO;AAAA,kBACL,OAAO;AAAA,kBACP;AAAA,gBACF;AAAA,cACF;AAAA,YACF,OAAO;AACL,qBAAO;AAAA,gBACL,OAAO;AAAA,gBACP,SAAS;AAAA,cACX;AAAA,YACF;AAAA,UACF;AAAA,UACA,SAAS;AAAA,QACX,CAAC;AACD,uBAAe,wBAAwB;AAAA,UACrC,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AACD,uBAAe,aAAa,EAAE,WAAW,WAAS,SAAS,KAAK,KAAK,WAAW,KAAK,KAAK,UAAU,OAAO,QAAQ,EAAE,CAAC;AACtH,uBAAe,uBAAuB,EAAE,WAAW,SAAS,CAAC;AAC7D,uBAAe,yBAAyB;AAAA,UACtC,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AACD,uBAAe,oBAAoB;AAAA,UACjC,WAAW,WAAS,UAAU,KAAK,KAAK,UAAU,OAAO,QAAQ;AAAA,UACjE,SAAS;AAAA,QACX,CAAC;AACD,uBAAe,iBAAiB;AAAA,UAC9B,WAAW;AAAA,UACX,SAAS,CAAC;AAAA,QACZ,CAAC;AACD,uBAAe,mBAAmB;AAAA,UAChC,WAAW;AAAA,UACX,SAAS,CAAC;AAAA,QACZ,CAAC;AACD,uBAAe,cAAc;AAAA,UAC3B,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AACD,uBAAe,4BAA4B;AAAA,UACzC,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AACD,uBAAe,kBAAkB;AAAA,UAC/B,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AACA,YAAM,wBAAwB,OAAO,8BAA8B;AACnE,YAAM,oBAAoB,OAAO,sBAAsB;AACvD,YAAM,cAAc,OAAO,WAAW;AACtC,YAAM,uBAAuB,OAAO,qBAAqB;AACzD,YAAM,yBAAyB,OAAO,uBAAuB;AAC7D,YAAM,gBAAgB,OAAO,kBAAkB;AAC/C,YAAM,aAAa,OAAO,eAAe;AACzC,YAAM,mBAAmB,OAAO,iBAAiB;AACjD,YAAM,sBAAsB,OAAO,YAAY;AAC/C,YAAM,wBAAwB,OAAO,0BAA0B;AAC/D,YAAM,eAAe,OAAO,gBAAgB;AAE5C,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,oBAAoB;AAE9D,YAAM,WAAW,UAAQ,SAAS,KAAK,KAAK,IAAI,KAAK,QAAQ;AAC7D,YAAM,UAAU,UAAQ;AACtB,YAAI,SAAS,KAAK,IAAI,GAAG;AACvB,iBAAO,KAAK;AAAA,QACd,WAAW,SAAS,KAAK,KAAK,GAAG;AAC/B,iBAAO,KAAK;AAAA,QACd,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,eAAe,CAAC,MAAM,iBAAiB;AAC3C,cAAM,MAAM,CAAC;AACb,iBAAS,KAAK,MAAM,UAAQ;AAC1B,gBAAM,OAAO,QAAQ,IAAI;AACzB,cAAI,KAAK,SAAS,QAAW;AAC3B,kBAAM,QAAQ,aAAa,KAAK,MAAM,YAAY;AAClD,gBAAI,KAAK;AAAA,cACP;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH,OAAO;AACL,kBAAM,QAAQ,aAAa,IAAI;AAC/B,gBAAI,KAAK;AAAA,cACP;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AACD,eAAO;AAAA,MACT;AACA,YAAM,eAAe,CAAC,YAAY,aAAa,UAAQ,SAAS,KAAK,IAAI,EAAE,IAAI,CAAAA,UAAQ,aAAaA,OAAM,SAAS,CAAC;AACpH,YAAM,WAAW,UAAQ,aAAa,QAAQ,EAAE,IAAI;AACpD,YAAM,WAAW,CAAC,MAAM,UAAU,YAAU;AAAA,QAC1C;AAAA,QACA,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AACA,YAAM,cAAc;AAAA,QAClB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,OAAO,OAAO;AACpB,YAAM,iBAAiB,OAAO;AAC9B,YAAM,OAAO,CAAC,KAAK,MAAM;AACvB,cAAM,QAAQ,KAAK,GAAG;AACtB,iBAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,gBAAM,IAAI,MAAM,CAAC;AACjB,gBAAM,IAAI,IAAI,CAAC;AACf,YAAE,GAAG,CAAC;AAAA,QACR;AAAA,MACF;AACA,YAAM,SAAS,OAAK,CAAC,GAAG,MAAM;AAC5B,UAAE,CAAC,IAAI;AAAA,MACT;AACA,YAAM,iBAAiB,CAAC,KAAK,MAAM,QAAQ,YAAY;AACrD,aAAK,KAAK,CAAC,GAAG,MAAM;AAClB,WAAC,KAAK,GAAG,CAAC,IAAI,SAAS,SAAS,GAAG,CAAC;AAAA,QACtC,CAAC;AAAA,MACH;AACA,YAAM,SAAS,CAAC,KAAK,SAAS;AAC5B,cAAM,IAAI,CAAC;AACX,uBAAe,KAAK,MAAM,OAAO,CAAC,GAAG,IAAI;AACzC,eAAO;AAAA,MACT;AACA,YAAM,MAAM,CAAC,KAAK,QAAQ,eAAe,KAAK,KAAK,GAAG;AACtD,YAAM,oBAAoB,CAAC,KAAK,QAAQ,IAAI,KAAK,GAAG,KAAK,IAAI,GAAG,MAAM,UAAa,IAAI,GAAG,MAAM;AAEhG,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,wBAAwB;AAElE,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,kBAAkB;AAE5D,YAAM,WAAW,SAAO,cAAc,GAAG,KAAK,IAAI,SAAS,YAAY,MAAM;AAC7E,YAAM,SAAS,SAAO,SAAS,GAAG,KAAK,CAAC,CAAC,QAAQ,GAAG;AACpD,YAAM,sBAAsB,CAAC,KAAK,cAAc;AAC9C,YAAI,IAAI,WAAW;AACjB,iBAAO,CAAC;AAAA,QACV,OAAO;AACL,gBAAM,WAAW,IAAI,cAAc;AACnC,gBAAM,aAAa,SAAS;AAC5B,gBAAM,SAAS,IAAI,SAAS,YAAY,QAAQ;AAChD,gBAAM,WAAW,CAAC;AAClB,cAAI,UAAU;AACd,aAAG;AACD,gBAAI,UAAU,OAAO,GAAG;AACtB,uBAAS,KAAK,OAAO;AAAA,YACvB;AAAA,UACF,SAAS,UAAU,OAAO,KAAK;AAC/B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,cAAc,SAAO,SAAS,KAAK,GAAG;AAC5C,YAAM,UAAU,SAAO;AACrB,YAAI,IAAI;AACR,gBAAQ,MAAM,KAAK,IAAI,aAAa,eAAe,OAAO,QAAQ,OAAO,SAAS,KAAK,IAAI,aAAa,MAAM,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,MACpJ;AACA,YAAM,sBAAsB,CAAC,KAAK,aAAa;AAC7C,cAAM,QAAQ,CAAC,UAAU;AACzB,cAAM,OAAO,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC;AACvC,cAAM,WAAW,CAAAC,UAAQ,SAAS,KAAKA,MAAK,KAAK,EAAE,KAAK,GAAG,CAAC;AAC5D,cAAM,iBAAiB,CAAAA,UAAQ;AAC7B,UAAAA,QAAO,kBAAkBA,KAAI;AAC7B,iBAAOA,MAAK,SAAS,IAAIA,MAAK,OAAO,KAAK,IAAI;AAAA,QAChD;AACA,cAAM,oBAAoB,CAAAA,UAAQA,MAAK,OAAO,SAAO,SAAS,QAAQ,OAAO,GAAG,MAAM,EAAE;AACxF,cAAM,UAAU,WAAW,eAAe,IAAI,IAAI,kBAAkB,IAAI;AACxE,eAAO,QAAQ,SAAS,IAAI,SAAS,OAAO,IAAI;AAAA,MAClD;AACA,YAAM,sBAAsB,UAAQ,KAAK,QAAQ,WAAW,EAAE;AAC9D,YAAM,mBAAmB,CAAC,QAAQ,gBAAgB;AAChD,sBAAc,eAAe,oBAAoB,OAAO,UAAU,OAAO,CAAC,EAAE,CAAC,KAAK,OAAO,UAAU,QAAQ;AAC3G,YAAI,cAAc,WAAW,GAAG;AAC9B,iBAAO,SAAS,KAAK,OAAO,IAAI,OAAO,WAAW,WAAW,EAAE,CAAC,CAAC;AAAA,QACnE,OAAO;AACL,iBAAO,SAAS,KAAK,OAAO,IAAI,UAAU,aAAa,SAAS,CAAC;AAAA,QACnE;AAAA,MACF;AACA,YAAM,aAAa,CAAC,QAAQ,gBAAgB,iBAAiB,QAAQ,WAAW,EAAE,OAAO;AACzF,YAAM,gBAAgB,CAAC,WAAW,cAAc;AAC9C,cAAM,OAAO,UAAU,KAAK,MAAM,UAAU,WAAW,EAAE,QAAQ,OAAO,CAAC,GAAG,CAAAC,eAAaA,WAAU,aAAaA,WAAU,eAAe,EAAE;AAC3I,eAAO,oBAAoB,IAAI;AAAA,MACjC;AACA,YAAM,sBAAsB,SAAO,oBAAoB,KAAK,MAAM;AAClE,YAAM,aAAa,cAAY,SAAS,KAAK,UAAU,MAAM;AAC7D,YAAM,WAAW,cAAY,WAAW,QAAQ,EAAE,SAAS;AAC3D,YAAM,sBAAsB,SAAO,oBAAoB,GAAG,EAAE,SAAS;AACrE,YAAM,qBAAqB,YAAU;AACnC,cAAM,qBAAqB,OAAO,OAAO,sBAAsB;AAC/D,cAAM,YAAY,SAAO,IAAI,aAAa,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,oBAAoB,IAAI,SAAS,YAAY,CAAC;AACpH,cAAM,kBAAkB,iBAAiB,MAAM,EAAE,OAAO,YAAU,OAAO,aAAa,gBAAgB,CAAC;AACvG,YAAI,iBAAiB;AACnB,iBAAO;AAAA,QACT;AACA,cAAM,MAAM,OAAO,UAAU,OAAO;AACpC,YAAI,CAAC,IAAI,WAAW;AAClB,gBAAM,WAAW,oBAAoB,KAAK,SAAS;AACnD,iBAAO,SAAS,WAAW;AAAA,QAC7B,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,gBAAgB,SAAO,cAAc,GAAG,KAAK,IAAI,aAAa,YAAY,aAAa,KAAK,IAAI,SAAS;AAC/G,YAAM,eAAe,UAAQ;AAC3B,cAAM,QAAQ;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,eAAO,MAAM,OAAO,CAAC,KAAK,QAAQ;AAChC,eAAK,GAAG,EAAE,KAAK,WAAS;AACtB,gBAAI,GAAG,IAAI,MAAM,SAAS,IAAI,QAAQ;AAAA,UACxC,CAAC;AACD,iBAAO;AAAA,QACT,GAAG,EAAE,MAAM,KAAK,KAAK,CAAC;AAAA,MACxB;AACA,YAAM,wBAAwB,CAAC,MAAMC,2BAA0B;AAC7D,aAAKA,2BAA0B,UAAUA,2BAA0B,YAAY,CAAC,YAAY,IAAI,GAAG;AACjG,iBAAOA,yBAAwB,QAAQ;AAAA,QACzC;AACA,eAAO;AAAA,MACT;AACA,YAAM,qBAAqB,CAAC,QAAQ,cAAc;AAChD,cAAM,eAAe,EAAE,GAAG,UAAU;AACpC,YAAI,WAAW,MAAM,EAAE,WAAW,KAAK,CAAC,sBAAsB,MAAM,GAAG;AACrE,gBAAM,SAAS,oBAAoB,aAAa,KAAK,aAAa,WAAW,QAAQ;AACrF,uBAAa,MAAM,SAAS,SAAS;AAAA,QACvC;AACA,YAAI,SAAS,KAAK,aAAa,MAAM,EAAE,OAAO,KAAK,cAAc,MAAM,MAAM,OAAO;AAClF,uBAAa,SAAS,qBAAqB,MAAM;AAAA,QACnD;AACA,qBAAa,OAAO,sBAAsB,aAAa,MAAM,sBAAsB,MAAM,CAAC;AAC1F,eAAO;AAAA,MACT;AACA,YAAM,aAAa,CAAC,QAAQ,WAAW,MAAM,cAAc;AACzD,aAAK,KAAK,CAAAC,UAAQ;AAChB,cAAI,IAAI,WAAW,WAAW,GAAG;AAC/B,sBAAU,YAAYA;AAAA,UACxB,OAAO;AACL,sBAAU,cAAcA;AAAA,UAC1B;AAAA,QACF,CAAC;AACD,eAAO,IAAI,WAAW,WAAW,SAAS;AAC1C,eAAO,UAAU,OAAO,SAAS;AAAA,MACnC;AACA,YAAM,aAAa,CAAC,QAAQ,aAAa,MAAM,cAAc;AAC3D,cAAM,MAAM,OAAO;AACnB,YAAI,cAAc,WAAW,GAAG;AAC9B,0BAAgB,KAAK,aAAa,SAAS;AAAA,QAC7C,OAAO;AACL,eAAK,KAAK,MAAM;AACd,mBAAO,YAAY,iBAAiB,OAAO,SAAS;AAAA,UACtD,GAAG,CAAAA,UAAQ;AACT,mBAAO,cAAc,IAAI,WAAW,KAAK,WAAW,IAAI,OAAOA,KAAI,CAAC,CAAC;AAAA,UACvE,CAAC;AAAA,QACH;AAAA,MACF;AACA,YAAM,kBAAkB,CAAC,QAAQ,aAAa,SAAS;AACrD,cAAM,cAAc,OAAO,UAAU,QAAQ;AAC7C,cAAM,YAAY,iBAAiB,QAAQ,WAAW;AACtD,cAAM,YAAY,mBAAmB,QAAQ,aAAa,IAAI,CAAC;AAC/D,eAAO,YAAY,SAAS,MAAM;AAChC,cAAI,KAAK,SAAS,YAAY,MAAM;AAClC,wBAAY,OAAO;AAAA,UACrB;AACA,oBAAU,KAAK,MAAM;AACnB,uBAAW,QAAQ,aAAa,KAAK,MAAM,SAAS;AAAA,UACtD,GAAG,SAAO;AACR,mBAAO,MAAM;AACb,uBAAW,QAAQ,KAAK,KAAK,MAAM,SAAS;AAAA,UAC9C,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,kBAAkB,YAAU;AAChC,cAAM,MAAM,OAAO,KAAK,YAAY,OAAO;AAC3C,cAAM,WAAW,UAAU,YAAY;AACvC,cAAM,MAAM,UAAU,OAAO,EAAE,WAAW;AAC1C,cAAM,iBAAiB,IAAI,UAAU,IAAI,gBAAgB,WAAW,OAAO,QAAQ,CAAC;AACpF,cAAM,eAAe,IAAI,UAAU,IAAI,cAAc,WAAW,OAAO,QAAQ,CAAC;AAChF,YAAI,gBAAgB;AAClB,cAAI,eAAe,cAAc;AAAA,QACnC;AACA,YAAI,cAAc;AAChB,cAAI,YAAY,YAAY;AAAA,QAC9B;AACA,kBAAU,OAAO,GAAG;AACpB,eAAO,YAAY,QAAQ;AAC3B,kBAAU,eAAe,QAAQ;AAAA,MACnC;AACA,YAAM,oBAAoB,YAAU;AAClC,eAAO,YAAY,SAAS,MAAM;AAChC,gBAAM,OAAO,OAAO,UAAU,QAAQ;AACtC,cAAI,cAAc,IAAI,GAAG;AACvB,8BAAkB,QAAQ,IAAI;AAAA,UAChC,OAAO;AACL,4BAAgB,MAAM;AAAA,UACxB;AACA,iBAAO,MAAM;AAAA,QACf,CAAC;AAAA,MACH;AACA,YAAM,gBAAgB,UAAQ;AAC5B,cAAM;AAAA,UACJ,OAAO;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,eAAO,OAAO;AAAA,UACZ,OAAO,IAAI,UAAU;AAAA,UACrB;AAAA,UACA,KAAK,IAAI,UAAU;AAAA,UACnB,QAAQ,OAAO,UAAU;AAAA,UACzB,MAAM,KAAK,UAAU;AAAA,UACrB,OAAO,MAAM,UAAU;AAAA,QACzB,GAAG,CAAC,GAAG,OAAO,OAAO,CAAC,MAAM,KAAK;AAAA,MACnC;AACA,YAAM,eAAe,CAAC,QAAQ,SAAS;AACrC,cAAM,YAAY,OAAO,QAAQ;AACjC,cAAM,aAAa;AAAA,UACjB,sBAAsB,UAAU,sBAAsB;AAAA,UACtD,mBAAmB,UAAU,mBAAmB;AAAA,UAChD,qBAAqB,UAAU,qBAAqB;AAAA,QACtD;AACA,cAAM,OAAO,KAAK;AAClB,eAAO;AAAA,UACL,GAAG;AAAA,UACH,MAAM,SAAS,UAAU,MAAM,KAAK,UAAU,IAAI,OAAO;AAAA,QAC3D;AAAA,MACF;AACA,YAAM,OAAO,CAAC,QAAQ,aAAa,SAAS;AAC1C,cAAM,gBAAgB,aAAa,QAAQ,IAAI;AAC/C,eAAO,UAAU,OAAO,IAAI,IAAI,OAAO,YAAY,cAAc,OAAO,cAAc,aAAa,CAAC,IAAI,gBAAgB,QAAQ,aAAa,aAAa;AAAA,MAC5J;AACA,YAAM,SAAS,YAAU;AACvB,eAAO,UAAU,OAAO,IAAI,IAAI,OAAO,YAAY,QAAQ,IAAI,kBAAkB,MAAM;AAAA,MACzF;AACA,YAAM,oBAAoB,CAAC,QAAQ,QAAQ;AACzC,YAAI;AACJ,cAAM,MAAM,OAAO,IAAI,OAAO,OAAO,GAAG,EAAE,CAAC;AAC3C,YAAI,KAAK;AACP,gBAAM,IAAI,OAAO,IAAI,WAAW,KAAK,WAAW,GAAG,EAAE,CAAC;AACtD,cAAI,GAAG;AACL,aAAC,KAAK,EAAE,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,KAAK,CAAC;AAC/E,mBAAO,IAAI,OAAO,CAAC;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AACA,YAAM,kBAAkB,CAAC,KAAK,KAAK,UAAU;AAC3C,YAAI;AACJ,cAAM,MAAM,IAAI,OAAO,OAAO,GAAG,EAAE,CAAC;AACpC,YAAI,KAAK;AACP,gBAAM,IAAI,IAAI,OAAO,KAAK,KAAK;AAC/B,WAAC,KAAK,IAAI,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,GAAG,GAAG;AACjF,YAAE,YAAY,GAAG;AAAA,QACnB;AAAA,MACF;AAEA,YAAM,cAAc,UAAQ,kBAAkB,MAAM,OAAO;AAC3D,YAAM,kBAAkB,CAAC,OAAO,YAAY,QAAQ,SAAS,UAAQ;AACnE,YAAI,YAAY,IAAI,GAAG;AACrB,iBAAO,gBAAgB,OAAO,KAAK,KAAK;AAAA,QAC1C,OAAO;AACL,iBAAO,OAAO,KAAK,UAAU,OAAO,IAAI;AAAA,QAC1C;AAAA,MACF,CAAC;AACD,YAAM,WAAW,CAAC,gBAAgB,WAAW,SAAS,SAAS;AAC7D,cAAM,QAAQ,KAAK,SAAS;AAC5B,cAAM,oBAAoB,eAAe,SAAS;AAClD,eAAO,UAAU,SAAY,gBAAgB,OAAO,OAAO,EAAE,IAAI,QAAM;AAAA,UACrE,KAAK;AAAA,YACH,OAAO,EAAE;AAAA,YACT,MAAM;AAAA,cACJ,MAAM,oBAAoB,iBAAiB,EAAE;AAAA,cAC7C,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,UACA,MAAM,oBAAoB,iBAAiB,EAAE;AAAA,QAC/C,EAAE,IAAI,SAAS,KAAK;AAAA,MACtB;AACA,YAAM,cAAc,CAAC,UAAU,cAAc;AAC3C,YAAI,cAAc,QAAQ;AACxB,iBAAO,SAAS;AAAA,QAClB,WAAW,cAAc,UAAU;AACjC,iBAAO,SAAS;AAAA,QAClB,OAAO;AACL,iBAAO,SAAS,KAAK;AAAA,QACvB;AAAA,MACF;AACA,YAAM,OAAO,CAAC,aAAa,gBAAgB;AACzC,cAAM,iBAAiB;AAAA,UACrB,MAAM,YAAY;AAAA,UAClB,OAAO,YAAY;AAAA,QACrB;AACA,cAAM,wBAAwB,SAAO;AACnC,cAAI;AACJ,iBAAO,OAAO,eAAe,MAAM,UAAU,GAAG,SAAS,MAAM,KAAK,IAAI,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,EAAE,MAAM,EAAE,CAAC;AAAA,QACxI;AACA,cAAM,uBAAuB,SAAO;AAClC,cAAI;AACJ,iBAAO,OAAO,eAAe,KAAK,UAAU,GAAG,SAAS,MAAM,KAAK,IAAI,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI,EAAE,MAAM,IAAI,KAAK,CAAC;AAAA,QAC7I;AACA,cAAM,cAAc,UAAQ;AAC1B,gBAAM,OAAO,qBAAqB,KAAK,GAAG;AAC1C,gBAAM,QAAQ,sBAAsB,KAAK,GAAG;AAC5C,cAAI,KAAK,OAAO,KAAK,MAAM,OAAO,GAAG;AACnC,mBAAO,SAAS,KAAK;AAAA,cACnB,GAAG,KAAK,IAAI,CAAAA,WAAS,EAAE,MAAAA,MAAK,EAAE,EAAE,MAAM,CAAC,CAAC;AAAA,cACxC,GAAG,MAAM,IAAI,CAAAC,YAAU,EAAE,OAAAA,OAAM,EAAE,EAAE,MAAM,CAAC,CAAC;AAAA,YAC7C,CAAC;AAAA,UACH,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AACA,cAAM,kBAAkB,CAAC,MAAM,WAAW;AACxC,gBAAM,UAAU,YAAY,aAAa,MAAM,EAAE,MAAM,CAAC,CAAC;AACzD,iBAAO,SAAS,eAAe,MAAM,QAAQ,SAAS,IAAI;AAAA,QAC5D;AACA,cAAM,WAAW,CAAC,SAAS,WAAW;AACpC,gBAAM,OAAO,OAAO;AACpB,cAAI,SAAS,OAAO;AAClB,mBAAO,YAAY,QAAQ,CAAC;AAAA,UAC9B,WAAW,SAAS;AAAA,YAChB;AAAA,YACA;AAAA,UACF,GAAG,IAAI,GAAG;AACV,mBAAO,gBAAgB,QAAQ,GAAG,IAAI;AAAA,UACxC,WAAW,SAAS,UAAU,SAAS,SAAS;AAC9C,2BAAe,IAAI,IAAI,QAAQ,EAAE,IAAI;AACrC,mBAAO,SAAS,KAAK;AAAA,UACvB,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AACA,eAAO,EAAE,SAAS;AAAA,MACpB;AACA,YAAM,gBAAgB;AAAA,QACpB;AAAA,QACA;AAAA,MACF;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,oBAAoB;AAE9D,YAAM,iBAAiB,CAAC,QAAQ,SAAS,aAAa;AACpD,cAAM,MAAM,OAAO,UAAU,OAAO;AACpC,iBAAS,iBAAiB,QAAQ,MAAM;AACtC,iBAAO,cAAc,QAAQ,SAAS,WAAS;AAC7C,mBAAO,UAAU,OAAO,GAAG;AAC3B,qBAAS,KAAK;AAAA,UAChB,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,YAAM,oBAAoB,UAAQ;AAChC,cAAM,MAAM,KAAK;AACjB,cAAM,gBAAgB,IAAI,QAAQ,GAAG,IAAI,KAAK,IAAI,QAAQ,GAAG,MAAM,MAAM,IAAI,QAAQ,SAAS,MAAM;AACpG,eAAO,gBAAgB,SAAS,KAAK;AAAA,UACnC,SAAS;AAAA,UACT,YAAY,cAAY;AAAA,YACtB,GAAG;AAAA,YACH,MAAM,YAAY;AAAA,UACpB;AAAA,QACF,CAAC,IAAI,SAAS,KAAK;AAAA,MACrB;AACA,YAAM,uBAAuB,CAACF,wBAAuB,wBAAwB,UAAQ;AACnF,cAAM,MAAM,KAAK;AACjB,cAAM,kBAAkBA,2BAA0B,KAAK,CAAC,YAAY,GAAG,KAAKA,2BAA0B,KAAK,oBAAoB,KAAK,GAAG;AACvI,eAAO,kBAAkB,SAAS,KAAK;AAAA,UACrC,SAAS,qFAAsF,mBAAoB;AAAA,UACnH,YAAY,cAAY;AAAA,YACtB,GAAG;AAAA,YACH,MAAM,sBAAsB,QAAQ;AAAA,UACtC;AAAA,QACF,CAAC,IAAI,SAAS,KAAK;AAAA,MACrB;AACA,YAAM,aAAa,CAAC,QAAQ,SAAS,QAAQ;AAAA,QAC3C;AAAA,QACA,qBAAqB,sBAAsB,MAAM,GAAG,uBAAuB,MAAM,CAAC;AAAA,MACpF,GAAG,OAAK,EAAE,IAAI,CAAC,EAAE,KAAK,MAAM,QAAQ,QAAQ,IAAI,GAAG,eAAa,IAAI,QAAQ,cAAY;AACtF,uBAAe,QAAQ,UAAU,SAAS,WAAS;AACjD,mBAAS,QAAQ,UAAU,WAAW,IAAI,IAAI,IAAI;AAAA,QACpD,CAAC;AAAA,MACH,CAAC,CAAC;AACF,YAAM,iBAAiB,EAAE,WAAW;AAEpC,YAAM,aAAa,YAAU;AAC3B,cAAM,cAAc,OAAO,IAAI,OAAO,eAAe;AACrD,cAAM,UAAU,KAAK,aAAa,YAAU;AAC1C,gBAAM,KAAK,OAAO,QAAQ,OAAO;AACjC,iBAAO,KAAK,CAAC;AAAA,YACT,MAAM;AAAA,YACN,OAAO,MAAM;AAAA,UACf,CAAC,IAAI,CAAC;AAAA,QACV,CAAC;AACD,eAAO,QAAQ,SAAS,IAAI,SAAS,KAAK,CAAC;AAAA,UACvC,MAAM;AAAA,UACN,OAAO;AAAA,QACT,CAAC,EAAE,OAAO,OAAO,CAAC,IAAI,SAAS,KAAK;AAAA,MACxC;AACA,YAAM,oBAAoB,EAAE,WAAW;AAEvC,YAAM,aAAa,YAAU;AAC3B,cAAM,OAAO,iBAAiB,MAAM;AACpC,YAAI,KAAK,SAAS,GAAG;AACnB,iBAAO,YAAY,SAAS,IAAI;AAAA,QAClC;AACA,eAAO,SAAS,KAAK;AAAA,MACvB;AACA,YAAM,mBAAmB,EAAE,WAAW;AAEtC,YAAM,YAAY,UAAQ;AACxB,YAAI;AACF,iBAAO,SAAS,KAAK,KAAK,MAAM,IAAI,CAAC;AAAA,QACvC,SAAS,KAAK;AACZ,iBAAO,SAAS,KAAK;AAAA,QACvB;AAAA,MACF;AACA,YAAM,WAAW,YAAU;AACzB,cAAM,YAAY,UAAQ,OAAO,WAAW,KAAK,SAAS,KAAK,OAAO,IAAI,MAAM;AAChF,cAAM,WAAW,YAAY,MAAM;AACnC,eAAO,IAAI,QAAQ,aAAW;AAC5B,cAAI,SAAS,QAAQ,GAAG;AACtB,kBAAM,QAAQ,EAAE,KAAK,SAAO,IAAI,KAAK,IAAI,KAAK,EAAE,KAAK,SAAS,IAAI,QAAQ,OAAO,CAAC,EAAE,KAAK,SAAS,MAAM,QAAQ,SAAS,KAAK,CAAC,CAAC;AAAA,UAClI,WAAW,WAAW,QAAQ,GAAG;AAC/B,qBAAS,YAAU,QAAQ,SAAS,KAAK,MAAM,CAAC,CAAC;AAAA,UACnD,OAAO;AACL,oBAAQ,SAAS,KAAK,QAAQ,CAAC;AAAA,UACjC;AAAA,QACF,CAAC,EAAE,KAAK,cAAY,SAAS,KAAK,YAAY,aAAa,SAAS,CAAC,EAAE,IAAI,WAAS;AAClF,cAAI,MAAM,SAAS,GAAG;AACpB,kBAAM,WAAW,CAAC;AAAA,cACd,MAAM;AAAA,cACN,OAAO;AAAA,YACT,CAAC;AACH,mBAAO,SAAS,OAAO,KAAK;AAAA,UAC9B,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF,CAAC,CAAC;AAAA,MACJ;AACA,YAAM,kBAAkB,EAAE,SAAS;AAEnC,YAAM,UAAU,CAAC,QAAQ,kBAAkB;AACzC,cAAM,OAAO,WAAW,MAAM;AAC9B,YAAI,KAAK,SAAS,GAAG;AACnB,gBAAM,gBAAgB,GAAG,eAAe,QAAQ;AAChD,gBAAM,cAAc,sBAAsB,MAAM,MAAM;AACtD,gBAAM,mBAAmB,UAAQ,oBAAoB,YAAY,SAAS,IAAI,GAAG,aAAa;AAC9F,gBAAM,YAAY,cAAc,YAAY,aAAa,gBAAgB,IAAI,YAAY;AACzF,iBAAO,UAAU,IAAI;AAAA,QACvB;AACA,eAAO,SAAS,KAAK;AAAA,MACvB;AACA,YAAM,aAAa,EAAE,QAAQ;AAE7B,YAAM,YAAY;AAAA,QAChB;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,aAAa,YAAU;AAC3B,cAAM,OAAO,cAAc,MAAM;AACjC,YAAI,QAAQ,IAAI,GAAG;AACjB,iBAAO,YAAY,SAAS,IAAI,EAAE,QAAQ,MAAM,SAAS,KAAK,SAAS,CAAC;AAAA,QAC1E,WAAW,SAAS,OAAO;AACzB,iBAAO,SAAS,KAAK;AAAA,QACvB;AACA,eAAO,SAAS,KAAK,SAAS;AAAA,MAChC;AACA,YAAM,gBAAgB,EAAE,WAAW;AAEnC,YAAM,eAAe,CAAC,KAAK,MAAM,SAAS;AACxC,cAAM,MAAM,IAAI,UAAU,MAAM,IAAI;AACpC,eAAO,QAAQ,QAAQ,IAAI,SAAS,IAAI,SAAS,KAAK,GAAG,IAAI,SAAS,KAAK;AAAA,MAC7E;AACA,YAAM,oBAAoB,CAAC,QAAQ,WAAW;AAC5C,cAAM,MAAM,OAAO;AACnB,cAAM,WAAW,mBAAmB,MAAM;AAC1C,cAAM,OAAO,WAAW,SAAS,KAAK,cAAc,OAAO,WAAW,MAAM,CAAC,IAAI,SAAS,KAAK;AAC/F,cAAM,MAAM,OAAO,KAAK,eAAa,SAAS,KAAK,IAAI,UAAU,WAAW,MAAM,CAAC,CAAC;AACpF,cAAM,SAAS,OAAO,KAAK,eAAa,SAAS,KAAK,IAAI,UAAU,WAAW,QAAQ,CAAC,CAAC;AACzF,cAAM,MAAM,OAAO,KAAK,eAAa,aAAa,KAAK,WAAW,KAAK,CAAC;AACxE,cAAM,YAAY,OAAO,KAAK,eAAa,aAAa,KAAK,WAAW,OAAO,CAAC;AAChF,cAAM,QAAQ,OAAO,KAAK,eAAa,aAAa,KAAK,WAAW,OAAO,CAAC;AAC5E,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,UAAU,CAAC,QAAQ,aAAa,gBAAgB,SAAS,MAAM,EAAE,KAAK,WAAS;AACnF,cAAM,SAAS,kBAAkB,QAAQ,QAAQ;AACjD,eAAO;AAAA,UACL;AAAA,UACA,UAAU;AAAA,YACR,SAAS,cAAc,WAAW,MAAM;AAAA,YACxC,MAAM,WAAW,QAAQ,QAAQ,OAAO,MAAM;AAAA,YAC9C,SAAS,iBAAiB,WAAW,MAAM;AAAA,YAC3C,QAAQ,kBAAkB,WAAW,MAAM;AAAA,YAC3C,MAAM;AAAA,UACR;AAAA,UACA,SAAS;AAAA,UACT,OAAO,EAAE,cAAc,oBAAoB,MAAM,EAAE;AAAA,QACrD;AAAA,MACF,CAAC;AACD,YAAM,aAAa,EAAE,QAAQ;AAE7B,YAAM,eAAe,CAAC,QAAQ,SAAS,SAAO;AAC5C,cAAM,OAAO,IAAI,QAAQ;AACzB,YAAI,CAAC,KAAK,IAAI,OAAO;AACnB,iBAAO,MAAM;AACb,cAAI,MAAM;AACV;AAAA,QACF;AACA,cAAM,kBAAkB,SAAO,SAAS,KAAK,KAAK,GAAG,CAAC,EAAE,OAAO,WAAS,CAAC,GAAG,KAAK,OAAO,GAAG,GAAG,KAAK,CAAC;AACpG,cAAM,cAAc;AAAA,UAClB,MAAM,KAAK,IAAI;AAAA,UACf,MAAM,gBAAgB,MAAM;AAAA,UAC5B,QAAQ,gBAAgB,QAAQ;AAAA,UAChC,KAAK,gBAAgB,KAAK;AAAA,UAC1B,OAAO,gBAAgB,WAAW;AAAA,UAClC,OAAO,gBAAgB,OAAO;AAAA,QAChC;AACA,cAAM,cAAc;AAAA,UAClB,MAAM,KAAK,IAAI;AAAA,UACf,QAAQ,KAAK,IAAI,SAAS,UAAa,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK,SAAS;AAAA,QACvF;AACA,uBAAe,WAAW,QAAQ,WAAW,EAAE,KAAK,WAAS;AAC3D,eAAK,QAAQ,aAAa,KAAK;AAAA,QACjC,CAAC;AACD,YAAI,MAAM;AAAA,MACZ;AACA,YAAM,cAAc,YAAU;AAC5B,cAAM,aAAa,iBAAiB,MAAM;AAC1C,eAAO,WAAW,QAAQ,QAAQ,UAAU;AAAA,MAC9C;AACA,YAAM,iBAAiB,CAAC,MAAM,kBAAkB;AAC9C,cAAM,SAAS,KAAK;AACpB,cAAM,MAAM,OAAO,IAAI,MAAM,EAAE;AAC/B,eAAO;AAAA,UACL,KAAK;AAAA,YACH,OAAO;AAAA,YACP,MAAM,EAAE,UAAU,EAAE,OAAO,IAAI,EAAE;AAAA,UACnC;AAAA,UACA,MAAM,OAAO,KAAK,MAAM,EAAE;AAAA,UAC1B,OAAO,OAAO,MAAM,MAAM,EAAE;AAAA,UAC5B,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,KAAK,OAAO,IAAI,MAAM,EAAE;AAAA,UACxB,QAAQ,OAAO,OAAO,GAAG,aAAa,EAAE,MAAM,EAAE;AAAA,UAChD,WAAW,OAAO,UAAU,MAAM,EAAE;AAAA,QACtC;AAAA,MACF;AACA,YAAM,aAAa,CAAC,UAAU,UAAU,WAAW;AACjD,cAAM,WAAW,CAAC;AAAA,UACd,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,OAAO;AAAA,QACT,CAAC;AACH,cAAM,cAAc,SAAS,OAAO,KAAK,IAAI,OAAO;AAAA,UAClD,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACT,EAAE,EAAE,QAAQ;AACZ,cAAM,YAAY,SAAS,MAAM,eAAe,CAAC;AAAA,UAC7C,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACT,CAAC,IAAI,CAAC;AACR,cAAM,gBAAgB,SAAS,KAAK,qBAAqB,MAAM,CAAC;AAChE,cAAM,cAAc,eAAe,UAAU,aAAa;AAC1D,cAAM,WAAW,SAAS;AAC1B,cAAM,cAAc,cAAc,KAAK,aAAa,QAAQ;AAC5D,cAAM,OAAO;AAAA,UACX,MAAM;AAAA,UACN,OAAO,QAAQ;AAAA,YACb;AAAA,YACA;AAAA,YACA;AAAA,YACA,IAAI;AAAA,cACF,SAAS,OAAO,IAAI,YAAY,SAAS,UAAU,SAAS,CAAC;AAAA,cAC7D,SAAS,KAAK,IAAI,YAAY,SAAS,OAAO,KAAK,CAAC;AAAA,cACpD,SAAS,QAAQ,IAAI,YAAY,SAAS,UAAU,iBAAiB,CAAC;AAAA,cACtE,SAAS,KAAK,IAAI,YAAY,SAAS,QAAQ,WAAW,CAAC;AAAA,cAC3D,SAAS,QAAQ,IAAI,YAAY,SAAS,aAAa,OAAO,CAAC;AAAA,YACjE,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AACA,eAAO;AAAA,UACL,OAAO;AAAA,UACP,MAAM;AAAA,UACN;AAAA,UACA,SAAS;AAAA,YACP;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,YACR;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,UACF;AAAA,UACA;AAAA,UACA,UAAU,CAAC,KAAK,EAAC,KAAI,MAAM;AACzB,wBAAY,SAAS,IAAI,SAAS,EAAE,KAAK,CAAC,EAAE,KAAK,aAAW;AAC1D,kBAAI,QAAQ,OAAO;AAAA,YACrB,CAAC;AAAA,UACH;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,SAAS,YAAU;AACvB,cAAM,OAAO,YAAY,MAAM;AAC/B,aAAK,KAAK,UAAQ;AAChB,gBAAM,WAAW,aAAa,QAAQ,IAAI;AAC1C,iBAAO,WAAW,MAAM,UAAU,MAAM;AAAA,QAC1C,CAAC,EAAE,KAAK,UAAQ;AACd,iBAAO,cAAc,KAAK,IAAI;AAAA,QAChC,CAAC;AAAA,MACH;AAEA,YAAM,WAAW,YAAU;AACzB,eAAO,WAAW,WAAW,CAAC,KAAK,UAAU;AAC3C,eAAK,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,YAAY,QAAQ,CAAC,aAAa,MAAM,GAAG;AAClG,mBAAO,MAAM;AAAA,UACf,OAAO;AACL,mBAAO,SAAS,uBAAuB,EAAE,YAAY,YAAY,CAAC;AAAA,UACpE;AAAA,QACF,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,iBAAiB;AAEzD,YAAM,oBAAoB,CAACG,OAAM,QAAQ;AACvC,iBAAS,KAAK,YAAYA,KAAI;AAC9B,QAAAA,MAAK,cAAc,GAAG;AACtB,iBAAS,KAAK,YAAYA,KAAI;AAAA,MAChC;AACA,YAAM,OAAO,SAAO;AAClB,cAAMA,QAAO,SAAS,cAAc,GAAG;AACvC,QAAAA,MAAK,SAAS;AACd,QAAAA,MAAK,OAAO;AACZ,QAAAA,MAAK,MAAM;AACX,cAAM,MAAM,SAAS,YAAY,aAAa;AAC9C,YAAI,eAAe,SAAS,MAAM,MAAM,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,OAAO,OAAO,OAAO,OAAO,GAAG,IAAI;AAClG,0BAAkBA,OAAM,GAAG;AAAA,MAC7B;AAEA,YAAM,UAAU,CAAC,QAAQ,QAAQ,OAAO,IAAI,UAAU,KAAK,SAAS;AACpE,YAAM,kBAAkB,YAAU,QAAQ,QAAQ,OAAO,UAAU,SAAS,CAAC;AAC7E,YAAM,qBAAqB,OAAK;AAC9B,eAAO,EAAE,WAAW,QAAQ,EAAE,aAAa,SAAS,EAAE,YAAY,SAAS,EAAE,YAAY;AAAA,MAC3F;AACA,YAAM,WAAW,CAAC,QAAQ,MAAM;AAC9B,YAAI,GAAG;AACL,gBAAM,OAAO,QAAQ,CAAC;AACtB,cAAI,KAAK,KAAK,IAAI,GAAG;AACnB,kBAAM,WAAW,OAAO,IAAI,OAAO,IAAI;AACvC,gBAAI,SAAS,QAAQ;AACnB,qBAAO,UAAU,eAAe,SAAS,CAAC,GAAG,IAAI;AAAA,YACnD;AAAA,UACF,OAAO;AACL,iBAAK,EAAE,IAAI;AAAA,UACb;AAAA,QACF;AAAA,MACF;AACA,YAAM,aAAa,YAAU,MAAM;AACjC,eAAO,YAAY,WAAW,OAAO,EAAE,QAAQ,KAAK,CAAC;AAAA,MACvD;AACA,YAAM,mBAAmB,YAAU,MAAM;AACvC,iBAAS,QAAQ,gBAAgB,MAAM,CAAC;AAAA,MAC1C;AACA,YAAM,iBAAiB,YAAU;AAC/B,eAAO,GAAG,SAAS,OAAK;AACtB,gBAAMA,QAAO,QAAQ,QAAQ,EAAE,MAAM;AACrC,cAAIA,SAAQ,OAAO,eAAe,CAAC,GAAG;AACpC,cAAE,eAAe;AACjB,qBAAS,QAAQA,KAAI;AAAA,UACvB;AAAA,QACF,CAAC;AACD,eAAO,GAAG,WAAW,OAAK;AACxB,cAAI,CAAC,EAAE,mBAAmB,KAAK,EAAE,YAAY,MAAM,mBAAmB,CAAC,GAAG;AACxE,kBAAMA,QAAO,gBAAgB,MAAM;AACnC,gBAAIA,OAAM;AACR,gBAAE,eAAe;AACjB,uBAAS,QAAQA,KAAI;AAAA,YACvB;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,cAAc,CAAC,QAAQ,YAAY;AACvC,eAAO,GAAG,cAAc,OAAO;AAC/B,eAAO,MAAM,OAAO,IAAI,cAAc,OAAO;AAAA,MAC/C;AACA,YAAM,kBAAkB,YAAU,SAAO;AACvC,cAAM,cAAc,MAAM;AACxB,cAAI,UAAU,CAAC,OAAO,KAAK,WAAW,KAAK,WAAW,QAAQ,OAAO,UAAU,QAAQ,CAAC,CAAC;AACzF,cAAI,WAAW,OAAO,UAAU,WAAW,CAAC;AAAA,QAC9C;AACA,oBAAY;AACZ,eAAO,YAAY,QAAQ,WAAW;AAAA,MACxC;AACA,YAAM,sBAAsB,YAAU,SAAO;AAC3C,cAAM,cAAc,MAAM;AACxB,cAAI,WAAW,OAAO,UAAU,WAAW,CAAC;AAAA,QAC9C;AACA,oBAAY;AACZ,eAAO,YAAY,QAAQ,WAAW;AAAA,MACxC;AACA,YAAM,+BAA+B,YAAU;AAC7C,cAAM,QAAQ,OAAO,UAAU,YAAY,IAAI,WAAW,OAAO,IAAI,WAAW,OAAO,UAAU,SAAS,CAAC,CAAC,IAAI,oBAAoB,OAAO,UAAU,OAAO,CAAC;AAC7J,eAAO,MAAM,WAAW;AAAA,MAC1B;AACA,YAAM,sBAAsB,YAAU,SAAO;AAC3C,cAAM,cAAc,MAAM,IAAI,WAAW,6BAA6B,MAAM,CAAC;AAC7E,oBAAY;AACZ,eAAO,YAAY,QAAQ,WAAW;AAAA,MACxC;AACA,YAAM,oBAAoB,YAAU,SAAO;AACzC,cAAM,aAAa,CAAAC,aAAW,SAASA,QAAO,KAAK,oBAAoB,OAAO,UAAU,OAAO,CAAC;AAChG,cAAM,UAAU,OAAO,IAAI,WAAW,OAAO,UAAU,SAAS,CAAC;AACjE,cAAM,gBAAgB,CAAAA,aAAW;AAC/B,cAAI,WAAW,WAAWA,QAAO,KAAK,OAAO,UAAU,WAAW,CAAC;AAAA,QACrE;AACA,sBAAc,OAAO;AACrB,eAAO,YAAY,QAAQ,OAAK,cAAc,EAAE,OAAO,CAAC;AAAA,MAC1D;AAEA,YAAM,QAAQ,YAAU;AACtB,eAAO,YAAY,UAAU,IAAI,MAAM;AACrC,iBAAO,YAAY,SAAS;AAAA,QAC9B,CAAC;AAAA,MACH;AAEA,YAAM,eAAe,YAAU;AAC7B,eAAO,GAAG,SAAS,gBAAgB,QAAQ;AAAA,UACzC,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU,WAAW,MAAM;AAAA,UAC3B,SAAS,gBAAgB,MAAM;AAAA,QACjC,CAAC;AACD,eAAO,GAAG,SAAS,UAAU,YAAY;AAAA,UACvC,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU,iBAAiB,MAAM;AAAA,UACjC,SAAS,oBAAoB,MAAM;AAAA,QACrC,CAAC;AACD,eAAO,GAAG,SAAS,UAAU,UAAU;AAAA,UACrC,MAAM;AAAA,UACN,SAAS;AAAA,UACT,UAAU,MAAM,OAAO,MAAM;AAAA,UAC7B,SAAS,kBAAkB,MAAM;AAAA,QACnC,CAAC;AAAA,MACH;AACA,YAAM,iBAAiB,YAAU;AAC/B,eAAO,GAAG,SAAS,YAAY,YAAY;AAAA,UACzC,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU,iBAAiB,MAAM;AAAA,UACjC,SAAS,oBAAoB,MAAM;AAAA,QACrC,CAAC;AACD,eAAO,GAAG,SAAS,YAAY,QAAQ;AAAA,UACrC,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS,oBAAoB,MAAM;AAAA,UACnC,UAAU,WAAW,MAAM;AAAA,QAC7B,CAAC;AACD,eAAO,GAAG,SAAS,YAAY,UAAU;AAAA,UACvC,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU,MAAM,OAAO,MAAM;AAAA,UAC7B,SAAS,kBAAkB,MAAM;AAAA,QACnC,CAAC;AAAA,MACH;AACA,YAAM,mBAAmB,YAAU;AACjC,cAAM,SAAS;AACf,cAAM,SAAS;AACf,eAAO,GAAG,SAAS,eAAe,QAAQ;AAAA,UACxC,QAAQ,aAAW;AACjB,kBAAM,aAAa,OAAO,IAAI,WAAW,OAAO;AAChD,gBAAI,CAAC,YAAY;AACf,qBAAO;AAAA,YACT;AACA,mBAAO,SAAS,OAAO,IAAI,WAAW,SAAS,GAAG,CAAC,IAAI,SAAS;AAAA,UAClE;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,uBAAuB,YAAU;AACrC,cAAM,yBAAyB,CAAAC,YAAU;AACvC,UAAAA,QAAO,UAAU,SAAS,KAAK;AAAA,QACjC;AACA,cAAM,cAAc,eAAa;AAC/B,gBAAM,OAAO,OAAO,UAAU,QAAQ;AACtC,oBAAU,WAAW,WAAW,QAAQ,IAAI,CAAC;AAC7C,iBAAO;AAAA,QACT;AACA,cAAM,cAAc,WAAS;AAC3B,gBAAM,SAAS,iBAAiB,MAAM;AACtC,gBAAM,WAAW,mBAAmB,MAAM;AAC1C,cAAI,OAAO,OAAO,KAAK,UAAU;AAC/B,kBAAM,OAAO,cAAc,OAAO,WAAW,MAAM;AACnD,mBAAO,OAAO,KAAK,WAAW,GAAG,KAAK;AAAA,UACxC,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AACA,eAAO,GAAG,SAAS,eAAe,aAAa;AAAA,UAC7C,QAAQ;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS,gBAAgB,MAAM;AAAA,UACjC;AAAA,UACA,OAAO;AAAA,UACP,WAAW,UAAQ,kBAAkB,MAAM,KAAK,WAAW,QAAQ,IAAI;AAAA,UACvE,WAAW,MAAM;AACf,kBAAM,MAAM,iBAAiB,MAAM;AACnC,mBAAO,IAAI,KAAK,SAAS,EAAE,GAAG,OAAO;AAAA,UACvC;AAAA,UACA,UAAU;AAAA,YACR;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,SAAS;AAAA,cACT,SAAS,eAAa;AACpB,sBAAM,OAAO,OAAO,UAAU,QAAQ;AACtC,0BAAU,UAAU,WAAW,QAAQ,IAAI,CAAC;AAC5C,uBAAO,gBAAgB,MAAM,EAAE,SAAS;AAAA,cAC1C;AAAA,cACA,UAAU,aAAW;AACnB,sBAAM,QAAQ,QAAQ,SAAS;AAC/B,sBAAM,OAAO,YAAY,KAAK;AAC9B,sBAAM,cAAc;AAAA,kBAClB,MAAM;AAAA,kBACN,QAAQ;AAAA,gBACV;AACA,qBAAK,QAAQ,aAAa;AAAA,kBACxB,MAAM;AAAA,kBACN;AAAA,kBACA,OAAO,SAAS,KAAK;AAAA,kBACrB,KAAK,SAAS,KAAK;AAAA,kBACnB,QAAQ,SAAS,KAAK;AAAA,kBACtB,OAAO,SAAS,KAAK;AAAA,gBACvB,CAAC;AACD,uCAAuB,MAAM;AAC7B,wBAAQ,KAAK;AAAA,cACf;AAAA,YACF;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,SAAS;AAAA,cACT,UAAU,aAAW;AACnB,uBAAO,MAAM;AACb,wBAAQ,KAAK;AAAA,cACf;AAAA,YACF;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,MAAM;AAAA,cACN,SAAS;AAAA,cACT,SAAS;AAAA,cACT,UAAU,aAAW;AACnB,iCAAiB,MAAM,EAAE;AACzB,wBAAQ,KAAK;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,MAAM;AACjB,iBAAS,IAAI,QAAQ,YAAU;AAC7B,qBAAW,MAAM;AACjB,uBAAa,MAAM;AACnB,yBAAe,MAAM;AACrB,2BAAiB,MAAM;AACvB,+BAAqB,MAAM;AAC3B,yBAAe,MAAM;AACrB,mBAAS,MAAM;AACf,gBAAM,MAAM;AAAA,QACd,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IAEX,GAAG;AAAA;AAAA;;;ACltCH;", "names": ["list", "rels", "anchorElm", "assumeExternalTargets", "text", "title", "link", "parents", "editor"]}