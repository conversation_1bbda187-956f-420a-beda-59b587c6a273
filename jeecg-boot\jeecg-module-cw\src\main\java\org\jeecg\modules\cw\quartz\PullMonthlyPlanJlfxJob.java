package org.jeecg.modules.cw.quartz;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.jeecg.modules.cw.base.entity.CwNameDict;
import org.jeecg.modules.cw.base.service.ICwNameDictService;
import org.jeecg.modules.cw.jlfx.entity.CwJlfxMonth;
import org.jeecg.modules.cw.jlfx.service.ICwJlfxMonthService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * 拉取“月计划-金属销量(价量分析)”并入库到 cw_jlfx_month
 * 接口字段映射：
 *   - tjkhtl -> 铜(nameKey: t)
 *   - tjkhjl -> 金(nameKey: j)
 *   - tjkhyl -> 银(nameKey: y)
 *   - ljkzhl/ljkzh -> 硫精矿(nameKey: ljk)
 *   - mjk    -> 钼精矿(nameKey: m)
 * 仅更新字典中 type = "xl" 的行，按月初日期归档。
 */
@Log4j2
@Service
public class PullMonthlyPlanJlfxJob implements Job {

    private static final String DICT_GROUP = "jlfx";

    @Resource
    private ICwJlfxMonthService jlfxService;
    @Resource
    private ICwNameDictService nameDictService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        try {
            String monthParam = DateUtil.format(new Date(), "yyyy-MM");
            Object paramStr = context.getJobDetail().getJobDataMap().get("parameter");
            if (paramStr instanceof String) {
                try {
                    JSONObject p = JSONObject.parseObject((String) paramStr);
                    String jhyf = p.getString("jhyf");
                    if (ObjectUtil.isNotEmpty(jhyf)) {
                        monthParam = jhyf;
                    } else {
                        String month = p.getString("month");
                        if (ObjectUtil.isNotEmpty(month)) {
                            monthParam = month;
                        }
                    }
                } catch (Exception ignore) {
                }
            }

            String url = "http://172.18.136.24:8102/GetXXZX/getTechnicalPlannedValue?jhyf=" + monthParam;
            String resp = HttpUtil.get(url);
            if (ObjectUtil.isEmpty(resp)) {
                log.error("[jlfx-plan] 月计划接口无响应: {}", url);
                return;
            }

            JSONObject root = JSONObject.parseObject(resp);
            if (!root.getBooleanValue("Code")) {
                log.error("[jlfx-plan] 接口返回失败: {}", resp);
                return;
            }

            JSONObject data = root.getJSONObject("Data");
            if (ObjectUtil.isEmpty(data)) {
                log.warn("[jlfx-plan] Data为空: {}", resp);
                return;
            }

            String jhyf = data.getString("jhyf");
            Date recordTime = DateUtil.beginOfMonth(ObjectUtil.isNotEmpty(jhyf)
                    ? DateUtil.parse(jhyf, "yyyy-MM")
                    : new Date());

            // 构建接口字段到nameKey的映射
            Map<String, String> apiKeyToNameKey = new LinkedHashMap<>();
            apiKeyToNameKey.put("tjkhtl", "t");   // 铜
            apiKeyToNameKey.put("tjkhjl", "j");   // 金
            apiKeyToNameKey.put("tjkhyl", "y");   // 银
            apiKeyToNameKey.put("ljkzhl", "ljk"); // 硫精矿(折合量)
            apiKeyToNameKey.put("mjk",     "m");   // 钼精矿

            // 读取字典(type=xl)，用于获取行名称和单位
            List<CwNameDict> dictList = nameDictService.queryList(DICT_GROUP);
            Map<String, CwNameDict> nameKeyToDict = new HashMap<>();
            if (dictList != null) {
                for (CwNameDict d : dictList) {
                    if ("xl".equals(d.getType())) {
                        nameKeyToDict.put(d.getNameKey(), d);
                    }
                }
            }

            // Upsert 每个金属的计划销量
            for (Map.Entry<String, String> e : apiKeyToNameKey.entrySet()) {
                String apiKey = e.getKey();
                String nameKey = e.getValue();
                String valStr = data.getString(apiKey);
                if (ObjectUtil.isEmpty(valStr)) {
                    continue;
                }
                BigDecimal plan = parseDecimal(valStr);
                if (plan == null) {
                    continue;
                }

                CwNameDict dict = nameKeyToDict.get(nameKey);
                if (dict == null) {
                    log.warn("[jlfx-plan] 未找到字典映射: nameKey={}, type=xl", nameKey);
                    continue;
                }

                upsertJlfx(dict, plan, recordTime);
            }

            log.info("[jlfx-plan] 月计划(销量)入库完成：{}", DateUtil.format(recordTime, "yyyy-MM"));
        } catch (Exception e) {
            log.error("[jlfx-plan] 拉取月计划(销量)失败", e);
        }
    }

    private void upsertJlfx(CwNameDict dict, BigDecimal plan, Date recordTime) {
        if (plan == null) {
            return;
        }
        CwJlfxMonth entity = jlfxService.lambdaQuery()
                .ge(CwJlfxMonth::getRecordTime, DateUtil.beginOfMonth(recordTime))
                .le(CwJlfxMonth::getRecordTime, DateUtil.endOfMonth(recordTime))
                .eq(CwJlfxMonth::getName, dict.getName())
                .eq(CwJlfxMonth::getType, "xl")
                .one();
        if (entity == null) {
            entity = new CwJlfxMonth();
            entity.setRecordTime(recordTime);
            entity.setName(dict.getName());
            entity.setType("xl");
            entity.setUnit(dict.getUnit());
        }
        entity.setJh(plan);
        if (entity.getId() == null) {
            jlfxService.save(entity);
        } else {
            jlfxService.updateById(entity);
        }
    }

    private static BigDecimal parseDecimal(String s) {
        try {
            if (ObjectUtil.isEmpty(s)) {
                return null;
            }
            return new BigDecimal(s.trim());
        } catch (Exception e) {
            return null;
        }
    }
}


