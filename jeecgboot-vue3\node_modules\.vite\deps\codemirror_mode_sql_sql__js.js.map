{"version": 3, "sources": ["../../.pnpm/codemirror@5.65.18/node_modules/codemirror/mode/sql/sql.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n\"use strict\";\n\nCodeMirror.defineMode(\"sql\", function(config, parserConfig) {\n  var client         = parserConfig.client || {},\n      atoms          = parserConfig.atoms || {\"false\": true, \"true\": true, \"null\": true},\n      builtin        = parserConfig.builtin || set(defaultBuiltin),\n      keywords       = parserConfig.keywords || set(sqlKeywords),\n      operatorChars  = parserConfig.operatorChars || /^[*+\\-%<>!=&|~^\\/]/,\n      support        = parserConfig.support || {},\n      hooks          = parserConfig.hooks || {},\n      dateSQL        = parserConfig.dateSQL || {\"date\" : true, \"time\" : true, \"timestamp\" : true},\n      backslashStringEscapes = parserConfig.backslashStringEscapes !== false,\n      brackets       = parserConfig.brackets || /^[\\{}\\(\\)\\[\\]]/,\n      punctuation    = parserConfig.punctuation || /^[;.,:]/\n\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n\n    // call hooks from the mime type\n    if (hooks[ch]) {\n      var result = hooks[ch](stream, state);\n      if (result !== false) return result;\n    }\n\n    if (support.hexNumber &&\n      ((ch == \"0\" && stream.match(/^[xX][0-9a-fA-F]+/))\n      || (ch == \"x\" || ch == \"X\") && stream.match(/^'[0-9a-fA-F]*'/))) {\n      // hex\n      // ref: https://dev.mysql.com/doc/refman/8.0/en/hexadecimal-literals.html\n      return \"number\";\n    } else if (support.binaryNumber &&\n      (((ch == \"b\" || ch == \"B\") && stream.match(/^'[01]*'/))\n      || (ch == \"0\" && stream.match(/^b[01]+/)))) {\n      // bitstring\n      // ref: https://dev.mysql.com/doc/refman/8.0/en/bit-value-literals.html\n      return \"number\";\n    } else if (ch.charCodeAt(0) > 47 && ch.charCodeAt(0) < 58) {\n      // numbers\n      // ref: https://dev.mysql.com/doc/refman/8.0/en/number-literals.html\n      stream.match(/^[0-9]*(\\.[0-9]+)?([eE][-+]?[0-9]+)?/);\n      support.decimallessFloat && stream.match(/^\\.(?!\\.)/);\n      return \"number\";\n    } else if (ch == \"?\" && (stream.eatSpace() || stream.eol() || stream.eat(\";\"))) {\n      // placeholders\n      return \"variable-3\";\n    } else if (ch == \"'\" || (ch == '\"' && support.doubleQuote)) {\n      // strings\n      // ref: https://dev.mysql.com/doc/refman/8.0/en/string-literals.html\n      state.tokenize = tokenLiteral(ch);\n      return state.tokenize(stream, state);\n    } else if ((((support.nCharCast && (ch == \"n\" || ch == \"N\"))\n        || (support.charsetCast && ch == \"_\" && stream.match(/[a-z][a-z0-9]*/i)))\n        && (stream.peek() == \"'\" || stream.peek() == '\"'))) {\n      // charset casting: _utf8'str', N'str', n'str'\n      // ref: https://dev.mysql.com/doc/refman/8.0/en/string-literals.html\n      return \"keyword\";\n    } else if (support.escapeConstant && (ch == \"e\" || ch == \"E\")\n        && (stream.peek() == \"'\" || (stream.peek() == '\"' && support.doubleQuote))) {\n      // escape constant: E'str', e'str'\n      // ref: https://www.postgresql.org/docs/current/sql-syntax-lexical.html#SQL-SYNTAX-STRINGS-ESCAPE\n      state.tokenize = function(stream, state) {\n        return (state.tokenize = tokenLiteral(stream.next(), true))(stream, state);\n      }\n      return \"keyword\";\n    } else if (support.commentSlashSlash && ch == \"/\" && stream.eat(\"/\")) {\n      // 1-line comment\n      stream.skipToEnd();\n      return \"comment\";\n    } else if ((support.commentHash && ch == \"#\")\n        || (ch == \"-\" && stream.eat(\"-\") && (!support.commentSpaceRequired || stream.eat(\" \")))) {\n      // 1-line comments\n      // ref: https://kb.askmonty.org/en/comment-syntax/\n      stream.skipToEnd();\n      return \"comment\";\n    } else if (ch == \"/\" && stream.eat(\"*\")) {\n      // multi-line comments\n      // ref: https://kb.askmonty.org/en/comment-syntax/\n      state.tokenize = tokenComment(1);\n      return state.tokenize(stream, state);\n    } else if (ch == \".\") {\n      // .1 for 0.1\n      if (support.zerolessFloat && stream.match(/^(?:\\d+(?:e[+-]?\\d+)?)/i))\n        return \"number\";\n      if (stream.match(/^\\.+/))\n        return null\n      if (stream.match(/^[\\w\\d_$#]+/))\n        return \"variable-2\";\n    } else if (operatorChars.test(ch)) {\n      // operators\n      stream.eatWhile(operatorChars);\n      return \"operator\";\n    } else if (brackets.test(ch)) {\n      // brackets\n      return \"bracket\";\n    } else if (punctuation.test(ch)) {\n      // punctuation\n      stream.eatWhile(punctuation);\n      return \"punctuation\";\n    } else if (ch == '{' &&\n        (stream.match(/^( )*(d|D|t|T|ts|TS)( )*'[^']*'( )*}/) || stream.match(/^( )*(d|D|t|T|ts|TS)( )*\"[^\"]*\"( )*}/))) {\n      // dates (weird ODBC syntax)\n      // ref: https://dev.mysql.com/doc/refman/8.0/en/date-and-time-literals.html\n      return \"number\";\n    } else {\n      stream.eatWhile(/^[_\\w\\d]/);\n      var word = stream.current().toLowerCase();\n      // dates (standard SQL syntax)\n      // ref: https://dev.mysql.com/doc/refman/8.0/en/date-and-time-literals.html\n      if (dateSQL.hasOwnProperty(word) && (stream.match(/^( )+'[^']*'/) || stream.match(/^( )+\"[^\"]*\"/)))\n        return \"number\";\n      if (atoms.hasOwnProperty(word)) return \"atom\";\n      if (builtin.hasOwnProperty(word)) return \"type\";\n      if (keywords.hasOwnProperty(word)) return \"keyword\";\n      if (client.hasOwnProperty(word)) return \"builtin\";\n      return null;\n    }\n  }\n\n  // 'string', with char specified in quote escaped by '\\'\n  function tokenLiteral(quote, backslashEscapes) {\n    return function(stream, state) {\n      var escaped = false, ch;\n      while ((ch = stream.next()) != null) {\n        if (ch == quote && !escaped) {\n          state.tokenize = tokenBase;\n          break;\n        }\n        escaped = (backslashStringEscapes || backslashEscapes) && !escaped && ch == \"\\\\\";\n      }\n      return \"string\";\n    };\n  }\n  function tokenComment(depth) {\n    return function(stream, state) {\n      var m = stream.match(/^.*?(\\/\\*|\\*\\/)/)\n      if (!m) stream.skipToEnd()\n      else if (m[1] == \"/*\") state.tokenize = tokenComment(depth + 1)\n      else if (depth > 1) state.tokenize = tokenComment(depth - 1)\n      else state.tokenize = tokenBase\n      return \"comment\"\n    }\n  }\n\n  function pushContext(stream, state, type) {\n    state.context = {\n      prev: state.context,\n      indent: stream.indentation(),\n      col: stream.column(),\n      type: type\n    };\n  }\n\n  function popContext(state) {\n    state.indent = state.context.indent;\n    state.context = state.context.prev;\n  }\n\n  return {\n    startState: function() {\n      return {tokenize: tokenBase, context: null};\n    },\n\n    token: function(stream, state) {\n      if (stream.sol()) {\n        if (state.context && state.context.align == null)\n          state.context.align = false;\n      }\n      if (state.tokenize == tokenBase && stream.eatSpace()) return null;\n\n      var style = state.tokenize(stream, state);\n      if (style == \"comment\") return style;\n\n      if (state.context && state.context.align == null)\n        state.context.align = true;\n\n      var tok = stream.current();\n      if (tok == \"(\")\n        pushContext(stream, state, \")\");\n      else if (tok == \"[\")\n        pushContext(stream, state, \"]\");\n      else if (state.context && state.context.type == tok)\n        popContext(state);\n      return style;\n    },\n\n    indent: function(state, textAfter) {\n      var cx = state.context;\n      if (!cx) return CodeMirror.Pass;\n      var closing = textAfter.charAt(0) == cx.type;\n      if (cx.align) return cx.col + (closing ? 0 : 1);\n      else return cx.indent + (closing ? 0 : config.indentUnit);\n    },\n\n    blockCommentStart: \"/*\",\n    blockCommentEnd: \"*/\",\n    lineComment: support.commentSlashSlash ? \"//\" : support.commentHash ? \"#\" : \"--\",\n    closeBrackets: \"()[]{}''\\\"\\\"``\",\n    config: parserConfig\n  };\n});\n\n  // `identifier`\n  function hookIdentifier(stream) {\n    // MySQL/MariaDB identifiers\n    // ref: https://dev.mysql.com/doc/refman/8.0/en/identifier-qualifiers.html\n    var ch;\n    while ((ch = stream.next()) != null) {\n      if (ch == \"`\" && !stream.eat(\"`\")) return \"variable-2\";\n    }\n    stream.backUp(stream.current().length - 1);\n    return stream.eatWhile(/\\w/) ? \"variable-2\" : null;\n  }\n\n  // \"identifier\"\n  function hookIdentifierDoublequote(stream) {\n    // Standard SQL /SQLite identifiers\n    // ref: http://web.archive.org/web/20160813185132/http://savage.net.au/SQL/sql-99.bnf.html#delimited%20identifier\n    // ref: http://sqlite.org/lang_keywords.html\n    var ch;\n    while ((ch = stream.next()) != null) {\n      if (ch == \"\\\"\" && !stream.eat(\"\\\"\")) return \"variable-2\";\n    }\n    stream.backUp(stream.current().length - 1);\n    return stream.eatWhile(/\\w/) ? \"variable-2\" : null;\n  }\n\n  // variable token\n  function hookVar(stream) {\n    // variables\n    // @@prefix.varName @varName\n    // varName can be quoted with ` or ' or \"\n    // ref: https://dev.mysql.com/doc/refman/8.0/en/user-variables.html\n    if (stream.eat(\"@\")) {\n      stream.match('session.');\n      stream.match('local.');\n      stream.match('global.');\n    }\n\n    if (stream.eat(\"'\")) {\n      stream.match(/^.*'/);\n      return \"variable-2\";\n    } else if (stream.eat('\"')) {\n      stream.match(/^.*\"/);\n      return \"variable-2\";\n    } else if (stream.eat(\"`\")) {\n      stream.match(/^.*`/);\n      return \"variable-2\";\n    } else if (stream.match(/^[0-9a-zA-Z$\\.\\_]+/)) {\n      return \"variable-2\";\n    }\n    return null;\n  };\n\n  // short client keyword token\n  function hookClient(stream) {\n    // \\N means NULL\n    // ref: https://dev.mysql.com/doc/refman/8.0/en/null-values.html\n    if (stream.eat(\"N\")) {\n        return \"atom\";\n    }\n    // \\g, etc\n    // ref: https://dev.mysql.com/doc/refman/8.0/en/mysql-commands.html\n    return stream.match(/^[a-zA-Z.#!?]/) ? \"variable-2\" : null;\n  }\n\n  // these keywords are used by all SQL dialects (however, a mode can still overwrite it)\n  var sqlKeywords = \"alter and as asc between by count create delete desc distinct drop from group having in insert into is join like not on or order select set table union update values where limit \";\n\n  // turn a space-separated list into an array\n  function set(str) {\n    var obj = {}, words = str.split(\" \");\n    for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n    return obj;\n  }\n\n  var defaultBuiltin = \"bool boolean bit blob enum long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text bigint int int1 int2 int3 int4 int8 integer float float4 float8 double char varbinary varchar varcharacter precision real date datetime year unsigned signed decimal numeric\"\n\n  // A generic SQL Mode. It's not a standard, it just tries to support what is generally supported\n  CodeMirror.defineMIME(\"text/x-sql\", {\n    name: \"sql\",\n    keywords: set(sqlKeywords + \"begin\"),\n    builtin: set(defaultBuiltin),\n    atoms: set(\"false true null unknown\"),\n    dateSQL: set(\"date time timestamp\"),\n    support: set(\"doubleQuote binaryNumber hexNumber\")\n  });\n\n  CodeMirror.defineMIME(\"text/x-mssql\", {\n    name: \"sql\",\n    client: set(\"$partition binary_checksum checksum connectionproperty context_info current_request_id error_line error_message error_number error_procedure error_severity error_state formatmessage get_filestream_transaction_context getansinull host_id host_name isnull isnumeric min_active_rowversion newid newsequentialid rowcount_big xact_state object_id\"),\n    keywords: set(sqlKeywords + \"begin trigger proc view index for add constraint key primary foreign collate clustered nonclustered declare exec go if use index holdlock nolock nowait paglock readcommitted readcommittedlock readpast readuncommitted repeatableread rowlock serializable snapshot tablock tablockx updlock with\"),\n    builtin: set(\"bigint numeric bit smallint decimal smallmoney int tinyint money float real char varchar text nchar nvarchar ntext binary varbinary image cursor timestamp hierarchyid uniqueidentifier sql_variant xml table \"),\n    atoms: set(\"is not null like and or in left right between inner outer join all any some cross unpivot pivot exists\"),\n    operatorChars: /^[*+\\-%<>!=^\\&|\\/]/,\n    brackets: /^[\\{}\\(\\)]/,\n    punctuation: /^[;.,:/]/,\n    backslashStringEscapes: false,\n    dateSQL: set(\"date datetimeoffset datetime2 smalldatetime datetime time\"),\n    hooks: {\n      \"@\":   hookVar\n    }\n  });\n\n  CodeMirror.defineMIME(\"text/x-mysql\", {\n    name: \"sql\",\n    client: set(\"charset clear connect edit ego exit go help nopager notee nowarning pager print prompt quit rehash source status system tee\"),\n    keywords: set(sqlKeywords + \"accessible action add after algorithm all analyze asensitive at authors auto_increment autocommit avg avg_row_length before binary binlog both btree cache call cascade cascaded case catalog_name chain change changed character check checkpoint checksum class_origin client_statistics close coalesce code collate collation collations column columns comment commit committed completion concurrent condition connection consistent constraint contains continue contributors convert cross current current_date current_time current_timestamp current_user cursor data database databases day_hour day_microsecond day_minute day_second deallocate dec declare default delay_key_write delayed delimiter des_key_file describe deterministic dev_pop dev_samp deviance diagnostics directory disable discard distinctrow div dual dumpfile each elseif enable enclosed end ends engine engines enum errors escape escaped even event events every execute exists exit explain extended fast fetch field fields first flush for force foreign found_rows full fulltext function general get global grant grants group group_concat handler hash help high_priority hosts hour_microsecond hour_minute hour_second if ignore ignore_server_ids import index index_statistics infile inner innodb inout insensitive insert_method install interval invoker isolation iterate key keys kill language last leading leave left level limit linear lines list load local localtime localtimestamp lock logs low_priority master master_heartbeat_period master_ssl_verify_server_cert masters match max max_rows maxvalue message_text middleint migrate min min_rows minute_microsecond minute_second mod mode modifies modify mutex mysql_errno natural next no no_write_to_binlog offline offset one online open optimize option optionally out outer outfile pack_keys parser partition partitions password phase plugin plugins prepare preserve prev primary privileges procedure processlist profile profiles purge query quick range read read_write reads real rebuild recover references regexp relaylog release remove rename reorganize repair repeatable replace require resignal restrict resume return returns revoke right rlike rollback rollup row row_format rtree savepoint schedule schema schema_name schemas second_microsecond security sensitive separator serializable server session share show signal slave slow smallint snapshot soname spatial specific sql sql_big_result sql_buffer_result sql_cache sql_calc_found_rows sql_no_cache sql_small_result sqlexception sqlstate sqlwarning ssl start starting starts status std stddev stddev_pop stddev_samp storage straight_join subclass_origin sum suspend table_name table_statistics tables tablespace temporary terminated to trailing transaction trigger triggers truncate uncommitted undo uninstall unique unlock upgrade usage use use_frm user user_resources user_statistics using utc_date utc_time utc_timestamp value variables varying view views warnings when while with work write xa xor year_month zerofill begin do then else loop repeat\"),\n    builtin: set(\"bool boolean bit blob decimal double float long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text bigint int int1 int2 int3 int4 int8 integer float float4 float8 double char varbinary varchar varcharacter precision date datetime year unsigned signed numeric\"),\n    atoms: set(\"false true null unknown\"),\n    operatorChars: /^[*+\\-%<>!=&|^]/,\n    dateSQL: set(\"date time timestamp\"),\n    support: set(\"decimallessFloat zerolessFloat binaryNumber hexNumber doubleQuote nCharCast charsetCast commentHash commentSpaceRequired\"),\n    hooks: {\n      \"@\":   hookVar,\n      \"`\":   hookIdentifier,\n      \"\\\\\":  hookClient\n    }\n  });\n\n  CodeMirror.defineMIME(\"text/x-mariadb\", {\n    name: \"sql\",\n    client: set(\"charset clear connect edit ego exit go help nopager notee nowarning pager print prompt quit rehash source status system tee\"),\n    keywords: set(sqlKeywords + \"accessible action add after algorithm all always analyze asensitive at authors auto_increment autocommit avg avg_row_length before binary binlog both btree cache call cascade cascaded case catalog_name chain change changed character check checkpoint checksum class_origin client_statistics close coalesce code collate collation collations column columns comment commit committed completion concurrent condition connection consistent constraint contains continue contributors convert cross current current_date current_time current_timestamp current_user cursor data database databases day_hour day_microsecond day_minute day_second deallocate dec declare default delay_key_write delayed delimiter des_key_file describe deterministic dev_pop dev_samp deviance diagnostics directory disable discard distinctrow div dual dumpfile each elseif enable enclosed end ends engine engines enum errors escape escaped even event events every execute exists exit explain extended fast fetch field fields first flush for force foreign found_rows full fulltext function general generated get global grant grants group group_concat handler hard hash help high_priority hosts hour_microsecond hour_minute hour_second if ignore ignore_server_ids import index index_statistics infile inner innodb inout insensitive insert_method install interval invoker isolation iterate key keys kill language last leading leave left level limit linear lines list load local localtime localtimestamp lock logs low_priority master master_heartbeat_period master_ssl_verify_server_cert masters match max max_rows maxvalue message_text middleint migrate min min_rows minute_microsecond minute_second mod mode modifies modify mutex mysql_errno natural next no no_write_to_binlog offline offset one online open optimize option optionally out outer outfile pack_keys parser partition partitions password persistent phase plugin plugins prepare preserve prev primary privileges procedure processlist profile profiles purge query quick range read read_write reads real rebuild recover references regexp relaylog release remove rename reorganize repair repeatable replace require resignal restrict resume return returns revoke right rlike rollback rollup row row_format rtree savepoint schedule schema schema_name schemas second_microsecond security sensitive separator serializable server session share show shutdown signal slave slow smallint snapshot soft soname spatial specific sql sql_big_result sql_buffer_result sql_cache sql_calc_found_rows sql_no_cache sql_small_result sqlexception sqlstate sqlwarning ssl start starting starts status std stddev stddev_pop stddev_samp storage straight_join subclass_origin sum suspend table_name table_statistics tables tablespace temporary terminated to trailing transaction trigger triggers truncate uncommitted undo uninstall unique unlock upgrade usage use use_frm user user_resources user_statistics using utc_date utc_time utc_timestamp value variables varying view views virtual warnings when while with work write xa xor year_month zerofill begin do then else loop repeat\"),\n    builtin: set(\"bool boolean bit blob decimal double float long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text bigint int int1 int2 int3 int4 int8 integer float float4 float8 double char varbinary varchar varcharacter precision date datetime year unsigned signed numeric\"),\n    atoms: set(\"false true null unknown\"),\n    operatorChars: /^[*+\\-%<>!=&|^]/,\n    dateSQL: set(\"date time timestamp\"),\n    support: set(\"decimallessFloat zerolessFloat binaryNumber hexNumber doubleQuote nCharCast charsetCast commentHash commentSpaceRequired\"),\n    hooks: {\n      \"@\":   hookVar,\n      \"`\":   hookIdentifier,\n      \"\\\\\":  hookClient\n    }\n  });\n\n  // provided by the phpLiteAdmin project - phpliteadmin.org\n  CodeMirror.defineMIME(\"text/x-sqlite\", {\n    name: \"sql\",\n    // commands of the official SQLite client, ref: https://www.sqlite.org/cli.html#dotcmd\n    client: set(\"auth backup bail binary changes check clone databases dbinfo dump echo eqp exit explain fullschema headers help import imposter indexes iotrace limit lint load log mode nullvalue once open output print prompt quit read restore save scanstats schema separator session shell show stats system tables testcase timeout timer trace vfsinfo vfslist vfsname width\"),\n    // ref: http://sqlite.org/lang_keywords.html\n    keywords: set(sqlKeywords + \"abort action add after all analyze attach autoincrement before begin cascade case cast check collate column commit conflict constraint cross current_date current_time current_timestamp database default deferrable deferred detach each else end escape except exclusive exists explain fail for foreign full glob if ignore immediate index indexed initially inner instead intersect isnull key left limit match natural no notnull null of offset outer plan pragma primary query raise recursive references regexp reindex release rename replace restrict right rollback row savepoint temp temporary then to transaction trigger unique using vacuum view virtual when with without\"),\n    // SQLite is weakly typed, ref: http://sqlite.org/datatype3.html. This is just a list of some common types.\n    builtin: set(\"bool boolean bit blob decimal double float long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text clob bigint int int2 int8 integer float double char varchar date datetime year unsigned signed numeric real\"),\n    // ref: http://sqlite.org/syntax/literal-value.html\n    atoms: set(\"null current_date current_time current_timestamp\"),\n    // ref: http://sqlite.org/lang_expr.html#binaryops\n    operatorChars: /^[*+\\-%<>!=&|/~]/,\n    // SQLite is weakly typed, ref: http://sqlite.org/datatype3.html. This is just a list of some common types.\n    dateSQL: set(\"date time timestamp datetime\"),\n    support: set(\"decimallessFloat zerolessFloat\"),\n    identifierQuote: \"\\\"\",  //ref: http://sqlite.org/lang_keywords.html\n    hooks: {\n      // bind-parameters ref:http://sqlite.org/lang_expr.html#varparam\n      \"@\":   hookVar,\n      \":\":   hookVar,\n      \"?\":   hookVar,\n      \"$\":   hookVar,\n      // The preferred way to escape Identifiers is using double quotes, ref: http://sqlite.org/lang_keywords.html\n      \"\\\"\":   hookIdentifierDoublequote,\n      // there is also support for backticks, ref: http://sqlite.org/lang_keywords.html\n      \"`\":   hookIdentifier\n    }\n  });\n\n  // the query language used by Apache Cassandra is called CQL, but this mime type\n  // is called Cassandra to avoid confusion with Contextual Query Language\n  CodeMirror.defineMIME(\"text/x-cassandra\", {\n    name: \"sql\",\n    client: { },\n    keywords: set(\"add all allow alter and any apply as asc authorize batch begin by clustering columnfamily compact consistency count create custom delete desc distinct drop each_quorum exists filtering from grant if in index insert into key keyspace keyspaces level limit local_one local_quorum modify nan norecursive nosuperuser not of on one order password permission permissions primary quorum rename revoke schema select set storage superuser table three to token truncate ttl two type unlogged update use user users using values where with writetime\"),\n    builtin: set(\"ascii bigint blob boolean counter decimal double float frozen inet int list map static text timestamp timeuuid tuple uuid varchar varint\"),\n    atoms: set(\"false true infinity NaN\"),\n    operatorChars: /^[<>=]/,\n    dateSQL: { },\n    support: set(\"commentSlashSlash decimallessFloat\"),\n    hooks: { }\n  });\n\n  // this is based on Peter Raganitsch's 'plsql' mode\n  CodeMirror.defineMIME(\"text/x-plsql\", {\n    name:       \"sql\",\n    client:     set(\"appinfo arraysize autocommit autoprint autorecovery autotrace blockterminator break btitle cmdsep colsep compatibility compute concat copycommit copytypecheck define describe echo editfile embedded escape exec execute feedback flagger flush heading headsep instance linesize lno loboffset logsource long longchunksize markup native newpage numformat numwidth pagesize pause pno recsep recsepchar release repfooter repheader serveroutput shiftinout show showmode size spool sqlblanklines sqlcase sqlcode sqlcontinue sqlnumber sqlpluscompatibility sqlprefix sqlprompt sqlterminator suffix tab term termout time timing trimout trimspool ttitle underline verify version wrap\"),\n    keywords:   set(\"abort accept access add all alter and any array arraylen as asc assert assign at attributes audit authorization avg base_table begin between binary_integer body boolean by case cast char char_base check close cluster clusters colauth column comment commit compress connect connected constant constraint crash create current currval cursor data_base database date dba deallocate debugoff debugon decimal declare default definition delay delete desc digits dispose distinct do drop else elseif elsif enable end entry escape exception exception_init exchange exclusive exists exit external fast fetch file for force form from function generic goto grant group having identified if immediate in increment index indexes indicator initial initrans insert interface intersect into is key level library like limited local lock log logging long loop master maxextents maxtrans member minextents minus mislabel mode modify multiset new next no noaudit nocompress nologging noparallel not nowait number_base object of off offline on online only open option or order out package parallel partition pctfree pctincrease pctused pls_integer positive positiven pragma primary prior private privileges procedure public raise range raw read rebuild record ref references refresh release rename replace resource restrict return returning returns reverse revoke rollback row rowid rowlabel rownum rows run savepoint schema segment select separate session set share snapshot some space split sql start statement storage subtype successful synonym tabauth table tables tablespace task terminate then to trigger truncate type union unique unlimited unrecoverable unusable update use using validate value values variable view views when whenever where while with work\"),\n    builtin:    set(\"abs acos add_months ascii asin atan atan2 average bfile bfilename bigserial bit blob ceil character chartorowid chr clob concat convert cos cosh count dec decode deref dual dump dup_val_on_index empty error exp false float floor found glb greatest hextoraw initcap instr instrb int integer isopen last_day least length lengthb ln lower lpad ltrim lub make_ref max min mlslabel mod months_between natural naturaln nchar nclob new_time next_day nextval nls_charset_decl_len nls_charset_id nls_charset_name nls_initcap nls_lower nls_sort nls_upper nlssort no_data_found notfound null number numeric nvarchar2 nvl others power rawtohex real reftohex round rowcount rowidtochar rowtype rpad rtrim serial sign signtype sin sinh smallint soundex sqlcode sqlerrm sqrt stddev string substr substrb sum sysdate tan tanh to_char text to_date to_label to_multi_byte to_number to_single_byte translate true trunc uid unlogged upper user userenv varchar varchar2 variance varying vsize xml\"),\n    operatorChars: /^[*\\/+\\-%<>!=~]/,\n    dateSQL:    set(\"date time timestamp\"),\n    support:    set(\"doubleQuote nCharCast zerolessFloat binaryNumber hexNumber\")\n  });\n\n  // Created to support specific hive keywords\n  CodeMirror.defineMIME(\"text/x-hive\", {\n    name: \"sql\",\n    keywords: set(\"select alter $elem$ $key$ $value$ add after all analyze and archive as asc before between binary both bucket buckets by cascade case cast change cluster clustered clusterstatus collection column columns comment compute concatenate continue create cross cursor data database databases dbproperties deferred delete delimited desc describe directory disable distinct distribute drop else enable end escaped exclusive exists explain export extended external fetch fields fileformat first format formatted from full function functions grant group having hold_ddltime idxproperties if import in index indexes inpath inputdriver inputformat insert intersect into is items join keys lateral left like limit lines load local location lock locks mapjoin materialized minus msck no_drop nocompress not of offline on option or order out outer outputdriver outputformat overwrite partition partitioned partitions percent plus preserve procedure purge range rcfile read readonly reads rebuild recordreader recordwriter recover reduce regexp rename repair replace restrict revoke right rlike row schema schemas semi sequencefile serde serdeproperties set shared show show_database sort sorted ssl statistics stored streamtable table tables tablesample tblproperties temporary terminated textfile then tmp to touch transform trigger unarchive undo union uniquejoin unlock update use using utc utc_tmestamp view when where while with admin authorization char compact compactions conf cube current current_date current_timestamp day decimal defined dependency directories elem_type exchange file following for grouping hour ignore inner interval jar less logical macro minute month more none noscan over owner partialscan preceding pretty principals protection reload rewrite role roles rollup rows second server sets skewed transactions truncate unbounded unset uri user values window year\"),\n    builtin: set(\"bool boolean long timestamp tinyint smallint bigint int float double date datetime unsigned string array struct map uniontype key_type utctimestamp value_type varchar\"),\n    atoms: set(\"false true null unknown\"),\n    operatorChars: /^[*+\\-%<>!=]/,\n    dateSQL: set(\"date timestamp\"),\n    support: set(\"doubleQuote binaryNumber hexNumber\")\n  });\n\n  CodeMirror.defineMIME(\"text/x-pgsql\", {\n    name: \"sql\",\n    client: set(\"source\"),\n    // For PostgreSQL - https://www.postgresql.org/docs/11/sql-keywords-appendix.html\n    // For pl/pgsql lang - https://github.com/postgres/postgres/blob/REL_11_2/src/pl/plpgsql/src/pl_scanner.c\n    keywords: set(sqlKeywords + \"a abort abs absent absolute access according action ada add admin after aggregate alias all allocate also alter always analyse analyze and any are array array_agg array_max_cardinality as asc asensitive assert assertion assignment asymmetric at atomic attach attribute attributes authorization avg backward base64 before begin begin_frame begin_partition bernoulli between bigint binary bit bit_length blob blocked bom boolean both breadth by c cache call called cardinality cascade cascaded case cast catalog catalog_name ceil ceiling chain char char_length character character_length character_set_catalog character_set_name character_set_schema characteristics characters check checkpoint class class_origin clob close cluster coalesce cobol collate collation collation_catalog collation_name collation_schema collect column column_name columns command_function command_function_code comment comments commit committed concurrently condition condition_number configuration conflict connect connection connection_name constant constraint constraint_catalog constraint_name constraint_schema constraints constructor contains content continue control conversion convert copy corr corresponding cost count covar_pop covar_samp create cross csv cube cume_dist current current_catalog current_date current_default_transform_group current_path current_role current_row current_schema current_time current_timestamp current_transform_group_for_type current_user cursor cursor_name cycle data database datalink datatype date datetime_interval_code datetime_interval_precision day db deallocate debug dec decimal declare default defaults deferrable deferred defined definer degree delete delimiter delimiters dense_rank depends depth deref derived desc describe descriptor detach detail deterministic diagnostics dictionary disable discard disconnect dispatch distinct dlnewcopy dlpreviouscopy dlurlcomplete dlurlcompleteonly dlurlcompletewrite dlurlpath dlurlpathonly dlurlpathwrite dlurlscheme dlurlserver dlvalue do document domain double drop dump dynamic dynamic_function dynamic_function_code each element else elseif elsif empty enable encoding encrypted end end_frame end_partition endexec enforced enum equals errcode error escape event every except exception exclude excluding exclusive exec execute exists exit exp explain expression extension external extract false family fetch file filter final first first_value flag float floor following for force foreach foreign fortran forward found frame_row free freeze from fs full function functions fusion g general generated get global go goto grant granted greatest group grouping groups handler having header hex hierarchy hint hold hour id identity if ignore ilike immediate immediately immutable implementation implicit import in include including increment indent index indexes indicator info inherit inherits initially inline inner inout input insensitive insert instance instantiable instead int integer integrity intersect intersection interval into invoker is isnull isolation join k key key_member key_type label lag language large last last_value lateral lead leading leakproof least left length level library like like_regex limit link listen ln load local localtime localtimestamp location locator lock locked log logged loop lower m map mapping match matched materialized max max_cardinality maxvalue member merge message message_length message_octet_length message_text method min minute minvalue mod mode modifies module month more move multiset mumps name names namespace national natural nchar nclob nesting new next nfc nfd nfkc nfkd nil no none normalize normalized not nothing notice notify notnull nowait nth_value ntile null nullable nullif nulls number numeric object occurrences_regex octet_length octets of off offset oids old on only open operator option options or order ordering ordinality others out outer output over overlaps overlay overriding owned owner p pad parallel parameter parameter_mode parameter_name parameter_ordinal_position parameter_specific_catalog parameter_specific_name parameter_specific_schema parser partial partition pascal passing passthrough password path percent percent_rank percentile_cont percentile_disc perform period permission pg_context pg_datatype_name pg_exception_context pg_exception_detail pg_exception_hint placing plans pli policy portion position position_regex power precedes preceding precision prepare prepared preserve primary print_strict_params prior privileges procedural procedure procedures program public publication query quote raise range rank read reads real reassign recheck recovery recursive ref references referencing refresh regr_avgx regr_avgy regr_count regr_intercept regr_r2 regr_slope regr_sxx regr_sxy regr_syy reindex relative release rename repeatable replace replica requiring reset respect restart restore restrict result result_oid return returned_cardinality returned_length returned_octet_length returned_sqlstate returning returns reverse revoke right role rollback rollup routine routine_catalog routine_name routine_schema routines row row_count row_number rows rowtype rule savepoint scale schema schema_name schemas scope scope_catalog scope_name scope_schema scroll search second section security select selective self sensitive sequence sequences serializable server server_name session session_user set setof sets share show similar simple size skip slice smallint snapshot some source space specific specific_name specifictype sql sqlcode sqlerror sqlexception sqlstate sqlwarning sqrt stable stacked standalone start state statement static statistics stddev_pop stddev_samp stdin stdout storage strict strip structure style subclass_origin submultiset subscription substring substring_regex succeeds sum symmetric sysid system system_time system_user t table table_name tables tablesample tablespace temp template temporary text then ties time timestamp timezone_hour timezone_minute to token top_level_count trailing transaction transaction_active transactions_committed transactions_rolled_back transform transforms translate translate_regex translation treat trigger trigger_catalog trigger_name trigger_schema trim trim_array true truncate trusted type types uescape unbounded uncommitted under unencrypted union unique unknown unlink unlisten unlogged unnamed unnest until untyped update upper uri usage use_column use_variable user user_defined_type_catalog user_defined_type_code user_defined_type_name user_defined_type_schema using vacuum valid validate validator value value_of values var_pop var_samp varbinary varchar variable_conflict variadic varying verbose version versioning view views volatile warning when whenever where while whitespace width_bucket window with within without work wrapper write xml xmlagg xmlattributes xmlbinary xmlcast xmlcomment xmlconcat xmldeclaration xmldocument xmlelement xmlexists xmlforest xmliterate xmlnamespaces xmlparse xmlpi xmlquery xmlroot xmlschema xmlserialize xmltable xmltext xmlvalidate year yes zone\"),\n    // https://www.postgresql.org/docs/11/datatype.html\n    builtin: set(\"bigint int8 bigserial serial8 bit varying varbit boolean bool box bytea character char varchar cidr circle date double precision float8 inet integer int int4 interval json jsonb line lseg macaddr macaddr8 money numeric decimal path pg_lsn point polygon real float4 smallint int2 smallserial serial2 serial serial4 text time zone timetz timestamp timestamptz tsquery tsvector txid_snapshot uuid xml\"),\n    atoms: set(\"false true null unknown\"),\n    operatorChars: /^[*\\/+\\-%<>!=&|^\\/#@?~]/,\n    backslashStringEscapes: false,\n    dateSQL: set(\"date time timestamp\"),\n    support: set(\"decimallessFloat zerolessFloat binaryNumber hexNumber nCharCast charsetCast escapeConstant\")\n  });\n\n  // Google's SQL-like query language, GQL\n  CodeMirror.defineMIME(\"text/x-gql\", {\n    name: \"sql\",\n    keywords: set(\"ancestor and asc by contains desc descendant distinct from group has in is limit offset on order select superset where\"),\n    atoms: set(\"false true\"),\n    builtin: set(\"blob datetime first key __key__ string integer double boolean null\"),\n    operatorChars: /^[*+\\-%<>!=]/\n  });\n\n  // Greenplum\n  CodeMirror.defineMIME(\"text/x-gpsql\", {\n    name: \"sql\",\n    client: set(\"source\"),\n    //https://github.com/greenplum-db/gpdb/blob/master/src/include/parser/kwlist.h\n    keywords: set(\"abort absolute access action active add admin after aggregate all also alter always analyse analyze and any array as asc assertion assignment asymmetric at authorization backward before begin between bigint binary bit boolean both by cache called cascade cascaded case cast chain char character characteristics check checkpoint class close cluster coalesce codegen collate column comment commit committed concurrency concurrently configuration connection constraint constraints contains content continue conversion copy cost cpu_rate_limit create createdb createexttable createrole createuser cross csv cube current current_catalog current_date current_role current_schema current_time current_timestamp current_user cursor cycle data database day deallocate dec decimal declare decode default defaults deferrable deferred definer delete delimiter delimiters deny desc dictionary disable discard distinct distributed do document domain double drop dxl each else enable encoding encrypted end enum errors escape every except exchange exclude excluding exclusive execute exists explain extension external extract false family fetch fields filespace fill filter first float following for force foreign format forward freeze from full function global grant granted greatest group group_id grouping handler hash having header hold host hour identity if ignore ilike immediate immutable implicit in including inclusive increment index indexes inherit inherits initially inline inner inout input insensitive insert instead int integer intersect interval into invoker is isnull isolation join key language large last leading least left level like limit list listen load local localtime localtimestamp location lock log login mapping master match maxvalue median merge minute minvalue missing mode modifies modify month move name names national natural nchar new newline next no nocreatedb nocreateexttable nocreaterole nocreateuser noinherit nologin none noovercommit nosuperuser not nothing notify notnull nowait null nullif nulls numeric object of off offset oids old on only operator option options or order ordered others out outer over overcommit overlaps overlay owned owner parser partial partition partitions passing password percent percentile_cont percentile_disc placing plans position preceding precision prepare prepared preserve primary prior privileges procedural procedure protocol queue quote randomly range read readable reads real reassign recheck recursive ref references reindex reject relative release rename repeatable replace replica reset resource restart restrict returning returns revoke right role rollback rollup rootpartition row rows rule savepoint scatter schema scroll search second security segment select sequence serializable session session_user set setof sets share show similar simple smallint some split sql stable standalone start statement statistics stdin stdout storage strict strip subpartition subpartitions substring superuser symmetric sysid system table tablespace temp template temporary text then threshold ties time timestamp to trailing transaction treat trigger trim true truncate trusted type unbounded uncommitted unencrypted union unique unknown unlisten until update user using vacuum valid validation validator value values varchar variadic varying verbose version view volatile web when where whitespace window with within without work writable write xml xmlattributes xmlconcat xmlelement xmlexists xmlforest xmlparse xmlpi xmlroot xmlserialize year yes zone\"),\n    builtin: set(\"bigint int8 bigserial serial8 bit varying varbit boolean bool box bytea character char varchar cidr circle date double precision float float8 inet integer int int4 interval json jsonb line lseg macaddr macaddr8 money numeric decimal path pg_lsn point polygon real float4 smallint int2 smallserial serial2 serial serial4 text time without zone with timetz timestamp timestamptz tsquery tsvector txid_snapshot uuid xml\"),\n    atoms: set(\"false true null unknown\"),\n    operatorChars: /^[*+\\-%<>!=&|^\\/#@?~]/,\n    dateSQL: set(\"date time timestamp\"),\n    support: set(\"decimallessFloat zerolessFloat binaryNumber hexNumber nCharCast charsetCast\")\n  });\n\n  // Spark SQL\n  CodeMirror.defineMIME(\"text/x-sparksql\", {\n    name: \"sql\",\n    keywords: set(\"add after all alter analyze and anti archive array as asc at between bucket buckets by cache cascade case cast change clear cluster clustered codegen collection column columns comment commit compact compactions compute concatenate cost create cross cube current current_date current_timestamp database databases data dbproperties defined delete delimited deny desc describe dfs directories distinct distribute drop else end escaped except exchange exists explain export extended external false fields fileformat first following for format formatted from full function functions global grant group grouping having if ignore import in index indexes inner inpath inputformat insert intersect interval into is items join keys last lateral lazy left like limit lines list load local location lock locks logical macro map minus msck natural no not null nulls of on optimize option options or order out outer outputformat over overwrite partition partitioned partitions percent preceding principals purge range recordreader recordwriter recover reduce refresh regexp rename repair replace reset restrict revoke right rlike role roles rollback rollup row rows schema schemas select semi separated serde serdeproperties set sets show skewed sort sorted start statistics stored stratify struct table tables tablesample tblproperties temp temporary terminated then to touch transaction transactions transform true truncate unarchive unbounded uncache union unlock unset use using values view when where window with\"),\n    builtin: set(\"abs acos acosh add_months aggregate and any approx_count_distinct approx_percentile array array_contains array_distinct array_except array_intersect array_join array_max array_min array_position array_remove array_repeat array_sort array_union arrays_overlap arrays_zip ascii asin asinh assert_true atan atan2 atanh avg base64 between bigint bin binary bit_and bit_count bit_get bit_length bit_or bit_xor bool_and bool_or boolean bround btrim cardinality case cast cbrt ceil ceiling char char_length character_length chr coalesce collect_list collect_set concat concat_ws conv corr cos cosh cot count count_if count_min_sketch covar_pop covar_samp crc32 cume_dist current_catalog current_database current_date current_timestamp current_timezone current_user date date_add date_format date_from_unix_date date_part date_sub date_trunc datediff day dayofmonth dayofweek dayofyear decimal decode degrees delimited dense_rank div double e element_at elt encode every exists exp explode explode_outer expm1 extract factorial filter find_in_set first first_value flatten float floor forall format_number format_string from_csv from_json from_unixtime from_utc_timestamp get_json_object getbit greatest grouping grouping_id hash hex hour hypot if ifnull in initcap inline inline_outer input_file_block_length input_file_block_start input_file_name inputformat instr int isnan isnotnull isnull java_method json_array_length json_object_keys json_tuple kurtosis lag last last_day last_value lcase lead least left length levenshtein like ln locate log log10 log1p log2 lower lpad ltrim make_date make_dt_interval make_interval make_timestamp make_ym_interval map map_concat map_entries map_filter map_from_arrays map_from_entries map_keys map_values map_zip_with max max_by md5 mean min min_by minute mod monotonically_increasing_id month months_between named_struct nanvl negative next_day not now nth_value ntile nullif nvl nvl2 octet_length or outputformat overlay parse_url percent_rank percentile percentile_approx pi pmod posexplode posexplode_outer position positive pow power printf quarter radians raise_error rand randn random rank rcfile reflect regexp regexp_extract regexp_extract_all regexp_like regexp_replace repeat replace reverse right rint rlike round row_number rpad rtrim schema_of_csv schema_of_json second sentences sequence sequencefile serde session_window sha sha1 sha2 shiftleft shiftright shiftrightunsigned shuffle sign signum sin sinh size skewness slice smallint some sort_array soundex space spark_partition_id split sqrt stack std stddev stddev_pop stddev_samp str_to_map string struct substr substring substring_index sum tan tanh textfile timestamp timestamp_micros timestamp_millis timestamp_seconds tinyint to_csv to_date to_json to_timestamp to_unix_timestamp to_utc_timestamp transform transform_keys transform_values translate trim trunc try_add try_divide typeof ucase unbase64 unhex uniontype unix_date unix_micros unix_millis unix_seconds unix_timestamp upper uuid var_pop var_samp variance version weekday weekofyear when width_bucket window xpath xpath_boolean xpath_double xpath_float xpath_int xpath_long xpath_number xpath_short xpath_string xxhash64 year zip_with\"),\n    atoms: set(\"false true null\"),\n    operatorChars: /^[*\\/+\\-%<>!=~&|^]/,\n    dateSQL: set(\"date time timestamp\"),\n    support: set(\"doubleQuote zerolessFloat\")\n  });\n\n  // Esper\n  CodeMirror.defineMIME(\"text/x-esper\", {\n    name: \"sql\",\n    client: set(\"source\"),\n    // http://www.espertech.com/esper/release-5.5.0/esper-reference/html/appendix_keywords.html\n    keywords: set(\"alter and as asc between by count create delete desc distinct drop from group having in insert into is join like not on or order select set table union update values where limit after all and as at asc avedev avg between by case cast coalesce count create current_timestamp day days delete define desc distinct else end escape events every exists false first from full group having hour hours in inner insert instanceof into irstream is istream join last lastweekday left limit like max match_recognize matches median measures metadatasql min minute minutes msec millisecond milliseconds not null offset on or order outer output partition pattern prev prior regexp retain-union retain-intersection right rstream sec second seconds select set some snapshot sql stddev sum then true unidirectional until update variable weekday when where window\"),\n    builtin: {},\n    atoms: set(\"false true null\"),\n    operatorChars: /^[*+\\-%<>!=&|^\\/#@?~]/,\n    dateSQL: set(\"time\"),\n    support: set(\"decimallessFloat zerolessFloat binaryNumber hexNumber\")\n  });\n\n  // Trino (formerly known as Presto)\n  CodeMirror.defineMIME(\"text/x-trino\", {\n    name: \"sql\",\n    // https://github.com/trinodb/trino/blob/bc7a4eeedde28684c7ae6f74cefcaf7c6e782174/core/trino-parser/src/main/antlr4/io/trino/sql/parser/SqlBase.g4#L859-L1129\n    // https://github.com/trinodb/trino/blob/bc7a4eeedde28684c7ae6f74cefcaf7c6e782174/docs/src/main/sphinx/functions/list.rst\n    keywords: set(\"abs absent acos add admin after all all_match alter analyze and any any_match approx_distinct approx_most_frequent approx_percentile approx_set arbitrary array_agg array_distinct array_except array_intersect array_join array_max array_min array_position array_remove array_sort array_union arrays_overlap as asc asin at at_timezone atan atan2 authorization avg bar bernoulli beta_cdf between bing_tile bing_tile_at bing_tile_coordinates bing_tile_polygon bing_tile_quadkey bing_tile_zoom_level bing_tiles_around bit_count bitwise_and bitwise_and_agg bitwise_left_shift bitwise_not bitwise_or bitwise_or_agg bitwise_right_shift bitwise_right_shift_arithmetic bitwise_xor bool_and bool_or both by call cardinality cascade case cast catalogs cbrt ceil ceiling char2hexint checksum chr classify coalesce codepoint column columns combinations comment commit committed concat concat_ws conditional constraint contains contains_sequence convex_hull_agg copartition corr cos cosh cosine_similarity count count_if covar_pop covar_samp crc32 create cross cube cume_dist current current_catalog current_date current_groups current_path current_role current_schema current_time current_timestamp current_timezone current_user data date_add date_diff date_format date_parse date_trunc day day_of_month day_of_week day_of_year deallocate default define definer degrees delete dense_rank deny desc describe descriptor distinct distributed dow doy drop e element_at else empty empty_approx_set encoding end error escape evaluate_classifier_predictions every except excluding execute exists exp explain extract false features fetch filter final first first_value flatten floor following for format format_datetime format_number from from_base from_base32 from_base64 from_base64url from_big_endian_32 from_big_endian_64 from_encoded_polyline from_geojson_geometry from_hex from_ieee754_32 from_ieee754_64 from_iso8601_date from_iso8601_timestamp from_iso8601_timestamp_nanos from_unixtime from_unixtime_nanos from_utf8 full functions geometric_mean geometry_from_hadoop_shape geometry_invalid_reason geometry_nearest_points geometry_to_bing_tiles geometry_union geometry_union_agg grant granted grants graphviz great_circle_distance greatest group grouping groups hamming_distance hash_counts having histogram hmac_md5 hmac_sha1 hmac_sha256 hmac_sha512 hour human_readable_seconds if ignore in including index infinity initial inner input insert intersect intersection_cardinality into inverse_beta_cdf inverse_normal_cdf invoker io is is_finite is_infinite is_json_scalar is_nan isolation jaccard_index join json_array json_array_contains json_array_get json_array_length json_exists json_extract json_extract_scalar json_format json_object json_parse json_query json_size json_value keep key keys kurtosis lag last last_day_of_month last_value lateral lead leading learn_classifier learn_libsvm_classifier learn_libsvm_regressor learn_regressor least left length level levenshtein_distance like limit line_interpolate_point line_interpolate_points line_locate_point listagg ln local localtime localtimestamp log log10 log2 logical lower lpad ltrim luhn_check make_set_digest map_agg map_concat map_entries map_filter map_from_entries map_keys map_union map_values map_zip_with match match_recognize matched matches materialized max max_by md5 measures merge merge_set_digest millisecond min min_by minute mod month multimap_agg multimap_from_entries murmur3 nan natural next nfc nfd nfkc nfkd ngrams no none none_match normal_cdf normalize not now nth_value ntile null nullif nulls numeric_histogram object objectid_timestamp of offset omit on one only option or order ordinality outer output over overflow parse_data_size parse_datetime parse_duration partition partitions passing past path pattern per percent_rank permute pi position pow power preceding prepare privileges properties prune qdigest_agg quarter quotes radians rand random range rank read recursive reduce reduce_agg refresh regexp_count regexp_extract regexp_extract_all regexp_like regexp_position regexp_replace regexp_split regr_intercept regr_slope regress rename render repeat repeatable replace reset respect restrict returning reverse revoke rgb right role roles rollback rollup round row_number rows rpad rtrim running scalar schema schemas second security seek select sequence serializable session set sets sha1 sha256 sha512 show shuffle sign simplify_geometry sin skewness skip slice some soundex spatial_partitioning spatial_partitions split split_part split_to_map split_to_multimap spooky_hash_v2_32 spooky_hash_v2_64 sqrt st_area st_asbinary st_astext st_boundary st_buffer st_centroid st_contains st_convexhull st_coorddim st_crosses st_difference st_dimension st_disjoint st_distance st_endpoint st_envelope st_envelopeaspts st_equals st_exteriorring st_geometries st_geometryfromtext st_geometryn st_geometrytype st_geomfrombinary st_interiorringn st_interiorrings st_intersection st_intersects st_isclosed st_isempty st_isring st_issimple st_isvalid st_length st_linefromtext st_linestring st_multipoint st_numgeometries st_numinteriorring st_numpoints st_overlaps st_point st_pointn st_points st_polygon st_relate st_startpoint st_symdifference st_touches st_union st_within st_x st_xmax st_xmin st_y st_ymax st_ymin start starts_with stats stddev stddev_pop stddev_samp string strpos subset substr substring sum system table tables tablesample tan tanh tdigest_agg text then ties timestamp_objectid timezone_hour timezone_minute to to_base to_base32 to_base64 to_base64url to_big_endian_32 to_big_endian_64 to_char to_date to_encoded_polyline to_geojson_geometry to_geometry to_hex to_ieee754_32 to_ieee754_64 to_iso8601 to_milliseconds to_spherical_geography to_timestamp to_unixtime to_utf8 trailing transaction transform transform_keys transform_values translate trim trim_array true truncate try try_cast type typeof uescape unbounded uncommitted unconditional union unique unknown unmatched unnest update upper url_decode url_encode url_extract_fragment url_extract_host url_extract_parameter url_extract_path url_extract_port url_extract_protocol url_extract_query use user using utf16 utf32 utf8 validate value value_at_quantile values values_at_quantiles var_pop var_samp variance verbose version view week week_of_year when where width_bucket wilson_interval_lower wilson_interval_upper window with with_timezone within without word_stem work wrapper write xxhash64 year year_of_week yow zip zip_with\"),\n    // https://github.com/trinodb/trino/blob/bc7a4eeedde28684c7ae6f74cefcaf7c6e782174/core/trino-main/src/main/java/io/trino/metadata/TypeRegistry.java#L131-L168\n    // https://github.com/trinodb/trino/blob/bc7a4eeedde28684c7ae6f74cefcaf7c6e782174/plugin/trino-ml/src/main/java/io/trino/plugin/ml/MLPlugin.java#L35\n    // https://github.com/trinodb/trino/blob/bc7a4eeedde28684c7ae6f74cefcaf7c6e782174/plugin/trino-mongodb/src/main/java/io/trino/plugin/mongodb/MongoPlugin.java#L32\n    // https://github.com/trinodb/trino/blob/bc7a4eeedde28684c7ae6f74cefcaf7c6e782174/plugin/trino-geospatial/src/main/java/io/trino/plugin/geospatial/GeoPlugin.java#L37\n    builtin: set(\"array bigint bingtile boolean char codepoints color date decimal double function geometry hyperloglog int integer interval ipaddress joniregexp json json2016 jsonpath kdbtree likepattern map model objectid p4hyperloglog precision qdigest re2jregexp real regressor row setdigest smallint sphericalgeography tdigest time timestamp tinyint uuid varbinary varchar zone\"),\n    atoms: set(\"false true null unknown\"),\n    // https://trino.io/docs/current/functions/list.html#id1\n    operatorChars: /^[[\\]|<>=!\\-+*/%]/,\n    dateSQL: set(\"date time timestamp zone\"),\n    // hexNumber is necessary for VARBINARY literals, e.g. X'65683F'\n    // but it also enables 0xFF hex numbers, which Trino doesn't support.\n    support: set(\"decimallessFloat zerolessFloat hexNumber\")\n  });\n});\n\n/*\n  How Properties of Mime Types are used by SQL Mode\n  =================================================\n\n  keywords:\n    A list of keywords you want to be highlighted.\n  builtin:\n    A list of builtin types you want to be highlighted (if you want types to be of class \"builtin\" instead of \"keyword\").\n  operatorChars:\n    All characters that must be handled as operators.\n  client:\n    Commands parsed and executed by the client (not the server).\n  support:\n    A list of supported syntaxes which are not common, but are supported by more than 1 DBMS.\n    * zerolessFloat: .1\n    * decimallessFloat: 1.\n    * hexNumber: X'01AF' X'01af' x'01AF' x'01af' 0x01AF 0x01af\n    * binaryNumber: b'01' B'01' 0b01\n    * doubleQuote: \"string\"\n    * escapeConstant: E''\n    * nCharCast: N'string'\n    * charsetCast: _utf8'string'\n    * commentHash: use # char for comments\n    * commentSlashSlash: use // for comments\n    * commentSpaceRequired: require a space after -- for comments\n  atoms:\n    Keywords that must be highlighted as atoms,. Some DBMS's support more atoms than others:\n    UNKNOWN, INFINITY, UNDERFLOW, NaN...\n  dateSQL:\n    Used for date/time SQL standard syntax, because not all DBMS's support same temporal types.\n*/\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACxB;AAEA,MAAAA,YAAW,WAAW,OAAO,SAAS,QAAQ,cAAc;AAC1D,YAAI,SAAiB,aAAa,UAAU,CAAC,GACzC,QAAiB,aAAa,SAAS,EAAC,SAAS,MAAM,QAAQ,MAAM,QAAQ,KAAI,GACjF,UAAiB,aAAa,WAAW,IAAI,cAAc,GAC3D,WAAiB,aAAa,YAAY,IAAI,WAAW,GACzD,gBAAiB,aAAa,iBAAiB,sBAC/C,UAAiB,aAAa,WAAW,CAAC,GAC1C,QAAiB,aAAa,SAAS,CAAC,GACxC,UAAiB,aAAa,WAAW,EAAC,QAAS,MAAM,QAAS,MAAM,aAAc,KAAI,GAC1F,yBAAyB,aAAa,2BAA2B,OACjE,WAAiB,aAAa,YAAY,kBAC1C,cAAiB,aAAa,eAAe;AAEjD,iBAAS,UAAU,QAAQ,OAAO;AAChC,cAAI,KAAK,OAAO,KAAK;AAGrB,cAAI,MAAM,EAAE,GAAG;AACb,gBAAI,SAAS,MAAM,EAAE,EAAE,QAAQ,KAAK;AACpC,gBAAI,WAAW,MAAO,QAAO;AAAA,UAC/B;AAEA,cAAI,QAAQ,cACR,MAAM,OAAO,OAAO,MAAM,mBAAmB,MAC3C,MAAM,OAAO,MAAM,QAAQ,OAAO,MAAM,iBAAiB,IAAI;AAGjE,mBAAO;AAAA,UACT,WAAW,QAAQ,kBACd,MAAM,OAAO,MAAM,QAAQ,OAAO,MAAM,UAAU,KACjD,MAAM,OAAO,OAAO,MAAM,SAAS,IAAK;AAG5C,mBAAO;AAAA,UACT,WAAW,GAAG,WAAW,CAAC,IAAI,MAAM,GAAG,WAAW,CAAC,IAAI,IAAI;AAGzD,mBAAO,MAAM,sCAAsC;AACnD,oBAAQ,oBAAoB,OAAO,MAAM,WAAW;AACpD,mBAAO;AAAA,UACT,WAAW,MAAM,QAAQ,OAAO,SAAS,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,GAAG,IAAI;AAE9E,mBAAO;AAAA,UACT,WAAW,MAAM,OAAQ,MAAM,OAAO,QAAQ,aAAc;AAG1D,kBAAM,WAAW,aAAa,EAAE;AAChC,mBAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,UACrC,YAAc,QAAQ,cAAc,MAAM,OAAO,MAAM,QAC/C,QAAQ,eAAe,MAAM,OAAO,OAAO,MAAM,iBAAiB,OAClE,OAAO,KAAK,KAAK,OAAO,OAAO,KAAK,KAAK,MAAO;AAGtD,mBAAO;AAAA,UACT,WAAW,QAAQ,mBAAmB,MAAM,OAAO,MAAM,SACjD,OAAO,KAAK,KAAK,OAAQ,OAAO,KAAK,KAAK,OAAO,QAAQ,cAAe;AAG9E,kBAAM,WAAW,SAASC,SAAQC,QAAO;AACvC,sBAAQA,OAAM,WAAW,aAAaD,QAAO,KAAK,GAAG,IAAI,GAAGA,SAAQC,MAAK;AAAA,YAC3E;AACA,mBAAO;AAAA,UACT,WAAW,QAAQ,qBAAqB,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AAEpE,mBAAO,UAAU;AACjB,mBAAO;AAAA,UACT,WAAY,QAAQ,eAAe,MAAM,OACjC,MAAM,OAAO,OAAO,IAAI,GAAG,MAAM,CAAC,QAAQ,wBAAwB,OAAO,IAAI,GAAG,IAAK;AAG3F,mBAAO,UAAU;AACjB,mBAAO;AAAA,UACT,WAAW,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AAGvC,kBAAM,WAAW,aAAa,CAAC;AAC/B,mBAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,UACrC,WAAW,MAAM,KAAK;AAEpB,gBAAI,QAAQ,iBAAiB,OAAO,MAAM,yBAAyB;AACjE,qBAAO;AACT,gBAAI,OAAO,MAAM,MAAM;AACrB,qBAAO;AACT,gBAAI,OAAO,MAAM,aAAa;AAC5B,qBAAO;AAAA,UACX,WAAW,cAAc,KAAK,EAAE,GAAG;AAEjC,mBAAO,SAAS,aAAa;AAC7B,mBAAO;AAAA,UACT,WAAW,SAAS,KAAK,EAAE,GAAG;AAE5B,mBAAO;AAAA,UACT,WAAW,YAAY,KAAK,EAAE,GAAG;AAE/B,mBAAO,SAAS,WAAW;AAC3B,mBAAO;AAAA,UACT,WAAW,MAAM,QACZ,OAAO,MAAM,sCAAsC,KAAK,OAAO,MAAM,sCAAsC,IAAI;AAGlH,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,SAAS,UAAU;AAC1B,gBAAI,OAAO,OAAO,QAAQ,EAAE,YAAY;AAGxC,gBAAI,QAAQ,eAAe,IAAI,MAAM,OAAO,MAAM,cAAc,KAAK,OAAO,MAAM,cAAc;AAC9F,qBAAO;AACT,gBAAI,MAAM,eAAe,IAAI,EAAG,QAAO;AACvC,gBAAI,QAAQ,eAAe,IAAI,EAAG,QAAO;AACzC,gBAAI,SAAS,eAAe,IAAI,EAAG,QAAO;AAC1C,gBAAI,OAAO,eAAe,IAAI,EAAG,QAAO;AACxC,mBAAO;AAAA,UACT;AAAA,QACF;AAGA,iBAAS,aAAa,OAAO,kBAAkB;AAC7C,iBAAO,SAAS,QAAQ,OAAO;AAC7B,gBAAI,UAAU,OAAO;AACrB,oBAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,kBAAI,MAAM,SAAS,CAAC,SAAS;AAC3B,sBAAM,WAAW;AACjB;AAAA,cACF;AACA,yBAAW,0BAA0B,qBAAqB,CAAC,WAAW,MAAM;AAAA,YAC9E;AACA,mBAAO;AAAA,UACT;AAAA,QACF;AACA,iBAAS,aAAa,OAAO;AAC3B,iBAAO,SAAS,QAAQ,OAAO;AAC7B,gBAAI,IAAI,OAAO,MAAM,iBAAiB;AACtC,gBAAI,CAAC,EAAG,QAAO,UAAU;AAAA,qBAChB,EAAE,CAAC,KAAK,KAAM,OAAM,WAAW,aAAa,QAAQ,CAAC;AAAA,qBACrD,QAAQ,EAAG,OAAM,WAAW,aAAa,QAAQ,CAAC;AAAA,gBACtD,OAAM,WAAW;AACtB,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,iBAAS,YAAY,QAAQ,OAAO,MAAM;AACxC,gBAAM,UAAU;AAAA,YACd,MAAM,MAAM;AAAA,YACZ,QAAQ,OAAO,YAAY;AAAA,YAC3B,KAAK,OAAO,OAAO;AAAA,YACnB;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,WAAW,OAAO;AACzB,gBAAM,SAAS,MAAM,QAAQ;AAC7B,gBAAM,UAAU,MAAM,QAAQ;AAAA,QAChC;AAEA,eAAO;AAAA,UACL,YAAY,WAAW;AACrB,mBAAO,EAAC,UAAU,WAAW,SAAS,KAAI;AAAA,UAC5C;AAAA,UAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,gBAAI,OAAO,IAAI,GAAG;AAChB,kBAAI,MAAM,WAAW,MAAM,QAAQ,SAAS;AAC1C,sBAAM,QAAQ,QAAQ;AAAA,YAC1B;AACA,gBAAI,MAAM,YAAY,aAAa,OAAO,SAAS,EAAG,QAAO;AAE7D,gBAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AACxC,gBAAI,SAAS,UAAW,QAAO;AAE/B,gBAAI,MAAM,WAAW,MAAM,QAAQ,SAAS;AAC1C,oBAAM,QAAQ,QAAQ;AAExB,gBAAI,MAAM,OAAO,QAAQ;AACzB,gBAAI,OAAO;AACT,0BAAY,QAAQ,OAAO,GAAG;AAAA,qBACvB,OAAO;AACd,0BAAY,QAAQ,OAAO,GAAG;AAAA,qBACvB,MAAM,WAAW,MAAM,QAAQ,QAAQ;AAC9C,yBAAW,KAAK;AAClB,mBAAO;AAAA,UACT;AAAA,UAEA,QAAQ,SAAS,OAAO,WAAW;AACjC,gBAAI,KAAK,MAAM;AACf,gBAAI,CAAC,GAAI,QAAOF,YAAW;AAC3B,gBAAI,UAAU,UAAU,OAAO,CAAC,KAAK,GAAG;AACxC,gBAAI,GAAG,MAAO,QAAO,GAAG,OAAO,UAAU,IAAI;AAAA,gBACxC,QAAO,GAAG,UAAU,UAAU,IAAI,OAAO;AAAA,UAChD;AAAA,UAEA,mBAAmB;AAAA,UACnB,iBAAiB;AAAA,UACjB,aAAa,QAAQ,oBAAoB,OAAO,QAAQ,cAAc,MAAM;AAAA,UAC5E,eAAe;AAAA,UACf,QAAQ;AAAA,QACV;AAAA,MACF,CAAC;AAGC,eAAS,eAAe,QAAQ;AAG9B,YAAI;AACJ,gBAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,cAAI,MAAM,OAAO,CAAC,OAAO,IAAI,GAAG,EAAG,QAAO;AAAA,QAC5C;AACA,eAAO,OAAO,OAAO,QAAQ,EAAE,SAAS,CAAC;AACzC,eAAO,OAAO,SAAS,IAAI,IAAI,eAAe;AAAA,MAChD;AAGA,eAAS,0BAA0B,QAAQ;AAIzC,YAAI;AACJ,gBAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,cAAI,MAAM,OAAQ,CAAC,OAAO,IAAI,GAAI,EAAG,QAAO;AAAA,QAC9C;AACA,eAAO,OAAO,OAAO,QAAQ,EAAE,SAAS,CAAC;AACzC,eAAO,OAAO,SAAS,IAAI,IAAI,eAAe;AAAA,MAChD;AAGA,eAAS,QAAQ,QAAQ;AAKvB,YAAI,OAAO,IAAI,GAAG,GAAG;AACnB,iBAAO,MAAM,UAAU;AACvB,iBAAO,MAAM,QAAQ;AACrB,iBAAO,MAAM,SAAS;AAAA,QACxB;AAEA,YAAI,OAAO,IAAI,GAAG,GAAG;AACnB,iBAAO,MAAM,MAAM;AACnB,iBAAO;AAAA,QACT,WAAW,OAAO,IAAI,GAAG,GAAG;AAC1B,iBAAO,MAAM,MAAM;AACnB,iBAAO;AAAA,QACT,WAAW,OAAO,IAAI,GAAG,GAAG;AAC1B,iBAAO,MAAM,MAAM;AACnB,iBAAO;AAAA,QACT,WAAW,OAAO,MAAM,oBAAoB,GAAG;AAC7C,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAC;AAGD,eAAS,WAAW,QAAQ;AAG1B,YAAI,OAAO,IAAI,GAAG,GAAG;AACjB,iBAAO;AAAA,QACX;AAGA,eAAO,OAAO,MAAM,eAAe,IAAI,eAAe;AAAA,MACxD;AAGA,UAAI,cAAc;AAGlB,eAAS,IAAI,KAAK;AAChB,YAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,MAAM,GAAG;AACnC,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,EAAG,KAAI,MAAM,CAAC,CAAC,IAAI;AACvD,eAAO;AAAA,MACT;AAEA,UAAI,iBAAiB;AAGrB,MAAAA,YAAW,WAAW,cAAc;AAAA,QAClC,MAAM;AAAA,QACN,UAAU,IAAI,cAAc,OAAO;AAAA,QACnC,SAAS,IAAI,cAAc;AAAA,QAC3B,OAAO,IAAI,yBAAyB;AAAA,QACpC,SAAS,IAAI,qBAAqB;AAAA,QAClC,SAAS,IAAI,oCAAoC;AAAA,MACnD,CAAC;AAED,MAAAA,YAAW,WAAW,gBAAgB;AAAA,QACpC,MAAM;AAAA,QACN,QAAQ,IAAI,uVAAuV;AAAA,QACnW,UAAU,IAAI,cAAc,qSAAqS;AAAA,QACjU,SAAS,IAAI,gNAAgN;AAAA,QAC7N,OAAO,IAAI,wGAAwG;AAAA,QACnH,eAAe;AAAA,QACf,UAAU;AAAA,QACV,aAAa;AAAA,QACb,wBAAwB;AAAA,QACxB,SAAS,IAAI,2DAA2D;AAAA,QACxE,OAAO;AAAA,UACL,KAAO;AAAA,QACT;AAAA,MACF,CAAC;AAED,MAAAA,YAAW,WAAW,gBAAgB;AAAA,QACpC,MAAM;AAAA,QACN,QAAQ,IAAI,6HAA6H;AAAA,QACzI,UAAU,IAAI,cAAc,o9FAAo9F;AAAA,QACh/F,SAAS,IAAI,wTAAwT;AAAA,QACrU,OAAO,IAAI,yBAAyB;AAAA,QACpC,eAAe;AAAA,QACf,SAAS,IAAI,qBAAqB;AAAA,QAClC,SAAS,IAAI,0HAA0H;AAAA,QACvI,OAAO;AAAA,UACL,KAAO;AAAA,UACP,KAAO;AAAA,UACP,MAAO;AAAA,QACT;AAAA,MACF,CAAC;AAED,MAAAA,YAAW,WAAW,kBAAkB;AAAA,QACtC,MAAM;AAAA,QACN,QAAQ,IAAI,6HAA6H;AAAA,QACzI,UAAU,IAAI,cAAc,2gGAA2gG;AAAA,QACviG,SAAS,IAAI,wTAAwT;AAAA,QACrU,OAAO,IAAI,yBAAyB;AAAA,QACpC,eAAe;AAAA,QACf,SAAS,IAAI,qBAAqB;AAAA,QAClC,SAAS,IAAI,0HAA0H;AAAA,QACvI,OAAO;AAAA,UACL,KAAO;AAAA,UACP,KAAO;AAAA,UACP,MAAO;AAAA,QACT;AAAA,MACF,CAAC;AAGD,MAAAA,YAAW,WAAW,iBAAiB;AAAA,QACrC,MAAM;AAAA;AAAA,QAEN,QAAQ,IAAI,sWAAsW;AAAA;AAAA,QAElX,UAAU,IAAI,cAAc,6pBAA6pB;AAAA;AAAA,QAEzrB,SAAS,IAAI,oQAAoQ;AAAA;AAAA,QAEjR,OAAO,IAAI,kDAAkD;AAAA;AAAA,QAE7D,eAAe;AAAA;AAAA,QAEf,SAAS,IAAI,8BAA8B;AAAA,QAC3C,SAAS,IAAI,gCAAgC;AAAA,QAC7C,iBAAiB;AAAA;AAAA,QACjB,OAAO;AAAA;AAAA,UAEL,KAAO;AAAA,UACP,KAAO;AAAA,UACP,KAAO;AAAA,UACP,KAAO;AAAA;AAAA,UAEP,KAAQ;AAAA;AAAA,UAER,KAAO;AAAA,QACT;AAAA,MACF,CAAC;AAID,MAAAA,YAAW,WAAW,oBAAoB;AAAA,QACxC,MAAM;AAAA,QACN,QAAQ,CAAE;AAAA,QACV,UAAU,IAAI,2hBAA2hB;AAAA,QACziB,SAAS,IAAI,0IAA0I;AAAA,QACvJ,OAAO,IAAI,yBAAyB;AAAA,QACpC,eAAe;AAAA,QACf,SAAS,CAAE;AAAA,QACX,SAAS,IAAI,oCAAoC;AAAA,QACjD,OAAO,CAAE;AAAA,MACX,CAAC;AAGD,MAAAA,YAAW,WAAW,gBAAgB;AAAA,QACpC,MAAY;AAAA,QACZ,QAAY,IAAI,gqBAAgqB;AAAA,QAChrB,UAAY,IAAI,itDAAitD;AAAA,QACjuD,SAAY,IAAI,i9BAAi9B;AAAA,QACj+B,eAAe;AAAA,QACf,SAAY,IAAI,qBAAqB;AAAA,QACrC,SAAY,IAAI,4DAA4D;AAAA,MAC9E,CAAC;AAGD,MAAAA,YAAW,WAAW,eAAe;AAAA,QACnC,MAAM;AAAA,QACN,UAAU,IAAI,k1DAAk1D;AAAA,QACh2D,SAAS,IAAI,wKAAwK;AAAA,QACrL,OAAO,IAAI,yBAAyB;AAAA,QACpC,eAAe;AAAA,QACf,SAAS,IAAI,gBAAgB;AAAA,QAC7B,SAAS,IAAI,oCAAoC;AAAA,MACnD,CAAC;AAED,MAAAA,YAAW,WAAW,gBAAgB;AAAA,QACpC,MAAM;AAAA,QACN,QAAQ,IAAI,QAAQ;AAAA;AAAA;AAAA,QAGpB,UAAU,IAAI,cAAc,m3NAAm3N;AAAA;AAAA,QAE/4N,SAAS,IAAI,+YAA+Y;AAAA,QAC5Z,OAAO,IAAI,yBAAyB;AAAA,QACpC,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,SAAS,IAAI,qBAAqB;AAAA,QAClC,SAAS,IAAI,4FAA4F;AAAA,MAC3G,CAAC;AAGD,MAAAA,YAAW,WAAW,cAAc;AAAA,QAClC,MAAM;AAAA,QACN,UAAU,IAAI,wHAAwH;AAAA,QACtI,OAAO,IAAI,YAAY;AAAA,QACvB,SAAS,IAAI,oEAAoE;AAAA,QACjF,eAAe;AAAA,MACjB,CAAC;AAGD,MAAAA,YAAW,WAAW,gBAAgB;AAAA,QACpC,MAAM;AAAA,QACN,QAAQ,IAAI,QAAQ;AAAA;AAAA,QAEpB,UAAU,IAAI,y7GAAy7G;AAAA,QACv8G,SAAS,IAAI,kaAAka;AAAA,QAC/a,OAAO,IAAI,yBAAyB;AAAA,QACpC,eAAe;AAAA,QACf,SAAS,IAAI,qBAAqB;AAAA,QAClC,SAAS,IAAI,6EAA6E;AAAA,MAC5F,CAAC;AAGD,MAAAA,YAAW,WAAW,mBAAmB;AAAA,QACvC,MAAM;AAAA,QACN,UAAU,IAAI,i+CAAi+C;AAAA,QAC/+C,SAAS,IAAI,ooGAAooG;AAAA,QACjpG,OAAO,IAAI,iBAAiB;AAAA,QAC5B,eAAe;AAAA,QACf,SAAS,IAAI,qBAAqB;AAAA,QAClC,SAAS,IAAI,2BAA2B;AAAA,MAC1C,CAAC;AAGD,MAAAA,YAAW,WAAW,gBAAgB;AAAA,QACpC,MAAM;AAAA,QACN,QAAQ,IAAI,QAAQ;AAAA;AAAA,QAEpB,UAAU,IAAI,60BAA60B;AAAA,QAC31B,SAAS,CAAC;AAAA,QACV,OAAO,IAAI,iBAAiB;AAAA,QAC5B,eAAe;AAAA,QACf,SAAS,IAAI,MAAM;AAAA,QACnB,SAAS,IAAI,uDAAuD;AAAA,MACtE,CAAC;AAGD,MAAAA,YAAW,WAAW,gBAAgB;AAAA,QACpC,MAAM;AAAA;AAAA;AAAA,QAGN,UAAU,IAAI,q1MAAq1M;AAAA;AAAA;AAAA;AAAA;AAAA,QAKn2M,SAAS,IAAI,8WAA8W;AAAA,QAC3X,OAAO,IAAI,yBAAyB;AAAA;AAAA,QAEpC,eAAe;AAAA,QACf,SAAS,IAAI,0BAA0B;AAAA;AAAA;AAAA,QAGvC,SAAS,IAAI,0CAA0C;AAAA,MACzD,CAAC;AAAA,IACH,CAAC;AAAA;AAAA;", "names": ["CodeMirror", "stream", "state"]}