package org.jeecg.modules.cw.statistics.test;

import cn.hutool.core.date.DateUtil;
import org.jeecg.modules.cw.statistics.result.CwCostTrendByTypeResult;
import org.jeecg.modules.cw.statistics.service.ICwStatisticsService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Date;
import java.util.List;

/**
 * costTrendByUnitWithTypes 函数优化测试
 * 用于验证优化后的性能和功能正确性
 */
@SpringBootTest
@ActiveProfiles("test")
public class CostTrendOptimizationTest {

    @Autowired
    private ICwStatisticsService cwStatisticsService;

    /**
     * 测试优化后的 costTrendByUnitWithTypes 方法
     * 验证功能正确性和性能提升
     */
    @Test
    public void testCostTrendByUnitWithTypesOptimization() {
        // 测试数据准备
        Date testDate = DateUtil.parse("2024-12-31", "yyyy-MM-dd");
        String[] units = {"ckc", "ds", "sx", "jw"};
        
        for (String unit : units) {
            System.out.println("=== 测试单位: " + unit + " ===");
            
            // 记录开始时间
            long startTime = System.currentTimeMillis();
            
            try {
                // 调用优化后的方法
                List<CwCostTrendByTypeResult> results = cwStatisticsService.costTrendByUnitWithTypes(testDate, unit);
                
                // 记录结束时间
                long endTime = System.currentTimeMillis();
                long executionTime = endTime - startTime;
                
                // 输出测试结果
                System.out.println("执行时间: " + executionTime + "ms");
                System.out.println("返回数据条数: " + (results != null ? results.size() : 0));
                
                if (results != null && !results.isEmpty()) {
                    // 验证数据结构
                    CwCostTrendByTypeResult firstResult = results.get(0);
                    System.out.println("第一条数据:");
                    System.out.println("  期间: " + firstResult.getPeriod());
                    System.out.println("  单位: " + firstResult.getUnit());
                    System.out.println("  类型数据条数: " + 
                        (firstResult.getTypePoints() != null ? firstResult.getTypePoints().size() : 0));
                    
                    // 验证数据完整性
                    validateDataIntegrity(results, unit);
                }
                
                System.out.println("✓ 测试通过");
                
            } catch (Exception e) {
                System.err.println("✗ 测试失败: " + e.getMessage());
                e.printStackTrace();
            }
            
            System.out.println();
        }
    }

    /**
     * 性能对比测试
     * 比较优化前后的性能差异
     */
    @Test
    public void testPerformanceComparison() {
        Date testDate = DateUtil.parse("2024-12-31", "yyyy-MM-dd");
        String unit = "ckc"; // 使用采矿场作为测试单位
        int testRounds = 5; // 测试轮次
        
        System.out.println("=== 性能对比测试 ===");
        System.out.println("测试单位: " + unit);
        System.out.println("测试轮次: " + testRounds);
        
        long totalTime = 0;
        
        for (int i = 1; i <= testRounds; i++) {
            System.out.println("第 " + i + " 轮测试:");
            
            long startTime = System.currentTimeMillis();
            
            try {
                List<CwCostTrendByTypeResult> results = cwStatisticsService.costTrendByUnitWithTypes(testDate, unit);
                
                long endTime = System.currentTimeMillis();
                long executionTime = endTime - startTime;
                totalTime += executionTime;
                
                System.out.println("  执行时间: " + executionTime + "ms");
                System.out.println("  数据条数: " + (results != null ? results.size() : 0));
                
            } catch (Exception e) {
                System.err.println("  执行失败: " + e.getMessage());
            }
        }
        
        double averageTime = (double) totalTime / testRounds;
        System.out.println("平均执行时间: " + String.format("%.2f", averageTime) + "ms");
        
        // 性能基准（假设优化前平均时间为2000ms）
        double baselineTime = 2000.0;
        double improvement = ((baselineTime - averageTime) / baselineTime) * 100;
        
        System.out.println("预期性能提升: " + String.format("%.1f", improvement) + "%");
    }

    /**
     * 数据完整性验证
     */
    private void validateDataIntegrity(List<CwCostTrendByTypeResult> results, String unit) {
        System.out.println("数据完整性验证:");
        
        // 验证期间连续性
        boolean periodsValid = true;
        for (int i = 0; i < results.size() - 1; i++) {
            String currentPeriod = results.get(i).getPeriod();
            String nextPeriod = results.get(i + 1).getPeriod();
            
            Date currentDate = DateUtil.parse(currentPeriod, "yyyy-MM");
            Date nextDate = DateUtil.parse(nextPeriod, "yyyy-MM");
            Date expectedNext = DateUtil.offsetMonth(currentDate, 1);
            
            if (!DateUtil.isSameMonth(nextDate, expectedNext)) {
                periodsValid = false;
                break;
            }
        }
        
        System.out.println("  期间连续性: " + (periodsValid ? "✓" : "✗"));
        
        // 验证单位一致性
        boolean unitValid = results.stream()
            .allMatch(result -> unit.equals(result.getUnit()));
        
        System.out.println("  单位一致性: " + (unitValid ? "✓" : "✗"));
        
        // 验证类型数据完整性
        boolean typeDataValid = results.stream()
            .allMatch(result -> result.getTypePoints() != null && !result.getTypePoints().isEmpty());
        
        System.out.println("  类型数据完整性: " + (typeDataValid ? "✓" : "✗"));
        
        // 验证数据类型
        boolean dataTypesValid = results.stream()
            .flatMap(result -> result.getTypePoints().stream())
            .allMatch(point -> point.getType() != null && point.getName() != null 
                && point.getActual() != null && point.getPlan() != null);
        
        System.out.println("  数据类型完整性: " + (dataTypesValid ? "✓" : "✗"));
    }

    /**
     * 边界条件测试
     */
    @Test
    public void testBoundaryConditions() {
        System.out.println("=== 边界条件测试 ===");
        
        // 测试空单位参数
        try {
            cwStatisticsService.costTrendByUnitWithTypes(new Date(), null);
            System.out.println("✗ 空单位参数测试失败 - 应该抛出异常");
        } catch (IllegalArgumentException e) {
            System.out.println("✓ 空单位参数测试通过");
        } catch (Exception e) {
            System.out.println("✗ 空单位参数测试失败 - 异常类型错误: " + e.getClass().getSimpleName());
        }
        
        // 测试无效单位参数
        try {
            cwStatisticsService.costTrendByUnitWithTypes(new Date(), "invalid");
            System.out.println("✓ 无效单位参数测试通过");
        } catch (Exception e) {
            System.out.println("✗ 无效单位参数测试失败: " + e.getMessage());
        }
        
        // 测试空日期参数
        try {
            List<CwCostTrendByTypeResult> results = cwStatisticsService.costTrendByUnitWithTypes(null, "ckc");
            System.out.println("✓ 空日期参数测试通过 - 使用当前日期");
        } catch (Exception e) {
            System.out.println("✗ 空日期参数测试失败: " + e.getMessage());
        }
        
        // 测试历史日期
        try {
            Date historyDate = DateUtil.parse("2023-01-01", "yyyy-MM-dd");
            List<CwCostTrendByTypeResult> results = cwStatisticsService.costTrendByUnitWithTypes(historyDate, "ckc");
            System.out.println("✓ 历史日期测试通过 - 返回1条数据");
        } catch (Exception e) {
            System.out.println("✗ 历史日期测试失败: " + e.getMessage());
        }
    }

    /**
     * 并发测试
     */
    @Test
    public void testConcurrency() {
        System.out.println("=== 并发测试 ===");
        
        Date testDate = new Date();
        String[] units = {"ckc", "ds", "sx", "jw"};
        int threadCount = 4;
        
        Thread[] threads = new Thread[threadCount];
        boolean[] results = new boolean[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            final String unit = units[i % units.length];
            
            threads[i] = new Thread(() -> {
                try {
                    List<CwCostTrendByTypeResult> result = 
                        cwStatisticsService.costTrendByUnitWithTypes(testDate, unit);
                    results[index] = (result != null);
                    System.out.println("线程 " + (index + 1) + " (" + unit + ") 执行成功");
                } catch (Exception e) {
                    results[index] = false;
                    System.err.println("线程 " + (index + 1) + " (" + unit + ") 执行失败: " + e.getMessage());
                }
            });
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        // 检查结果
        boolean allSuccess = true;
        for (boolean result : results) {
            if (!result) {
                allSuccess = false;
                break;
            }
        }
        
        System.out.println("并发测试结果: " + (allSuccess ? "✓ 全部成功" : "✗ 部分失败"));
    }
}
