package org.jeecg.modules.cw.krb.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.ListUtils;
import org.jeecg.modules.cw.base.entity.CwNameDict;
import org.jeecg.modules.cw.base.service.ICwNameDictService;
import org.jeecg.modules.cw.ckc.service.ICwCkcZhcbService;
import org.jeecg.modules.cw.ds.service.ICwDsZhcbService;
import org.jeecg.modules.cw.jg.service.ICwJgZhcbService;
import org.jeecg.modules.cw.jh.service.ICwJhZhcbService;
import org.jeecg.modules.cw.jw.service.ICwJwZhcbService;
import org.jeecg.modules.cw.krb.entity.CwKrb;
import org.jeecg.modules.cw.krb.entity.CwKrbRow;
import org.jeecg.modules.cw.krb.mapper.CwKrbMapper;
import org.jeecg.modules.cw.krb.param.CwKrbSumbitParam;
import org.jeecg.modules.cw.krb.result.CwKrbQueryResult;
import org.jeecg.modules.cw.krb.service.ICwKrbService;
import org.jeecg.modules.cw.sx.service.ICwSxZhcbService;
import org.jeecg.modules.cw.base.service.ICwCllDataService;
import org.jeecg.modules.cw.base.constants.CwCllDataName;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Description: 矿成本日报表
 * @Author: jeecg-boot
 * @Date: 2024-12-31
 * @Version: V1.0
 */
@Service
public class CwKrbServiceImpl extends ServiceImpl<CwKrbMapper, CwKrb> implements ICwKrbService {

    @Resource
    private ICwCkcZhcbService ckcZhcbService;
    @Resource
    private ICwDsZhcbService dsZhcbService;
    @Resource
    private ICwSxZhcbService sxZhcbService;
    @Resource
    private ICwJwZhcbService jwZhcbService;
    @Resource
    private ICwJhZhcbService jhZhcbService;
    @Resource
    private ICwJgZhcbService jgZhcbService;

    @Resource
    private ICwCllDataService cwCllDataService;

    @Override
    public CwKrbQueryResult queryByDate(Date queryDate) {
        CwKrbQueryResult result = new CwKrbQueryResult();
        result.setQueryDate(queryDate);
        // 获取各单位数据
        List<CwKrbRow> ckcRows = ckcZhcbService.sumByMonth(queryDate);
        List<CwKrbRow> dsRows = dsZhcbService.sumByMonth(queryDate);
        List<CwKrbRow> sxRows = sxZhcbService.sumByMonth(queryDate);
        List<CwKrbRow> jwRows = jwZhcbService.sumByMonth(queryDate);
        List<CwKrbRow> jhRows = jhZhcbService.sumByMonth(queryDate);
        List<CwKrbRow> jgRows = jgZhcbService.sumByMonth(queryDate);
        // === 日处理量：jw的处理量 = ds的处理量 + sx的处理量 当天 ===
        String todayKey = DateUtil.format(queryDate, "yyyy-MM-dd");
        BigDecimal cllSum = BigDecimal.ZERO;

        // 获取大山厂处理量
        Map<String, String> dsCllDataMap = cwCllDataService.getRangeCllData(CwCllDataName.DSCLL, queryDate, queryDate);
        if(ObjectUtil.isNotEmpty(dsCllDataMap)){
            String dsCllStr = dsCllDataMap.get(todayKey);
            if(ObjectUtil.isNotEmpty(dsCllStr)){
                cllSum = cllSum.add(new BigDecimal(dsCllStr));
            }
        }
        // 获取泗选厂处理量
        Map<String, String> sxCllDataMap = cwCllDataService.getRangeCllData(CwCllDataName.SZCLL, queryDate, queryDate);
        if(ObjectUtil.isNotEmpty(sxCllDataMap)){
            String sxCllStr = sxCllDataMap.get(todayKey);
            if(ObjectUtil.isNotEmpty(sxCllStr)){
                cllSum = cllSum.add(new BigDecimal(sxCllStr));
            }
        }
        result.setCll(cllSum);

        // === 当日总成本：各分类行 drs 字段求和 ===
        List<CwKrbRow> rows = Stream.of(ckcRows, dsRows, sxRows, jwRows, jhRows, jgRows).flatMap(List::stream).collect(Collectors.toList());
        BigDecimal costSum = BigDecimal.ZERO;
        for (CwKrbRow row : rows) {
            if (row.getDrs() != null && row.getDrs().compareTo(BigDecimal.ZERO) != 0) {
                costSum = costSum.add(row.getDrs());
            }
        }
        if (cllSum.compareTo(BigDecimal.ZERO) > 0) {
            result.setDkcb(costSum.divide(cllSum, 4, RoundingMode.HALF_UP));
        } else {
            result.setDkcb(BigDecimal.ZERO);
        }
        // 结果
        result.setRows(rows);
        return result;
    }

    @Override
    public void submit(CwKrbSumbitParam param) {
        Date submitDate = param.getSubmitDate();
        // 更新行数据
        List<CwKrbRow> rows = param.getRows();
        this.remove(new LambdaQueryWrapper<>(CwKrb.class)
                .ge(CwKrb::getRecordTime, DateUtil.beginOfMonth(submitDate))
                .le(CwKrb::getRecordTime, DateUtil.endOfMonth(submitDate)));
        ArrayList<CwKrb> months = new ArrayList<>();
        for (CwKrbRow row : rows) {
            CwKrb month = new CwKrb();
            BeanUtil.copyProperties(row, month);
            month.setRecordTime(submitDate);
            months.add(month);
        }
        this.saveBatch(months);
    }
}
