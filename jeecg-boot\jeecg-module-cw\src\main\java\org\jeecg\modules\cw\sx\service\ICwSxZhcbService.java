package org.jeecg.modules.cw.sx.service;

import org.jeecg.modules.cw.krb.entity.CwKrbRow;
import org.jeecg.modules.cw.sx.param.CwSxZhcbSumbitParam;
import org.jeecg.modules.cw.sx.result.CwSxZhcbListResult;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface ICwSxZhcbService {

    CwSxZhcbListResult query(Date queryDate);

    void submit(CwSxZhcbSumbitParam param);

    BigDecimal sumDrs(Date queryDate);

    List<CwKrbRow> sumByMonth(Date queryDate);

    BigDecimal sumBudgetMonth(Date monthDate);

    CwSxZhcbListResult autoFill(Date queryDate);

    /**
     * 根据日期重新计算drs
     * @param date
     */
    void recalculateDrsByDate(Date date);

    /**
     * 重新计算所有的drs
     */
    void recalculateAllDrs();

    /**
     * 重新计算当日的drs
     */
    void recalculateTodayDrs();

    /**
     * 重新计算指定日期范围内的drs
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    void recalculateDrs(Date startDate, Date endDate);

    /**
     * 批量查询指定日期范围内的月度成本数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return Map<String, List<CwKrbRow>> key为yyyy-MM格式的月份，value为该月的成本数据
     */
    Map<String, List<CwKrbRow>> sumByMonthRange(Date startDate, Date endDate);
}
