{"version": 3, "sources": ["../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/common/utils.mjs", "../../.pnpm/mdurl@2.0.0/node_modules/mdurl/index.mjs", "../../.pnpm/mdurl@2.0.0/node_modules/mdurl/lib/decode.mjs", "../../.pnpm/mdurl@2.0.0/node_modules/mdurl/lib/encode.mjs", "../../.pnpm/mdurl@2.0.0/node_modules/mdurl/lib/format.mjs", "../../.pnpm/mdurl@2.0.0/node_modules/mdurl/lib/parse.mjs", "../../.pnpm/uc.micro@2.1.0/node_modules/uc.micro/index.mjs", "../../.pnpm/uc.micro@2.1.0/node_modules/uc.micro/properties/Any/regex.mjs", "../../.pnpm/uc.micro@2.1.0/node_modules/uc.micro/categories/Cc/regex.mjs", "../../.pnpm/uc.micro@2.1.0/node_modules/uc.micro/categories/Cf/regex.mjs", "../../.pnpm/uc.micro@2.1.0/node_modules/uc.micro/categories/P/regex.mjs", "../../.pnpm/uc.micro@2.1.0/node_modules/uc.micro/categories/S/regex.mjs", "../../.pnpm/uc.micro@2.1.0/node_modules/uc.micro/categories/Z/regex.mjs", "../../.pnpm/entities@4.5.0/node_modules/entities/lib/esm/generated/generated/decode-data-html.ts", "../../.pnpm/entities@4.5.0/node_modules/entities/lib/esm/generated/generated/decode-data-xml.ts", "../../.pnpm/entities@4.5.0/node_modules/entities/lib/esm/decode_codepoint.ts", "../../.pnpm/entities@4.5.0/node_modules/entities/lib/esm/decode.ts", "../../.pnpm/entities@4.5.0/node_modules/entities/lib/esm/generated/generated/encode-html.ts", "../../.pnpm/entities@4.5.0/node_modules/entities/lib/esm/escape.ts", "../../.pnpm/entities@4.5.0/node_modules/entities/lib/esm/index.ts", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/helpers/index.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/helpers/parse_link_label.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/helpers/parse_link_destination.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/helpers/parse_link_title.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/renderer.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/ruler.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/token.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_core/state_core.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_core/normalize.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_core/block.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_core/inline.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_core/linkify.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_core/replacements.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_core/smartquotes.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_core/text_join.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/parser_core.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_block/state_block.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_block/table.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_block/code.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_block/fence.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_block/blockquote.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_block/hr.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_block/list.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_block/reference.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/common/html_blocks.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/common/html_re.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_block/html_block.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_block/heading.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_block/lheading.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_block/paragraph.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/parser_block.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_inline/state_inline.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_inline/text.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_inline/linkify.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_inline/newline.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_inline/escape.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_inline/backticks.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_inline/strikethrough.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_inline/emphasis.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_inline/link.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_inline/image.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_inline/autolink.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_inline/html_inline.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_inline/entity.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_inline/balance_pairs.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/rules_inline/fragments_join.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/parser_inline.mjs", "../../.pnpm/linkify-it@5.0.0/node_modules/linkify-it/lib/re.mjs", "../../.pnpm/linkify-it@5.0.0/node_modules/linkify-it/index.mjs", "../../.pnpm/punycode.js@2.3.1/node_modules/punycode.js/punycode.es6.js", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/presets/default.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/presets/zero.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/presets/commonmark.mjs", "../../.pnpm/markdown-it@14.1.0/node_modules/markdown-it/lib/index.mjs"], "sourcesContent": ["// Utilities\n//\n\nimport * as mdurl from 'mdurl'\nimport * as ucmicro from 'uc.micro'\nimport { decodeHTML } from 'entities'\n\nfunction _class (obj) { return Object.prototype.toString.call(obj) }\n\nfunction isString (obj) { return _class(obj) === '[object String]' }\n\nconst _hasOwnProperty = Object.prototype.hasOwnProperty\n\nfunction has (object, key) {\n  return _hasOwnProperty.call(object, key)\n}\n\n// Merge objects\n//\nfunction assign (obj /* from1, from2, from3, ... */) {\n  const sources = Array.prototype.slice.call(arguments, 1)\n\n  sources.forEach(function (source) {\n    if (!source) { return }\n\n    if (typeof source !== 'object') {\n      throw new TypeError(source + 'must be object')\n    }\n\n    Object.keys(source).forEach(function (key) {\n      obj[key] = source[key]\n    })\n  })\n\n  return obj\n}\n\n// Remove element from array and put another array at those position.\n// Useful for some operations with tokens\nfunction arrayReplaceAt (src, pos, newElements) {\n  return [].concat(src.slice(0, pos), newElements, src.slice(pos + 1))\n}\n\nfunction isValidEntityCode (c) {\n  /* eslint no-bitwise:0 */\n  // broken sequence\n  if (c >= 0xD800 && c <= 0xDFFF) { return false }\n  // never used\n  if (c >= 0xFDD0 && c <= 0xFDEF) { return false }\n  if ((c & 0xFFFF) === 0xFFFF || (c & 0xFFFF) === 0xFFFE) { return false }\n  // control codes\n  if (c >= 0x00 && c <= 0x08) { return false }\n  if (c === 0x0B) { return false }\n  if (c >= 0x0E && c <= 0x1F) { return false }\n  if (c >= 0x7F && c <= 0x9F) { return false }\n  // out of range\n  if (c > 0x10FFFF) { return false }\n  return true\n}\n\nfunction fromCodePoint (c) {\n  /* eslint no-bitwise:0 */\n  if (c > 0xffff) {\n    c -= 0x10000\n    const surrogate1 = 0xd800 + (c >> 10)\n    const surrogate2 = 0xdc00 + (c & 0x3ff)\n\n    return String.fromCharCode(surrogate1, surrogate2)\n  }\n  return String.fromCharCode(c)\n}\n\nconst UNESCAPE_MD_RE  = /\\\\([!\"#$%&'()*+,\\-./:;<=>?@[\\\\\\]^_`{|}~])/g\nconst ENTITY_RE       = /&([a-z#][a-z0-9]{1,31});/gi\nconst UNESCAPE_ALL_RE = new RegExp(UNESCAPE_MD_RE.source + '|' + ENTITY_RE.source, 'gi')\n\nconst DIGITAL_ENTITY_TEST_RE = /^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))$/i\n\nfunction replaceEntityPattern (match, name) {\n  if (name.charCodeAt(0) === 0x23/* # */ && DIGITAL_ENTITY_TEST_RE.test(name)) {\n    const code = name[1].toLowerCase() === 'x'\n      ? parseInt(name.slice(2), 16)\n      : parseInt(name.slice(1), 10)\n\n    if (isValidEntityCode(code)) {\n      return fromCodePoint(code)\n    }\n\n    return match\n  }\n\n  const decoded = decodeHTML(match)\n  if (decoded !== match) {\n    return decoded\n  }\n\n  return match\n}\n\n/* function replaceEntities(str) {\n  if (str.indexOf('&') < 0) { return str; }\n\n  return str.replace(ENTITY_RE, replaceEntityPattern);\n} */\n\nfunction unescapeMd (str) {\n  if (str.indexOf('\\\\') < 0) { return str }\n  return str.replace(UNESCAPE_MD_RE, '$1')\n}\n\nfunction unescapeAll (str) {\n  if (str.indexOf('\\\\') < 0 && str.indexOf('&') < 0) { return str }\n\n  return str.replace(UNESCAPE_ALL_RE, function (match, escaped, entity) {\n    if (escaped) { return escaped }\n    return replaceEntityPattern(match, entity)\n  })\n}\n\nconst HTML_ESCAPE_TEST_RE = /[&<>\"]/\nconst HTML_ESCAPE_REPLACE_RE = /[&<>\"]/g\nconst HTML_REPLACEMENTS = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;'\n}\n\nfunction replaceUnsafeChar (ch) {\n  return HTML_REPLACEMENTS[ch]\n}\n\nfunction escapeHtml (str) {\n  if (HTML_ESCAPE_TEST_RE.test(str)) {\n    return str.replace(HTML_ESCAPE_REPLACE_RE, replaceUnsafeChar)\n  }\n  return str\n}\n\nconst REGEXP_ESCAPE_RE = /[.?*+^$[\\]\\\\(){}|-]/g\n\nfunction escapeRE (str) {\n  return str.replace(REGEXP_ESCAPE_RE, '\\\\$&')\n}\n\nfunction isSpace (code) {\n  switch (code) {\n    case 0x09:\n    case 0x20:\n      return true\n  }\n  return false\n}\n\n// Zs (unicode class) || [\\t\\f\\v\\r\\n]\nfunction isWhiteSpace (code) {\n  if (code >= 0x2000 && code <= 0x200A) { return true }\n  switch (code) {\n    case 0x09: // \\t\n    case 0x0A: // \\n\n    case 0x0B: // \\v\n    case 0x0C: // \\f\n    case 0x0D: // \\r\n    case 0x20:\n    case 0xA0:\n    case 0x1680:\n    case 0x202F:\n    case 0x205F:\n    case 0x3000:\n      return true\n  }\n  return false\n}\n\n/* eslint-disable max-len */\n\n// Currently without astral characters support.\nfunction isPunctChar (ch) {\n  return ucmicro.P.test(ch) || ucmicro.S.test(ch)\n}\n\n// Markdown ASCII punctuation characters.\n//\n// !, \", #, $, %, &, ', (, ), *, +, ,, -, ., /, :, ;, <, =, >, ?, @, [, \\, ], ^, _, `, {, |, }, or ~\n// http://spec.commonmark.org/0.15/#ascii-punctuation-character\n//\n// Don't confuse with unicode punctuation !!! It lacks some chars in ascii range.\n//\nfunction isMdAsciiPunct (ch) {\n  switch (ch) {\n    case 0x21/* ! */:\n    case 0x22/* \" */:\n    case 0x23/* # */:\n    case 0x24/* $ */:\n    case 0x25/* % */:\n    case 0x26/* & */:\n    case 0x27/* ' */:\n    case 0x28/* ( */:\n    case 0x29/* ) */:\n    case 0x2A/* * */:\n    case 0x2B/* + */:\n    case 0x2C/* , */:\n    case 0x2D/* - */:\n    case 0x2E/* . */:\n    case 0x2F/* / */:\n    case 0x3A/* : */:\n    case 0x3B/* ; */:\n    case 0x3C/* < */:\n    case 0x3D/* = */:\n    case 0x3E/* > */:\n    case 0x3F/* ? */:\n    case 0x40/* @ */:\n    case 0x5B/* [ */:\n    case 0x5C/* \\ */:\n    case 0x5D/* ] */:\n    case 0x5E/* ^ */:\n    case 0x5F/* _ */:\n    case 0x60/* ` */:\n    case 0x7B/* { */:\n    case 0x7C/* | */:\n    case 0x7D/* } */:\n    case 0x7E/* ~ */:\n      return true\n    default:\n      return false\n  }\n}\n\n// Hepler to unify [reference labels].\n//\nfunction normalizeReference (str) {\n  // Trim and collapse whitespace\n  //\n  str = str.trim().replace(/\\s+/g, ' ')\n\n  // In node v10 'ẞ'.toLowerCase() === 'Ṿ', which is presumed to be a bug\n  // fixed in v12 (couldn't find any details).\n  //\n  // So treat this one as a special case\n  // (remove this when node v10 is no longer supported).\n  //\n  if ('ẞ'.toLowerCase() === 'Ṿ') {\n    str = str.replace(/ẞ/g, 'ß')\n  }\n\n  // .toLowerCase().toUpperCase() should get rid of all differences\n  // between letter variants.\n  //\n  // Simple .toLowerCase() doesn't normalize 125 code points correctly,\n  // and .toUpperCase doesn't normalize 6 of them (list of exceptions:\n  // İ, ϴ, ẞ, Ω, K, Å - those are already uppercased, but have differently\n  // uppercased versions).\n  //\n  // Here's an example showing how it happens. Lets take greek letter omega:\n  // uppercase U+0398 (Θ), U+03f4 (ϴ) and lowercase U+03b8 (θ), U+03d1 (ϑ)\n  //\n  // Unicode entries:\n  // 0398;GREEK CAPITAL LETTER THETA;Lu;0;L;;;;;N;;;;03B8;\n  // 03B8;GREEK SMALL LETTER THETA;Ll;0;L;;;;;N;;;0398;;0398\n  // 03D1;GREEK THETA SYMBOL;Ll;0;L;<compat> 03B8;;;;N;GREEK SMALL LETTER SCRIPT THETA;;0398;;0398\n  // 03F4;GREEK CAPITAL THETA SYMBOL;Lu;0;L;<compat> 0398;;;;N;;;;03B8;\n  //\n  // Case-insensitive comparison should treat all of them as equivalent.\n  //\n  // But .toLowerCase() doesn't change ϑ (it's already lowercase),\n  // and .toUpperCase() doesn't change ϴ (already uppercase).\n  //\n  // Applying first lower then upper case normalizes any character:\n  // '\\u0398\\u03f4\\u03b8\\u03d1'.toLowerCase().toUpperCase() === '\\u0398\\u0398\\u0398\\u0398'\n  //\n  // Note: this is equivalent to unicode case folding; unicode normalization\n  // is a different step that is not required here.\n  //\n  // Final result should be uppercased, because it's later stored in an object\n  // (this avoid a conflict with Object.prototype members,\n  // most notably, `__proto__`)\n  //\n  return str.toLowerCase().toUpperCase()\n}\n\n// Re-export libraries commonly used in both markdown-it and its plugins,\n// so plugins won't have to depend on them explicitly, which reduces their\n// bundled size (e.g. a browser build).\n//\nconst lib = { mdurl, ucmicro }\n\nexport {\n  lib,\n  assign,\n  isString,\n  has,\n  unescapeMd,\n  unescapeAll,\n  isValidEntityCode,\n  fromCodePoint,\n  escapeHtml,\n  arrayReplaceAt,\n  isSpace,\n  isWhiteSpace,\n  isMdAsciiPunct,\n  isPunctChar,\n  escapeRE,\n  normalizeReference\n}\n", "import decode from './lib/decode.mjs'\nimport encode from './lib/encode.mjs'\nimport format from './lib/format.mjs'\nimport parse from './lib/parse.mjs'\n\nexport {\n  decode,\n  encode,\n  format,\n  parse\n}\n", "/* eslint-disable no-bitwise */\n\nconst decodeCache = {}\n\nfunction getDecodeCache (exclude) {\n  let cache = decodeCache[exclude]\n  if (cache) { return cache }\n\n  cache = decodeCache[exclude] = []\n\n  for (let i = 0; i < 128; i++) {\n    const ch = String.fromCharCode(i)\n    cache.push(ch)\n  }\n\n  for (let i = 0; i < exclude.length; i++) {\n    const ch = exclude.charCodeAt(i)\n    cache[ch] = '%' + ('0' + ch.toString(16).toUpperCase()).slice(-2)\n  }\n\n  return cache\n}\n\n// Decode percent-encoded string.\n//\nfunction decode (string, exclude) {\n  if (typeof exclude !== 'string') {\n    exclude = decode.defaultChars\n  }\n\n  const cache = getDecodeCache(exclude)\n\n  return string.replace(/(%[a-f0-9]{2})+/gi, function (seq) {\n    let result = ''\n\n    for (let i = 0, l = seq.length; i < l; i += 3) {\n      const b1 = parseInt(seq.slice(i + 1, i + 3), 16)\n\n      if (b1 < 0x80) {\n        result += cache[b1]\n        continue\n      }\n\n      if ((b1 & 0xE0) === 0xC0 && (i + 3 < l)) {\n        // 110xxxxx 10xxxxxx\n        const b2 = parseInt(seq.slice(i + 4, i + 6), 16)\n\n        if ((b2 & 0xC0) === 0x80) {\n          const chr = ((b1 << 6) & 0x7C0) | (b2 & 0x3F)\n\n          if (chr < 0x80) {\n            result += '\\ufffd\\ufffd'\n          } else {\n            result += String.fromCharCode(chr)\n          }\n\n          i += 3\n          continue\n        }\n      }\n\n      if ((b1 & 0xF0) === 0xE0 && (i + 6 < l)) {\n        // 1110xxxx 10xxxxxx 10xxxxxx\n        const b2 = parseInt(seq.slice(i + 4, i + 6), 16)\n        const b3 = parseInt(seq.slice(i + 7, i + 9), 16)\n\n        if ((b2 & 0xC0) === 0x80 && (b3 & 0xC0) === 0x80) {\n          const chr = ((b1 << 12) & 0xF000) | ((b2 << 6) & 0xFC0) | (b3 & 0x3F)\n\n          if (chr < 0x800 || (chr >= 0xD800 && chr <= 0xDFFF)) {\n            result += '\\ufffd\\ufffd\\ufffd'\n          } else {\n            result += String.fromCharCode(chr)\n          }\n\n          i += 6\n          continue\n        }\n      }\n\n      if ((b1 & 0xF8) === 0xF0 && (i + 9 < l)) {\n        // 111110xx 10xxxxxx 10xxxxxx 10xxxxxx\n        const b2 = parseInt(seq.slice(i + 4, i + 6), 16)\n        const b3 = parseInt(seq.slice(i + 7, i + 9), 16)\n        const b4 = parseInt(seq.slice(i + 10, i + 12), 16)\n\n        if ((b2 & 0xC0) === 0x80 && (b3 & 0xC0) === 0x80 && (b4 & 0xC0) === 0x80) {\n          let chr = ((b1 << 18) & 0x1C0000) | ((b2 << 12) & 0x3F000) | ((b3 << 6) & 0xFC0) | (b4 & 0x3F)\n\n          if (chr < 0x10000 || chr > 0x10FFFF) {\n            result += '\\ufffd\\ufffd\\ufffd\\ufffd'\n          } else {\n            chr -= 0x10000\n            result += String.fromCharCode(0xD800 + (chr >> 10), 0xDC00 + (chr & 0x3FF))\n          }\n\n          i += 9\n          continue\n        }\n      }\n\n      result += '\\ufffd'\n    }\n\n    return result\n  })\n}\n\ndecode.defaultChars = ';/?:@&=+$,#'\ndecode.componentChars = ''\n\nexport default decode\n", "const encodeCache = {}\n\n// Create a lookup array where anything but characters in `chars` string\n// and alphanumeric chars is percent-encoded.\n//\nfunction getEncodeCache (exclude) {\n  let cache = encodeCache[exclude]\n  if (cache) { return cache }\n\n  cache = encodeCache[exclude] = []\n\n  for (let i = 0; i < 128; i++) {\n    const ch = String.fromCharCode(i)\n\n    if (/^[0-9a-z]$/i.test(ch)) {\n      // always allow unencoded alphanumeric characters\n      cache.push(ch)\n    } else {\n      cache.push('%' + ('0' + i.toString(16).toUpperCase()).slice(-2))\n    }\n  }\n\n  for (let i = 0; i < exclude.length; i++) {\n    cache[exclude.charCodeAt(i)] = exclude[i]\n  }\n\n  return cache\n}\n\n// Encode unsafe characters with percent-encoding, skipping already\n// encoded sequences.\n//\n//  - string       - string to encode\n//  - exclude      - list of characters to ignore (in addition to a-zA-Z0-9)\n//  - keepEscaped  - don't encode '%' in a correct escape sequence (default: true)\n//\nfunction encode (string, exclude, keepEscaped) {\n  if (typeof exclude !== 'string') {\n    // encode(string, keepEscaped)\n    keepEscaped = exclude\n    exclude = encode.defaultChars\n  }\n\n  if (typeof keepEscaped === 'undefined') {\n    keepEscaped = true\n  }\n\n  const cache = getEncodeCache(exclude)\n  let result = ''\n\n  for (let i = 0, l = string.length; i < l; i++) {\n    const code = string.charCodeAt(i)\n\n    if (keepEscaped && code === 0x25 /* % */ && i + 2 < l) {\n      if (/^[0-9a-f]{2}$/i.test(string.slice(i + 1, i + 3))) {\n        result += string.slice(i, i + 3)\n        i += 2\n        continue\n      }\n    }\n\n    if (code < 128) {\n      result += cache[code]\n      continue\n    }\n\n    if (code >= 0xD800 && code <= 0xDFFF) {\n      if (code >= 0xD800 && code <= 0xDBFF && i + 1 < l) {\n        const nextCode = string.charCodeAt(i + 1)\n        if (nextCode >= 0xDC00 && nextCode <= 0xDFFF) {\n          result += encodeURIComponent(string[i] + string[i + 1])\n          i++\n          continue\n        }\n      }\n      result += '%EF%BF%BD'\n      continue\n    }\n\n    result += encodeURIComponent(string[i])\n  }\n\n  return result\n}\n\nencode.defaultChars = \";/?:@&=+$,-_.!~*'()#\"\nencode.componentChars = \"-_.!~*'()\"\n\nexport default encode\n", "export default function format (url) {\n  let result = ''\n\n  result += url.protocol || ''\n  result += url.slashes ? '//' : ''\n  result += url.auth ? url.auth + '@' : ''\n\n  if (url.hostname && url.hostname.indexOf(':') !== -1) {\n    // ipv6 address\n    result += '[' + url.hostname + ']'\n  } else {\n    result += url.hostname || ''\n  }\n\n  result += url.port ? ':' + url.port : ''\n  result += url.pathname || ''\n  result += url.search || ''\n  result += url.hash || ''\n\n  return result\n};\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n//\n// Changes from joyent/node:\n//\n// 1. No leading slash in paths,\n//    e.g. in `url.parse('http://foo?bar')` pathname is ``, not `/`\n//\n// 2. Backslashes are not replaced with slashes,\n//    so `http:\\\\example.org\\` is treated like a relative path\n//\n// 3. Trailing colon is treated like a part of the path,\n//    i.e. in `http://example.org:foo` pathname is `:foo`\n//\n// 4. Nothing is URL-encoded in the resulting object,\n//    (in joyent/node some chars in auth and paths are encoded)\n//\n// 5. `url.parse()` does not have `parseQueryString` argument\n//\n// 6. Removed extraneous result properties: `host`, `path`, `query`, etc.,\n//    which can be constructed using other parts of the url.\n//\n\nfunction Url () {\n  this.protocol = null\n  this.slashes = null\n  this.auth = null\n  this.port = null\n  this.hostname = null\n  this.hash = null\n  this.search = null\n  this.pathname = null\n}\n\n// Reference: RFC 3986, RFC 1808, RFC 2396\n\n// define these here so at least they only have to be\n// compiled once on the first module load.\nconst protocolPattern = /^([a-z0-9.+-]+:)/i\nconst portPattern = /:[0-9]*$/\n\n// Special case for a simple path URL\n/* eslint-disable-next-line no-useless-escape */\nconst simplePathPattern = /^(\\/\\/?(?!\\/)[^\\?\\s]*)(\\?[^\\s]*)?$/\n\n// RFC 2396: characters reserved for delimiting URLs.\n// We actually just auto-escape these.\nconst delims = ['<', '>', '\"', '`', ' ', '\\r', '\\n', '\\t']\n\n// RFC 2396: characters not allowed for various reasons.\nconst unwise = ['{', '}', '|', '\\\\', '^', '`'].concat(delims)\n\n// Allowed by RFCs, but cause of XSS attacks.  Always escape these.\nconst autoEscape = ['\\''].concat(unwise)\n// Characters that are never ever allowed in a hostname.\n// Note that any invalid chars are also handled, but these\n// are the ones that are *expected* to be seen, so we fast-path\n// them.\nconst nonHostChars = ['%', '/', '?', ';', '#'].concat(autoEscape)\nconst hostEndingChars = ['/', '?', '#']\nconst hostnameMaxLen = 255\nconst hostnamePartPattern = /^[+a-z0-9A-Z_-]{0,63}$/\nconst hostnamePartStart = /^([+a-z0-9A-Z_-]{0,63})(.*)$/\n// protocols that can allow \"unsafe\" and \"unwise\" chars.\n// protocols that never have a hostname.\nconst hostlessProtocol = {\n  javascript: true,\n  'javascript:': true\n}\n// protocols that always contain a // bit.\nconst slashedProtocol = {\n  http: true,\n  https: true,\n  ftp: true,\n  gopher: true,\n  file: true,\n  'http:': true,\n  'https:': true,\n  'ftp:': true,\n  'gopher:': true,\n  'file:': true\n}\n\nfunction urlParse (url, slashesDenoteHost) {\n  if (url && url instanceof Url) return url\n\n  const u = new Url()\n  u.parse(url, slashesDenoteHost)\n  return u\n}\n\nUrl.prototype.parse = function (url, slashesDenoteHost) {\n  let lowerProto, hec, slashes\n  let rest = url\n\n  // trim before proceeding.\n  // This is to support parse stuff like \"  http://foo.com  \\n\"\n  rest = rest.trim()\n\n  if (!slashesDenoteHost && url.split('#').length === 1) {\n    // Try fast path regexp\n    const simplePath = simplePathPattern.exec(rest)\n    if (simplePath) {\n      this.pathname = simplePath[1]\n      if (simplePath[2]) {\n        this.search = simplePath[2]\n      }\n      return this\n    }\n  }\n\n  let proto = protocolPattern.exec(rest)\n  if (proto) {\n    proto = proto[0]\n    lowerProto = proto.toLowerCase()\n    this.protocol = proto\n    rest = rest.substr(proto.length)\n  }\n\n  // figure out if it's got a host\n  // user@server is *always* interpreted as a hostname, and url\n  // resolution will treat //foo/bar as host=foo,path=bar because that's\n  // how the browser resolves relative URLs.\n  /* eslint-disable-next-line no-useless-escape */\n  if (slashesDenoteHost || proto || rest.match(/^\\/\\/[^@\\/]+@[^@\\/]+/)) {\n    slashes = rest.substr(0, 2) === '//'\n    if (slashes && !(proto && hostlessProtocol[proto])) {\n      rest = rest.substr(2)\n      this.slashes = true\n    }\n  }\n\n  if (!hostlessProtocol[proto] &&\n      (slashes || (proto && !slashedProtocol[proto]))) {\n    // there's a hostname.\n    // the first instance of /, ?, ;, or # ends the host.\n    //\n    // If there is an @ in the hostname, then non-host chars *are* allowed\n    // to the left of the last @ sign, unless some host-ending character\n    // comes *before* the @-sign.\n    // URLs are obnoxious.\n    //\n    // ex:\n    // http://a@b@c/ => user:a@b host:c\n    // http://a@b?@c => user:a host:c path:/?@c\n\n    // v0.12 TODO(isaacs): This is not quite how Chrome does things.\n    // Review our test case against browsers more comprehensively.\n\n    // find the first instance of any hostEndingChars\n    let hostEnd = -1\n    for (let i = 0; i < hostEndingChars.length; i++) {\n      hec = rest.indexOf(hostEndingChars[i])\n      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd)) {\n        hostEnd = hec\n      }\n    }\n\n    // at this point, either we have an explicit point where the\n    // auth portion cannot go past, or the last @ char is the decider.\n    let auth, atSign\n    if (hostEnd === -1) {\n      // atSign can be anywhere.\n      atSign = rest.lastIndexOf('@')\n    } else {\n      // atSign must be in auth portion.\n      // http://a@b/c@d => host:b auth:a path:/c@d\n      atSign = rest.lastIndexOf('@', hostEnd)\n    }\n\n    // Now we have a portion which is definitely the auth.\n    // Pull that off.\n    if (atSign !== -1) {\n      auth = rest.slice(0, atSign)\n      rest = rest.slice(atSign + 1)\n      this.auth = auth\n    }\n\n    // the host is the remaining to the left of the first non-host char\n    hostEnd = -1\n    for (let i = 0; i < nonHostChars.length; i++) {\n      hec = rest.indexOf(nonHostChars[i])\n      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd)) {\n        hostEnd = hec\n      }\n    }\n    // if we still have not hit it, then the entire thing is a host.\n    if (hostEnd === -1) {\n      hostEnd = rest.length\n    }\n\n    if (rest[hostEnd - 1] === ':') { hostEnd-- }\n    const host = rest.slice(0, hostEnd)\n    rest = rest.slice(hostEnd)\n\n    // pull out port.\n    this.parseHost(host)\n\n    // we've indicated that there is a hostname,\n    // so even if it's empty, it has to be present.\n    this.hostname = this.hostname || ''\n\n    // if hostname begins with [ and ends with ]\n    // assume that it's an IPv6 address.\n    const ipv6Hostname = this.hostname[0] === '[' &&\n        this.hostname[this.hostname.length - 1] === ']'\n\n    // validate a little.\n    if (!ipv6Hostname) {\n      const hostparts = this.hostname.split(/\\./)\n      for (let i = 0, l = hostparts.length; i < l; i++) {\n        const part = hostparts[i]\n        if (!part) { continue }\n        if (!part.match(hostnamePartPattern)) {\n          let newpart = ''\n          for (let j = 0, k = part.length; j < k; j++) {\n            if (part.charCodeAt(j) > 127) {\n              // we replace non-ASCII char with a temporary placeholder\n              // we need this to make sure size of hostname is not\n              // broken by replacing non-ASCII by nothing\n              newpart += 'x'\n            } else {\n              newpart += part[j]\n            }\n          }\n          // we test again with ASCII char only\n          if (!newpart.match(hostnamePartPattern)) {\n            const validParts = hostparts.slice(0, i)\n            const notHost = hostparts.slice(i + 1)\n            const bit = part.match(hostnamePartStart)\n            if (bit) {\n              validParts.push(bit[1])\n              notHost.unshift(bit[2])\n            }\n            if (notHost.length) {\n              rest = notHost.join('.') + rest\n            }\n            this.hostname = validParts.join('.')\n            break\n          }\n        }\n      }\n    }\n\n    if (this.hostname.length > hostnameMaxLen) {\n      this.hostname = ''\n    }\n\n    // strip [ and ] from the hostname\n    // the host field still retains them, though\n    if (ipv6Hostname) {\n      this.hostname = this.hostname.substr(1, this.hostname.length - 2)\n    }\n  }\n\n  // chop off from the tail first.\n  const hash = rest.indexOf('#')\n  if (hash !== -1) {\n    // got a fragment string.\n    this.hash = rest.substr(hash)\n    rest = rest.slice(0, hash)\n  }\n  const qm = rest.indexOf('?')\n  if (qm !== -1) {\n    this.search = rest.substr(qm)\n    rest = rest.slice(0, qm)\n  }\n  if (rest) { this.pathname = rest }\n  if (slashedProtocol[lowerProto] &&\n      this.hostname && !this.pathname) {\n    this.pathname = ''\n  }\n\n  return this\n}\n\nUrl.prototype.parseHost = function (host) {\n  let port = portPattern.exec(host)\n  if (port) {\n    port = port[0]\n    if (port !== ':') {\n      this.port = port.substr(1)\n    }\n    host = host.substr(0, host.length - port.length)\n  }\n  if (host) { this.hostname = host }\n}\n\nexport default urlParse\n", "import Any from './properties/Any/regex.mjs';\nimport Cc  from './categories/Cc/regex.mjs';\nimport Cf  from './categories/Cf/regex.mjs';\nimport P   from './categories/P/regex.mjs';\nimport S   from './categories/S/regex.mjs';\nimport Z   from './categories/Z/regex.mjs';\n\nexport { Any, Cc, Cf, P, S, Z };\n", "export default /[\\0-\\uD7FF\\uE000-\\uFFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]/", "export default /[\\0-\\x1F\\x7F-\\x9F]/", "export default /[\\xAD\\u0600-\\u0605\\u061C\\u06DD\\u070F\\u0890\\u0891\\u08E2\\u180E\\u200B-\\u200F\\u202A-\\u202E\\u2060-\\u2064\\u2066-\\u206F\\uFEFF\\uFFF9-\\uFFFB]|\\uD804[\\uDCBD\\uDCCD]|\\uD80D[\\uDC30-\\uDC3F]|\\uD82F[\\uDCA0-\\uDCA3]|\\uD834[\\uDD73-\\uDD7A]|\\uDB40[\\uDC01\\uDC20-\\uDC7F]/", "export default /[!-#%-\\*,-\\/:;\\?@\\[-\\]_\\{\\}\\xA1\\xA7\\xAB\\xB6\\xB7\\xBB\\xBF\\u037E\\u0387\\u055A-\\u055F\\u0589\\u058A\\u05BE\\u05C0\\u05C3\\u05C6\\u05F3\\u05F4\\u0609\\u060A\\u060C\\u060D\\u061B\\u061D-\\u061F\\u066A-\\u066D\\u06D4\\u0700-\\u070D\\u07F7-\\u07F9\\u0830-\\u083E\\u085E\\u0964\\u0965\\u0970\\u09FD\\u0A76\\u0AF0\\u0C77\\u0C84\\u0DF4\\u0E4F\\u0E5A\\u0E5B\\u0F04-\\u0F12\\u0F14\\u0F3A-\\u0F3D\\u0F85\\u0FD0-\\u0FD4\\u0FD9\\u0FDA\\u104A-\\u104F\\u10FB\\u1360-\\u1368\\u1400\\u166E\\u169B\\u169C\\u16EB-\\u16ED\\u1735\\u1736\\u17D4-\\u17D6\\u17D8-\\u17DA\\u1800-\\u180A\\u1944\\u1945\\u1A1E\\u1A1F\\u1AA0-\\u1AA6\\u1AA8-\\u1AAD\\u1B5A-\\u1B60\\u1B7D\\u1B7E\\u1BFC-\\u1BFF\\u1C3B-\\u1C3F\\u1C7E\\u1C7F\\u1CC0-\\u1CC7\\u1CD3\\u2010-\\u2027\\u2030-\\u2043\\u2045-\\u2051\\u2053-\\u205E\\u207D\\u207E\\u208D\\u208E\\u2308-\\u230B\\u2329\\u232A\\u2768-\\u2775\\u27C5\\u27C6\\u27E6-\\u27EF\\u2983-\\u2998\\u29D8-\\u29DB\\u29FC\\u29FD\\u2CF9-\\u2CFC\\u2CFE\\u2CFF\\u2D70\\u2E00-\\u2E2E\\u2E30-\\u2E4F\\u2E52-\\u2E5D\\u3001-\\u3003\\u3008-\\u3011\\u3014-\\u301F\\u3030\\u303D\\u30A0\\u30FB\\uA4FE\\uA4FF\\uA60D-\\uA60F\\uA673\\uA67E\\uA6F2-\\uA6F7\\uA874-\\uA877\\uA8CE\\uA8CF\\uA8F8-\\uA8FA\\uA8FC\\uA92E\\uA92F\\uA95F\\uA9C1-\\uA9CD\\uA9DE\\uA9DF\\uAA5C-\\uAA5F\\uAADE\\uAADF\\uAAF0\\uAAF1\\uABEB\\uFD3E\\uFD3F\\uFE10-\\uFE19\\uFE30-\\uFE52\\uFE54-\\uFE61\\uFE63\\uFE68\\uFE6A\\uFE6B\\uFF01-\\uFF03\\uFF05-\\uFF0A\\uFF0C-\\uFF0F\\uFF1A\\uFF1B\\uFF1F\\uFF20\\uFF3B-\\uFF3D\\uFF3F\\uFF5B\\uFF5D\\uFF5F-\\uFF65]|\\uD800[\\uDD00-\\uDD02\\uDF9F\\uDFD0]|\\uD801\\uDD6F|\\uD802[\\uDC57\\uDD1F\\uDD3F\\uDE50-\\uDE58\\uDE7F\\uDEF0-\\uDEF6\\uDF39-\\uDF3F\\uDF99-\\uDF9C]|\\uD803[\\uDEAD\\uDF55-\\uDF59\\uDF86-\\uDF89]|\\uD804[\\uDC47-\\uDC4D\\uDCBB\\uDCBC\\uDCBE-\\uDCC1\\uDD40-\\uDD43\\uDD74\\uDD75\\uDDC5-\\uDDC8\\uDDCD\\uDDDB\\uDDDD-\\uDDDF\\uDE38-\\uDE3D\\uDEA9]|\\uD805[\\uDC4B-\\uDC4F\\uDC5A\\uDC5B\\uDC5D\\uDCC6\\uDDC1-\\uDDD7\\uDE41-\\uDE43\\uDE60-\\uDE6C\\uDEB9\\uDF3C-\\uDF3E]|\\uD806[\\uDC3B\\uDD44-\\uDD46\\uDDE2\\uDE3F-\\uDE46\\uDE9A-\\uDE9C\\uDE9E-\\uDEA2\\uDF00-\\uDF09]|\\uD807[\\uDC41-\\uDC45\\uDC70\\uDC71\\uDEF7\\uDEF8\\uDF43-\\uDF4F\\uDFFF]|\\uD809[\\uDC70-\\uDC74]|\\uD80B[\\uDFF1\\uDFF2]|\\uD81A[\\uDE6E\\uDE6F\\uDEF5\\uDF37-\\uDF3B\\uDF44]|\\uD81B[\\uDE97-\\uDE9A\\uDFE2]|\\uD82F\\uDC9F|\\uD836[\\uDE87-\\uDE8B]|\\uD83A[\\uDD5E\\uDD5F]/", "export default /[\\$\\+<->\\^`\\|~\\xA2-\\xA6\\xA8\\xA9\\xAC\\xAE-\\xB1\\xB4\\xB8\\xD7\\xF7\\u02C2-\\u02C5\\u02D2-\\u02DF\\u02E5-\\u02EB\\u02ED\\u02EF-\\u02FF\\u0375\\u0384\\u0385\\u03F6\\u0482\\u058D-\\u058F\\u0606-\\u0608\\u060B\\u060E\\u060F\\u06DE\\u06E9\\u06FD\\u06FE\\u07F6\\u07FE\\u07FF\\u0888\\u09F2\\u09F3\\u09FA\\u09FB\\u0AF1\\u0B70\\u0BF3-\\u0BFA\\u0C7F\\u0D4F\\u0D79\\u0E3F\\u0F01-\\u0F03\\u0F13\\u0F15-\\u0F17\\u0F1A-\\u0F1F\\u0F34\\u0F36\\u0F38\\u0FBE-\\u0FC5\\u0FC7-\\u0FCC\\u0FCE\\u0FCF\\u0FD5-\\u0FD8\\u109E\\u109F\\u1390-\\u1399\\u166D\\u17DB\\u1940\\u19DE-\\u19FF\\u1B61-\\u1B6A\\u1B74-\\u1B7C\\u1FBD\\u1FBF-\\u1FC1\\u1FCD-\\u1FCF\\u1FDD-\\u1FDF\\u1FED-\\u1FEF\\u1FFD\\u1FFE\\u2044\\u2052\\u207A-\\u207C\\u208A-\\u208C\\u20A0-\\u20C0\\u2100\\u2101\\u2103-\\u2106\\u2108\\u2109\\u2114\\u2116-\\u2118\\u211E-\\u2123\\u2125\\u2127\\u2129\\u212E\\u213A\\u213B\\u2140-\\u2144\\u214A-\\u214D\\u214F\\u218A\\u218B\\u2190-\\u2307\\u230C-\\u2328\\u232B-\\u2426\\u2440-\\u244A\\u249C-\\u24E9\\u2500-\\u2767\\u2794-\\u27C4\\u27C7-\\u27E5\\u27F0-\\u2982\\u2999-\\u29D7\\u29DC-\\u29FB\\u29FE-\\u2B73\\u2B76-\\u2B95\\u2B97-\\u2BFF\\u2CE5-\\u2CEA\\u2E50\\u2E51\\u2E80-\\u2E99\\u2E9B-\\u2EF3\\u2F00-\\u2FD5\\u2FF0-\\u2FFF\\u3004\\u3012\\u3013\\u3020\\u3036\\u3037\\u303E\\u303F\\u309B\\u309C\\u3190\\u3191\\u3196-\\u319F\\u31C0-\\u31E3\\u31EF\\u3200-\\u321E\\u322A-\\u3247\\u3250\\u3260-\\u327F\\u328A-\\u32B0\\u32C0-\\u33FF\\u4DC0-\\u4DFF\\uA490-\\uA4C6\\uA700-\\uA716\\uA720\\uA721\\uA789\\uA78A\\uA828-\\uA82B\\uA836-\\uA839\\uAA77-\\uAA79\\uAB5B\\uAB6A\\uAB6B\\uFB29\\uFBB2-\\uFBC2\\uFD40-\\uFD4F\\uFDCF\\uFDFC-\\uFDFF\\uFE62\\uFE64-\\uFE66\\uFE69\\uFF04\\uFF0B\\uFF1C-\\uFF1E\\uFF3E\\uFF40\\uFF5C\\uFF5E\\uFFE0-\\uFFE6\\uFFE8-\\uFFEE\\uFFFC\\uFFFD]|\\uD800[\\uDD37-\\uDD3F\\uDD79-\\uDD89\\uDD8C-\\uDD8E\\uDD90-\\uDD9C\\uDDA0\\uDDD0-\\uDDFC]|\\uD802[\\uDC77\\uDC78\\uDEC8]|\\uD805\\uDF3F|\\uD807[\\uDFD5-\\uDFF1]|\\uD81A[\\uDF3C-\\uDF3F\\uDF45]|\\uD82F\\uDC9C|\\uD833[\\uDF50-\\uDFC3]|\\uD834[\\uDC00-\\uDCF5\\uDD00-\\uDD26\\uDD29-\\uDD64\\uDD6A-\\uDD6C\\uDD83\\uDD84\\uDD8C-\\uDDA9\\uDDAE-\\uDDEA\\uDE00-\\uDE41\\uDE45\\uDF00-\\uDF56]|\\uD835[\\uDEC1\\uDEDB\\uDEFB\\uDF15\\uDF35\\uDF4F\\uDF6F\\uDF89\\uDFA9\\uDFC3]|\\uD836[\\uDC00-\\uDDFF\\uDE37-\\uDE3A\\uDE6D-\\uDE74\\uDE76-\\uDE83\\uDE85\\uDE86]|\\uD838[\\uDD4F\\uDEFF]|\\uD83B[\\uDCAC\\uDCB0\\uDD2E\\uDEF0\\uDEF1]|\\uD83C[\\uDC00-\\uDC2B\\uDC30-\\uDC93\\uDCA0-\\uDCAE\\uDCB1-\\uDCBF\\uDCC1-\\uDCCF\\uDCD1-\\uDCF5\\uDD0D-\\uDDAD\\uDDE6-\\uDE02\\uDE10-\\uDE3B\\uDE40-\\uDE48\\uDE50\\uDE51\\uDE60-\\uDE65\\uDF00-\\uDFFF]|\\uD83D[\\uDC00-\\uDED7\\uDEDC-\\uDEEC\\uDEF0-\\uDEFC\\uDF00-\\uDF76\\uDF7B-\\uDFD9\\uDFE0-\\uDFEB\\uDFF0]|\\uD83E[\\uDC00-\\uDC0B\\uDC10-\\uDC47\\uDC50-\\uDC59\\uDC60-\\uDC87\\uDC90-\\uDCAD\\uDCB0\\uDCB1\\uDD00-\\uDE53\\uDE60-\\uDE6D\\uDE70-\\uDE7C\\uDE80-\\uDE88\\uDE90-\\uDEBD\\uDEBF-\\uDEC5\\uDECE-\\uDEDB\\uDEE0-\\uDEE8\\uDEF0-\\uDEF8\\uDF00-\\uDF92\\uDF94-\\uDFCA]/", "export default /[ \\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000]/", null, null, null, null, null, null, null, "// Just a shortcut for bulk export\n\nimport parseLinkLabel from './parse_link_label.mjs'\nimport parseLinkDestination from './parse_link_destination.mjs'\nimport parseLinkTitle from './parse_link_title.mjs'\n\nexport {\n  parseLinkLabel,\n  parseLinkDestination,\n  parseLinkTitle\n}\n", "// Parse link label\n//\n// this function assumes that first character (\"[\") already matches;\n// returns the end of the label\n//\n\nexport default function parseLinkLabel (state, start, disableNested) {\n  let level, found, marker, prevPos\n\n  const max = state.posMax\n  const oldPos = state.pos\n\n  state.pos = start + 1\n  level = 1\n\n  while (state.pos < max) {\n    marker = state.src.charCodeAt(state.pos)\n    if (marker === 0x5D /* ] */) {\n      level--\n      if (level === 0) {\n        found = true\n        break\n      }\n    }\n\n    prevPos = state.pos\n    state.md.inline.skipToken(state)\n    if (marker === 0x5B /* [ */) {\n      if (prevPos === state.pos - 1) {\n        // increase level if we find text `[`, which is not a part of any token\n        level++\n      } else if (disableNested) {\n        state.pos = oldPos\n        return -1\n      }\n    }\n  }\n\n  let labelEnd = -1\n\n  if (found) {\n    labelEnd = state.pos\n  }\n\n  // restore old state\n  state.pos = oldPos\n\n  return labelEnd\n}\n", "// Parse link destination\n//\n\nimport { unescapeAll } from '../common/utils.mjs'\n\nexport default function parseLinkDestination (str, start, max) {\n  let code\n  let pos = start\n\n  const result = {\n    ok: false,\n    pos: 0,\n    str: ''\n  }\n\n  if (str.charCodeAt(pos) === 0x3C /* < */) {\n    pos++\n    while (pos < max) {\n      code = str.charCodeAt(pos)\n      if (code === 0x0A /* \\n */) { return result }\n      if (code === 0x3C /* < */) { return result }\n      if (code === 0x3E /* > */) {\n        result.pos = pos + 1\n        result.str = unescapeAll(str.slice(start + 1, pos))\n        result.ok = true\n        return result\n      }\n      if (code === 0x5C /* \\ */ && pos + 1 < max) {\n        pos += 2\n        continue\n      }\n\n      pos++\n    }\n\n    // no closing '>'\n    return result\n  }\n\n  // this should be ... } else { ... branch\n\n  let level = 0\n  while (pos < max) {\n    code = str.charCodeAt(pos)\n\n    if (code === 0x20) { break }\n\n    // ascii control characters\n    if (code < 0x20 || code === 0x7F) { break }\n\n    if (code === 0x5C /* \\ */ && pos + 1 < max) {\n      if (str.charCodeAt(pos + 1) === 0x20) { break }\n      pos += 2\n      continue\n    }\n\n    if (code === 0x28 /* ( */) {\n      level++\n      if (level > 32) { return result }\n    }\n\n    if (code === 0x29 /* ) */) {\n      if (level === 0) { break }\n      level--\n    }\n\n    pos++\n  }\n\n  if (start === pos) { return result }\n  if (level !== 0) { return result }\n\n  result.str = unescapeAll(str.slice(start, pos))\n  result.pos = pos\n  result.ok = true\n  return result\n}\n", "// Parse link title\n//\n\nimport { unescapeAll } from '../common/utils.mjs'\n\n// Parse link title within `str` in [start, max] range,\n// or continue previous parsing if `prev_state` is defined (equal to result of last execution).\n//\nexport default function parseLinkTitle (str, start, max, prev_state) {\n  let code\n  let pos = start\n\n  const state = {\n    // if `true`, this is a valid link title\n    ok: false,\n    // if `true`, this link can be continued on the next line\n    can_continue: false,\n    // if `ok`, it's the position of the first character after the closing marker\n    pos: 0,\n    // if `ok`, it's the unescaped title\n    str: '',\n    // expected closing marker character code\n    marker: 0\n  }\n\n  if (prev_state) {\n    // this is a continuation of a previous parseLinkTitle call on the next line,\n    // used in reference links only\n    state.str = prev_state.str\n    state.marker = prev_state.marker\n  } else {\n    if (pos >= max) { return state }\n\n    let marker = str.charCodeAt(pos)\n    if (marker !== 0x22 /* \" */ && marker !== 0x27 /* ' */ && marker !== 0x28 /* ( */) { return state }\n\n    start++\n    pos++\n\n    // if opening marker is \"(\", switch it to closing marker \")\"\n    if (marker === 0x28) { marker = 0x29 }\n\n    state.marker = marker\n  }\n\n  while (pos < max) {\n    code = str.charCodeAt(pos)\n    if (code === state.marker) {\n      state.pos = pos + 1\n      state.str += unescapeAll(str.slice(start, pos))\n      state.ok = true\n      return state\n    } else if (code === 0x28 /* ( */ && state.marker === 0x29 /* ) */) {\n      return state\n    } else if (code === 0x5C /* \\ */ && pos + 1 < max) {\n      pos++\n    }\n\n    pos++\n  }\n\n  // no closing marker found, but this link title may continue on the next line (for references)\n  state.can_continue = true\n  state.str += unescapeAll(str.slice(start, pos))\n  return state\n}\n", "/**\n * class Renderer\n *\n * Generates HTML from parsed token stream. Each instance has independent\n * copy of rules. Those can be rewritten with ease. Also, you can add new\n * rules if you create plugin and adds new token types.\n **/\n\nimport { assign, unescapeAll, escapeHtml } from './common/utils.mjs'\n\nconst default_rules = {}\n\ndefault_rules.code_inline = function (tokens, idx, options, env, slf) {\n  const token = tokens[idx]\n\n  return  '<code' + slf.renderAttrs(token) + '>' +\n          escapeHtml(token.content) +\n          '</code>'\n}\n\ndefault_rules.code_block = function (tokens, idx, options, env, slf) {\n  const token = tokens[idx]\n\n  return  '<pre' + slf.renderAttrs(token) + '><code>' +\n          escapeHtml(tokens[idx].content) +\n          '</code></pre>\\n'\n}\n\ndefault_rules.fence = function (tokens, idx, options, env, slf) {\n  const token = tokens[idx]\n  const info = token.info ? unescapeAll(token.info).trim() : ''\n  let langName = ''\n  let langAttrs = ''\n\n  if (info) {\n    const arr = info.split(/(\\s+)/g)\n    langName = arr[0]\n    langAttrs = arr.slice(2).join('')\n  }\n\n  let highlighted\n  if (options.highlight) {\n    highlighted = options.highlight(token.content, langName, langAttrs) || escapeHtml(token.content)\n  } else {\n    highlighted = escapeHtml(token.content)\n  }\n\n  if (highlighted.indexOf('<pre') === 0) {\n    return highlighted + '\\n'\n  }\n\n  // If language exists, inject class gently, without modifying original token.\n  // May be, one day we will add .deepClone() for token and simplify this part, but\n  // now we prefer to keep things local.\n  if (info) {\n    const i = token.attrIndex('class')\n    const tmpAttrs = token.attrs ? token.attrs.slice() : []\n\n    if (i < 0) {\n      tmpAttrs.push(['class', options.langPrefix + langName])\n    } else {\n      tmpAttrs[i] = tmpAttrs[i].slice()\n      tmpAttrs[i][1] += ' ' + options.langPrefix + langName\n    }\n\n    // Fake token just to render attributes\n    const tmpToken = {\n      attrs: tmpAttrs\n    }\n\n    return `<pre><code${slf.renderAttrs(tmpToken)}>${highlighted}</code></pre>\\n`\n  }\n\n  return `<pre><code${slf.renderAttrs(token)}>${highlighted}</code></pre>\\n`\n}\n\ndefault_rules.image = function (tokens, idx, options, env, slf) {\n  const token = tokens[idx]\n\n  // \"alt\" attr MUST be set, even if empty. Because it's mandatory and\n  // should be placed on proper position for tests.\n  //\n  // Replace content with actual value\n\n  token.attrs[token.attrIndex('alt')][1] =\n    slf.renderInlineAsText(token.children, options, env)\n\n  return slf.renderToken(tokens, idx, options)\n}\n\ndefault_rules.hardbreak = function (tokens, idx, options /*, env */) {\n  return options.xhtmlOut ? '<br />\\n' : '<br>\\n'\n}\ndefault_rules.softbreak = function (tokens, idx, options /*, env */) {\n  return options.breaks ? (options.xhtmlOut ? '<br />\\n' : '<br>\\n') : '\\n'\n}\n\ndefault_rules.text = function (tokens, idx /*, options, env */) {\n  return escapeHtml(tokens[idx].content)\n}\n\ndefault_rules.html_block = function (tokens, idx /*, options, env */) {\n  return tokens[idx].content\n}\ndefault_rules.html_inline = function (tokens, idx /*, options, env */) {\n  return tokens[idx].content\n}\n\n/**\n * new Renderer()\n *\n * Creates new [[Renderer]] instance and fill [[Renderer#rules]] with defaults.\n **/\nfunction Renderer () {\n  /**\n   * Renderer#rules -> Object\n   *\n   * Contains render rules for tokens. Can be updated and extended.\n   *\n   * ##### Example\n   *\n   * ```javascript\n   * var md = require('markdown-it')();\n   *\n   * md.renderer.rules.strong_open  = function () { return '<b>'; };\n   * md.renderer.rules.strong_close = function () { return '</b>'; };\n   *\n   * var result = md.renderInline(...);\n   * ```\n   *\n   * Each rule is called as independent static function with fixed signature:\n   *\n   * ```javascript\n   * function my_token_render(tokens, idx, options, env, renderer) {\n   *   // ...\n   *   return renderedHTML;\n   * }\n   * ```\n   *\n   * See [source code](https://github.com/markdown-it/markdown-it/blob/master/lib/renderer.mjs)\n   * for more details and examples.\n   **/\n  this.rules = assign({}, default_rules)\n}\n\n/**\n * Renderer.renderAttrs(token) -> String\n *\n * Render token attributes to string.\n **/\nRenderer.prototype.renderAttrs = function renderAttrs (token) {\n  let i, l, result\n\n  if (!token.attrs) { return '' }\n\n  result = ''\n\n  for (i = 0, l = token.attrs.length; i < l; i++) {\n    result += ' ' + escapeHtml(token.attrs[i][0]) + '=\"' + escapeHtml(token.attrs[i][1]) + '\"'\n  }\n\n  return result\n}\n\n/**\n * Renderer.renderToken(tokens, idx, options) -> String\n * - tokens (Array): list of tokens\n * - idx (Numbed): token index to render\n * - options (Object): params of parser instance\n *\n * Default token renderer. Can be overriden by custom function\n * in [[Renderer#rules]].\n **/\nRenderer.prototype.renderToken = function renderToken (tokens, idx, options) {\n  const token = tokens[idx]\n  let result = ''\n\n  // Tight list paragraphs\n  if (token.hidden) {\n    return ''\n  }\n\n  // Insert a newline between hidden paragraph and subsequent opening\n  // block-level tag.\n  //\n  // For example, here we should insert a newline before blockquote:\n  //  - a\n  //    >\n  //\n  if (token.block && token.nesting !== -1 && idx && tokens[idx - 1].hidden) {\n    result += '\\n'\n  }\n\n  // Add token name, e.g. `<img`\n  result += (token.nesting === -1 ? '</' : '<') + token.tag\n\n  // Encode attributes, e.g. `<img src=\"foo\"`\n  result += this.renderAttrs(token)\n\n  // Add a slash for self-closing tags, e.g. `<img src=\"foo\" /`\n  if (token.nesting === 0 && options.xhtmlOut) {\n    result += ' /'\n  }\n\n  // Check if we need to add a newline after this tag\n  let needLf = false\n  if (token.block) {\n    needLf = true\n\n    if (token.nesting === 1) {\n      if (idx + 1 < tokens.length) {\n        const nextToken = tokens[idx + 1]\n\n        if (nextToken.type === 'inline' || nextToken.hidden) {\n          // Block-level tag containing an inline tag.\n          //\n          needLf = false\n        } else if (nextToken.nesting === -1 && nextToken.tag === token.tag) {\n          // Opening tag + closing tag of the same type. E.g. `<li></li>`.\n          //\n          needLf = false\n        }\n      }\n    }\n  }\n\n  result += needLf ? '>\\n' : '>'\n\n  return result\n}\n\n/**\n * Renderer.renderInline(tokens, options, env) -> String\n * - tokens (Array): list on block tokens to render\n * - options (Object): params of parser instance\n * - env (Object): additional data from parsed input (references, for example)\n *\n * The same as [[Renderer.render]], but for single token of `inline` type.\n **/\nRenderer.prototype.renderInline = function (tokens, options, env) {\n  let result = ''\n  const rules = this.rules\n\n  for (let i = 0, len = tokens.length; i < len; i++) {\n    const type = tokens[i].type\n\n    if (typeof rules[type] !== 'undefined') {\n      result += rules[type](tokens, i, options, env, this)\n    } else {\n      result += this.renderToken(tokens, i, options)\n    }\n  }\n\n  return result\n}\n\n/** internal\n * Renderer.renderInlineAsText(tokens, options, env) -> String\n * - tokens (Array): list on block tokens to render\n * - options (Object): params of parser instance\n * - env (Object): additional data from parsed input (references, for example)\n *\n * Special kludge for image `alt` attributes to conform CommonMark spec.\n * Don't try to use it! Spec requires to show `alt` content with stripped markup,\n * instead of simple escaping.\n **/\nRenderer.prototype.renderInlineAsText = function (tokens, options, env) {\n  let result = ''\n\n  for (let i = 0, len = tokens.length; i < len; i++) {\n    switch (tokens[i].type) {\n      case 'text':\n        result += tokens[i].content\n        break\n      case 'image':\n        result += this.renderInlineAsText(tokens[i].children, options, env)\n        break\n      case 'html_inline':\n      case 'html_block':\n        result += tokens[i].content\n        break\n      case 'softbreak':\n      case 'hardbreak':\n        result += '\\n'\n        break\n      default:\n        // all other tokens are skipped\n    }\n  }\n\n  return result\n}\n\n/**\n * Renderer.render(tokens, options, env) -> String\n * - tokens (Array): list on block tokens to render\n * - options (Object): params of parser instance\n * - env (Object): additional data from parsed input (references, for example)\n *\n * Takes token stream and generates HTML. Probably, you will never need to call\n * this method directly.\n **/\nRenderer.prototype.render = function (tokens, options, env) {\n  let result = ''\n  const rules = this.rules\n\n  for (let i = 0, len = tokens.length; i < len; i++) {\n    const type = tokens[i].type\n\n    if (type === 'inline') {\n      result += this.renderInline(tokens[i].children, options, env)\n    } else if (typeof rules[type] !== 'undefined') {\n      result += rules[type](tokens, i, options, env, this)\n    } else {\n      result += this.renderToken(tokens, i, options, env)\n    }\n  }\n\n  return result\n}\n\nexport default Renderer\n", "/**\n * class Ruler\n *\n * Helper class, used by [[MarkdownIt#core]], [[MarkdownIt#block]] and\n * [[MarkdownIt#inline]] to manage sequences of functions (rules):\n *\n * - keep rules in defined order\n * - assign the name to each rule\n * - enable/disable rules\n * - add/replace rules\n * - allow assign rules to additional named chains (in the same)\n * - cacheing lists of active rules\n *\n * You will not need use this class directly until write plugins. For simple\n * rules control use [[MarkdownIt.disable]], [[MarkdownIt.enable]] and\n * [[MarkdownIt.use]].\n **/\n\n/**\n * new Ruler()\n **/\nfunction Ruler () {\n  // List of added rules. Each element is:\n  //\n  // {\n  //   name: XXX,\n  //   enabled: Boolean,\n  //   fn: Function(),\n  //   alt: [ name2, name3 ]\n  // }\n  //\n  this.__rules__ = []\n\n  // Cached rule chains.\n  //\n  // First level - chain name, '' for default.\n  // Second level - diginal anchor for fast filtering by charcodes.\n  //\n  this.__cache__ = null\n}\n\n// Helper methods, should not be used directly\n\n// Find rule index by name\n//\nRuler.prototype.__find__ = function (name) {\n  for (let i = 0; i < this.__rules__.length; i++) {\n    if (this.__rules__[i].name === name) {\n      return i\n    }\n  }\n  return -1\n}\n\n// Build rules lookup cache\n//\nRuler.prototype.__compile__ = function () {\n  const self = this\n  const chains = ['']\n\n  // collect unique names\n  self.__rules__.forEach(function (rule) {\n    if (!rule.enabled) { return }\n\n    rule.alt.forEach(function (altName) {\n      if (chains.indexOf(altName) < 0) {\n        chains.push(altName)\n      }\n    })\n  })\n\n  self.__cache__ = {}\n\n  chains.forEach(function (chain) {\n    self.__cache__[chain] = []\n    self.__rules__.forEach(function (rule) {\n      if (!rule.enabled) { return }\n\n      if (chain && rule.alt.indexOf(chain) < 0) { return }\n\n      self.__cache__[chain].push(rule.fn)\n    })\n  })\n}\n\n/**\n * Ruler.at(name, fn [, options])\n * - name (String): rule name to replace.\n * - fn (Function): new rule function.\n * - options (Object): new rule options (not mandatory).\n *\n * Replace rule by name with new function & options. Throws error if name not\n * found.\n *\n * ##### Options:\n *\n * - __alt__ - array with names of \"alternate\" chains.\n *\n * ##### Example\n *\n * Replace existing typographer replacement rule with new one:\n *\n * ```javascript\n * var md = require('markdown-it')();\n *\n * md.core.ruler.at('replacements', function replace(state) {\n *   //...\n * });\n * ```\n **/\nRuler.prototype.at = function (name, fn, options) {\n  const index = this.__find__(name)\n  const opt = options || {}\n\n  if (index === -1) { throw new Error('Parser rule not found: ' + name) }\n\n  this.__rules__[index].fn = fn\n  this.__rules__[index].alt = opt.alt || []\n  this.__cache__ = null\n}\n\n/**\n * Ruler.before(beforeName, ruleName, fn [, options])\n * - beforeName (String): new rule will be added before this one.\n * - ruleName (String): name of added rule.\n * - fn (Function): rule function.\n * - options (Object): rule options (not mandatory).\n *\n * Add new rule to chain before one with given name. See also\n * [[Ruler.after]], [[Ruler.push]].\n *\n * ##### Options:\n *\n * - __alt__ - array with names of \"alternate\" chains.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')();\n *\n * md.block.ruler.before('paragraph', 'my_rule', function replace(state) {\n *   //...\n * });\n * ```\n **/\nRuler.prototype.before = function (beforeName, ruleName, fn, options) {\n  const index = this.__find__(beforeName)\n  const opt = options || {}\n\n  if (index === -1) { throw new Error('Parser rule not found: ' + beforeName) }\n\n  this.__rules__.splice(index, 0, {\n    name: ruleName,\n    enabled: true,\n    fn,\n    alt: opt.alt || []\n  })\n\n  this.__cache__ = null\n}\n\n/**\n * Ruler.after(afterName, ruleName, fn [, options])\n * - afterName (String): new rule will be added after this one.\n * - ruleName (String): name of added rule.\n * - fn (Function): rule function.\n * - options (Object): rule options (not mandatory).\n *\n * Add new rule to chain after one with given name. See also\n * [[Ruler.before]], [[Ruler.push]].\n *\n * ##### Options:\n *\n * - __alt__ - array with names of \"alternate\" chains.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')();\n *\n * md.inline.ruler.after('text', 'my_rule', function replace(state) {\n *   //...\n * });\n * ```\n **/\nRuler.prototype.after = function (afterName, ruleName, fn, options) {\n  const index = this.__find__(afterName)\n  const opt = options || {}\n\n  if (index === -1) { throw new Error('Parser rule not found: ' + afterName) }\n\n  this.__rules__.splice(index + 1, 0, {\n    name: ruleName,\n    enabled: true,\n    fn,\n    alt: opt.alt || []\n  })\n\n  this.__cache__ = null\n}\n\n/**\n * Ruler.push(ruleName, fn [, options])\n * - ruleName (String): name of added rule.\n * - fn (Function): rule function.\n * - options (Object): rule options (not mandatory).\n *\n * Push new rule to the end of chain. See also\n * [[Ruler.before]], [[Ruler.after]].\n *\n * ##### Options:\n *\n * - __alt__ - array with names of \"alternate\" chains.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')();\n *\n * md.core.ruler.push('my_rule', function replace(state) {\n *   //...\n * });\n * ```\n **/\nRuler.prototype.push = function (ruleName, fn, options) {\n  const opt = options || {}\n\n  this.__rules__.push({\n    name: ruleName,\n    enabled: true,\n    fn,\n    alt: opt.alt || []\n  })\n\n  this.__cache__ = null\n}\n\n/**\n * Ruler.enable(list [, ignoreInvalid]) -> Array\n * - list (String|Array): list of rule names to enable.\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * Enable rules with given names. If any rule name not found - throw Error.\n * Errors can be disabled by second param.\n *\n * Returns list of found rule names (if no exception happened).\n *\n * See also [[Ruler.disable]], [[Ruler.enableOnly]].\n **/\nRuler.prototype.enable = function (list, ignoreInvalid) {\n  if (!Array.isArray(list)) { list = [list] }\n\n  const result = []\n\n  // Search by name and enable\n  list.forEach(function (name) {\n    const idx = this.__find__(name)\n\n    if (idx < 0) {\n      if (ignoreInvalid) { return }\n      throw new Error('Rules manager: invalid rule name ' + name)\n    }\n    this.__rules__[idx].enabled = true\n    result.push(name)\n  }, this)\n\n  this.__cache__ = null\n  return result\n}\n\n/**\n * Ruler.enableOnly(list [, ignoreInvalid])\n * - list (String|Array): list of rule names to enable (whitelist).\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * Enable rules with given names, and disable everything else. If any rule name\n * not found - throw Error. Errors can be disabled by second param.\n *\n * See also [[Ruler.disable]], [[Ruler.enable]].\n **/\nRuler.prototype.enableOnly = function (list, ignoreInvalid) {\n  if (!Array.isArray(list)) { list = [list] }\n\n  this.__rules__.forEach(function (rule) { rule.enabled = false })\n\n  this.enable(list, ignoreInvalid)\n}\n\n/**\n * Ruler.disable(list [, ignoreInvalid]) -> Array\n * - list (String|Array): list of rule names to disable.\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * Disable rules with given names. If any rule name not found - throw Error.\n * Errors can be disabled by second param.\n *\n * Returns list of found rule names (if no exception happened).\n *\n * See also [[Ruler.enable]], [[Ruler.enableOnly]].\n **/\nRuler.prototype.disable = function (list, ignoreInvalid) {\n  if (!Array.isArray(list)) { list = [list] }\n\n  const result = []\n\n  // Search by name and disable\n  list.forEach(function (name) {\n    const idx = this.__find__(name)\n\n    if (idx < 0) {\n      if (ignoreInvalid) { return }\n      throw new Error('Rules manager: invalid rule name ' + name)\n    }\n    this.__rules__[idx].enabled = false\n    result.push(name)\n  }, this)\n\n  this.__cache__ = null\n  return result\n}\n\n/**\n * Ruler.getRules(chainName) -> Array\n *\n * Return array of active functions (rules) for given chain name. It analyzes\n * rules configuration, compiles caches if not exists and returns result.\n *\n * Default chain name is `''` (empty string). It can't be skipped. That's\n * done intentionally, to keep signature monomorphic for high speed.\n **/\nRuler.prototype.getRules = function (chainName) {\n  if (this.__cache__ === null) {\n    this.__compile__()\n  }\n\n  // Chain can be empty, if rules disabled. But we still have to return Array.\n  return this.__cache__[chainName] || []\n}\n\nexport default Ruler\n", "// Token class\n\n/**\n * class Token\n **/\n\n/**\n * new Token(type, tag, nesting)\n *\n * Create new token and fill passed properties.\n **/\nfunction Token (type, tag, nesting) {\n  /**\n   * Token#type -> String\n   *\n   * Type of the token (string, e.g. \"paragraph_open\")\n   **/\n  this.type     = type\n\n  /**\n   * Token#tag -> String\n   *\n   * html tag name, e.g. \"p\"\n   **/\n  this.tag      = tag\n\n  /**\n   * Token#attrs -> Array\n   *\n   * Html attributes. Format: `[ [ name1, value1 ], [ name2, value2 ] ]`\n   **/\n  this.attrs    = null\n\n  /**\n   * Token#map -> Array\n   *\n   * Source map info. Format: `[ line_begin, line_end ]`\n   **/\n  this.map      = null\n\n  /**\n   * Token#nesting -> Number\n   *\n   * Level change (number in {-1, 0, 1} set), where:\n   *\n   * -  `1` means the tag is opening\n   * -  `0` means the tag is self-closing\n   * - `-1` means the tag is closing\n   **/\n  this.nesting  = nesting\n\n  /**\n   * Token#level -> Number\n   *\n   * nesting level, the same as `state.level`\n   **/\n  this.level    = 0\n\n  /**\n   * Token#children -> Array\n   *\n   * An array of child nodes (inline and img tokens)\n   **/\n  this.children = null\n\n  /**\n   * Token#content -> String\n   *\n   * In a case of self-closing tag (code, html, fence, etc.),\n   * it has contents of this tag.\n   **/\n  this.content  = ''\n\n  /**\n   * Token#markup -> String\n   *\n   * '*' or '_' for emphasis, fence string for fence, etc.\n   **/\n  this.markup   = ''\n\n  /**\n   * Token#info -> String\n   *\n   * Additional information:\n   *\n   * - Info string for \"fence\" tokens\n   * - The value \"auto\" for autolink \"link_open\" and \"link_close\" tokens\n   * - The string value of the item marker for ordered-list \"list_item_open\" tokens\n   **/\n  this.info     = ''\n\n  /**\n   * Token#meta -> Object\n   *\n   * A place for plugins to store an arbitrary data\n   **/\n  this.meta     = null\n\n  /**\n   * Token#block -> Boolean\n   *\n   * True for block-level tokens, false for inline tokens.\n   * Used in renderer to calculate line breaks\n   **/\n  this.block    = false\n\n  /**\n   * Token#hidden -> Boolean\n   *\n   * If it's true, ignore this element when rendering. Used for tight lists\n   * to hide paragraphs.\n   **/\n  this.hidden   = false\n}\n\n/**\n * Token.attrIndex(name) -> Number\n *\n * Search attribute index by name.\n **/\nToken.prototype.attrIndex = function attrIndex (name) {\n  if (!this.attrs) { return -1 }\n\n  const attrs = this.attrs\n\n  for (let i = 0, len = attrs.length; i < len; i++) {\n    if (attrs[i][0] === name) { return i }\n  }\n  return -1\n}\n\n/**\n * Token.attrPush(attrData)\n *\n * Add `[ name, value ]` attribute to list. Init attrs if necessary\n **/\nToken.prototype.attrPush = function attrPush (attrData) {\n  if (this.attrs) {\n    this.attrs.push(attrData)\n  } else {\n    this.attrs = [attrData]\n  }\n}\n\n/**\n * Token.attrSet(name, value)\n *\n * Set `name` attribute to `value`. Override old value if exists.\n **/\nToken.prototype.attrSet = function attrSet (name, value) {\n  const idx = this.attrIndex(name)\n  const attrData = [name, value]\n\n  if (idx < 0) {\n    this.attrPush(attrData)\n  } else {\n    this.attrs[idx] = attrData\n  }\n}\n\n/**\n * Token.attrGet(name)\n *\n * Get the value of attribute `name`, or null if it does not exist.\n **/\nToken.prototype.attrGet = function attrGet (name) {\n  const idx = this.attrIndex(name)\n  let value = null\n  if (idx >= 0) {\n    value = this.attrs[idx][1]\n  }\n  return value\n}\n\n/**\n * Token.attrJoin(name, value)\n *\n * Join value to existing attribute via space. Or create new attribute if not\n * exists. Useful to operate with token classes.\n **/\nToken.prototype.attrJoin = function attrJoin (name, value) {\n  const idx = this.attrIndex(name)\n\n  if (idx < 0) {\n    this.attrPush([name, value])\n  } else {\n    this.attrs[idx][1] = this.attrs[idx][1] + ' ' + value\n  }\n}\n\nexport default Token\n", "// Core state object\n//\n\nimport Token from '../token.mjs'\n\nfunction StateCore (src, md, env) {\n  this.src = src\n  this.env = env\n  this.tokens = []\n  this.inlineMode = false\n  this.md = md // link to parser instance\n}\n\n// re-export Token class to use in core rules\nStateCore.prototype.Token = Token\n\nexport default StateCore\n", "// Normalize input string\n\n// https://spec.commonmark.org/0.29/#line-ending\nconst NEWLINES_RE  = /\\r\\n?|\\n/g\nconst NULL_RE      = /\\0/g\n\nexport default function normalize (state) {\n  let str\n\n  // Normalize newlines\n  str = state.src.replace(NEWLINES_RE, '\\n')\n\n  // Replace NULL characters\n  str = str.replace(NULL_RE, '\\uFFFD')\n\n  state.src = str\n}\n", "export default function block (state) {\n  let token\n\n  if (state.inlineMode) {\n    token          = new state.Token('inline', '', 0)\n    token.content  = state.src\n    token.map      = [0, 1]\n    token.children = []\n    state.tokens.push(token)\n  } else {\n    state.md.block.parse(state.src, state.md, state.env, state.tokens)\n  }\n}\n", "export default function inline (state) {\n  const tokens = state.tokens\n\n  // Parse inlines\n  for (let i = 0, l = tokens.length; i < l; i++) {\n    const tok = tokens[i]\n    if (tok.type === 'inline') {\n      state.md.inline.parse(tok.content, state.md, state.env, tok.children)\n    }\n  }\n}\n", "// Replace link-like texts with link nodes.\n//\n// Currently restricted by `md.validateLink()` to http/https/ftp\n//\n\nimport { arrayReplaceAt } from '../common/utils.mjs'\n\nfunction isLinkOpen (str) {\n  return /^<a[>\\s]/i.test(str)\n}\nfunction isLinkClose (str) {\n  return /^<\\/a\\s*>/i.test(str)\n}\n\nexport default function linkify (state) {\n  const blockTokens = state.tokens\n\n  if (!state.md.options.linkify) { return }\n\n  for (let j = 0, l = blockTokens.length; j < l; j++) {\n    if (blockTokens[j].type !== 'inline' ||\n        !state.md.linkify.pretest(blockTokens[j].content)) {\n      continue\n    }\n\n    let tokens = blockTokens[j].children\n\n    let htmlLinkLevel = 0\n\n    // We scan from the end, to keep position when new tags added.\n    // Use reversed logic in links start/end match\n    for (let i = tokens.length - 1; i >= 0; i--) {\n      const currentToken = tokens[i]\n\n      // Skip content of markdown links\n      if (currentToken.type === 'link_close') {\n        i--\n        while (tokens[i].level !== currentToken.level && tokens[i].type !== 'link_open') {\n          i--\n        }\n        continue\n      }\n\n      // Skip content of html tag links\n      if (currentToken.type === 'html_inline') {\n        if (isLinkOpen(currentToken.content) && htmlLinkLevel > 0) {\n          htmlLinkLevel--\n        }\n        if (isLinkClose(currentToken.content)) {\n          htmlLinkLevel++\n        }\n      }\n      if (htmlLinkLevel > 0) { continue }\n\n      if (currentToken.type === 'text' && state.md.linkify.test(currentToken.content)) {\n        const text = currentToken.content\n        let links = state.md.linkify.match(text)\n\n        // Now split string to nodes\n        const nodes = []\n        let level = currentToken.level\n        let lastPos = 0\n\n        // forbid escape sequence at the start of the string,\n        // this avoids http\\://example.com/ from being linkified as\n        // http:<a href=\"//example.com/\">//example.com/</a>\n        if (links.length > 0 &&\n            links[0].index === 0 &&\n            i > 0 &&\n            tokens[i - 1].type === 'text_special') {\n          links = links.slice(1)\n        }\n\n        for (let ln = 0; ln < links.length; ln++) {\n          const url = links[ln].url\n          const fullUrl = state.md.normalizeLink(url)\n          if (!state.md.validateLink(fullUrl)) { continue }\n\n          let urlText = links[ln].text\n\n          // Linkifier might send raw hostnames like \"example.com\", where url\n          // starts with domain name. So we prepend http:// in those cases,\n          // and remove it afterwards.\n          //\n          if (!links[ln].schema) {\n            urlText = state.md.normalizeLinkText('http://' + urlText).replace(/^http:\\/\\//, '')\n          } else if (links[ln].schema === 'mailto:' && !/^mailto:/i.test(urlText)) {\n            urlText = state.md.normalizeLinkText('mailto:' + urlText).replace(/^mailto:/, '')\n          } else {\n            urlText = state.md.normalizeLinkText(urlText)\n          }\n\n          const pos = links[ln].index\n\n          if (pos > lastPos) {\n            const token   = new state.Token('text', '', 0)\n            token.content = text.slice(lastPos, pos)\n            token.level   = level\n            nodes.push(token)\n          }\n\n          const token_o   = new state.Token('link_open', 'a', 1)\n          token_o.attrs   = [['href', fullUrl]]\n          token_o.level   = level++\n          token_o.markup  = 'linkify'\n          token_o.info    = 'auto'\n          nodes.push(token_o)\n\n          const token_t   = new state.Token('text', '', 0)\n          token_t.content = urlText\n          token_t.level   = level\n          nodes.push(token_t)\n\n          const token_c   = new state.Token('link_close', 'a', -1)\n          token_c.level   = --level\n          token_c.markup  = 'linkify'\n          token_c.info    = 'auto'\n          nodes.push(token_c)\n\n          lastPos = links[ln].lastIndex\n        }\n        if (lastPos < text.length) {\n          const token   = new state.Token('text', '', 0)\n          token.content = text.slice(lastPos)\n          token.level   = level\n          nodes.push(token)\n        }\n\n        // replace current node\n        blockTokens[j].children = tokens = arrayReplaceAt(tokens, i, nodes)\n      }\n    }\n  }\n}\n", "// Simple typographic replacements\n//\n// (c) (C) → ©\n// (tm) (TM) → ™\n// (r) (R) → ®\n// +- → ±\n// ... → … (also ?.... → ?.., !.... → !..)\n// ???????? → ???, !!!!! → !!!, `,,` → `,`\n// -- → &ndash;, --- → &mdash;\n//\n\n// TODO:\n// - fractionals 1/2, 1/4, 3/4 -> ½, ¼, ¾\n// - multiplications 2 x 4 -> 2 × 4\n\nconst RARE_RE = /\\+-|\\.\\.|\\?\\?\\?\\?|!!!!|,,|--/\n\n// Workaround for phantomjs - need regex without /g flag,\n// or root check will fail every second time\nconst SCOPED_ABBR_TEST_RE = /\\((c|tm|r)\\)/i\n\nconst SCOPED_ABBR_RE = /\\((c|tm|r)\\)/ig\nconst SCOPED_ABBR = {\n  c: '©',\n  r: '®',\n  tm: '™'\n}\n\nfunction replaceFn (match, name) {\n  return SCOPED_ABBR[name.toLowerCase()]\n}\n\nfunction replace_scoped (inlineTokens) {\n  let inside_autolink = 0\n\n  for (let i = inlineTokens.length - 1; i >= 0; i--) {\n    const token = inlineTokens[i]\n\n    if (token.type === 'text' && !inside_autolink) {\n      token.content = token.content.replace(SCOPED_ABBR_RE, replaceFn)\n    }\n\n    if (token.type === 'link_open' && token.info === 'auto') {\n      inside_autolink--\n    }\n\n    if (token.type === 'link_close' && token.info === 'auto') {\n      inside_autolink++\n    }\n  }\n}\n\nfunction replace_rare (inlineTokens) {\n  let inside_autolink = 0\n\n  for (let i = inlineTokens.length - 1; i >= 0; i--) {\n    const token = inlineTokens[i]\n\n    if (token.type === 'text' && !inside_autolink) {\n      if (RARE_RE.test(token.content)) {\n        token.content = token.content\n          .replace(/\\+-/g, '±')\n          // .., ..., ....... -> …\n          // but ?..... & !..... -> ?.. & !..\n          .replace(/\\.{2,}/g, '…').replace(/([?!])…/g, '$1..')\n          .replace(/([?!]){4,}/g, '$1$1$1').replace(/,{2,}/g, ',')\n          // em-dash\n          .replace(/(^|[^-])---(?=[^-]|$)/mg, '$1\\u2014')\n          // en-dash\n          .replace(/(^|\\s)--(?=\\s|$)/mg, '$1\\u2013')\n          .replace(/(^|[^-\\s])--(?=[^-\\s]|$)/mg, '$1\\u2013')\n      }\n    }\n\n    if (token.type === 'link_open' && token.info === 'auto') {\n      inside_autolink--\n    }\n\n    if (token.type === 'link_close' && token.info === 'auto') {\n      inside_autolink++\n    }\n  }\n}\n\nexport default function replace (state) {\n  let blkIdx\n\n  if (!state.md.options.typographer) { return }\n\n  for (blkIdx = state.tokens.length - 1; blkIdx >= 0; blkIdx--) {\n    if (state.tokens[blkIdx].type !== 'inline') { continue }\n\n    if (SCOPED_ABBR_TEST_RE.test(state.tokens[blkIdx].content)) {\n      replace_scoped(state.tokens[blkIdx].children)\n    }\n\n    if (RARE_RE.test(state.tokens[blkIdx].content)) {\n      replace_rare(state.tokens[blkIdx].children)\n    }\n  }\n}\n", "// Convert straight quotation marks to typographic ones\n//\n\nimport { isWhiteSpace, isPunctChar, isMdAsciiPunct } from '../common/utils.mjs'\n\nconst QUOTE_TEST_RE = /['\"]/\nconst QUOTE_RE = /['\"]/g\nconst APOSTROPHE = '\\u2019' /* ’ */\n\nfunction replaceAt (str, index, ch) {\n  return str.slice(0, index) + ch + str.slice(index + 1)\n}\n\nfunction process_inlines (tokens, state) {\n  let j\n\n  const stack = []\n\n  for (let i = 0; i < tokens.length; i++) {\n    const token = tokens[i]\n\n    const thisLevel = tokens[i].level\n\n    for (j = stack.length - 1; j >= 0; j--) {\n      if (stack[j].level <= thisLevel) { break }\n    }\n    stack.length = j + 1\n\n    if (token.type !== 'text') { continue }\n\n    let text = token.content\n    let pos = 0\n    let max = text.length\n\n    /* eslint no-labels:0,block-scoped-var:0 */\n    OUTER:\n    while (pos < max) {\n      QUOTE_RE.lastIndex = pos\n      const t = QUOTE_RE.exec(text)\n      if (!t) { break }\n\n      let canOpen = true\n      let canClose = true\n      pos = t.index + 1\n      const isSingle = (t[0] === \"'\")\n\n      // Find previous character,\n      // default to space if it's the beginning of the line\n      //\n      let lastChar = 0x20\n\n      if (t.index - 1 >= 0) {\n        lastChar = text.charCodeAt(t.index - 1)\n      } else {\n        for (j = i - 1; j >= 0; j--) {\n          if (tokens[j].type === 'softbreak' || tokens[j].type === 'hardbreak') break // lastChar defaults to 0x20\n          if (!tokens[j].content) continue // should skip all tokens except 'text', 'html_inline' or 'code_inline'\n\n          lastChar = tokens[j].content.charCodeAt(tokens[j].content.length - 1)\n          break\n        }\n      }\n\n      // Find next character,\n      // default to space if it's the end of the line\n      //\n      let nextChar = 0x20\n\n      if (pos < max) {\n        nextChar = text.charCodeAt(pos)\n      } else {\n        for (j = i + 1; j < tokens.length; j++) {\n          if (tokens[j].type === 'softbreak' || tokens[j].type === 'hardbreak') break // nextChar defaults to 0x20\n          if (!tokens[j].content) continue // should skip all tokens except 'text', 'html_inline' or 'code_inline'\n\n          nextChar = tokens[j].content.charCodeAt(0)\n          break\n        }\n      }\n\n      const isLastPunctChar = isMdAsciiPunct(lastChar) || isPunctChar(String.fromCharCode(lastChar))\n      const isNextPunctChar = isMdAsciiPunct(nextChar) || isPunctChar(String.fromCharCode(nextChar))\n\n      const isLastWhiteSpace = isWhiteSpace(lastChar)\n      const isNextWhiteSpace = isWhiteSpace(nextChar)\n\n      if (isNextWhiteSpace) {\n        canOpen = false\n      } else if (isNextPunctChar) {\n        if (!(isLastWhiteSpace || isLastPunctChar)) {\n          canOpen = false\n        }\n      }\n\n      if (isLastWhiteSpace) {\n        canClose = false\n      } else if (isLastPunctChar) {\n        if (!(isNextWhiteSpace || isNextPunctChar)) {\n          canClose = false\n        }\n      }\n\n      if (nextChar === 0x22 /* \" */ && t[0] === '\"') {\n        if (lastChar >= 0x30 /* 0 */ && lastChar <= 0x39 /* 9 */) {\n          // special case: 1\"\" - count first quote as an inch\n          canClose = canOpen = false\n        }\n      }\n\n      if (canOpen && canClose) {\n        // Replace quotes in the middle of punctuation sequence, but not\n        // in the middle of the words, i.e.:\n        //\n        // 1. foo \" bar \" baz - not replaced\n        // 2. foo-\"-bar-\"-baz - replaced\n        // 3. foo\"bar\"baz     - not replaced\n        //\n        canOpen = isLastPunctChar\n        canClose = isNextPunctChar\n      }\n\n      if (!canOpen && !canClose) {\n        // middle of word\n        if (isSingle) {\n          token.content = replaceAt(token.content, t.index, APOSTROPHE)\n        }\n        continue\n      }\n\n      if (canClose) {\n        // this could be a closing quote, rewind the stack to get a match\n        for (j = stack.length - 1; j >= 0; j--) {\n          let item = stack[j]\n          if (stack[j].level < thisLevel) { break }\n          if (item.single === isSingle && stack[j].level === thisLevel) {\n            item = stack[j]\n\n            let openQuote\n            let closeQuote\n            if (isSingle) {\n              openQuote = state.md.options.quotes[2]\n              closeQuote = state.md.options.quotes[3]\n            } else {\n              openQuote = state.md.options.quotes[0]\n              closeQuote = state.md.options.quotes[1]\n            }\n\n            // replace token.content *before* tokens[item.token].content,\n            // because, if they are pointing at the same token, replaceAt\n            // could mess up indices when quote length != 1\n            token.content = replaceAt(token.content, t.index, closeQuote)\n            tokens[item.token].content = replaceAt(\n              tokens[item.token].content, item.pos, openQuote)\n\n            pos += closeQuote.length - 1\n            if (item.token === i) { pos += openQuote.length - 1 }\n\n            text = token.content\n            max = text.length\n\n            stack.length = j\n            continue OUTER\n          }\n        }\n      }\n\n      if (canOpen) {\n        stack.push({\n          token: i,\n          pos: t.index,\n          single: isSingle,\n          level: thisLevel\n        })\n      } else if (canClose && isSingle) {\n        token.content = replaceAt(token.content, t.index, APOSTROPHE)\n      }\n    }\n  }\n}\n\nexport default function smartquotes (state) {\n  /* eslint max-depth:0 */\n  if (!state.md.options.typographer) { return }\n\n  for (let blkIdx = state.tokens.length - 1; blkIdx >= 0; blkIdx--) {\n    if (state.tokens[blkIdx].type !== 'inline' ||\n        !QUOTE_TEST_RE.test(state.tokens[blkIdx].content)) {\n      continue\n    }\n\n    process_inlines(state.tokens[blkIdx].children, state)\n  }\n}\n", "// Join raw text tokens with the rest of the text\n//\n// This is set as a separate rule to provide an opportunity for plugins\n// to run text replacements after text join, but before escape join.\n//\n// For example, `\\:)` shouldn't be replaced with an emoji.\n//\n\nexport default function text_join (state) {\n  let curr, last\n  const blockTokens = state.tokens\n  const l = blockTokens.length\n\n  for (let j = 0; j < l; j++) {\n    if (blockTokens[j].type !== 'inline') continue\n\n    const tokens = blockTokens[j].children\n    const max = tokens.length\n\n    for (curr = 0; curr < max; curr++) {\n      if (tokens[curr].type === 'text_special') {\n        tokens[curr].type = 'text'\n      }\n    }\n\n    for (curr = last = 0; curr < max; curr++) {\n      if (tokens[curr].type === 'text' &&\n          curr + 1 < max &&\n          tokens[curr + 1].type === 'text') {\n        // collapse two adjacent text nodes\n        tokens[curr + 1].content = tokens[curr].content + tokens[curr + 1].content\n      } else {\n        if (curr !== last) { tokens[last] = tokens[curr] }\n\n        last++\n      }\n    }\n\n    if (curr !== last) {\n      tokens.length = last\n    }\n  }\n}\n", "/** internal\n * class Core\n *\n * Top-level rules executor. Glues block/inline parsers and does intermediate\n * transformations.\n **/\n\nimport Ruler from './ruler.mjs'\nimport StateCore from './rules_core/state_core.mjs'\n\nimport r_normalize from './rules_core/normalize.mjs'\nimport r_block from './rules_core/block.mjs'\nimport r_inline from './rules_core/inline.mjs'\nimport r_linkify from './rules_core/linkify.mjs'\nimport r_replacements from './rules_core/replacements.mjs'\nimport r_smartquotes from './rules_core/smartquotes.mjs'\nimport r_text_join from './rules_core/text_join.mjs'\n\nconst _rules = [\n  ['normalize',      r_normalize],\n  ['block',          r_block],\n  ['inline',         r_inline],\n  ['linkify',        r_linkify],\n  ['replacements',   r_replacements],\n  ['smartquotes',    r_smartquotes],\n  // `text_join` finds `text_special` tokens (for escape sequences)\n  // and joins them with the rest of the text\n  ['text_join',      r_text_join]\n]\n\n/**\n * new Core()\n **/\nfunction Core () {\n  /**\n   * Core#ruler -> Ruler\n   *\n   * [[Ruler]] instance. Keep configuration of core rules.\n   **/\n  this.ruler = new Ruler()\n\n  for (let i = 0; i < _rules.length; i++) {\n    this.ruler.push(_rules[i][0], _rules[i][1])\n  }\n}\n\n/**\n * Core.process(state)\n *\n * Executes core chain rules.\n **/\nCore.prototype.process = function (state) {\n  const rules = this.ruler.getRules('')\n\n  for (let i = 0, l = rules.length; i < l; i++) {\n    rules[i](state)\n  }\n}\n\nCore.prototype.State = StateCore\n\nexport default Core\n", "// Parser state class\n\nimport Token from '../token.mjs'\nimport { isSpace } from '../common/utils.mjs'\n\nfunction StateBlock (src, md, env, tokens) {\n  this.src = src\n\n  // link to parser instance\n  this.md     = md\n\n  this.env = env\n\n  //\n  // Internal state vartiables\n  //\n\n  this.tokens = tokens\n\n  this.bMarks = []  // line begin offsets for fast jumps\n  this.eMarks = []  // line end offsets for fast jumps\n  this.tShift = []  // offsets of the first non-space characters (tabs not expanded)\n  this.sCount = []  // indents for each line (tabs expanded)\n\n  // An amount of virtual spaces (tabs expanded) between beginning\n  // of each line (bMarks) and real beginning of that line.\n  //\n  // It exists only as a hack because blockquotes override bMarks\n  // losing information in the process.\n  //\n  // It's used only when expanding tabs, you can think about it as\n  // an initial tab length, e.g. bsCount=21 applied to string `\\t123`\n  // means first tab should be expanded to 4-21%4 === 3 spaces.\n  //\n  this.bsCount = []\n\n  // block parser variables\n\n  // required block content indent (for example, if we are\n  // inside a list, it would be positioned after list marker)\n  this.blkIndent  = 0\n  this.line       = 0 // line index in src\n  this.lineMax    = 0 // lines count\n  this.tight      = false  // loose/tight mode for lists\n  this.ddIndent   = -1 // indent of the current dd block (-1 if there isn't any)\n  this.listIndent = -1 // indent of the current list block (-1 if there isn't any)\n\n  // can be 'blockquote', 'list', 'root', 'paragraph' or 'reference'\n  // used in lists to determine if they interrupt a paragraph\n  this.parentType = 'root'\n\n  this.level = 0\n\n  // Create caches\n  // Generate markers.\n  const s = this.src\n\n  for (let start = 0, pos = 0, indent = 0, offset = 0, len = s.length, indent_found = false; pos < len; pos++) {\n    const ch = s.charCodeAt(pos)\n\n    if (!indent_found) {\n      if (isSpace(ch)) {\n        indent++\n\n        if (ch === 0x09) {\n          offset += 4 - offset % 4\n        } else {\n          offset++\n        }\n        continue\n      } else {\n        indent_found = true\n      }\n    }\n\n    if (ch === 0x0A || pos === len - 1) {\n      if (ch !== 0x0A) { pos++ }\n      this.bMarks.push(start)\n      this.eMarks.push(pos)\n      this.tShift.push(indent)\n      this.sCount.push(offset)\n      this.bsCount.push(0)\n\n      indent_found = false\n      indent = 0\n      offset = 0\n      start = pos + 1\n    }\n  }\n\n  // Push fake entry to simplify cache bounds checks\n  this.bMarks.push(s.length)\n  this.eMarks.push(s.length)\n  this.tShift.push(0)\n  this.sCount.push(0)\n  this.bsCount.push(0)\n\n  this.lineMax = this.bMarks.length - 1 // don't count last fake line\n}\n\n// Push new token to \"stream\".\n//\nStateBlock.prototype.push = function (type, tag, nesting) {\n  const token = new Token(type, tag, nesting)\n  token.block = true\n\n  if (nesting < 0) this.level-- // closing tag\n  token.level = this.level\n  if (nesting > 0) this.level++ // opening tag\n\n  this.tokens.push(token)\n  return token\n}\n\nStateBlock.prototype.isEmpty = function isEmpty (line) {\n  return this.bMarks[line] + this.tShift[line] >= this.eMarks[line]\n}\n\nStateBlock.prototype.skipEmptyLines = function skipEmptyLines (from) {\n  for (let max = this.lineMax; from < max; from++) {\n    if (this.bMarks[from] + this.tShift[from] < this.eMarks[from]) {\n      break\n    }\n  }\n  return from\n}\n\n// Skip spaces from given position.\nStateBlock.prototype.skipSpaces = function skipSpaces (pos) {\n  for (let max = this.src.length; pos < max; pos++) {\n    const ch = this.src.charCodeAt(pos)\n    if (!isSpace(ch)) { break }\n  }\n  return pos\n}\n\n// Skip spaces from given position in reverse.\nStateBlock.prototype.skipSpacesBack = function skipSpacesBack (pos, min) {\n  if (pos <= min) { return pos }\n\n  while (pos > min) {\n    if (!isSpace(this.src.charCodeAt(--pos))) { return pos + 1 }\n  }\n  return pos\n}\n\n// Skip char codes from given position\nStateBlock.prototype.skipChars = function skipChars (pos, code) {\n  for (let max = this.src.length; pos < max; pos++) {\n    if (this.src.charCodeAt(pos) !== code) { break }\n  }\n  return pos\n}\n\n// Skip char codes reverse from given position - 1\nStateBlock.prototype.skipCharsBack = function skipCharsBack (pos, code, min) {\n  if (pos <= min) { return pos }\n\n  while (pos > min) {\n    if (code !== this.src.charCodeAt(--pos)) { return pos + 1 }\n  }\n  return pos\n}\n\n// cut lines range from source.\nStateBlock.prototype.getLines = function getLines (begin, end, indent, keepLastLF) {\n  if (begin >= end) {\n    return ''\n  }\n\n  const queue = new Array(end - begin)\n\n  for (let i = 0, line = begin; line < end; line++, i++) {\n    let lineIndent = 0\n    const lineStart = this.bMarks[line]\n    let first = lineStart\n    let last\n\n    if (line + 1 < end || keepLastLF) {\n      // No need for bounds check because we have fake entry on tail.\n      last = this.eMarks[line] + 1\n    } else {\n      last = this.eMarks[line]\n    }\n\n    while (first < last && lineIndent < indent) {\n      const ch = this.src.charCodeAt(first)\n\n      if (isSpace(ch)) {\n        if (ch === 0x09) {\n          lineIndent += 4 - (lineIndent + this.bsCount[line]) % 4\n        } else {\n          lineIndent++\n        }\n      } else if (first - lineStart < this.tShift[line]) {\n        // patched tShift masked characters to look like spaces (blockquotes, list markers)\n        lineIndent++\n      } else {\n        break\n      }\n\n      first++\n    }\n\n    if (lineIndent > indent) {\n      // partially expanding tabs in code blocks, e.g '\\t\\tfoobar'\n      // with indent=2 becomes '  \\tfoobar'\n      queue[i] = new Array(lineIndent - indent + 1).join(' ') + this.src.slice(first, last)\n    } else {\n      queue[i] = this.src.slice(first, last)\n    }\n  }\n\n  return queue.join('')\n}\n\n// re-export Token class to use in block rules\nStateBlock.prototype.Token = Token\n\nexport default StateBlock\n", "// GFM table, https://github.github.com/gfm/#tables-extension-\n\nimport { isSpace } from '../common/utils.mjs'\n\n// Limit the amount of empty autocompleted cells in a table,\n// see https://github.com/markdown-it/markdown-it/issues/1000,\n//\n// Both pulldown-cmark and commonmark-hs limit the number of cells this way to ~200k.\n// We set it to 65k, which can expand user input by a factor of x370\n// (256x256 square is 1.8kB expanded into 650kB).\nconst MAX_AUTOCOMPLETED_CELLS = 0x10000\n\nfunction getLine (state, line) {\n  const pos = state.bMarks[line] + state.tShift[line]\n  const max = state.eMarks[line]\n\n  return state.src.slice(pos, max)\n}\n\nfunction escapedSplit (str) {\n  const result = []\n  const max = str.length\n\n  let pos = 0\n  let ch = str.charCodeAt(pos)\n  let isEscaped = false\n  let lastPos = 0\n  let current = ''\n\n  while (pos < max) {\n    if (ch === 0x7c/* | */) {\n      if (!isEscaped) {\n        // pipe separating cells, '|'\n        result.push(current + str.substring(lastPos, pos))\n        current = ''\n        lastPos = pos + 1\n      } else {\n        // escaped pipe, '\\|'\n        current += str.substring(lastPos, pos - 1)\n        lastPos = pos\n      }\n    }\n\n    isEscaped = (ch === 0x5c/* \\ */)\n    pos++\n\n    ch = str.charCodeAt(pos)\n  }\n\n  result.push(current + str.substring(lastPos))\n\n  return result\n}\n\nexport default function table (state, startLine, endLine, silent) {\n  // should have at least two lines\n  if (startLine + 2 > endLine) { return false }\n\n  let nextLine = startLine + 1\n\n  if (state.sCount[nextLine] < state.blkIndent) { return false }\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[nextLine] - state.blkIndent >= 4) { return false }\n\n  // first character of the second line should be '|', '-', ':',\n  // and no other characters are allowed but spaces;\n  // basically, this is the equivalent of /^[-:|][-:|\\s]*$/ regexp\n\n  let pos = state.bMarks[nextLine] + state.tShift[nextLine]\n  if (pos >= state.eMarks[nextLine]) { return false }\n\n  const firstCh = state.src.charCodeAt(pos++)\n  if (firstCh !== 0x7C/* | */ && firstCh !== 0x2D/* - */ && firstCh !== 0x3A/* : */) { return false }\n\n  if (pos >= state.eMarks[nextLine]) { return false }\n\n  const secondCh = state.src.charCodeAt(pos++)\n  if (secondCh !== 0x7C/* | */ && secondCh !== 0x2D/* - */ && secondCh !== 0x3A/* : */ && !isSpace(secondCh)) {\n    return false\n  }\n\n  // if first character is '-', then second character must not be a space\n  // (due to parsing ambiguity with list)\n  if (firstCh === 0x2D/* - */ && isSpace(secondCh)) { return false }\n\n  while (pos < state.eMarks[nextLine]) {\n    const ch = state.src.charCodeAt(pos)\n\n    if (ch !== 0x7C/* | */ && ch !== 0x2D/* - */ && ch !== 0x3A/* : */ && !isSpace(ch)) { return false }\n\n    pos++\n  }\n\n  let lineText = getLine(state, startLine + 1)\n  let columns = lineText.split('|')\n  const aligns = []\n  for (let i = 0; i < columns.length; i++) {\n    const t = columns[i].trim()\n    if (!t) {\n      // allow empty columns before and after table, but not in between columns;\n      // e.g. allow ` |---| `, disallow ` ---||--- `\n      if (i === 0 || i === columns.length - 1) {\n        continue\n      } else {\n        return false\n      }\n    }\n\n    if (!/^:?-+:?$/.test(t)) { return false }\n    if (t.charCodeAt(t.length - 1) === 0x3A/* : */) {\n      aligns.push(t.charCodeAt(0) === 0x3A/* : */ ? 'center' : 'right')\n    } else if (t.charCodeAt(0) === 0x3A/* : */) {\n      aligns.push('left')\n    } else {\n      aligns.push('')\n    }\n  }\n\n  lineText = getLine(state, startLine).trim()\n  if (lineText.indexOf('|') === -1) { return false }\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false }\n  columns = escapedSplit(lineText)\n  if (columns.length && columns[0] === '') columns.shift()\n  if (columns.length && columns[columns.length - 1] === '') columns.pop()\n\n  // header row will define an amount of columns in the entire table,\n  // and align row should be exactly the same (the rest of the rows can differ)\n  const columnCount = columns.length\n  if (columnCount === 0 || columnCount !== aligns.length) { return false }\n\n  if (silent) { return true }\n\n  const oldParentType = state.parentType\n  state.parentType = 'table'\n\n  // use 'blockquote' lists for termination because it's\n  // the most similar to tables\n  const terminatorRules = state.md.block.ruler.getRules('blockquote')\n\n  const token_to = state.push('table_open', 'table', 1)\n  const tableLines = [startLine, 0]\n  token_to.map = tableLines\n\n  const token_tho = state.push('thead_open', 'thead', 1)\n  token_tho.map = [startLine, startLine + 1]\n\n  const token_htro = state.push('tr_open', 'tr', 1)\n  token_htro.map = [startLine, startLine + 1]\n\n  for (let i = 0; i < columns.length; i++) {\n    const token_ho = state.push('th_open', 'th', 1)\n    if (aligns[i]) {\n      token_ho.attrs  = [['style', 'text-align:' + aligns[i]]]\n    }\n\n    const token_il = state.push('inline', '', 0)\n    token_il.content  = columns[i].trim()\n    token_il.children = []\n\n    state.push('th_close', 'th', -1)\n  }\n\n  state.push('tr_close', 'tr', -1)\n  state.push('thead_close', 'thead', -1)\n\n  let tbodyLines\n  let autocompletedCells = 0\n\n  for (nextLine = startLine + 2; nextLine < endLine; nextLine++) {\n    if (state.sCount[nextLine] < state.blkIndent) { break }\n\n    let terminate = false\n    for (let i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true\n        break\n      }\n    }\n\n    if (terminate) { break }\n    lineText = getLine(state, nextLine).trim()\n    if (!lineText) { break }\n    if (state.sCount[nextLine] - state.blkIndent >= 4) { break }\n    columns = escapedSplit(lineText)\n    if (columns.length && columns[0] === '') columns.shift()\n    if (columns.length && columns[columns.length - 1] === '') columns.pop()\n\n    // note: autocomplete count can be negative if user specifies more columns than header,\n    // but that does not affect intended use (which is limiting expansion)\n    autocompletedCells += columnCount - columns.length\n    if (autocompletedCells > MAX_AUTOCOMPLETED_CELLS) { break }\n\n    if (nextLine === startLine + 2) {\n      const token_tbo = state.push('tbody_open', 'tbody', 1)\n      token_tbo.map = tbodyLines = [startLine + 2, 0]\n    }\n\n    const token_tro = state.push('tr_open', 'tr', 1)\n    token_tro.map = [nextLine, nextLine + 1]\n\n    for (let i = 0; i < columnCount; i++) {\n      const token_tdo = state.push('td_open', 'td', 1)\n      if (aligns[i]) {\n        token_tdo.attrs  = [['style', 'text-align:' + aligns[i]]]\n      }\n\n      const token_il = state.push('inline', '', 0)\n      token_il.content  = columns[i] ? columns[i].trim() : ''\n      token_il.children = []\n\n      state.push('td_close', 'td', -1)\n    }\n    state.push('tr_close', 'tr', -1)\n  }\n\n  if (tbodyLines) {\n    state.push('tbody_close', 'tbody', -1)\n    tbodyLines[1] = nextLine\n  }\n\n  state.push('table_close', 'table', -1)\n  tableLines[1] = nextLine\n\n  state.parentType = oldParentType\n  state.line = nextLine\n  return true\n}\n", "// Code block (4 spaces padded)\n\nexport default function code (state, startLine, endLine/*, silent */) {\n  if (state.sCount[startLine] - state.blkIndent < 4) { return false }\n\n  let nextLine = startLine + 1\n  let last = nextLine\n\n  while (nextLine < endLine) {\n    if (state.isEmpty(nextLine)) {\n      nextLine++\n      continue\n    }\n\n    if (state.sCount[nextLine] - state.blkIndent >= 4) {\n      nextLine++\n      last = nextLine\n      continue\n    }\n    break\n  }\n\n  state.line = last\n\n  const token   = state.push('code_block', 'code', 0)\n  token.content = state.getLines(startLine, last, 4 + state.blkIndent, false) + '\\n'\n  token.map     = [startLine, state.line]\n\n  return true\n}\n", "// fences (``` lang, ~~~ lang)\n\nexport default function fence (state, startLine, endLine, silent) {\n  let pos = state.bMarks[startLine] + state.tShift[startLine]\n  let max = state.eMarks[startLine]\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false }\n\n  if (pos + 3 > max) { return false }\n\n  const marker = state.src.charCodeAt(pos)\n\n  if (marker !== 0x7E/* ~ */ && marker !== 0x60 /* ` */) {\n    return false\n  }\n\n  // scan marker length\n  let mem = pos\n  pos = state.skipChars(pos, marker)\n\n  let len = pos - mem\n\n  if (len < 3) { return false }\n\n  const markup = state.src.slice(mem, pos)\n  const params = state.src.slice(pos, max)\n\n  if (marker === 0x60 /* ` */) {\n    if (params.indexOf(String.fromCharCode(marker)) >= 0) {\n      return false\n    }\n  }\n\n  // Since start is found, we can report success here in validation mode\n  if (silent) { return true }\n\n  // search end of block\n  let nextLine = startLine\n  let haveEndMarker = false\n\n  for (;;) {\n    nextLine++\n    if (nextLine >= endLine) {\n      // unclosed block should be autoclosed by end of document.\n      // also block seems to be autoclosed by end of parent\n      break\n    }\n\n    pos = mem = state.bMarks[nextLine] + state.tShift[nextLine]\n    max = state.eMarks[nextLine]\n\n    if (pos < max && state.sCount[nextLine] < state.blkIndent) {\n      // non-empty line with negative indent should stop the list:\n      // - ```\n      //  test\n      break\n    }\n\n    if (state.src.charCodeAt(pos) !== marker) { continue }\n\n    if (state.sCount[nextLine] - state.blkIndent >= 4) {\n      // closing fence should be indented less than 4 spaces\n      continue\n    }\n\n    pos = state.skipChars(pos, marker)\n\n    // closing code fence must be at least as long as the opening one\n    if (pos - mem < len) { continue }\n\n    // make sure tail has spaces only\n    pos = state.skipSpaces(pos)\n\n    if (pos < max) { continue }\n\n    haveEndMarker = true\n    // found!\n    break\n  }\n\n  // If a fence has heading spaces, they should be removed from its inner block\n  len = state.sCount[startLine]\n\n  state.line = nextLine + (haveEndMarker ? 1 : 0)\n\n  const token   = state.push('fence', 'code', 0)\n  token.info    = params\n  token.content = state.getLines(startLine + 1, nextLine, len, true)\n  token.markup  = markup\n  token.map     = [startLine, state.line]\n\n  return true\n}\n", "// Block quotes\n\nimport { isSpace } from '../common/utils.mjs'\n\nexport default function blockquote (state, startLine, endLine, silent) {\n  let pos = state.bMarks[startLine] + state.tShift[startLine]\n  let max = state.eMarks[startLine]\n\n  const oldLineMax = state.lineMax\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false }\n\n  // check the block quote marker\n  if (state.src.charCodeAt(pos) !== 0x3E/* > */) { return false }\n\n  // we know that it's going to be a valid blockquote,\n  // so no point trying to find the end of it in silent mode\n  if (silent) { return true }\n\n  const oldBMarks  = []\n  const oldBSCount = []\n  const oldSCount  = []\n  const oldTShift  = []\n\n  const terminatorRules = state.md.block.ruler.getRules('blockquote')\n\n  const oldParentType = state.parentType\n  state.parentType = 'blockquote'\n  let lastLineEmpty = false\n  let nextLine\n\n  // Search the end of the block\n  //\n  // Block ends with either:\n  //  1. an empty line outside:\n  //     ```\n  //     > test\n  //\n  //     ```\n  //  2. an empty line inside:\n  //     ```\n  //     >\n  //     test\n  //     ```\n  //  3. another tag:\n  //     ```\n  //     > test\n  //      - - -\n  //     ```\n  for (nextLine = startLine; nextLine < endLine; nextLine++) {\n    // check if it's outdented, i.e. it's inside list item and indented\n    // less than said list item:\n    //\n    // ```\n    // 1. anything\n    //    > current blockquote\n    // 2. checking this line\n    // ```\n    const isOutdented = state.sCount[nextLine] < state.blkIndent\n\n    pos = state.bMarks[nextLine] + state.tShift[nextLine]\n    max = state.eMarks[nextLine]\n\n    if (pos >= max) {\n      // Case 1: line is not inside the blockquote, and this line is empty.\n      break\n    }\n\n    if (state.src.charCodeAt(pos++) === 0x3E/* > */ && !isOutdented) {\n      // This line is inside the blockquote.\n\n      // set offset past spaces and \">\"\n      let initial = state.sCount[nextLine] + 1\n      let spaceAfterMarker\n      let adjustTab\n\n      // skip one optional space after '>'\n      if (state.src.charCodeAt(pos) === 0x20 /* space */) {\n        // ' >   test '\n        //     ^ -- position start of line here:\n        pos++\n        initial++\n        adjustTab = false\n        spaceAfterMarker = true\n      } else if (state.src.charCodeAt(pos) === 0x09 /* tab */) {\n        spaceAfterMarker = true\n\n        if ((state.bsCount[nextLine] + initial) % 4 === 3) {\n          // '  >\\t  test '\n          //       ^ -- position start of line here (tab has width===1)\n          pos++\n          initial++\n          adjustTab = false\n        } else {\n          // ' >\\t  test '\n          //    ^ -- position start of line here + shift bsCount slightly\n          //         to make extra space appear\n          adjustTab = true\n        }\n      } else {\n        spaceAfterMarker = false\n      }\n\n      let offset = initial\n      oldBMarks.push(state.bMarks[nextLine])\n      state.bMarks[nextLine] = pos\n\n      while (pos < max) {\n        const ch = state.src.charCodeAt(pos)\n\n        if (isSpace(ch)) {\n          if (ch === 0x09) {\n            offset += 4 - (offset + state.bsCount[nextLine] + (adjustTab ? 1 : 0)) % 4\n          } else {\n            offset++\n          }\n        } else {\n          break\n        }\n\n        pos++\n      }\n\n      lastLineEmpty = pos >= max\n\n      oldBSCount.push(state.bsCount[nextLine])\n      state.bsCount[nextLine] = state.sCount[nextLine] + 1 + (spaceAfterMarker ? 1 : 0)\n\n      oldSCount.push(state.sCount[nextLine])\n      state.sCount[nextLine] = offset - initial\n\n      oldTShift.push(state.tShift[nextLine])\n      state.tShift[nextLine] = pos - state.bMarks[nextLine]\n      continue\n    }\n\n    // Case 2: line is not inside the blockquote, and the last line was empty.\n    if (lastLineEmpty) { break }\n\n    // Case 3: another tag found.\n    let terminate = false\n    for (let i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true\n        break\n      }\n    }\n\n    if (terminate) {\n      // Quirk to enforce \"hard termination mode\" for paragraphs;\n      // normally if you call `tokenize(state, startLine, nextLine)`,\n      // paragraphs will look below nextLine for paragraph continuation,\n      // but if blockquote is terminated by another tag, they shouldn't\n      state.lineMax = nextLine\n\n      if (state.blkIndent !== 0) {\n        // state.blkIndent was non-zero, we now set it to zero,\n        // so we need to re-calculate all offsets to appear as\n        // if indent wasn't changed\n        oldBMarks.push(state.bMarks[nextLine])\n        oldBSCount.push(state.bsCount[nextLine])\n        oldTShift.push(state.tShift[nextLine])\n        oldSCount.push(state.sCount[nextLine])\n        state.sCount[nextLine] -= state.blkIndent\n      }\n\n      break\n    }\n\n    oldBMarks.push(state.bMarks[nextLine])\n    oldBSCount.push(state.bsCount[nextLine])\n    oldTShift.push(state.tShift[nextLine])\n    oldSCount.push(state.sCount[nextLine])\n\n    // A negative indentation means that this is a paragraph continuation\n    //\n    state.sCount[nextLine] = -1\n  }\n\n  const oldIndent = state.blkIndent\n  state.blkIndent = 0\n\n  const token_o  = state.push('blockquote_open', 'blockquote', 1)\n  token_o.markup = '>'\n  const lines = [startLine, 0]\n  token_o.map    = lines\n\n  state.md.block.tokenize(state, startLine, nextLine)\n\n  const token_c  = state.push('blockquote_close', 'blockquote', -1)\n  token_c.markup = '>'\n\n  state.lineMax = oldLineMax\n  state.parentType = oldParentType\n  lines[1] = state.line\n\n  // Restore original tShift; this might not be necessary since the parser\n  // has already been here, but just to make sure we can do that.\n  for (let i = 0; i < oldTShift.length; i++) {\n    state.bMarks[i + startLine] = oldBMarks[i]\n    state.tShift[i + startLine] = oldTShift[i]\n    state.sCount[i + startLine] = oldSCount[i]\n    state.bsCount[i + startLine] = oldBSCount[i]\n  }\n  state.blkIndent = oldIndent\n\n  return true\n}\n", "// Horizontal rule\n\nimport { isSpace } from '../common/utils.mjs'\n\nexport default function hr (state, startLine, endLine, silent) {\n  const max = state.eMarks[startLine]\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false }\n\n  let pos = state.bMarks[startLine] + state.tShift[startLine]\n  const marker = state.src.charCodeAt(pos++)\n\n  // Check hr marker\n  if (marker !== 0x2A/* * */ &&\n      marker !== 0x2D/* - */ &&\n      marker !== 0x5F/* _ */) {\n    return false\n  }\n\n  // markers can be mixed with spaces, but there should be at least 3 of them\n\n  let cnt = 1\n  while (pos < max) {\n    const ch = state.src.charCodeAt(pos++)\n    if (ch !== marker && !isSpace(ch)) { return false }\n    if (ch === marker) { cnt++ }\n  }\n\n  if (cnt < 3) { return false }\n\n  if (silent) { return true }\n\n  state.line = startLine + 1\n\n  const token  = state.push('hr', 'hr', 0)\n  token.map    = [startLine, state.line]\n  token.markup = Array(cnt + 1).join(String.fromCharCode(marker))\n\n  return true\n}\n", "// Lists\n\nimport { isSpace } from '../common/utils.mjs'\n\n// Search `[-+*][\\n ]`, returns next pos after marker on success\n// or -1 on fail.\nfunction skipBulletListMarker (state, startLine) {\n  const max = state.eMarks[startLine]\n  let pos = state.bMarks[startLine] + state.tShift[startLine]\n\n  const marker = state.src.charCodeAt(pos++)\n  // Check bullet\n  if (marker !== 0x2A/* * */ &&\n      marker !== 0x2D/* - */ &&\n      marker !== 0x2B/* + */) {\n    return -1\n  }\n\n  if (pos < max) {\n    const ch = state.src.charCodeAt(pos)\n\n    if (!isSpace(ch)) {\n      // \" -test \" - is not a list item\n      return -1\n    }\n  }\n\n  return pos\n}\n\n// Search `\\d+[.)][\\n ]`, returns next pos after marker on success\n// or -1 on fail.\nfunction skipOrderedListMarker (state, startLine) {\n  const start = state.bMarks[startLine] + state.tShift[startLine]\n  const max = state.eMarks[startLine]\n  let pos = start\n\n  // List marker should have at least 2 chars (digit + dot)\n  if (pos + 1 >= max) { return -1 }\n\n  let ch = state.src.charCodeAt(pos++)\n\n  if (ch < 0x30/* 0 */ || ch > 0x39/* 9 */) { return -1 }\n\n  for (;;) {\n    // EOL -> fail\n    if (pos >= max) { return -1 }\n\n    ch = state.src.charCodeAt(pos++)\n\n    if (ch >= 0x30/* 0 */ && ch <= 0x39/* 9 */) {\n      // List marker should have no more than 9 digits\n      // (prevents integer overflow in browsers)\n      if (pos - start >= 10) { return -1 }\n\n      continue\n    }\n\n    // found valid marker\n    if (ch === 0x29/* ) */ || ch === 0x2e/* . */) {\n      break\n    }\n\n    return -1\n  }\n\n  if (pos < max) {\n    ch = state.src.charCodeAt(pos)\n\n    if (!isSpace(ch)) {\n      // \" 1.test \" - is not a list item\n      return -1\n    }\n  }\n  return pos\n}\n\nfunction markTightParagraphs (state, idx) {\n  const level = state.level + 2\n\n  for (let i = idx + 2, l = state.tokens.length - 2; i < l; i++) {\n    if (state.tokens[i].level === level && state.tokens[i].type === 'paragraph_open') {\n      state.tokens[i + 2].hidden = true\n      state.tokens[i].hidden = true\n      i += 2\n    }\n  }\n}\n\nexport default function list (state, startLine, endLine, silent) {\n  let max, pos, start, token\n  let nextLine = startLine\n  let tight = true\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[nextLine] - state.blkIndent >= 4) { return false }\n\n  // Special case:\n  //  - item 1\n  //   - item 2\n  //    - item 3\n  //     - item 4\n  //      - this one is a paragraph continuation\n  if (state.listIndent >= 0 &&\n      state.sCount[nextLine] - state.listIndent >= 4 &&\n      state.sCount[nextLine] < state.blkIndent) {\n    return false\n  }\n\n  let isTerminatingParagraph = false\n\n  // limit conditions when list can interrupt\n  // a paragraph (validation mode only)\n  if (silent && state.parentType === 'paragraph') {\n    // Next list item should still terminate previous list item;\n    //\n    // This code can fail if plugins use blkIndent as well as lists,\n    // but I hope the spec gets fixed long before that happens.\n    //\n    if (state.sCount[nextLine] >= state.blkIndent) {\n      isTerminatingParagraph = true\n    }\n  }\n\n  // Detect list type and position after marker\n  let isOrdered\n  let markerValue\n  let posAfterMarker\n  if ((posAfterMarker = skipOrderedListMarker(state, nextLine)) >= 0) {\n    isOrdered = true\n    start = state.bMarks[nextLine] + state.tShift[nextLine]\n    markerValue = Number(state.src.slice(start, posAfterMarker - 1))\n\n    // If we're starting a new ordered list right after\n    // a paragraph, it should start with 1.\n    if (isTerminatingParagraph && markerValue !== 1) return false\n  } else if ((posAfterMarker = skipBulletListMarker(state, nextLine)) >= 0) {\n    isOrdered = false\n  } else {\n    return false\n  }\n\n  // If we're starting a new unordered list right after\n  // a paragraph, first line should not be empty.\n  if (isTerminatingParagraph) {\n    if (state.skipSpaces(posAfterMarker) >= state.eMarks[nextLine]) return false\n  }\n\n  // For validation mode we can terminate immediately\n  if (silent) { return true }\n\n  // We should terminate list on style change. Remember first one to compare.\n  const markerCharCode = state.src.charCodeAt(posAfterMarker - 1)\n\n  // Start list\n  const listTokIdx = state.tokens.length\n\n  if (isOrdered) {\n    token       = state.push('ordered_list_open', 'ol', 1)\n    if (markerValue !== 1) {\n      token.attrs = [['start', markerValue]]\n    }\n  } else {\n    token       = state.push('bullet_list_open', 'ul', 1)\n  }\n\n  const listLines = [nextLine, 0]\n  token.map    = listLines\n  token.markup = String.fromCharCode(markerCharCode)\n\n  //\n  // Iterate list items\n  //\n\n  let prevEmptyEnd = false\n  const terminatorRules = state.md.block.ruler.getRules('list')\n\n  const oldParentType = state.parentType\n  state.parentType = 'list'\n\n  while (nextLine < endLine) {\n    pos = posAfterMarker\n    max = state.eMarks[nextLine]\n\n    const initial = state.sCount[nextLine] + posAfterMarker - (state.bMarks[nextLine] + state.tShift[nextLine])\n    let offset = initial\n\n    while (pos < max) {\n      const ch = state.src.charCodeAt(pos)\n\n      if (ch === 0x09) {\n        offset += 4 - (offset + state.bsCount[nextLine]) % 4\n      } else if (ch === 0x20) {\n        offset++\n      } else {\n        break\n      }\n\n      pos++\n    }\n\n    const contentStart = pos\n    let indentAfterMarker\n\n    if (contentStart >= max) {\n      // trimming space in \"-    \\n  3\" case, indent is 1 here\n      indentAfterMarker = 1\n    } else {\n      indentAfterMarker = offset - initial\n    }\n\n    // If we have more than 4 spaces, the indent is 1\n    // (the rest is just indented code block)\n    if (indentAfterMarker > 4) { indentAfterMarker = 1 }\n\n    // \"  -  test\"\n    //  ^^^^^ - calculating total length of this thing\n    const indent = initial + indentAfterMarker\n\n    // Run subparser & write tokens\n    token        = state.push('list_item_open', 'li', 1)\n    token.markup = String.fromCharCode(markerCharCode)\n    const itemLines = [nextLine, 0]\n    token.map    = itemLines\n    if (isOrdered) {\n      token.info = state.src.slice(start, posAfterMarker - 1)\n    }\n\n    // change current state, then restore it after parser subcall\n    const oldTight = state.tight\n    const oldTShift = state.tShift[nextLine]\n    const oldSCount = state.sCount[nextLine]\n\n    //  - example list\n    // ^ listIndent position will be here\n    //   ^ blkIndent position will be here\n    //\n    const oldListIndent = state.listIndent\n    state.listIndent = state.blkIndent\n    state.blkIndent = indent\n\n    state.tight = true\n    state.tShift[nextLine] = contentStart - state.bMarks[nextLine]\n    state.sCount[nextLine] = offset\n\n    if (contentStart >= max && state.isEmpty(nextLine + 1)) {\n      // workaround for this case\n      // (list item is empty, list terminates before \"foo\"):\n      // ~~~~~~~~\n      //   -\n      //\n      //     foo\n      // ~~~~~~~~\n      state.line = Math.min(state.line + 2, endLine)\n    } else {\n      state.md.block.tokenize(state, nextLine, endLine, true)\n    }\n\n    // If any of list item is tight, mark list as tight\n    if (!state.tight || prevEmptyEnd) {\n      tight = false\n    }\n    // Item become loose if finish with empty line,\n    // but we should filter last element, because it means list finish\n    prevEmptyEnd = (state.line - nextLine) > 1 && state.isEmpty(state.line - 1)\n\n    state.blkIndent = state.listIndent\n    state.listIndent = oldListIndent\n    state.tShift[nextLine] = oldTShift\n    state.sCount[nextLine] = oldSCount\n    state.tight = oldTight\n\n    token        = state.push('list_item_close', 'li', -1)\n    token.markup = String.fromCharCode(markerCharCode)\n\n    nextLine = state.line\n    itemLines[1] = nextLine\n\n    if (nextLine >= endLine) { break }\n\n    //\n    // Try to check if list is terminated or continued.\n    //\n    if (state.sCount[nextLine] < state.blkIndent) { break }\n\n    // if it's indented more than 3 spaces, it should be a code block\n    if (state.sCount[nextLine] - state.blkIndent >= 4) { break }\n\n    // fail if terminating block found\n    let terminate = false\n    for (let i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true\n        break\n      }\n    }\n    if (terminate) { break }\n\n    // fail if list has another type\n    if (isOrdered) {\n      posAfterMarker = skipOrderedListMarker(state, nextLine)\n      if (posAfterMarker < 0) { break }\n      start = state.bMarks[nextLine] + state.tShift[nextLine]\n    } else {\n      posAfterMarker = skipBulletListMarker(state, nextLine)\n      if (posAfterMarker < 0) { break }\n    }\n\n    if (markerCharCode !== state.src.charCodeAt(posAfterMarker - 1)) { break }\n  }\n\n  // Finalize list\n  if (isOrdered) {\n    token = state.push('ordered_list_close', 'ol', -1)\n  } else {\n    token = state.push('bullet_list_close', 'ul', -1)\n  }\n  token.markup = String.fromCharCode(markerCharCode)\n\n  listLines[1] = nextLine\n  state.line = nextLine\n\n  state.parentType = oldParentType\n\n  // mark paragraphs tight if needed\n  if (tight) {\n    markTightParagraphs(state, listTokIdx)\n  }\n\n  return true\n}\n", "import { isSpace, normalizeReference } from '../common/utils.mjs'\n\nexport default function reference (state, startLine, _endLine, silent) {\n  let pos = state.bMarks[startLine] + state.tShift[startLine]\n  let max = state.eMarks[startLine]\n  let nextLine = startLine + 1\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false }\n\n  if (state.src.charCodeAt(pos) !== 0x5B/* [ */) { return false }\n\n  function getNextLine (nextLine) {\n    const endLine = state.lineMax\n\n    if (nextLine >= endLine || state.isEmpty(nextLine)) {\n      // empty line or end of input\n      return null\n    }\n\n    let isContinuation = false\n\n    // this would be a code block normally, but after paragraph\n    // it's considered a lazy continuation regardless of what's there\n    if (state.sCount[nextLine] - state.blkIndent > 3) { isContinuation = true }\n\n    // quirk for blockquotes, this line should already be checked by that rule\n    if (state.sCount[nextLine] < 0) { isContinuation = true }\n\n    if (!isContinuation) {\n      const terminatorRules = state.md.block.ruler.getRules('reference')\n      const oldParentType = state.parentType\n      state.parentType = 'reference'\n\n      // Some tags can terminate paragraph without empty line.\n      let terminate = false\n      for (let i = 0, l = terminatorRules.length; i < l; i++) {\n        if (terminatorRules[i](state, nextLine, endLine, true)) {\n          terminate = true\n          break\n        }\n      }\n\n      state.parentType = oldParentType\n      if (terminate) {\n        // terminated by another block\n        return null\n      }\n    }\n\n    const pos = state.bMarks[nextLine] + state.tShift[nextLine]\n    const max = state.eMarks[nextLine]\n\n    // max + 1 explicitly includes the newline\n    return state.src.slice(pos, max + 1)\n  }\n\n  let str = state.src.slice(pos, max + 1)\n\n  max = str.length\n  let labelEnd = -1\n\n  for (pos = 1; pos < max; pos++) {\n    const ch = str.charCodeAt(pos)\n    if (ch === 0x5B /* [ */) {\n      return false\n    } else if (ch === 0x5D /* ] */) {\n      labelEnd = pos\n      break\n    } else if (ch === 0x0A /* \\n */) {\n      const lineContent = getNextLine(nextLine)\n      if (lineContent !== null) {\n        str += lineContent\n        max = str.length\n        nextLine++\n      }\n    } else if (ch === 0x5C /* \\ */) {\n      pos++\n      if (pos < max && str.charCodeAt(pos) === 0x0A) {\n        const lineContent = getNextLine(nextLine)\n        if (lineContent !== null) {\n          str += lineContent\n          max = str.length\n          nextLine++\n        }\n      }\n    }\n  }\n\n  if (labelEnd < 0 || str.charCodeAt(labelEnd + 1) !== 0x3A/* : */) { return false }\n\n  // [label]:   destination   'title'\n  //         ^^^ skip optional whitespace here\n  for (pos = labelEnd + 2; pos < max; pos++) {\n    const ch = str.charCodeAt(pos)\n    if (ch === 0x0A) {\n      const lineContent = getNextLine(nextLine)\n      if (lineContent !== null) {\n        str += lineContent\n        max = str.length\n        nextLine++\n      }\n    } else if (isSpace(ch)) {\n      /* eslint no-empty:0 */\n    } else {\n      break\n    }\n  }\n\n  // [label]:   destination   'title'\n  //            ^^^^^^^^^^^ parse this\n  const destRes = state.md.helpers.parseLinkDestination(str, pos, max)\n  if (!destRes.ok) { return false }\n\n  const href = state.md.normalizeLink(destRes.str)\n  if (!state.md.validateLink(href)) { return false }\n\n  pos = destRes.pos\n\n  // save cursor state, we could require to rollback later\n  const destEndPos = pos\n  const destEndLineNo = nextLine\n\n  // [label]:   destination   'title'\n  //                       ^^^ skipping those spaces\n  const start = pos\n  for (; pos < max; pos++) {\n    const ch = str.charCodeAt(pos)\n    if (ch === 0x0A) {\n      const lineContent = getNextLine(nextLine)\n      if (lineContent !== null) {\n        str += lineContent\n        max = str.length\n        nextLine++\n      }\n    } else if (isSpace(ch)) {\n      /* eslint no-empty:0 */\n    } else {\n      break\n    }\n  }\n\n  // [label]:   destination   'title'\n  //                          ^^^^^^^ parse this\n  let titleRes = state.md.helpers.parseLinkTitle(str, pos, max)\n  while (titleRes.can_continue) {\n    const lineContent = getNextLine(nextLine)\n    if (lineContent === null) break\n    str += lineContent\n    pos = max\n    max = str.length\n    nextLine++\n    titleRes = state.md.helpers.parseLinkTitle(str, pos, max, titleRes)\n  }\n  let title\n\n  if (pos < max && start !== pos && titleRes.ok) {\n    title = titleRes.str\n    pos = titleRes.pos\n  } else {\n    title = ''\n    pos = destEndPos\n    nextLine = destEndLineNo\n  }\n\n  // skip trailing spaces until the rest of the line\n  while (pos < max) {\n    const ch = str.charCodeAt(pos)\n    if (!isSpace(ch)) { break }\n    pos++\n  }\n\n  if (pos < max && str.charCodeAt(pos) !== 0x0A) {\n    if (title) {\n      // garbage at the end of the line after title,\n      // but it could still be a valid reference if we roll back\n      title = ''\n      pos = destEndPos\n      nextLine = destEndLineNo\n      while (pos < max) {\n        const ch = str.charCodeAt(pos)\n        if (!isSpace(ch)) { break }\n        pos++\n      }\n    }\n  }\n\n  if (pos < max && str.charCodeAt(pos) !== 0x0A) {\n    // garbage at the end of the line\n    return false\n  }\n\n  const label = normalizeReference(str.slice(1, labelEnd))\n  if (!label) {\n    // CommonMark 0.20 disallows empty labels\n    return false\n  }\n\n  // Reference can not terminate anything. This check is for safety only.\n  /* istanbul ignore if */\n  if (silent) { return true }\n\n  if (typeof state.env.references === 'undefined') {\n    state.env.references = {}\n  }\n  if (typeof state.env.references[label] === 'undefined') {\n    state.env.references[label] = { title, href }\n  }\n\n  state.line = nextLine\n  return true\n}\n", "// List of valid html blocks names, according to commonmark spec\n// https://spec.commonmark.org/0.30/#html-blocks\n\nexport default [\n  'address',\n  'article',\n  'aside',\n  'base',\n  'basefont',\n  'blockquote',\n  'body',\n  'caption',\n  'center',\n  'col',\n  'colgroup',\n  'dd',\n  'details',\n  'dialog',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'frame',\n  'frameset',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hr',\n  'html',\n  'iframe',\n  'legend',\n  'li',\n  'link',\n  'main',\n  'menu',\n  'menuitem',\n  'nav',\n  'noframes',\n  'ol',\n  'optgroup',\n  'option',\n  'p',\n  'param',\n  'search',\n  'section',\n  'summary',\n  'table',\n  'tbody',\n  'td',\n  'tfoot',\n  'th',\n  'thead',\n  'title',\n  'tr',\n  'track',\n  'ul'\n]\n", "// Regexps to match html elements\n\nconst attr_name     = '[a-zA-Z_:][a-zA-Z0-9:._-]*'\n\nconst unquoted      = '[^\"\\'=<>`\\\\x00-\\\\x20]+'\nconst single_quoted = \"'[^']*'\"\nconst double_quoted = '\"[^\"]*\"'\n\nconst attr_value  = '(?:' + unquoted + '|' + single_quoted + '|' + double_quoted + ')'\n\nconst attribute   = '(?:\\\\s+' + attr_name + '(?:\\\\s*=\\\\s*' + attr_value + ')?)'\n\nconst open_tag    = '<[A-Za-z][A-Za-z0-9\\\\-]*' + attribute + '*\\\\s*\\\\/?>'\n\nconst close_tag   = '<\\\\/[A-Za-z][A-Za-z0-9\\\\-]*\\\\s*>'\nconst comment     = '<!---?>|<!--(?:[^-]|-[^-]|--[^>])*-->'\nconst processing  = '<[?][\\\\s\\\\S]*?[?]>'\nconst declaration = '<![A-Za-z][^>]*>'\nconst cdata       = '<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>'\n\nconst HTML_TAG_RE = new RegExp('^(?:' + open_tag + '|' + close_tag + '|' + comment +\n                        '|' + processing + '|' + declaration + '|' + cdata + ')')\nconst HTML_OPEN_CLOSE_TAG_RE = new RegExp('^(?:' + open_tag + '|' + close_tag + ')')\n\nexport { HTML_TAG_RE, HTML_OPEN_CLOSE_TAG_RE }\n", "// HTML block\n\nimport block_names from '../common/html_blocks.mjs'\nimport { HTML_OPEN_CLOSE_TAG_RE } from '../common/html_re.mjs'\n\n// An array of opening and corresponding closing sequences for html tags,\n// last argument defines whether it can terminate a paragraph or not\n//\nconst HTML_SEQUENCES = [\n  [/^<(script|pre|style|textarea)(?=(\\s|>|$))/i, /<\\/(script|pre|style|textarea)>/i, true],\n  [/^<!--/,        /-->/,   true],\n  [/^<\\?/,         /\\?>/,   true],\n  [/^<![A-Z]/,     />/,     true],\n  [/^<!\\[CDATA\\[/, /\\]\\]>/, true],\n  [new RegExp('^</?(' + block_names.join('|') + ')(?=(\\\\s|/?>|$))', 'i'), /^$/, true],\n  [new RegExp(HTML_OPEN_CLOSE_TAG_RE.source + '\\\\s*$'),  /^$/, false]\n]\n\nexport default function html_block (state, startLine, endLine, silent) {\n  let pos = state.bMarks[startLine] + state.tShift[startLine]\n  let max = state.eMarks[startLine]\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false }\n\n  if (!state.md.options.html) { return false }\n\n  if (state.src.charCodeAt(pos) !== 0x3C/* < */) { return false }\n\n  let lineText = state.src.slice(pos, max)\n\n  let i = 0\n  for (; i < HTML_SEQUENCES.length; i++) {\n    if (HTML_SEQUENCES[i][0].test(lineText)) { break }\n  }\n  if (i === HTML_SEQUENCES.length) { return false }\n\n  if (silent) {\n    // true if this sequence can be a terminator, false otherwise\n    return HTML_SEQUENCES[i][2]\n  }\n\n  let nextLine = startLine + 1\n\n  // If we are here - we detected HTML block.\n  // Let's roll down till block end.\n  if (!HTML_SEQUENCES[i][1].test(lineText)) {\n    for (; nextLine < endLine; nextLine++) {\n      if (state.sCount[nextLine] < state.blkIndent) { break }\n\n      pos = state.bMarks[nextLine] + state.tShift[nextLine]\n      max = state.eMarks[nextLine]\n      lineText = state.src.slice(pos, max)\n\n      if (HTML_SEQUENCES[i][1].test(lineText)) {\n        if (lineText.length !== 0) { nextLine++ }\n        break\n      }\n    }\n  }\n\n  state.line = nextLine\n\n  const token   = state.push('html_block', '', 0)\n  token.map     = [startLine, nextLine]\n  token.content = state.getLines(startLine, nextLine, state.blkIndent, true)\n\n  return true\n}\n", "// heading (#, ##, ...)\n\nimport { isSpace } from '../common/utils.mjs'\n\nexport default function heading (state, startLine, endLine, silent) {\n  let pos = state.bMarks[startLine] + state.tShift[startLine]\n  let max = state.eMarks[startLine]\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false }\n\n  let ch  = state.src.charCodeAt(pos)\n\n  if (ch !== 0x23/* # */ || pos >= max) { return false }\n\n  // count heading level\n  let level = 1\n  ch = state.src.charCodeAt(++pos)\n  while (ch === 0x23/* # */ && pos < max && level <= 6) {\n    level++\n    ch = state.src.charCodeAt(++pos)\n  }\n\n  if (level > 6 || (pos < max && !isSpace(ch))) { return false }\n\n  if (silent) { return true }\n\n  // Let's cut tails like '    ###  ' from the end of string\n\n  max = state.skipSpacesBack(max, pos)\n  const tmp = state.skipCharsBack(max, 0x23, pos) // #\n  if (tmp > pos && isSpace(state.src.charCodeAt(tmp - 1))) {\n    max = tmp\n  }\n\n  state.line = startLine + 1\n\n  const token_o  = state.push('heading_open', 'h' + String(level), 1)\n  token_o.markup = '########'.slice(0, level)\n  token_o.map    = [startLine, state.line]\n\n  const token_i    = state.push('inline', '', 0)\n  token_i.content  = state.src.slice(pos, max).trim()\n  token_i.map      = [startLine, state.line]\n  token_i.children = []\n\n  const token_c  = state.push('heading_close', 'h' + String(level), -1)\n  token_c.markup = '########'.slice(0, level)\n\n  return true\n}\n", "// lheading (---, ===)\n\nexport default function lheading (state, startLine, endLine/*, silent */) {\n  const terminatorRules = state.md.block.ruler.getRules('paragraph')\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false }\n\n  const oldParentType = state.parentType\n  state.parentType = 'paragraph' // use paragraph to match terminatorRules\n\n  // jump line-by-line until empty one or EOF\n  let level = 0\n  let marker\n  let nextLine = startLine + 1\n\n  for (; nextLine < endLine && !state.isEmpty(nextLine); nextLine++) {\n    // this would be a code block normally, but after paragraph\n    // it's considered a lazy continuation regardless of what's there\n    if (state.sCount[nextLine] - state.blkIndent > 3) { continue }\n\n    //\n    // Check for underline in setext header\n    //\n    if (state.sCount[nextLine] >= state.blkIndent) {\n      let pos = state.bMarks[nextLine] + state.tShift[nextLine]\n      const max = state.eMarks[nextLine]\n\n      if (pos < max) {\n        marker = state.src.charCodeAt(pos)\n\n        if (marker === 0x2D/* - */ || marker === 0x3D/* = */) {\n          pos = state.skipChars(pos, marker)\n          pos = state.skipSpaces(pos)\n\n          if (pos >= max) {\n            level = (marker === 0x3D/* = */ ? 1 : 2)\n            break\n          }\n        }\n      }\n    }\n\n    // quirk for blockquotes, this line should already be checked by that rule\n    if (state.sCount[nextLine] < 0) { continue }\n\n    // Some tags can terminate paragraph without empty line.\n    let terminate = false\n    for (let i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true\n        break\n      }\n    }\n    if (terminate) { break }\n  }\n\n  if (!level) {\n    // Didn't find valid underline\n    return false\n  }\n\n  const content = state.getLines(startLine, nextLine, state.blkIndent, false).trim()\n\n  state.line = nextLine + 1\n\n  const token_o    = state.push('heading_open', 'h' + String(level), 1)\n  token_o.markup   = String.fromCharCode(marker)\n  token_o.map      = [startLine, state.line]\n\n  const token_i    = state.push('inline', '', 0)\n  token_i.content  = content\n  token_i.map      = [startLine, state.line - 1]\n  token_i.children = []\n\n  const token_c    = state.push('heading_close', 'h' + String(level), -1)\n  token_c.markup   = String.fromCharCode(marker)\n\n  state.parentType = oldParentType\n\n  return true\n}\n", "// Paragraph\n\nexport default function paragraph (state, startLine, endLine) {\n  const terminatorRules = state.md.block.ruler.getRules('paragraph')\n  const oldParentType = state.parentType\n  let nextLine = startLine + 1\n  state.parentType = 'paragraph'\n\n  // jump line-by-line until empty one or EOF\n  for (; nextLine < endLine && !state.isEmpty(nextLine); nextLine++) {\n    // this would be a code block normally, but after paragraph\n    // it's considered a lazy continuation regardless of what's there\n    if (state.sCount[nextLine] - state.blkIndent > 3) { continue }\n\n    // quirk for blockquotes, this line should already be checked by that rule\n    if (state.sCount[nextLine] < 0) { continue }\n\n    // Some tags can terminate paragraph without empty line.\n    let terminate = false\n    for (let i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true\n        break\n      }\n    }\n    if (terminate) { break }\n  }\n\n  const content = state.getLines(startLine, nextLine, state.blkIndent, false).trim()\n\n  state.line = nextLine\n\n  const token_o    = state.push('paragraph_open', 'p', 1)\n  token_o.map      = [startLine, state.line]\n\n  const token_i    = state.push('inline', '', 0)\n  token_i.content  = content\n  token_i.map      = [startLine, state.line]\n  token_i.children = []\n\n  state.push('paragraph_close', 'p', -1)\n\n  state.parentType = oldParentType\n\n  return true\n}\n", "/** internal\n * class ParserBlock\n *\n * Block-level tokenizer.\n **/\n\nimport Ruler from './ruler.mjs'\nimport StateBlock from './rules_block/state_block.mjs'\n\nimport r_table from './rules_block/table.mjs'\nimport r_code from './rules_block/code.mjs'\nimport r_fence from './rules_block/fence.mjs'\nimport r_blockquote from './rules_block/blockquote.mjs'\nimport r_hr from './rules_block/hr.mjs'\nimport r_list from './rules_block/list.mjs'\nimport r_reference from './rules_block/reference.mjs'\nimport r_html_block from './rules_block/html_block.mjs'\nimport r_heading from './rules_block/heading.mjs'\nimport r_lheading from './rules_block/lheading.mjs'\nimport r_paragraph from './rules_block/paragraph.mjs'\n\nconst _rules = [\n  // First 2 params - rule name & source. Secondary array - list of rules,\n  // which can be terminated by this one.\n  ['table',      r_table,      ['paragraph', 'reference']],\n  ['code',       r_code],\n  ['fence',      r_fence,      ['paragraph', 'reference', 'blockquote', 'list']],\n  ['blockquote', r_blockquote, ['paragraph', 'reference', 'blockquote', 'list']],\n  ['hr',         r_hr,         ['paragraph', 'reference', 'blockquote', 'list']],\n  ['list',       r_list,       ['paragraph', 'reference', 'blockquote']],\n  ['reference',  r_reference],\n  ['html_block', r_html_block, ['paragraph', 'reference', 'blockquote']],\n  ['heading',    r_heading,    ['paragraph', 'reference', 'blockquote']],\n  ['lheading',   r_lheading],\n  ['paragraph',  r_paragraph]\n]\n\n/**\n * new ParserBlock()\n **/\nfunction ParserBlock () {\n  /**\n   * ParserBlock#ruler -> Ruler\n   *\n   * [[Ruler]] instance. Keep configuration of block rules.\n   **/\n  this.ruler = new Ruler()\n\n  for (let i = 0; i < _rules.length; i++) {\n    this.ruler.push(_rules[i][0], _rules[i][1], { alt: (_rules[i][2] || []).slice() })\n  }\n}\n\n// Generate tokens for input range\n//\nParserBlock.prototype.tokenize = function (state, startLine, endLine) {\n  const rules = this.ruler.getRules('')\n  const len = rules.length\n  const maxNesting = state.md.options.maxNesting\n  let line = startLine\n  let hasEmptyLines = false\n\n  while (line < endLine) {\n    state.line = line = state.skipEmptyLines(line)\n    if (line >= endLine) { break }\n\n    // Termination condition for nested calls.\n    // Nested calls currently used for blockquotes & lists\n    if (state.sCount[line] < state.blkIndent) { break }\n\n    // If nesting level exceeded - skip tail to the end. That's not ordinary\n    // situation and we should not care about content.\n    if (state.level >= maxNesting) {\n      state.line = endLine\n      break\n    }\n\n    // Try all possible rules.\n    // On success, rule should:\n    //\n    // - update `state.line`\n    // - update `state.tokens`\n    // - return true\n    const prevLine = state.line\n    let ok = false\n\n    for (let i = 0; i < len; i++) {\n      ok = rules[i](state, line, endLine, false)\n      if (ok) {\n        if (prevLine >= state.line) {\n          throw new Error(\"block rule didn't increment state.line\")\n        }\n        break\n      }\n    }\n\n    // this can only happen if user disables paragraph rule\n    if (!ok) throw new Error('none of the block rules matched')\n\n    // set state.tight if we had an empty line before current tag\n    // i.e. latest empty line should not count\n    state.tight = !hasEmptyLines\n\n    // paragraph might \"eat\" one newline after it in nested lists\n    if (state.isEmpty(state.line - 1)) {\n      hasEmptyLines = true\n    }\n\n    line = state.line\n\n    if (line < endLine && state.isEmpty(line)) {\n      hasEmptyLines = true\n      line++\n      state.line = line\n    }\n  }\n}\n\n/**\n * ParserBlock.parse(str, md, env, outTokens)\n *\n * Process input string and push block tokens into `outTokens`\n **/\nParserBlock.prototype.parse = function (src, md, env, outTokens) {\n  if (!src) { return }\n\n  const state = new this.State(src, md, env, outTokens)\n\n  this.tokenize(state, state.line, state.lineMax)\n}\n\nParserBlock.prototype.State = StateBlock\n\nexport default ParserBlock\n", "// Inline parser state\n\nimport Token from '../token.mjs'\nimport { isWhiteSpace, isPunctChar, isMdAsciiPunct } from '../common/utils.mjs'\n\nfunction StateInline (src, md, env, outTokens) {\n  this.src = src\n  this.env = env\n  this.md = md\n  this.tokens = outTokens\n  this.tokens_meta = Array(outTokens.length)\n\n  this.pos = 0\n  this.posMax = this.src.length\n  this.level = 0\n  this.pending = ''\n  this.pendingLevel = 0\n\n  // Stores { start: end } pairs. Useful for backtrack\n  // optimization of pairs parse (emphasis, strikes).\n  this.cache = {}\n\n  // List of emphasis-like delimiters for current tag\n  this.delimiters = []\n\n  // Stack of delimiter lists for upper level tags\n  this._prev_delimiters = []\n\n  // backtick length => last seen position\n  this.backticks = {}\n  this.backticksScanned = false\n\n  // Counter used to disable inline linkify-it execution\n  // inside <a> and markdown links\n  this.linkLevel = 0\n}\n\n// Flush pending text\n//\nStateInline.prototype.pushPending = function () {\n  const token = new Token('text', '', 0)\n  token.content = this.pending\n  token.level = this.pendingLevel\n  this.tokens.push(token)\n  this.pending = ''\n  return token\n}\n\n// Push new token to \"stream\".\n// If pending text exists - flush it as text token\n//\nStateInline.prototype.push = function (type, tag, nesting) {\n  if (this.pending) {\n    this.pushPending()\n  }\n\n  const token = new Token(type, tag, nesting)\n  let token_meta = null\n\n  if (nesting < 0) {\n    // closing tag\n    this.level--\n    this.delimiters = this._prev_delimiters.pop()\n  }\n\n  token.level = this.level\n\n  if (nesting > 0) {\n    // opening tag\n    this.level++\n    this._prev_delimiters.push(this.delimiters)\n    this.delimiters = []\n    token_meta = { delimiters: this.delimiters }\n  }\n\n  this.pendingLevel = this.level\n  this.tokens.push(token)\n  this.tokens_meta.push(token_meta)\n  return token\n}\n\n// Scan a sequence of emphasis-like markers, and determine whether\n// it can start an emphasis sequence or end an emphasis sequence.\n//\n//  - start - position to scan from (it should point at a valid marker);\n//  - canSplitWord - determine if these markers can be found inside a word\n//\nStateInline.prototype.scanDelims = function (start, canSplitWord) {\n  const max = this.posMax\n  const marker = this.src.charCodeAt(start)\n\n  // treat beginning of the line as a whitespace\n  const lastChar = start > 0 ? this.src.charCodeAt(start - 1) : 0x20\n\n  let pos = start\n  while (pos < max && this.src.charCodeAt(pos) === marker) { pos++ }\n\n  const count = pos - start\n\n  // treat end of the line as a whitespace\n  const nextChar = pos < max ? this.src.charCodeAt(pos) : 0x20\n\n  const isLastPunctChar = isMdAsciiPunct(lastChar) || isPunctChar(String.fromCharCode(lastChar))\n  const isNextPunctChar = isMdAsciiPunct(nextChar) || isPunctChar(String.fromCharCode(nextChar))\n\n  const isLastWhiteSpace = isWhiteSpace(lastChar)\n  const isNextWhiteSpace = isWhiteSpace(nextChar)\n\n  const left_flanking =\n    !isNextWhiteSpace && (!isNextPunctChar || isLastWhiteSpace || isLastPunctChar)\n  const right_flanking =\n    !isLastWhiteSpace && (!isLastPunctChar || isNextWhiteSpace || isNextPunctChar)\n\n  const can_open  = left_flanking  && (canSplitWord || !right_flanking || isLastPunctChar)\n  const can_close = right_flanking && (canSplitWord || !left_flanking  || isNextPunctChar)\n\n  return { can_open, can_close, length: count }\n}\n\n// re-export Token class to use in block rules\nStateInline.prototype.Token = Token\n\nexport default StateInline\n", "// Skip text characters for text token, place those to pending buffer\n// and increment current pos\n\n// Rule to skip pure text\n// '{}$%@~+=:' reserved for extentions\n\n// !, \", #, $, %, &, ', (, ), *, +, ,, -, ., /, :, ;, <, =, >, ?, @, [, \\, ], ^, _, `, {, |, }, or ~\n\n// !!!! Don't confuse with \"Markdown ASCII Punctuation\" chars\n// http://spec.commonmark.org/0.15/#ascii-punctuation-character\nfunction isTerminatorChar (ch) {\n  switch (ch) {\n    case 0x0A/* \\n */:\n    case 0x21/* ! */:\n    case 0x23/* # */:\n    case 0x24/* $ */:\n    case 0x25/* % */:\n    case 0x26/* & */:\n    case 0x2A/* * */:\n    case 0x2B/* + */:\n    case 0x2D/* - */:\n    case 0x3A/* : */:\n    case 0x3C/* < */:\n    case 0x3D/* = */:\n    case 0x3E/* > */:\n    case 0x40/* @ */:\n    case 0x5B/* [ */:\n    case 0x5C/* \\ */:\n    case 0x5D/* ] */:\n    case 0x5E/* ^ */:\n    case 0x5F/* _ */:\n    case 0x60/* ` */:\n    case 0x7B/* { */:\n    case 0x7D/* } */:\n    case 0x7E/* ~ */:\n      return true\n    default:\n      return false\n  }\n}\n\nexport default function text (state, silent) {\n  let pos = state.pos\n\n  while (pos < state.posMax && !isTerminatorChar(state.src.charCodeAt(pos))) {\n    pos++\n  }\n\n  if (pos === state.pos) { return false }\n\n  if (!silent) { state.pending += state.src.slice(state.pos, pos) }\n\n  state.pos = pos\n\n  return true\n}\n\n// Alternative implementation, for memory.\n//\n// It costs 10% of performance, but allows extend terminators list, if place it\n// to `ParserInline` property. Probably, will switch to it sometime, such\n// flexibility required.\n\n/*\nvar TERMINATOR_RE = /[\\n!#$%&*+\\-:<=>@[\\\\\\]^_`{}~]/;\n\nmodule.exports = function text(state, silent) {\n  var pos = state.pos,\n      idx = state.src.slice(pos).search(TERMINATOR_RE);\n\n  // first char is terminator -> empty text\n  if (idx === 0) { return false; }\n\n  // no terminator -> text till end of string\n  if (idx < 0) {\n    if (!silent) { state.pending += state.src.slice(pos); }\n    state.pos = state.src.length;\n    return true;\n  }\n\n  if (!silent) { state.pending += state.src.slice(pos, pos + idx); }\n\n  state.pos += idx;\n\n  return true;\n}; */\n", "// Process links like https://example.org/\n\n// RFC3986: scheme = ALPHA *( ALPHA / DIGIT / \"+\" / \"-\" / \".\" )\nconst SCHEME_RE = /(?:^|[^a-z0-9.+-])([a-z][a-z0-9.+-]*)$/i\n\nexport default function linkify (state, silent) {\n  if (!state.md.options.linkify) return false\n  if (state.linkLevel > 0) return false\n\n  const pos = state.pos\n  const max = state.posMax\n\n  if (pos + 3 > max) return false\n  if (state.src.charCodeAt(pos) !== 0x3A/* : */) return false\n  if (state.src.charCodeAt(pos + 1) !== 0x2F/* / */) return false\n  if (state.src.charCodeAt(pos + 2) !== 0x2F/* / */) return false\n\n  const match = state.pending.match(SCHEME_RE)\n  if (!match) return false\n\n  const proto = match[1]\n\n  const link = state.md.linkify.matchAtStart(state.src.slice(pos - proto.length))\n  if (!link) return false\n\n  let url = link.url\n\n  // invalid link, but still detected by linkify somehow;\n  // need to check to prevent infinite loop below\n  if (url.length <= proto.length) return false\n\n  // disallow '*' at the end of the link (conflicts with emphasis)\n  url = url.replace(/\\*+$/, '')\n\n  const fullUrl = state.md.normalizeLink(url)\n  if (!state.md.validateLink(fullUrl)) return false\n\n  if (!silent) {\n    state.pending = state.pending.slice(0, -proto.length)\n\n    const token_o = state.push('link_open', 'a', 1)\n    token_o.attrs = [['href', fullUrl]]\n    token_o.markup = 'linkify'\n    token_o.info = 'auto'\n\n    const token_t = state.push('text', '', 0)\n    token_t.content = state.md.normalizeLinkText(url)\n\n    const token_c = state.push('link_close', 'a', -1)\n    token_c.markup = 'linkify'\n    token_c.info = 'auto'\n  }\n\n  state.pos += url.length - proto.length\n  return true\n}\n", "// Proceess '\\n'\n\nimport { isSpace } from '../common/utils.mjs'\n\nexport default function newline (state, silent) {\n  let pos = state.pos\n\n  if (state.src.charCodeAt(pos) !== 0x0A/* \\n */) { return false }\n\n  const pmax = state.pending.length - 1\n  const max = state.posMax\n\n  // '  \\n' -> hardbreak\n  // Lookup in pending chars is bad practice! Don't copy to other rules!\n  // Pending string is stored in concat mode, indexed lookups will cause\n  // convertion to flat mode.\n  if (!silent) {\n    if (pmax >= 0 && state.pending.charCodeAt(pmax) === 0x20) {\n      if (pmax >= 1 && state.pending.charCodeAt(pmax - 1) === 0x20) {\n        // Find whitespaces tail of pending chars.\n        let ws = pmax - 1\n        while (ws >= 1 && state.pending.charCodeAt(ws - 1) === 0x20) ws--\n\n        state.pending = state.pending.slice(0, ws)\n        state.push('hardbreak', 'br', 0)\n      } else {\n        state.pending = state.pending.slice(0, -1)\n        state.push('softbreak', 'br', 0)\n      }\n    } else {\n      state.push('softbreak', 'br', 0)\n    }\n  }\n\n  pos++\n\n  // skip heading spaces for next line\n  while (pos < max && isSpace(state.src.charCodeAt(pos))) { pos++ }\n\n  state.pos = pos\n  return true\n}\n", "// Process escaped chars and hardbreaks\n\nimport { isSpace } from '../common/utils.mjs'\n\nconst ESCAPED = []\n\nfor (let i = 0; i < 256; i++) { ESCAPED.push(0) }\n\n'\\\\!\"#$%&\\'()*+,./:;<=>?@[]^_`{|}~-'\n  .split('').forEach(function (ch) { ESCAPED[ch.charCodeAt(0)] = 1 })\n\nexport default function escape (state, silent) {\n  let pos = state.pos\n  const max = state.posMax\n\n  if (state.src.charCodeAt(pos) !== 0x5C/* \\ */) return false\n  pos++\n\n  // '\\' at the end of the inline block\n  if (pos >= max) return false\n\n  let ch1 = state.src.charCodeAt(pos)\n\n  if (ch1 === 0x0A) {\n    if (!silent) {\n      state.push('hardbreak', 'br', 0)\n    }\n\n    pos++\n    // skip leading whitespaces from next line\n    while (pos < max) {\n      ch1 = state.src.charCodeAt(pos)\n      if (!isSpace(ch1)) break\n      pos++\n    }\n\n    state.pos = pos\n    return true\n  }\n\n  let escapedStr = state.src[pos]\n\n  if (ch1 >= 0xD800 && ch1 <= 0xDBFF && pos + 1 < max) {\n    const ch2 = state.src.charCodeAt(pos + 1)\n\n    if (ch2 >= 0xDC00 && ch2 <= 0xDFFF) {\n      escapedStr += state.src[pos + 1]\n      pos++\n    }\n  }\n\n  const origStr = '\\\\' + escapedStr\n\n  if (!silent) {\n    const token = state.push('text_special', '', 0)\n\n    if (ch1 < 256 && ESCAPED[ch1] !== 0) {\n      token.content = escapedStr\n    } else {\n      token.content = origStr\n    }\n\n    token.markup = origStr\n    token.info   = 'escape'\n  }\n\n  state.pos = pos + 1\n  return true\n}\n", "// Parse backticks\n\nexport default function backtick (state, silent) {\n  let pos = state.pos\n  const ch = state.src.charCodeAt(pos)\n\n  if (ch !== 0x60/* ` */) { return false }\n\n  const start = pos\n  pos++\n  const max = state.posMax\n\n  // scan marker length\n  while (pos < max && state.src.charCodeAt(pos) === 0x60/* ` */) { pos++ }\n\n  const marker = state.src.slice(start, pos)\n  const openerLength = marker.length\n\n  if (state.backticksScanned && (state.backticks[openerLength] || 0) <= start) {\n    if (!silent) state.pending += marker\n    state.pos += openerLength\n    return true\n  }\n\n  let matchEnd = pos\n  let matchStart\n\n  // Nothing found in the cache, scan until the end of the line (or until marker is found)\n  while ((matchStart = state.src.indexOf('`', matchEnd)) !== -1) {\n    matchEnd = matchStart + 1\n\n    // scan marker length\n    while (matchEnd < max && state.src.charCodeAt(matchEnd) === 0x60/* ` */) { matchEnd++ }\n\n    const closerLength = matchEnd - matchStart\n\n    if (closerLength === openerLength) {\n      // Found matching closer length.\n      if (!silent) {\n        const token = state.push('code_inline', 'code', 0)\n        token.markup = marker\n        token.content = state.src.slice(pos, matchStart)\n          .replace(/\\n/g, ' ')\n          .replace(/^ (.+) $/, '$1')\n      }\n      state.pos = matchEnd\n      return true\n    }\n\n    // Some different length found, put it in cache as upper limit of where closer can be found\n    state.backticks[closerLength] = matchStart\n  }\n\n  // Scanned through the end, didn't find anything\n  state.backticksScanned = true\n\n  if (!silent) state.pending += marker\n  state.pos += openerLength\n  return true\n}\n", "// ~~strike through~~\n//\n\n// Insert each marker as a separate text token, and add it to delimiter list\n//\nfunction strikethrough_tokenize (state, silent) {\n  const start = state.pos\n  const marker = state.src.charCodeAt(start)\n\n  if (silent) { return false }\n\n  if (marker !== 0x7E/* ~ */) { return false }\n\n  const scanned = state.scanDelims(state.pos, true)\n  let len = scanned.length\n  const ch = String.fromCharCode(marker)\n\n  if (len < 2) { return false }\n\n  let token\n\n  if (len % 2) {\n    token         = state.push('text', '', 0)\n    token.content = ch\n    len--\n  }\n\n  for (let i = 0; i < len; i += 2) {\n    token         = state.push('text', '', 0)\n    token.content = ch + ch\n\n    state.delimiters.push({\n      marker,\n      length: 0,     // disable \"rule of 3\" length checks meant for emphasis\n      token: state.tokens.length - 1,\n      end: -1,\n      open: scanned.can_open,\n      close: scanned.can_close\n    })\n  }\n\n  state.pos += scanned.length\n\n  return true\n}\n\nfunction postProcess (state, delimiters) {\n  let token\n  const loneMarkers = []\n  const max = delimiters.length\n\n  for (let i = 0; i < max; i++) {\n    const startDelim = delimiters[i]\n\n    if (startDelim.marker !== 0x7E/* ~ */) {\n      continue\n    }\n\n    if (startDelim.end === -1) {\n      continue\n    }\n\n    const endDelim = delimiters[startDelim.end]\n\n    token         = state.tokens[startDelim.token]\n    token.type    = 's_open'\n    token.tag     = 's'\n    token.nesting = 1\n    token.markup  = '~~'\n    token.content = ''\n\n    token         = state.tokens[endDelim.token]\n    token.type    = 's_close'\n    token.tag     = 's'\n    token.nesting = -1\n    token.markup  = '~~'\n    token.content = ''\n\n    if (state.tokens[endDelim.token - 1].type === 'text' &&\n        state.tokens[endDelim.token - 1].content === '~') {\n      loneMarkers.push(endDelim.token - 1)\n    }\n  }\n\n  // If a marker sequence has an odd number of characters, it's splitted\n  // like this: `~~~~~` -> `~` + `~~` + `~~`, leaving one marker at the\n  // start of the sequence.\n  //\n  // So, we have to move all those markers after subsequent s_close tags.\n  //\n  while (loneMarkers.length) {\n    const i = loneMarkers.pop()\n    let j = i + 1\n\n    while (j < state.tokens.length && state.tokens[j].type === 's_close') {\n      j++\n    }\n\n    j--\n\n    if (i !== j) {\n      token = state.tokens[j]\n      state.tokens[j] = state.tokens[i]\n      state.tokens[i] = token\n    }\n  }\n}\n\n// Walk through delimiter list and replace text tokens with tags\n//\nfunction strikethrough_postProcess (state) {\n  const tokens_meta = state.tokens_meta\n  const max = state.tokens_meta.length\n\n  postProcess(state, state.delimiters)\n\n  for (let curr = 0; curr < max; curr++) {\n    if (tokens_meta[curr] && tokens_meta[curr].delimiters) {\n      postProcess(state, tokens_meta[curr].delimiters)\n    }\n  }\n}\n\nexport default {\n  tokenize: strikethrough_tokenize,\n  postProcess: strikethrough_postProcess\n}\n", "// Process *this* and _that_\n//\n\n// Insert each marker as a separate text token, and add it to delimiter list\n//\nfunction emphasis_tokenize (state, silent) {\n  const start = state.pos\n  const marker = state.src.charCodeAt(start)\n\n  if (silent) { return false }\n\n  if (marker !== 0x5F /* _ */ && marker !== 0x2A /* * */) { return false }\n\n  const scanned = state.scanDelims(state.pos, marker === 0x2A)\n\n  for (let i = 0; i < scanned.length; i++) {\n    const token = state.push('text', '', 0)\n    token.content = String.fromCharCode(marker)\n\n    state.delimiters.push({\n      // Char code of the starting marker (number).\n      //\n      marker,\n\n      // Total length of these series of delimiters.\n      //\n      length: scanned.length,\n\n      // A position of the token this delimiter corresponds to.\n      //\n      token: state.tokens.length - 1,\n\n      // If this delimiter is matched as a valid opener, `end` will be\n      // equal to its position, otherwise it's `-1`.\n      //\n      end: -1,\n\n      // Boolean flags that determine if this delimiter could open or close\n      // an emphasis.\n      //\n      open: scanned.can_open,\n      close: scanned.can_close\n    })\n  }\n\n  state.pos += scanned.length\n\n  return true\n}\n\nfunction postProcess (state, delimiters) {\n  const max = delimiters.length\n\n  for (let i = max - 1; i >= 0; i--) {\n    const startDelim = delimiters[i]\n\n    if (startDelim.marker !== 0x5F/* _ */ && startDelim.marker !== 0x2A/* * */) {\n      continue\n    }\n\n    // Process only opening markers\n    if (startDelim.end === -1) {\n      continue\n    }\n\n    const endDelim = delimiters[startDelim.end]\n\n    // If the previous delimiter has the same marker and is adjacent to this one,\n    // merge those into one strong delimiter.\n    //\n    // `<em><em>whatever</em></em>` -> `<strong>whatever</strong>`\n    //\n    const isStrong = i > 0 &&\n               delimiters[i - 1].end === startDelim.end + 1 &&\n               // check that first two markers match and adjacent\n               delimiters[i - 1].marker === startDelim.marker &&\n               delimiters[i - 1].token === startDelim.token - 1 &&\n               // check that last two markers are adjacent (we can safely assume they match)\n               delimiters[startDelim.end + 1].token === endDelim.token + 1\n\n    const ch = String.fromCharCode(startDelim.marker)\n\n    const token_o   = state.tokens[startDelim.token]\n    token_o.type    = isStrong ? 'strong_open' : 'em_open'\n    token_o.tag     = isStrong ? 'strong' : 'em'\n    token_o.nesting = 1\n    token_o.markup  = isStrong ? ch + ch : ch\n    token_o.content = ''\n\n    const token_c   = state.tokens[endDelim.token]\n    token_c.type    = isStrong ? 'strong_close' : 'em_close'\n    token_c.tag     = isStrong ? 'strong' : 'em'\n    token_c.nesting = -1\n    token_c.markup  = isStrong ? ch + ch : ch\n    token_c.content = ''\n\n    if (isStrong) {\n      state.tokens[delimiters[i - 1].token].content = ''\n      state.tokens[delimiters[startDelim.end + 1].token].content = ''\n      i--\n    }\n  }\n}\n\n// Walk through delimiter list and replace text tokens with tags\n//\nfunction emphasis_post_process (state) {\n  const tokens_meta = state.tokens_meta\n  const max = state.tokens_meta.length\n\n  postProcess(state, state.delimiters)\n\n  for (let curr = 0; curr < max; curr++) {\n    if (tokens_meta[curr] && tokens_meta[curr].delimiters) {\n      postProcess(state, tokens_meta[curr].delimiters)\n    }\n  }\n}\n\nexport default {\n  tokenize: emphasis_tokenize,\n  postProcess: emphasis_post_process\n}\n", "// Process [link](<to> \"stuff\")\n\nimport { normalizeReference, isSpace } from '../common/utils.mjs'\n\nexport default function link (state, silent) {\n  let code, label, res, ref\n  let href = ''\n  let title = ''\n  let start = state.pos\n  let parseReference = true\n\n  if (state.src.charCodeAt(state.pos) !== 0x5B/* [ */) { return false }\n\n  const oldPos = state.pos\n  const max = state.posMax\n  const labelStart = state.pos + 1\n  const labelEnd = state.md.helpers.parseLinkLabel(state, state.pos, true)\n\n  // parser failed to find ']', so it's not a valid link\n  if (labelEnd < 0) { return false }\n\n  let pos = labelEnd + 1\n  if (pos < max && state.src.charCodeAt(pos) === 0x28/* ( */) {\n    //\n    // Inline link\n    //\n\n    // might have found a valid shortcut link, disable reference parsing\n    parseReference = false\n\n    // [link](  <href>  \"title\"  )\n    //        ^^ skipping these spaces\n    pos++\n    for (; pos < max; pos++) {\n      code = state.src.charCodeAt(pos)\n      if (!isSpace(code) && code !== 0x0A) { break }\n    }\n    if (pos >= max) { return false }\n\n    // [link](  <href>  \"title\"  )\n    //          ^^^^^^ parsing link destination\n    start = pos\n    res = state.md.helpers.parseLinkDestination(state.src, pos, state.posMax)\n    if (res.ok) {\n      href = state.md.normalizeLink(res.str)\n      if (state.md.validateLink(href)) {\n        pos = res.pos\n      } else {\n        href = ''\n      }\n\n      // [link](  <href>  \"title\"  )\n      //                ^^ skipping these spaces\n      start = pos\n      for (; pos < max; pos++) {\n        code = state.src.charCodeAt(pos)\n        if (!isSpace(code) && code !== 0x0A) { break }\n      }\n\n      // [link](  <href>  \"title\"  )\n      //                  ^^^^^^^ parsing link title\n      res = state.md.helpers.parseLinkTitle(state.src, pos, state.posMax)\n      if (pos < max && start !== pos && res.ok) {\n        title = res.str\n        pos = res.pos\n\n        // [link](  <href>  \"title\"  )\n        //                         ^^ skipping these spaces\n        for (; pos < max; pos++) {\n          code = state.src.charCodeAt(pos)\n          if (!isSpace(code) && code !== 0x0A) { break }\n        }\n      }\n    }\n\n    if (pos >= max || state.src.charCodeAt(pos) !== 0x29/* ) */) {\n      // parsing a valid shortcut link failed, fallback to reference\n      parseReference = true\n    }\n    pos++\n  }\n\n  if (parseReference) {\n    //\n    // Link reference\n    //\n    if (typeof state.env.references === 'undefined') { return false }\n\n    if (pos < max && state.src.charCodeAt(pos) === 0x5B/* [ */) {\n      start = pos + 1\n      pos = state.md.helpers.parseLinkLabel(state, pos)\n      if (pos >= 0) {\n        label = state.src.slice(start, pos++)\n      } else {\n        pos = labelEnd + 1\n      }\n    } else {\n      pos = labelEnd + 1\n    }\n\n    // covers label === '' and label === undefined\n    // (collapsed reference link and shortcut reference link respectively)\n    if (!label) { label = state.src.slice(labelStart, labelEnd) }\n\n    ref = state.env.references[normalizeReference(label)]\n    if (!ref) {\n      state.pos = oldPos\n      return false\n    }\n    href = ref.href\n    title = ref.title\n  }\n\n  //\n  // We found the end of the link, and know for a fact it's a valid link;\n  // so all that's left to do is to call tokenizer.\n  //\n  if (!silent) {\n    state.pos = labelStart\n    state.posMax = labelEnd\n\n    const token_o = state.push('link_open', 'a', 1)\n    const attrs = [['href', href]]\n    token_o.attrs  = attrs\n    if (title) {\n      attrs.push(['title', title])\n    }\n\n    state.linkLevel++\n    state.md.inline.tokenize(state)\n    state.linkLevel--\n\n    state.push('link_close', 'a', -1)\n  }\n\n  state.pos = pos\n  state.posMax = max\n  return true\n}\n", "// Process ![image](<src> \"title\")\n\nimport { normalizeReference, isSpace } from '../common/utils.mjs'\n\nexport default function image (state, silent) {\n  let code, content, label, pos, ref, res, title, start\n  let href = ''\n  const oldPos = state.pos\n  const max = state.posMax\n\n  if (state.src.charCodeAt(state.pos) !== 0x21/* ! */) { return false }\n  if (state.src.charCodeAt(state.pos + 1) !== 0x5B/* [ */) { return false }\n\n  const labelStart = state.pos + 2\n  const labelEnd = state.md.helpers.parseLinkLabel(state, state.pos + 1, false)\n\n  // parser failed to find ']', so it's not a valid link\n  if (labelEnd < 0) { return false }\n\n  pos = labelEnd + 1\n  if (pos < max && state.src.charCodeAt(pos) === 0x28/* ( */) {\n    //\n    // Inline link\n    //\n\n    // [link](  <href>  \"title\"  )\n    //        ^^ skipping these spaces\n    pos++\n    for (; pos < max; pos++) {\n      code = state.src.charCodeAt(pos)\n      if (!isSpace(code) && code !== 0x0A) { break }\n    }\n    if (pos >= max) { return false }\n\n    // [link](  <href>  \"title\"  )\n    //          ^^^^^^ parsing link destination\n    start = pos\n    res = state.md.helpers.parseLinkDestination(state.src, pos, state.posMax)\n    if (res.ok) {\n      href = state.md.normalizeLink(res.str)\n      if (state.md.validateLink(href)) {\n        pos = res.pos\n      } else {\n        href = ''\n      }\n    }\n\n    // [link](  <href>  \"title\"  )\n    //                ^^ skipping these spaces\n    start = pos\n    for (; pos < max; pos++) {\n      code = state.src.charCodeAt(pos)\n      if (!isSpace(code) && code !== 0x0A) { break }\n    }\n\n    // [link](  <href>  \"title\"  )\n    //                  ^^^^^^^ parsing link title\n    res = state.md.helpers.parseLinkTitle(state.src, pos, state.posMax)\n    if (pos < max && start !== pos && res.ok) {\n      title = res.str\n      pos = res.pos\n\n      // [link](  <href>  \"title\"  )\n      //                         ^^ skipping these spaces\n      for (; pos < max; pos++) {\n        code = state.src.charCodeAt(pos)\n        if (!isSpace(code) && code !== 0x0A) { break }\n      }\n    } else {\n      title = ''\n    }\n\n    if (pos >= max || state.src.charCodeAt(pos) !== 0x29/* ) */) {\n      state.pos = oldPos\n      return false\n    }\n    pos++\n  } else {\n    //\n    // Link reference\n    //\n    if (typeof state.env.references === 'undefined') { return false }\n\n    if (pos < max && state.src.charCodeAt(pos) === 0x5B/* [ */) {\n      start = pos + 1\n      pos = state.md.helpers.parseLinkLabel(state, pos)\n      if (pos >= 0) {\n        label = state.src.slice(start, pos++)\n      } else {\n        pos = labelEnd + 1\n      }\n    } else {\n      pos = labelEnd + 1\n    }\n\n    // covers label === '' and label === undefined\n    // (collapsed reference link and shortcut reference link respectively)\n    if (!label) { label = state.src.slice(labelStart, labelEnd) }\n\n    ref = state.env.references[normalizeReference(label)]\n    if (!ref) {\n      state.pos = oldPos\n      return false\n    }\n    href = ref.href\n    title = ref.title\n  }\n\n  //\n  // We found the end of the link, and know for a fact it's a valid link;\n  // so all that's left to do is to call tokenizer.\n  //\n  if (!silent) {\n    content = state.src.slice(labelStart, labelEnd)\n\n    const tokens = []\n    state.md.inline.parse(\n      content,\n      state.md,\n      state.env,\n      tokens\n    )\n\n    const token = state.push('image', 'img', 0)\n    const attrs = [['src', href], ['alt', '']]\n    token.attrs = attrs\n    token.children = tokens\n    token.content = content\n\n    if (title) {\n      attrs.push(['title', title])\n    }\n  }\n\n  state.pos = pos\n  state.posMax = max\n  return true\n}\n", "// Process autolinks '<protocol:...>'\n\n/* eslint max-len:0 */\nconst EMAIL_RE    = /^([a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/\n/* eslint-disable-next-line no-control-regex */\nconst AUTOLINK_RE = /^([a-zA-Z][a-zA-Z0-9+.-]{1,31}):([^<>\\x00-\\x20]*)$/\n\nexport default function autolink (state, silent) {\n  let pos = state.pos\n\n  if (state.src.charCodeAt(pos) !== 0x3C/* < */) { return false }\n\n  const start = state.pos\n  const max = state.posMax\n\n  for (;;) {\n    if (++pos >= max) return false\n\n    const ch = state.src.charCodeAt(pos)\n\n    if (ch === 0x3C /* < */) return false\n    if (ch === 0x3E /* > */) break\n  }\n\n  const url = state.src.slice(start + 1, pos)\n\n  if (AUTOLINK_RE.test(url)) {\n    const fullUrl = state.md.normalizeLink(url)\n    if (!state.md.validateLink(fullUrl)) { return false }\n\n    if (!silent) {\n      const token_o   = state.push('link_open', 'a', 1)\n      token_o.attrs   = [['href', fullUrl]]\n      token_o.markup  = 'autolink'\n      token_o.info    = 'auto'\n\n      const token_t   = state.push('text', '', 0)\n      token_t.content = state.md.normalizeLinkText(url)\n\n      const token_c   = state.push('link_close', 'a', -1)\n      token_c.markup  = 'autolink'\n      token_c.info    = 'auto'\n    }\n\n    state.pos += url.length + 2\n    return true\n  }\n\n  if (EMAIL_RE.test(url)) {\n    const fullUrl = state.md.normalizeLink('mailto:' + url)\n    if (!state.md.validateLink(fullUrl)) { return false }\n\n    if (!silent) {\n      const token_o   = state.push('link_open', 'a', 1)\n      token_o.attrs   = [['href', fullUrl]]\n      token_o.markup  = 'autolink'\n      token_o.info    = 'auto'\n\n      const token_t   = state.push('text', '', 0)\n      token_t.content = state.md.normalizeLinkText(url)\n\n      const token_c   = state.push('link_close', 'a', -1)\n      token_c.markup  = 'autolink'\n      token_c.info    = 'auto'\n    }\n\n    state.pos += url.length + 2\n    return true\n  }\n\n  return false\n}\n", "// Process html tags\n\nimport { HTML_TAG_RE } from '../common/html_re.mjs'\n\nfunction isLinkOpen (str) {\n  return /^<a[>\\s]/i.test(str)\n}\nfunction isLinkClose (str) {\n  return /^<\\/a\\s*>/i.test(str)\n}\n\nfunction isLetter (ch) {\n  /* eslint no-bitwise:0 */\n  const lc = ch | 0x20 // to lower case\n  return (lc >= 0x61/* a */) && (lc <= 0x7a/* z */)\n}\n\nexport default function html_inline (state, silent) {\n  if (!state.md.options.html) { return false }\n\n  // Check start\n  const max = state.posMax\n  const pos = state.pos\n  if (state.src.charCodeAt(pos) !== 0x3C/* < */ ||\n      pos + 2 >= max) {\n    return false\n  }\n\n  // Quick fail on second char\n  const ch = state.src.charCodeAt(pos + 1)\n  if (ch !== 0x21/* ! */ &&\n      ch !== 0x3F/* ? */ &&\n      ch !== 0x2F/* / */ &&\n      !isLetter(ch)) {\n    return false\n  }\n\n  const match = state.src.slice(pos).match(HTML_TAG_RE)\n  if (!match) { return false }\n\n  if (!silent) {\n    const token = state.push('html_inline', '', 0)\n    token.content = match[0]\n\n    if (isLinkOpen(token.content))  state.linkLevel++\n    if (isLinkClose(token.content)) state.linkLevel--\n  }\n  state.pos += match[0].length\n  return true\n}\n", "// Process html entity - &#123;, &#xAF;, &quot;, ...\n\nimport { decodeHTML } from 'entities'\nimport { isValidEntityCode, fromCodePoint } from '../common/utils.mjs'\n\nconst DIGITAL_RE = /^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i\nconst NAMED_RE   = /^&([a-z][a-z0-9]{1,31});/i\n\nexport default function entity (state, silent) {\n  const pos = state.pos\n  const max = state.posMax\n\n  if (state.src.charCodeAt(pos) !== 0x26/* & */) return false\n\n  if (pos + 1 >= max) return false\n\n  const ch = state.src.charCodeAt(pos + 1)\n\n  if (ch === 0x23 /* # */) {\n    const match = state.src.slice(pos).match(DIGITAL_RE)\n    if (match) {\n      if (!silent) {\n        const code = match[1][0].toLowerCase() === 'x' ? parseInt(match[1].slice(1), 16) : parseInt(match[1], 10)\n\n        const token   = state.push('text_special', '', 0)\n        token.content = isValidEntityCode(code) ? fromCodePoint(code) : fromCodePoint(0xFFFD)\n        token.markup  = match[0]\n        token.info    = 'entity'\n      }\n      state.pos += match[0].length\n      return true\n    }\n  } else {\n    const match = state.src.slice(pos).match(NAMED_RE)\n    if (match) {\n      const decoded = decodeHTML(match[0])\n      if (decoded !== match[0]) {\n        if (!silent) {\n          const token   = state.push('text_special', '', 0)\n          token.content = decoded\n          token.markup  = match[0]\n          token.info    = 'entity'\n        }\n        state.pos += match[0].length\n        return true\n      }\n    }\n  }\n\n  return false\n}\n", "// For each opening emphasis-like marker find a matching closing one\n//\n\nfunction processDelimiters (delimiters) {\n  const openersBottom = {}\n  const max = delimiters.length\n\n  if (!max) return\n\n  // headerIdx is the first delimiter of the current (where closer is) delimiter run\n  let headerIdx = 0\n  let lastTokenIdx = -2 // needs any value lower than -1\n  const jumps = []\n\n  for (let closerIdx = 0; closerIdx < max; closerIdx++) {\n    const closer = delimiters[closerIdx]\n\n    jumps.push(0)\n\n    // markers belong to same delimiter run if:\n    //  - they have adjacent tokens\n    //  - AND markers are the same\n    //\n    if (delimiters[headerIdx].marker !== closer.marker || lastTokenIdx !== closer.token - 1) {\n      headerIdx = closerIdx\n    }\n\n    lastTokenIdx = closer.token\n\n    // Length is only used for emphasis-specific \"rule of 3\",\n    // if it's not defined (in strikethrough or 3rd party plugins),\n    // we can default it to 0 to disable those checks.\n    //\n    closer.length = closer.length || 0\n\n    if (!closer.close) continue\n\n    // Previously calculated lower bounds (previous fails)\n    // for each marker, each delimiter length modulo 3,\n    // and for whether this closer can be an opener;\n    // https://github.com/commonmark/cmark/commit/34250e12ccebdc6372b8b49c44fab57c72443460\n    /* eslint-disable-next-line no-prototype-builtins */\n    if (!openersBottom.hasOwnProperty(closer.marker)) {\n      openersBottom[closer.marker] = [-1, -1, -1, -1, -1, -1]\n    }\n\n    const minOpenerIdx = openersBottom[closer.marker][(closer.open ? 3 : 0) + (closer.length % 3)]\n\n    let openerIdx = headerIdx - jumps[headerIdx] - 1\n\n    let newMinOpenerIdx = openerIdx\n\n    for (; openerIdx > minOpenerIdx; openerIdx -= jumps[openerIdx] + 1) {\n      const opener = delimiters[openerIdx]\n\n      if (opener.marker !== closer.marker) continue\n\n      if (opener.open && opener.end < 0) {\n        let isOddMatch = false\n\n        // from spec:\n        //\n        // If one of the delimiters can both open and close emphasis, then the\n        // sum of the lengths of the delimiter runs containing the opening and\n        // closing delimiters must not be a multiple of 3 unless both lengths\n        // are multiples of 3.\n        //\n        if (opener.close || closer.open) {\n          if ((opener.length + closer.length) % 3 === 0) {\n            if (opener.length % 3 !== 0 || closer.length % 3 !== 0) {\n              isOddMatch = true\n            }\n          }\n        }\n\n        if (!isOddMatch) {\n          // If previous delimiter cannot be an opener, we can safely skip\n          // the entire sequence in future checks. This is required to make\n          // sure algorithm has linear complexity (see *_*_*_*_*_... case).\n          //\n          const lastJump = openerIdx > 0 && !delimiters[openerIdx - 1].open\n            ? jumps[openerIdx - 1] + 1\n            : 0\n\n          jumps[closerIdx] = closerIdx - openerIdx + lastJump\n          jumps[openerIdx] = lastJump\n\n          closer.open  = false\n          opener.end   = closerIdx\n          opener.close = false\n          newMinOpenerIdx = -1\n          // treat next token as start of run,\n          // it optimizes skips in **<...>**a**<...>** pathological case\n          lastTokenIdx = -2\n          break\n        }\n      }\n    }\n\n    if (newMinOpenerIdx !== -1) {\n      // If match for this delimiter run failed, we want to set lower bound for\n      // future lookups. This is required to make sure algorithm has linear\n      // complexity.\n      //\n      // See details here:\n      // https://github.com/commonmark/cmark/issues/178#issuecomment-270417442\n      //\n      openersBottom[closer.marker][(closer.open ? 3 : 0) + ((closer.length || 0) % 3)] = newMinOpenerIdx\n    }\n  }\n}\n\nexport default function link_pairs (state) {\n  const tokens_meta = state.tokens_meta\n  const max = state.tokens_meta.length\n\n  processDelimiters(state.delimiters)\n\n  for (let curr = 0; curr < max; curr++) {\n    if (tokens_meta[curr] && tokens_meta[curr].delimiters) {\n      processDelimiters(tokens_meta[curr].delimiters)\n    }\n  }\n}\n", "// Clean up tokens after emphasis and strikethrough postprocessing:\n// merge adjacent text nodes into one and re-calculate all token levels\n//\n// This is necessary because initially emphasis delimiter markers (*, _, ~)\n// are treated as their own separate text tokens. Then emphasis rule either\n// leaves them as text (needed to merge with adjacent text) or turns them\n// into opening/closing tags (which messes up levels inside).\n//\n\nexport default function fragments_join (state) {\n  let curr, last\n  let level = 0\n  const tokens = state.tokens\n  const max = state.tokens.length\n\n  for (curr = last = 0; curr < max; curr++) {\n    // re-calculate levels after emphasis/strikethrough turns some text nodes\n    // into opening/closing tags\n    if (tokens[curr].nesting < 0) level-- // closing tag\n    tokens[curr].level = level\n    if (tokens[curr].nesting > 0) level++ // opening tag\n\n    if (tokens[curr].type === 'text' &&\n        curr + 1 < max &&\n        tokens[curr + 1].type === 'text') {\n      // collapse two adjacent text nodes\n      tokens[curr + 1].content = tokens[curr].content + tokens[curr + 1].content\n    } else {\n      if (curr !== last) { tokens[last] = tokens[curr] }\n\n      last++\n    }\n  }\n\n  if (curr !== last) {\n    tokens.length = last\n  }\n}\n", "/** internal\n * class ParserInline\n *\n * Tokenizes paragraph content.\n **/\n\nimport Ruler from './ruler.mjs'\nimport StateInline from './rules_inline/state_inline.mjs'\n\nimport r_text from './rules_inline/text.mjs'\nimport r_linkify from './rules_inline/linkify.mjs'\nimport r_newline from './rules_inline/newline.mjs'\nimport r_escape from './rules_inline/escape.mjs'\nimport r_backticks from './rules_inline/backticks.mjs'\nimport r_strikethrough from './rules_inline/strikethrough.mjs'\nimport r_emphasis from './rules_inline/emphasis.mjs'\nimport r_link from './rules_inline/link.mjs'\nimport r_image from './rules_inline/image.mjs'\nimport r_autolink from './rules_inline/autolink.mjs'\nimport r_html_inline from './rules_inline/html_inline.mjs'\nimport r_entity from './rules_inline/entity.mjs'\n\nimport r_balance_pairs from './rules_inline/balance_pairs.mjs'\nimport r_fragments_join from './rules_inline/fragments_join.mjs'\n\n// Parser rules\n\nconst _rules = [\n  ['text',            r_text],\n  ['linkify',         r_linkify],\n  ['newline',         r_newline],\n  ['escape',          r_escape],\n  ['backticks',       r_backticks],\n  ['strikethrough',   r_strikethrough.tokenize],\n  ['emphasis',        r_emphasis.tokenize],\n  ['link',            r_link],\n  ['image',           r_image],\n  ['autolink',        r_autolink],\n  ['html_inline',     r_html_inline],\n  ['entity',          r_entity]\n]\n\n// `rule2` ruleset was created specifically for emphasis/strikethrough\n// post-processing and may be changed in the future.\n//\n// Don't use this for anything except pairs (plugins working with `balance_pairs`).\n//\nconst _rules2 = [\n  ['balance_pairs',   r_balance_pairs],\n  ['strikethrough',   r_strikethrough.postProcess],\n  ['emphasis',        r_emphasis.postProcess],\n  // rules for pairs separate '**' into its own text tokens, which may be left unused,\n  // rule below merges unused segments back with the rest of the text\n  ['fragments_join',  r_fragments_join]\n]\n\n/**\n * new ParserInline()\n **/\nfunction ParserInline () {\n  /**\n   * ParserInline#ruler -> Ruler\n   *\n   * [[Ruler]] instance. Keep configuration of inline rules.\n   **/\n  this.ruler = new Ruler()\n\n  for (let i = 0; i < _rules.length; i++) {\n    this.ruler.push(_rules[i][0], _rules[i][1])\n  }\n\n  /**\n   * ParserInline#ruler2 -> Ruler\n   *\n   * [[Ruler]] instance. Second ruler used for post-processing\n   * (e.g. in emphasis-like rules).\n   **/\n  this.ruler2 = new Ruler()\n\n  for (let i = 0; i < _rules2.length; i++) {\n    this.ruler2.push(_rules2[i][0], _rules2[i][1])\n  }\n}\n\n// Skip single token by running all rules in validation mode;\n// returns `true` if any rule reported success\n//\nParserInline.prototype.skipToken = function (state) {\n  const pos = state.pos\n  const rules = this.ruler.getRules('')\n  const len = rules.length\n  const maxNesting = state.md.options.maxNesting\n  const cache = state.cache\n\n  if (typeof cache[pos] !== 'undefined') {\n    state.pos = cache[pos]\n    return\n  }\n\n  let ok = false\n\n  if (state.level < maxNesting) {\n    for (let i = 0; i < len; i++) {\n      // Increment state.level and decrement it later to limit recursion.\n      // It's harmless to do here, because no tokens are created. But ideally,\n      // we'd need a separate private state variable for this purpose.\n      //\n      state.level++\n      ok = rules[i](state, true)\n      state.level--\n\n      if (ok) {\n        if (pos >= state.pos) { throw new Error(\"inline rule didn't increment state.pos\") }\n        break\n      }\n    }\n  } else {\n    // Too much nesting, just skip until the end of the paragraph.\n    //\n    // NOTE: this will cause links to behave incorrectly in the following case,\n    //       when an amount of `[` is exactly equal to `maxNesting + 1`:\n    //\n    //       [[[[[[[[[[[[[[[[[[[[[foo]()\n    //\n    // TODO: remove this workaround when CM standard will allow nested links\n    //       (we can replace it by preventing links from being parsed in\n    //       validation mode)\n    //\n    state.pos = state.posMax\n  }\n\n  if (!ok) { state.pos++ }\n  cache[pos] = state.pos\n}\n\n// Generate tokens for input range\n//\nParserInline.prototype.tokenize = function (state) {\n  const rules = this.ruler.getRules('')\n  const len = rules.length\n  const end = state.posMax\n  const maxNesting = state.md.options.maxNesting\n\n  while (state.pos < end) {\n    // Try all possible rules.\n    // On success, rule should:\n    //\n    // - update `state.pos`\n    // - update `state.tokens`\n    // - return true\n    const prevPos = state.pos\n    let ok = false\n\n    if (state.level < maxNesting) {\n      for (let i = 0; i < len; i++) {\n        ok = rules[i](state, false)\n        if (ok) {\n          if (prevPos >= state.pos) { throw new Error(\"inline rule didn't increment state.pos\") }\n          break\n        }\n      }\n    }\n\n    if (ok) {\n      if (state.pos >= end) { break }\n      continue\n    }\n\n    state.pending += state.src[state.pos++]\n  }\n\n  if (state.pending) {\n    state.pushPending()\n  }\n}\n\n/**\n * ParserInline.parse(str, md, env, outTokens)\n *\n * Process input string and push inline tokens into `outTokens`\n **/\nParserInline.prototype.parse = function (str, md, env, outTokens) {\n  const state = new this.State(str, md, env, outTokens)\n\n  this.tokenize(state)\n\n  const rules = this.ruler2.getRules('')\n  const len = rules.length\n\n  for (let i = 0; i < len; i++) {\n    rules[i](state)\n  }\n}\n\nParserInline.prototype.State = StateInline\n\nexport default ParserInline\n", "import { <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> } from 'uc.micro'\n\nexport default function (opts) {\n  const re = {}\n  opts = opts || {}\n\n  re.src_Any = Any.source\n  re.src_Cc = Cc.source\n  re.src_Z = Z.source\n  re.src_P = P.source\n\n  // \\p{\\Z\\P\\Cc\\CF} (white spaces + control + format + punctuation)\n  re.src_ZPCc = [re.src_Z, re.src_P, re.src_Cc].join('|')\n\n  // \\p{\\Z\\Cc} (white spaces + control)\n  re.src_ZCc = [re.src_Z, re.src_Cc].join('|')\n\n  // Experimental. List of chars, completely prohibited in links\n  // because can separate it from other part of text\n  const text_separators = '[><\\uff5c]'\n\n  // All possible word characters (everything without punctuation, spaces & controls)\n  // Defined via punctuation & spaces to save space\n  // Should be something like \\p{\\L\\N\\S\\M} (\\w but without `_`)\n  re.src_pseudo_letter = '(?:(?!' + text_separators + '|' + re.src_ZPCc + ')' + re.src_Any + ')'\n  // The same as abothe but without [0-9]\n  // var src_pseudo_letter_non_d = '(?:(?![0-9]|' + src_ZPCc + ')' + src_Any + ')';\n\n  re.src_ip4 =\n\n    '(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)'\n\n  // Prohibit any of \"@/[]()\" in user/pass to avoid wrong domain fetch.\n  re.src_auth = '(?:(?:(?!' + re.src_ZCc + '|[@/\\\\[\\\\]()]).)+@)?'\n\n  re.src_port =\n\n    '(?::(?:6(?:[0-4]\\\\d{3}|5(?:[0-4]\\\\d{2}|5(?:[0-2]\\\\d|3[0-5])))|[1-5]?\\\\d{1,4}))?'\n\n  re.src_host_terminator =\n\n    '(?=$|' + text_separators + '|' + re.src_ZPCc + ')' +\n    '(?!' + (opts['---'] ? '-(?!--)|' : '-|') + '_|:\\\\d|\\\\.-|\\\\.(?!$|' + re.src_ZPCc + '))'\n\n  re.src_path =\n\n    '(?:' +\n      '[/?#]' +\n        '(?:' +\n          '(?!' + re.src_ZCc + '|' + text_separators + '|[()[\\\\]{}.,\"\\'?!\\\\-;]).|' +\n          '\\\\[(?:(?!' + re.src_ZCc + '|\\\\]).)*\\\\]|' +\n          '\\\\((?:(?!' + re.src_ZCc + '|[)]).)*\\\\)|' +\n          '\\\\{(?:(?!' + re.src_ZCc + '|[}]).)*\\\\}|' +\n          '\\\\\"(?:(?!' + re.src_ZCc + '|[\"]).)+\\\\\"|' +\n          \"\\\\'(?:(?!\" + re.src_ZCc + \"|[']).)+\\\\'|\" +\n\n          // allow `I'm_king` if no pair found\n          \"\\\\'(?=\" + re.src_pseudo_letter + '|[-])|' +\n\n          // google has many dots in \"google search\" links (#66, #81).\n          // github has ... in commit range links,\n          // Restrict to\n          // - english\n          // - percent-encoded\n          // - parts of file path\n          // - params separator\n          // until more examples found.\n          '\\\\.{2,}[a-zA-Z0-9%/&]|' +\n\n          '\\\\.(?!' + re.src_ZCc + '|[.]|$)|' +\n          (opts['---']\n            ? '\\\\-(?!--(?:[^-]|$))(?:-*)|' // `---` => long dash, terminate\n            : '\\\\-+|'\n          ) +\n          // allow `,,,` in paths\n          ',(?!' + re.src_ZCc + '|$)|' +\n\n          // allow `;` if not followed by space-like char\n          ';(?!' + re.src_ZCc + '|$)|' +\n\n          // allow `!!!` in paths, but not at the end\n          '\\\\!+(?!' + re.src_ZCc + '|[!]|$)|' +\n\n          '\\\\?(?!' + re.src_ZCc + '|[?]|$)' +\n        ')+' +\n      '|\\\\/' +\n    ')?'\n\n  // Allow anything in markdown spec, forbid quote (\") at the first position\n  // because emails enclosed in quotes are far more common\n  re.src_email_name =\n\n    '[\\\\-;:&=\\\\+\\\\$,\\\\.a-zA-Z0-9_][\\\\-;:&=\\\\+\\\\$,\\\\\"\\\\.a-zA-Z0-9_]*'\n\n  re.src_xn =\n\n    'xn--[a-z0-9\\\\-]{1,59}'\n\n  // More to read about domain names\n  // http://serverfault.com/questions/638260/\n\n  re.src_domain_root =\n\n    // Allow letters & digits (http://test1)\n    '(?:' +\n      re.src_xn +\n      '|' +\n      re.src_pseudo_letter + '{1,63}' +\n    ')'\n\n  re.src_domain =\n\n    '(?:' +\n      re.src_xn +\n      '|' +\n      '(?:' + re.src_pseudo_letter + ')' +\n      '|' +\n      '(?:' + re.src_pseudo_letter + '(?:-|' + re.src_pseudo_letter + '){0,61}' + re.src_pseudo_letter + ')' +\n    ')'\n\n  re.src_host =\n\n    '(?:' +\n    // Don't need IP check, because digits are already allowed in normal domain names\n    //   src_ip4 +\n    // '|' +\n      '(?:(?:(?:' + re.src_domain + ')\\\\.)*' + re.src_domain/* _root */ + ')' +\n    ')'\n\n  re.tpl_host_fuzzy =\n\n    '(?:' +\n      re.src_ip4 +\n    '|' +\n      '(?:(?:(?:' + re.src_domain + ')\\\\.)+(?:%TLDS%))' +\n    ')'\n\n  re.tpl_host_no_ip_fuzzy =\n\n    '(?:(?:(?:' + re.src_domain + ')\\\\.)+(?:%TLDS%))'\n\n  re.src_host_strict =\n\n    re.src_host + re.src_host_terminator\n\n  re.tpl_host_fuzzy_strict =\n\n    re.tpl_host_fuzzy + re.src_host_terminator\n\n  re.src_host_port_strict =\n\n    re.src_host + re.src_port + re.src_host_terminator\n\n  re.tpl_host_port_fuzzy_strict =\n\n    re.tpl_host_fuzzy + re.src_port + re.src_host_terminator\n\n  re.tpl_host_port_no_ip_fuzzy_strict =\n\n    re.tpl_host_no_ip_fuzzy + re.src_port + re.src_host_terminator\n\n  //\n  // Main rules\n  //\n\n  // Rude test fuzzy links by host, for quick deny\n  re.tpl_host_fuzzy_test =\n\n    'localhost|www\\\\.|\\\\.\\\\d{1,3}\\\\.|(?:\\\\.(?:%TLDS%)(?:' + re.src_ZPCc + '|>|$))'\n\n  re.tpl_email_fuzzy =\n\n      '(^|' + text_separators + '|\"|\\\\(|' + re.src_ZCc + ')' +\n      '(' + re.src_email_name + '@' + re.tpl_host_fuzzy_strict + ')'\n\n  re.tpl_link_fuzzy =\n      // Fuzzy link can't be prepended with .:/\\- and non punctuation.\n      // but can start with > (markdown blockquote)\n      '(^|(?![.:/\\\\-_@])(?:[$+<=>^`|\\uff5c]|' + re.src_ZPCc + '))' +\n      '((?![$+<=>^`|\\uff5c])' + re.tpl_host_port_fuzzy_strict + re.src_path + ')'\n\n  re.tpl_link_no_ip_fuzzy =\n      // Fuzzy link can't be prepended with .:/\\- and non punctuation.\n      // but can start with > (markdown blockquote)\n      '(^|(?![.:/\\\\-_@])(?:[$+<=>^`|\\uff5c]|' + re.src_ZPCc + '))' +\n      '((?![$+<=>^`|\\uff5c])' + re.tpl_host_port_no_ip_fuzzy_strict + re.src_path + ')'\n\n  return re\n}\n", "import reFactory from './lib/re.mjs'\n\n//\n// Helpers\n//\n\n// Merge objects\n//\nfunction assign (obj /* from1, from2, from3, ... */) {\n  const sources = Array.prototype.slice.call(arguments, 1)\n\n  sources.forEach(function (source) {\n    if (!source) { return }\n\n    Object.keys(source).forEach(function (key) {\n      obj[key] = source[key]\n    })\n  })\n\n  return obj\n}\n\nfunction _class (obj) { return Object.prototype.toString.call(obj) }\nfunction isString (obj) { return _class(obj) === '[object String]' }\nfunction isObject (obj) { return _class(obj) === '[object Object]' }\nfunction isRegExp (obj) { return _class(obj) === '[object RegExp]' }\nfunction isFunction (obj) { return _class(obj) === '[object Function]' }\n\nfunction escapeRE (str) { return str.replace(/[.?*+^$[\\]\\\\(){}|-]/g, '\\\\$&') }\n\n//\n\nconst defaultOptions = {\n  fuzzyLink: true,\n  fuzzyEmail: true,\n  fuzzyIP: false\n}\n\nfunction isOptionsObj (obj) {\n  return Object.keys(obj || {}).reduce(function (acc, k) {\n    /* eslint-disable-next-line no-prototype-builtins */\n    return acc || defaultOptions.hasOwnProperty(k)\n  }, false)\n}\n\nconst defaultSchemas = {\n  'http:': {\n    validate: function (text, pos, self) {\n      const tail = text.slice(pos)\n\n      if (!self.re.http) {\n        // compile lazily, because \"host\"-containing variables can change on tlds update.\n        self.re.http = new RegExp(\n          '^\\\\/\\\\/' + self.re.src_auth + self.re.src_host_port_strict + self.re.src_path, 'i'\n        )\n      }\n      if (self.re.http.test(tail)) {\n        return tail.match(self.re.http)[0].length\n      }\n      return 0\n    }\n  },\n  'https:': 'http:',\n  'ftp:': 'http:',\n  '//': {\n    validate: function (text, pos, self) {\n      const tail = text.slice(pos)\n\n      if (!self.re.no_http) {\n      // compile lazily, because \"host\"-containing variables can change on tlds update.\n        self.re.no_http = new RegExp(\n          '^' +\n          self.re.src_auth +\n          // Don't allow single-level domains, because of false positives like '//test'\n          // with code comments\n          '(?:localhost|(?:(?:' + self.re.src_domain + ')\\\\.)+' + self.re.src_domain_root + ')' +\n          self.re.src_port +\n          self.re.src_host_terminator +\n          self.re.src_path,\n\n          'i'\n        )\n      }\n\n      if (self.re.no_http.test(tail)) {\n        // should not be `://` & `///`, that protects from errors in protocol name\n        if (pos >= 3 && text[pos - 3] === ':') { return 0 }\n        if (pos >= 3 && text[pos - 3] === '/') { return 0 }\n        return tail.match(self.re.no_http)[0].length\n      }\n      return 0\n    }\n  },\n  'mailto:': {\n    validate: function (text, pos, self) {\n      const tail = text.slice(pos)\n\n      if (!self.re.mailto) {\n        self.re.mailto = new RegExp(\n          '^' + self.re.src_email_name + '@' + self.re.src_host_strict, 'i'\n        )\n      }\n      if (self.re.mailto.test(tail)) {\n        return tail.match(self.re.mailto)[0].length\n      }\n      return 0\n    }\n  }\n}\n\n// RE pattern for 2-character tlds (autogenerated by ./support/tlds_2char_gen.js)\n/* eslint-disable-next-line max-len */\nconst tlds_2ch_src_re = 'a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]'\n\n// DON'T try to make PRs with changes. Extend TLDs with LinkifyIt.tlds() instead\nconst tlds_default = 'biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф'.split('|')\n\nfunction resetScanCache (self) {\n  self.__index__ = -1\n  self.__text_cache__ = ''\n}\n\nfunction createValidator (re) {\n  return function (text, pos) {\n    const tail = text.slice(pos)\n\n    if (re.test(tail)) {\n      return tail.match(re)[0].length\n    }\n    return 0\n  }\n}\n\nfunction createNormalizer () {\n  return function (match, self) {\n    self.normalize(match)\n  }\n}\n\n// Schemas compiler. Build regexps.\n//\nfunction compile (self) {\n  // Load & clone RE patterns.\n  const re = self.re = reFactory(self.__opts__)\n\n  // Define dynamic patterns\n  const tlds = self.__tlds__.slice()\n\n  self.onCompile()\n\n  if (!self.__tlds_replaced__) {\n    tlds.push(tlds_2ch_src_re)\n  }\n  tlds.push(re.src_xn)\n\n  re.src_tlds = tlds.join('|')\n\n  function untpl (tpl) { return tpl.replace('%TLDS%', re.src_tlds) }\n\n  re.email_fuzzy = RegExp(untpl(re.tpl_email_fuzzy), 'i')\n  re.link_fuzzy = RegExp(untpl(re.tpl_link_fuzzy), 'i')\n  re.link_no_ip_fuzzy = RegExp(untpl(re.tpl_link_no_ip_fuzzy), 'i')\n  re.host_fuzzy_test = RegExp(untpl(re.tpl_host_fuzzy_test), 'i')\n\n  //\n  // Compile each schema\n  //\n\n  const aliases = []\n\n  self.__compiled__ = {} // Reset compiled data\n\n  function schemaError (name, val) {\n    throw new Error('(LinkifyIt) Invalid schema \"' + name + '\": ' + val)\n  }\n\n  Object.keys(self.__schemas__).forEach(function (name) {\n    const val = self.__schemas__[name]\n\n    // skip disabled methods\n    if (val === null) { return }\n\n    const compiled = { validate: null, link: null }\n\n    self.__compiled__[name] = compiled\n\n    if (isObject(val)) {\n      if (isRegExp(val.validate)) {\n        compiled.validate = createValidator(val.validate)\n      } else if (isFunction(val.validate)) {\n        compiled.validate = val.validate\n      } else {\n        schemaError(name, val)\n      }\n\n      if (isFunction(val.normalize)) {\n        compiled.normalize = val.normalize\n      } else if (!val.normalize) {\n        compiled.normalize = createNormalizer()\n      } else {\n        schemaError(name, val)\n      }\n\n      return\n    }\n\n    if (isString(val)) {\n      aliases.push(name)\n      return\n    }\n\n    schemaError(name, val)\n  })\n\n  //\n  // Compile postponed aliases\n  //\n\n  aliases.forEach(function (alias) {\n    if (!self.__compiled__[self.__schemas__[alias]]) {\n      // Silently fail on missed schemas to avoid errons on disable.\n      // schemaError(alias, self.__schemas__[alias]);\n      return\n    }\n\n    self.__compiled__[alias].validate =\n      self.__compiled__[self.__schemas__[alias]].validate\n    self.__compiled__[alias].normalize =\n      self.__compiled__[self.__schemas__[alias]].normalize\n  })\n\n  //\n  // Fake record for guessed links\n  //\n  self.__compiled__[''] = { validate: null, normalize: createNormalizer() }\n\n  //\n  // Build schema condition\n  //\n  const slist = Object.keys(self.__compiled__)\n    .filter(function (name) {\n      // Filter disabled & fake schemas\n      return name.length > 0 && self.__compiled__[name]\n    })\n    .map(escapeRE)\n    .join('|')\n  // (?!_) cause 1.5x slowdown\n  self.re.schema_test = RegExp('(^|(?!_)(?:[><\\uff5c]|' + re.src_ZPCc + '))(' + slist + ')', 'i')\n  self.re.schema_search = RegExp('(^|(?!_)(?:[><\\uff5c]|' + re.src_ZPCc + '))(' + slist + ')', 'ig')\n  self.re.schema_at_start = RegExp('^' + self.re.schema_search.source, 'i')\n\n  self.re.pretest = RegExp(\n    '(' + self.re.schema_test.source + ')|(' + self.re.host_fuzzy_test.source + ')|@',\n    'i'\n  )\n\n  //\n  // Cleanup\n  //\n\n  resetScanCache(self)\n}\n\n/**\n * class Match\n *\n * Match result. Single element of array, returned by [[LinkifyIt#match]]\n **/\nfunction Match (self, shift) {\n  const start = self.__index__\n  const end = self.__last_index__\n  const text = self.__text_cache__.slice(start, end)\n\n  /**\n   * Match#schema -> String\n   *\n   * Prefix (protocol) for matched string.\n   **/\n  this.schema = self.__schema__.toLowerCase()\n  /**\n   * Match#index -> Number\n   *\n   * First position of matched string.\n   **/\n  this.index = start + shift\n  /**\n   * Match#lastIndex -> Number\n   *\n   * Next position after matched string.\n   **/\n  this.lastIndex = end + shift\n  /**\n   * Match#raw -> String\n   *\n   * Matched string.\n   **/\n  this.raw = text\n  /**\n   * Match#text -> String\n   *\n   * Notmalized text of matched string.\n   **/\n  this.text = text\n  /**\n   * Match#url -> String\n   *\n   * Normalized url of matched string.\n   **/\n  this.url = text\n}\n\nfunction createMatch (self, shift) {\n  const match = new Match(self, shift)\n\n  self.__compiled__[match.schema].normalize(match, self)\n\n  return match\n}\n\n/**\n * class LinkifyIt\n **/\n\n/**\n * new LinkifyIt(schemas, options)\n * - schemas (Object): Optional. Additional schemas to validate (prefix/validator)\n * - options (Object): { fuzzyLink|fuzzyEmail|fuzzyIP: true|false }\n *\n * Creates new linkifier instance with optional additional schemas.\n * Can be called without `new` keyword for convenience.\n *\n * By default understands:\n *\n * - `http(s)://...` , `ftp://...`, `mailto:...` & `//...` links\n * - \"fuzzy\" links and emails (example.com, <EMAIL>).\n *\n * `schemas` is an object, where each key/value describes protocol/rule:\n *\n * - __key__ - link prefix (usually, protocol name with `:` at the end, `skype:`\n *   for example). `linkify-it` makes shure that prefix is not preceeded with\n *   alphanumeric char and symbols. Only whitespaces and punctuation allowed.\n * - __value__ - rule to check tail after link prefix\n *   - _String_ - just alias to existing rule\n *   - _Object_\n *     - _validate_ - validator function (should return matched length on success),\n *       or `RegExp`.\n *     - _normalize_ - optional function to normalize text & url of matched result\n *       (for example, for @twitter mentions).\n *\n * `options`:\n *\n * - __fuzzyLink__ - recognige URL-s without `http(s):` prefix. Default `true`.\n * - __fuzzyIP__ - allow IPs in fuzzy links above. Can conflict with some texts\n *   like version numbers. Default `false`.\n * - __fuzzyEmail__ - recognize emails without `mailto:` prefix.\n *\n **/\nfunction LinkifyIt (schemas, options) {\n  if (!(this instanceof LinkifyIt)) {\n    return new LinkifyIt(schemas, options)\n  }\n\n  if (!options) {\n    if (isOptionsObj(schemas)) {\n      options = schemas\n      schemas = {}\n    }\n  }\n\n  this.__opts__ = assign({}, defaultOptions, options)\n\n  // Cache last tested result. Used to skip repeating steps on next `match` call.\n  this.__index__ = -1\n  this.__last_index__ = -1 // Next scan position\n  this.__schema__ = ''\n  this.__text_cache__ = ''\n\n  this.__schemas__ = assign({}, defaultSchemas, schemas)\n  this.__compiled__ = {}\n\n  this.__tlds__ = tlds_default\n  this.__tlds_replaced__ = false\n\n  this.re = {}\n\n  compile(this)\n}\n\n/** chainable\n * LinkifyIt#add(schema, definition)\n * - schema (String): rule name (fixed pattern prefix)\n * - definition (String|RegExp|Object): schema definition\n *\n * Add new rule definition. See constructor description for details.\n **/\nLinkifyIt.prototype.add = function add (schema, definition) {\n  this.__schemas__[schema] = definition\n  compile(this)\n  return this\n}\n\n/** chainable\n * LinkifyIt#set(options)\n * - options (Object): { fuzzyLink|fuzzyEmail|fuzzyIP: true|false }\n *\n * Set recognition options for links without schema.\n **/\nLinkifyIt.prototype.set = function set (options) {\n  this.__opts__ = assign(this.__opts__, options)\n  return this\n}\n\n/**\n * LinkifyIt#test(text) -> Boolean\n *\n * Searches linkifiable pattern and returns `true` on success or `false` on fail.\n **/\nLinkifyIt.prototype.test = function test (text) {\n  // Reset scan cache\n  this.__text_cache__ = text\n  this.__index__ = -1\n\n  if (!text.length) { return false }\n\n  let m, ml, me, len, shift, next, re, tld_pos, at_pos\n\n  // try to scan for link with schema - that's the most simple rule\n  if (this.re.schema_test.test(text)) {\n    re = this.re.schema_search\n    re.lastIndex = 0\n    while ((m = re.exec(text)) !== null) {\n      len = this.testSchemaAt(text, m[2], re.lastIndex)\n      if (len) {\n        this.__schema__ = m[2]\n        this.__index__ = m.index + m[1].length\n        this.__last_index__ = m.index + m[0].length + len\n        break\n      }\n    }\n  }\n\n  if (this.__opts__.fuzzyLink && this.__compiled__['http:']) {\n    // guess schemaless links\n    tld_pos = text.search(this.re.host_fuzzy_test)\n    if (tld_pos >= 0) {\n      // if tld is located after found link - no need to check fuzzy pattern\n      if (this.__index__ < 0 || tld_pos < this.__index__) {\n        if ((ml = text.match(this.__opts__.fuzzyIP ? this.re.link_fuzzy : this.re.link_no_ip_fuzzy)) !== null) {\n          shift = ml.index + ml[1].length\n\n          if (this.__index__ < 0 || shift < this.__index__) {\n            this.__schema__ = ''\n            this.__index__ = shift\n            this.__last_index__ = ml.index + ml[0].length\n          }\n        }\n      }\n    }\n  }\n\n  if (this.__opts__.fuzzyEmail && this.__compiled__['mailto:']) {\n    // guess schemaless emails\n    at_pos = text.indexOf('@')\n    if (at_pos >= 0) {\n      // We can't skip this check, because this cases are possible:\n      // <EMAIL>, <EMAIL>\n      if ((me = text.match(this.re.email_fuzzy)) !== null) {\n        shift = me.index + me[1].length\n        next = me.index + me[0].length\n\n        if (this.__index__ < 0 || shift < this.__index__ ||\n            (shift === this.__index__ && next > this.__last_index__)) {\n          this.__schema__ = 'mailto:'\n          this.__index__ = shift\n          this.__last_index__ = next\n        }\n      }\n    }\n  }\n\n  return this.__index__ >= 0\n}\n\n/**\n * LinkifyIt#pretest(text) -> Boolean\n *\n * Very quick check, that can give false positives. Returns true if link MAY BE\n * can exists. Can be used for speed optimization, when you need to check that\n * link NOT exists.\n **/\nLinkifyIt.prototype.pretest = function pretest (text) {\n  return this.re.pretest.test(text)\n}\n\n/**\n * LinkifyIt#testSchemaAt(text, name, position) -> Number\n * - text (String): text to scan\n * - name (String): rule (schema) name\n * - position (Number): text offset to check from\n *\n * Similar to [[LinkifyIt#test]] but checks only specific protocol tail exactly\n * at given position. Returns length of found pattern (0 on fail).\n **/\nLinkifyIt.prototype.testSchemaAt = function testSchemaAt (text, schema, pos) {\n  // If not supported schema check requested - terminate\n  if (!this.__compiled__[schema.toLowerCase()]) {\n    return 0\n  }\n  return this.__compiled__[schema.toLowerCase()].validate(text, pos, this)\n}\n\n/**\n * LinkifyIt#match(text) -> Array|null\n *\n * Returns array of found link descriptions or `null` on fail. We strongly\n * recommend to use [[LinkifyIt#test]] first, for best speed.\n *\n * ##### Result match description\n *\n * - __schema__ - link schema, can be empty for fuzzy links, or `//` for\n *   protocol-neutral  links.\n * - __index__ - offset of matched text\n * - __lastIndex__ - index of next char after mathch end\n * - __raw__ - matched text\n * - __text__ - normalized text\n * - __url__ - link, generated from matched text\n **/\nLinkifyIt.prototype.match = function match (text) {\n  const result = []\n  let shift = 0\n\n  // Try to take previous element from cache, if .test() called before\n  if (this.__index__ >= 0 && this.__text_cache__ === text) {\n    result.push(createMatch(this, shift))\n    shift = this.__last_index__\n  }\n\n  // Cut head if cache was used\n  let tail = shift ? text.slice(shift) : text\n\n  // Scan string until end reached\n  while (this.test(tail)) {\n    result.push(createMatch(this, shift))\n\n    tail = tail.slice(this.__last_index__)\n    shift += this.__last_index__\n  }\n\n  if (result.length) {\n    return result\n  }\n\n  return null\n}\n\n/**\n * LinkifyIt#matchAtStart(text) -> Match|null\n *\n * Returns fully-formed (not fuzzy) link if it starts at the beginning\n * of the string, and null otherwise.\n **/\nLinkifyIt.prototype.matchAtStart = function matchAtStart (text) {\n  // Reset scan cache\n  this.__text_cache__ = text\n  this.__index__ = -1\n\n  if (!text.length) return null\n\n  const m = this.re.schema_at_start.exec(text)\n  if (!m) return null\n\n  const len = this.testSchemaAt(text, m[2], m[0].length)\n  if (!len) return null\n\n  this.__schema__ = m[2]\n  this.__index__ = m.index + m[1].length\n  this.__last_index__ = m.index + m[0].length + len\n\n  return createMatch(this, 0)\n}\n\n/** chainable\n * LinkifyIt#tlds(list [, keepOld]) -> this\n * - list (Array): list of tlds\n * - keepOld (Boolean): merge with current list if `true` (`false` by default)\n *\n * Load (or merge) new tlds list. Those are user for fuzzy links (without prefix)\n * to avoid false positives. By default this algorythm used:\n *\n * - hostname with any 2-letter root zones are ok.\n * - biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф\n *   are ok.\n * - encoded (`xn--...`) root zones are ok.\n *\n * If list is replaced, then exact match for 2-chars root zones will be checked.\n **/\nLinkifyIt.prototype.tlds = function tlds (list, keepOld) {\n  list = Array.isArray(list) ? list : [list]\n\n  if (!keepOld) {\n    this.__tlds__ = list.slice()\n    this.__tlds_replaced__ = true\n    compile(this)\n    return this\n  }\n\n  this.__tlds__ = this.__tlds__.concat(list)\n    .sort()\n    .filter(function (el, idx, arr) {\n      return el !== arr[idx - 1]\n    })\n    .reverse()\n\n  compile(this)\n  return this\n}\n\n/**\n * LinkifyIt#normalize(match)\n *\n * Default normalizer (if schema does not define it's own).\n **/\nLinkifyIt.prototype.normalize = function normalize (match) {\n  // Do minimal possible changes by default. Need to collect feedback prior\n  // to move forward https://github.com/markdown-it/linkify-it/issues/1\n\n  if (!match.schema) { match.url = 'http://' + match.url }\n\n  if (match.schema === 'mailto:' && !/^mailto:/i.test(match.url)) {\n    match.url = 'mailto:' + match.url\n  }\n}\n\n/**\n * LinkifyIt#onCompile()\n *\n * Override to modify basic RegExp-s.\n **/\nLinkifyIt.prototype.onCompile = function onCompile () {\n}\n\nexport default LinkifyIt\n", "'use strict';\n\n/** Highest positive signed 32-bit float value */\nconst maxInt = 2147483647; // aka. 0x7FFFFFFF or 2^31-1\n\n/** Bootstring parameters */\nconst base = 36;\nconst tMin = 1;\nconst tMax = 26;\nconst skew = 38;\nconst damp = 700;\nconst initialBias = 72;\nconst initialN = 128; // 0x80\nconst delimiter = '-'; // '\\x2D'\n\n/** Regular expressions */\nconst regexPunycode = /^xn--/;\nconst regexNonASCII = /[^\\0-\\x7F]/; // Note: U+007F DEL is excluded too.\nconst regexSeparators = /[\\x2E\\u3002\\uFF0E\\uFF61]/g; // RFC 3490 separators\n\n/** Error messages */\nconst errors = {\n\t'overflow': 'Overflow: input needs wider integers to process',\n\t'not-basic': 'Illegal input >= 0x80 (not a basic code point)',\n\t'invalid-input': 'Invalid input'\n};\n\n/** Convenience shortcuts */\nconst baseMinusTMin = base - tMin;\nconst floor = Math.floor;\nconst stringFromCharCode = String.fromCharCode;\n\n/*--------------------------------------------------------------------------*/\n\n/**\n * A generic error utility function.\n * @private\n * @param {String} type The error type.\n * @returns {Error} Throws a `RangeError` with the applicable error message.\n */\nfunction error(type) {\n\tthrow new RangeError(errors[type]);\n}\n\n/**\n * A generic `Array#map` utility function.\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} callback The function that gets called for every array\n * item.\n * @returns {Array} A new array of values returned by the callback function.\n */\nfunction map(array, callback) {\n\tconst result = [];\n\tlet length = array.length;\n\twhile (length--) {\n\t\tresult[length] = callback(array[length]);\n\t}\n\treturn result;\n}\n\n/**\n * A simple `Array#map`-like wrapper to work with domain name strings or email\n * addresses.\n * @private\n * @param {String} domain The domain name or email address.\n * @param {Function} callback The function that gets called for every\n * character.\n * @returns {String} A new string of characters returned by the callback\n * function.\n */\nfunction mapDomain(domain, callback) {\n\tconst parts = domain.split('@');\n\tlet result = '';\n\tif (parts.length > 1) {\n\t\t// In email addresses, only the domain name should be punycoded. Leave\n\t\t// the local part (i.e. everything up to `@`) intact.\n\t\tresult = parts[0] + '@';\n\t\tdomain = parts[1];\n\t}\n\t// Avoid `split(regex)` for IE8 compatibility. See #17.\n\tdomain = domain.replace(regexSeparators, '\\x2E');\n\tconst labels = domain.split('.');\n\tconst encoded = map(labels, callback).join('.');\n\treturn result + encoded;\n}\n\n/**\n * Creates an array containing the numeric code points of each Unicode\n * character in the string. While JavaScript uses UCS-2 internally,\n * this function will convert a pair of surrogate halves (each of which\n * UCS-2 exposes as separate characters) into a single code point,\n * matching UTF-16.\n * @see `punycode.ucs2.encode`\n * @see <https://mathiasbynens.be/notes/javascript-encoding>\n * @memberOf punycode.ucs2\n * @name decode\n * @param {String} string The Unicode input string (UCS-2).\n * @returns {Array} The new array of code points.\n */\nfunction ucs2decode(string) {\n\tconst output = [];\n\tlet counter = 0;\n\tconst length = string.length;\n\twhile (counter < length) {\n\t\tconst value = string.charCodeAt(counter++);\n\t\tif (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n\t\t\t// It's a high surrogate, and there is a next character.\n\t\t\tconst extra = string.charCodeAt(counter++);\n\t\t\tif ((extra & 0xFC00) == 0xDC00) { // Low surrogate.\n\t\t\t\toutput.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n\t\t\t} else {\n\t\t\t\t// It's an unmatched surrogate; only append this code unit, in case the\n\t\t\t\t// next code unit is the high surrogate of a surrogate pair.\n\t\t\t\toutput.push(value);\n\t\t\t\tcounter--;\n\t\t\t}\n\t\t} else {\n\t\t\toutput.push(value);\n\t\t}\n\t}\n\treturn output;\n}\n\n/**\n * Creates a string based on an array of numeric code points.\n * @see `punycode.ucs2.decode`\n * @memberOf punycode.ucs2\n * @name encode\n * @param {Array} codePoints The array of numeric code points.\n * @returns {String} The new Unicode string (UCS-2).\n */\nconst ucs2encode = codePoints => String.fromCodePoint(...codePoints);\n\n/**\n * Converts a basic code point into a digit/integer.\n * @see `digitToBasic()`\n * @private\n * @param {Number} codePoint The basic numeric code point value.\n * @returns {Number} The numeric value of a basic code point (for use in\n * representing integers) in the range `0` to `base - 1`, or `base` if\n * the code point does not represent a value.\n */\nconst basicToDigit = function(codePoint) {\n\tif (codePoint >= 0x30 && codePoint < 0x3A) {\n\t\treturn 26 + (codePoint - 0x30);\n\t}\n\tif (codePoint >= 0x41 && codePoint < 0x5B) {\n\t\treturn codePoint - 0x41;\n\t}\n\tif (codePoint >= 0x61 && codePoint < 0x7B) {\n\t\treturn codePoint - 0x61;\n\t}\n\treturn base;\n};\n\n/**\n * Converts a digit/integer into a basic code point.\n * @see `basicToDigit()`\n * @private\n * @param {Number} digit The numeric value of a basic code point.\n * @returns {Number} The basic code point whose value (when used for\n * representing integers) is `digit`, which needs to be in the range\n * `0` to `base - 1`. If `flag` is non-zero, the uppercase form is\n * used; else, the lowercase form is used. The behavior is undefined\n * if `flag` is non-zero and `digit` has no uppercase form.\n */\nconst digitToBasic = function(digit, flag) {\n\t//  0..25 map to ASCII a..z or A..Z\n\t// 26..35 map to ASCII 0..9\n\treturn digit + 22 + 75 * (digit < 26) - ((flag != 0) << 5);\n};\n\n/**\n * Bias adaptation function as per section 3.4 of RFC 3492.\n * https://tools.ietf.org/html/rfc3492#section-3.4\n * @private\n */\nconst adapt = function(delta, numPoints, firstTime) {\n\tlet k = 0;\n\tdelta = firstTime ? floor(delta / damp) : delta >> 1;\n\tdelta += floor(delta / numPoints);\n\tfor (/* no initialization */; delta > baseMinusTMin * tMax >> 1; k += base) {\n\t\tdelta = floor(delta / baseMinusTMin);\n\t}\n\treturn floor(k + (baseMinusTMin + 1) * delta / (delta + skew));\n};\n\n/**\n * Converts a Punycode string of ASCII-only symbols to a string of Unicode\n * symbols.\n * @memberOf punycode\n * @param {String} input The Punycode string of ASCII-only symbols.\n * @returns {String} The resulting string of Unicode symbols.\n */\nconst decode = function(input) {\n\t// Don't use UCS-2.\n\tconst output = [];\n\tconst inputLength = input.length;\n\tlet i = 0;\n\tlet n = initialN;\n\tlet bias = initialBias;\n\n\t// Handle the basic code points: let `basic` be the number of input code\n\t// points before the last delimiter, or `0` if there is none, then copy\n\t// the first basic code points to the output.\n\n\tlet basic = input.lastIndexOf(delimiter);\n\tif (basic < 0) {\n\t\tbasic = 0;\n\t}\n\n\tfor (let j = 0; j < basic; ++j) {\n\t\t// if it's not a basic code point\n\t\tif (input.charCodeAt(j) >= 0x80) {\n\t\t\terror('not-basic');\n\t\t}\n\t\toutput.push(input.charCodeAt(j));\n\t}\n\n\t// Main decoding loop: start just after the last delimiter if any basic code\n\t// points were copied; start at the beginning otherwise.\n\n\tfor (let index = basic > 0 ? basic + 1 : 0; index < inputLength; /* no final expression */) {\n\n\t\t// `index` is the index of the next character to be consumed.\n\t\t// Decode a generalized variable-length integer into `delta`,\n\t\t// which gets added to `i`. The overflow checking is easier\n\t\t// if we increase `i` as we go, then subtract off its starting\n\t\t// value at the end to obtain `delta`.\n\t\tconst oldi = i;\n\t\tfor (let w = 1, k = base; /* no condition */; k += base) {\n\n\t\t\tif (index >= inputLength) {\n\t\t\t\terror('invalid-input');\n\t\t\t}\n\n\t\t\tconst digit = basicToDigit(input.charCodeAt(index++));\n\n\t\t\tif (digit >= base) {\n\t\t\t\terror('invalid-input');\n\t\t\t}\n\t\t\tif (digit > floor((maxInt - i) / w)) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\n\t\t\ti += digit * w;\n\t\t\tconst t = k <= bias ? tMin : (k >= bias + tMax ? tMax : k - bias);\n\n\t\t\tif (digit < t) {\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tconst baseMinusT = base - t;\n\t\t\tif (w > floor(maxInt / baseMinusT)) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\n\t\t\tw *= baseMinusT;\n\n\t\t}\n\n\t\tconst out = output.length + 1;\n\t\tbias = adapt(i - oldi, out, oldi == 0);\n\n\t\t// `i` was supposed to wrap around from `out` to `0`,\n\t\t// incrementing `n` each time, so we'll fix that now:\n\t\tif (floor(i / out) > maxInt - n) {\n\t\t\terror('overflow');\n\t\t}\n\n\t\tn += floor(i / out);\n\t\ti %= out;\n\n\t\t// Insert `n` at position `i` of the output.\n\t\toutput.splice(i++, 0, n);\n\n\t}\n\n\treturn String.fromCodePoint(...output);\n};\n\n/**\n * Converts a string of Unicode symbols (e.g. a domain name label) to a\n * Punycode string of ASCII-only symbols.\n * @memberOf punycode\n * @param {String} input The string of Unicode symbols.\n * @returns {String} The resulting Punycode string of ASCII-only symbols.\n */\nconst encode = function(input) {\n\tconst output = [];\n\n\t// Convert the input in UCS-2 to an array of Unicode code points.\n\tinput = ucs2decode(input);\n\n\t// Cache the length.\n\tconst inputLength = input.length;\n\n\t// Initialize the state.\n\tlet n = initialN;\n\tlet delta = 0;\n\tlet bias = initialBias;\n\n\t// Handle the basic code points.\n\tfor (const currentValue of input) {\n\t\tif (currentValue < 0x80) {\n\t\t\toutput.push(stringFromCharCode(currentValue));\n\t\t}\n\t}\n\n\tconst basicLength = output.length;\n\tlet handledCPCount = basicLength;\n\n\t// `handledCPCount` is the number of code points that have been handled;\n\t// `basicLength` is the number of basic code points.\n\n\t// Finish the basic string with a delimiter unless it's empty.\n\tif (basicLength) {\n\t\toutput.push(delimiter);\n\t}\n\n\t// Main encoding loop:\n\twhile (handledCPCount < inputLength) {\n\n\t\t// All non-basic code points < n have been handled already. Find the next\n\t\t// larger one:\n\t\tlet m = maxInt;\n\t\tfor (const currentValue of input) {\n\t\t\tif (currentValue >= n && currentValue < m) {\n\t\t\t\tm = currentValue;\n\t\t\t}\n\t\t}\n\n\t\t// Increase `delta` enough to advance the decoder's <n,i> state to <m,0>,\n\t\t// but guard against overflow.\n\t\tconst handledCPCountPlusOne = handledCPCount + 1;\n\t\tif (m - n > floor((maxInt - delta) / handledCPCountPlusOne)) {\n\t\t\terror('overflow');\n\t\t}\n\n\t\tdelta += (m - n) * handledCPCountPlusOne;\n\t\tn = m;\n\n\t\tfor (const currentValue of input) {\n\t\t\tif (currentValue < n && ++delta > maxInt) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\t\t\tif (currentValue === n) {\n\t\t\t\t// Represent delta as a generalized variable-length integer.\n\t\t\t\tlet q = delta;\n\t\t\t\tfor (let k = base; /* no condition */; k += base) {\n\t\t\t\t\tconst t = k <= bias ? tMin : (k >= bias + tMax ? tMax : k - bias);\n\t\t\t\t\tif (q < t) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tconst qMinusT = q - t;\n\t\t\t\t\tconst baseMinusT = base - t;\n\t\t\t\t\toutput.push(\n\t\t\t\t\t\tstringFromCharCode(digitToBasic(t + qMinusT % baseMinusT, 0))\n\t\t\t\t\t);\n\t\t\t\t\tq = floor(qMinusT / baseMinusT);\n\t\t\t\t}\n\n\t\t\t\toutput.push(stringFromCharCode(digitToBasic(q, 0)));\n\t\t\t\tbias = adapt(delta, handledCPCountPlusOne, handledCPCount === basicLength);\n\t\t\t\tdelta = 0;\n\t\t\t\t++handledCPCount;\n\t\t\t}\n\t\t}\n\n\t\t++delta;\n\t\t++n;\n\n\t}\n\treturn output.join('');\n};\n\n/**\n * Converts a Punycode string representing a domain name or an email address\n * to Unicode. Only the Punycoded parts of the input will be converted, i.e.\n * it doesn't matter if you call it on a string that has already been\n * converted to Unicode.\n * @memberOf punycode\n * @param {String} input The Punycoded domain name or email address to\n * convert to Unicode.\n * @returns {String} The Unicode representation of the given Punycode\n * string.\n */\nconst toUnicode = function(input) {\n\treturn mapDomain(input, function(string) {\n\t\treturn regexPunycode.test(string)\n\t\t\t? decode(string.slice(4).toLowerCase())\n\t\t\t: string;\n\t});\n};\n\n/**\n * Converts a Unicode string representing a domain name or an email address to\n * Punycode. Only the non-ASCII parts of the domain name will be converted,\n * i.e. it doesn't matter if you call it with a domain that's already in\n * ASCII.\n * @memberOf punycode\n * @param {String} input The domain name or email address to convert, as a\n * Unicode string.\n * @returns {String} The Punycode representation of the given domain name or\n * email address.\n */\nconst toASCII = function(input) {\n\treturn mapDomain(input, function(string) {\n\t\treturn regexNonASCII.test(string)\n\t\t\t? 'xn--' + encode(string)\n\t\t\t: string;\n\t});\n};\n\n/*--------------------------------------------------------------------------*/\n\n/** Define the public API */\nconst punycode = {\n\t/**\n\t * A string representing the current Punycode.js version number.\n\t * @memberOf punycode\n\t * @type String\n\t */\n\t'version': '2.3.1',\n\t/**\n\t * An object of methods to convert from JavaScript's internal character\n\t * representation (UCS-2) to Unicode code points, and back.\n\t * @see <https://mathiasbynens.be/notes/javascript-encoding>\n\t * @memberOf punycode\n\t * @type Object\n\t */\n\t'ucs2': {\n\t\t'decode': ucs2decode,\n\t\t'encode': ucs2encode\n\t},\n\t'decode': decode,\n\t'encode': encode,\n\t'toASCII': toASCII,\n\t'toUnicode': toUnicode\n};\n\nexport { ucs2decode, ucs2encode, decode, encode, toASCII, toUnicode };\nexport default punycode;\n", "// markdown-it default options\n\nexport default {\n  options: {\n    // Enable HTML tags in source\n    html: false,\n\n    // Use '/' to close single tags (<br />)\n    xhtmlOut: false,\n\n    // Convert '\\n' in paragraphs into <br>\n    breaks: false,\n\n    // CSS language prefix for fenced blocks\n    langPrefix: 'language-',\n\n    // autoconvert URL-like texts to links\n    linkify: false,\n\n    // Enable some language-neutral replacements + quotes beautification\n    typographer: false,\n\n    // Double + single quotes replacement pairs, when typographer enabled,\n    // and smartquotes on. Could be either a String or an Array.\n    //\n    // For example, you can use '«»„“' for Russian, '„“‚‘' for German,\n    // and ['«\\xA0', '\\xA0»', '‹\\xA0', '\\xA0›'] for French (including nbsp).\n    quotes: '\\u201c\\u201d\\u2018\\u2019', /* “”‘’ */\n\n    // Highlighter function. Should return escaped HTML,\n    // or '' if the source string is not changed and should be escaped externaly.\n    // If result starts with <pre... internal wrapper is skipped.\n    //\n    // function (/*str, lang*/) { return ''; }\n    //\n    highlight: null,\n\n    // Internal protection, recursion limit\n    maxNesting: 100\n  },\n\n  components: {\n    core: {},\n    block: {},\n    inline: {}\n  }\n}\n", "// \"Zero\" preset, with nothing enabled. Useful for manual configuring of simple\n// modes. For example, to parse bold/italic only.\n\nexport default {\n  options: {\n    // Enable HTML tags in source\n    html: false,\n\n    // Use '/' to close single tags (<br />)\n    xhtmlOut: false,\n\n    // Convert '\\n' in paragraphs into <br>\n    breaks: false,\n\n    // CSS language prefix for fenced blocks\n    langPrefix: 'language-',\n\n    // autoconvert URL-like texts to links\n    linkify: false,\n\n    // Enable some language-neutral replacements + quotes beautification\n    typographer: false,\n\n    // Double + single quotes replacement pairs, when typographer enabled,\n    // and smartquotes on. Could be either a String or an Array.\n    //\n    // For example, you can use '«»„“' for Russian, '„“‚‘' for German,\n    // and ['«\\xA0', '\\xA0»', '‹\\xA0', '\\xA0›'] for French (including nbsp).\n    quotes: '\\u201c\\u201d\\u2018\\u2019', /* “”‘’ */\n\n    // Highlighter function. Should return escaped HTML,\n    // or '' if the source string is not changed and should be escaped externaly.\n    // If result starts with <pre... internal wrapper is skipped.\n    //\n    // function (/*str, lang*/) { return ''; }\n    //\n    highlight: null,\n\n    // Internal protection, recursion limit\n    maxNesting: 20\n  },\n\n  components: {\n\n    core: {\n      rules: [\n        'normalize',\n        'block',\n        'inline',\n        'text_join'\n      ]\n    },\n\n    block: {\n      rules: [\n        'paragraph'\n      ]\n    },\n\n    inline: {\n      rules: [\n        'text'\n      ],\n      rules2: [\n        'balance_pairs',\n        'fragments_join'\n      ]\n    }\n  }\n}\n", "// Commonmark default options\n\nexport default {\n  options: {\n    // Enable HTML tags in source\n    html: true,\n\n    // Use '/' to close single tags (<br />)\n    xhtmlOut: true,\n\n    // Convert '\\n' in paragraphs into <br>\n    breaks: false,\n\n    // CSS language prefix for fenced blocks\n    langPrefix: 'language-',\n\n    // autoconvert URL-like texts to links\n    linkify: false,\n\n    // Enable some language-neutral replacements + quotes beautification\n    typographer: false,\n\n    // Double + single quotes replacement pairs, when typographer enabled,\n    // and smartquotes on. Could be either a String or an Array.\n    //\n    // For example, you can use '«»„“' for Russian, '„“‚‘' for German,\n    // and ['«\\xA0', '\\xA0»', '‹\\xA0', '\\xA0›'] for French (including nbsp).\n    quotes: '\\u201c\\u201d\\u2018\\u2019', /* “”‘’ */\n\n    // Highlighter function. Should return escaped HTML,\n    // or '' if the source string is not changed and should be escaped externaly.\n    // If result starts with <pre... internal wrapper is skipped.\n    //\n    // function (/*str, lang*/) { return ''; }\n    //\n    highlight: null,\n\n    // Internal protection, recursion limit\n    maxNesting: 20\n  },\n\n  components: {\n\n    core: {\n      rules: [\n        'normalize',\n        'block',\n        'inline',\n        'text_join'\n      ]\n    },\n\n    block: {\n      rules: [\n        'blockquote',\n        'code',\n        'fence',\n        'heading',\n        'hr',\n        'html_block',\n        'lheading',\n        'list',\n        'reference',\n        'paragraph'\n      ]\n    },\n\n    inline: {\n      rules: [\n        'autolink',\n        'backticks',\n        'emphasis',\n        'entity',\n        'escape',\n        'html_inline',\n        'image',\n        'link',\n        'newline',\n        'text'\n      ],\n      rules2: [\n        'balance_pairs',\n        'emphasis',\n        'fragments_join'\n      ]\n    }\n  }\n}\n", "// Main parser class\n\nimport * as utils from './common/utils.mjs'\nimport * as helpers from './helpers/index.mjs'\nimport Renderer from './renderer.mjs'\nimport ParserCore from './parser_core.mjs'\nimport ParserBlock from './parser_block.mjs'\nimport ParserInline from './parser_inline.mjs'\nimport LinkifyIt from 'linkify-it'\nimport * as mdurl from 'mdurl'\nimport punycode from 'punycode.js'\n\nimport cfg_default from './presets/default.mjs'\nimport cfg_zero from './presets/zero.mjs'\nimport cfg_commonmark from './presets/commonmark.mjs'\n\nconst config = {\n  default: cfg_default,\n  zero: cfg_zero,\n  commonmark: cfg_commonmark\n}\n\n//\n// This validator can prohibit more than really needed to prevent XSS. It's a\n// tradeoff to keep code simple and to be secure by default.\n//\n// If you need different setup - override validator method as you wish. Or\n// replace it with dummy function and use external sanitizer.\n//\n\nconst BAD_PROTO_RE = /^(vbscript|javascript|file|data):/\nconst GOOD_DATA_RE = /^data:image\\/(gif|png|jpeg|webp);/\n\nfunction validateLink (url) {\n  // url should be normalized at this point, and existing entities are decoded\n  const str = url.trim().toLowerCase()\n\n  return BAD_PROTO_RE.test(str) ? GOOD_DATA_RE.test(str) : true\n}\n\nconst RECODE_HOSTNAME_FOR = ['http:', 'https:', 'mailto:']\n\nfunction normalizeLink (url) {\n  const parsed = mdurl.parse(url, true)\n\n  if (parsed.hostname) {\n    // Encode hostnames in urls like:\n    // `http://host/`, `https://host/`, `mailto:user@host`, `//host/`\n    //\n    // We don't encode unknown schemas, because it's likely that we encode\n    // something we shouldn't (e.g. `skype:name` treated as `skype:host`)\n    //\n    if (!parsed.protocol || RECODE_HOSTNAME_FOR.indexOf(parsed.protocol) >= 0) {\n      try {\n        parsed.hostname = punycode.toASCII(parsed.hostname)\n      } catch (er) { /**/ }\n    }\n  }\n\n  return mdurl.encode(mdurl.format(parsed))\n}\n\nfunction normalizeLinkText (url) {\n  const parsed = mdurl.parse(url, true)\n\n  if (parsed.hostname) {\n    // Encode hostnames in urls like:\n    // `http://host/`, `https://host/`, `mailto:user@host`, `//host/`\n    //\n    // We don't encode unknown schemas, because it's likely that we encode\n    // something we shouldn't (e.g. `skype:name` treated as `skype:host`)\n    //\n    if (!parsed.protocol || RECODE_HOSTNAME_FOR.indexOf(parsed.protocol) >= 0) {\n      try {\n        parsed.hostname = punycode.toUnicode(parsed.hostname)\n      } catch (er) { /**/ }\n    }\n  }\n\n  // add '%' to exclude list because of https://github.com/markdown-it/markdown-it/issues/720\n  return mdurl.decode(mdurl.format(parsed), mdurl.decode.defaultChars + '%')\n}\n\n/**\n * class MarkdownIt\n *\n * Main parser/renderer class.\n *\n * ##### Usage\n *\n * ```javascript\n * // node.js, \"classic\" way:\n * var MarkdownIt = require('markdown-it'),\n *     md = new MarkdownIt();\n * var result = md.render('# markdown-it rulezz!');\n *\n * // node.js, the same, but with sugar:\n * var md = require('markdown-it')();\n * var result = md.render('# markdown-it rulezz!');\n *\n * // browser without AMD, added to \"window\" on script load\n * // Note, there are no dash.\n * var md = window.markdownit();\n * var result = md.render('# markdown-it rulezz!');\n * ```\n *\n * Single line rendering, without paragraph wrap:\n *\n * ```javascript\n * var md = require('markdown-it')();\n * var result = md.renderInline('__markdown-it__ rulezz!');\n * ```\n **/\n\n/**\n * new MarkdownIt([presetName, options])\n * - presetName (String): optional, `commonmark` / `zero`\n * - options (Object)\n *\n * Creates parser instanse with given config. Can be called without `new`.\n *\n * ##### presetName\n *\n * MarkdownIt provides named presets as a convenience to quickly\n * enable/disable active syntax rules and options for common use cases.\n *\n * - [\"commonmark\"](https://github.com/markdown-it/markdown-it/blob/master/lib/presets/commonmark.mjs) -\n *   configures parser to strict [CommonMark](http://commonmark.org/) mode.\n * - [default](https://github.com/markdown-it/markdown-it/blob/master/lib/presets/default.mjs) -\n *   similar to GFM, used when no preset name given. Enables all available rules,\n *   but still without html, typographer & autolinker.\n * - [\"zero\"](https://github.com/markdown-it/markdown-it/blob/master/lib/presets/zero.mjs) -\n *   all rules disabled. Useful to quickly setup your config via `.enable()`.\n *   For example, when you need only `bold` and `italic` markup and nothing else.\n *\n * ##### options:\n *\n * - __html__ - `false`. Set `true` to enable HTML tags in source. Be careful!\n *   That's not safe! You may need external sanitizer to protect output from XSS.\n *   It's better to extend features via plugins, instead of enabling HTML.\n * - __xhtmlOut__ - `false`. Set `true` to add '/' when closing single tags\n *   (`<br />`). This is needed only for full CommonMark compatibility. In real\n *   world you will need HTML output.\n * - __breaks__ - `false`. Set `true` to convert `\\n` in paragraphs into `<br>`.\n * - __langPrefix__ - `language-`. CSS language class prefix for fenced blocks.\n *   Can be useful for external highlighters.\n * - __linkify__ - `false`. Set `true` to autoconvert URL-like text to links.\n * - __typographer__  - `false`. Set `true` to enable [some language-neutral\n *   replacement](https://github.com/markdown-it/markdown-it/blob/master/lib/rules_core/replacements.mjs) +\n *   quotes beautification (smartquotes).\n * - __quotes__ - `“”‘’`, String or Array. Double + single quotes replacement\n *   pairs, when typographer enabled and smartquotes on. For example, you can\n *   use `'«»„“'` for Russian, `'„“‚‘'` for German, and\n *   `['«\\xA0', '\\xA0»', '‹\\xA0', '\\xA0›']` for French (including nbsp).\n * - __highlight__ - `null`. Highlighter function for fenced code blocks.\n *   Highlighter `function (str, lang)` should return escaped HTML. It can also\n *   return empty string if the source was not changed and should be escaped\n *   externaly. If result starts with <pre... internal wrapper is skipped.\n *\n * ##### Example\n *\n * ```javascript\n * // commonmark mode\n * var md = require('markdown-it')('commonmark');\n *\n * // default mode\n * var md = require('markdown-it')();\n *\n * // enable everything\n * var md = require('markdown-it')({\n *   html: true,\n *   linkify: true,\n *   typographer: true\n * });\n * ```\n *\n * ##### Syntax highlighting\n *\n * ```js\n * var hljs = require('highlight.js') // https://highlightjs.org/\n *\n * var md = require('markdown-it')({\n *   highlight: function (str, lang) {\n *     if (lang && hljs.getLanguage(lang)) {\n *       try {\n *         return hljs.highlight(str, { language: lang, ignoreIllegals: true }).value;\n *       } catch (__) {}\n *     }\n *\n *     return ''; // use external default escaping\n *   }\n * });\n * ```\n *\n * Or with full wrapper override (if you need assign class to `<pre>` or `<code>`):\n *\n * ```javascript\n * var hljs = require('highlight.js') // https://highlightjs.org/\n *\n * // Actual default values\n * var md = require('markdown-it')({\n *   highlight: function (str, lang) {\n *     if (lang && hljs.getLanguage(lang)) {\n *       try {\n *         return '<pre><code class=\"hljs\">' +\n *                hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +\n *                '</code></pre>';\n *       } catch (__) {}\n *     }\n *\n *     return '<pre><code class=\"hljs\">' + md.utils.escapeHtml(str) + '</code></pre>';\n *   }\n * });\n * ```\n *\n **/\nfunction MarkdownIt (presetName, options) {\n  if (!(this instanceof MarkdownIt)) {\n    return new MarkdownIt(presetName, options)\n  }\n\n  if (!options) {\n    if (!utils.isString(presetName)) {\n      options = presetName || {}\n      presetName = 'default'\n    }\n  }\n\n  /**\n   * MarkdownIt#inline -> ParserInline\n   *\n   * Instance of [[ParserInline]]. You may need it to add new rules when\n   * writing plugins. For simple rules control use [[MarkdownIt.disable]] and\n   * [[MarkdownIt.enable]].\n   **/\n  this.inline = new ParserInline()\n\n  /**\n   * MarkdownIt#block -> ParserBlock\n   *\n   * Instance of [[ParserBlock]]. You may need it to add new rules when\n   * writing plugins. For simple rules control use [[MarkdownIt.disable]] and\n   * [[MarkdownIt.enable]].\n   **/\n  this.block = new ParserBlock()\n\n  /**\n   * MarkdownIt#core -> Core\n   *\n   * Instance of [[Core]] chain executor. You may need it to add new rules when\n   * writing plugins. For simple rules control use [[MarkdownIt.disable]] and\n   * [[MarkdownIt.enable]].\n   **/\n  this.core = new ParserCore()\n\n  /**\n   * MarkdownIt#renderer -> Renderer\n   *\n   * Instance of [[Renderer]]. Use it to modify output look. Or to add rendering\n   * rules for new token types, generated by plugins.\n   *\n   * ##### Example\n   *\n   * ```javascript\n   * var md = require('markdown-it')();\n   *\n   * function myToken(tokens, idx, options, env, self) {\n   *   //...\n   *   return result;\n   * };\n   *\n   * md.renderer.rules['my_token'] = myToken\n   * ```\n   *\n   * See [[Renderer]] docs and [source code](https://github.com/markdown-it/markdown-it/blob/master/lib/renderer.mjs).\n   **/\n  this.renderer = new Renderer()\n\n  /**\n   * MarkdownIt#linkify -> LinkifyIt\n   *\n   * [linkify-it](https://github.com/markdown-it/linkify-it) instance.\n   * Used by [linkify](https://github.com/markdown-it/markdown-it/blob/master/lib/rules_core/linkify.mjs)\n   * rule.\n   **/\n  this.linkify = new LinkifyIt()\n\n  /**\n   * MarkdownIt#validateLink(url) -> Boolean\n   *\n   * Link validation function. CommonMark allows too much in links. By default\n   * we disable `javascript:`, `vbscript:`, `file:` schemas, and almost all `data:...` schemas\n   * except some embedded image types.\n   *\n   * You can change this behaviour:\n   *\n   * ```javascript\n   * var md = require('markdown-it')();\n   * // enable everything\n   * md.validateLink = function () { return true; }\n   * ```\n   **/\n  this.validateLink = validateLink\n\n  /**\n   * MarkdownIt#normalizeLink(url) -> String\n   *\n   * Function used to encode link url to a machine-readable format,\n   * which includes url-encoding, punycode, etc.\n   **/\n  this.normalizeLink = normalizeLink\n\n  /**\n   * MarkdownIt#normalizeLinkText(url) -> String\n   *\n   * Function used to decode link url to a human-readable format`\n   **/\n  this.normalizeLinkText = normalizeLinkText\n\n  // Expose utils & helpers for easy acces from plugins\n\n  /**\n   * MarkdownIt#utils -> utils\n   *\n   * Assorted utility functions, useful to write plugins. See details\n   * [here](https://github.com/markdown-it/markdown-it/blob/master/lib/common/utils.mjs).\n   **/\n  this.utils = utils\n\n  /**\n   * MarkdownIt#helpers -> helpers\n   *\n   * Link components parser functions, useful to write plugins. See details\n   * [here](https://github.com/markdown-it/markdown-it/blob/master/lib/helpers).\n   **/\n  this.helpers = utils.assign({}, helpers)\n\n  this.options = {}\n  this.configure(presetName)\n\n  if (options) { this.set(options) }\n}\n\n/** chainable\n * MarkdownIt.set(options)\n *\n * Set parser options (in the same format as in constructor). Probably, you\n * will never need it, but you can change options after constructor call.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')()\n *             .set({ html: true, breaks: true })\n *             .set({ typographer, true });\n * ```\n *\n * __Note:__ To achieve the best possible performance, don't modify a\n * `markdown-it` instance options on the fly. If you need multiple configurations\n * it's best to create multiple instances and initialize each with separate\n * config.\n **/\nMarkdownIt.prototype.set = function (options) {\n  utils.assign(this.options, options)\n  return this\n}\n\n/** chainable, internal\n * MarkdownIt.configure(presets)\n *\n * Batch load of all options and compenent settings. This is internal method,\n * and you probably will not need it. But if you will - see available presets\n * and data structure [here](https://github.com/markdown-it/markdown-it/tree/master/lib/presets)\n *\n * We strongly recommend to use presets instead of direct config loads. That\n * will give better compatibility with next versions.\n **/\nMarkdownIt.prototype.configure = function (presets) {\n  const self = this\n\n  if (utils.isString(presets)) {\n    const presetName = presets\n    presets = config[presetName]\n    if (!presets) { throw new Error('Wrong `markdown-it` preset \"' + presetName + '\", check name') }\n  }\n\n  if (!presets) { throw new Error('Wrong `markdown-it` preset, can\\'t be empty') }\n\n  if (presets.options) { self.set(presets.options) }\n\n  if (presets.components) {\n    Object.keys(presets.components).forEach(function (name) {\n      if (presets.components[name].rules) {\n        self[name].ruler.enableOnly(presets.components[name].rules)\n      }\n      if (presets.components[name].rules2) {\n        self[name].ruler2.enableOnly(presets.components[name].rules2)\n      }\n    })\n  }\n  return this\n}\n\n/** chainable\n * MarkdownIt.enable(list, ignoreInvalid)\n * - list (String|Array): rule name or list of rule names to enable\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * Enable list or rules. It will automatically find appropriate components,\n * containing rules with given names. If rule not found, and `ignoreInvalid`\n * not set - throws exception.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')()\n *             .enable(['sub', 'sup'])\n *             .disable('smartquotes');\n * ```\n **/\nMarkdownIt.prototype.enable = function (list, ignoreInvalid) {\n  let result = []\n\n  if (!Array.isArray(list)) { list = [list] }\n\n  ['core', 'block', 'inline'].forEach(function (chain) {\n    result = result.concat(this[chain].ruler.enable(list, true))\n  }, this)\n\n  result = result.concat(this.inline.ruler2.enable(list, true))\n\n  const missed = list.filter(function (name) { return result.indexOf(name) < 0 })\n\n  if (missed.length && !ignoreInvalid) {\n    throw new Error('MarkdownIt. Failed to enable unknown rule(s): ' + missed)\n  }\n\n  return this\n}\n\n/** chainable\n * MarkdownIt.disable(list, ignoreInvalid)\n * - list (String|Array): rule name or list of rule names to disable.\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * The same as [[MarkdownIt.enable]], but turn specified rules off.\n **/\nMarkdownIt.prototype.disable = function (list, ignoreInvalid) {\n  let result = []\n\n  if (!Array.isArray(list)) { list = [list] }\n\n  ['core', 'block', 'inline'].forEach(function (chain) {\n    result = result.concat(this[chain].ruler.disable(list, true))\n  }, this)\n\n  result = result.concat(this.inline.ruler2.disable(list, true))\n\n  const missed = list.filter(function (name) { return result.indexOf(name) < 0 })\n\n  if (missed.length && !ignoreInvalid) {\n    throw new Error('MarkdownIt. Failed to disable unknown rule(s): ' + missed)\n  }\n  return this\n}\n\n/** chainable\n * MarkdownIt.use(plugin, params)\n *\n * Load specified plugin with given params into current parser instance.\n * It's just a sugar to call `plugin(md, params)` with curring.\n *\n * ##### Example\n *\n * ```javascript\n * var iterator = require('markdown-it-for-inline');\n * var md = require('markdown-it')()\n *             .use(iterator, 'foo_replace', 'text', function (tokens, idx) {\n *               tokens[idx].content = tokens[idx].content.replace(/foo/g, 'bar');\n *             });\n * ```\n **/\nMarkdownIt.prototype.use = function (plugin /*, params, ... */) {\n  const args = [this].concat(Array.prototype.slice.call(arguments, 1))\n  plugin.apply(plugin, args)\n  return this\n}\n\n/** internal\n * MarkdownIt.parse(src, env) -> Array\n * - src (String): source string\n * - env (Object): environment sandbox\n *\n * Parse input string and return list of block tokens (special token type\n * \"inline\" will contain list of inline tokens). You should not call this\n * method directly, until you write custom renderer (for example, to produce\n * AST).\n *\n * `env` is used to pass data between \"distributed\" rules and return additional\n * metadata like reference info, needed for the renderer. It also can be used to\n * inject data in specific cases. Usually, you will be ok to pass `{}`,\n * and then pass updated object to renderer.\n **/\nMarkdownIt.prototype.parse = function (src, env) {\n  if (typeof src !== 'string') {\n    throw new Error('Input data should be a String')\n  }\n\n  const state = new this.core.State(src, this, env)\n\n  this.core.process(state)\n\n  return state.tokens\n}\n\n/**\n * MarkdownIt.render(src [, env]) -> String\n * - src (String): source string\n * - env (Object): environment sandbox\n *\n * Render markdown string into html. It does all magic for you :).\n *\n * `env` can be used to inject additional metadata (`{}` by default).\n * But you will not need it with high probability. See also comment\n * in [[MarkdownIt.parse]].\n **/\nMarkdownIt.prototype.render = function (src, env) {\n  env = env || {}\n\n  return this.renderer.render(this.parse(src, env), this.options, env)\n}\n\n/** internal\n * MarkdownIt.parseInline(src, env) -> Array\n * - src (String): source string\n * - env (Object): environment sandbox\n *\n * The same as [[MarkdownIt.parse]] but skip all block rules. It returns the\n * block tokens list with the single `inline` element, containing parsed inline\n * tokens in `children` property. Also updates `env` object.\n **/\nMarkdownIt.prototype.parseInline = function (src, env) {\n  const state = new this.core.State(src, this, env)\n\n  state.inlineMode = true\n  this.core.process(state)\n\n  return state.tokens\n}\n\n/**\n * MarkdownIt.renderInline(src [, env]) -> String\n * - src (String): source string\n * - env (Object): environment sandbox\n *\n * Similar to [[MarkdownIt.render]] but for single paragraph content. Result\n * will NOT be wrapped into `<p>` tags.\n **/\nMarkdownIt.prototype.renderInline = function (src, env) {\n  env = env || {}\n\n  return this.renderer.render(this.parseInline(src, env), this.options, env)\n}\n\nexport default MarkdownIt\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACEA,IAAM,cAAc,CAAC;AAErB,SAAS,eAAgB,SAAS;AAChC,MAAI,QAAQ,YAAY,OAAO;AAC/B,MAAI,OAAO;AAAE,WAAO;AAAA,EAAM;AAE1B,UAAQ,YAAY,OAAO,IAAI,CAAC;AAEhC,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,UAAM,KAAK,OAAO,aAAa,CAAC;AAChC,UAAM,KAAK,EAAE;AAAA,EACf;AAEA,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAM,KAAK,QAAQ,WAAW,CAAC;AAC/B,UAAM,EAAE,IAAI,OAAO,MAAM,GAAG,SAAS,EAAE,EAAE,YAAY,GAAG,MAAM,EAAE;AAAA,EAClE;AAEA,SAAO;AACT;AAIA,SAAS,OAAQ,QAAQ,SAAS;AAChC,MAAI,OAAO,YAAY,UAAU;AAC/B,cAAU,OAAO;AAAA,EACnB;AAEA,QAAM,QAAQ,eAAe,OAAO;AAEpC,SAAO,OAAO,QAAQ,qBAAqB,SAAU,KAAK;AACxD,QAAI,SAAS;AAEb,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,KAAK,GAAG;AAC7C,YAAM,KAAK,SAAS,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE;AAE/C,UAAI,KAAK,KAAM;AACb,kBAAU,MAAM,EAAE;AAClB;AAAA,MACF;AAEA,WAAK,KAAK,SAAU,OAAS,IAAI,IAAI,GAAI;AAEvC,cAAM,KAAK,SAAS,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE;AAE/C,aAAK,KAAK,SAAU,KAAM;AACxB,gBAAM,MAAQ,MAAM,IAAK,OAAU,KAAK;AAExC,cAAI,MAAM,KAAM;AACd,sBAAU;AAAA,UACZ,OAAO;AACL,sBAAU,OAAO,aAAa,GAAG;AAAA,UACnC;AAEA,eAAK;AACL;AAAA,QACF;AAAA,MACF;AAEA,WAAK,KAAK,SAAU,OAAS,IAAI,IAAI,GAAI;AAEvC,cAAM,KAAK,SAAS,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE;AAC/C,cAAM,KAAK,SAAS,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE;AAE/C,aAAK,KAAK,SAAU,QAAS,KAAK,SAAU,KAAM;AAChD,gBAAM,MAAQ,MAAM,KAAM,QAAY,MAAM,IAAK,OAAU,KAAK;AAEhE,cAAI,MAAM,QAAU,OAAO,SAAU,OAAO,OAAS;AACnD,sBAAU;AAAA,UACZ,OAAO;AACL,sBAAU,OAAO,aAAa,GAAG;AAAA,UACnC;AAEA,eAAK;AACL;AAAA,QACF;AAAA,MACF;AAEA,WAAK,KAAK,SAAU,OAAS,IAAI,IAAI,GAAI;AAEvC,cAAM,KAAK,SAAS,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE;AAC/C,cAAM,KAAK,SAAS,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE;AAC/C,cAAM,KAAK,SAAS,IAAI,MAAM,IAAI,IAAI,IAAI,EAAE,GAAG,EAAE;AAEjD,aAAK,KAAK,SAAU,QAAS,KAAK,SAAU,QAAS,KAAK,SAAU,KAAM;AACxE,cAAI,MAAQ,MAAM,KAAM,UAAc,MAAM,KAAM,SAAa,MAAM,IAAK,OAAU,KAAK;AAEzF,cAAI,MAAM,SAAW,MAAM,SAAU;AACnC,sBAAU;AAAA,UACZ,OAAO;AACL,mBAAO;AACP,sBAAU,OAAO,aAAa,SAAU,OAAO,KAAK,SAAU,MAAM,KAAM;AAAA,UAC5E;AAEA,eAAK;AACL;AAAA,QACF;AAAA,MACF;AAEA,gBAAU;AAAA,IACZ;AAEA,WAAO;AAAA,EACT,CAAC;AACH;AAEA,OAAO,eAAe;AACtB,OAAO,iBAAiB;AAExB,IAAO,iBAAQ;;;AC/Gf,IAAM,cAAc,CAAC;AAKrB,SAAS,eAAgB,SAAS;AAChC,MAAI,QAAQ,YAAY,OAAO;AAC/B,MAAI,OAAO;AAAE,WAAO;AAAA,EAAM;AAE1B,UAAQ,YAAY,OAAO,IAAI,CAAC;AAEhC,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,UAAM,KAAK,OAAO,aAAa,CAAC;AAEhC,QAAI,cAAc,KAAK,EAAE,GAAG;AAE1B,YAAM,KAAK,EAAE;AAAA,IACf,OAAO;AACL,YAAM,KAAK,OAAO,MAAM,EAAE,SAAS,EAAE,EAAE,YAAY,GAAG,MAAM,EAAE,CAAC;AAAA,IACjE;AAAA,EACF;AAEA,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAM,QAAQ,WAAW,CAAC,CAAC,IAAI,QAAQ,CAAC;AAAA,EAC1C;AAEA,SAAO;AACT;AASA,SAAS,OAAQ,QAAQ,SAAS,aAAa;AAC7C,MAAI,OAAO,YAAY,UAAU;AAE/B,kBAAc;AACd,cAAU,OAAO;AAAA,EACnB;AAEA,MAAI,OAAO,gBAAgB,aAAa;AACtC,kBAAc;AAAA,EAChB;AAEA,QAAM,QAAQ,eAAe,OAAO;AACpC,MAAI,SAAS;AAEb,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,UAAMC,QAAO,OAAO,WAAW,CAAC;AAEhC,QAAI,eAAeA,UAAS,MAAgB,IAAI,IAAI,GAAG;AACrD,UAAI,iBAAiB,KAAK,OAAO,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG;AACrD,kBAAU,OAAO,MAAM,GAAG,IAAI,CAAC;AAC/B,aAAK;AACL;AAAA,MACF;AAAA,IACF;AAEA,QAAIA,QAAO,KAAK;AACd,gBAAU,MAAMA,KAAI;AACpB;AAAA,IACF;AAEA,QAAIA,SAAQ,SAAUA,SAAQ,OAAQ;AACpC,UAAIA,SAAQ,SAAUA,SAAQ,SAAU,IAAI,IAAI,GAAG;AACjD,cAAM,WAAW,OAAO,WAAW,IAAI,CAAC;AACxC,YAAI,YAAY,SAAU,YAAY,OAAQ;AAC5C,oBAAU,mBAAmB,OAAO,CAAC,IAAI,OAAO,IAAI,CAAC,CAAC;AACtD;AACA;AAAA,QACF;AAAA,MACF;AACA,gBAAU;AACV;AAAA,IACF;AAEA,cAAU,mBAAmB,OAAO,CAAC,CAAC;AAAA,EACxC;AAEA,SAAO;AACT;AAEA,OAAO,eAAe;AACtB,OAAO,iBAAiB;AAExB,IAAO,iBAAQ;;;ACxFA,SAAR,OAAyB,KAAK;AACnC,MAAI,SAAS;AAEb,YAAU,IAAI,YAAY;AAC1B,YAAU,IAAI,UAAU,OAAO;AAC/B,YAAU,IAAI,OAAO,IAAI,OAAO,MAAM;AAEtC,MAAI,IAAI,YAAY,IAAI,SAAS,QAAQ,GAAG,MAAM,IAAI;AAEpD,cAAU,MAAM,IAAI,WAAW;AAAA,EACjC,OAAO;AACL,cAAU,IAAI,YAAY;AAAA,EAC5B;AAEA,YAAU,IAAI,OAAO,MAAM,IAAI,OAAO;AACtC,YAAU,IAAI,YAAY;AAC1B,YAAU,IAAI,UAAU;AACxB,YAAU,IAAI,QAAQ;AAEtB,SAAO;AACT;;;ACsBA,SAAS,MAAO;AACd,OAAK,WAAW;AAChB,OAAK,UAAU;AACf,OAAK,OAAO;AACZ,OAAK,OAAO;AACZ,OAAK,WAAW;AAChB,OAAK,OAAO;AACZ,OAAK,SAAS;AACd,OAAK,WAAW;AAClB;AAMA,IAAM,kBAAkB;AACxB,IAAM,cAAc;AAIpB,IAAM,oBAAoB;AAI1B,IAAM,SAAS,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,GAAI;AAGzD,IAAM,SAAS,CAAC,KAAK,KAAK,KAAK,MAAM,KAAK,GAAG,EAAE,OAAO,MAAM;AAG5D,IAAM,aAAa,CAAC,GAAI,EAAE,OAAO,MAAM;AAKvC,IAAM,eAAe,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG,EAAE,OAAO,UAAU;AAChE,IAAM,kBAAkB,CAAC,KAAK,KAAK,GAAG;AACtC,IAAM,iBAAiB;AACvB,IAAM,sBAAsB;AAC5B,IAAM,oBAAoB;AAG1B,IAAM,mBAAmB;AAAA,EACvB,YAAY;AAAA,EACZ,eAAe;AACjB;AAEA,IAAM,kBAAkB;AAAA,EACtB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,SAAS;AAAA,EACT,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,SAAS;AACX;AAEA,SAAS,SAAU,KAAK,mBAAmB;AACzC,MAAI,OAAO,eAAe,IAAK,QAAO;AAEtC,QAAM,IAAI,IAAI,IAAI;AAClB,IAAE,MAAM,KAAK,iBAAiB;AAC9B,SAAO;AACT;AAEA,IAAI,UAAU,QAAQ,SAAU,KAAK,mBAAmB;AACtD,MAAI,YAAY,KAAK;AACrB,MAAI,OAAO;AAIX,SAAO,KAAK,KAAK;AAEjB,MAAI,CAAC,qBAAqB,IAAI,MAAM,GAAG,EAAE,WAAW,GAAG;AAErD,UAAM,aAAa,kBAAkB,KAAK,IAAI;AAC9C,QAAI,YAAY;AACd,WAAK,WAAW,WAAW,CAAC;AAC5B,UAAI,WAAW,CAAC,GAAG;AACjB,aAAK,SAAS,WAAW,CAAC;AAAA,MAC5B;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,QAAQ,gBAAgB,KAAK,IAAI;AACrC,MAAI,OAAO;AACT,YAAQ,MAAM,CAAC;AACf,iBAAa,MAAM,YAAY;AAC/B,SAAK,WAAW;AAChB,WAAO,KAAK,OAAO,MAAM,MAAM;AAAA,EACjC;AAOA,MAAI,qBAAqB,SAAS,KAAK,MAAM,sBAAsB,GAAG;AACpE,cAAU,KAAK,OAAO,GAAG,CAAC,MAAM;AAChC,QAAI,WAAW,EAAE,SAAS,iBAAiB,KAAK,IAAI;AAClD,aAAO,KAAK,OAAO,CAAC;AACpB,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAEA,MAAI,CAAC,iBAAiB,KAAK,MACtB,WAAY,SAAS,CAAC,gBAAgB,KAAK,IAAK;AAiBnD,QAAI,UAAU;AACd,aAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,YAAM,KAAK,QAAQ,gBAAgB,CAAC,CAAC;AACrC,UAAI,QAAQ,OAAO,YAAY,MAAM,MAAM,UAAU;AACnD,kBAAU;AAAA,MACZ;AAAA,IACF;AAIA,QAAI,MAAM;AACV,QAAI,YAAY,IAAI;AAElB,eAAS,KAAK,YAAY,GAAG;AAAA,IAC/B,OAAO;AAGL,eAAS,KAAK,YAAY,KAAK,OAAO;AAAA,IACxC;AAIA,QAAI,WAAW,IAAI;AACjB,aAAO,KAAK,MAAM,GAAG,MAAM;AAC3B,aAAO,KAAK,MAAM,SAAS,CAAC;AAC5B,WAAK,OAAO;AAAA,IACd;AAGA,cAAU;AACV,aAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,YAAM,KAAK,QAAQ,aAAa,CAAC,CAAC;AAClC,UAAI,QAAQ,OAAO,YAAY,MAAM,MAAM,UAAU;AACnD,kBAAU;AAAA,MACZ;AAAA,IACF;AAEA,QAAI,YAAY,IAAI;AAClB,gBAAU,KAAK;AAAA,IACjB;AAEA,QAAI,KAAK,UAAU,CAAC,MAAM,KAAK;AAAE;AAAA,IAAU;AAC3C,UAAM,OAAO,KAAK,MAAM,GAAG,OAAO;AAClC,WAAO,KAAK,MAAM,OAAO;AAGzB,SAAK,UAAU,IAAI;AAInB,SAAK,WAAW,KAAK,YAAY;AAIjC,UAAM,eAAe,KAAK,SAAS,CAAC,MAAM,OACtC,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC,MAAM;AAGhD,QAAI,CAAC,cAAc;AACjB,YAAM,YAAY,KAAK,SAAS,MAAM,IAAI;AAC1C,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AAChD,cAAM,OAAO,UAAU,CAAC;AACxB,YAAI,CAAC,MAAM;AAAE;AAAA,QAAS;AACtB,YAAI,CAAC,KAAK,MAAM,mBAAmB,GAAG;AACpC,cAAI,UAAU;AACd,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK;AAC3C,gBAAI,KAAK,WAAW,CAAC,IAAI,KAAK;AAI5B,yBAAW;AAAA,YACb,OAAO;AACL,yBAAW,KAAK,CAAC;AAAA,YACnB;AAAA,UACF;AAEA,cAAI,CAAC,QAAQ,MAAM,mBAAmB,GAAG;AACvC,kBAAM,aAAa,UAAU,MAAM,GAAG,CAAC;AACvC,kBAAM,UAAU,UAAU,MAAM,IAAI,CAAC;AACrC,kBAAM,MAAM,KAAK,MAAM,iBAAiB;AACxC,gBAAI,KAAK;AACP,yBAAW,KAAK,IAAI,CAAC,CAAC;AACtB,sBAAQ,QAAQ,IAAI,CAAC,CAAC;AAAA,YACxB;AACA,gBAAI,QAAQ,QAAQ;AAClB,qBAAO,QAAQ,KAAK,GAAG,IAAI;AAAA,YAC7B;AACA,iBAAK,WAAW,WAAW,KAAK,GAAG;AACnC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,QAAI,KAAK,SAAS,SAAS,gBAAgB;AACzC,WAAK,WAAW;AAAA,IAClB;AAIA,QAAI,cAAc;AAChB,WAAK,WAAW,KAAK,SAAS,OAAO,GAAG,KAAK,SAAS,SAAS,CAAC;AAAA,IAClE;AAAA,EACF;AAGA,QAAM,OAAO,KAAK,QAAQ,GAAG;AAC7B,MAAI,SAAS,IAAI;AAEf,SAAK,OAAO,KAAK,OAAO,IAAI;AAC5B,WAAO,KAAK,MAAM,GAAG,IAAI;AAAA,EAC3B;AACA,QAAM,KAAK,KAAK,QAAQ,GAAG;AAC3B,MAAI,OAAO,IAAI;AACb,SAAK,SAAS,KAAK,OAAO,EAAE;AAC5B,WAAO,KAAK,MAAM,GAAG,EAAE;AAAA,EACzB;AACA,MAAI,MAAM;AAAE,SAAK,WAAW;AAAA,EAAK;AACjC,MAAI,gBAAgB,UAAU,KAC1B,KAAK,YAAY,CAAC,KAAK,UAAU;AACnC,SAAK,WAAW;AAAA,EAClB;AAEA,SAAO;AACT;AAEA,IAAI,UAAU,YAAY,SAAU,MAAM;AACxC,MAAI,OAAO,YAAY,KAAK,IAAI;AAChC,MAAI,MAAM;AACR,WAAO,KAAK,CAAC;AACb,QAAI,SAAS,KAAK;AAChB,WAAK,OAAO,KAAK,OAAO,CAAC;AAAA,IAC3B;AACA,WAAO,KAAK,OAAO,GAAG,KAAK,SAAS,KAAK,MAAM;AAAA,EACjD;AACA,MAAI,MAAM;AAAE,SAAK,WAAW;AAAA,EAAK;AACnC;AAEA,IAAO,gBAAQ;;;ACnTf;AAAA;AAAA;AAAA,YAAAC;AAAA,EAAA,UAAAA;AAAA,EAAA,SAAAA;AAAA,EAAA,SAAAA;AAAA,EAAA,SAAAA;AAAA;;;ACAA,IAAO,gBAAQ;;;ACAf,IAAOC,iBAAQ;;;ACAf,IAAOC,iBAAQ;;;ACAf,IAAOC,iBAAQ;;;ACAf,IAAOC,iBAAQ;;;ACAf,IAAOC,iBAAQ;;;ACEf,IAAA,2BAAe,IAAI;;EAEf,2keACK,MAAM,EAAE,EACR,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AAAC;;;ACJpC,IAAA,0BAAe,IAAI;;EAEf,wCACK,MAAM,EAAE,EACR,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AAAC;;;;ACJpC,IAAM,YAAY,oBAAI,IAAI;EACtB,CAAC,GAAG,KAAK;;EAET,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,GAAG;EACT,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,GAAG;EACT,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,GAAG;EACT,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,GAAG;EACT,CAAC,KAAK,GAAG;EACT,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,GAAG;EACT,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,GAAG;EACT,CAAC,KAAK,IAAI;EACV,CAAC,KAAK,GAAG;EACT,CAAC,KAAK,GAAG;EACT,CAAC,KAAK,GAAG;CACZ;AAKM,IAAM;;GAET,KAAA,OAAO,mBAAa,QAAA,OAAA,SAAA,KACpB,SAAU,WAAiB;AACvB,QAAI,SAAS;AAEb,QAAI,YAAY,OAAQ;AACpB,mBAAa;AACb,gBAAU,OAAO,aACX,cAAc,KAAM,OAAS,KAAM;AAEzC,kBAAY,QAAU,YAAY;;AAGtC,cAAU,OAAO,aAAa,SAAS;AACvC,WAAO;EACX;;AAOE,SAAU,iBAAiB,WAAiB;;AAC9C,MAAK,aAAa,SAAU,aAAa,SAAW,YAAY,SAAU;AACtE,WAAO;;AAGX,UAAOC,MAAA,UAAU,IAAI,SAAS,OAAC,QAAAA,QAAA,SAAAA,MAAI;AACvC;;;ACvDA,IAAW;CAAX,SAAWC,YAAS;AAChB,EAAAA,WAAAA,WAAA,KAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,MAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,QAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,MAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,MAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,SAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,SAAA,IAAA,GAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,SAAA,IAAA,GAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,SAAA,IAAA,GAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,SAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,SAAA,IAAA,EAAA,IAAA;AACA,EAAAA,WAAAA,WAAA,SAAA,IAAA,EAAA,IAAA;AACJ,GAbW,cAAA,YAAS,CAAA,EAAA;AAgBpB,IAAM,eAAe;AAErB,IAAY;CAAZ,SAAYC,eAAY;AACpB,EAAAA,cAAAA,cAAA,cAAA,IAAA,KAAA,IAAA;AACA,EAAAA,cAAAA,cAAA,eAAA,IAAA,KAAA,IAAA;AACA,EAAAA,cAAAA,cAAA,YAAA,IAAA,GAAA,IAAA;AACJ,GAJY,iBAAA,eAAY,CAAA,EAAA;AAMxB,SAAS,SAASC,OAAY;AAC1B,SAAOA,SAAQ,UAAU,QAAQA,SAAQ,UAAU;AACvD;AAEA,SAAS,uBAAuBA,OAAY;AACxC,SACKA,SAAQ,UAAU,WAAWA,SAAQ,UAAU,WAC/CA,SAAQ,UAAU,WAAWA,SAAQ,UAAU;AAExD;AAEA,SAAS,oBAAoBA,OAAY;AACrC,SACKA,SAAQ,UAAU,WAAWA,SAAQ,UAAU,WAC/CA,SAAQ,UAAU,WAAWA,SAAQ,UAAU,WAChD,SAASA,KAAI;AAErB;AAQA,SAAS,8BAA8BA,OAAY;AAC/C,SAAOA,UAAS,UAAU,UAAU,oBAAoBA,KAAI;AAChE;AAEA,IAAW;CAAX,SAAWC,qBAAkB;AACzB,EAAAA,oBAAAA,oBAAA,aAAA,IAAA,CAAA,IAAA;AACA,EAAAA,oBAAAA,oBAAA,cAAA,IAAA,CAAA,IAAA;AACA,EAAAA,oBAAAA,oBAAA,gBAAA,IAAA,CAAA,IAAA;AACA,EAAAA,oBAAAA,oBAAA,YAAA,IAAA,CAAA,IAAA;AACA,EAAAA,oBAAAA,oBAAA,aAAA,IAAA,CAAA,IAAA;AACJ,GANW,uBAAA,qBAAkB,CAAA,EAAA;AAQ7B,IAAY;CAAZ,SAAYC,eAAY;AAEpB,EAAAA,cAAAA,cAAA,QAAA,IAAA,CAAA,IAAA;AAEA,EAAAA,cAAAA,cAAA,QAAA,IAAA,CAAA,IAAA;AAEA,EAAAA,cAAAA,cAAA,WAAA,IAAA,CAAA,IAAA;AACJ,GAPY,iBAAA,eAAY,CAAA,EAAA;AAuBlB,IAAO,gBAAP,MAAoB;EACtB,YAEqB,YAUA,eAEAC,SAA4B;AAZ5B,SAAA,aAAA;AAUA,SAAA,gBAAA;AAEA,SAAA,SAAAA;AAIb,SAAA,QAAQ,mBAAmB;AAE3B,SAAA,WAAW;AAOX,SAAA,SAAS;AAGT,SAAA,YAAY;AAEZ,SAAA,SAAS;AAET,SAAA,aAAa,aAAa;EAnB/B;;EAsBH,YAAY,YAAwB;AAChC,SAAK,aAAa;AAClB,SAAK,QAAQ,mBAAmB;AAChC,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,WAAW;EACpB;;;;;;;;;;;;EAaA,MAAM,KAAa,QAAc;AAC7B,YAAQ,KAAK,OAAO;MAChB,KAAK,mBAAmB,aAAa;AACjC,YAAI,IAAI,WAAW,MAAM,MAAM,UAAU,KAAK;AAC1C,eAAK,QAAQ,mBAAmB;AAChC,eAAK,YAAY;AACjB,iBAAO,KAAK,kBAAkB,KAAK,SAAS,CAAC;;AAEjD,aAAK,QAAQ,mBAAmB;AAChC,eAAO,KAAK,iBAAiB,KAAK,MAAM;;MAG5C,KAAK,mBAAmB,cAAc;AAClC,eAAO,KAAK,kBAAkB,KAAK,MAAM;;MAG7C,KAAK,mBAAmB,gBAAgB;AACpC,eAAO,KAAK,oBAAoB,KAAK,MAAM;;MAG/C,KAAK,mBAAmB,YAAY;AAChC,eAAO,KAAK,gBAAgB,KAAK,MAAM;;MAG3C,KAAK,mBAAmB,aAAa;AACjC,eAAO,KAAK,iBAAiB,KAAK,MAAM;;;EAGpD;;;;;;;;;;EAWQ,kBAAkB,KAAa,QAAc;AACjD,QAAI,UAAU,IAAI,QAAQ;AACtB,aAAO;;AAGX,SAAK,IAAI,WAAW,MAAM,IAAI,kBAAkB,UAAU,SAAS;AAC/D,WAAK,QAAQ,mBAAmB;AAChC,WAAK,YAAY;AACjB,aAAO,KAAK,gBAAgB,KAAK,SAAS,CAAC;;AAG/C,SAAK,QAAQ,mBAAmB;AAChC,WAAO,KAAK,oBAAoB,KAAK,MAAM;EAC/C;EAEQ,mBACJ,KACA,OACA,KACAC,OAAY;AAEZ,QAAI,UAAU,KAAK;AACf,YAAM,aAAa,MAAM;AACzB,WAAK,SACD,KAAK,SAAS,KAAK,IAAIA,OAAM,UAAU,IACvC,SAAS,IAAI,OAAO,OAAO,UAAU,GAAGA,KAAI;AAChD,WAAK,YAAY;;EAEzB;;;;;;;;;;EAWQ,gBAAgB,KAAa,QAAc;AAC/C,UAAM,WAAW;AAEjB,WAAO,SAAS,IAAI,QAAQ;AACxB,YAAM,OAAO,IAAI,WAAW,MAAM;AAClC,UAAI,SAAS,IAAI,KAAK,uBAAuB,IAAI,GAAG;AAChD,kBAAU;aACP;AACH,aAAK,mBAAmB,KAAK,UAAU,QAAQ,EAAE;AACjD,eAAO,KAAK,kBAAkB,MAAM,CAAC;;;AAI7C,SAAK,mBAAmB,KAAK,UAAU,QAAQ,EAAE;AAEjD,WAAO;EACX;;;;;;;;;;EAWQ,oBAAoB,KAAa,QAAc;AACnD,UAAM,WAAW;AAEjB,WAAO,SAAS,IAAI,QAAQ;AACxB,YAAM,OAAO,IAAI,WAAW,MAAM;AAClC,UAAI,SAAS,IAAI,GAAG;AAChB,kBAAU;aACP;AACH,aAAK,mBAAmB,KAAK,UAAU,QAAQ,EAAE;AACjD,eAAO,KAAK,kBAAkB,MAAM,CAAC;;;AAI7C,SAAK,mBAAmB,KAAK,UAAU,QAAQ,EAAE;AAEjD,WAAO;EACX;;;;;;;;;;;;;;EAeQ,kBAAkB,QAAgB,gBAAsB;;AAE5D,QAAI,KAAK,YAAY,gBAAgB;AACjC,OAAAC,MAAA,KAAK,YAAM,QAAAA,QAAA,SAAA,SAAAA,IAAE,2CACT,KAAK,QAAQ;AAEjB,aAAO;;AAIX,QAAI,WAAW,UAAU,MAAM;AAC3B,WAAK,YAAY;eACV,KAAK,eAAe,aAAa,QAAQ;AAChD,aAAO;;AAGX,SAAK,cAAc,iBAAiB,KAAK,MAAM,GAAG,KAAK,QAAQ;AAE/D,QAAI,KAAK,QAAQ;AACb,UAAI,WAAW,UAAU,MAAM;AAC3B,aAAK,OAAO,wCAAuC;;AAGvD,WAAK,OAAO,kCAAkC,KAAK,MAAM;;AAG7D,WAAO,KAAK;EAChB;;;;;;;;;;EAWQ,iBAAiB,KAAa,QAAc;AAChD,UAAM,EAAE,WAAU,IAAK;AACvB,QAAI,UAAU,WAAW,KAAK,SAAS;AAEvC,QAAI,eAAe,UAAU,aAAa,iBAAiB;AAE3D,WAAO,SAAS,IAAI,QAAQ,UAAU,KAAK,UAAU;AACjD,YAAM,OAAO,IAAI,WAAW,MAAM;AAElC,WAAK,YAAY,gBACb,YACA,SACA,KAAK,YAAY,KAAK,IAAI,GAAG,WAAW,GACxC,IAAI;AAGR,UAAI,KAAK,YAAY,GAAG;AACpB,eAAO,KAAK,WAAW;QAElB,KAAK,eAAe,aAAa;SAE7B,gBAAgB;QAEb,8BAA8B,IAAI,KACxC,IACA,KAAK,6BAA4B;;AAG3C,gBAAU,WAAW,KAAK,SAAS;AACnC,qBAAe,UAAU,aAAa,iBAAiB;AAGvD,UAAI,gBAAgB,GAAG;AAEnB,YAAI,SAAS,UAAU,MAAM;AACzB,iBAAO,KAAK,oBACR,KAAK,WACL,aACA,KAAK,WAAW,KAAK,MAAM;;AAKnC,YAAI,KAAK,eAAe,aAAa,QAAQ;AACzC,eAAK,SAAS,KAAK;AACnB,eAAK,YAAY,KAAK;AACtB,eAAK,SAAS;;;;AAK1B,WAAO;EACX;;;;;;EAOQ,+BAA4B;;AAChC,UAAM,EAAE,QAAQ,WAAU,IAAK;AAE/B,UAAM,eACD,WAAW,MAAM,IAAI,aAAa,iBAAiB;AAExD,SAAK,oBAAoB,QAAQ,aAAa,KAAK,QAAQ;AAC3D,KAAAA,MAAA,KAAK,YAAM,QAAAA,QAAA,SAAA,SAAAA,IAAE,wCAAuC;AAEpD,WAAO,KAAK;EAChB;;;;;;;;;;EAWQ,oBACJ,QACA,aACA,UAAgB;AAEhB,UAAM,EAAE,WAAU,IAAK;AAEvB,SAAK,cACD,gBAAgB,IACV,WAAW,MAAM,IAAI,CAAC,aAAa,eACnC,WAAW,SAAS,CAAC,GAC3B,QAAQ;AAEZ,QAAI,gBAAgB,GAAG;AAEnB,WAAK,cAAc,WAAW,SAAS,CAAC,GAAG,QAAQ;;AAGvD,WAAO;EACX;;;;;;;;EASA,MAAG;;AACC,YAAQ,KAAK,OAAO;MAChB,KAAK,mBAAmB,aAAa;AAEjC,eAAO,KAAK,WAAW,MAClB,KAAK,eAAe,aAAa,aAC9B,KAAK,WAAW,KAAK,aACvB,KAAK,6BAA4B,IACjC;;MAGV,KAAK,mBAAmB,gBAAgB;AACpC,eAAO,KAAK,kBAAkB,GAAG,CAAC;;MAEtC,KAAK,mBAAmB,YAAY;AAChC,eAAO,KAAK,kBAAkB,GAAG,CAAC;;MAEtC,KAAK,mBAAmB,cAAc;AAClC,SAAAA,MAAA,KAAK,YAAM,QAAAA,QAAA,SAAA,SAAAA,IAAE,2CACT,KAAK,QAAQ;AAEjB,eAAO;;MAEX,KAAK,mBAAmB,aAAa;AAEjC,eAAO;;;EAGnB;;AASJ,SAAS,WAAW,YAAuB;AACvC,MAAI,MAAM;AACV,QAAM,UAAU,IAAI,cAChB,YACA,CAAC,QAAS,OAAO,cAAc,GAAG,CAAE;AAGxC,SAAO,SAAS,eACZ,KACA,YAAwB;AAExB,QAAI,YAAY;AAChB,QAAI,SAAS;AAEb,YAAQ,SAAS,IAAI,QAAQ,KAAK,MAAM,MAAM,GAAG;AAC7C,aAAO,IAAI,MAAM,WAAW,MAAM;AAElC,cAAQ,YAAY,UAAU;AAE9B,YAAM,MAAM,QAAQ;QAChB;;QAEA,SAAS;MAAC;AAGd,UAAI,MAAM,GAAG;AACT,oBAAY,SAAS,QAAQ,IAAG;AAChC;;AAGJ,kBAAY,SAAS;AAErB,eAAS,QAAQ,IAAI,YAAY,IAAI;;AAGzC,UAAM,SAAS,MAAM,IAAI,MAAM,SAAS;AAGxC,UAAM;AAEN,WAAO;EACX;AACJ;AAYM,SAAU,gBACZ,YACA,SACA,SACA,MAAY;AAEZ,QAAM,eAAe,UAAU,aAAa,kBAAkB;AAC9D,QAAM,aAAa,UAAU,aAAa;AAG1C,MAAI,gBAAgB,GAAG;AACnB,WAAO,eAAe,KAAK,SAAS,aAAa,UAAU;;AAI/D,MAAI,YAAY;AACZ,UAAM,QAAQ,OAAO;AAErB,WAAO,QAAQ,KAAK,SAAS,cACvB,KACA,WAAW,UAAU,KAAK,IAAI;;AAMxC,MAAI,KAAK;AACT,MAAI,KAAK,KAAK,cAAc;AAE5B,SAAO,MAAM,IAAI;AACb,UAAM,MAAO,KAAK,OAAQ;AAC1B,UAAM,SAAS,WAAW,GAAG;AAE7B,QAAI,SAAS,MAAM;AACf,WAAK,MAAM;eACJ,SAAS,MAAM;AACtB,WAAK,MAAM;WACR;AACH,aAAO,WAAW,MAAM,WAAW;;;AAI3C,SAAO;AACX;AAEA,IAAM,cAAc,WAAW,wBAAc;AAC7C,IAAM,aAAa,WAAW,uBAAa;AASrC,SAAU,WAAW,KAAa,OAAO,aAAa,QAAM;AAC9D,SAAO,YAAY,KAAK,IAAI;AAChC;;;ACjkBA,SAAS,YACL,KAAM;AAEN,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,QAAI,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI;;AAEjC,SAAO;AACX;AAGA,IAAA,sBAAe,IAAI,IAA0C,YAAY,CAAC,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,IAAG,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,IAAG,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,EAAC,GAAE,QAAO,GAAE,MAAK,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,YAAW,GAAE,MAAK,GAAE,QAAO,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,QAAO,GAAE,MAAK,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,IAAG,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,EAAC,GAAE,KAAI,GAAE,UAAS,CAAC,GAAE,CAAC,IAAG,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,IAAG,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,IAAG,QAAQ,GAAE,CAAC,IAAG,SAAS,GAAE,CAAC,IAAG,UAAU,GAAE,CAAC,IAAG,SAAS,GAAE,CAAC,KAAI,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,IAAG,SAAS,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,IAAG,aAAa,GAAE,CAAC,KAAI,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,IAAG,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,IAAG,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,MAAK,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,uBAAuB,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,mBAAmB,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,yBAAyB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,IAAG,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,EAAC,GAAE,iBAAgB,GAAE,MAAK,GAAE,eAAc,CAAC,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,IAAG,QAAQ,GAAE,CAAC,IAAG,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,IAAG,aAAa,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,cAAc,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,IAAG,wBAAwB,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,IAAG,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,IAAG,QAAQ,GAAE,CAAC,GAAE,gBAAgB,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,mBAAmB,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,KAAI,GAAE,WAAU,CAAC,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,gBAAgB,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,gBAAgB,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,mBAAmB,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,gBAAgB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,mBAAmB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,uBAAuB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,uBAAuB,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,cAAc,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,qBAAqB,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,mBAAmB,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,wBAAwB,GAAE,CAAC,GAAE,qBAAqB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,IAAG,oBAAoB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,EAAC,GAAE,UAAS,GAAE,KAAI,GAAE,UAAS,CAAC,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,cAAc,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,MAAK,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,qBAAqB,GAAE,CAAC,GAAE,wBAAwB,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,OAAM,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,OAAM,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,4BAA4B,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,MAAK,GAAE,UAAS,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,aAAY,GAAE,KAAI,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,QAAO,GAAE,KAAI,GAAE,QAAO,CAAC,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,KAAI,GAAE,UAAS,CAAC,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,EAAC,GAAE,UAAS,GAAE,KAAI,GAAE,UAAS,CAAC,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,EAAC,GAAE,aAAY,GAAE,MAAK,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,UAAS,GAAE,KAAI,GAAE,UAAS,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,KAAI,GAAE,WAAU,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,KAAI,GAAE,UAAS,CAAC,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,EAAC,GAAE,eAAc,GAAE,MAAK,GAAE,YAAW,CAAC,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,EAAC,GAAE,QAAO,GAAE,MAAK,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,QAAO,GAAE,MAAK,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,QAAO,GAAE,KAAI,GAAE,QAAO,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,QAAO,GAAE,KAAI,GAAE,QAAO,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,OAAM,GAAE,cAAa,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,OAAM,GAAE,cAAa,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,QAAO,GAAE,IAAI,IAAkC,YAAY,CAAC,CAAC,KAAI,QAAQ,GAAE,CAAC,MAAK,OAAO,CAAC,CAAC,CAAC,EAAC,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,QAAO,GAAE,IAAI,IAAkC,YAAY,CAAC,CAAC,KAAI,QAAQ,GAAE,CAAC,MAAK,OAAO,CAAC,CAAC,CAAC,EAAC,CAAC,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,gBAAgB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,KAAI,GAAE,qBAAoB,CAAC,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,MAAK,GAAE,cAAa,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,MAAK,GAAE,gBAAe,CAAC,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,OAAM,GAAE,iBAAgB,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,OAAM,GAAE,iBAAgB,CAAC,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,KAAI,GAAE,oBAAmB,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,KAAI,GAAE,sBAAqB,CAAC,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,OAAM,GAAE,WAAU,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,OAAM,GAAE,WAAU,CAAC,GAAE,CAAC,GAAE,cAAc,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,cAAc,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,gBAAgB,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,EAAC,GAAE,uBAAsB,GAAE,MAAK,GAAE,YAAW,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,wBAAuB,GAAE,MAAK,GAAE,YAAW,CAAC,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,mBAAmB,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,cAAc,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,EAAC,GAAE,QAAO,GAAE,KAAI,GAAE,QAAO,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,QAAO,GAAE,KAAI,GAAE,QAAO,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,OAAM,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,OAAM,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,yBAAyB,GAAE,CAAC,GAAE,yBAAyB,GAAE,CAAC,GAAE,wBAAwB,GAAE,CAAC,GAAE,0BAA0B,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,yBAAyB,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,EAAC,GAAE,aAAY,GAAE,KAAI,GAAE,aAAY,CAAC,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,KAAI,GAAE,WAAU,CAAC,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,IAAG,WAAW,GAAE,CAAC,IAAG,cAAc,GAAE,CAAC,GAAE,cAAc,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,IAAG,mBAAmB,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,cAAc,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,IAAG,SAAS,GAAE,CAAC,KAAI,YAAY,GAAE,CAAC,IAAG,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,IAAG,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,IAAG,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,IAAG,UAAU,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,wBAAwB,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,sBAAsB,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,mBAAmB,GAAE,CAAC,GAAE,qBAAqB,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,qBAAqB,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,IAAG,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,qBAAqB,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,IAAG,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,IAAG,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,KAAI,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,IAAG,QAAQ,GAAE,CAAC,IAAG,qBAAqB,GAAE,CAAC,IAAG,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,IAAG,YAAY,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,IAAG,qBAAqB,GAAE,CAAC,GAAE,sBAAsB,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,sBAAsB,GAAE,CAAC,GAAE,uBAAuB,GAAE,CAAC,GAAE,wBAAwB,GAAE,CAAC,GAAE,4BAA4B,GAAE,CAAC,GAAE,cAAc,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,KAAI,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,cAAc,GAAE,CAAC,GAAE,gBAAgB,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,KAAI,GAAE,WAAU,CAAC,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,mBAAmB,GAAE,CAAC,GAAE,qBAAqB,GAAE,CAAC,GAAE,uBAAuB,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,sBAAsB,GAAE,CAAC,GAAE,qBAAqB,GAAE,CAAC,GAAE,sBAAsB,GAAE,CAAC,GAAE,mBAAmB,GAAE,CAAC,GAAE,qBAAqB,GAAE,CAAC,GAAE,iBAAiB,GAAE,CAAC,GAAE,kBAAkB,GAAE,CAAC,GAAE,oBAAoB,GAAE,CAAC,GAAE,sBAAsB,GAAE,CAAC,GAAE,qBAAqB,GAAE,CAAC,GAAE,sBAAsB,GAAE,CAAC,GAAE,mBAAmB,GAAE,CAAC,GAAE,qBAAqB,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,gBAAgB,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,EAAC,GAAE,qBAAoB,GAAE,KAAI,GAAE,uBAAsB,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,sBAAqB,GAAE,KAAI,GAAE,wBAAuB,CAAC,GAAE,CAAC,IAAG,UAAU,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,gBAAgB,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,aAAa,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,IAAG,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,EAAC,GAAE,aAAY,GAAE,KAAI,GAAE,aAAY,CAAC,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,KAAI,GAAE,SAAQ,CAAC,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,EAAC,GAAE,cAAa,GAAE,KAAI,GAAE,cAAa,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,cAAa,GAAE,KAAI,GAAE,cAAa,CAAC,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,EAAC,GAAE,cAAa,GAAE,KAAI,GAAE,sBAAqB,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,oBAAmB,GAAE,KAAI,GAAE,4BAA2B,CAAC,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,EAAC,GAAE,UAAS,GAAE,OAAM,GAAE,UAAS,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,UAAS,GAAE,OAAM,GAAE,UAAS,CAAC,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,EAAC,GAAE,mBAAkB,GAAE,KAAI,GAAE,qBAAoB,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,SAAQ,GAAE,KAAI,GAAE,qBAAoB,CAAC,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,YAAY,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,eAAe,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,MAAM,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,EAAC,GAAE,UAAS,GAAE,KAAI,GAAE,UAAS,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,UAAS,GAAE,KAAI,GAAE,UAAS,CAAC,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,OAAM,GAAE,kBAAiB,CAAC,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,OAAM,GAAE,kBAAiB,CAAC,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,WAAW,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,EAAC,GAAE,WAAU,GAAE,MAAK,GAAE,WAAU,CAAC,GAAE,CAAC,OAAM,EAAC,GAAE,IAAI,IAAkC,YAAY,CAAC,CAAC,OAAM,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,IAAG,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,OAAO,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,GAAE,CAAC,GAAE,QAAQ,CAAC,CAAC,CAAC,EAAC,CAAC,GAAE,CAAC,MAAK,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,SAAS,GAAE,CAAC,GAAE,UAAU,GAAE,CAAC,GAAE,UAAU,CAAC,CAAC,CAAC;;;ACdl+tB,IAAM,aAAa,oBAAI,IAAI;EACvB,CAAC,IAAI,QAAQ;EACb,CAAC,IAAI,OAAO;EACZ,CAAC,IAAI,QAAQ;EACb,CAAC,IAAI,MAAM;EACX,CAAC,IAAI,MAAM;CACd;AAGM,IAAM;;EAET,OAAO,UAAU,eAAe,OAC1B,CAAC,KAAa,UAA0B,IAAI,YAAY,KAAK;;IAE7D,CAAC,GAAW,WACP,EAAE,WAAW,KAAK,IAAI,WAAY,SAC5B,EAAE,WAAW,KAAK,IAAI,SAAU,OACjC,EAAE,WAAW,QAAQ,CAAC,IACtB,QACA,QACA,EAAE,WAAW,KAAK;;;AA0DtC,SAAS,WACL,OACAC,MAAwB;AAExB,SAAO,SAASC,QAAO,MAAY;AAC/B,QAAIC;AACJ,QAAI,UAAU;AACd,QAAI,SAAS;AAEb,WAAQA,SAAQ,MAAM,KAAK,IAAI,GAAI;AAC/B,UAAI,YAAYA,OAAM,OAAO;AACzB,kBAAU,KAAK,UAAU,SAASA,OAAM,KAAK;;AAIjD,gBAAUF,KAAI,IAAIE,OAAM,CAAC,EAAE,WAAW,CAAC,CAAC;AAGxC,gBAAUA,OAAM,QAAQ;;AAG5B,WAAO,SAAS,KAAK,UAAU,OAAO;EAC1C;AACJ;AASO,IAAM,aAAa,WAAW,YAAY,UAAU;AAQpD,IAAM,kBAAkB,WAC3B,eACA,oBAAI,IAAI;EACJ,CAAC,IAAI,QAAQ;EACb,CAAC,IAAI,OAAO;EACZ,CAAC,KAAK,QAAQ;CACjB,CAAC;AASC,IAAM,aAAa,WACtB,gBACA,oBAAI,IAAI;EACJ,CAAC,IAAI,OAAO;EACZ,CAAC,IAAI,MAAM;EACX,CAAC,IAAI,MAAM;EACX,CAAC,KAAK,QAAQ;CACjB,CAAC;;;ACpIN,IAAY;CAAZ,SAAYC,cAAW;AAEnB,EAAAA,aAAAA,aAAA,KAAA,IAAA,CAAA,IAAA;AAEA,EAAAA,aAAAA,aAAA,MAAA,IAAA,CAAA,IAAA;AACJ,GALY,gBAAA,cAAW,CAAA,EAAA;AAOvB,IAAY;CAAZ,SAAYC,eAAY;AAKpB,EAAAA,cAAAA,cAAA,MAAA,IAAA,CAAA,IAAA;AAMA,EAAAA,cAAAA,cAAA,OAAA,IAAA,CAAA,IAAA;AAKA,EAAAA,cAAAA,cAAA,WAAA,IAAA,CAAA,IAAA;AAKA,EAAAA,cAAAA,cAAA,WAAA,IAAA,CAAA,IAAA;AAKA,EAAAA,cAAAA,cAAA,MAAA,IAAA,CAAA,IAAA;AACJ,GA3BY,iBAAA,eAAY,CAAA,EAAA;;;AnBVxB,SAAS,OAAQ,KAAK;AAAE,SAAO,OAAO,UAAU,SAAS,KAAK,GAAG;AAAE;AAEnE,SAAS,SAAU,KAAK;AAAE,SAAO,OAAO,GAAG,MAAM;AAAkB;AAEnE,IAAM,kBAAkB,OAAO,UAAU;AAEzC,SAAS,IAAK,QAAQ,KAAK;AACzB,SAAO,gBAAgB,KAAK,QAAQ,GAAG;AACzC;AAIA,SAAS,OAAQ,KAAoC;AACnD,QAAM,UAAU,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AAEvD,UAAQ,QAAQ,SAAU,QAAQ;AAChC,QAAI,CAAC,QAAQ;AAAE;AAAA,IAAO;AAEtB,QAAI,OAAO,WAAW,UAAU;AAC9B,YAAM,IAAI,UAAU,SAAS,gBAAgB;AAAA,IAC/C;AAEA,WAAO,KAAK,MAAM,EAAE,QAAQ,SAAU,KAAK;AACzC,UAAI,GAAG,IAAI,OAAO,GAAG;AAAA,IACvB,CAAC;AAAA,EACH,CAAC;AAED,SAAO;AACT;AAIA,SAAS,eAAgB,KAAK,KAAK,aAAa;AAC9C,SAAO,CAAC,EAAE,OAAO,IAAI,MAAM,GAAG,GAAG,GAAG,aAAa,IAAI,MAAM,MAAM,CAAC,CAAC;AACrE;AAEA,SAAS,kBAAmB,GAAG;AAG7B,MAAI,KAAK,SAAU,KAAK,OAAQ;AAAE,WAAO;AAAA,EAAM;AAE/C,MAAI,KAAK,SAAU,KAAK,OAAQ;AAAE,WAAO;AAAA,EAAM;AAC/C,OAAK,IAAI,WAAY,UAAW,IAAI,WAAY,OAAQ;AAAE,WAAO;AAAA,EAAM;AAEvE,MAAI,KAAK,KAAQ,KAAK,GAAM;AAAE,WAAO;AAAA,EAAM;AAC3C,MAAI,MAAM,IAAM;AAAE,WAAO;AAAA,EAAM;AAC/B,MAAI,KAAK,MAAQ,KAAK,IAAM;AAAE,WAAO;AAAA,EAAM;AAC3C,MAAI,KAAK,OAAQ,KAAK,KAAM;AAAE,WAAO;AAAA,EAAM;AAE3C,MAAI,IAAI,SAAU;AAAE,WAAO;AAAA,EAAM;AACjC,SAAO;AACT;AAEA,SAASC,eAAe,GAAG;AAEzB,MAAI,IAAI,OAAQ;AACd,SAAK;AACL,UAAM,aAAa,SAAU,KAAK;AAClC,UAAM,aAAa,SAAU,IAAI;AAEjC,WAAO,OAAO,aAAa,YAAY,UAAU;AAAA,EACnD;AACA,SAAO,OAAO,aAAa,CAAC;AAC9B;AAEA,IAAM,iBAAkB;AACxB,IAAM,YAAkB;AACxB,IAAM,kBAAkB,IAAI,OAAO,eAAe,SAAS,MAAM,UAAU,QAAQ,IAAI;AAEvF,IAAM,yBAAyB;AAE/B,SAAS,qBAAsBC,QAAO,MAAM;AAC1C,MAAI,KAAK,WAAW,CAAC,MAAM,MAAe,uBAAuB,KAAK,IAAI,GAAG;AAC3E,UAAMC,QAAO,KAAK,CAAC,EAAE,YAAY,MAAM,MACnC,SAAS,KAAK,MAAM,CAAC,GAAG,EAAE,IAC1B,SAAS,KAAK,MAAM,CAAC,GAAG,EAAE;AAE9B,QAAI,kBAAkBA,KAAI,GAAG;AAC3B,aAAOF,eAAcE,KAAI;AAAA,IAC3B;AAEA,WAAOD;AAAA,EACT;AAEA,QAAM,UAAU,WAAWA,MAAK;AAChC,MAAI,YAAYA,QAAO;AACrB,WAAO;AAAA,EACT;AAEA,SAAOA;AACT;AAQA,SAAS,WAAY,KAAK;AACxB,MAAI,IAAI,QAAQ,IAAI,IAAI,GAAG;AAAE,WAAO;AAAA,EAAI;AACxC,SAAO,IAAI,QAAQ,gBAAgB,IAAI;AACzC;AAEA,SAAS,YAAa,KAAK;AACzB,MAAI,IAAI,QAAQ,IAAI,IAAI,KAAK,IAAI,QAAQ,GAAG,IAAI,GAAG;AAAE,WAAO;AAAA,EAAI;AAEhE,SAAO,IAAI,QAAQ,iBAAiB,SAAUA,QAAO,SAASE,SAAQ;AACpE,QAAI,SAAS;AAAE,aAAO;AAAA,IAAQ;AAC9B,WAAO,qBAAqBF,QAAOE,OAAM;AAAA,EAC3C,CAAC;AACH;AAEA,IAAM,sBAAsB;AAC5B,IAAM,yBAAyB;AAC/B,IAAM,oBAAoB;AAAA,EACxB,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP;AAEA,SAAS,kBAAmB,IAAI;AAC9B,SAAO,kBAAkB,EAAE;AAC7B;AAEA,SAAS,WAAY,KAAK;AACxB,MAAI,oBAAoB,KAAK,GAAG,GAAG;AACjC,WAAO,IAAI,QAAQ,wBAAwB,iBAAiB;AAAA,EAC9D;AACA,SAAO;AACT;AAEA,IAAM,mBAAmB;AAEzB,SAAS,SAAU,KAAK;AACtB,SAAO,IAAI,QAAQ,kBAAkB,MAAM;AAC7C;AAEA,SAAS,QAASD,OAAM;AACtB,UAAQA,OAAM;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,EACX;AACA,SAAO;AACT;AAGA,SAAS,aAAcA,OAAM;AAC3B,MAAIA,SAAQ,QAAUA,SAAQ,MAAQ;AAAE,WAAO;AAAA,EAAK;AACpD,UAAQA,OAAM;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,EACX;AACA,SAAO;AACT;AAKA,SAAS,YAAa,IAAI;AACxB,SAAeE,eAAE,KAAK,EAAE,KAAaA,eAAE,KAAK,EAAE;AAChD;AASA,SAAS,eAAgB,IAAI;AAC3B,UAAQ,IAAI;AAAA,IACV,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AAIA,SAAS,mBAAoB,KAAK;AAGhC,QAAM,IAAI,KAAK,EAAE,QAAQ,QAAQ,GAAG;AAQpC,MAAI,IAAI,YAAY,MAAM,KAAK;AAC7B,UAAM,IAAI,QAAQ,MAAM,GAAG;AAAA,EAC7B;AAkCA,SAAO,IAAI,YAAY,EAAE,YAAY;AACvC;AAMA,IAAM,MAAM,EAAE,sBAAO,oBAAQ;;;AoB5R7B;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACMe,SAAR,eAAiC,OAAO,OAAO,eAAe;AACnE,MAAI,OAAO,OAAO,QAAQ;AAE1B,QAAM,MAAM,MAAM;AAClB,QAAM,SAAS,MAAM;AAErB,QAAM,MAAM,QAAQ;AACpB,UAAQ;AAER,SAAO,MAAM,MAAM,KAAK;AACtB,aAAS,MAAM,IAAI,WAAW,MAAM,GAAG;AACvC,QAAI,WAAW,IAAc;AAC3B;AACA,UAAI,UAAU,GAAG;AACf,gBAAQ;AACR;AAAA,MACF;AAAA,IACF;AAEA,cAAU,MAAM;AAChB,UAAM,GAAG,OAAO,UAAU,KAAK;AAC/B,QAAI,WAAW,IAAc;AAC3B,UAAI,YAAY,MAAM,MAAM,GAAG;AAE7B;AAAA,MACF,WAAW,eAAe;AACxB,cAAM,MAAM;AACZ,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAEA,MAAI,WAAW;AAEf,MAAI,OAAO;AACT,eAAW,MAAM;AAAA,EACnB;AAGA,QAAM,MAAM;AAEZ,SAAO;AACT;;;AC3Ce,SAAR,qBAAuC,KAAK,OAAO,KAAK;AAC7D,MAAIC;AACJ,MAAI,MAAM;AAEV,QAAM,SAAS;AAAA,IACb,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,EACP;AAEA,MAAI,IAAI,WAAW,GAAG,MAAM,IAAc;AACxC;AACA,WAAO,MAAM,KAAK;AAChB,MAAAA,QAAO,IAAI,WAAW,GAAG;AACzB,UAAIA,UAAS,IAAe;AAAE,eAAO;AAAA,MAAO;AAC5C,UAAIA,UAAS,IAAc;AAAE,eAAO;AAAA,MAAO;AAC3C,UAAIA,UAAS,IAAc;AACzB,eAAO,MAAM,MAAM;AACnB,eAAO,MAAM,YAAY,IAAI,MAAM,QAAQ,GAAG,GAAG,CAAC;AAClD,eAAO,KAAK;AACZ,eAAO;AAAA,MACT;AACA,UAAIA,UAAS,MAAgB,MAAM,IAAI,KAAK;AAC1C,eAAO;AACP;AAAA,MACF;AAEA;AAAA,IACF;AAGA,WAAO;AAAA,EACT;AAIA,MAAI,QAAQ;AACZ,SAAO,MAAM,KAAK;AAChB,IAAAA,QAAO,IAAI,WAAW,GAAG;AAEzB,QAAIA,UAAS,IAAM;AAAE;AAAA,IAAM;AAG3B,QAAIA,QAAO,MAAQA,UAAS,KAAM;AAAE;AAAA,IAAM;AAE1C,QAAIA,UAAS,MAAgB,MAAM,IAAI,KAAK;AAC1C,UAAI,IAAI,WAAW,MAAM,CAAC,MAAM,IAAM;AAAE;AAAA,MAAM;AAC9C,aAAO;AACP;AAAA,IACF;AAEA,QAAIA,UAAS,IAAc;AACzB;AACA,UAAI,QAAQ,IAAI;AAAE,eAAO;AAAA,MAAO;AAAA,IAClC;AAEA,QAAIA,UAAS,IAAc;AACzB,UAAI,UAAU,GAAG;AAAE;AAAA,MAAM;AACzB;AAAA,IACF;AAEA;AAAA,EACF;AAEA,MAAI,UAAU,KAAK;AAAE,WAAO;AAAA,EAAO;AACnC,MAAI,UAAU,GAAG;AAAE,WAAO;AAAA,EAAO;AAEjC,SAAO,MAAM,YAAY,IAAI,MAAM,OAAO,GAAG,CAAC;AAC9C,SAAO,MAAM;AACb,SAAO,KAAK;AACZ,SAAO;AACT;;;ACpEe,SAAR,eAAiC,KAAK,OAAO,KAAK,YAAY;AACnE,MAAIC;AACJ,MAAI,MAAM;AAEV,QAAM,QAAQ;AAAA;AAAA,IAEZ,IAAI;AAAA;AAAA,IAEJ,cAAc;AAAA;AAAA,IAEd,KAAK;AAAA;AAAA,IAEL,KAAK;AAAA;AAAA,IAEL,QAAQ;AAAA,EACV;AAEA,MAAI,YAAY;AAGd,UAAM,MAAM,WAAW;AACvB,UAAM,SAAS,WAAW;AAAA,EAC5B,OAAO;AACL,QAAI,OAAO,KAAK;AAAE,aAAO;AAAA,IAAM;AAE/B,QAAI,SAAS,IAAI,WAAW,GAAG;AAC/B,QAAI,WAAW,MAAgB,WAAW,MAAgB,WAAW,IAAc;AAAE,aAAO;AAAA,IAAM;AAElG;AACA;AAGA,QAAI,WAAW,IAAM;AAAE,eAAS;AAAA,IAAK;AAErC,UAAM,SAAS;AAAA,EACjB;AAEA,SAAO,MAAM,KAAK;AAChB,IAAAA,QAAO,IAAI,WAAW,GAAG;AACzB,QAAIA,UAAS,MAAM,QAAQ;AACzB,YAAM,MAAM,MAAM;AAClB,YAAM,OAAO,YAAY,IAAI,MAAM,OAAO,GAAG,CAAC;AAC9C,YAAM,KAAK;AACX,aAAO;AAAA,IACT,WAAWA,UAAS,MAAgB,MAAM,WAAW,IAAc;AACjE,aAAO;AAAA,IACT,WAAWA,UAAS,MAAgB,MAAM,IAAI,KAAK;AACjD;AAAA,IACF;AAEA;AAAA,EACF;AAGA,QAAM,eAAe;AACrB,QAAM,OAAO,YAAY,IAAI,MAAM,OAAO,GAAG,CAAC;AAC9C,SAAO;AACT;;;ACvDA,IAAM,gBAAgB,CAAC;AAEvB,cAAc,cAAc,SAAU,QAAQ,KAAK,SAAS,KAAK,KAAK;AACpE,QAAM,QAAQ,OAAO,GAAG;AAExB,SAAQ,UAAU,IAAI,YAAY,KAAK,IAAI,MACnC,WAAW,MAAM,OAAO,IACxB;AACV;AAEA,cAAc,aAAa,SAAU,QAAQ,KAAK,SAAS,KAAK,KAAK;AACnE,QAAM,QAAQ,OAAO,GAAG;AAExB,SAAQ,SAAS,IAAI,YAAY,KAAK,IAAI,YAClC,WAAW,OAAO,GAAG,EAAE,OAAO,IAC9B;AACV;AAEA,cAAc,QAAQ,SAAU,QAAQ,KAAK,SAAS,KAAK,KAAK;AAC9D,QAAM,QAAQ,OAAO,GAAG;AACxB,QAAM,OAAO,MAAM,OAAO,YAAY,MAAM,IAAI,EAAE,KAAK,IAAI;AAC3D,MAAI,WAAW;AACf,MAAI,YAAY;AAEhB,MAAI,MAAM;AACR,UAAM,MAAM,KAAK,MAAM,QAAQ;AAC/B,eAAW,IAAI,CAAC;AAChB,gBAAY,IAAI,MAAM,CAAC,EAAE,KAAK,EAAE;AAAA,EAClC;AAEA,MAAI;AACJ,MAAI,QAAQ,WAAW;AACrB,kBAAc,QAAQ,UAAU,MAAM,SAAS,UAAU,SAAS,KAAK,WAAW,MAAM,OAAO;AAAA,EACjG,OAAO;AACL,kBAAc,WAAW,MAAM,OAAO;AAAA,EACxC;AAEA,MAAI,YAAY,QAAQ,MAAM,MAAM,GAAG;AACrC,WAAO,cAAc;AAAA,EACvB;AAKA,MAAI,MAAM;AACR,UAAM,IAAI,MAAM,UAAU,OAAO;AACjC,UAAM,WAAW,MAAM,QAAQ,MAAM,MAAM,MAAM,IAAI,CAAC;AAEtD,QAAI,IAAI,GAAG;AACT,eAAS,KAAK,CAAC,SAAS,QAAQ,aAAa,QAAQ,CAAC;AAAA,IACxD,OAAO;AACL,eAAS,CAAC,IAAI,SAAS,CAAC,EAAE,MAAM;AAChC,eAAS,CAAC,EAAE,CAAC,KAAK,MAAM,QAAQ,aAAa;AAAA,IAC/C;AAGA,UAAM,WAAW;AAAA,MACf,OAAO;AAAA,IACT;AAEA,WAAO,aAAa,IAAI,YAAY,QAAQ,CAAC,IAAI,WAAW;AAAA;AAAA,EAC9D;AAEA,SAAO,aAAa,IAAI,YAAY,KAAK,CAAC,IAAI,WAAW;AAAA;AAC3D;AAEA,cAAc,QAAQ,SAAU,QAAQ,KAAK,SAAS,KAAK,KAAK;AAC9D,QAAM,QAAQ,OAAO,GAAG;AAOxB,QAAM,MAAM,MAAM,UAAU,KAAK,CAAC,EAAE,CAAC,IACnC,IAAI,mBAAmB,MAAM,UAAU,SAAS,GAAG;AAErD,SAAO,IAAI,YAAY,QAAQ,KAAK,OAAO;AAC7C;AAEA,cAAc,YAAY,SAAU,QAAQ,KAAK,SAAoB;AACnE,SAAO,QAAQ,WAAW,aAAa;AACzC;AACA,cAAc,YAAY,SAAU,QAAQ,KAAK,SAAoB;AACnE,SAAO,QAAQ,SAAU,QAAQ,WAAW,aAAa,WAAY;AACvE;AAEA,cAAc,OAAO,SAAU,QAAQ,KAAyB;AAC9D,SAAO,WAAW,OAAO,GAAG,EAAE,OAAO;AACvC;AAEA,cAAc,aAAa,SAAU,QAAQ,KAAyB;AACpE,SAAO,OAAO,GAAG,EAAE;AACrB;AACA,cAAc,cAAc,SAAU,QAAQ,KAAyB;AACrE,SAAO,OAAO,GAAG,EAAE;AACrB;AAOA,SAAS,WAAY;AA6BnB,OAAK,QAAQ,OAAO,CAAC,GAAG,aAAa;AACvC;AAOA,SAAS,UAAU,cAAc,SAAS,YAAa,OAAO;AAC5D,MAAI,GAAG,GAAG;AAEV,MAAI,CAAC,MAAM,OAAO;AAAE,WAAO;AAAA,EAAG;AAE9B,WAAS;AAET,OAAK,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC9C,cAAU,MAAM,WAAW,MAAM,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,OAAO,WAAW,MAAM,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI;AAAA,EACzF;AAEA,SAAO;AACT;AAWA,SAAS,UAAU,cAAc,SAAS,YAAa,QAAQ,KAAK,SAAS;AAC3E,QAAM,QAAQ,OAAO,GAAG;AACxB,MAAI,SAAS;AAGb,MAAI,MAAM,QAAQ;AAChB,WAAO;AAAA,EACT;AASA,MAAI,MAAM,SAAS,MAAM,YAAY,MAAM,OAAO,OAAO,MAAM,CAAC,EAAE,QAAQ;AACxE,cAAU;AAAA,EACZ;AAGA,aAAW,MAAM,YAAY,KAAK,OAAO,OAAO,MAAM;AAGtD,YAAU,KAAK,YAAY,KAAK;AAGhC,MAAI,MAAM,YAAY,KAAK,QAAQ,UAAU;AAC3C,cAAU;AAAA,EACZ;AAGA,MAAI,SAAS;AACb,MAAI,MAAM,OAAO;AACf,aAAS;AAET,QAAI,MAAM,YAAY,GAAG;AACvB,UAAI,MAAM,IAAI,OAAO,QAAQ;AAC3B,cAAM,YAAY,OAAO,MAAM,CAAC;AAEhC,YAAI,UAAU,SAAS,YAAY,UAAU,QAAQ;AAGnD,mBAAS;AAAA,QACX,WAAW,UAAU,YAAY,MAAM,UAAU,QAAQ,MAAM,KAAK;AAGlE,mBAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,YAAU,SAAS,QAAQ;AAE3B,SAAO;AACT;AAUA,SAAS,UAAU,eAAe,SAAU,QAAQ,SAAS,KAAK;AAChE,MAAI,SAAS;AACb,QAAM,QAAQ,KAAK;AAEnB,WAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AACjD,UAAM,OAAO,OAAO,CAAC,EAAE;AAEvB,QAAI,OAAO,MAAM,IAAI,MAAM,aAAa;AACtC,gBAAU,MAAM,IAAI,EAAE,QAAQ,GAAG,SAAS,KAAK,IAAI;AAAA,IACrD,OAAO;AACL,gBAAU,KAAK,YAAY,QAAQ,GAAG,OAAO;AAAA,IAC/C;AAAA,EACF;AAEA,SAAO;AACT;AAYA,SAAS,UAAU,qBAAqB,SAAU,QAAQ,SAAS,KAAK;AACtE,MAAI,SAAS;AAEb,WAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AACjD,YAAQ,OAAO,CAAC,EAAE,MAAM;AAAA,MACtB,KAAK;AACH,kBAAU,OAAO,CAAC,EAAE;AACpB;AAAA,MACF,KAAK;AACH,kBAAU,KAAK,mBAAmB,OAAO,CAAC,EAAE,UAAU,SAAS,GAAG;AAClE;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,kBAAU,OAAO,CAAC,EAAE;AACpB;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,kBAAU;AACV;AAAA,MACF;AAAA,IAEF;AAAA,EACF;AAEA,SAAO;AACT;AAWA,SAAS,UAAU,SAAS,SAAU,QAAQ,SAAS,KAAK;AAC1D,MAAI,SAAS;AACb,QAAM,QAAQ,KAAK;AAEnB,WAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AACjD,UAAM,OAAO,OAAO,CAAC,EAAE;AAEvB,QAAI,SAAS,UAAU;AACrB,gBAAU,KAAK,aAAa,OAAO,CAAC,EAAE,UAAU,SAAS,GAAG;AAAA,IAC9D,WAAW,OAAO,MAAM,IAAI,MAAM,aAAa;AAC7C,gBAAU,MAAM,IAAI,EAAE,QAAQ,GAAG,SAAS,KAAK,IAAI;AAAA,IACrD,OAAO;AACL,gBAAU,KAAK,YAAY,QAAQ,GAAG,SAAS,GAAG;AAAA,IACpD;AAAA,EACF;AAEA,SAAO;AACT;AAEA,IAAO,mBAAQ;;;AC5Sf,SAAS,QAAS;AAUhB,OAAK,YAAY,CAAC;AAOlB,OAAK,YAAY;AACnB;AAMA,MAAM,UAAU,WAAW,SAAU,MAAM;AACzC,WAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC9C,QAAI,KAAK,UAAU,CAAC,EAAE,SAAS,MAAM;AACnC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAIA,MAAM,UAAU,cAAc,WAAY;AACxC,QAAM,OAAO;AACb,QAAM,SAAS,CAAC,EAAE;AAGlB,OAAK,UAAU,QAAQ,SAAU,MAAM;AACrC,QAAI,CAAC,KAAK,SAAS;AAAE;AAAA,IAAO;AAE5B,SAAK,IAAI,QAAQ,SAAU,SAAS;AAClC,UAAI,OAAO,QAAQ,OAAO,IAAI,GAAG;AAC/B,eAAO,KAAK,OAAO;AAAA,MACrB;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AAED,OAAK,YAAY,CAAC;AAElB,SAAO,QAAQ,SAAU,OAAO;AAC9B,SAAK,UAAU,KAAK,IAAI,CAAC;AACzB,SAAK,UAAU,QAAQ,SAAU,MAAM;AACrC,UAAI,CAAC,KAAK,SAAS;AAAE;AAAA,MAAO;AAE5B,UAAI,SAAS,KAAK,IAAI,QAAQ,KAAK,IAAI,GAAG;AAAE;AAAA,MAAO;AAEnD,WAAK,UAAU,KAAK,EAAE,KAAK,KAAK,EAAE;AAAA,IACpC,CAAC;AAAA,EACH,CAAC;AACH;AA2BA,MAAM,UAAU,KAAK,SAAU,MAAM,IAAI,SAAS;AAChD,QAAM,QAAQ,KAAK,SAAS,IAAI;AAChC,QAAM,MAAM,WAAW,CAAC;AAExB,MAAI,UAAU,IAAI;AAAE,UAAM,IAAI,MAAM,4BAA4B,IAAI;AAAA,EAAE;AAEtE,OAAK,UAAU,KAAK,EAAE,KAAK;AAC3B,OAAK,UAAU,KAAK,EAAE,MAAM,IAAI,OAAO,CAAC;AACxC,OAAK,YAAY;AACnB;AA0BA,MAAM,UAAU,SAAS,SAAU,YAAY,UAAU,IAAI,SAAS;AACpE,QAAM,QAAQ,KAAK,SAAS,UAAU;AACtC,QAAM,MAAM,WAAW,CAAC;AAExB,MAAI,UAAU,IAAI;AAAE,UAAM,IAAI,MAAM,4BAA4B,UAAU;AAAA,EAAE;AAE5E,OAAK,UAAU,OAAO,OAAO,GAAG;AAAA,IAC9B,MAAM;AAAA,IACN,SAAS;AAAA,IACT;AAAA,IACA,KAAK,IAAI,OAAO,CAAC;AAAA,EACnB,CAAC;AAED,OAAK,YAAY;AACnB;AA0BA,MAAM,UAAU,QAAQ,SAAU,WAAW,UAAU,IAAI,SAAS;AAClE,QAAM,QAAQ,KAAK,SAAS,SAAS;AACrC,QAAM,MAAM,WAAW,CAAC;AAExB,MAAI,UAAU,IAAI;AAAE,UAAM,IAAI,MAAM,4BAA4B,SAAS;AAAA,EAAE;AAE3E,OAAK,UAAU,OAAO,QAAQ,GAAG,GAAG;AAAA,IAClC,MAAM;AAAA,IACN,SAAS;AAAA,IACT;AAAA,IACA,KAAK,IAAI,OAAO,CAAC;AAAA,EACnB,CAAC;AAED,OAAK,YAAY;AACnB;AAyBA,MAAM,UAAU,OAAO,SAAU,UAAU,IAAI,SAAS;AACtD,QAAM,MAAM,WAAW,CAAC;AAExB,OAAK,UAAU,KAAK;AAAA,IAClB,MAAM;AAAA,IACN,SAAS;AAAA,IACT;AAAA,IACA,KAAK,IAAI,OAAO,CAAC;AAAA,EACnB,CAAC;AAED,OAAK,YAAY;AACnB;AAcA,MAAM,UAAU,SAAS,SAAUC,OAAM,eAAe;AACtD,MAAI,CAAC,MAAM,QAAQA,KAAI,GAAG;AAAE,IAAAA,QAAO,CAACA,KAAI;AAAA,EAAE;AAE1C,QAAM,SAAS,CAAC;AAGhB,EAAAA,MAAK,QAAQ,SAAU,MAAM;AAC3B,UAAM,MAAM,KAAK,SAAS,IAAI;AAE9B,QAAI,MAAM,GAAG;AACX,UAAI,eAAe;AAAE;AAAA,MAAO;AAC5B,YAAM,IAAI,MAAM,sCAAsC,IAAI;AAAA,IAC5D;AACA,SAAK,UAAU,GAAG,EAAE,UAAU;AAC9B,WAAO,KAAK,IAAI;AAAA,EAClB,GAAG,IAAI;AAEP,OAAK,YAAY;AACjB,SAAO;AACT;AAYA,MAAM,UAAU,aAAa,SAAUA,OAAM,eAAe;AAC1D,MAAI,CAAC,MAAM,QAAQA,KAAI,GAAG;AAAE,IAAAA,QAAO,CAACA,KAAI;AAAA,EAAE;AAE1C,OAAK,UAAU,QAAQ,SAAU,MAAM;AAAE,SAAK,UAAU;AAAA,EAAM,CAAC;AAE/D,OAAK,OAAOA,OAAM,aAAa;AACjC;AAcA,MAAM,UAAU,UAAU,SAAUA,OAAM,eAAe;AACvD,MAAI,CAAC,MAAM,QAAQA,KAAI,GAAG;AAAE,IAAAA,QAAO,CAACA,KAAI;AAAA,EAAE;AAE1C,QAAM,SAAS,CAAC;AAGhB,EAAAA,MAAK,QAAQ,SAAU,MAAM;AAC3B,UAAM,MAAM,KAAK,SAAS,IAAI;AAE9B,QAAI,MAAM,GAAG;AACX,UAAI,eAAe;AAAE;AAAA,MAAO;AAC5B,YAAM,IAAI,MAAM,sCAAsC,IAAI;AAAA,IAC5D;AACA,SAAK,UAAU,GAAG,EAAE,UAAU;AAC9B,WAAO,KAAK,IAAI;AAAA,EAClB,GAAG,IAAI;AAEP,OAAK,YAAY;AACjB,SAAO;AACT;AAWA,MAAM,UAAU,WAAW,SAAU,WAAW;AAC9C,MAAI,KAAK,cAAc,MAAM;AAC3B,SAAK,YAAY;AAAA,EACnB;AAGA,SAAO,KAAK,UAAU,SAAS,KAAK,CAAC;AACvC;AAEA,IAAO,gBAAQ;;;ACxUf,SAAS,MAAO,MAAM,KAAK,SAAS;AAMlC,OAAK,OAAW;AAOhB,OAAK,MAAW;AAOhB,OAAK,QAAW;AAOhB,OAAK,MAAW;AAWhB,OAAK,UAAW;AAOhB,OAAK,QAAW;AAOhB,OAAK,WAAW;AAQhB,OAAK,UAAW;AAOhB,OAAK,SAAW;AAWhB,OAAK,OAAW;AAOhB,OAAK,OAAW;AAQhB,OAAK,QAAW;AAQhB,OAAK,SAAW;AAClB;AAOA,MAAM,UAAU,YAAY,SAAS,UAAW,MAAM;AACpD,MAAI,CAAC,KAAK,OAAO;AAAE,WAAO;AAAA,EAAG;AAE7B,QAAM,QAAQ,KAAK;AAEnB,WAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,QAAI,MAAM,CAAC,EAAE,CAAC,MAAM,MAAM;AAAE,aAAO;AAAA,IAAE;AAAA,EACvC;AACA,SAAO;AACT;AAOA,MAAM,UAAU,WAAW,SAAS,SAAU,UAAU;AACtD,MAAI,KAAK,OAAO;AACd,SAAK,MAAM,KAAK,QAAQ;AAAA,EAC1B,OAAO;AACL,SAAK,QAAQ,CAAC,QAAQ;AAAA,EACxB;AACF;AAOA,MAAM,UAAU,UAAU,SAAS,QAAS,MAAM,OAAO;AACvD,QAAM,MAAM,KAAK,UAAU,IAAI;AAC/B,QAAM,WAAW,CAAC,MAAM,KAAK;AAE7B,MAAI,MAAM,GAAG;AACX,SAAK,SAAS,QAAQ;AAAA,EACxB,OAAO;AACL,SAAK,MAAM,GAAG,IAAI;AAAA,EACpB;AACF;AAOA,MAAM,UAAU,UAAU,SAAS,QAAS,MAAM;AAChD,QAAM,MAAM,KAAK,UAAU,IAAI;AAC/B,MAAI,QAAQ;AACZ,MAAI,OAAO,GAAG;AACZ,YAAQ,KAAK,MAAM,GAAG,EAAE,CAAC;AAAA,EAC3B;AACA,SAAO;AACT;AAQA,MAAM,UAAU,WAAW,SAAS,SAAU,MAAM,OAAO;AACzD,QAAM,MAAM,KAAK,UAAU,IAAI;AAE/B,MAAI,MAAM,GAAG;AACX,SAAK,SAAS,CAAC,MAAM,KAAK,CAAC;AAAA,EAC7B,OAAO;AACL,SAAK,MAAM,GAAG,EAAE,CAAC,IAAI,KAAK,MAAM,GAAG,EAAE,CAAC,IAAI,MAAM;AAAA,EAClD;AACF;AAEA,IAAO,gBAAQ;;;ACzLf,SAAS,UAAW,KAAK,IAAI,KAAK;AAChC,OAAK,MAAM;AACX,OAAK,MAAM;AACX,OAAK,SAAS,CAAC;AACf,OAAK,aAAa;AAClB,OAAK,KAAK;AACZ;AAGA,UAAU,UAAU,QAAQ;AAE5B,IAAO,qBAAQ;;;ACbf,IAAM,cAAe;AACrB,IAAM,UAAe;AAEN,SAAR,UAA4B,OAAO;AACxC,MAAI;AAGJ,QAAM,MAAM,IAAI,QAAQ,aAAa,IAAI;AAGzC,QAAM,IAAI,QAAQ,SAAS,GAAQ;AAEnC,QAAM,MAAM;AACd;;;AChBe,SAAR,MAAwB,OAAO;AACpC,MAAI;AAEJ,MAAI,MAAM,YAAY;AACpB,YAAiB,IAAI,MAAM,MAAM,UAAU,IAAI,CAAC;AAChD,UAAM,UAAW,MAAM;AACvB,UAAM,MAAW,CAAC,GAAG,CAAC;AACtB,UAAM,WAAW,CAAC;AAClB,UAAM,OAAO,KAAK,KAAK;AAAA,EACzB,OAAO;AACL,UAAM,GAAG,MAAM,MAAM,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK,MAAM,MAAM;AAAA,EACnE;AACF;;;ACZe,SAAR,OAAyB,OAAO;AACrC,QAAM,SAAS,MAAM;AAGrB,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,UAAM,MAAM,OAAO,CAAC;AACpB,QAAI,IAAI,SAAS,UAAU;AACzB,YAAM,GAAG,OAAO,MAAM,IAAI,SAAS,MAAM,IAAI,MAAM,KAAK,IAAI,QAAQ;AAAA,IACtE;AAAA,EACF;AACF;;;ACHA,SAAS,WAAY,KAAK;AACxB,SAAO,YAAY,KAAK,GAAG;AAC7B;AACA,SAAS,YAAa,KAAK;AACzB,SAAO,aAAa,KAAK,GAAG;AAC9B;AAEe,SAAR,QAA0B,OAAO;AACtC,QAAM,cAAc,MAAM;AAE1B,MAAI,CAAC,MAAM,GAAG,QAAQ,SAAS;AAAE;AAAA,EAAO;AAExC,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,IAAI,GAAG,KAAK;AAClD,QAAI,YAAY,CAAC,EAAE,SAAS,YACxB,CAAC,MAAM,GAAG,QAAQ,QAAQ,YAAY,CAAC,EAAE,OAAO,GAAG;AACrD;AAAA,IACF;AAEA,QAAI,SAAS,YAAY,CAAC,EAAE;AAE5B,QAAI,gBAAgB;AAIpB,aAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,YAAM,eAAe,OAAO,CAAC;AAG7B,UAAI,aAAa,SAAS,cAAc;AACtC;AACA,eAAO,OAAO,CAAC,EAAE,UAAU,aAAa,SAAS,OAAO,CAAC,EAAE,SAAS,aAAa;AAC/E;AAAA,QACF;AACA;AAAA,MACF;AAGA,UAAI,aAAa,SAAS,eAAe;AACvC,YAAI,WAAW,aAAa,OAAO,KAAK,gBAAgB,GAAG;AACzD;AAAA,QACF;AACA,YAAI,YAAY,aAAa,OAAO,GAAG;AACrC;AAAA,QACF;AAAA,MACF;AACA,UAAI,gBAAgB,GAAG;AAAE;AAAA,MAAS;AAElC,UAAI,aAAa,SAAS,UAAU,MAAM,GAAG,QAAQ,KAAK,aAAa,OAAO,GAAG;AAC/E,cAAMC,QAAO,aAAa;AAC1B,YAAI,QAAQ,MAAM,GAAG,QAAQ,MAAMA,KAAI;AAGvC,cAAM,QAAQ,CAAC;AACf,YAAI,QAAQ,aAAa;AACzB,YAAI,UAAU;AAKd,YAAI,MAAM,SAAS,KACf,MAAM,CAAC,EAAE,UAAU,KACnB,IAAI,KACJ,OAAO,IAAI,CAAC,EAAE,SAAS,gBAAgB;AACzC,kBAAQ,MAAM,MAAM,CAAC;AAAA,QACvB;AAEA,iBAAS,KAAK,GAAG,KAAK,MAAM,QAAQ,MAAM;AACxC,gBAAM,MAAM,MAAM,EAAE,EAAE;AACtB,gBAAM,UAAU,MAAM,GAAG,cAAc,GAAG;AAC1C,cAAI,CAAC,MAAM,GAAG,aAAa,OAAO,GAAG;AAAE;AAAA,UAAS;AAEhD,cAAI,UAAU,MAAM,EAAE,EAAE;AAMxB,cAAI,CAAC,MAAM,EAAE,EAAE,QAAQ;AACrB,sBAAU,MAAM,GAAG,kBAAkB,YAAY,OAAO,EAAE,QAAQ,cAAc,EAAE;AAAA,UACpF,WAAW,MAAM,EAAE,EAAE,WAAW,aAAa,CAAC,YAAY,KAAK,OAAO,GAAG;AACvE,sBAAU,MAAM,GAAG,kBAAkB,YAAY,OAAO,EAAE,QAAQ,YAAY,EAAE;AAAA,UAClF,OAAO;AACL,sBAAU,MAAM,GAAG,kBAAkB,OAAO;AAAA,UAC9C;AAEA,gBAAM,MAAM,MAAM,EAAE,EAAE;AAEtB,cAAI,MAAM,SAAS;AACjB,kBAAM,QAAU,IAAI,MAAM,MAAM,QAAQ,IAAI,CAAC;AAC7C,kBAAM,UAAUA,MAAK,MAAM,SAAS,GAAG;AACvC,kBAAM,QAAU;AAChB,kBAAM,KAAK,KAAK;AAAA,UAClB;AAEA,gBAAM,UAAY,IAAI,MAAM,MAAM,aAAa,KAAK,CAAC;AACrD,kBAAQ,QAAU,CAAC,CAAC,QAAQ,OAAO,CAAC;AACpC,kBAAQ,QAAU;AAClB,kBAAQ,SAAU;AAClB,kBAAQ,OAAU;AAClB,gBAAM,KAAK,OAAO;AAElB,gBAAM,UAAY,IAAI,MAAM,MAAM,QAAQ,IAAI,CAAC;AAC/C,kBAAQ,UAAU;AAClB,kBAAQ,QAAU;AAClB,gBAAM,KAAK,OAAO;AAElB,gBAAM,UAAY,IAAI,MAAM,MAAM,cAAc,KAAK,EAAE;AACvD,kBAAQ,QAAU,EAAE;AACpB,kBAAQ,SAAU;AAClB,kBAAQ,OAAU;AAClB,gBAAM,KAAK,OAAO;AAElB,oBAAU,MAAM,EAAE,EAAE;AAAA,QACtB;AACA,YAAI,UAAUA,MAAK,QAAQ;AACzB,gBAAM,QAAU,IAAI,MAAM,MAAM,QAAQ,IAAI,CAAC;AAC7C,gBAAM,UAAUA,MAAK,MAAM,OAAO;AAClC,gBAAM,QAAU;AAChB,gBAAM,KAAK,KAAK;AAAA,QAClB;AAGA,oBAAY,CAAC,EAAE,WAAW,SAAS,eAAe,QAAQ,GAAG,KAAK;AAAA,MACpE;AAAA,IACF;AAAA,EACF;AACF;;;ACtHA,IAAM,UAAU;AAIhB,IAAM,sBAAsB;AAE5B,IAAM,iBAAiB;AACvB,IAAM,cAAc;AAAA,EAClB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,IAAI;AACN;AAEA,SAAS,UAAWC,QAAO,MAAM;AAC/B,SAAO,YAAY,KAAK,YAAY,CAAC;AACvC;AAEA,SAAS,eAAgB,cAAc;AACrC,MAAI,kBAAkB;AAEtB,WAAS,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG,KAAK;AACjD,UAAM,QAAQ,aAAa,CAAC;AAE5B,QAAI,MAAM,SAAS,UAAU,CAAC,iBAAiB;AAC7C,YAAM,UAAU,MAAM,QAAQ,QAAQ,gBAAgB,SAAS;AAAA,IACjE;AAEA,QAAI,MAAM,SAAS,eAAe,MAAM,SAAS,QAAQ;AACvD;AAAA,IACF;AAEA,QAAI,MAAM,SAAS,gBAAgB,MAAM,SAAS,QAAQ;AACxD;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,aAAc,cAAc;AACnC,MAAI,kBAAkB;AAEtB,WAAS,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG,KAAK;AACjD,UAAM,QAAQ,aAAa,CAAC;AAE5B,QAAI,MAAM,SAAS,UAAU,CAAC,iBAAiB;AAC7C,UAAI,QAAQ,KAAK,MAAM,OAAO,GAAG;AAC/B,cAAM,UAAU,MAAM,QACnB,QAAQ,QAAQ,GAAG,EAGnB,QAAQ,WAAW,GAAG,EAAE,QAAQ,YAAY,MAAM,EAClD,QAAQ,eAAe,QAAQ,EAAE,QAAQ,UAAU,GAAG,EAEtD,QAAQ,2BAA2B,KAAU,EAE7C,QAAQ,sBAAsB,KAAU,EACxC,QAAQ,8BAA8B,KAAU;AAAA,MACrD;AAAA,IACF;AAEA,QAAI,MAAM,SAAS,eAAe,MAAM,SAAS,QAAQ;AACvD;AAAA,IACF;AAEA,QAAI,MAAM,SAAS,gBAAgB,MAAM,SAAS,QAAQ;AACxD;AAAA,IACF;AAAA,EACF;AACF;AAEe,SAAR,QAA0B,OAAO;AACtC,MAAI;AAEJ,MAAI,CAAC,MAAM,GAAG,QAAQ,aAAa;AAAE;AAAA,EAAO;AAE5C,OAAK,SAAS,MAAM,OAAO,SAAS,GAAG,UAAU,GAAG,UAAU;AAC5D,QAAI,MAAM,OAAO,MAAM,EAAE,SAAS,UAAU;AAAE;AAAA,IAAS;AAEvD,QAAI,oBAAoB,KAAK,MAAM,OAAO,MAAM,EAAE,OAAO,GAAG;AAC1D,qBAAe,MAAM,OAAO,MAAM,EAAE,QAAQ;AAAA,IAC9C;AAEA,QAAI,QAAQ,KAAK,MAAM,OAAO,MAAM,EAAE,OAAO,GAAG;AAC9C,mBAAa,MAAM,OAAO,MAAM,EAAE,QAAQ;AAAA,IAC5C;AAAA,EACF;AACF;;;AC/FA,IAAM,gBAAgB;AACtB,IAAM,WAAW;AACjB,IAAM,aAAa;AAEnB,SAAS,UAAW,KAAK,OAAO,IAAI;AAClC,SAAO,IAAI,MAAM,GAAG,KAAK,IAAI,KAAK,IAAI,MAAM,QAAQ,CAAC;AACvD;AAEA,SAAS,gBAAiB,QAAQ,OAAO;AACvC,MAAI;AAEJ,QAAM,QAAQ,CAAC;AAEf,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAM,QAAQ,OAAO,CAAC;AAEtB,UAAM,YAAY,OAAO,CAAC,EAAE;AAE5B,SAAK,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AACtC,UAAI,MAAM,CAAC,EAAE,SAAS,WAAW;AAAE;AAAA,MAAM;AAAA,IAC3C;AACA,UAAM,SAAS,IAAI;AAEnB,QAAI,MAAM,SAAS,QAAQ;AAAE;AAAA,IAAS;AAEtC,QAAIC,QAAO,MAAM;AACjB,QAAI,MAAM;AACV,QAAI,MAAMA,MAAK;AAGf;AACA,aAAO,MAAM,KAAK;AAChB,iBAAS,YAAY;AACrB,cAAM,IAAI,SAAS,KAAKA,KAAI;AAC5B,YAAI,CAAC,GAAG;AAAE;AAAA,QAAM;AAEhB,YAAI,UAAU;AACd,YAAI,WAAW;AACf,cAAM,EAAE,QAAQ;AAChB,cAAM,WAAY,EAAE,CAAC,MAAM;AAK3B,YAAI,WAAW;AAEf,YAAI,EAAE,QAAQ,KAAK,GAAG;AACpB,qBAAWA,MAAK,WAAW,EAAE,QAAQ,CAAC;AAAA,QACxC,OAAO;AACL,eAAK,IAAI,IAAI,GAAG,KAAK,GAAG,KAAK;AAC3B,gBAAI,OAAO,CAAC,EAAE,SAAS,eAAe,OAAO,CAAC,EAAE,SAAS,YAAa;AACtE,gBAAI,CAAC,OAAO,CAAC,EAAE,QAAS;AAExB,uBAAW,OAAO,CAAC,EAAE,QAAQ,WAAW,OAAO,CAAC,EAAE,QAAQ,SAAS,CAAC;AACpE;AAAA,UACF;AAAA,QACF;AAKA,YAAI,WAAW;AAEf,YAAI,MAAM,KAAK;AACb,qBAAWA,MAAK,WAAW,GAAG;AAAA,QAChC,OAAO;AACL,eAAK,IAAI,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,gBAAI,OAAO,CAAC,EAAE,SAAS,eAAe,OAAO,CAAC,EAAE,SAAS,YAAa;AACtE,gBAAI,CAAC,OAAO,CAAC,EAAE,QAAS;AAExB,uBAAW,OAAO,CAAC,EAAE,QAAQ,WAAW,CAAC;AACzC;AAAA,UACF;AAAA,QACF;AAEA,cAAM,kBAAkB,eAAe,QAAQ,KAAK,YAAY,OAAO,aAAa,QAAQ,CAAC;AAC7F,cAAM,kBAAkB,eAAe,QAAQ,KAAK,YAAY,OAAO,aAAa,QAAQ,CAAC;AAE7F,cAAM,mBAAmB,aAAa,QAAQ;AAC9C,cAAM,mBAAmB,aAAa,QAAQ;AAE9C,YAAI,kBAAkB;AACpB,oBAAU;AAAA,QACZ,WAAW,iBAAiB;AAC1B,cAAI,EAAE,oBAAoB,kBAAkB;AAC1C,sBAAU;AAAA,UACZ;AAAA,QACF;AAEA,YAAI,kBAAkB;AACpB,qBAAW;AAAA,QACb,WAAW,iBAAiB;AAC1B,cAAI,EAAE,oBAAoB,kBAAkB;AAC1C,uBAAW;AAAA,UACb;AAAA,QACF;AAEA,YAAI,aAAa,MAAgB,EAAE,CAAC,MAAM,KAAK;AAC7C,cAAI,YAAY,MAAgB,YAAY,IAAc;AAExD,uBAAW,UAAU;AAAA,UACvB;AAAA,QACF;AAEA,YAAI,WAAW,UAAU;AAQvB,oBAAU;AACV,qBAAW;AAAA,QACb;AAEA,YAAI,CAAC,WAAW,CAAC,UAAU;AAEzB,cAAI,UAAU;AACZ,kBAAM,UAAU,UAAU,MAAM,SAAS,EAAE,OAAO,UAAU;AAAA,UAC9D;AACA;AAAA,QACF;AAEA,YAAI,UAAU;AAEZ,eAAK,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AACtC,gBAAI,OAAO,MAAM,CAAC;AAClB,gBAAI,MAAM,CAAC,EAAE,QAAQ,WAAW;AAAE;AAAA,YAAM;AACxC,gBAAI,KAAK,WAAW,YAAY,MAAM,CAAC,EAAE,UAAU,WAAW;AAC5D,qBAAO,MAAM,CAAC;AAEd,kBAAI;AACJ,kBAAI;AACJ,kBAAI,UAAU;AACZ,4BAAY,MAAM,GAAG,QAAQ,OAAO,CAAC;AACrC,6BAAa,MAAM,GAAG,QAAQ,OAAO,CAAC;AAAA,cACxC,OAAO;AACL,4BAAY,MAAM,GAAG,QAAQ,OAAO,CAAC;AACrC,6BAAa,MAAM,GAAG,QAAQ,OAAO,CAAC;AAAA,cACxC;AAKA,oBAAM,UAAU,UAAU,MAAM,SAAS,EAAE,OAAO,UAAU;AAC5D,qBAAO,KAAK,KAAK,EAAE,UAAU;AAAA,gBAC3B,OAAO,KAAK,KAAK,EAAE;AAAA,gBAAS,KAAK;AAAA,gBAAK;AAAA,cAAS;AAEjD,qBAAO,WAAW,SAAS;AAC3B,kBAAI,KAAK,UAAU,GAAG;AAAE,uBAAO,UAAU,SAAS;AAAA,cAAE;AAEpD,cAAAA,QAAO,MAAM;AACb,oBAAMA,MAAK;AAEX,oBAAM,SAAS;AACf,uBAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAEA,YAAI,SAAS;AACX,gBAAM,KAAK;AAAA,YACT,OAAO;AAAA,YACP,KAAK,EAAE;AAAA,YACP,QAAQ;AAAA,YACR,OAAO;AAAA,UACT,CAAC;AAAA,QACH,WAAW,YAAY,UAAU;AAC/B,gBAAM,UAAU,UAAU,MAAM,SAAS,EAAE,OAAO,UAAU;AAAA,QAC9D;AAAA,MACF;AAAA,EACF;AACF;AAEe,SAAR,YAA8B,OAAO;AAE1C,MAAI,CAAC,MAAM,GAAG,QAAQ,aAAa;AAAE;AAAA,EAAO;AAE5C,WAAS,SAAS,MAAM,OAAO,SAAS,GAAG,UAAU,GAAG,UAAU;AAChE,QAAI,MAAM,OAAO,MAAM,EAAE,SAAS,YAC9B,CAAC,cAAc,KAAK,MAAM,OAAO,MAAM,EAAE,OAAO,GAAG;AACrD;AAAA,IACF;AAEA,oBAAgB,MAAM,OAAO,MAAM,EAAE,UAAU,KAAK;AAAA,EACtD;AACF;;;ACxLe,SAAR,UAA4B,OAAO;AACxC,MAAI,MAAM;AACV,QAAM,cAAc,MAAM;AAC1B,QAAM,IAAI,YAAY;AAEtB,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,QAAI,YAAY,CAAC,EAAE,SAAS,SAAU;AAEtC,UAAM,SAAS,YAAY,CAAC,EAAE;AAC9B,UAAM,MAAM,OAAO;AAEnB,SAAK,OAAO,GAAG,OAAO,KAAK,QAAQ;AACjC,UAAI,OAAO,IAAI,EAAE,SAAS,gBAAgB;AACxC,eAAO,IAAI,EAAE,OAAO;AAAA,MACtB;AAAA,IACF;AAEA,SAAK,OAAO,OAAO,GAAG,OAAO,KAAK,QAAQ;AACxC,UAAI,OAAO,IAAI,EAAE,SAAS,UACtB,OAAO,IAAI,OACX,OAAO,OAAO,CAAC,EAAE,SAAS,QAAQ;AAEpC,eAAO,OAAO,CAAC,EAAE,UAAU,OAAO,IAAI,EAAE,UAAU,OAAO,OAAO,CAAC,EAAE;AAAA,MACrE,OAAO;AACL,YAAI,SAAS,MAAM;AAAE,iBAAO,IAAI,IAAI,OAAO,IAAI;AAAA,QAAE;AAEjD;AAAA,MACF;AAAA,IACF;AAEA,QAAI,SAAS,MAAM;AACjB,aAAO,SAAS;AAAA,IAClB;AAAA,EACF;AACF;;;ACxBA,IAAM,SAAS;AAAA,EACb,CAAC,aAAkB,SAAW;AAAA,EAC9B,CAAC,SAAkB,KAAO;AAAA,EAC1B,CAAC,UAAkB,MAAQ;AAAA,EAC3B,CAAC,WAAkB,OAAS;AAAA,EAC5B,CAAC,gBAAkB,OAAc;AAAA,EACjC,CAAC,eAAkB,WAAa;AAAA;AAAA;AAAA,EAGhC,CAAC,aAAkB,SAAW;AAChC;AAKA,SAAS,OAAQ;AAMf,OAAK,QAAQ,IAAI,cAAM;AAEvB,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,SAAK,MAAM,KAAK,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC;AAAA,EAC5C;AACF;AAOA,KAAK,UAAU,UAAU,SAAU,OAAO;AACxC,QAAM,QAAQ,KAAK,MAAM,SAAS,EAAE;AAEpC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,UAAM,CAAC,EAAE,KAAK;AAAA,EAChB;AACF;AAEA,KAAK,UAAU,QAAQ;AAEvB,IAAO,sBAAQ;;;ACxDf,SAAS,WAAY,KAAK,IAAI,KAAK,QAAQ;AACzC,OAAK,MAAM;AAGX,OAAK,KAAS;AAEd,OAAK,MAAM;AAMX,OAAK,SAAS;AAEd,OAAK,SAAS,CAAC;AACf,OAAK,SAAS,CAAC;AACf,OAAK,SAAS,CAAC;AACf,OAAK,SAAS,CAAC;AAYf,OAAK,UAAU,CAAC;AAMhB,OAAK,YAAa;AAClB,OAAK,OAAa;AAClB,OAAK,UAAa;AAClB,OAAK,QAAa;AAClB,OAAK,WAAa;AAClB,OAAK,aAAa;AAIlB,OAAK,aAAa;AAElB,OAAK,QAAQ;AAIb,QAAM,IAAI,KAAK;AAEf,WAAS,QAAQ,GAAG,MAAM,GAAG,SAAS,GAAG,SAAS,GAAG,MAAM,EAAE,QAAQ,eAAe,OAAO,MAAM,KAAK,OAAO;AAC3G,UAAM,KAAK,EAAE,WAAW,GAAG;AAE3B,QAAI,CAAC,cAAc;AACjB,UAAI,QAAQ,EAAE,GAAG;AACf;AAEA,YAAI,OAAO,GAAM;AACf,oBAAU,IAAI,SAAS;AAAA,QACzB,OAAO;AACL;AAAA,QACF;AACA;AAAA,MACF,OAAO;AACL,uBAAe;AAAA,MACjB;AAAA,IACF;AAEA,QAAI,OAAO,MAAQ,QAAQ,MAAM,GAAG;AAClC,UAAI,OAAO,IAAM;AAAE;AAAA,MAAM;AACzB,WAAK,OAAO,KAAK,KAAK;AACtB,WAAK,OAAO,KAAK,GAAG;AACpB,WAAK,OAAO,KAAK,MAAM;AACvB,WAAK,OAAO,KAAK,MAAM;AACvB,WAAK,QAAQ,KAAK,CAAC;AAEnB,qBAAe;AACf,eAAS;AACT,eAAS;AACT,cAAQ,MAAM;AAAA,IAChB;AAAA,EACF;AAGA,OAAK,OAAO,KAAK,EAAE,MAAM;AACzB,OAAK,OAAO,KAAK,EAAE,MAAM;AACzB,OAAK,OAAO,KAAK,CAAC;AAClB,OAAK,OAAO,KAAK,CAAC;AAClB,OAAK,QAAQ,KAAK,CAAC;AAEnB,OAAK,UAAU,KAAK,OAAO,SAAS;AACtC;AAIA,WAAW,UAAU,OAAO,SAAU,MAAM,KAAK,SAAS;AACxD,QAAM,QAAQ,IAAI,cAAM,MAAM,KAAK,OAAO;AAC1C,QAAM,QAAQ;AAEd,MAAI,UAAU,EAAG,MAAK;AACtB,QAAM,QAAQ,KAAK;AACnB,MAAI,UAAU,EAAG,MAAK;AAEtB,OAAK,OAAO,KAAK,KAAK;AACtB,SAAO;AACT;AAEA,WAAW,UAAU,UAAU,SAAS,QAAS,MAAM;AACrD,SAAO,KAAK,OAAO,IAAI,IAAI,KAAK,OAAO,IAAI,KAAK,KAAK,OAAO,IAAI;AAClE;AAEA,WAAW,UAAU,iBAAiB,SAAS,eAAgB,MAAM;AACnE,WAAS,MAAM,KAAK,SAAS,OAAO,KAAK,QAAQ;AAC/C,QAAI,KAAK,OAAO,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,OAAO,IAAI,GAAG;AAC7D;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAGA,WAAW,UAAU,aAAa,SAAS,WAAY,KAAK;AAC1D,WAAS,MAAM,KAAK,IAAI,QAAQ,MAAM,KAAK,OAAO;AAChD,UAAM,KAAK,KAAK,IAAI,WAAW,GAAG;AAClC,QAAI,CAAC,QAAQ,EAAE,GAAG;AAAE;AAAA,IAAM;AAAA,EAC5B;AACA,SAAO;AACT;AAGA,WAAW,UAAU,iBAAiB,SAAS,eAAgB,KAAK,KAAK;AACvE,MAAI,OAAO,KAAK;AAAE,WAAO;AAAA,EAAI;AAE7B,SAAO,MAAM,KAAK;AAChB,QAAI,CAAC,QAAQ,KAAK,IAAI,WAAW,EAAE,GAAG,CAAC,GAAG;AAAE,aAAO,MAAM;AAAA,IAAE;AAAA,EAC7D;AACA,SAAO;AACT;AAGA,WAAW,UAAU,YAAY,SAAS,UAAW,KAAKC,OAAM;AAC9D,WAAS,MAAM,KAAK,IAAI,QAAQ,MAAM,KAAK,OAAO;AAChD,QAAI,KAAK,IAAI,WAAW,GAAG,MAAMA,OAAM;AAAE;AAAA,IAAM;AAAA,EACjD;AACA,SAAO;AACT;AAGA,WAAW,UAAU,gBAAgB,SAAS,cAAe,KAAKA,OAAM,KAAK;AAC3E,MAAI,OAAO,KAAK;AAAE,WAAO;AAAA,EAAI;AAE7B,SAAO,MAAM,KAAK;AAChB,QAAIA,UAAS,KAAK,IAAI,WAAW,EAAE,GAAG,GAAG;AAAE,aAAO,MAAM;AAAA,IAAE;AAAA,EAC5D;AACA,SAAO;AACT;AAGA,WAAW,UAAU,WAAW,SAAS,SAAU,OAAO,KAAK,QAAQ,YAAY;AACjF,MAAI,SAAS,KAAK;AAChB,WAAO;AAAA,EACT;AAEA,QAAM,QAAQ,IAAI,MAAM,MAAM,KAAK;AAEnC,WAAS,IAAI,GAAG,OAAO,OAAO,OAAO,KAAK,QAAQ,KAAK;AACrD,QAAI,aAAa;AACjB,UAAM,YAAY,KAAK,OAAO,IAAI;AAClC,QAAI,QAAQ;AACZ,QAAI;AAEJ,QAAI,OAAO,IAAI,OAAO,YAAY;AAEhC,aAAO,KAAK,OAAO,IAAI,IAAI;AAAA,IAC7B,OAAO;AACL,aAAO,KAAK,OAAO,IAAI;AAAA,IACzB;AAEA,WAAO,QAAQ,QAAQ,aAAa,QAAQ;AAC1C,YAAM,KAAK,KAAK,IAAI,WAAW,KAAK;AAEpC,UAAI,QAAQ,EAAE,GAAG;AACf,YAAI,OAAO,GAAM;AACf,wBAAc,KAAK,aAAa,KAAK,QAAQ,IAAI,KAAK;AAAA,QACxD,OAAO;AACL;AAAA,QACF;AAAA,MACF,WAAW,QAAQ,YAAY,KAAK,OAAO,IAAI,GAAG;AAEhD;AAAA,MACF,OAAO;AACL;AAAA,MACF;AAEA;AAAA,IACF;AAEA,QAAI,aAAa,QAAQ;AAGvB,YAAM,CAAC,IAAI,IAAI,MAAM,aAAa,SAAS,CAAC,EAAE,KAAK,GAAG,IAAI,KAAK,IAAI,MAAM,OAAO,IAAI;AAAA,IACtF,OAAO;AACL,YAAM,CAAC,IAAI,KAAK,IAAI,MAAM,OAAO,IAAI;AAAA,IACvC;AAAA,EACF;AAEA,SAAO,MAAM,KAAK,EAAE;AACtB;AAGA,WAAW,UAAU,QAAQ;AAE7B,IAAO,sBAAQ;;;ACjNf,IAAM,0BAA0B;AAEhC,SAAS,QAAS,OAAO,MAAM;AAC7B,QAAM,MAAM,MAAM,OAAO,IAAI,IAAI,MAAM,OAAO,IAAI;AAClD,QAAM,MAAM,MAAM,OAAO,IAAI;AAE7B,SAAO,MAAM,IAAI,MAAM,KAAK,GAAG;AACjC;AAEA,SAAS,aAAc,KAAK;AAC1B,QAAM,SAAS,CAAC;AAChB,QAAM,MAAM,IAAI;AAEhB,MAAI,MAAM;AACV,MAAI,KAAK,IAAI,WAAW,GAAG;AAC3B,MAAI,YAAY;AAChB,MAAI,UAAU;AACd,MAAI,UAAU;AAEd,SAAO,MAAM,KAAK;AAChB,QAAI,OAAO,KAAa;AACtB,UAAI,CAAC,WAAW;AAEd,eAAO,KAAK,UAAU,IAAI,UAAU,SAAS,GAAG,CAAC;AACjD,kBAAU;AACV,kBAAU,MAAM;AAAA,MAClB,OAAO;AAEL,mBAAW,IAAI,UAAU,SAAS,MAAM,CAAC;AACzC,kBAAU;AAAA,MACZ;AAAA,IACF;AAEA,gBAAa,OAAO;AACpB;AAEA,SAAK,IAAI,WAAW,GAAG;AAAA,EACzB;AAEA,SAAO,KAAK,UAAU,IAAI,UAAU,OAAO,CAAC;AAE5C,SAAO;AACT;AAEe,SAAR,MAAwB,OAAO,WAAW,SAAS,QAAQ;AAEhE,MAAI,YAAY,IAAI,SAAS;AAAE,WAAO;AAAA,EAAM;AAE5C,MAAI,WAAW,YAAY;AAE3B,MAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,WAAW;AAAE,WAAO;AAAA,EAAM;AAG7D,MAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,aAAa,GAAG;AAAE,WAAO;AAAA,EAAM;AAMlE,MAAI,MAAM,MAAM,OAAO,QAAQ,IAAI,MAAM,OAAO,QAAQ;AACxD,MAAI,OAAO,MAAM,OAAO,QAAQ,GAAG;AAAE,WAAO;AAAA,EAAM;AAElD,QAAM,UAAU,MAAM,IAAI,WAAW,KAAK;AAC1C,MAAI,YAAY,OAAe,YAAY,MAAe,YAAY,IAAa;AAAE,WAAO;AAAA,EAAM;AAElG,MAAI,OAAO,MAAM,OAAO,QAAQ,GAAG;AAAE,WAAO;AAAA,EAAM;AAElD,QAAM,WAAW,MAAM,IAAI,WAAW,KAAK;AAC3C,MAAI,aAAa,OAAe,aAAa,MAAe,aAAa,MAAe,CAAC,QAAQ,QAAQ,GAAG;AAC1G,WAAO;AAAA,EACT;AAIA,MAAI,YAAY,MAAe,QAAQ,QAAQ,GAAG;AAAE,WAAO;AAAA,EAAM;AAEjE,SAAO,MAAM,MAAM,OAAO,QAAQ,GAAG;AACnC,UAAM,KAAK,MAAM,IAAI,WAAW,GAAG;AAEnC,QAAI,OAAO,OAAe,OAAO,MAAe,OAAO,MAAe,CAAC,QAAQ,EAAE,GAAG;AAAE,aAAO;AAAA,IAAM;AAEnG;AAAA,EACF;AAEA,MAAI,WAAW,QAAQ,OAAO,YAAY,CAAC;AAC3C,MAAI,UAAU,SAAS,MAAM,GAAG;AAChC,QAAM,SAAS,CAAC;AAChB,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAM,IAAI,QAAQ,CAAC,EAAE,KAAK;AAC1B,QAAI,CAAC,GAAG;AAGN,UAAI,MAAM,KAAK,MAAM,QAAQ,SAAS,GAAG;AACvC;AAAA,MACF,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,CAAC,WAAW,KAAK,CAAC,GAAG;AAAE,aAAO;AAAA,IAAM;AACxC,QAAI,EAAE,WAAW,EAAE,SAAS,CAAC,MAAM,IAAa;AAC9C,aAAO,KAAK,EAAE,WAAW,CAAC,MAAM,KAAc,WAAW,OAAO;AAAA,IAClE,WAAW,EAAE,WAAW,CAAC,MAAM,IAAa;AAC1C,aAAO,KAAK,MAAM;AAAA,IACpB,OAAO;AACL,aAAO,KAAK,EAAE;AAAA,IAChB;AAAA,EACF;AAEA,aAAW,QAAQ,OAAO,SAAS,EAAE,KAAK;AAC1C,MAAI,SAAS,QAAQ,GAAG,MAAM,IAAI;AAAE,WAAO;AAAA,EAAM;AACjD,MAAI,MAAM,OAAO,SAAS,IAAI,MAAM,aAAa,GAAG;AAAE,WAAO;AAAA,EAAM;AACnE,YAAU,aAAa,QAAQ;AAC/B,MAAI,QAAQ,UAAU,QAAQ,CAAC,MAAM,GAAI,SAAQ,MAAM;AACvD,MAAI,QAAQ,UAAU,QAAQ,QAAQ,SAAS,CAAC,MAAM,GAAI,SAAQ,IAAI;AAItE,QAAM,cAAc,QAAQ;AAC5B,MAAI,gBAAgB,KAAK,gBAAgB,OAAO,QAAQ;AAAE,WAAO;AAAA,EAAM;AAEvE,MAAI,QAAQ;AAAE,WAAO;AAAA,EAAK;AAE1B,QAAM,gBAAgB,MAAM;AAC5B,QAAM,aAAa;AAInB,QAAM,kBAAkB,MAAM,GAAG,MAAM,MAAM,SAAS,YAAY;AAElE,QAAM,WAAW,MAAM,KAAK,cAAc,SAAS,CAAC;AACpD,QAAM,aAAa,CAAC,WAAW,CAAC;AAChC,WAAS,MAAM;AAEf,QAAM,YAAY,MAAM,KAAK,cAAc,SAAS,CAAC;AACrD,YAAU,MAAM,CAAC,WAAW,YAAY,CAAC;AAEzC,QAAM,aAAa,MAAM,KAAK,WAAW,MAAM,CAAC;AAChD,aAAW,MAAM,CAAC,WAAW,YAAY,CAAC;AAE1C,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAM,WAAW,MAAM,KAAK,WAAW,MAAM,CAAC;AAC9C,QAAI,OAAO,CAAC,GAAG;AACb,eAAS,QAAS,CAAC,CAAC,SAAS,gBAAgB,OAAO,CAAC,CAAC,CAAC;AAAA,IACzD;AAEA,UAAM,WAAW,MAAM,KAAK,UAAU,IAAI,CAAC;AAC3C,aAAS,UAAW,QAAQ,CAAC,EAAE,KAAK;AACpC,aAAS,WAAW,CAAC;AAErB,UAAM,KAAK,YAAY,MAAM,EAAE;AAAA,EACjC;AAEA,QAAM,KAAK,YAAY,MAAM,EAAE;AAC/B,QAAM,KAAK,eAAe,SAAS,EAAE;AAErC,MAAI;AACJ,MAAI,qBAAqB;AAEzB,OAAK,WAAW,YAAY,GAAG,WAAW,SAAS,YAAY;AAC7D,QAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,WAAW;AAAE;AAAA,IAAM;AAEtD,QAAI,YAAY;AAChB,aAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,IAAI,GAAG,KAAK;AACtD,UAAI,gBAAgB,CAAC,EAAE,OAAO,UAAU,SAAS,IAAI,GAAG;AACtD,oBAAY;AACZ;AAAA,MACF;AAAA,IACF;AAEA,QAAI,WAAW;AAAE;AAAA,IAAM;AACvB,eAAW,QAAQ,OAAO,QAAQ,EAAE,KAAK;AACzC,QAAI,CAAC,UAAU;AAAE;AAAA,IAAM;AACvB,QAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,aAAa,GAAG;AAAE;AAAA,IAAM;AAC3D,cAAU,aAAa,QAAQ;AAC/B,QAAI,QAAQ,UAAU,QAAQ,CAAC,MAAM,GAAI,SAAQ,MAAM;AACvD,QAAI,QAAQ,UAAU,QAAQ,QAAQ,SAAS,CAAC,MAAM,GAAI,SAAQ,IAAI;AAItE,0BAAsB,cAAc,QAAQ;AAC5C,QAAI,qBAAqB,yBAAyB;AAAE;AAAA,IAAM;AAE1D,QAAI,aAAa,YAAY,GAAG;AAC9B,YAAM,YAAY,MAAM,KAAK,cAAc,SAAS,CAAC;AACrD,gBAAU,MAAM,aAAa,CAAC,YAAY,GAAG,CAAC;AAAA,IAChD;AAEA,UAAM,YAAY,MAAM,KAAK,WAAW,MAAM,CAAC;AAC/C,cAAU,MAAM,CAAC,UAAU,WAAW,CAAC;AAEvC,aAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,YAAM,YAAY,MAAM,KAAK,WAAW,MAAM,CAAC;AAC/C,UAAI,OAAO,CAAC,GAAG;AACb,kBAAU,QAAS,CAAC,CAAC,SAAS,gBAAgB,OAAO,CAAC,CAAC,CAAC;AAAA,MAC1D;AAEA,YAAM,WAAW,MAAM,KAAK,UAAU,IAAI,CAAC;AAC3C,eAAS,UAAW,QAAQ,CAAC,IAAI,QAAQ,CAAC,EAAE,KAAK,IAAI;AACrD,eAAS,WAAW,CAAC;AAErB,YAAM,KAAK,YAAY,MAAM,EAAE;AAAA,IACjC;AACA,UAAM,KAAK,YAAY,MAAM,EAAE;AAAA,EACjC;AAEA,MAAI,YAAY;AACd,UAAM,KAAK,eAAe,SAAS,EAAE;AACrC,eAAW,CAAC,IAAI;AAAA,EAClB;AAEA,QAAM,KAAK,eAAe,SAAS,EAAE;AACrC,aAAW,CAAC,IAAI;AAEhB,QAAM,aAAa;AACnB,QAAM,OAAO;AACb,SAAO;AACT;;;ACjOe,SAAR,KAAuB,OAAO,WAAW,SAAsB;AACpE,MAAI,MAAM,OAAO,SAAS,IAAI,MAAM,YAAY,GAAG;AAAE,WAAO;AAAA,EAAM;AAElE,MAAI,WAAW,YAAY;AAC3B,MAAI,OAAO;AAEX,SAAO,WAAW,SAAS;AACzB,QAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B;AACA;AAAA,IACF;AAEA,QAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,aAAa,GAAG;AACjD;AACA,aAAO;AACP;AAAA,IACF;AACA;AAAA,EACF;AAEA,QAAM,OAAO;AAEb,QAAM,QAAU,MAAM,KAAK,cAAc,QAAQ,CAAC;AAClD,QAAM,UAAU,MAAM,SAAS,WAAW,MAAM,IAAI,MAAM,WAAW,KAAK,IAAI;AAC9E,QAAM,MAAU,CAAC,WAAW,MAAM,IAAI;AAEtC,SAAO;AACT;;;AC3Be,SAAR,MAAwB,OAAO,WAAW,SAAS,QAAQ;AAChE,MAAI,MAAM,MAAM,OAAO,SAAS,IAAI,MAAM,OAAO,SAAS;AAC1D,MAAI,MAAM,MAAM,OAAO,SAAS;AAGhC,MAAI,MAAM,OAAO,SAAS,IAAI,MAAM,aAAa,GAAG;AAAE,WAAO;AAAA,EAAM;AAEnE,MAAI,MAAM,IAAI,KAAK;AAAE,WAAO;AAAA,EAAM;AAElC,QAAM,SAAS,MAAM,IAAI,WAAW,GAAG;AAEvC,MAAI,WAAW,OAAe,WAAW,IAAc;AACrD,WAAO;AAAA,EACT;AAGA,MAAI,MAAM;AACV,QAAM,MAAM,UAAU,KAAK,MAAM;AAEjC,MAAI,MAAM,MAAM;AAEhB,MAAI,MAAM,GAAG;AAAE,WAAO;AAAA,EAAM;AAE5B,QAAM,SAAS,MAAM,IAAI,MAAM,KAAK,GAAG;AACvC,QAAM,SAAS,MAAM,IAAI,MAAM,KAAK,GAAG;AAEvC,MAAI,WAAW,IAAc;AAC3B,QAAI,OAAO,QAAQ,OAAO,aAAa,MAAM,CAAC,KAAK,GAAG;AACpD,aAAO;AAAA,IACT;AAAA,EACF;AAGA,MAAI,QAAQ;AAAE,WAAO;AAAA,EAAK;AAG1B,MAAI,WAAW;AACf,MAAI,gBAAgB;AAEpB,aAAS;AACP;AACA,QAAI,YAAY,SAAS;AAGvB;AAAA,IACF;AAEA,UAAM,MAAM,MAAM,OAAO,QAAQ,IAAI,MAAM,OAAO,QAAQ;AAC1D,UAAM,MAAM,OAAO,QAAQ;AAE3B,QAAI,MAAM,OAAO,MAAM,OAAO,QAAQ,IAAI,MAAM,WAAW;AAIzD;AAAA,IACF;AAEA,QAAI,MAAM,IAAI,WAAW,GAAG,MAAM,QAAQ;AAAE;AAAA,IAAS;AAErD,QAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,aAAa,GAAG;AAEjD;AAAA,IACF;AAEA,UAAM,MAAM,UAAU,KAAK,MAAM;AAGjC,QAAI,MAAM,MAAM,KAAK;AAAE;AAAA,IAAS;AAGhC,UAAM,MAAM,WAAW,GAAG;AAE1B,QAAI,MAAM,KAAK;AAAE;AAAA,IAAS;AAE1B,oBAAgB;AAEhB;AAAA,EACF;AAGA,QAAM,MAAM,OAAO,SAAS;AAE5B,QAAM,OAAO,YAAY,gBAAgB,IAAI;AAE7C,QAAM,QAAU,MAAM,KAAK,SAAS,QAAQ,CAAC;AAC7C,QAAM,OAAU;AAChB,QAAM,UAAU,MAAM,SAAS,YAAY,GAAG,UAAU,KAAK,IAAI;AACjE,QAAM,SAAU;AAChB,QAAM,MAAU,CAAC,WAAW,MAAM,IAAI;AAEtC,SAAO;AACT;;;ACzFe,SAAR,WAA6B,OAAO,WAAW,SAAS,QAAQ;AACrE,MAAI,MAAM,MAAM,OAAO,SAAS,IAAI,MAAM,OAAO,SAAS;AAC1D,MAAI,MAAM,MAAM,OAAO,SAAS;AAEhC,QAAM,aAAa,MAAM;AAGzB,MAAI,MAAM,OAAO,SAAS,IAAI,MAAM,aAAa,GAAG;AAAE,WAAO;AAAA,EAAM;AAGnE,MAAI,MAAM,IAAI,WAAW,GAAG,MAAM,IAAa;AAAE,WAAO;AAAA,EAAM;AAI9D,MAAI,QAAQ;AAAE,WAAO;AAAA,EAAK;AAE1B,QAAM,YAAa,CAAC;AACpB,QAAM,aAAa,CAAC;AACpB,QAAM,YAAa,CAAC;AACpB,QAAM,YAAa,CAAC;AAEpB,QAAM,kBAAkB,MAAM,GAAG,MAAM,MAAM,SAAS,YAAY;AAElE,QAAM,gBAAgB,MAAM;AAC5B,QAAM,aAAa;AACnB,MAAI,gBAAgB;AACpB,MAAI;AAoBJ,OAAK,WAAW,WAAW,WAAW,SAAS,YAAY;AASzD,UAAM,cAAc,MAAM,OAAO,QAAQ,IAAI,MAAM;AAEnD,UAAM,MAAM,OAAO,QAAQ,IAAI,MAAM,OAAO,QAAQ;AACpD,UAAM,MAAM,OAAO,QAAQ;AAE3B,QAAI,OAAO,KAAK;AAEd;AAAA,IACF;AAEA,QAAI,MAAM,IAAI,WAAW,KAAK,MAAM,MAAe,CAAC,aAAa;AAI/D,UAAI,UAAU,MAAM,OAAO,QAAQ,IAAI;AACvC,UAAI;AACJ,UAAI;AAGJ,UAAI,MAAM,IAAI,WAAW,GAAG,MAAM,IAAkB;AAGlD;AACA;AACA,oBAAY;AACZ,2BAAmB;AAAA,MACrB,WAAW,MAAM,IAAI,WAAW,GAAG,MAAM,GAAgB;AACvD,2BAAmB;AAEnB,aAAK,MAAM,QAAQ,QAAQ,IAAI,WAAW,MAAM,GAAG;AAGjD;AACA;AACA,sBAAY;AAAA,QACd,OAAO;AAIL,sBAAY;AAAA,QACd;AAAA,MACF,OAAO;AACL,2BAAmB;AAAA,MACrB;AAEA,UAAI,SAAS;AACb,gBAAU,KAAK,MAAM,OAAO,QAAQ,CAAC;AACrC,YAAM,OAAO,QAAQ,IAAI;AAEzB,aAAO,MAAM,KAAK;AAChB,cAAM,KAAK,MAAM,IAAI,WAAW,GAAG;AAEnC,YAAI,QAAQ,EAAE,GAAG;AACf,cAAI,OAAO,GAAM;AACf,sBAAU,KAAK,SAAS,MAAM,QAAQ,QAAQ,KAAK,YAAY,IAAI,MAAM;AAAA,UAC3E,OAAO;AACL;AAAA,UACF;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAEA;AAAA,MACF;AAEA,sBAAgB,OAAO;AAEvB,iBAAW,KAAK,MAAM,QAAQ,QAAQ,CAAC;AACvC,YAAM,QAAQ,QAAQ,IAAI,MAAM,OAAO,QAAQ,IAAI,KAAK,mBAAmB,IAAI;AAE/E,gBAAU,KAAK,MAAM,OAAO,QAAQ,CAAC;AACrC,YAAM,OAAO,QAAQ,IAAI,SAAS;AAElC,gBAAU,KAAK,MAAM,OAAO,QAAQ,CAAC;AACrC,YAAM,OAAO,QAAQ,IAAI,MAAM,MAAM,OAAO,QAAQ;AACpD;AAAA,IACF;AAGA,QAAI,eAAe;AAAE;AAAA,IAAM;AAG3B,QAAI,YAAY;AAChB,aAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,IAAI,GAAG,KAAK;AACtD,UAAI,gBAAgB,CAAC,EAAE,OAAO,UAAU,SAAS,IAAI,GAAG;AACtD,oBAAY;AACZ;AAAA,MACF;AAAA,IACF;AAEA,QAAI,WAAW;AAKb,YAAM,UAAU;AAEhB,UAAI,MAAM,cAAc,GAAG;AAIzB,kBAAU,KAAK,MAAM,OAAO,QAAQ,CAAC;AACrC,mBAAW,KAAK,MAAM,QAAQ,QAAQ,CAAC;AACvC,kBAAU,KAAK,MAAM,OAAO,QAAQ,CAAC;AACrC,kBAAU,KAAK,MAAM,OAAO,QAAQ,CAAC;AACrC,cAAM,OAAO,QAAQ,KAAK,MAAM;AAAA,MAClC;AAEA;AAAA,IACF;AAEA,cAAU,KAAK,MAAM,OAAO,QAAQ,CAAC;AACrC,eAAW,KAAK,MAAM,QAAQ,QAAQ,CAAC;AACvC,cAAU,KAAK,MAAM,OAAO,QAAQ,CAAC;AACrC,cAAU,KAAK,MAAM,OAAO,QAAQ,CAAC;AAIrC,UAAM,OAAO,QAAQ,IAAI;AAAA,EAC3B;AAEA,QAAM,YAAY,MAAM;AACxB,QAAM,YAAY;AAElB,QAAM,UAAW,MAAM,KAAK,mBAAmB,cAAc,CAAC;AAC9D,UAAQ,SAAS;AACjB,QAAM,QAAQ,CAAC,WAAW,CAAC;AAC3B,UAAQ,MAAS;AAEjB,QAAM,GAAG,MAAM,SAAS,OAAO,WAAW,QAAQ;AAElD,QAAM,UAAW,MAAM,KAAK,oBAAoB,cAAc,EAAE;AAChE,UAAQ,SAAS;AAEjB,QAAM,UAAU;AAChB,QAAM,aAAa;AACnB,QAAM,CAAC,IAAI,MAAM;AAIjB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAM,OAAO,IAAI,SAAS,IAAI,UAAU,CAAC;AACzC,UAAM,OAAO,IAAI,SAAS,IAAI,UAAU,CAAC;AACzC,UAAM,OAAO,IAAI,SAAS,IAAI,UAAU,CAAC;AACzC,UAAM,QAAQ,IAAI,SAAS,IAAI,WAAW,CAAC;AAAA,EAC7C;AACA,QAAM,YAAY;AAElB,SAAO;AACT;;;AC5Me,SAAR,GAAqB,OAAO,WAAW,SAAS,QAAQ;AAC7D,QAAM,MAAM,MAAM,OAAO,SAAS;AAElC,MAAI,MAAM,OAAO,SAAS,IAAI,MAAM,aAAa,GAAG;AAAE,WAAO;AAAA,EAAM;AAEnE,MAAI,MAAM,MAAM,OAAO,SAAS,IAAI,MAAM,OAAO,SAAS;AAC1D,QAAM,SAAS,MAAM,IAAI,WAAW,KAAK;AAGzC,MAAI,WAAW,MACX,WAAW,MACX,WAAW,IAAa;AAC1B,WAAO;AAAA,EACT;AAIA,MAAI,MAAM;AACV,SAAO,MAAM,KAAK;AAChB,UAAM,KAAK,MAAM,IAAI,WAAW,KAAK;AACrC,QAAI,OAAO,UAAU,CAAC,QAAQ,EAAE,GAAG;AAAE,aAAO;AAAA,IAAM;AAClD,QAAI,OAAO,QAAQ;AAAE;AAAA,IAAM;AAAA,EAC7B;AAEA,MAAI,MAAM,GAAG;AAAE,WAAO;AAAA,EAAM;AAE5B,MAAI,QAAQ;AAAE,WAAO;AAAA,EAAK;AAE1B,QAAM,OAAO,YAAY;AAEzB,QAAM,QAAS,MAAM,KAAK,MAAM,MAAM,CAAC;AACvC,QAAM,MAAS,CAAC,WAAW,MAAM,IAAI;AACrC,QAAM,SAAS,MAAM,MAAM,CAAC,EAAE,KAAK,OAAO,aAAa,MAAM,CAAC;AAE9D,SAAO;AACT;;;ACjCA,SAAS,qBAAsB,OAAO,WAAW;AAC/C,QAAM,MAAM,MAAM,OAAO,SAAS;AAClC,MAAI,MAAM,MAAM,OAAO,SAAS,IAAI,MAAM,OAAO,SAAS;AAE1D,QAAM,SAAS,MAAM,IAAI,WAAW,KAAK;AAEzC,MAAI,WAAW,MACX,WAAW,MACX,WAAW,IAAa;AAC1B,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,KAAK;AACb,UAAM,KAAK,MAAM,IAAI,WAAW,GAAG;AAEnC,QAAI,CAAC,QAAQ,EAAE,GAAG;AAEhB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAIA,SAAS,sBAAuB,OAAO,WAAW;AAChD,QAAM,QAAQ,MAAM,OAAO,SAAS,IAAI,MAAM,OAAO,SAAS;AAC9D,QAAM,MAAM,MAAM,OAAO,SAAS;AAClC,MAAI,MAAM;AAGV,MAAI,MAAM,KAAK,KAAK;AAAE,WAAO;AAAA,EAAG;AAEhC,MAAI,KAAK,MAAM,IAAI,WAAW,KAAK;AAEnC,MAAI,KAAK,MAAe,KAAK,IAAa;AAAE,WAAO;AAAA,EAAG;AAEtD,aAAS;AAEP,QAAI,OAAO,KAAK;AAAE,aAAO;AAAA,IAAG;AAE5B,SAAK,MAAM,IAAI,WAAW,KAAK;AAE/B,QAAI,MAAM,MAAe,MAAM,IAAa;AAG1C,UAAI,MAAM,SAAS,IAAI;AAAE,eAAO;AAAA,MAAG;AAEnC;AAAA,IACF;AAGA,QAAI,OAAO,MAAe,OAAO,IAAa;AAC5C;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,KAAK;AACb,SAAK,MAAM,IAAI,WAAW,GAAG;AAE7B,QAAI,CAAC,QAAQ,EAAE,GAAG;AAEhB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,oBAAqB,OAAO,KAAK;AACxC,QAAM,QAAQ,MAAM,QAAQ;AAE5B,WAAS,IAAI,MAAM,GAAG,IAAI,MAAM,OAAO,SAAS,GAAG,IAAI,GAAG,KAAK;AAC7D,QAAI,MAAM,OAAO,CAAC,EAAE,UAAU,SAAS,MAAM,OAAO,CAAC,EAAE,SAAS,kBAAkB;AAChF,YAAM,OAAO,IAAI,CAAC,EAAE,SAAS;AAC7B,YAAM,OAAO,CAAC,EAAE,SAAS;AACzB,WAAK;AAAA,IACP;AAAA,EACF;AACF;AAEe,SAAR,KAAuB,OAAO,WAAW,SAAS,QAAQ;AAC/D,MAAI,KAAK,KAAK,OAAO;AACrB,MAAI,WAAW;AACf,MAAI,QAAQ;AAGZ,MAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,aAAa,GAAG;AAAE,WAAO;AAAA,EAAM;AAQlE,MAAI,MAAM,cAAc,KACpB,MAAM,OAAO,QAAQ,IAAI,MAAM,cAAc,KAC7C,MAAM,OAAO,QAAQ,IAAI,MAAM,WAAW;AAC5C,WAAO;AAAA,EACT;AAEA,MAAI,yBAAyB;AAI7B,MAAI,UAAU,MAAM,eAAe,aAAa;AAM9C,QAAI,MAAM,OAAO,QAAQ,KAAK,MAAM,WAAW;AAC7C,+BAAyB;AAAA,IAC3B;AAAA,EACF;AAGA,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,OAAK,iBAAiB,sBAAsB,OAAO,QAAQ,MAAM,GAAG;AAClE,gBAAY;AACZ,YAAQ,MAAM,OAAO,QAAQ,IAAI,MAAM,OAAO,QAAQ;AACtD,kBAAc,OAAO,MAAM,IAAI,MAAM,OAAO,iBAAiB,CAAC,CAAC;AAI/D,QAAI,0BAA0B,gBAAgB,EAAG,QAAO;AAAA,EAC1D,YAAY,iBAAiB,qBAAqB,OAAO,QAAQ,MAAM,GAAG;AACxE,gBAAY;AAAA,EACd,OAAO;AACL,WAAO;AAAA,EACT;AAIA,MAAI,wBAAwB;AAC1B,QAAI,MAAM,WAAW,cAAc,KAAK,MAAM,OAAO,QAAQ,EAAG,QAAO;AAAA,EACzE;AAGA,MAAI,QAAQ;AAAE,WAAO;AAAA,EAAK;AAG1B,QAAM,iBAAiB,MAAM,IAAI,WAAW,iBAAiB,CAAC;AAG9D,QAAM,aAAa,MAAM,OAAO;AAEhC,MAAI,WAAW;AACb,YAAc,MAAM,KAAK,qBAAqB,MAAM,CAAC;AACrD,QAAI,gBAAgB,GAAG;AACrB,YAAM,QAAQ,CAAC,CAAC,SAAS,WAAW,CAAC;AAAA,IACvC;AAAA,EACF,OAAO;AACL,YAAc,MAAM,KAAK,oBAAoB,MAAM,CAAC;AAAA,EACtD;AAEA,QAAM,YAAY,CAAC,UAAU,CAAC;AAC9B,QAAM,MAAS;AACf,QAAM,SAAS,OAAO,aAAa,cAAc;AAMjD,MAAI,eAAe;AACnB,QAAM,kBAAkB,MAAM,GAAG,MAAM,MAAM,SAAS,MAAM;AAE5D,QAAM,gBAAgB,MAAM;AAC5B,QAAM,aAAa;AAEnB,SAAO,WAAW,SAAS;AACzB,UAAM;AACN,UAAM,MAAM,OAAO,QAAQ;AAE3B,UAAM,UAAU,MAAM,OAAO,QAAQ,IAAI,kBAAkB,MAAM,OAAO,QAAQ,IAAI,MAAM,OAAO,QAAQ;AACzG,QAAI,SAAS;AAEb,WAAO,MAAM,KAAK;AAChB,YAAM,KAAK,MAAM,IAAI,WAAW,GAAG;AAEnC,UAAI,OAAO,GAAM;AACf,kBAAU,KAAK,SAAS,MAAM,QAAQ,QAAQ,KAAK;AAAA,MACrD,WAAW,OAAO,IAAM;AACtB;AAAA,MACF,OAAO;AACL;AAAA,MACF;AAEA;AAAA,IACF;AAEA,UAAM,eAAe;AACrB,QAAI;AAEJ,QAAI,gBAAgB,KAAK;AAEvB,0BAAoB;AAAA,IACtB,OAAO;AACL,0BAAoB,SAAS;AAAA,IAC/B;AAIA,QAAI,oBAAoB,GAAG;AAAE,0BAAoB;AAAA,IAAE;AAInD,UAAM,SAAS,UAAU;AAGzB,YAAe,MAAM,KAAK,kBAAkB,MAAM,CAAC;AACnD,UAAM,SAAS,OAAO,aAAa,cAAc;AACjD,UAAM,YAAY,CAAC,UAAU,CAAC;AAC9B,UAAM,MAAS;AACf,QAAI,WAAW;AACb,YAAM,OAAO,MAAM,IAAI,MAAM,OAAO,iBAAiB,CAAC;AAAA,IACxD;AAGA,UAAM,WAAW,MAAM;AACvB,UAAM,YAAY,MAAM,OAAO,QAAQ;AACvC,UAAM,YAAY,MAAM,OAAO,QAAQ;AAMvC,UAAM,gBAAgB,MAAM;AAC5B,UAAM,aAAa,MAAM;AACzB,UAAM,YAAY;AAElB,UAAM,QAAQ;AACd,UAAM,OAAO,QAAQ,IAAI,eAAe,MAAM,OAAO,QAAQ;AAC7D,UAAM,OAAO,QAAQ,IAAI;AAEzB,QAAI,gBAAgB,OAAO,MAAM,QAAQ,WAAW,CAAC,GAAG;AAQtD,YAAM,OAAO,KAAK,IAAI,MAAM,OAAO,GAAG,OAAO;AAAA,IAC/C,OAAO;AACL,YAAM,GAAG,MAAM,SAAS,OAAO,UAAU,SAAS,IAAI;AAAA,IACxD;AAGA,QAAI,CAAC,MAAM,SAAS,cAAc;AAChC,cAAQ;AAAA,IACV;AAGA,mBAAgB,MAAM,OAAO,WAAY,KAAK,MAAM,QAAQ,MAAM,OAAO,CAAC;AAE1E,UAAM,YAAY,MAAM;AACxB,UAAM,aAAa;AACnB,UAAM,OAAO,QAAQ,IAAI;AACzB,UAAM,OAAO,QAAQ,IAAI;AACzB,UAAM,QAAQ;AAEd,YAAe,MAAM,KAAK,mBAAmB,MAAM,EAAE;AACrD,UAAM,SAAS,OAAO,aAAa,cAAc;AAEjD,eAAW,MAAM;AACjB,cAAU,CAAC,IAAI;AAEf,QAAI,YAAY,SAAS;AAAE;AAAA,IAAM;AAKjC,QAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,WAAW;AAAE;AAAA,IAAM;AAGtD,QAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,aAAa,GAAG;AAAE;AAAA,IAAM;AAG3D,QAAI,YAAY;AAChB,aAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,IAAI,GAAG,KAAK;AACtD,UAAI,gBAAgB,CAAC,EAAE,OAAO,UAAU,SAAS,IAAI,GAAG;AACtD,oBAAY;AACZ;AAAA,MACF;AAAA,IACF;AACA,QAAI,WAAW;AAAE;AAAA,IAAM;AAGvB,QAAI,WAAW;AACb,uBAAiB,sBAAsB,OAAO,QAAQ;AACtD,UAAI,iBAAiB,GAAG;AAAE;AAAA,MAAM;AAChC,cAAQ,MAAM,OAAO,QAAQ,IAAI,MAAM,OAAO,QAAQ;AAAA,IACxD,OAAO;AACL,uBAAiB,qBAAqB,OAAO,QAAQ;AACrD,UAAI,iBAAiB,GAAG;AAAE;AAAA,MAAM;AAAA,IAClC;AAEA,QAAI,mBAAmB,MAAM,IAAI,WAAW,iBAAiB,CAAC,GAAG;AAAE;AAAA,IAAM;AAAA,EAC3E;AAGA,MAAI,WAAW;AACb,YAAQ,MAAM,KAAK,sBAAsB,MAAM,EAAE;AAAA,EACnD,OAAO;AACL,YAAQ,MAAM,KAAK,qBAAqB,MAAM,EAAE;AAAA,EAClD;AACA,QAAM,SAAS,OAAO,aAAa,cAAc;AAEjD,YAAU,CAAC,IAAI;AACf,QAAM,OAAO;AAEb,QAAM,aAAa;AAGnB,MAAI,OAAO;AACT,wBAAoB,OAAO,UAAU;AAAA,EACvC;AAEA,SAAO;AACT;;;ACxUe,SAAR,UAA4B,OAAO,WAAW,UAAU,QAAQ;AACrE,MAAI,MAAM,MAAM,OAAO,SAAS,IAAI,MAAM,OAAO,SAAS;AAC1D,MAAI,MAAM,MAAM,OAAO,SAAS;AAChC,MAAI,WAAW,YAAY;AAG3B,MAAI,MAAM,OAAO,SAAS,IAAI,MAAM,aAAa,GAAG;AAAE,WAAO;AAAA,EAAM;AAEnE,MAAI,MAAM,IAAI,WAAW,GAAG,MAAM,IAAa;AAAE,WAAO;AAAA,EAAM;AAE9D,WAAS,YAAaC,WAAU;AAC9B,UAAM,UAAU,MAAM;AAEtB,QAAIA,aAAY,WAAW,MAAM,QAAQA,SAAQ,GAAG;AAElD,aAAO;AAAA,IACT;AAEA,QAAI,iBAAiB;AAIrB,QAAI,MAAM,OAAOA,SAAQ,IAAI,MAAM,YAAY,GAAG;AAAE,uBAAiB;AAAA,IAAK;AAG1E,QAAI,MAAM,OAAOA,SAAQ,IAAI,GAAG;AAAE,uBAAiB;AAAA,IAAK;AAExD,QAAI,CAAC,gBAAgB;AACnB,YAAM,kBAAkB,MAAM,GAAG,MAAM,MAAM,SAAS,WAAW;AACjE,YAAM,gBAAgB,MAAM;AAC5B,YAAM,aAAa;AAGnB,UAAI,YAAY;AAChB,eAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,IAAI,GAAG,KAAK;AACtD,YAAI,gBAAgB,CAAC,EAAE,OAAOA,WAAU,SAAS,IAAI,GAAG;AACtD,sBAAY;AACZ;AAAA,QACF;AAAA,MACF;AAEA,YAAM,aAAa;AACnB,UAAI,WAAW;AAEb,eAAO;AAAA,MACT;AAAA,IACF;AAEA,UAAMC,OAAM,MAAM,OAAOD,SAAQ,IAAI,MAAM,OAAOA,SAAQ;AAC1D,UAAME,OAAM,MAAM,OAAOF,SAAQ;AAGjC,WAAO,MAAM,IAAI,MAAMC,MAAKC,OAAM,CAAC;AAAA,EACrC;AAEA,MAAI,MAAM,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC;AAEtC,QAAM,IAAI;AACV,MAAI,WAAW;AAEf,OAAK,MAAM,GAAG,MAAM,KAAK,OAAO;AAC9B,UAAM,KAAK,IAAI,WAAW,GAAG;AAC7B,QAAI,OAAO,IAAc;AACvB,aAAO;AAAA,IACT,WAAW,OAAO,IAAc;AAC9B,iBAAW;AACX;AAAA,IACF,WAAW,OAAO,IAAe;AAC/B,YAAM,cAAc,YAAY,QAAQ;AACxC,UAAI,gBAAgB,MAAM;AACxB,eAAO;AACP,cAAM,IAAI;AACV;AAAA,MACF;AAAA,IACF,WAAW,OAAO,IAAc;AAC9B;AACA,UAAI,MAAM,OAAO,IAAI,WAAW,GAAG,MAAM,IAAM;AAC7C,cAAM,cAAc,YAAY,QAAQ;AACxC,YAAI,gBAAgB,MAAM;AACxB,iBAAO;AACP,gBAAM,IAAI;AACV;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,WAAW,KAAK,IAAI,WAAW,WAAW,CAAC,MAAM,IAAa;AAAE,WAAO;AAAA,EAAM;AAIjF,OAAK,MAAM,WAAW,GAAG,MAAM,KAAK,OAAO;AACzC,UAAM,KAAK,IAAI,WAAW,GAAG;AAC7B,QAAI,OAAO,IAAM;AACf,YAAM,cAAc,YAAY,QAAQ;AACxC,UAAI,gBAAgB,MAAM;AACxB,eAAO;AACP,cAAM,IAAI;AACV;AAAA,MACF;AAAA,IACF,WAAW,QAAQ,EAAE,GAAG;AAAA,IAExB,OAAO;AACL;AAAA,IACF;AAAA,EACF;AAIA,QAAM,UAAU,MAAM,GAAG,QAAQ,qBAAqB,KAAK,KAAK,GAAG;AACnE,MAAI,CAAC,QAAQ,IAAI;AAAE,WAAO;AAAA,EAAM;AAEhC,QAAM,OAAO,MAAM,GAAG,cAAc,QAAQ,GAAG;AAC/C,MAAI,CAAC,MAAM,GAAG,aAAa,IAAI,GAAG;AAAE,WAAO;AAAA,EAAM;AAEjD,QAAM,QAAQ;AAGd,QAAM,aAAa;AACnB,QAAM,gBAAgB;AAItB,QAAM,QAAQ;AACd,SAAO,MAAM,KAAK,OAAO;AACvB,UAAM,KAAK,IAAI,WAAW,GAAG;AAC7B,QAAI,OAAO,IAAM;AACf,YAAM,cAAc,YAAY,QAAQ;AACxC,UAAI,gBAAgB,MAAM;AACxB,eAAO;AACP,cAAM,IAAI;AACV;AAAA,MACF;AAAA,IACF,WAAW,QAAQ,EAAE,GAAG;AAAA,IAExB,OAAO;AACL;AAAA,IACF;AAAA,EACF;AAIA,MAAI,WAAW,MAAM,GAAG,QAAQ,eAAe,KAAK,KAAK,GAAG;AAC5D,SAAO,SAAS,cAAc;AAC5B,UAAM,cAAc,YAAY,QAAQ;AACxC,QAAI,gBAAgB,KAAM;AAC1B,WAAO;AACP,UAAM;AACN,UAAM,IAAI;AACV;AACA,eAAW,MAAM,GAAG,QAAQ,eAAe,KAAK,KAAK,KAAK,QAAQ;AAAA,EACpE;AACA,MAAI;AAEJ,MAAI,MAAM,OAAO,UAAU,OAAO,SAAS,IAAI;AAC7C,YAAQ,SAAS;AACjB,UAAM,SAAS;AAAA,EACjB,OAAO;AACL,YAAQ;AACR,UAAM;AACN,eAAW;AAAA,EACb;AAGA,SAAO,MAAM,KAAK;AAChB,UAAM,KAAK,IAAI,WAAW,GAAG;AAC7B,QAAI,CAAC,QAAQ,EAAE,GAAG;AAAE;AAAA,IAAM;AAC1B;AAAA,EACF;AAEA,MAAI,MAAM,OAAO,IAAI,WAAW,GAAG,MAAM,IAAM;AAC7C,QAAI,OAAO;AAGT,cAAQ;AACR,YAAM;AACN,iBAAW;AACX,aAAO,MAAM,KAAK;AAChB,cAAM,KAAK,IAAI,WAAW,GAAG;AAC7B,YAAI,CAAC,QAAQ,EAAE,GAAG;AAAE;AAAA,QAAM;AAC1B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,MAAM,OAAO,IAAI,WAAW,GAAG,MAAM,IAAM;AAE7C,WAAO;AAAA,EACT;AAEA,QAAM,QAAQ,mBAAmB,IAAI,MAAM,GAAG,QAAQ,CAAC;AACvD,MAAI,CAAC,OAAO;AAEV,WAAO;AAAA,EACT;AAIA,MAAI,QAAQ;AAAE,WAAO;AAAA,EAAK;AAE1B,MAAI,OAAO,MAAM,IAAI,eAAe,aAAa;AAC/C,UAAM,IAAI,aAAa,CAAC;AAAA,EAC1B;AACA,MAAI,OAAO,MAAM,IAAI,WAAW,KAAK,MAAM,aAAa;AACtD,UAAM,IAAI,WAAW,KAAK,IAAI,EAAE,OAAO,KAAK;AAAA,EAC9C;AAEA,QAAM,OAAO;AACb,SAAO;AACT;;;AChNA,IAAO,sBAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;AChEA,IAAM,YAAgB;AAEtB,IAAM,WAAgB;AACtB,IAAM,gBAAgB;AACtB,IAAM,gBAAgB;AAEtB,IAAM,aAAc,QAAQ,WAAW,MAAM,gBAAgB,MAAM,gBAAgB;AAEnF,IAAM,YAAc,YAAY,YAAY,iBAAiB,aAAa;AAE1E,IAAM,WAAc,6BAA6B,YAAY;AAE7D,IAAM,YAAc;AACpB,IAAM,UAAc;AACpB,IAAM,aAAc;AACpB,IAAM,cAAc;AACpB,IAAM,QAAc;AAEpB,IAAM,cAAc,IAAI,OAAO,SAAS,WAAW,MAAM,YAAY,MAAM,UACnD,MAAM,aAAa,MAAM,cAAc,MAAM,QAAQ,GAAG;AAChF,IAAM,yBAAyB,IAAI,OAAO,SAAS,WAAW,MAAM,YAAY,GAAG;;;ACdnF,IAAM,iBAAiB;AAAA,EACrB,CAAC,8CAA8C,oCAAoC,IAAI;AAAA,EACvF,CAAC,SAAgB,OAAS,IAAI;AAAA,EAC9B,CAAC,QAAgB,OAAS,IAAI;AAAA,EAC9B,CAAC,YAAgB,KAAS,IAAI;AAAA,EAC9B,CAAC,gBAAgB,SAAS,IAAI;AAAA,EAC9B,CAAC,IAAI,OAAO,UAAU,oBAAY,KAAK,GAAG,IAAI,oBAAoB,GAAG,GAAG,MAAM,IAAI;AAAA,EAClF,CAAC,IAAI,OAAO,uBAAuB,SAAS,OAAO,GAAI,MAAM,KAAK;AACpE;AAEe,SAAR,WAA6B,OAAO,WAAW,SAAS,QAAQ;AACrE,MAAI,MAAM,MAAM,OAAO,SAAS,IAAI,MAAM,OAAO,SAAS;AAC1D,MAAI,MAAM,MAAM,OAAO,SAAS;AAGhC,MAAI,MAAM,OAAO,SAAS,IAAI,MAAM,aAAa,GAAG;AAAE,WAAO;AAAA,EAAM;AAEnE,MAAI,CAAC,MAAM,GAAG,QAAQ,MAAM;AAAE,WAAO;AAAA,EAAM;AAE3C,MAAI,MAAM,IAAI,WAAW,GAAG,MAAM,IAAa;AAAE,WAAO;AAAA,EAAM;AAE9D,MAAI,WAAW,MAAM,IAAI,MAAM,KAAK,GAAG;AAEvC,MAAI,IAAI;AACR,SAAO,IAAI,eAAe,QAAQ,KAAK;AACrC,QAAI,eAAe,CAAC,EAAE,CAAC,EAAE,KAAK,QAAQ,GAAG;AAAE;AAAA,IAAM;AAAA,EACnD;AACA,MAAI,MAAM,eAAe,QAAQ;AAAE,WAAO;AAAA,EAAM;AAEhD,MAAI,QAAQ;AAEV,WAAO,eAAe,CAAC,EAAE,CAAC;AAAA,EAC5B;AAEA,MAAI,WAAW,YAAY;AAI3B,MAAI,CAAC,eAAe,CAAC,EAAE,CAAC,EAAE,KAAK,QAAQ,GAAG;AACxC,WAAO,WAAW,SAAS,YAAY;AACrC,UAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,WAAW;AAAE;AAAA,MAAM;AAEtD,YAAM,MAAM,OAAO,QAAQ,IAAI,MAAM,OAAO,QAAQ;AACpD,YAAM,MAAM,OAAO,QAAQ;AAC3B,iBAAW,MAAM,IAAI,MAAM,KAAK,GAAG;AAEnC,UAAI,eAAe,CAAC,EAAE,CAAC,EAAE,KAAK,QAAQ,GAAG;AACvC,YAAI,SAAS,WAAW,GAAG;AAAE;AAAA,QAAW;AACxC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,QAAM,OAAO;AAEb,QAAM,QAAU,MAAM,KAAK,cAAc,IAAI,CAAC;AAC9C,QAAM,MAAU,CAAC,WAAW,QAAQ;AACpC,QAAM,UAAU,MAAM,SAAS,WAAW,UAAU,MAAM,WAAW,IAAI;AAEzE,SAAO;AACT;;;AChEe,SAAR,QAA0B,OAAO,WAAW,SAAS,QAAQ;AAClE,MAAI,MAAM,MAAM,OAAO,SAAS,IAAI,MAAM,OAAO,SAAS;AAC1D,MAAI,MAAM,MAAM,OAAO,SAAS;AAGhC,MAAI,MAAM,OAAO,SAAS,IAAI,MAAM,aAAa,GAAG;AAAE,WAAO;AAAA,EAAM;AAEnE,MAAI,KAAM,MAAM,IAAI,WAAW,GAAG;AAElC,MAAI,OAAO,MAAe,OAAO,KAAK;AAAE,WAAO;AAAA,EAAM;AAGrD,MAAI,QAAQ;AACZ,OAAK,MAAM,IAAI,WAAW,EAAE,GAAG;AAC/B,SAAO,OAAO,MAAe,MAAM,OAAO,SAAS,GAAG;AACpD;AACA,SAAK,MAAM,IAAI,WAAW,EAAE,GAAG;AAAA,EACjC;AAEA,MAAI,QAAQ,KAAM,MAAM,OAAO,CAAC,QAAQ,EAAE,GAAI;AAAE,WAAO;AAAA,EAAM;AAE7D,MAAI,QAAQ;AAAE,WAAO;AAAA,EAAK;AAI1B,QAAM,MAAM,eAAe,KAAK,GAAG;AACnC,QAAM,MAAM,MAAM,cAAc,KAAK,IAAM,GAAG;AAC9C,MAAI,MAAM,OAAO,QAAQ,MAAM,IAAI,WAAW,MAAM,CAAC,CAAC,GAAG;AACvD,UAAM;AAAA,EACR;AAEA,QAAM,OAAO,YAAY;AAEzB,QAAM,UAAW,MAAM,KAAK,gBAAgB,MAAM,OAAO,KAAK,GAAG,CAAC;AAClE,UAAQ,SAAS,WAAW,MAAM,GAAG,KAAK;AAC1C,UAAQ,MAAS,CAAC,WAAW,MAAM,IAAI;AAEvC,QAAM,UAAa,MAAM,KAAK,UAAU,IAAI,CAAC;AAC7C,UAAQ,UAAW,MAAM,IAAI,MAAM,KAAK,GAAG,EAAE,KAAK;AAClD,UAAQ,MAAW,CAAC,WAAW,MAAM,IAAI;AACzC,UAAQ,WAAW,CAAC;AAEpB,QAAM,UAAW,MAAM,KAAK,iBAAiB,MAAM,OAAO,KAAK,GAAG,EAAE;AACpE,UAAQ,SAAS,WAAW,MAAM,GAAG,KAAK;AAE1C,SAAO;AACT;;;AChDe,SAAR,SAA2B,OAAO,WAAW,SAAsB;AACxE,QAAM,kBAAkB,MAAM,GAAG,MAAM,MAAM,SAAS,WAAW;AAGjE,MAAI,MAAM,OAAO,SAAS,IAAI,MAAM,aAAa,GAAG;AAAE,WAAO;AAAA,EAAM;AAEnE,QAAM,gBAAgB,MAAM;AAC5B,QAAM,aAAa;AAGnB,MAAI,QAAQ;AACZ,MAAI;AACJ,MAAI,WAAW,YAAY;AAE3B,SAAO,WAAW,WAAW,CAAC,MAAM,QAAQ,QAAQ,GAAG,YAAY;AAGjE,QAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,YAAY,GAAG;AAAE;AAAA,IAAS;AAK7D,QAAI,MAAM,OAAO,QAAQ,KAAK,MAAM,WAAW;AAC7C,UAAI,MAAM,MAAM,OAAO,QAAQ,IAAI,MAAM,OAAO,QAAQ;AACxD,YAAM,MAAM,MAAM,OAAO,QAAQ;AAEjC,UAAI,MAAM,KAAK;AACb,iBAAS,MAAM,IAAI,WAAW,GAAG;AAEjC,YAAI,WAAW,MAAe,WAAW,IAAa;AACpD,gBAAM,MAAM,UAAU,KAAK,MAAM;AACjC,gBAAM,MAAM,WAAW,GAAG;AAE1B,cAAI,OAAO,KAAK;AACd,oBAAS,WAAW,KAAc,IAAI;AACtC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,QAAI,MAAM,OAAO,QAAQ,IAAI,GAAG;AAAE;AAAA,IAAS;AAG3C,QAAI,YAAY;AAChB,aAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,IAAI,GAAG,KAAK;AACtD,UAAI,gBAAgB,CAAC,EAAE,OAAO,UAAU,SAAS,IAAI,GAAG;AACtD,oBAAY;AACZ;AAAA,MACF;AAAA,IACF;AACA,QAAI,WAAW;AAAE;AAAA,IAAM;AAAA,EACzB;AAEA,MAAI,CAAC,OAAO;AAEV,WAAO;AAAA,EACT;AAEA,QAAM,UAAU,MAAM,SAAS,WAAW,UAAU,MAAM,WAAW,KAAK,EAAE,KAAK;AAEjF,QAAM,OAAO,WAAW;AAExB,QAAM,UAAa,MAAM,KAAK,gBAAgB,MAAM,OAAO,KAAK,GAAG,CAAC;AACpE,UAAQ,SAAW,OAAO,aAAa,MAAM;AAC7C,UAAQ,MAAW,CAAC,WAAW,MAAM,IAAI;AAEzC,QAAM,UAAa,MAAM,KAAK,UAAU,IAAI,CAAC;AAC7C,UAAQ,UAAW;AACnB,UAAQ,MAAW,CAAC,WAAW,MAAM,OAAO,CAAC;AAC7C,UAAQ,WAAW,CAAC;AAEpB,QAAM,UAAa,MAAM,KAAK,iBAAiB,MAAM,OAAO,KAAK,GAAG,EAAE;AACtE,UAAQ,SAAW,OAAO,aAAa,MAAM;AAE7C,QAAM,aAAa;AAEnB,SAAO;AACT;;;AC/Ee,SAAR,UAA4B,OAAO,WAAW,SAAS;AAC5D,QAAM,kBAAkB,MAAM,GAAG,MAAM,MAAM,SAAS,WAAW;AACjE,QAAM,gBAAgB,MAAM;AAC5B,MAAI,WAAW,YAAY;AAC3B,QAAM,aAAa;AAGnB,SAAO,WAAW,WAAW,CAAC,MAAM,QAAQ,QAAQ,GAAG,YAAY;AAGjE,QAAI,MAAM,OAAO,QAAQ,IAAI,MAAM,YAAY,GAAG;AAAE;AAAA,IAAS;AAG7D,QAAI,MAAM,OAAO,QAAQ,IAAI,GAAG;AAAE;AAAA,IAAS;AAG3C,QAAI,YAAY;AAChB,aAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,IAAI,GAAG,KAAK;AACtD,UAAI,gBAAgB,CAAC,EAAE,OAAO,UAAU,SAAS,IAAI,GAAG;AACtD,oBAAY;AACZ;AAAA,MACF;AAAA,IACF;AACA,QAAI,WAAW;AAAE;AAAA,IAAM;AAAA,EACzB;AAEA,QAAM,UAAU,MAAM,SAAS,WAAW,UAAU,MAAM,WAAW,KAAK,EAAE,KAAK;AAEjF,QAAM,OAAO;AAEb,QAAM,UAAa,MAAM,KAAK,kBAAkB,KAAK,CAAC;AACtD,UAAQ,MAAW,CAAC,WAAW,MAAM,IAAI;AAEzC,QAAM,UAAa,MAAM,KAAK,UAAU,IAAI,CAAC;AAC7C,UAAQ,UAAW;AACnB,UAAQ,MAAW,CAAC,WAAW,MAAM,IAAI;AACzC,UAAQ,WAAW,CAAC;AAEpB,QAAM,KAAK,mBAAmB,KAAK,EAAE;AAErC,QAAM,aAAa;AAEnB,SAAO;AACT;;;ACxBA,IAAMC,UAAS;AAAA;AAAA;AAAA,EAGb,CAAC,SAAc,OAAc,CAAC,aAAa,WAAW,CAAC;AAAA,EACvD,CAAC,QAAc,IAAM;AAAA,EACrB,CAAC,SAAc,OAAc,CAAC,aAAa,aAAa,cAAc,MAAM,CAAC;AAAA,EAC7E,CAAC,cAAc,YAAc,CAAC,aAAa,aAAa,cAAc,MAAM,CAAC;AAAA,EAC7E,CAAC,MAAc,IAAc,CAAC,aAAa,aAAa,cAAc,MAAM,CAAC;AAAA,EAC7E,CAAC,QAAc,MAAc,CAAC,aAAa,aAAa,YAAY,CAAC;AAAA,EACrE,CAAC,aAAc,SAAW;AAAA,EAC1B,CAAC,cAAc,YAAc,CAAC,aAAa,aAAa,YAAY,CAAC;AAAA,EACrE,CAAC,WAAc,SAAc,CAAC,aAAa,aAAa,YAAY,CAAC;AAAA,EACrE,CAAC,YAAc,QAAU;AAAA,EACzB,CAAC,aAAc,SAAW;AAC5B;AAKA,SAAS,cAAe;AAMtB,OAAK,QAAQ,IAAI,cAAM;AAEvB,WAAS,IAAI,GAAG,IAAIA,QAAO,QAAQ,KAAK;AACtC,SAAK,MAAM,KAAKA,QAAO,CAAC,EAAE,CAAC,GAAGA,QAAO,CAAC,EAAE,CAAC,GAAG,EAAE,MAAMA,QAAO,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC;AAAA,EACnF;AACF;AAIA,YAAY,UAAU,WAAW,SAAU,OAAO,WAAW,SAAS;AACpE,QAAM,QAAQ,KAAK,MAAM,SAAS,EAAE;AACpC,QAAM,MAAM,MAAM;AAClB,QAAM,aAAa,MAAM,GAAG,QAAQ;AACpC,MAAI,OAAO;AACX,MAAI,gBAAgB;AAEpB,SAAO,OAAO,SAAS;AACrB,UAAM,OAAO,OAAO,MAAM,eAAe,IAAI;AAC7C,QAAI,QAAQ,SAAS;AAAE;AAAA,IAAM;AAI7B,QAAI,MAAM,OAAO,IAAI,IAAI,MAAM,WAAW;AAAE;AAAA,IAAM;AAIlD,QAAI,MAAM,SAAS,YAAY;AAC7B,YAAM,OAAO;AACb;AAAA,IACF;AAQA,UAAM,WAAW,MAAM;AACvB,QAAI,KAAK;AAET,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,WAAK,MAAM,CAAC,EAAE,OAAO,MAAM,SAAS,KAAK;AACzC,UAAI,IAAI;AACN,YAAI,YAAY,MAAM,MAAM;AAC1B,gBAAM,IAAI,MAAM,wCAAwC;AAAA,QAC1D;AACA;AAAA,MACF;AAAA,IACF;AAGA,QAAI,CAAC,GAAI,OAAM,IAAI,MAAM,iCAAiC;AAI1D,UAAM,QAAQ,CAAC;AAGf,QAAI,MAAM,QAAQ,MAAM,OAAO,CAAC,GAAG;AACjC,sBAAgB;AAAA,IAClB;AAEA,WAAO,MAAM;AAEb,QAAI,OAAO,WAAW,MAAM,QAAQ,IAAI,GAAG;AACzC,sBAAgB;AAChB;AACA,YAAM,OAAO;AAAA,IACf;AAAA,EACF;AACF;AAOA,YAAY,UAAU,QAAQ,SAAU,KAAK,IAAI,KAAK,WAAW;AAC/D,MAAI,CAAC,KAAK;AAAE;AAAA,EAAO;AAEnB,QAAM,QAAQ,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,SAAS;AAEpD,OAAK,SAAS,OAAO,MAAM,MAAM,MAAM,OAAO;AAChD;AAEA,YAAY,UAAU,QAAQ;AAE9B,IAAO,uBAAQ;;;AChIf,SAAS,YAAa,KAAK,IAAI,KAAK,WAAW;AAC7C,OAAK,MAAM;AACX,OAAK,MAAM;AACX,OAAK,KAAK;AACV,OAAK,SAAS;AACd,OAAK,cAAc,MAAM,UAAU,MAAM;AAEzC,OAAK,MAAM;AACX,OAAK,SAAS,KAAK,IAAI;AACvB,OAAK,QAAQ;AACb,OAAK,UAAU;AACf,OAAK,eAAe;AAIpB,OAAK,QAAQ,CAAC;AAGd,OAAK,aAAa,CAAC;AAGnB,OAAK,mBAAmB,CAAC;AAGzB,OAAK,YAAY,CAAC;AAClB,OAAK,mBAAmB;AAIxB,OAAK,YAAY;AACnB;AAIA,YAAY,UAAU,cAAc,WAAY;AAC9C,QAAM,QAAQ,IAAI,cAAM,QAAQ,IAAI,CAAC;AACrC,QAAM,UAAU,KAAK;AACrB,QAAM,QAAQ,KAAK;AACnB,OAAK,OAAO,KAAK,KAAK;AACtB,OAAK,UAAU;AACf,SAAO;AACT;AAKA,YAAY,UAAU,OAAO,SAAU,MAAM,KAAK,SAAS;AACzD,MAAI,KAAK,SAAS;AAChB,SAAK,YAAY;AAAA,EACnB;AAEA,QAAM,QAAQ,IAAI,cAAM,MAAM,KAAK,OAAO;AAC1C,MAAI,aAAa;AAEjB,MAAI,UAAU,GAAG;AAEf,SAAK;AACL,SAAK,aAAa,KAAK,iBAAiB,IAAI;AAAA,EAC9C;AAEA,QAAM,QAAQ,KAAK;AAEnB,MAAI,UAAU,GAAG;AAEf,SAAK;AACL,SAAK,iBAAiB,KAAK,KAAK,UAAU;AAC1C,SAAK,aAAa,CAAC;AACnB,iBAAa,EAAE,YAAY,KAAK,WAAW;AAAA,EAC7C;AAEA,OAAK,eAAe,KAAK;AACzB,OAAK,OAAO,KAAK,KAAK;AACtB,OAAK,YAAY,KAAK,UAAU;AAChC,SAAO;AACT;AAQA,YAAY,UAAU,aAAa,SAAU,OAAO,cAAc;AAChE,QAAM,MAAM,KAAK;AACjB,QAAM,SAAS,KAAK,IAAI,WAAW,KAAK;AAGxC,QAAM,WAAW,QAAQ,IAAI,KAAK,IAAI,WAAW,QAAQ,CAAC,IAAI;AAE9D,MAAI,MAAM;AACV,SAAO,MAAM,OAAO,KAAK,IAAI,WAAW,GAAG,MAAM,QAAQ;AAAE;AAAA,EAAM;AAEjE,QAAM,QAAQ,MAAM;AAGpB,QAAM,WAAW,MAAM,MAAM,KAAK,IAAI,WAAW,GAAG,IAAI;AAExD,QAAM,kBAAkB,eAAe,QAAQ,KAAK,YAAY,OAAO,aAAa,QAAQ,CAAC;AAC7F,QAAM,kBAAkB,eAAe,QAAQ,KAAK,YAAY,OAAO,aAAa,QAAQ,CAAC;AAE7F,QAAM,mBAAmB,aAAa,QAAQ;AAC9C,QAAM,mBAAmB,aAAa,QAAQ;AAE9C,QAAM,gBACJ,CAAC,qBAAqB,CAAC,mBAAmB,oBAAoB;AAChE,QAAM,iBACJ,CAAC,qBAAqB,CAAC,mBAAmB,oBAAoB;AAEhE,QAAM,WAAY,kBAAmB,gBAAgB,CAAC,kBAAkB;AACxE,QAAM,YAAY,mBAAmB,gBAAgB,CAAC,iBAAkB;AAExE,SAAO,EAAE,UAAU,WAAW,QAAQ,MAAM;AAC9C;AAGA,YAAY,UAAU,QAAQ;AAE9B,IAAO,uBAAQ;;;AChHf,SAAS,iBAAkB,IAAI;AAC7B,UAAQ,IAAI;AAAA,IACV,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AAEe,SAAR,KAAuB,OAAO,QAAQ;AAC3C,MAAI,MAAM,MAAM;AAEhB,SAAO,MAAM,MAAM,UAAU,CAAC,iBAAiB,MAAM,IAAI,WAAW,GAAG,CAAC,GAAG;AACzE;AAAA,EACF;AAEA,MAAI,QAAQ,MAAM,KAAK;AAAE,WAAO;AAAA,EAAM;AAEtC,MAAI,CAAC,QAAQ;AAAE,UAAM,WAAW,MAAM,IAAI,MAAM,MAAM,KAAK,GAAG;AAAA,EAAE;AAEhE,QAAM,MAAM;AAEZ,SAAO;AACT;;;ACpDA,IAAM,YAAY;AAEH,SAARC,SAA0B,OAAO,QAAQ;AAC9C,MAAI,CAAC,MAAM,GAAG,QAAQ,QAAS,QAAO;AACtC,MAAI,MAAM,YAAY,EAAG,QAAO;AAEhC,QAAM,MAAM,MAAM;AAClB,QAAM,MAAM,MAAM;AAElB,MAAI,MAAM,IAAI,IAAK,QAAO;AAC1B,MAAI,MAAM,IAAI,WAAW,GAAG,MAAM,GAAa,QAAO;AACtD,MAAI,MAAM,IAAI,WAAW,MAAM,CAAC,MAAM,GAAa,QAAO;AAC1D,MAAI,MAAM,IAAI,WAAW,MAAM,CAAC,MAAM,GAAa,QAAO;AAE1D,QAAMC,SAAQ,MAAM,QAAQ,MAAM,SAAS;AAC3C,MAAI,CAACA,OAAO,QAAO;AAEnB,QAAM,QAAQA,OAAM,CAAC;AAErB,QAAMC,QAAO,MAAM,GAAG,QAAQ,aAAa,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,CAAC;AAC9E,MAAI,CAACA,MAAM,QAAO;AAElB,MAAI,MAAMA,MAAK;AAIf,MAAI,IAAI,UAAU,MAAM,OAAQ,QAAO;AAGvC,QAAM,IAAI,QAAQ,QAAQ,EAAE;AAE5B,QAAM,UAAU,MAAM,GAAG,cAAc,GAAG;AAC1C,MAAI,CAAC,MAAM,GAAG,aAAa,OAAO,EAAG,QAAO;AAE5C,MAAI,CAAC,QAAQ;AACX,UAAM,UAAU,MAAM,QAAQ,MAAM,GAAG,CAAC,MAAM,MAAM;AAEpD,UAAM,UAAU,MAAM,KAAK,aAAa,KAAK,CAAC;AAC9C,YAAQ,QAAQ,CAAC,CAAC,QAAQ,OAAO,CAAC;AAClC,YAAQ,SAAS;AACjB,YAAQ,OAAO;AAEf,UAAM,UAAU,MAAM,KAAK,QAAQ,IAAI,CAAC;AACxC,YAAQ,UAAU,MAAM,GAAG,kBAAkB,GAAG;AAEhD,UAAM,UAAU,MAAM,KAAK,cAAc,KAAK,EAAE;AAChD,YAAQ,SAAS;AACjB,YAAQ,OAAO;AAAA,EACjB;AAEA,QAAM,OAAO,IAAI,SAAS,MAAM;AAChC,SAAO;AACT;;;ACnDe,SAAR,QAA0B,OAAO,QAAQ;AAC9C,MAAI,MAAM,MAAM;AAEhB,MAAI,MAAM,IAAI,WAAW,GAAG,MAAM,IAAc;AAAE,WAAO;AAAA,EAAM;AAE/D,QAAM,OAAO,MAAM,QAAQ,SAAS;AACpC,QAAM,MAAM,MAAM;AAMlB,MAAI,CAAC,QAAQ;AACX,QAAI,QAAQ,KAAK,MAAM,QAAQ,WAAW,IAAI,MAAM,IAAM;AACxD,UAAI,QAAQ,KAAK,MAAM,QAAQ,WAAW,OAAO,CAAC,MAAM,IAAM;AAE5D,YAAI,KAAK,OAAO;AAChB,eAAO,MAAM,KAAK,MAAM,QAAQ,WAAW,KAAK,CAAC,MAAM,GAAM;AAE7D,cAAM,UAAU,MAAM,QAAQ,MAAM,GAAG,EAAE;AACzC,cAAM,KAAK,aAAa,MAAM,CAAC;AAAA,MACjC,OAAO;AACL,cAAM,UAAU,MAAM,QAAQ,MAAM,GAAG,EAAE;AACzC,cAAM,KAAK,aAAa,MAAM,CAAC;AAAA,MACjC;AAAA,IACF,OAAO;AACL,YAAM,KAAK,aAAa,MAAM,CAAC;AAAA,IACjC;AAAA,EACF;AAEA;AAGA,SAAO,MAAM,OAAO,QAAQ,MAAM,IAAI,WAAW,GAAG,CAAC,GAAG;AAAE;AAAA,EAAM;AAEhE,QAAM,MAAM;AACZ,SAAO;AACT;;;ACrCA,IAAM,UAAU,CAAC;AAEjB,SAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAAE,UAAQ,KAAK,CAAC;AAAE;AAEhD,qCACG,MAAM,EAAE,EAAE,QAAQ,SAAU,IAAI;AAAE,UAAQ,GAAG,WAAW,CAAC,CAAC,IAAI;AAAE,CAAC;AAErD,SAARC,QAAyB,OAAO,QAAQ;AAC7C,MAAI,MAAM,MAAM;AAChB,QAAM,MAAM,MAAM;AAElB,MAAI,MAAM,IAAI,WAAW,GAAG,MAAM,GAAa,QAAO;AACtD;AAGA,MAAI,OAAO,IAAK,QAAO;AAEvB,MAAI,MAAM,MAAM,IAAI,WAAW,GAAG;AAElC,MAAI,QAAQ,IAAM;AAChB,QAAI,CAAC,QAAQ;AACX,YAAM,KAAK,aAAa,MAAM,CAAC;AAAA,IACjC;AAEA;AAEA,WAAO,MAAM,KAAK;AAChB,YAAM,MAAM,IAAI,WAAW,GAAG;AAC9B,UAAI,CAAC,QAAQ,GAAG,EAAG;AACnB;AAAA,IACF;AAEA,UAAM,MAAM;AACZ,WAAO;AAAA,EACT;AAEA,MAAI,aAAa,MAAM,IAAI,GAAG;AAE9B,MAAI,OAAO,SAAU,OAAO,SAAU,MAAM,IAAI,KAAK;AACnD,UAAM,MAAM,MAAM,IAAI,WAAW,MAAM,CAAC;AAExC,QAAI,OAAO,SAAU,OAAO,OAAQ;AAClC,oBAAc,MAAM,IAAI,MAAM,CAAC;AAC/B;AAAA,IACF;AAAA,EACF;AAEA,QAAM,UAAU,OAAO;AAEvB,MAAI,CAAC,QAAQ;AACX,UAAM,QAAQ,MAAM,KAAK,gBAAgB,IAAI,CAAC;AAE9C,QAAI,MAAM,OAAO,QAAQ,GAAG,MAAM,GAAG;AACnC,YAAM,UAAU;AAAA,IAClB,OAAO;AACL,YAAM,UAAU;AAAA,IAClB;AAEA,UAAM,SAAS;AACf,UAAM,OAAS;AAAA,EACjB;AAEA,QAAM,MAAM,MAAM;AAClB,SAAO;AACT;;;AClEe,SAAR,SAA2B,OAAO,QAAQ;AAC/C,MAAI,MAAM,MAAM;AAChB,QAAM,KAAK,MAAM,IAAI,WAAW,GAAG;AAEnC,MAAI,OAAO,IAAa;AAAE,WAAO;AAAA,EAAM;AAEvC,QAAM,QAAQ;AACd;AACA,QAAM,MAAM,MAAM;AAGlB,SAAO,MAAM,OAAO,MAAM,IAAI,WAAW,GAAG,MAAM,IAAa;AAAE;AAAA,EAAM;AAEvE,QAAM,SAAS,MAAM,IAAI,MAAM,OAAO,GAAG;AACzC,QAAM,eAAe,OAAO;AAE5B,MAAI,MAAM,qBAAqB,MAAM,UAAU,YAAY,KAAK,MAAM,OAAO;AAC3E,QAAI,CAAC,OAAQ,OAAM,WAAW;AAC9B,UAAM,OAAO;AACb,WAAO;AAAA,EACT;AAEA,MAAI,WAAW;AACf,MAAI;AAGJ,UAAQ,aAAa,MAAM,IAAI,QAAQ,KAAK,QAAQ,OAAO,IAAI;AAC7D,eAAW,aAAa;AAGxB,WAAO,WAAW,OAAO,MAAM,IAAI,WAAW,QAAQ,MAAM,IAAa;AAAE;AAAA,IAAW;AAEtF,UAAM,eAAe,WAAW;AAEhC,QAAI,iBAAiB,cAAc;AAEjC,UAAI,CAAC,QAAQ;AACX,cAAM,QAAQ,MAAM,KAAK,eAAe,QAAQ,CAAC;AACjD,cAAM,SAAS;AACf,cAAM,UAAU,MAAM,IAAI,MAAM,KAAK,UAAU,EAC5C,QAAQ,OAAO,GAAG,EAClB,QAAQ,YAAY,IAAI;AAAA,MAC7B;AACA,YAAM,MAAM;AACZ,aAAO;AAAA,IACT;AAGA,UAAM,UAAU,YAAY,IAAI;AAAA,EAClC;AAGA,QAAM,mBAAmB;AAEzB,MAAI,CAAC,OAAQ,OAAM,WAAW;AAC9B,QAAM,OAAO;AACb,SAAO;AACT;;;ACtDA,SAAS,uBAAwB,OAAO,QAAQ;AAC9C,QAAM,QAAQ,MAAM;AACpB,QAAM,SAAS,MAAM,IAAI,WAAW,KAAK;AAEzC,MAAI,QAAQ;AAAE,WAAO;AAAA,EAAM;AAE3B,MAAI,WAAW,KAAa;AAAE,WAAO;AAAA,EAAM;AAE3C,QAAM,UAAU,MAAM,WAAW,MAAM,KAAK,IAAI;AAChD,MAAI,MAAM,QAAQ;AAClB,QAAM,KAAK,OAAO,aAAa,MAAM;AAErC,MAAI,MAAM,GAAG;AAAE,WAAO;AAAA,EAAM;AAE5B,MAAI;AAEJ,MAAI,MAAM,GAAG;AACX,YAAgB,MAAM,KAAK,QAAQ,IAAI,CAAC;AACxC,UAAM,UAAU;AAChB;AAAA,EACF;AAEA,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC/B,YAAgB,MAAM,KAAK,QAAQ,IAAI,CAAC;AACxC,UAAM,UAAU,KAAK;AAErB,UAAM,WAAW,KAAK;AAAA,MACpB;AAAA,MACA,QAAQ;AAAA;AAAA,MACR,OAAO,MAAM,OAAO,SAAS;AAAA,MAC7B,KAAK;AAAA,MACL,MAAM,QAAQ;AAAA,MACd,OAAO,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH;AAEA,QAAM,OAAO,QAAQ;AAErB,SAAO;AACT;AAEA,SAAS,YAAa,OAAO,YAAY;AACvC,MAAI;AACJ,QAAM,cAAc,CAAC;AACrB,QAAM,MAAM,WAAW;AAEvB,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,UAAM,aAAa,WAAW,CAAC;AAE/B,QAAI,WAAW,WAAW,KAAa;AACrC;AAAA,IACF;AAEA,QAAI,WAAW,QAAQ,IAAI;AACzB;AAAA,IACF;AAEA,UAAM,WAAW,WAAW,WAAW,GAAG;AAE1C,YAAgB,MAAM,OAAO,WAAW,KAAK;AAC7C,UAAM,OAAU;AAChB,UAAM,MAAU;AAChB,UAAM,UAAU;AAChB,UAAM,SAAU;AAChB,UAAM,UAAU;AAEhB,YAAgB,MAAM,OAAO,SAAS,KAAK;AAC3C,UAAM,OAAU;AAChB,UAAM,MAAU;AAChB,UAAM,UAAU;AAChB,UAAM,SAAU;AAChB,UAAM,UAAU;AAEhB,QAAI,MAAM,OAAO,SAAS,QAAQ,CAAC,EAAE,SAAS,UAC1C,MAAM,OAAO,SAAS,QAAQ,CAAC,EAAE,YAAY,KAAK;AACpD,kBAAY,KAAK,SAAS,QAAQ,CAAC;AAAA,IACrC;AAAA,EACF;AAQA,SAAO,YAAY,QAAQ;AACzB,UAAM,IAAI,YAAY,IAAI;AAC1B,QAAI,IAAI,IAAI;AAEZ,WAAO,IAAI,MAAM,OAAO,UAAU,MAAM,OAAO,CAAC,EAAE,SAAS,WAAW;AACpE;AAAA,IACF;AAEA;AAEA,QAAI,MAAM,GAAG;AACX,cAAQ,MAAM,OAAO,CAAC;AACtB,YAAM,OAAO,CAAC,IAAI,MAAM,OAAO,CAAC;AAChC,YAAM,OAAO,CAAC,IAAI;AAAA,IACpB;AAAA,EACF;AACF;AAIA,SAAS,0BAA2B,OAAO;AACzC,QAAM,cAAc,MAAM;AAC1B,QAAM,MAAM,MAAM,YAAY;AAE9B,cAAY,OAAO,MAAM,UAAU;AAEnC,WAAS,OAAO,GAAG,OAAO,KAAK,QAAQ;AACrC,QAAI,YAAY,IAAI,KAAK,YAAY,IAAI,EAAE,YAAY;AACrD,kBAAY,OAAO,YAAY,IAAI,EAAE,UAAU;AAAA,IACjD;AAAA,EACF;AACF;AAEA,IAAO,wBAAQ;AAAA,EACb,UAAU;AAAA,EACV,aAAa;AACf;;;ACzHA,SAAS,kBAAmB,OAAO,QAAQ;AACzC,QAAM,QAAQ,MAAM;AACpB,QAAM,SAAS,MAAM,IAAI,WAAW,KAAK;AAEzC,MAAI,QAAQ;AAAE,WAAO;AAAA,EAAM;AAE3B,MAAI,WAAW,MAAgB,WAAW,IAAc;AAAE,WAAO;AAAA,EAAM;AAEvE,QAAM,UAAU,MAAM,WAAW,MAAM,KAAK,WAAW,EAAI;AAE3D,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAM,QAAQ,MAAM,KAAK,QAAQ,IAAI,CAAC;AACtC,UAAM,UAAU,OAAO,aAAa,MAAM;AAE1C,UAAM,WAAW,KAAK;AAAA;AAAA;AAAA,MAGpB;AAAA;AAAA;AAAA,MAIA,QAAQ,QAAQ;AAAA;AAAA;AAAA,MAIhB,OAAO,MAAM,OAAO,SAAS;AAAA;AAAA;AAAA;AAAA,MAK7B,KAAK;AAAA;AAAA;AAAA;AAAA,MAKL,MAAM,QAAQ;AAAA,MACd,OAAO,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH;AAEA,QAAM,OAAO,QAAQ;AAErB,SAAO;AACT;AAEA,SAASC,aAAa,OAAO,YAAY;AACvC,QAAM,MAAM,WAAW;AAEvB,WAAS,IAAI,MAAM,GAAG,KAAK,GAAG,KAAK;AACjC,UAAM,aAAa,WAAW,CAAC;AAE/B,QAAI,WAAW,WAAW,MAAe,WAAW,WAAW,IAAa;AAC1E;AAAA,IACF;AAGA,QAAI,WAAW,QAAQ,IAAI;AACzB;AAAA,IACF;AAEA,UAAM,WAAW,WAAW,WAAW,GAAG;AAO1C,UAAM,WAAW,IAAI,KACV,WAAW,IAAI,CAAC,EAAE,QAAQ,WAAW,MAAM;AAAA,IAE3C,WAAW,IAAI,CAAC,EAAE,WAAW,WAAW,UACxC,WAAW,IAAI,CAAC,EAAE,UAAU,WAAW,QAAQ;AAAA,IAE/C,WAAW,WAAW,MAAM,CAAC,EAAE,UAAU,SAAS,QAAQ;AAErE,UAAM,KAAK,OAAO,aAAa,WAAW,MAAM;AAEhD,UAAM,UAAY,MAAM,OAAO,WAAW,KAAK;AAC/C,YAAQ,OAAU,WAAW,gBAAgB;AAC7C,YAAQ,MAAU,WAAW,WAAW;AACxC,YAAQ,UAAU;AAClB,YAAQ,SAAU,WAAW,KAAK,KAAK;AACvC,YAAQ,UAAU;AAElB,UAAM,UAAY,MAAM,OAAO,SAAS,KAAK;AAC7C,YAAQ,OAAU,WAAW,iBAAiB;AAC9C,YAAQ,MAAU,WAAW,WAAW;AACxC,YAAQ,UAAU;AAClB,YAAQ,SAAU,WAAW,KAAK,KAAK;AACvC,YAAQ,UAAU;AAElB,QAAI,UAAU;AACZ,YAAM,OAAO,WAAW,IAAI,CAAC,EAAE,KAAK,EAAE,UAAU;AAChD,YAAM,OAAO,WAAW,WAAW,MAAM,CAAC,EAAE,KAAK,EAAE,UAAU;AAC7D;AAAA,IACF;AAAA,EACF;AACF;AAIA,SAAS,sBAAuB,OAAO;AACrC,QAAM,cAAc,MAAM;AAC1B,QAAM,MAAM,MAAM,YAAY;AAE9B,EAAAA,aAAY,OAAO,MAAM,UAAU;AAEnC,WAAS,OAAO,GAAG,OAAO,KAAK,QAAQ;AACrC,QAAI,YAAY,IAAI,KAAK,YAAY,IAAI,EAAE,YAAY;AACrD,MAAAA,aAAY,OAAO,YAAY,IAAI,EAAE,UAAU;AAAA,IACjD;AAAA,EACF;AACF;AAEA,IAAO,mBAAQ;AAAA,EACb,UAAU;AAAA,EACV,aAAa;AACf;;;ACtHe,SAAR,KAAuB,OAAO,QAAQ;AAC3C,MAAIC,OAAM,OAAO,KAAK;AACtB,MAAI,OAAO;AACX,MAAI,QAAQ;AACZ,MAAI,QAAQ,MAAM;AAClB,MAAI,iBAAiB;AAErB,MAAI,MAAM,IAAI,WAAW,MAAM,GAAG,MAAM,IAAa;AAAE,WAAO;AAAA,EAAM;AAEpE,QAAM,SAAS,MAAM;AACrB,QAAM,MAAM,MAAM;AAClB,QAAM,aAAa,MAAM,MAAM;AAC/B,QAAM,WAAW,MAAM,GAAG,QAAQ,eAAe,OAAO,MAAM,KAAK,IAAI;AAGvE,MAAI,WAAW,GAAG;AAAE,WAAO;AAAA,EAAM;AAEjC,MAAI,MAAM,WAAW;AACrB,MAAI,MAAM,OAAO,MAAM,IAAI,WAAW,GAAG,MAAM,IAAa;AAM1D,qBAAiB;AAIjB;AACA,WAAO,MAAM,KAAK,OAAO;AACvB,MAAAA,QAAO,MAAM,IAAI,WAAW,GAAG;AAC/B,UAAI,CAAC,QAAQA,KAAI,KAAKA,UAAS,IAAM;AAAE;AAAA,MAAM;AAAA,IAC/C;AACA,QAAI,OAAO,KAAK;AAAE,aAAO;AAAA,IAAM;AAI/B,YAAQ;AACR,UAAM,MAAM,GAAG,QAAQ,qBAAqB,MAAM,KAAK,KAAK,MAAM,MAAM;AACxE,QAAI,IAAI,IAAI;AACV,aAAO,MAAM,GAAG,cAAc,IAAI,GAAG;AACrC,UAAI,MAAM,GAAG,aAAa,IAAI,GAAG;AAC/B,cAAM,IAAI;AAAA,MACZ,OAAO;AACL,eAAO;AAAA,MACT;AAIA,cAAQ;AACR,aAAO,MAAM,KAAK,OAAO;AACvB,QAAAA,QAAO,MAAM,IAAI,WAAW,GAAG;AAC/B,YAAI,CAAC,QAAQA,KAAI,KAAKA,UAAS,IAAM;AAAE;AAAA,QAAM;AAAA,MAC/C;AAIA,YAAM,MAAM,GAAG,QAAQ,eAAe,MAAM,KAAK,KAAK,MAAM,MAAM;AAClE,UAAI,MAAM,OAAO,UAAU,OAAO,IAAI,IAAI;AACxC,gBAAQ,IAAI;AACZ,cAAM,IAAI;AAIV,eAAO,MAAM,KAAK,OAAO;AACvB,UAAAA,QAAO,MAAM,IAAI,WAAW,GAAG;AAC/B,cAAI,CAAC,QAAQA,KAAI,KAAKA,UAAS,IAAM;AAAE;AAAA,UAAM;AAAA,QAC/C;AAAA,MACF;AAAA,IACF;AAEA,QAAI,OAAO,OAAO,MAAM,IAAI,WAAW,GAAG,MAAM,IAAa;AAE3D,uBAAiB;AAAA,IACnB;AACA;AAAA,EACF;AAEA,MAAI,gBAAgB;AAIlB,QAAI,OAAO,MAAM,IAAI,eAAe,aAAa;AAAE,aAAO;AAAA,IAAM;AAEhE,QAAI,MAAM,OAAO,MAAM,IAAI,WAAW,GAAG,MAAM,IAAa;AAC1D,cAAQ,MAAM;AACd,YAAM,MAAM,GAAG,QAAQ,eAAe,OAAO,GAAG;AAChD,UAAI,OAAO,GAAG;AACZ,gBAAQ,MAAM,IAAI,MAAM,OAAO,KAAK;AAAA,MACtC,OAAO;AACL,cAAM,WAAW;AAAA,MACnB;AAAA,IACF,OAAO;AACL,YAAM,WAAW;AAAA,IACnB;AAIA,QAAI,CAAC,OAAO;AAAE,cAAQ,MAAM,IAAI,MAAM,YAAY,QAAQ;AAAA,IAAE;AAE5D,UAAM,MAAM,IAAI,WAAW,mBAAmB,KAAK,CAAC;AACpD,QAAI,CAAC,KAAK;AACR,YAAM,MAAM;AACZ,aAAO;AAAA,IACT;AACA,WAAO,IAAI;AACX,YAAQ,IAAI;AAAA,EACd;AAMA,MAAI,CAAC,QAAQ;AACX,UAAM,MAAM;AACZ,UAAM,SAAS;AAEf,UAAM,UAAU,MAAM,KAAK,aAAa,KAAK,CAAC;AAC9C,UAAM,QAAQ,CAAC,CAAC,QAAQ,IAAI,CAAC;AAC7B,YAAQ,QAAS;AACjB,QAAI,OAAO;AACT,YAAM,KAAK,CAAC,SAAS,KAAK,CAAC;AAAA,IAC7B;AAEA,UAAM;AACN,UAAM,GAAG,OAAO,SAAS,KAAK;AAC9B,UAAM;AAEN,UAAM,KAAK,cAAc,KAAK,EAAE;AAAA,EAClC;AAEA,QAAM,MAAM;AACZ,QAAM,SAAS;AACf,SAAO;AACT;;;ACtIe,SAAR,MAAwB,OAAO,QAAQ;AAC5C,MAAIC,OAAM,SAAS,OAAO,KAAK,KAAK,KAAK,OAAO;AAChD,MAAI,OAAO;AACX,QAAM,SAAS,MAAM;AACrB,QAAM,MAAM,MAAM;AAElB,MAAI,MAAM,IAAI,WAAW,MAAM,GAAG,MAAM,IAAa;AAAE,WAAO;AAAA,EAAM;AACpE,MAAI,MAAM,IAAI,WAAW,MAAM,MAAM,CAAC,MAAM,IAAa;AAAE,WAAO;AAAA,EAAM;AAExE,QAAM,aAAa,MAAM,MAAM;AAC/B,QAAM,WAAW,MAAM,GAAG,QAAQ,eAAe,OAAO,MAAM,MAAM,GAAG,KAAK;AAG5E,MAAI,WAAW,GAAG;AAAE,WAAO;AAAA,EAAM;AAEjC,QAAM,WAAW;AACjB,MAAI,MAAM,OAAO,MAAM,IAAI,WAAW,GAAG,MAAM,IAAa;AAO1D;AACA,WAAO,MAAM,KAAK,OAAO;AACvB,MAAAA,QAAO,MAAM,IAAI,WAAW,GAAG;AAC/B,UAAI,CAAC,QAAQA,KAAI,KAAKA,UAAS,IAAM;AAAE;AAAA,MAAM;AAAA,IAC/C;AACA,QAAI,OAAO,KAAK;AAAE,aAAO;AAAA,IAAM;AAI/B,YAAQ;AACR,UAAM,MAAM,GAAG,QAAQ,qBAAqB,MAAM,KAAK,KAAK,MAAM,MAAM;AACxE,QAAI,IAAI,IAAI;AACV,aAAO,MAAM,GAAG,cAAc,IAAI,GAAG;AACrC,UAAI,MAAM,GAAG,aAAa,IAAI,GAAG;AAC/B,cAAM,IAAI;AAAA,MACZ,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAIA,YAAQ;AACR,WAAO,MAAM,KAAK,OAAO;AACvB,MAAAA,QAAO,MAAM,IAAI,WAAW,GAAG;AAC/B,UAAI,CAAC,QAAQA,KAAI,KAAKA,UAAS,IAAM;AAAE;AAAA,MAAM;AAAA,IAC/C;AAIA,UAAM,MAAM,GAAG,QAAQ,eAAe,MAAM,KAAK,KAAK,MAAM,MAAM;AAClE,QAAI,MAAM,OAAO,UAAU,OAAO,IAAI,IAAI;AACxC,cAAQ,IAAI;AACZ,YAAM,IAAI;AAIV,aAAO,MAAM,KAAK,OAAO;AACvB,QAAAA,QAAO,MAAM,IAAI,WAAW,GAAG;AAC/B,YAAI,CAAC,QAAQA,KAAI,KAAKA,UAAS,IAAM;AAAE;AAAA,QAAM;AAAA,MAC/C;AAAA,IACF,OAAO;AACL,cAAQ;AAAA,IACV;AAEA,QAAI,OAAO,OAAO,MAAM,IAAI,WAAW,GAAG,MAAM,IAAa;AAC3D,YAAM,MAAM;AACZ,aAAO;AAAA,IACT;AACA;AAAA,EACF,OAAO;AAIL,QAAI,OAAO,MAAM,IAAI,eAAe,aAAa;AAAE,aAAO;AAAA,IAAM;AAEhE,QAAI,MAAM,OAAO,MAAM,IAAI,WAAW,GAAG,MAAM,IAAa;AAC1D,cAAQ,MAAM;AACd,YAAM,MAAM,GAAG,QAAQ,eAAe,OAAO,GAAG;AAChD,UAAI,OAAO,GAAG;AACZ,gBAAQ,MAAM,IAAI,MAAM,OAAO,KAAK;AAAA,MACtC,OAAO;AACL,cAAM,WAAW;AAAA,MACnB;AAAA,IACF,OAAO;AACL,YAAM,WAAW;AAAA,IACnB;AAIA,QAAI,CAAC,OAAO;AAAE,cAAQ,MAAM,IAAI,MAAM,YAAY,QAAQ;AAAA,IAAE;AAE5D,UAAM,MAAM,IAAI,WAAW,mBAAmB,KAAK,CAAC;AACpD,QAAI,CAAC,KAAK;AACR,YAAM,MAAM;AACZ,aAAO;AAAA,IACT;AACA,WAAO,IAAI;AACX,YAAQ,IAAI;AAAA,EACd;AAMA,MAAI,CAAC,QAAQ;AACX,cAAU,MAAM,IAAI,MAAM,YAAY,QAAQ;AAE9C,UAAM,SAAS,CAAC;AAChB,UAAM,GAAG,OAAO;AAAA,MACd;AAAA,MACA,MAAM;AAAA,MACN,MAAM;AAAA,MACN;AAAA,IACF;AAEA,UAAM,QAAQ,MAAM,KAAK,SAAS,OAAO,CAAC;AAC1C,UAAM,QAAQ,CAAC,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;AACzC,UAAM,QAAQ;AACd,UAAM,WAAW;AACjB,UAAM,UAAU;AAEhB,QAAI,OAAO;AACT,YAAM,KAAK,CAAC,SAAS,KAAK,CAAC;AAAA,IAC7B;AAAA,EACF;AAEA,QAAM,MAAM;AACZ,QAAM,SAAS;AACf,SAAO;AACT;;;ACtIA,IAAM,WAAc;AAEpB,IAAM,cAAc;AAEL,SAAR,SAA2B,OAAO,QAAQ;AAC/C,MAAI,MAAM,MAAM;AAEhB,MAAI,MAAM,IAAI,WAAW,GAAG,MAAM,IAAa;AAAE,WAAO;AAAA,EAAM;AAE9D,QAAM,QAAQ,MAAM;AACpB,QAAM,MAAM,MAAM;AAElB,aAAS;AACP,QAAI,EAAE,OAAO,IAAK,QAAO;AAEzB,UAAM,KAAK,MAAM,IAAI,WAAW,GAAG;AAEnC,QAAI,OAAO,GAAc,QAAO;AAChC,QAAI,OAAO,GAAc;AAAA,EAC3B;AAEA,QAAM,MAAM,MAAM,IAAI,MAAM,QAAQ,GAAG,GAAG;AAE1C,MAAI,YAAY,KAAK,GAAG,GAAG;AACzB,UAAM,UAAU,MAAM,GAAG,cAAc,GAAG;AAC1C,QAAI,CAAC,MAAM,GAAG,aAAa,OAAO,GAAG;AAAE,aAAO;AAAA,IAAM;AAEpD,QAAI,CAAC,QAAQ;AACX,YAAM,UAAY,MAAM,KAAK,aAAa,KAAK,CAAC;AAChD,cAAQ,QAAU,CAAC,CAAC,QAAQ,OAAO,CAAC;AACpC,cAAQ,SAAU;AAClB,cAAQ,OAAU;AAElB,YAAM,UAAY,MAAM,KAAK,QAAQ,IAAI,CAAC;AAC1C,cAAQ,UAAU,MAAM,GAAG,kBAAkB,GAAG;AAEhD,YAAM,UAAY,MAAM,KAAK,cAAc,KAAK,EAAE;AAClD,cAAQ,SAAU;AAClB,cAAQ,OAAU;AAAA,IACpB;AAEA,UAAM,OAAO,IAAI,SAAS;AAC1B,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,KAAK,GAAG,GAAG;AACtB,UAAM,UAAU,MAAM,GAAG,cAAc,YAAY,GAAG;AACtD,QAAI,CAAC,MAAM,GAAG,aAAa,OAAO,GAAG;AAAE,aAAO;AAAA,IAAM;AAEpD,QAAI,CAAC,QAAQ;AACX,YAAM,UAAY,MAAM,KAAK,aAAa,KAAK,CAAC;AAChD,cAAQ,QAAU,CAAC,CAAC,QAAQ,OAAO,CAAC;AACpC,cAAQ,SAAU;AAClB,cAAQ,OAAU;AAElB,YAAM,UAAY,MAAM,KAAK,QAAQ,IAAI,CAAC;AAC1C,cAAQ,UAAU,MAAM,GAAG,kBAAkB,GAAG;AAEhD,YAAM,UAAY,MAAM,KAAK,cAAc,KAAK,EAAE;AAClD,cAAQ,SAAU;AAClB,cAAQ,OAAU;AAAA,IACpB;AAEA,UAAM,OAAO,IAAI,SAAS;AAC1B,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ACnEA,SAASC,YAAY,KAAK;AACxB,SAAO,YAAY,KAAK,GAAG;AAC7B;AACA,SAASC,aAAa,KAAK;AACzB,SAAO,aAAa,KAAK,GAAG;AAC9B;AAEA,SAAS,SAAU,IAAI;AAErB,QAAM,KAAK,KAAK;AAChB,SAAQ,MAAM,MAAiB,MAAM;AACvC;AAEe,SAAR,YAA8B,OAAO,QAAQ;AAClD,MAAI,CAAC,MAAM,GAAG,QAAQ,MAAM;AAAE,WAAO;AAAA,EAAM;AAG3C,QAAM,MAAM,MAAM;AAClB,QAAM,MAAM,MAAM;AAClB,MAAI,MAAM,IAAI,WAAW,GAAG,MAAM,MAC9B,MAAM,KAAK,KAAK;AAClB,WAAO;AAAA,EACT;AAGA,QAAM,KAAK,MAAM,IAAI,WAAW,MAAM,CAAC;AACvC,MAAI,OAAO,MACP,OAAO,MACP,OAAO,MACP,CAAC,SAAS,EAAE,GAAG;AACjB,WAAO;AAAA,EACT;AAEA,QAAMC,SAAQ,MAAM,IAAI,MAAM,GAAG,EAAE,MAAM,WAAW;AACpD,MAAI,CAACA,QAAO;AAAE,WAAO;AAAA,EAAM;AAE3B,MAAI,CAAC,QAAQ;AACX,UAAM,QAAQ,MAAM,KAAK,eAAe,IAAI,CAAC;AAC7C,UAAM,UAAUA,OAAM,CAAC;AAEvB,QAAIF,YAAW,MAAM,OAAO,EAAI,OAAM;AACtC,QAAIC,aAAY,MAAM,OAAO,EAAG,OAAM;AAAA,EACxC;AACA,QAAM,OAAOC,OAAM,CAAC,EAAE;AACtB,SAAO;AACT;;;AC5CA,IAAM,aAAa;AACnB,IAAM,WAAa;AAEJ,SAAR,OAAyB,OAAO,QAAQ;AAC7C,QAAM,MAAM,MAAM;AAClB,QAAM,MAAM,MAAM;AAElB,MAAI,MAAM,IAAI,WAAW,GAAG,MAAM,GAAa,QAAO;AAEtD,MAAI,MAAM,KAAK,IAAK,QAAO;AAE3B,QAAM,KAAK,MAAM,IAAI,WAAW,MAAM,CAAC;AAEvC,MAAI,OAAO,IAAc;AACvB,UAAMC,SAAQ,MAAM,IAAI,MAAM,GAAG,EAAE,MAAM,UAAU;AACnD,QAAIA,QAAO;AACT,UAAI,CAAC,QAAQ;AACX,cAAMC,QAAOD,OAAM,CAAC,EAAE,CAAC,EAAE,YAAY,MAAM,MAAM,SAASA,OAAM,CAAC,EAAE,MAAM,CAAC,GAAG,EAAE,IAAI,SAASA,OAAM,CAAC,GAAG,EAAE;AAExG,cAAM,QAAU,MAAM,KAAK,gBAAgB,IAAI,CAAC;AAChD,cAAM,UAAU,kBAAkBC,KAAI,IAAIC,eAAcD,KAAI,IAAIC,eAAc,KAAM;AACpF,cAAM,SAAUF,OAAM,CAAC;AACvB,cAAM,OAAU;AAAA,MAClB;AACA,YAAM,OAAOA,OAAM,CAAC,EAAE;AACtB,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AACL,UAAMA,SAAQ,MAAM,IAAI,MAAM,GAAG,EAAE,MAAM,QAAQ;AACjD,QAAIA,QAAO;AACT,YAAM,UAAU,WAAWA,OAAM,CAAC,CAAC;AACnC,UAAI,YAAYA,OAAM,CAAC,GAAG;AACxB,YAAI,CAAC,QAAQ;AACX,gBAAM,QAAU,MAAM,KAAK,gBAAgB,IAAI,CAAC;AAChD,gBAAM,UAAU;AAChB,gBAAM,SAAUA,OAAM,CAAC;AACvB,gBAAM,OAAU;AAAA,QAClB;AACA,cAAM,OAAOA,OAAM,CAAC,EAAE;AACtB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;AC/CA,SAAS,kBAAmB,YAAY;AACtC,QAAM,gBAAgB,CAAC;AACvB,QAAM,MAAM,WAAW;AAEvB,MAAI,CAAC,IAAK;AAGV,MAAI,YAAY;AAChB,MAAI,eAAe;AACnB,QAAM,QAAQ,CAAC;AAEf,WAAS,YAAY,GAAG,YAAY,KAAK,aAAa;AACpD,UAAM,SAAS,WAAW,SAAS;AAEnC,UAAM,KAAK,CAAC;AAMZ,QAAI,WAAW,SAAS,EAAE,WAAW,OAAO,UAAU,iBAAiB,OAAO,QAAQ,GAAG;AACvF,kBAAY;AAAA,IACd;AAEA,mBAAe,OAAO;AAMtB,WAAO,SAAS,OAAO,UAAU;AAEjC,QAAI,CAAC,OAAO,MAAO;AAOnB,QAAI,CAAC,cAAc,eAAe,OAAO,MAAM,GAAG;AAChD,oBAAc,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IACxD;AAEA,UAAM,eAAe,cAAc,OAAO,MAAM,GAAG,OAAO,OAAO,IAAI,KAAM,OAAO,SAAS,CAAE;AAE7F,QAAI,YAAY,YAAY,MAAM,SAAS,IAAI;AAE/C,QAAI,kBAAkB;AAEtB,WAAO,YAAY,cAAc,aAAa,MAAM,SAAS,IAAI,GAAG;AAClE,YAAM,SAAS,WAAW,SAAS;AAEnC,UAAI,OAAO,WAAW,OAAO,OAAQ;AAErC,UAAI,OAAO,QAAQ,OAAO,MAAM,GAAG;AACjC,YAAI,aAAa;AASjB,YAAI,OAAO,SAAS,OAAO,MAAM;AAC/B,eAAK,OAAO,SAAS,OAAO,UAAU,MAAM,GAAG;AAC7C,gBAAI,OAAO,SAAS,MAAM,KAAK,OAAO,SAAS,MAAM,GAAG;AACtD,2BAAa;AAAA,YACf;AAAA,UACF;AAAA,QACF;AAEA,YAAI,CAAC,YAAY;AAKf,gBAAM,WAAW,YAAY,KAAK,CAAC,WAAW,YAAY,CAAC,EAAE,OACzD,MAAM,YAAY,CAAC,IAAI,IACvB;AAEJ,gBAAM,SAAS,IAAI,YAAY,YAAY;AAC3C,gBAAM,SAAS,IAAI;AAEnB,iBAAO,OAAQ;AACf,iBAAO,MAAQ;AACf,iBAAO,QAAQ;AACf,4BAAkB;AAGlB,yBAAe;AACf;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,QAAI,oBAAoB,IAAI;AAQ1B,oBAAc,OAAO,MAAM,GAAG,OAAO,OAAO,IAAI,MAAO,OAAO,UAAU,KAAK,CAAE,IAAI;AAAA,IACrF;AAAA,EACF;AACF;AAEe,SAAR,WAA6B,OAAO;AACzC,QAAM,cAAc,MAAM;AAC1B,QAAM,MAAM,MAAM,YAAY;AAE9B,oBAAkB,MAAM,UAAU;AAElC,WAAS,OAAO,GAAG,OAAO,KAAK,QAAQ;AACrC,QAAI,YAAY,IAAI,KAAK,YAAY,IAAI,EAAE,YAAY;AACrD,wBAAkB,YAAY,IAAI,EAAE,UAAU;AAAA,IAChD;AAAA,EACF;AACF;;;AClHe,SAAR,eAAiC,OAAO;AAC7C,MAAI,MAAM;AACV,MAAI,QAAQ;AACZ,QAAM,SAAS,MAAM;AACrB,QAAM,MAAM,MAAM,OAAO;AAEzB,OAAK,OAAO,OAAO,GAAG,OAAO,KAAK,QAAQ;AAGxC,QAAI,OAAO,IAAI,EAAE,UAAU,EAAG;AAC9B,WAAO,IAAI,EAAE,QAAQ;AACrB,QAAI,OAAO,IAAI,EAAE,UAAU,EAAG;AAE9B,QAAI,OAAO,IAAI,EAAE,SAAS,UACtB,OAAO,IAAI,OACX,OAAO,OAAO,CAAC,EAAE,SAAS,QAAQ;AAEpC,aAAO,OAAO,CAAC,EAAE,UAAU,OAAO,IAAI,EAAE,UAAU,OAAO,OAAO,CAAC,EAAE;AAAA,IACrE,OAAO;AACL,UAAI,SAAS,MAAM;AAAE,eAAO,IAAI,IAAI,OAAO,IAAI;AAAA,MAAE;AAEjD;AAAA,IACF;AAAA,EACF;AAEA,MAAI,SAAS,MAAM;AACjB,WAAO,SAAS;AAAA,EAClB;AACF;;;ACVA,IAAMG,UAAS;AAAA,EACb,CAAC,QAAmB,IAAM;AAAA,EAC1B,CAAC,WAAmBC,QAAS;AAAA,EAC7B,CAAC,WAAmB,OAAS;AAAA,EAC7B,CAAC,UAAmBC,OAAQ;AAAA,EAC5B,CAAC,aAAmB,QAAW;AAAA,EAC/B,CAAC,iBAAmB,sBAAgB,QAAQ;AAAA,EAC5C,CAAC,YAAmB,iBAAW,QAAQ;AAAA,EACvC,CAAC,QAAmB,IAAM;AAAA,EAC1B,CAAC,SAAmB,KAAO;AAAA,EAC3B,CAAC,YAAmB,QAAU;AAAA,EAC9B,CAAC,eAAmB,WAAa;AAAA,EACjC,CAAC,UAAmB,MAAQ;AAC9B;AAOA,IAAMC,WAAU;AAAA,EACd,CAAC,iBAAmB,UAAe;AAAA,EACnC,CAAC,iBAAmB,sBAAgB,WAAW;AAAA,EAC/C,CAAC,YAAmB,iBAAW,WAAW;AAAA;AAAA;AAAA,EAG1C,CAAC,kBAAmB,cAAgB;AACtC;AAKA,SAAS,eAAgB;AAMvB,OAAK,QAAQ,IAAI,cAAM;AAEvB,WAAS,IAAI,GAAG,IAAIH,QAAO,QAAQ,KAAK;AACtC,SAAK,MAAM,KAAKA,QAAO,CAAC,EAAE,CAAC,GAAGA,QAAO,CAAC,EAAE,CAAC,CAAC;AAAA,EAC5C;AAQA,OAAK,SAAS,IAAI,cAAM;AAExB,WAAS,IAAI,GAAG,IAAIG,SAAQ,QAAQ,KAAK;AACvC,SAAK,OAAO,KAAKA,SAAQ,CAAC,EAAE,CAAC,GAAGA,SAAQ,CAAC,EAAE,CAAC,CAAC;AAAA,EAC/C;AACF;AAKA,aAAa,UAAU,YAAY,SAAU,OAAO;AAClD,QAAM,MAAM,MAAM;AAClB,QAAM,QAAQ,KAAK,MAAM,SAAS,EAAE;AACpC,QAAM,MAAM,MAAM;AAClB,QAAM,aAAa,MAAM,GAAG,QAAQ;AACpC,QAAM,QAAQ,MAAM;AAEpB,MAAI,OAAO,MAAM,GAAG,MAAM,aAAa;AACrC,UAAM,MAAM,MAAM,GAAG;AACrB;AAAA,EACF;AAEA,MAAI,KAAK;AAET,MAAI,MAAM,QAAQ,YAAY;AAC5B,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAK5B,YAAM;AACN,WAAK,MAAM,CAAC,EAAE,OAAO,IAAI;AACzB,YAAM;AAEN,UAAI,IAAI;AACN,YAAI,OAAO,MAAM,KAAK;AAAE,gBAAM,IAAI,MAAM,wCAAwC;AAAA,QAAE;AAClF;AAAA,MACF;AAAA,IACF;AAAA,EACF,OAAO;AAYL,UAAM,MAAM,MAAM;AAAA,EACpB;AAEA,MAAI,CAAC,IAAI;AAAE,UAAM;AAAA,EAAM;AACvB,QAAM,GAAG,IAAI,MAAM;AACrB;AAIA,aAAa,UAAU,WAAW,SAAU,OAAO;AACjD,QAAM,QAAQ,KAAK,MAAM,SAAS,EAAE;AACpC,QAAM,MAAM,MAAM;AAClB,QAAM,MAAM,MAAM;AAClB,QAAM,aAAa,MAAM,GAAG,QAAQ;AAEpC,SAAO,MAAM,MAAM,KAAK;AAOtB,UAAM,UAAU,MAAM;AACtB,QAAI,KAAK;AAET,QAAI,MAAM,QAAQ,YAAY;AAC5B,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,aAAK,MAAM,CAAC,EAAE,OAAO,KAAK;AAC1B,YAAI,IAAI;AACN,cAAI,WAAW,MAAM,KAAK;AAAE,kBAAM,IAAI,MAAM,wCAAwC;AAAA,UAAE;AACtF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,QAAI,IAAI;AACN,UAAI,MAAM,OAAO,KAAK;AAAE;AAAA,MAAM;AAC9B;AAAA,IACF;AAEA,UAAM,WAAW,MAAM,IAAI,MAAM,KAAK;AAAA,EACxC;AAEA,MAAI,MAAM,SAAS;AACjB,UAAM,YAAY;AAAA,EACpB;AACF;AAOA,aAAa,UAAU,QAAQ,SAAU,KAAK,IAAI,KAAK,WAAW;AAChE,QAAM,QAAQ,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,SAAS;AAEpD,OAAK,SAAS,KAAK;AAEnB,QAAM,QAAQ,KAAK,OAAO,SAAS,EAAE;AACrC,QAAM,MAAM,MAAM;AAElB,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,UAAM,CAAC,EAAE,KAAK;AAAA,EAChB;AACF;AAEA,aAAa,UAAU,QAAQ;AAE/B,IAAO,wBAAQ;;;AClMA,SAAR,WAAkB,MAAM;AAC7B,QAAM,KAAK,CAAC;AACZ,SAAO,QAAQ,CAAC;AAEhB,KAAG,UAAU,cAAI;AACjB,KAAG,SAASC,eAAG;AACf,KAAG,QAAQA,eAAE;AACb,KAAG,QAAQA,eAAE;AAGb,KAAG,WAAW,CAAC,GAAG,OAAO,GAAG,OAAO,GAAG,MAAM,EAAE,KAAK,GAAG;AAGtD,KAAG,UAAU,CAAC,GAAG,OAAO,GAAG,MAAM,EAAE,KAAK,GAAG;AAI3C,QAAM,kBAAkB;AAKxB,KAAG,oBAAoB,WAAW,kBAAkB,MAAM,GAAG,WAAW,MAAM,GAAG,UAAU;AAI3F,KAAG,UAED;AAGF,KAAG,WAAW,cAAc,GAAG,UAAU;AAEzC,KAAG,WAED;AAEF,KAAG,sBAED,UAAU,kBAAkB,MAAM,GAAG,WAAW,UACvC,KAAK,KAAK,IAAI,aAAa,QAAQ,yBAAyB,GAAG,WAAW;AAErF,KAAG,WAED,mBAGc,GAAG,UAAU,MAAM,kBAAkB,sCAC/B,GAAG,UAAU,0BACb,GAAG,UAAU,0BACb,GAAG,UAAU,0BACb,GAAG,UAAU,0BACb,GAAG,UAAU,uBAGhB,GAAG,oBAAoB,uCAYvB,GAAG,UAAU,cACvB,KAAK,KAAK,IACP,+BACA;AAAA,EAGJ,SAAS,GAAG,UAAU,aAGb,GAAG,UAAU,gBAGV,GAAG,UAAU,mBAEd,GAAG,UAAU;AAOhC,KAAG,iBAED;AAEF,KAAG,SAED;AAKF,KAAG;AAAA,EAGD,QACE,GAAG,SACH,MACA,GAAG,oBAAoB;AAG3B,KAAG,aAED,QACE,GAAG,SACH,SACQ,GAAG,oBAAoB,UAEvB,GAAG,oBAAoB,UAAU,GAAG,oBAAoB,YAAY,GAAG,oBAAoB;AAGvG,KAAG,WAED,iBAIgB,GAAG,aAAa,WAAW,GAAG,aAAwB;AAGxE,KAAG,iBAED,QACE,GAAG,UACL,eACgB,GAAG,aAAa;AAGlC,KAAG,uBAED,cAAc,GAAG,aAAa;AAEhC,KAAG,kBAED,GAAG,WAAW,GAAG;AAEnB,KAAG,wBAED,GAAG,iBAAiB,GAAG;AAEzB,KAAG,uBAED,GAAG,WAAW,GAAG,WAAW,GAAG;AAEjC,KAAG,6BAED,GAAG,iBAAiB,GAAG,WAAW,GAAG;AAEvC,KAAG,mCAED,GAAG,uBAAuB,GAAG,WAAW,GAAG;AAO7C,KAAG,sBAED,wDAAwD,GAAG,WAAW;AAExE,KAAG,kBAEC,QAAQ,kBAAkB,YAAY,GAAG,UAAU,OAC7C,GAAG,iBAAiB,MAAM,GAAG,wBAAwB;AAE/D,KAAG;AAAA;AAAA,EAGC,qCAA0C,GAAG,WAAW,uBAC9B,GAAG,6BAA6B,GAAG,WAAW;AAE5E,KAAG;AAAA;AAAA,EAGC,qCAA0C,GAAG,WAAW,uBAC9B,GAAG,mCAAmC,GAAG,WAAW;AAElF,SAAO;AACT;;;ACpLA,SAASC,QAAQ,KAAoC;AACnD,QAAM,UAAU,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AAEvD,UAAQ,QAAQ,SAAU,QAAQ;AAChC,QAAI,CAAC,QAAQ;AAAE;AAAA,IAAO;AAEtB,WAAO,KAAK,MAAM,EAAE,QAAQ,SAAU,KAAK;AACzC,UAAI,GAAG,IAAI,OAAO,GAAG;AAAA,IACvB,CAAC;AAAA,EACH,CAAC;AAED,SAAO;AACT;AAEA,SAASC,QAAQ,KAAK;AAAE,SAAO,OAAO,UAAU,SAAS,KAAK,GAAG;AAAE;AACnE,SAASC,UAAU,KAAK;AAAE,SAAOD,QAAO,GAAG,MAAM;AAAkB;AACnE,SAAS,SAAU,KAAK;AAAE,SAAOA,QAAO,GAAG,MAAM;AAAkB;AACnE,SAAS,SAAU,KAAK;AAAE,SAAOA,QAAO,GAAG,MAAM;AAAkB;AACnE,SAAS,WAAY,KAAK;AAAE,SAAOA,QAAO,GAAG,MAAM;AAAoB;AAEvE,SAASE,UAAU,KAAK;AAAE,SAAO,IAAI,QAAQ,wBAAwB,MAAM;AAAE;AAI7E,IAAM,iBAAiB;AAAA,EACrB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AACX;AAEA,SAAS,aAAc,KAAK;AAC1B,SAAO,OAAO,KAAK,OAAO,CAAC,CAAC,EAAE,OAAO,SAAU,KAAK,GAAG;AAErD,WAAO,OAAO,eAAe,eAAe,CAAC;AAAA,EAC/C,GAAG,KAAK;AACV;AAEA,IAAM,iBAAiB;AAAA,EACrB,SAAS;AAAA,IACP,UAAU,SAAUC,OAAM,KAAK,MAAM;AACnC,YAAM,OAAOA,MAAK,MAAM,GAAG;AAE3B,UAAI,CAAC,KAAK,GAAG,MAAM;AAEjB,aAAK,GAAG,OAAO,IAAI;AAAA,UACjB,YAAY,KAAK,GAAG,WAAW,KAAK,GAAG,uBAAuB,KAAK,GAAG;AAAA,UAAU;AAAA,QAClF;AAAA,MACF;AACA,UAAI,KAAK,GAAG,KAAK,KAAK,IAAI,GAAG;AAC3B,eAAO,KAAK,MAAM,KAAK,GAAG,IAAI,EAAE,CAAC,EAAE;AAAA,MACrC;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,MAAM;AAAA,IACJ,UAAU,SAAUA,OAAM,KAAK,MAAM;AACnC,YAAM,OAAOA,MAAK,MAAM,GAAG;AAE3B,UAAI,CAAC,KAAK,GAAG,SAAS;AAEpB,aAAK,GAAG,UAAU,IAAI;AAAA,UACpB,MACA,KAAK,GAAG;AAAA;AAAA,UAGR,wBAAwB,KAAK,GAAG,aAAa,WAAW,KAAK,GAAG,kBAAkB,MAClF,KAAK,GAAG,WACR,KAAK,GAAG,sBACR,KAAK,GAAG;AAAA,UAER;AAAA,QACF;AAAA,MACF;AAEA,UAAI,KAAK,GAAG,QAAQ,KAAK,IAAI,GAAG;AAE9B,YAAI,OAAO,KAAKA,MAAK,MAAM,CAAC,MAAM,KAAK;AAAE,iBAAO;AAAA,QAAE;AAClD,YAAI,OAAO,KAAKA,MAAK,MAAM,CAAC,MAAM,KAAK;AAAE,iBAAO;AAAA,QAAE;AAClD,eAAO,KAAK,MAAM,KAAK,GAAG,OAAO,EAAE,CAAC,EAAE;AAAA,MACxC;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,WAAW;AAAA,IACT,UAAU,SAAUA,OAAM,KAAK,MAAM;AACnC,YAAM,OAAOA,MAAK,MAAM,GAAG;AAE3B,UAAI,CAAC,KAAK,GAAG,QAAQ;AACnB,aAAK,GAAG,SAAS,IAAI;AAAA,UACnB,MAAM,KAAK,GAAG,iBAAiB,MAAM,KAAK,GAAG;AAAA,UAAiB;AAAA,QAChE;AAAA,MACF;AACA,UAAI,KAAK,GAAG,OAAO,KAAK,IAAI,GAAG;AAC7B,eAAO,KAAK,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,EAAE;AAAA,MACvC;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAIA,IAAM,kBAAkB;AAGxB,IAAM,eAAe,8EAA8E,MAAM,GAAG;AAE5G,SAAS,eAAgB,MAAM;AAC7B,OAAK,YAAY;AACjB,OAAK,iBAAiB;AACxB;AAEA,SAAS,gBAAiB,IAAI;AAC5B,SAAO,SAAUA,OAAM,KAAK;AAC1B,UAAM,OAAOA,MAAK,MAAM,GAAG;AAE3B,QAAI,GAAG,KAAK,IAAI,GAAG;AACjB,aAAO,KAAK,MAAM,EAAE,EAAE,CAAC,EAAE;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,mBAAoB;AAC3B,SAAO,SAAUC,QAAO,MAAM;AAC5B,SAAK,UAAUA,MAAK;AAAA,EACtB;AACF;AAIA,SAAS,QAAS,MAAM;AAEtB,QAAM,KAAK,KAAK,KAAK,WAAU,KAAK,QAAQ;AAG5C,QAAMC,QAAO,KAAK,SAAS,MAAM;AAEjC,OAAK,UAAU;AAEf,MAAI,CAAC,KAAK,mBAAmB;AAC3B,IAAAA,MAAK,KAAK,eAAe;AAAA,EAC3B;AACA,EAAAA,MAAK,KAAK,GAAG,MAAM;AAEnB,KAAG,WAAWA,MAAK,KAAK,GAAG;AAE3B,WAAS,MAAO,KAAK;AAAE,WAAO,IAAI,QAAQ,UAAU,GAAG,QAAQ;AAAA,EAAE;AAEjE,KAAG,cAAc,OAAO,MAAM,GAAG,eAAe,GAAG,GAAG;AACtD,KAAG,aAAa,OAAO,MAAM,GAAG,cAAc,GAAG,GAAG;AACpD,KAAG,mBAAmB,OAAO,MAAM,GAAG,oBAAoB,GAAG,GAAG;AAChE,KAAG,kBAAkB,OAAO,MAAM,GAAG,mBAAmB,GAAG,GAAG;AAM9D,QAAM,UAAU,CAAC;AAEjB,OAAK,eAAe,CAAC;AAErB,WAAS,YAAa,MAAM,KAAK;AAC/B,UAAM,IAAI,MAAM,iCAAiC,OAAO,QAAQ,GAAG;AAAA,EACrE;AAEA,SAAO,KAAK,KAAK,WAAW,EAAE,QAAQ,SAAU,MAAM;AACpD,UAAM,MAAM,KAAK,YAAY,IAAI;AAGjC,QAAI,QAAQ,MAAM;AAAE;AAAA,IAAO;AAE3B,UAAM,WAAW,EAAE,UAAU,MAAM,MAAM,KAAK;AAE9C,SAAK,aAAa,IAAI,IAAI;AAE1B,QAAI,SAAS,GAAG,GAAG;AACjB,UAAI,SAAS,IAAI,QAAQ,GAAG;AAC1B,iBAAS,WAAW,gBAAgB,IAAI,QAAQ;AAAA,MAClD,WAAW,WAAW,IAAI,QAAQ,GAAG;AACnC,iBAAS,WAAW,IAAI;AAAA,MAC1B,OAAO;AACL,oBAAY,MAAM,GAAG;AAAA,MACvB;AAEA,UAAI,WAAW,IAAI,SAAS,GAAG;AAC7B,iBAAS,YAAY,IAAI;AAAA,MAC3B,WAAW,CAAC,IAAI,WAAW;AACzB,iBAAS,YAAY,iBAAiB;AAAA,MACxC,OAAO;AACL,oBAAY,MAAM,GAAG;AAAA,MACvB;AAEA;AAAA,IACF;AAEA,QAAIJ,UAAS,GAAG,GAAG;AACjB,cAAQ,KAAK,IAAI;AACjB;AAAA,IACF;AAEA,gBAAY,MAAM,GAAG;AAAA,EACvB,CAAC;AAMD,UAAQ,QAAQ,SAAU,OAAO;AAC/B,QAAI,CAAC,KAAK,aAAa,KAAK,YAAY,KAAK,CAAC,GAAG;AAG/C;AAAA,IACF;AAEA,SAAK,aAAa,KAAK,EAAE,WACvB,KAAK,aAAa,KAAK,YAAY,KAAK,CAAC,EAAE;AAC7C,SAAK,aAAa,KAAK,EAAE,YACvB,KAAK,aAAa,KAAK,YAAY,KAAK,CAAC,EAAE;AAAA,EAC/C,CAAC;AAKD,OAAK,aAAa,EAAE,IAAI,EAAE,UAAU,MAAM,WAAW,iBAAiB,EAAE;AAKxE,QAAM,QAAQ,OAAO,KAAK,KAAK,YAAY,EACxC,OAAO,SAAU,MAAM;AAEtB,WAAO,KAAK,SAAS,KAAK,KAAK,aAAa,IAAI;AAAA,EAClD,CAAC,EACA,IAAIC,SAAQ,EACZ,KAAK,GAAG;AAEX,OAAK,GAAG,cAAc,OAAO,sBAA2B,GAAG,WAAW,QAAQ,QAAQ,KAAK,GAAG;AAC9F,OAAK,GAAG,gBAAgB,OAAO,sBAA2B,GAAG,WAAW,QAAQ,QAAQ,KAAK,IAAI;AACjG,OAAK,GAAG,kBAAkB,OAAO,MAAM,KAAK,GAAG,cAAc,QAAQ,GAAG;AAExE,OAAK,GAAG,UAAU;AAAA,IAChB,MAAM,KAAK,GAAG,YAAY,SAAS,QAAQ,KAAK,GAAG,gBAAgB,SAAS;AAAA,IAC5E;AAAA,EACF;AAMA,iBAAe,IAAI;AACrB;AAOA,SAAS,MAAO,MAAM,OAAO;AAC3B,QAAM,QAAQ,KAAK;AACnB,QAAM,MAAM,KAAK;AACjB,QAAMC,QAAO,KAAK,eAAe,MAAM,OAAO,GAAG;AAOjD,OAAK,SAAS,KAAK,WAAW,YAAY;AAM1C,OAAK,QAAQ,QAAQ;AAMrB,OAAK,YAAY,MAAM;AAMvB,OAAK,MAAMA;AAMX,OAAK,OAAOA;AAMZ,OAAK,MAAMA;AACb;AAEA,SAAS,YAAa,MAAM,OAAO;AACjC,QAAMC,SAAQ,IAAI,MAAM,MAAM,KAAK;AAEnC,OAAK,aAAaA,OAAM,MAAM,EAAE,UAAUA,QAAO,IAAI;AAErD,SAAOA;AACT;AAwCA,SAAS,UAAW,SAAS,SAAS;AACpC,MAAI,EAAE,gBAAgB,YAAY;AAChC,WAAO,IAAI,UAAU,SAAS,OAAO;AAAA,EACvC;AAEA,MAAI,CAAC,SAAS;AACZ,QAAI,aAAa,OAAO,GAAG;AACzB,gBAAU;AACV,gBAAU,CAAC;AAAA,IACb;AAAA,EACF;AAEA,OAAK,WAAWL,QAAO,CAAC,GAAG,gBAAgB,OAAO;AAGlD,OAAK,YAAY;AACjB,OAAK,iBAAiB;AACtB,OAAK,aAAa;AAClB,OAAK,iBAAiB;AAEtB,OAAK,cAAcA,QAAO,CAAC,GAAG,gBAAgB,OAAO;AACrD,OAAK,eAAe,CAAC;AAErB,OAAK,WAAW;AAChB,OAAK,oBAAoB;AAEzB,OAAK,KAAK,CAAC;AAEX,UAAQ,IAAI;AACd;AASA,UAAU,UAAU,MAAM,SAAS,IAAK,QAAQ,YAAY;AAC1D,OAAK,YAAY,MAAM,IAAI;AAC3B,UAAQ,IAAI;AACZ,SAAO;AACT;AAQA,UAAU,UAAU,MAAM,SAAS,IAAK,SAAS;AAC/C,OAAK,WAAWA,QAAO,KAAK,UAAU,OAAO;AAC7C,SAAO;AACT;AAOA,UAAU,UAAU,OAAO,SAAS,KAAMI,OAAM;AAE9C,OAAK,iBAAiBA;AACtB,OAAK,YAAY;AAEjB,MAAI,CAACA,MAAK,QAAQ;AAAE,WAAO;AAAA,EAAM;AAEjC,MAAI,GAAG,IAAI,IAAI,KAAK,OAAO,MAAM,IAAI,SAAS;AAG9C,MAAI,KAAK,GAAG,YAAY,KAAKA,KAAI,GAAG;AAClC,SAAK,KAAK,GAAG;AACb,OAAG,YAAY;AACf,YAAQ,IAAI,GAAG,KAAKA,KAAI,OAAO,MAAM;AACnC,YAAM,KAAK,aAAaA,OAAM,EAAE,CAAC,GAAG,GAAG,SAAS;AAChD,UAAI,KAAK;AACP,aAAK,aAAa,EAAE,CAAC;AACrB,aAAK,YAAY,EAAE,QAAQ,EAAE,CAAC,EAAE;AAChC,aAAK,iBAAiB,EAAE,QAAQ,EAAE,CAAC,EAAE,SAAS;AAC9C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,KAAK,SAAS,aAAa,KAAK,aAAa,OAAO,GAAG;AAEzD,cAAUA,MAAK,OAAO,KAAK,GAAG,eAAe;AAC7C,QAAI,WAAW,GAAG;AAEhB,UAAI,KAAK,YAAY,KAAK,UAAU,KAAK,WAAW;AAClD,aAAK,KAAKA,MAAK,MAAM,KAAK,SAAS,UAAU,KAAK,GAAG,aAAa,KAAK,GAAG,gBAAgB,OAAO,MAAM;AACrG,kBAAQ,GAAG,QAAQ,GAAG,CAAC,EAAE;AAEzB,cAAI,KAAK,YAAY,KAAK,QAAQ,KAAK,WAAW;AAChD,iBAAK,aAAa;AAClB,iBAAK,YAAY;AACjB,iBAAK,iBAAiB,GAAG,QAAQ,GAAG,CAAC,EAAE;AAAA,UACzC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,KAAK,SAAS,cAAc,KAAK,aAAa,SAAS,GAAG;AAE5D,aAASA,MAAK,QAAQ,GAAG;AACzB,QAAI,UAAU,GAAG;AAGf,WAAK,KAAKA,MAAK,MAAM,KAAK,GAAG,WAAW,OAAO,MAAM;AACnD,gBAAQ,GAAG,QAAQ,GAAG,CAAC,EAAE;AACzB,eAAO,GAAG,QAAQ,GAAG,CAAC,EAAE;AAExB,YAAI,KAAK,YAAY,KAAK,QAAQ,KAAK,aAClC,UAAU,KAAK,aAAa,OAAO,KAAK,gBAAiB;AAC5D,eAAK,aAAa;AAClB,eAAK,YAAY;AACjB,eAAK,iBAAiB;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO,KAAK,aAAa;AAC3B;AASA,UAAU,UAAU,UAAU,SAAS,QAASA,OAAM;AACpD,SAAO,KAAK,GAAG,QAAQ,KAAKA,KAAI;AAClC;AAWA,UAAU,UAAU,eAAe,SAAS,aAAcA,OAAM,QAAQ,KAAK;AAE3E,MAAI,CAAC,KAAK,aAAa,OAAO,YAAY,CAAC,GAAG;AAC5C,WAAO;AAAA,EACT;AACA,SAAO,KAAK,aAAa,OAAO,YAAY,CAAC,EAAE,SAASA,OAAM,KAAK,IAAI;AACzE;AAkBA,UAAU,UAAU,QAAQ,SAAS,MAAOA,OAAM;AAChD,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AAGZ,MAAI,KAAK,aAAa,KAAK,KAAK,mBAAmBA,OAAM;AACvD,WAAO,KAAK,YAAY,MAAM,KAAK,CAAC;AACpC,YAAQ,KAAK;AAAA,EACf;AAGA,MAAI,OAAO,QAAQA,MAAK,MAAM,KAAK,IAAIA;AAGvC,SAAO,KAAK,KAAK,IAAI,GAAG;AACtB,WAAO,KAAK,YAAY,MAAM,KAAK,CAAC;AAEpC,WAAO,KAAK,MAAM,KAAK,cAAc;AACrC,aAAS,KAAK;AAAA,EAChB;AAEA,MAAI,OAAO,QAAQ;AACjB,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAQA,UAAU,UAAU,eAAe,SAAS,aAAcA,OAAM;AAE9D,OAAK,iBAAiBA;AACtB,OAAK,YAAY;AAEjB,MAAI,CAACA,MAAK,OAAQ,QAAO;AAEzB,QAAM,IAAI,KAAK,GAAG,gBAAgB,KAAKA,KAAI;AAC3C,MAAI,CAAC,EAAG,QAAO;AAEf,QAAM,MAAM,KAAK,aAAaA,OAAM,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM;AACrD,MAAI,CAAC,IAAK,QAAO;AAEjB,OAAK,aAAa,EAAE,CAAC;AACrB,OAAK,YAAY,EAAE,QAAQ,EAAE,CAAC,EAAE;AAChC,OAAK,iBAAiB,EAAE,QAAQ,EAAE,CAAC,EAAE,SAAS;AAE9C,SAAO,YAAY,MAAM,CAAC;AAC5B;AAiBA,UAAU,UAAU,OAAO,SAAS,KAAMG,OAAM,SAAS;AACvD,EAAAA,QAAO,MAAM,QAAQA,KAAI,IAAIA,QAAO,CAACA,KAAI;AAEzC,MAAI,CAAC,SAAS;AACZ,SAAK,WAAWA,MAAK,MAAM;AAC3B,SAAK,oBAAoB;AACzB,YAAQ,IAAI;AACZ,WAAO;AAAA,EACT;AAEA,OAAK,WAAW,KAAK,SAAS,OAAOA,KAAI,EACtC,KAAK,EACL,OAAO,SAAU,IAAI,KAAK,KAAK;AAC9B,WAAO,OAAO,IAAI,MAAM,CAAC;AAAA,EAC3B,CAAC,EACA,QAAQ;AAEX,UAAQ,IAAI;AACZ,SAAO;AACT;AAOA,UAAU,UAAU,YAAY,SAASC,WAAWH,QAAO;AAIzD,MAAI,CAACA,OAAM,QAAQ;AAAE,IAAAA,OAAM,MAAM,YAAYA,OAAM;AAAA,EAAI;AAEvD,MAAIA,OAAM,WAAW,aAAa,CAAC,YAAY,KAAKA,OAAM,GAAG,GAAG;AAC9D,IAAAA,OAAM,MAAM,YAAYA,OAAM;AAAA,EAChC;AACF;AAOA,UAAU,UAAU,YAAY,SAAS,YAAa;AACtD;AAEA,IAAO,qBAAQ;;;AC9nBf,IAAM,SAAS;AAGf,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAM,cAAc;AACpB,IAAM,WAAW;AACjB,IAAM,YAAY;AAGlB,IAAM,gBAAgB;AACtB,IAAM,gBAAgB;AACtB,IAAM,kBAAkB;AAGxB,IAAM,SAAS;AAAA,EACd,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,iBAAiB;AAClB;AAGA,IAAM,gBAAgB,OAAO;AAC7B,IAAM,QAAQ,KAAK;AACnB,IAAM,qBAAqB,OAAO;AAUlC,SAAS,MAAM,MAAM;AACpB,QAAM,IAAI,WAAW,OAAO,IAAI,CAAC;AAClC;AAUA,SAAS,IAAI,OAAO,UAAU;AAC7B,QAAM,SAAS,CAAC;AAChB,MAAI,SAAS,MAAM;AACnB,SAAO,UAAU;AAChB,WAAO,MAAM,IAAI,SAAS,MAAM,MAAM,CAAC;AAAA,EACxC;AACA,SAAO;AACR;AAYA,SAAS,UAAU,QAAQ,UAAU;AACpC,QAAM,QAAQ,OAAO,MAAM,GAAG;AAC9B,MAAI,SAAS;AACb,MAAI,MAAM,SAAS,GAAG;AAGrB,aAAS,MAAM,CAAC,IAAI;AACpB,aAAS,MAAM,CAAC;AAAA,EACjB;AAEA,WAAS,OAAO,QAAQ,iBAAiB,GAAM;AAC/C,QAAM,SAAS,OAAO,MAAM,GAAG;AAC/B,QAAM,UAAU,IAAI,QAAQ,QAAQ,EAAE,KAAK,GAAG;AAC9C,SAAO,SAAS;AACjB;AAeA,SAAS,WAAW,QAAQ;AAC3B,QAAM,SAAS,CAAC;AAChB,MAAI,UAAU;AACd,QAAM,SAAS,OAAO;AACtB,SAAO,UAAU,QAAQ;AACxB,UAAM,QAAQ,OAAO,WAAW,SAAS;AACzC,QAAI,SAAS,SAAU,SAAS,SAAU,UAAU,QAAQ;AAE3D,YAAM,QAAQ,OAAO,WAAW,SAAS;AACzC,WAAK,QAAQ,UAAW,OAAQ;AAC/B,eAAO,OAAO,QAAQ,SAAU,OAAO,QAAQ,QAAS,KAAO;AAAA,MAChE,OAAO;AAGN,eAAO,KAAK,KAAK;AACjB;AAAA,MACD;AAAA,IACD,OAAO;AACN,aAAO,KAAK,KAAK;AAAA,IAClB;AAAA,EACD;AACA,SAAO;AACR;AAUA,IAAM,aAAa,gBAAc,OAAO,cAAc,GAAG,UAAU;AAWnE,IAAM,eAAe,SAAS,WAAW;AACxC,MAAI,aAAa,MAAQ,YAAY,IAAM;AAC1C,WAAO,MAAM,YAAY;AAAA,EAC1B;AACA,MAAI,aAAa,MAAQ,YAAY,IAAM;AAC1C,WAAO,YAAY;AAAA,EACpB;AACA,MAAI,aAAa,MAAQ,YAAY,KAAM;AAC1C,WAAO,YAAY;AAAA,EACpB;AACA,SAAO;AACR;AAaA,IAAM,eAAe,SAAS,OAAO,MAAM;AAG1C,SAAO,QAAQ,KAAK,MAAM,QAAQ,QAAQ,QAAQ,MAAM;AACzD;AAOA,IAAM,QAAQ,SAAS,OAAO,WAAW,WAAW;AACnD,MAAI,IAAI;AACR,UAAQ,YAAY,MAAM,QAAQ,IAAI,IAAI,SAAS;AACnD,WAAS,MAAM,QAAQ,SAAS;AAChC,SAA8B,QAAQ,gBAAgB,QAAQ,GAAG,KAAK,MAAM;AAC3E,YAAQ,MAAM,QAAQ,aAAa;AAAA,EACpC;AACA,SAAO,MAAM,KAAK,gBAAgB,KAAK,SAAS,QAAQ,KAAK;AAC9D;AASA,IAAMI,UAAS,SAAS,OAAO;AAE9B,QAAM,SAAS,CAAC;AAChB,QAAM,cAAc,MAAM;AAC1B,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,OAAO;AAMX,MAAI,QAAQ,MAAM,YAAY,SAAS;AACvC,MAAI,QAAQ,GAAG;AACd,YAAQ;AAAA,EACT;AAEA,WAAS,IAAI,GAAG,IAAI,OAAO,EAAE,GAAG;AAE/B,QAAI,MAAM,WAAW,CAAC,KAAK,KAAM;AAChC,YAAM,WAAW;AAAA,IAClB;AACA,WAAO,KAAK,MAAM,WAAW,CAAC,CAAC;AAAA,EAChC;AAKA,WAAS,QAAQ,QAAQ,IAAI,QAAQ,IAAI,GAAG,QAAQ,eAAwC;AAO3F,UAAM,OAAO;AACb,aAAS,IAAI,GAAG,IAAI,QAA0B,KAAK,MAAM;AAExD,UAAI,SAAS,aAAa;AACzB,cAAM,eAAe;AAAA,MACtB;AAEA,YAAM,QAAQ,aAAa,MAAM,WAAW,OAAO,CAAC;AAEpD,UAAI,SAAS,MAAM;AAClB,cAAM,eAAe;AAAA,MACtB;AACA,UAAI,QAAQ,OAAO,SAAS,KAAK,CAAC,GAAG;AACpC,cAAM,UAAU;AAAA,MACjB;AAEA,WAAK,QAAQ;AACb,YAAM,IAAI,KAAK,OAAO,OAAQ,KAAK,OAAO,OAAO,OAAO,IAAI;AAE5D,UAAI,QAAQ,GAAG;AACd;AAAA,MACD;AAEA,YAAM,aAAa,OAAO;AAC1B,UAAI,IAAI,MAAM,SAAS,UAAU,GAAG;AACnC,cAAM,UAAU;AAAA,MACjB;AAEA,WAAK;AAAA,IAEN;AAEA,UAAM,MAAM,OAAO,SAAS;AAC5B,WAAO,MAAM,IAAI,MAAM,KAAK,QAAQ,CAAC;AAIrC,QAAI,MAAM,IAAI,GAAG,IAAI,SAAS,GAAG;AAChC,YAAM,UAAU;AAAA,IACjB;AAEA,SAAK,MAAM,IAAI,GAAG;AAClB,SAAK;AAGL,WAAO,OAAO,KAAK,GAAG,CAAC;AAAA,EAExB;AAEA,SAAO,OAAO,cAAc,GAAG,MAAM;AACtC;AASA,IAAMC,UAAS,SAAS,OAAO;AAC9B,QAAM,SAAS,CAAC;AAGhB,UAAQ,WAAW,KAAK;AAGxB,QAAM,cAAc,MAAM;AAG1B,MAAI,IAAI;AACR,MAAI,QAAQ;AACZ,MAAI,OAAO;AAGX,aAAW,gBAAgB,OAAO;AACjC,QAAI,eAAe,KAAM;AACxB,aAAO,KAAK,mBAAmB,YAAY,CAAC;AAAA,IAC7C;AAAA,EACD;AAEA,QAAM,cAAc,OAAO;AAC3B,MAAI,iBAAiB;AAMrB,MAAI,aAAa;AAChB,WAAO,KAAK,SAAS;AAAA,EACtB;AAGA,SAAO,iBAAiB,aAAa;AAIpC,QAAI,IAAI;AACR,eAAW,gBAAgB,OAAO;AACjC,UAAI,gBAAgB,KAAK,eAAe,GAAG;AAC1C,YAAI;AAAA,MACL;AAAA,IACD;AAIA,UAAM,wBAAwB,iBAAiB;AAC/C,QAAI,IAAI,IAAI,OAAO,SAAS,SAAS,qBAAqB,GAAG;AAC5D,YAAM,UAAU;AAAA,IACjB;AAEA,cAAU,IAAI,KAAK;AACnB,QAAI;AAEJ,eAAW,gBAAgB,OAAO;AACjC,UAAI,eAAe,KAAK,EAAE,QAAQ,QAAQ;AACzC,cAAM,UAAU;AAAA,MACjB;AACA,UAAI,iBAAiB,GAAG;AAEvB,YAAI,IAAI;AACR,iBAAS,IAAI,QAA0B,KAAK,MAAM;AACjD,gBAAM,IAAI,KAAK,OAAO,OAAQ,KAAK,OAAO,OAAO,OAAO,IAAI;AAC5D,cAAI,IAAI,GAAG;AACV;AAAA,UACD;AACA,gBAAM,UAAU,IAAI;AACpB,gBAAM,aAAa,OAAO;AAC1B,iBAAO;AAAA,YACN,mBAAmB,aAAa,IAAI,UAAU,YAAY,CAAC,CAAC;AAAA,UAC7D;AACA,cAAI,MAAM,UAAU,UAAU;AAAA,QAC/B;AAEA,eAAO,KAAK,mBAAmB,aAAa,GAAG,CAAC,CAAC,CAAC;AAClD,eAAO,MAAM,OAAO,uBAAuB,mBAAmB,WAAW;AACzE,gBAAQ;AACR,UAAE;AAAA,MACH;AAAA,IACD;AAEA,MAAE;AACF,MAAE;AAAA,EAEH;AACA,SAAO,OAAO,KAAK,EAAE;AACtB;AAaA,IAAM,YAAY,SAAS,OAAO;AACjC,SAAO,UAAU,OAAO,SAAS,QAAQ;AACxC,WAAO,cAAc,KAAK,MAAM,IAC7BD,QAAO,OAAO,MAAM,CAAC,EAAE,YAAY,CAAC,IACpC;AAAA,EACJ,CAAC;AACF;AAaA,IAAM,UAAU,SAAS,OAAO;AAC/B,SAAO,UAAU,OAAO,SAAS,QAAQ;AACxC,WAAO,cAAc,KAAK,MAAM,IAC7B,SAASC,QAAO,MAAM,IACtB;AAAA,EACJ,CAAC;AACF;AAKA,IAAM,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhB,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQX,QAAQ;AAAA,IACP,UAAU;AAAA,IACV,UAAU;AAAA,EACX;AAAA,EACA,UAAUD;AAAA,EACV,UAAUC;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AACd;AAGA,IAAO,uBAAQ;;;ACzbf,IAAO,kBAAQ;AAAA,EACb,SAAS;AAAA;AAAA,IAEP,MAAM;AAAA;AAAA,IAGN,UAAU;AAAA;AAAA,IAGV,QAAQ;AAAA;AAAA,IAGR,YAAY;AAAA;AAAA,IAGZ,SAAS;AAAA;AAAA,IAGT,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOb,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQR,WAAW;AAAA;AAAA,IAGX,YAAY;AAAA,EACd;AAAA,EAEA,YAAY;AAAA,IACV,MAAM,CAAC;AAAA,IACP,OAAO,CAAC;AAAA,IACR,QAAQ,CAAC;AAAA,EACX;AACF;;;AC3CA,IAAO,eAAQ;AAAA,EACb,SAAS;AAAA;AAAA,IAEP,MAAM;AAAA;AAAA,IAGN,UAAU;AAAA;AAAA,IAGV,QAAQ;AAAA;AAAA,IAGR,YAAY;AAAA;AAAA,IAGZ,SAAS;AAAA;AAAA,IAGT,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOb,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQR,WAAW;AAAA;AAAA,IAGX,YAAY;AAAA,EACd;AAAA,EAEA,YAAY;AAAA,IAEV,MAAM;AAAA,MACJ,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IAEA,OAAO;AAAA,MACL,OAAO;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA,IAEA,QAAQ;AAAA,MACN,OAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACnEA,IAAO,qBAAQ;AAAA,EACb,SAAS;AAAA;AAAA,IAEP,MAAM;AAAA;AAAA,IAGN,UAAU;AAAA;AAAA,IAGV,QAAQ;AAAA;AAAA,IAGR,YAAY;AAAA;AAAA,IAGZ,SAAS;AAAA;AAAA,IAGT,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOb,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQR,WAAW;AAAA;AAAA,IAGX,YAAY;AAAA,EACd;AAAA,EAEA,YAAY;AAAA,IAEV,MAAM;AAAA,MACJ,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IAEA,OAAO;AAAA,MACL,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IAEA,QAAQ;AAAA,MACN,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACvEA,IAAM,SAAS;AAAA,EACb,SAAS;AAAA,EACT,MAAM;AAAA,EACN,YAAY;AACd;AAUA,IAAM,eAAe;AACrB,IAAM,eAAe;AAErB,SAAS,aAAc,KAAK;AAE1B,QAAM,MAAM,IAAI,KAAK,EAAE,YAAY;AAEnC,SAAO,aAAa,KAAK,GAAG,IAAI,aAAa,KAAK,GAAG,IAAI;AAC3D;AAEA,IAAM,sBAAsB,CAAC,SAAS,UAAU,SAAS;AAEzD,SAAS,cAAe,KAAK;AAC3B,QAAM,SAAe,cAAM,KAAK,IAAI;AAEpC,MAAI,OAAO,UAAU;AAOnB,QAAI,CAAC,OAAO,YAAY,oBAAoB,QAAQ,OAAO,QAAQ,KAAK,GAAG;AACzE,UAAI;AACF,eAAO,WAAW,qBAAS,QAAQ,OAAO,QAAQ;AAAA,MACpD,SAAS,IAAI;AAAA,MAAO;AAAA,IACtB;AAAA,EACF;AAEA,SAAa,eAAa,OAAO,MAAM,CAAC;AAC1C;AAEA,SAAS,kBAAmB,KAAK;AAC/B,QAAM,SAAe,cAAM,KAAK,IAAI;AAEpC,MAAI,OAAO,UAAU;AAOnB,QAAI,CAAC,OAAO,YAAY,oBAAoB,QAAQ,OAAO,QAAQ,KAAK,GAAG;AACzE,UAAI;AACF,eAAO,WAAW,qBAAS,UAAU,OAAO,QAAQ;AAAA,MACtD,SAAS,IAAI;AAAA,MAAO;AAAA,IACtB;AAAA,EACF;AAGA,SAAa,eAAa,OAAO,MAAM,GAAS,eAAO,eAAe,GAAG;AAC3E;AAuIA,SAAS,WAAY,YAAY,SAAS;AACxC,MAAI,EAAE,gBAAgB,aAAa;AACjC,WAAO,IAAI,WAAW,YAAY,OAAO;AAAA,EAC3C;AAEA,MAAI,CAAC,SAAS;AACZ,QAAI,CAAO,SAAS,UAAU,GAAG;AAC/B,gBAAU,cAAc,CAAC;AACzB,mBAAa;AAAA,IACf;AAAA,EACF;AASA,OAAK,SAAS,IAAI,sBAAa;AAS/B,OAAK,QAAQ,IAAI,qBAAY;AAS7B,OAAK,OAAO,IAAI,oBAAW;AAuB3B,OAAK,WAAW,IAAI,iBAAS;AAS7B,OAAK,UAAU,IAAI,mBAAU;AAiB7B,OAAK,eAAe;AAQpB,OAAK,gBAAgB;AAOrB,OAAK,oBAAoB;AAUzB,OAAK,QAAQ;AAQb,OAAK,UAAgB,OAAO,CAAC,GAAG,eAAO;AAEvC,OAAK,UAAU,CAAC;AAChB,OAAK,UAAU,UAAU;AAEzB,MAAI,SAAS;AAAE,SAAK,IAAI,OAAO;AAAA,EAAE;AACnC;AAqBA,WAAW,UAAU,MAAM,SAAU,SAAS;AAC5C,EAAM,OAAO,KAAK,SAAS,OAAO;AAClC,SAAO;AACT;AAYA,WAAW,UAAU,YAAY,SAAU,SAAS;AAClD,QAAM,OAAO;AAEb,MAAU,SAAS,OAAO,GAAG;AAC3B,UAAM,aAAa;AACnB,cAAU,OAAO,UAAU;AAC3B,QAAI,CAAC,SAAS;AAAE,YAAM,IAAI,MAAM,iCAAiC,aAAa,eAAe;AAAA,IAAE;AAAA,EACjG;AAEA,MAAI,CAAC,SAAS;AAAE,UAAM,IAAI,MAAM,4CAA6C;AAAA,EAAE;AAE/E,MAAI,QAAQ,SAAS;AAAE,SAAK,IAAI,QAAQ,OAAO;AAAA,EAAE;AAEjD,MAAI,QAAQ,YAAY;AACtB,WAAO,KAAK,QAAQ,UAAU,EAAE,QAAQ,SAAU,MAAM;AACtD,UAAI,QAAQ,WAAW,IAAI,EAAE,OAAO;AAClC,aAAK,IAAI,EAAE,MAAM,WAAW,QAAQ,WAAW,IAAI,EAAE,KAAK;AAAA,MAC5D;AACA,UAAI,QAAQ,WAAW,IAAI,EAAE,QAAQ;AACnC,aAAK,IAAI,EAAE,OAAO,WAAW,QAAQ,WAAW,IAAI,EAAE,MAAM;AAAA,MAC9D;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAmBA,WAAW,UAAU,SAAS,SAAUC,OAAM,eAAe;AAC3D,MAAI,SAAS,CAAC;AAEd,MAAI,CAAC,MAAM,QAAQA,KAAI,GAAG;AAAE,IAAAA,QAAO,CAACA,KAAI;AAAA,EAAE;AAE1C,GAAC,QAAQ,SAAS,QAAQ,EAAE,QAAQ,SAAU,OAAO;AACnD,aAAS,OAAO,OAAO,KAAK,KAAK,EAAE,MAAM,OAAOA,OAAM,IAAI,CAAC;AAAA,EAC7D,GAAG,IAAI;AAEP,WAAS,OAAO,OAAO,KAAK,OAAO,OAAO,OAAOA,OAAM,IAAI,CAAC;AAE5D,QAAM,SAASA,MAAK,OAAO,SAAU,MAAM;AAAE,WAAO,OAAO,QAAQ,IAAI,IAAI;AAAA,EAAE,CAAC;AAE9E,MAAI,OAAO,UAAU,CAAC,eAAe;AACnC,UAAM,IAAI,MAAM,mDAAmD,MAAM;AAAA,EAC3E;AAEA,SAAO;AACT;AASA,WAAW,UAAU,UAAU,SAAUA,OAAM,eAAe;AAC5D,MAAI,SAAS,CAAC;AAEd,MAAI,CAAC,MAAM,QAAQA,KAAI,GAAG;AAAE,IAAAA,QAAO,CAACA,KAAI;AAAA,EAAE;AAE1C,GAAC,QAAQ,SAAS,QAAQ,EAAE,QAAQ,SAAU,OAAO;AACnD,aAAS,OAAO,OAAO,KAAK,KAAK,EAAE,MAAM,QAAQA,OAAM,IAAI,CAAC;AAAA,EAC9D,GAAG,IAAI;AAEP,WAAS,OAAO,OAAO,KAAK,OAAO,OAAO,QAAQA,OAAM,IAAI,CAAC;AAE7D,QAAM,SAASA,MAAK,OAAO,SAAU,MAAM;AAAE,WAAO,OAAO,QAAQ,IAAI,IAAI;AAAA,EAAE,CAAC;AAE9E,MAAI,OAAO,UAAU,CAAC,eAAe;AACnC,UAAM,IAAI,MAAM,oDAAoD,MAAM;AAAA,EAC5E;AACA,SAAO;AACT;AAkBA,WAAW,UAAU,MAAM,SAAU,QAA2B;AAC9D,QAAM,OAAO,CAAC,IAAI,EAAE,OAAO,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC,CAAC;AACnE,SAAO,MAAM,QAAQ,IAAI;AACzB,SAAO;AACT;AAiBA,WAAW,UAAU,QAAQ,SAAU,KAAK,KAAK;AAC/C,MAAI,OAAO,QAAQ,UAAU;AAC3B,UAAM,IAAI,MAAM,+BAA+B;AAAA,EACjD;AAEA,QAAM,QAAQ,IAAI,KAAK,KAAK,MAAM,KAAK,MAAM,GAAG;AAEhD,OAAK,KAAK,QAAQ,KAAK;AAEvB,SAAO,MAAM;AACf;AAaA,WAAW,UAAU,SAAS,SAAU,KAAK,KAAK;AAChD,QAAM,OAAO,CAAC;AAEd,SAAO,KAAK,SAAS,OAAO,KAAK,MAAM,KAAK,GAAG,GAAG,KAAK,SAAS,GAAG;AACrE;AAWA,WAAW,UAAU,cAAc,SAAU,KAAK,KAAK;AACrD,QAAM,QAAQ,IAAI,KAAK,KAAK,MAAM,KAAK,MAAM,GAAG;AAEhD,QAAM,aAAa;AACnB,OAAK,KAAK,QAAQ,KAAK;AAEvB,SAAO,MAAM;AACf;AAUA,WAAW,UAAU,eAAe,SAAU,KAAK,KAAK;AACtD,QAAM,OAAO,CAAC;AAEd,SAAO,KAAK,SAAS,OAAO,KAAK,YAAY,KAAK,GAAG,GAAG,KAAK,SAAS,GAAG;AAC3E;AAEA,IAAO,cAAQ;", "names": ["fromCodePoint", "code", "regex_default", "regex_default", "regex_default", "regex_default", "regex_default", "regex_default", "_a", "CharCodes", "BinTrieFlags", "code", "EntityDecoderState", "DecodingMode", "errors", "base", "_a", "map", "escape", "match", "EntityLevel", "EncodingMode", "fromCodePoint", "match", "code", "entity", "regex_default", "code", "code", "list", "text", "match", "text", "code", "nextLine", "pos", "max", "_rules", "linkify", "match", "link", "escape", "postProcess", "code", "code", "isLinkOpen", "isLinkClose", "match", "match", "code", "fromCodePoint", "_rules", "linkify", "escape", "_rules2", "regex_default", "assign", "_class", "isString", "escapeRE", "text", "match", "tlds", "list", "normalize", "decode", "encode", "list"]}