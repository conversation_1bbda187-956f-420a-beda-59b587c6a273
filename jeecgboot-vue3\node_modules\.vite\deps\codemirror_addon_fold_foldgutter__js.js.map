{"version": 3, "sources": ["../../.pnpm/codemirror@5.65.18/node_modules/codemirror/addon/fold/foldgutter.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"), require(\"./foldcode\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\", \"./foldcode\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  CodeMirror.defineOption(\"foldGutter\", false, function(cm, val, old) {\n    if (old && old != CodeMirror.Init) {\n      cm.clearGutter(cm.state.foldGutter.options.gutter);\n      cm.state.foldGutter = null;\n      cm.off(\"gutterClick\", onGutterClick);\n      cm.off(\"changes\", onChange);\n      cm.off(\"viewportChange\", onViewportChange);\n      cm.off(\"fold\", onFold);\n      cm.off(\"unfold\", onFold);\n      cm.off(\"swapDoc\", onChange);\n      cm.off(\"optionChange\", optionChange);\n    }\n    if (val) {\n      cm.state.foldGutter = new State(parseOptions(val));\n      updateInViewport(cm);\n      cm.on(\"gutterClick\", onGutterClick);\n      cm.on(\"changes\", onChange);\n      cm.on(\"viewportChange\", onViewportChange);\n      cm.on(\"fold\", onFold);\n      cm.on(\"unfold\", onFold);\n      cm.on(\"swapDoc\", onChange);\n      cm.on(\"optionChange\", optionChange);\n    }\n  });\n\n  var Pos = CodeMirror.Pos;\n\n  function State(options) {\n    this.options = options;\n    this.from = this.to = 0;\n  }\n\n  function parseOptions(opts) {\n    if (opts === true) opts = {};\n    if (opts.gutter == null) opts.gutter = \"CodeMirror-foldgutter\";\n    if (opts.indicatorOpen == null) opts.indicatorOpen = \"CodeMirror-foldgutter-open\";\n    if (opts.indicatorFolded == null) opts.indicatorFolded = \"CodeMirror-foldgutter-folded\";\n    return opts;\n  }\n\n  function isFolded(cm, line) {\n    var marks = cm.findMarks(Pos(line, 0), Pos(line + 1, 0));\n    for (var i = 0; i < marks.length; ++i) {\n      if (marks[i].__isFold) {\n        var fromPos = marks[i].find(-1);\n        if (fromPos && fromPos.line === line)\n          return marks[i];\n      }\n    }\n  }\n\n  function marker(spec) {\n    if (typeof spec == \"string\") {\n      var elt = document.createElement(\"div\");\n      elt.className = spec + \" CodeMirror-guttermarker-subtle\";\n      return elt;\n    } else {\n      return spec.cloneNode(true);\n    }\n  }\n\n  function updateFoldInfo(cm, from, to) {\n    var opts = cm.state.foldGutter.options, cur = from - 1;\n    var minSize = cm.foldOption(opts, \"minFoldSize\");\n    var func = cm.foldOption(opts, \"rangeFinder\");\n    // we can reuse the built-in indicator element if its className matches the new state\n    var clsFolded = typeof opts.indicatorFolded == \"string\" && classTest(opts.indicatorFolded);\n    var clsOpen = typeof opts.indicatorOpen == \"string\" && classTest(opts.indicatorOpen);\n    cm.eachLine(from, to, function(line) {\n      ++cur;\n      var mark = null;\n      var old = line.gutterMarkers;\n      if (old) old = old[opts.gutter];\n      if (isFolded(cm, cur)) {\n        if (clsFolded && old && clsFolded.test(old.className)) return;\n        mark = marker(opts.indicatorFolded);\n      } else {\n        var pos = Pos(cur, 0);\n        var range = func && func(cm, pos);\n        if (range && range.to.line - range.from.line >= minSize) {\n          if (clsOpen && old && clsOpen.test(old.className)) return;\n          mark = marker(opts.indicatorOpen);\n        }\n      }\n      if (!mark && !old) return;\n      cm.setGutterMarker(line, opts.gutter, mark);\n    });\n  }\n\n  // copied from CodeMirror/src/util/dom.js\n  function classTest(cls) { return new RegExp(\"(^|\\\\s)\" + cls + \"(?:$|\\\\s)\\\\s*\") }\n\n  function updateInViewport(cm) {\n    var vp = cm.getViewport(), state = cm.state.foldGutter;\n    if (!state) return;\n    cm.operation(function() {\n      updateFoldInfo(cm, vp.from, vp.to);\n    });\n    state.from = vp.from; state.to = vp.to;\n  }\n\n  function onGutterClick(cm, line, gutter) {\n    var state = cm.state.foldGutter;\n    if (!state) return;\n    var opts = state.options;\n    if (gutter != opts.gutter) return;\n    var folded = isFolded(cm, line);\n    if (folded) folded.clear();\n    else cm.foldCode(Pos(line, 0), opts);\n  }\n\n  function optionChange(cm, option) {\n    if (option == \"mode\") onChange(cm)\n  }\n\n  function onChange(cm) {\n    var state = cm.state.foldGutter;\n    if (!state) return;\n    var opts = state.options;\n    state.from = state.to = 0;\n    clearTimeout(state.changeUpdate);\n    state.changeUpdate = setTimeout(function() { updateInViewport(cm); }, opts.foldOnChangeTimeSpan || 600);\n  }\n\n  function onViewportChange(cm) {\n    var state = cm.state.foldGutter;\n    if (!state) return;\n    var opts = state.options;\n    clearTimeout(state.changeUpdate);\n    state.changeUpdate = setTimeout(function() {\n      var vp = cm.getViewport();\n      if (state.from == state.to || vp.from - state.to > 20 || state.from - vp.to > 20) {\n        updateInViewport(cm);\n      } else {\n        cm.operation(function() {\n          if (vp.from < state.from) {\n            updateFoldInfo(cm, vp.from, state.from);\n            state.from = vp.from;\n          }\n          if (vp.to > state.to) {\n            updateFoldInfo(cm, state.to, vp.to);\n            state.to = vp.to;\n          }\n        });\n      }\n    }, opts.updateViewportTimeSpan || 400);\n  }\n\n  function onFold(cm, from) {\n    var state = cm.state.foldGutter;\n    if (!state) return;\n    var line = from.line;\n    if (line >= state.from && line < state.to)\n      updateFoldInfo(cm, line, line + 1);\n  }\n});\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,sBAAiC,kBAAqB;AAAA,eACnD,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,wBAAwB,YAAY,GAAG,GAAG;AAAA;AAElD,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACtB;AAEA,MAAAA,YAAW,aAAa,cAAc,OAAO,SAAS,IAAI,KAAK,KAAK;AAClE,YAAI,OAAO,OAAOA,YAAW,MAAM;AACjC,aAAG,YAAY,GAAG,MAAM,WAAW,QAAQ,MAAM;AACjD,aAAG,MAAM,aAAa;AACtB,aAAG,IAAI,eAAe,aAAa;AACnC,aAAG,IAAI,WAAW,QAAQ;AAC1B,aAAG,IAAI,kBAAkB,gBAAgB;AACzC,aAAG,IAAI,QAAQ,MAAM;AACrB,aAAG,IAAI,UAAU,MAAM;AACvB,aAAG,IAAI,WAAW,QAAQ;AAC1B,aAAG,IAAI,gBAAgB,YAAY;AAAA,QACrC;AACA,YAAI,KAAK;AACP,aAAG,MAAM,aAAa,IAAI,MAAM,aAAa,GAAG,CAAC;AACjD,2BAAiB,EAAE;AACnB,aAAG,GAAG,eAAe,aAAa;AAClC,aAAG,GAAG,WAAW,QAAQ;AACzB,aAAG,GAAG,kBAAkB,gBAAgB;AACxC,aAAG,GAAG,QAAQ,MAAM;AACpB,aAAG,GAAG,UAAU,MAAM;AACtB,aAAG,GAAG,WAAW,QAAQ;AACzB,aAAG,GAAG,gBAAgB,YAAY;AAAA,QACpC;AAAA,MACF,CAAC;AAED,UAAI,MAAMA,YAAW;AAErB,eAAS,MAAM,SAAS;AACtB,aAAK,UAAU;AACf,aAAK,OAAO,KAAK,KAAK;AAAA,MACxB;AAEA,eAAS,aAAa,MAAM;AAC1B,YAAI,SAAS,KAAM,QAAO,CAAC;AAC3B,YAAI,KAAK,UAAU,KAAM,MAAK,SAAS;AACvC,YAAI,KAAK,iBAAiB,KAAM,MAAK,gBAAgB;AACrD,YAAI,KAAK,mBAAmB,KAAM,MAAK,kBAAkB;AACzD,eAAO;AAAA,MACT;AAEA,eAAS,SAAS,IAAI,MAAM;AAC1B,YAAI,QAAQ,GAAG,UAAU,IAAI,MAAM,CAAC,GAAG,IAAI,OAAO,GAAG,CAAC,CAAC;AACvD,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACrC,cAAI,MAAM,CAAC,EAAE,UAAU;AACrB,gBAAI,UAAU,MAAM,CAAC,EAAE,KAAK,EAAE;AAC9B,gBAAI,WAAW,QAAQ,SAAS;AAC9B,qBAAO,MAAM,CAAC;AAAA,UAClB;AAAA,QACF;AAAA,MACF;AAEA,eAAS,OAAO,MAAM;AACpB,YAAI,OAAO,QAAQ,UAAU;AAC3B,cAAI,MAAM,SAAS,cAAc,KAAK;AACtC,cAAI,YAAY,OAAO;AACvB,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,KAAK,UAAU,IAAI;AAAA,QAC5B;AAAA,MACF;AAEA,eAAS,eAAe,IAAI,MAAM,IAAI;AACpC,YAAI,OAAO,GAAG,MAAM,WAAW,SAAS,MAAM,OAAO;AACrD,YAAI,UAAU,GAAG,WAAW,MAAM,aAAa;AAC/C,YAAI,OAAO,GAAG,WAAW,MAAM,aAAa;AAE5C,YAAI,YAAY,OAAO,KAAK,mBAAmB,YAAY,UAAU,KAAK,eAAe;AACzF,YAAI,UAAU,OAAO,KAAK,iBAAiB,YAAY,UAAU,KAAK,aAAa;AACnF,WAAG,SAAS,MAAM,IAAI,SAAS,MAAM;AACnC,YAAE;AACF,cAAI,OAAO;AACX,cAAI,MAAM,KAAK;AACf,cAAI,IAAK,OAAM,IAAI,KAAK,MAAM;AAC9B,cAAI,SAAS,IAAI,GAAG,GAAG;AACrB,gBAAI,aAAa,OAAO,UAAU,KAAK,IAAI,SAAS,EAAG;AACvD,mBAAO,OAAO,KAAK,eAAe;AAAA,UACpC,OAAO;AACL,gBAAI,MAAM,IAAI,KAAK,CAAC;AACpB,gBAAI,QAAQ,QAAQ,KAAK,IAAI,GAAG;AAChC,gBAAI,SAAS,MAAM,GAAG,OAAO,MAAM,KAAK,QAAQ,SAAS;AACvD,kBAAI,WAAW,OAAO,QAAQ,KAAK,IAAI,SAAS,EAAG;AACnD,qBAAO,OAAO,KAAK,aAAa;AAAA,YAClC;AAAA,UACF;AACA,cAAI,CAAC,QAAQ,CAAC,IAAK;AACnB,aAAG,gBAAgB,MAAM,KAAK,QAAQ,IAAI;AAAA,QAC5C,CAAC;AAAA,MACH;AAGA,eAAS,UAAU,KAAK;AAAE,eAAO,IAAI,OAAO,YAAY,MAAM,eAAe;AAAA,MAAE;AAE/E,eAAS,iBAAiB,IAAI;AAC5B,YAAI,KAAK,GAAG,YAAY,GAAG,QAAQ,GAAG,MAAM;AAC5C,YAAI,CAAC,MAAO;AACZ,WAAG,UAAU,WAAW;AACtB,yBAAe,IAAI,GAAG,MAAM,GAAG,EAAE;AAAA,QACnC,CAAC;AACD,cAAM,OAAO,GAAG;AAAM,cAAM,KAAK,GAAG;AAAA,MACtC;AAEA,eAAS,cAAc,IAAI,MAAM,QAAQ;AACvC,YAAI,QAAQ,GAAG,MAAM;AACrB,YAAI,CAAC,MAAO;AACZ,YAAI,OAAO,MAAM;AACjB,YAAI,UAAU,KAAK,OAAQ;AAC3B,YAAI,SAAS,SAAS,IAAI,IAAI;AAC9B,YAAI,OAAQ,QAAO,MAAM;AAAA,YACpB,IAAG,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI;AAAA,MACrC;AAEA,eAAS,aAAa,IAAI,QAAQ;AAChC,YAAI,UAAU,OAAQ,UAAS,EAAE;AAAA,MACnC;AAEA,eAAS,SAAS,IAAI;AACpB,YAAI,QAAQ,GAAG,MAAM;AACrB,YAAI,CAAC,MAAO;AACZ,YAAI,OAAO,MAAM;AACjB,cAAM,OAAO,MAAM,KAAK;AACxB,qBAAa,MAAM,YAAY;AAC/B,cAAM,eAAe,WAAW,WAAW;AAAE,2BAAiB,EAAE;AAAA,QAAG,GAAG,KAAK,wBAAwB,GAAG;AAAA,MACxG;AAEA,eAAS,iBAAiB,IAAI;AAC5B,YAAI,QAAQ,GAAG,MAAM;AACrB,YAAI,CAAC,MAAO;AACZ,YAAI,OAAO,MAAM;AACjB,qBAAa,MAAM,YAAY;AAC/B,cAAM,eAAe,WAAW,WAAW;AACzC,cAAI,KAAK,GAAG,YAAY;AACxB,cAAI,MAAM,QAAQ,MAAM,MAAM,GAAG,OAAO,MAAM,KAAK,MAAM,MAAM,OAAO,GAAG,KAAK,IAAI;AAChF,6BAAiB,EAAE;AAAA,UACrB,OAAO;AACL,eAAG,UAAU,WAAW;AACtB,kBAAI,GAAG,OAAO,MAAM,MAAM;AACxB,+BAAe,IAAI,GAAG,MAAM,MAAM,IAAI;AACtC,sBAAM,OAAO,GAAG;AAAA,cAClB;AACA,kBAAI,GAAG,KAAK,MAAM,IAAI;AACpB,+BAAe,IAAI,MAAM,IAAI,GAAG,EAAE;AAClC,sBAAM,KAAK,GAAG;AAAA,cAChB;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,GAAG,KAAK,0BAA0B,GAAG;AAAA,MACvC;AAEA,eAAS,OAAO,IAAI,MAAM;AACxB,YAAI,QAAQ,GAAG,MAAM;AACrB,YAAI,CAAC,MAAO;AACZ,YAAI,OAAO,KAAK;AAChB,YAAI,QAAQ,MAAM,QAAQ,OAAO,MAAM;AACrC,yBAAe,IAAI,MAAM,OAAO,CAAC;AAAA,MACrC;AAAA,IACF,CAAC;AAAA;AAAA;", "names": ["CodeMirror"]}