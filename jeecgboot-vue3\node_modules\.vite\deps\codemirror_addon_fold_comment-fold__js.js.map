{"version": 3, "sources": ["../../.pnpm/codemirror@5.65.18/node_modules/codemirror/addon/fold/comment-fold.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n\"use strict\";\n\nCodeMirror.registerGlobalHelper(\"fold\", \"comment\", function(mode) {\n  return mode.blockCommentStart && mode.blockCommentEnd;\n}, function(cm, start) {\n  var mode = cm.getModeAt(start), startToken = mode.blockCommentStart, endToken = mode.blockCommentEnd;\n  if (!startToken || !endToken) return;\n  var line = start.line, lineText = cm.getLine(line);\n\n  var startCh;\n  for (var at = start.ch, pass = 0;;) {\n    var found = at <= 0 ? -1 : lineText.lastIndexOf(startToken, at - 1);\n    if (found == -1) {\n      if (pass == 1) return;\n      pass = 1;\n      at = lineText.length;\n      continue;\n    }\n    if (pass == 1 && found < start.ch) return;\n    if (/comment/.test(cm.getTokenTypeAt(CodeMirror.Pos(line, found + 1))) &&\n        (found == 0 || lineText.slice(found - endToken.length, found) == endToken ||\n         !/comment/.test(cm.getTokenTypeAt(CodeMirror.Pos(line, found))))) {\n      startCh = found + startToken.length;\n      break;\n    }\n    at = found - 1;\n  }\n\n  var depth = 1, lastLine = cm.lastLine(), end, endCh;\n  outer: for (var i = line; i <= lastLine; ++i) {\n    var text = cm.getLine(i), pos = i == line ? startCh : 0;\n    for (;;) {\n      var nextOpen = text.indexOf(startToken, pos), nextClose = text.indexOf(endToken, pos);\n      if (nextOpen < 0) nextOpen = text.length;\n      if (nextClose < 0) nextClose = text.length;\n      pos = Math.min(nextOpen, nextClose);\n      if (pos == text.length) break;\n      if (pos == nextOpen) ++depth;\n      else if (!--depth) { end = i; endCh = pos; break outer; }\n      ++pos;\n    }\n  }\n  if (end == null || line == end && endCh == startCh) return;\n  return {from: CodeMirror.Pos(line, startCh),\n          to: CodeMirror.Pos(end, endCh)};\n});\n\n});\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACxB;AAEA,MAAAA,YAAW,qBAAqB,QAAQ,WAAW,SAAS,MAAM;AAChE,eAAO,KAAK,qBAAqB,KAAK;AAAA,MACxC,GAAG,SAAS,IAAI,OAAO;AACrB,YAAI,OAAO,GAAG,UAAU,KAAK,GAAG,aAAa,KAAK,mBAAmB,WAAW,KAAK;AACrF,YAAI,CAAC,cAAc,CAAC,SAAU;AAC9B,YAAI,OAAO,MAAM,MAAM,WAAW,GAAG,QAAQ,IAAI;AAEjD,YAAI;AACJ,iBAAS,KAAK,MAAM,IAAI,OAAO,OAAK;AAClC,cAAI,QAAQ,MAAM,IAAI,KAAK,SAAS,YAAY,YAAY,KAAK,CAAC;AAClE,cAAI,SAAS,IAAI;AACf,gBAAI,QAAQ,EAAG;AACf,mBAAO;AACP,iBAAK,SAAS;AACd;AAAA,UACF;AACA,cAAI,QAAQ,KAAK,QAAQ,MAAM,GAAI;AACnC,cAAI,UAAU,KAAK,GAAG,eAAeA,YAAW,IAAI,MAAM,QAAQ,CAAC,CAAC,CAAC,MAChE,SAAS,KAAK,SAAS,MAAM,QAAQ,SAAS,QAAQ,KAAK,KAAK,YAChE,CAAC,UAAU,KAAK,GAAG,eAAeA,YAAW,IAAI,MAAM,KAAK,CAAC,CAAC,IAAI;AACrE,sBAAU,QAAQ,WAAW;AAC7B;AAAA,UACF;AACA,eAAK,QAAQ;AAAA,QACf;AAEA,YAAI,QAAQ,GAAG,WAAW,GAAG,SAAS,GAAG,KAAK;AAC9C,cAAO,UAAS,IAAI,MAAM,KAAK,UAAU,EAAE,GAAG;AAC5C,cAAI,OAAO,GAAG,QAAQ,CAAC,GAAG,MAAM,KAAK,OAAO,UAAU;AACtD,qBAAS;AACP,gBAAI,WAAW,KAAK,QAAQ,YAAY,GAAG,GAAG,YAAY,KAAK,QAAQ,UAAU,GAAG;AACpF,gBAAI,WAAW,EAAG,YAAW,KAAK;AAClC,gBAAI,YAAY,EAAG,aAAY,KAAK;AACpC,kBAAM,KAAK,IAAI,UAAU,SAAS;AAClC,gBAAI,OAAO,KAAK,OAAQ;AACxB,gBAAI,OAAO,SAAU,GAAE;AAAA,qBACd,CAAC,EAAE,OAAO;AAAE,oBAAM;AAAG,sBAAQ;AAAK,oBAAM;AAAA,YAAO;AACxD,cAAE;AAAA,UACJ;AAAA,QACF;AACA,YAAI,OAAO,QAAQ,QAAQ,OAAO,SAAS,QAAS;AACpD,eAAO;AAAA,UAAC,MAAMA,YAAW,IAAI,MAAM,OAAO;AAAA,UAClC,IAAIA,YAAW,IAAI,KAAK,KAAK;AAAA,QAAC;AAAA,MACxC,CAAC;AAAA,IAED,CAAC;AAAA;AAAA;", "names": ["CodeMirror"]}