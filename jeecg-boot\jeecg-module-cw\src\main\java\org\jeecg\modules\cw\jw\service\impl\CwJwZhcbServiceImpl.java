package org.jeecg.modules.cw.jw.service.impl;

import cn.hutool.core.bean.BeanUtil;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.cw.base.constants.CwCllDataName;
import org.jeecg.modules.cw.base.entity.CwCllycData;
import org.jeecg.modules.cw.base.entity.CwNameDict;
import org.jeecg.modules.cw.base.service.ICwCllDataService;
import org.jeecg.modules.cw.base.service.ICwCllycDataService;
import org.jeecg.modules.cw.base.service.ICwNameDictService;
import org.jeecg.modules.cw.jw.entity.CwJwDay;
import org.jeecg.modules.cw.jw.entity.CwJwMonth;
import org.jeecg.modules.cw.jw.entity.CwJwRow;
import org.jeecg.modules.cw.jw.mapper.CwJwDayMapper;
import org.jeecg.modules.cw.jw.param.CwJwZhcbSumbitParam;
import org.jeecg.modules.cw.jw.result.CwJwZhcbListResult;
import org.jeecg.modules.cw.jw.service.ICwJwDayService;
import org.jeecg.modules.cw.jw.service.ICwJwMonthService;
import org.jeecg.modules.cw.jw.service.ICwJwZhcbService;
import org.jeecg.modules.cw.krb.entity.CwKrbRow;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrStatisticsDayService;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import javax.annotation.PreDestroy;

/**
 * @Description: 精尾厂-日填报
 * @Author: jeecg-boot
 * @Date: 2024-12-06
 * @Version: V1.0
 */
@Service
@Slf4j
public class CwJwZhcbServiceImpl implements ICwJwZhcbService {

    private static final String DICT_TYPE = "jw";
    private static final String CACHE_KEY = "cw:jw:zhcb:";
    private static final String QUERY_CACHE_KEY = CACHE_KEY + "query";
    private static final String SUM_DRS_CACHE_KEY = CACHE_KEY + "sumDrs";
    private static final String SUM_BY_MONTH_CACHE_KEY = CACHE_KEY + "sumByMonth";
    private static final String NAME_JW = "jw";
    private static final String NAME_DS = "ds";
    private static final String NAME_SX = "sx";

    @Resource
    private CwJwDayMapper jwDayMapper;
    @Resource
    private ICwNameDictService nameDictService;
    @Resource
    private ICwJwDayService jwDayService;
    @Resource
    private ICwJwMonthService jwMonthService;
    @Resource
    private ICwCllDataService cwCllDataService;
    @Resource
    private ICwCllycDataService cwCllycDataService;
    @Lazy
    @Resource
    private ICwMnlrStatisticsDayService mnlrStatisticsDayService;

    // 不再在JW中反向触发SX

    private final ExecutorService drsRecalcExecutor = Executors.newSingleThreadExecutor(new ThreadFactory() {
        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r, "jw-drs-recalc");
            t.setDaemon(true);
            return t;
        }
    });

    @PreDestroy
    public void shutdownExecutor() { drsRecalcExecutor.shutdown(); }

    @Override
    public CwJwZhcbListResult query(Date queryDate) {
        // 1. 初始化结果对象
        CwJwZhcbListResult result = new CwJwZhcbListResult();
        result.setQueryDate(queryDate);
        
        // 2. 获取基础数据
        // 2.1 日期相关
        Date beginOfMonth = DateUtil.beginOfMonth(queryDate);
        Date endOfDay = DateUtil.endOfDay(queryDate);
        Date dayBeforeQuery = DateUtil.offsetDay(queryDate, -1);
        int currentDay = DateUtil.dayOfMonth(queryDate);
        boolean isFirstHalf = currentDay <= 15;
        result.setIsFirstHalf(isFirstHalf);
        
        // 2.2 项目字典
        List<CwNameDict> dict = nameDictService.queryList(DICT_TYPE);

        // 2.2.1 获取 cllyc 用于自动补全缺失日数据
        BigDecimal monthCllyc = null;
        {
            Date monthBegin = DateUtil.beginOfMonth(queryDate);
            CwCllycData cd = cwCllycDataService.lambdaQuery()
                    .eq(CwCllycData::getRecordTime, monthBegin)
                    .eq(CwCllycData::getName, NAME_JW)
                    .one();
            if (ObjectUtil.isNotEmpty(cd)) monthCllyc = cd.getCllyc();
        }
        
        // 2.3 日表和月表数据
        List<CwJwDay> allDaysInMonth = jwDayService.lambdaQuery()
                .between(CwJwDay::getRecordTime, beginOfMonth, endOfDay)
                .list();
        List<CwJwMonth> months = jwMonthService.lambdaQuery()
                .ge(CwJwMonth::getRecordTime, beginOfMonth)
                .le(CwJwMonth::getRecordTime, DateUtil.endOfMonth(queryDate))
                .list();
        // 当月是否存在任意项目的月预测
        boolean monthForecastExistsForService = months.stream().anyMatch(m -> m.getSecondHalfForecast() != null);

        if (ObjectUtil.isNotEmpty(monthCllyc) && monthCllyc.compareTo(BigDecimal.ZERO) > 0) {
            List<CwJwDay> fill = autoFillMissingDays(beginOfMonth, queryDate, dict, monthCllyc, months);
            if (ObjectUtil.isNotEmpty(fill)) allDaysInMonth.addAll(fill);
        }
        
        // 将日数据按名称分组
        Map<String, List<CwJwDay>> daysByName = allDaysInMonth.stream().collect(Collectors.groupingBy(CwJwDay::getName));
        
        // 3. 构建结果行数据
        List<CwJwRow> resRows = new ArrayList<>();
        
        for (CwNameDict d : dict) {
            CwJwRow row = new CwJwRow();
            BeanUtil.copyProperties(d, row);
            
            // 3.1 填充月数据
            months.stream()
                  .filter(m -> d.getName().equals(m.getName()))
                  .findFirst()
                  .ifPresent(m -> fillMonthData(row, m, isFirstHalf, monthForecastExistsForService));

            List<CwJwDay> itemDays = daysByName.get(d.getName());
            if (ObjectUtil.isNotEmpty(itemDays)) {
                // 3.2 填充当日日数据和当日数
                itemDays.stream()
                        .filter(day -> DateUtil.isSameDay(queryDate, day.getRecordTime()))
                        .findFirst()
                        .ifPresent(day -> {
                            fillDayData(row, day);
                            if (day.getDrs() != null) {
                                row.setDrs(day.getDrs());
                            }
                        });

                // 3.4 计算月累计 (直接累加drs)
                BigDecimal ylj = itemDays.stream()
                        .filter(day -> !day.getRecordTime().after(dayBeforeQuery))
                        .map(CwJwDay::getDrs)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                row.setYlj(ylj);
            }
            
            resRows.add(row);
        }
        
        result.setRows(resRows);
        
        // 4. 设置额外数据
        // 4.1 处理量 - jw的处理量 = ds的处理量 + sx的处理量
        String todayKey = DateUtil.format(queryDate, "yyyy-MM-dd");
        BigDecimal jwCll = BigDecimal.ZERO;

        // 获取大山厂处理量
        Map<String, String> dsCllDataMap = cwCllDataService.getRangeCllData(CwCllDataName.DSCLL, queryDate, queryDate);
        if(ObjectUtil.isNotEmpty(dsCllDataMap)){
            String dsCllStr = dsCllDataMap.get(todayKey);
            if(ObjectUtil.isNotEmpty(dsCllStr)){
                jwCll = jwCll.add(new BigDecimal(dsCllStr));
            }
        }
        // 获取泗选厂处理量
        Map<String, String> sxCllDataMap = cwCllDataService.getRangeCllData(CwCllDataName.SZCLL, queryDate, queryDate);
        if(ObjectUtil.isNotEmpty(sxCllDataMap)){
            String sxCllStr = sxCllDataMap.get(todayKey);
            if(ObjectUtil.isNotEmpty(sxCllStr)){
                jwCll = jwCll.add(new BigDecimal(sxCllStr));
            }
        }
        result.setJwCll(jwCll);

        // 4.2 处理量预测 - jw的处理量预测 = ds的处理量预测 + sx的处理量预测
        BigDecimal totalCllyc = BigDecimal.ZERO;
        // 获取大山厂处理量预测
        CwCllycData dsCllycData = cwCllycDataService.lambdaQuery()
                .eq(CwCllycData::getRecordTime, beginOfMonth)
                .eq(CwCllycData::getName, NAME_DS)
                .one();
        if (ObjectUtil.isNotEmpty(dsCllycData) && ObjectUtil.isNotEmpty(dsCllycData.getCllyc())) {
            totalCllyc = totalCllyc.add(dsCllycData.getCllyc());
        }
        // 获取泗选厂处理量预测
        CwCllycData sxCllycData = cwCllycDataService.lambdaQuery()
                .eq(CwCllycData::getRecordTime, beginOfMonth)
                .eq(CwCllycData::getName, NAME_SX)
                .one();
        if (ObjectUtil.isNotEmpty(sxCllycData) && ObjectUtil.isNotEmpty(sxCllycData.getCllyc())) {
            totalCllyc = totalCllyc.add(sxCllycData.getCllyc());
        }
        result.setCllyc(totalCllyc);
        
        return result;
    }

    @Override
    public void submit(CwJwZhcbSumbitParam param) {
        Date submitDate = param.getSubmitDate();
        Date monthBegin = DateUtil.beginOfMonth(submitDate);
        
        // jw的处理量和处理量预测为计算值，此处不做保存
        
        // 更新日数据
        List<CwJwRow> rows = param.getRows();
        jwDayService.remove(new LambdaQueryWrapper<>(CwJwDay.class).eq(CwJwDay::getRecordTime, submitDate));
        
        // 获取已有的月数据
        List<CwJwMonth> existingMonths = jwMonthService.lambdaQuery()
                .ge(CwJwMonth::getRecordTime, DateUtil.beginOfMonth(submitDate))
                .le(CwJwMonth::getRecordTime, DateUtil.endOfMonth(submitDate))
                .list();
        Map<String, CwJwMonth> existingMonthMap = new HashMap<>();
        for (CwJwMonth month : existingMonths) {
            existingMonthMap.put(month.getName(), month);
        }
        
        ArrayList<CwJwDay> days = new ArrayList<>();
        for (CwJwRow row : rows) {
            CwJwDay day = new CwJwDay();
            BeanUtil.copyProperties(row, day);
            day.setRecordTime(submitDate);
            day.setDrs(BigDecimal.ZERO);
            days.add(day);
        }
        jwDayService.saveBatch(days);
        
        ArrayList<CwJwMonth> months = new ArrayList<>();
        Boolean isFirstHalf = param.getIsFirstHalf();
        boolean forecastChanged = false;
        
        for (CwJwRow row : rows) {
            CwJwMonth month;
            if (existingMonthMap.containsKey(row.getName())) {
                // 使用已存在的月数据
                month = existingMonthMap.get(row.getName());
                
                // 只更新当前半月的预测值，保留另一半月的预测值
                if (ObjectUtil.isNotEmpty(row.getYyc())) {
                    BigDecimal forecastValue = row.getYyc();
                    if (isFirstHalf != null) {
                        if (isFirstHalf) {
                            month.setFirstHalfForecast(forecastValue);
                        } else {
                            if (month.getSecondHalfForecast() == null || month.getSecondHalfForecast().compareTo(forecastValue) != 0) {
                                forecastChanged = true;
                            }
                            month.setSecondHalfForecast(forecastValue);
                        }
                    }
                }
                // 月预算变更也应触发整月重算
                if (ObjectUtil.isNotEmpty(row.getYys())) {
                    BigDecimal newYys = row.getYys();
                    if (month.getYys() == null || month.getYys().compareTo(newYys) != 0) {
                        forecastChanged = true;
                    }
                    month.setYys(newYys);
                }
                
                // 更新其他字段
                if (ObjectUtil.isNotEmpty(row.getPjdj())) {
                    month.setPjdj(row.getPjdj());
                }
                if (ObjectUtil.isNotEmpty(row.getYys())) {
                    month.setYys(row.getYys());
                }
            } else {
                // 创建新的月数据
                month = new CwJwMonth();
                BeanUtil.copyProperties(row, month);
                month.setRecordTime(submitDate);
                
                // 根据isFirstHalf设置上半月或下半月预测值
                if (ObjectUtil.isNotEmpty(row.getYyc())) {
                    BigDecimal forecastValue = row.getYyc();
                    if (isFirstHalf != null) {
                        if (isFirstHalf) {
                            month.setFirstHalfForecast(forecastValue);
                        } else {
                            month.setSecondHalfForecast(forecastValue);
                            forecastChanged = true;
                        }
                    }
                }
                // 新建月记录时，如果提交了月预算，也视为月预测/预算变更
                if (ObjectUtil.isNotEmpty(row.getYys())) {
                    BigDecimal newYys = row.getYys();
                    if (month.getYys() == null || month.getYys().compareTo(newYys) != 0) {
                        forecastChanged = true;
                    }
                    month.setYys(newYys);
                }
            }
            months.add(month);
        }
        
        // 批量保存或更新月数据
        jwMonthService.saveOrUpdateBatch(months);

        // 重算当月
        this.recalculateDrsByDate(submitDate);
        Date endOfMonth = DateUtil.endOfMonth(submitDate);
        drsRecalcExecutor.submit(() -> recalculateDrs(monthBegin, endOfMonth));
    }

    @Override
    public BigDecimal sumDrs(Date queryDate) {
        // 1. 日期相关
        Date beginOfMonth = DateUtil.beginOfMonth(queryDate);
        Date endOfMonth = DateUtil.endOfMonth(queryDate);

        // 2. 数据准备
        List<CwJwDay> allDays = jwDayService.lambdaQuery()
                .between(CwJwDay::getRecordTime, beginOfMonth, endOfMonth)
                .list();

        // 3. 直接累加drs
        return allDays.stream()
                .map(CwJwDay::getDrs)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public List<CwKrbRow> sumByMonth(Date queryDate) {
        // 1. 初始化结果
        Map<String, CwKrbRow> result = CwKrbRow.takeDefaultKrbRows("精尾厂");

        // 2. 获取基础数据
        // 2.1 日期相关
        Date beginOfMonth = DateUtil.beginOfMonth(queryDate);
        Date endOfDay = DateUtil.endOfDay(queryDate);

        // 2.2 数据准备: 获取包含drs的日数据
        List<CwJwDay> allDays = jwDayService.lambdaQuery()
                .between(CwJwDay::getRecordTime, beginOfMonth, endOfDay)
                .list();
        List<CwJwMonth> months = jwMonthService.lambdaQuery()
                .ge(CwJwMonth::getRecordTime, beginOfMonth)
                .le(CwJwMonth::getRecordTime, DateUtil.endOfMonth(queryDate))
                .list();

        // 3. 定义计算分类行
        CwKrbRow clRow = result.get(CwKrbRow.CL);
        CwKrbRow bjRow = result.get(CwKrbRow.BJ);
        CwKrbRow rlRow = result.get(CwKrbRow.RL);
        CwKrbRow sRow = result.get(CwKrbRow.S);
        CwKrbRow dRow = result.get(CwKrbRow.D);
        CwKrbRow zzfyRow = result.get(CwKrbRow.ZZFY);

        // 4. 处理每天的数据
        for (CwJwDay day : allDays) {
            // 直接获取drs值
            BigDecimal calculatedValue = day.getDrs();

            // 如果drs不为空，则进行累加
            if (ObjectUtil.isNotEmpty(calculatedValue)) {
                // 只有当天的数据才计入当日数
                if (DateUtil.isSameDay(queryDate, day.getRecordTime())) {
                    addToDailyTotal(day.getType(), calculatedValue, clRow, bjRow, rlRow, sRow, dRow, zzfyRow);
                }

                // 所有当月数据都计入月累计
                addToMonthlyTotal(day.getType(), calculatedValue, clRow, bjRow, rlRow, sRow, dRow, zzfyRow);
            }
        }

        // 5. 处理月预算数据
        for (CwJwMonth month : months) {
            BigDecimal yys = month.getYys();
            if (ObjectUtil.isNotEmpty(month) && ObjectUtil.isNotEmpty(yys)) {
                addToBudget(month.getType(), yys, clRow, bjRow, rlRow, sRow, dRow, zzfyRow);
            }
        }

        return new ArrayList<>(result.values());
    }

    @Override
    public BigDecimal sumBudgetMonth(Date monthDate) {
        BigDecimal total = BigDecimal.ZERO;
        List<CwKrbRow> rows = this.sumByMonth(monthDate);
        if (rows != null) {
            for (CwKrbRow row : rows) {
                if (row != null && row.getYys() != null) {
                    total = total.add(row.getYys());
                }
            }
        }
        return total;
    }

    @Override
    public CwJwZhcbListResult autoFill(Date queryDate) {
        this.recalculateDrsByDate(queryDate);
        return query(queryDate);
    }
    
    @Override
    public void recalculateDrsByDate(Date date) {
        log.info("开始重新计算精尾厂{}的drs", DateUtil.formatDate(date));
        recalculateDrs(date, date);
        log.info("结束重新计算精尾厂{}的drs", DateUtil.formatDate(date));
    }

    @Override
    public void recalculateAllDrs() {
        log.info("开始重新计算精尾厂所有的drs");
        // 获取所有有记录的日期
        List<Date> dates = jwDayService.list().stream()
                .map(CwJwDay::getRecordTime)
                .map(DateUtil::beginOfDay)
                .distinct()
                .collect(Collectors.toList());

        // 逐日重新计算
        for (Date date : dates) {
            try {
                recalculateDrsByDate(date);
            } catch (Exception e) {
                log.error("重新计算精尾厂drs失败，日期: {}", DateUtil.formatDate(date), e);
            }
        }
        log.info("结束重新计算精尾厂所有的drs");
    }

    @Override
    public void recalculateTodayDrs() {
        recalculateDrsByDate(new Date());
    }

    /**
     * 重新计算指定日期范围内的所有精尾厂每日drs
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    @Override
    public void recalculateDrs(Date startDate, Date endDate) {
        log.info("[jw] 开始重算当日数，范围={}~{}", DateUtil.formatDate(startDate), DateUtil.formatDate(endDate));
        // 1. 查询日期范围内的所有日数据
        List<CwJwDay> allDaysInRange = jwDayService.lambdaQuery()
                .between(CwJwDay::getRecordTime, DateUtil.beginOfDay(startDate), DateUtil.endOfDay(endDate))
                .list();

        if (allDaysInRange.isEmpty()) {
            log.info("[jw] 重算范围内无日数据可处理，范围={}~{}", DateUtil.formatDate(startDate), DateUtil.formatDate(endDate));
            return;
        }

        // 2. 按月份对数据进行分组
        Map<String, List<CwJwDay>> daysByMonth = allDaysInRange.stream()
                .collect(Collectors.groupingBy(d -> DateUtil.format(d.getRecordTime(), "yyyy-MM")));

        List<CwJwDay> daysToUpdate = new ArrayList<>();

        // 3. 按月份处理数据
        for (Map.Entry<String, List<CwJwDay>> entry : daysByMonth.entrySet()) {
            String monthKey = entry.getKey();
            List<CwJwDay> daysInMonth = entry.getValue();
            Date monthDate = DateUtil.parse(monthKey, "yyyy-MM");
            Date beginOfMonth = DateUtil.beginOfMonth(monthDate);
            Date endOfMonth = DateUtil.endOfMonth(monthDate);
            log.info("[jw] 处理月份={}，记录天数={}", monthKey, daysInMonth.size());

            // 3.1 获取月表数据
            List<CwJwMonth> months = jwMonthService.lambdaQuery()
                    .ge(CwJwMonth::getRecordTime, beginOfMonth)
                    .le(CwJwMonth::getRecordTime, endOfMonth)
                    .list();

            // 3.2 获取处理量预测数据
            Map<String, BigDecimal> cllycDataMap = getMonthCllycData(monthDate);
            BigDecimal cllyc = cllycDataMap.get(monthKey);

            // 3.3 获取精尾厂处理量数据
            Map<String, BigDecimal> jwCllDataMap = getRangeCllData(beginOfMonth, endOfMonth);

            // 4. 遍历当月的每一天数据，计算drs
            for (CwJwDay day : daysInMonth) {
                String dateKey = DateUtil.format(day.getRecordTime(), "yyyy-MM-dd");
                BigDecimal jwCll = jwCllDataMap.get(dateKey);

                BigDecimal drs = calculateDrs(day, cllyc, jwCll, months);

                day.setDrs(drs);
                daysToUpdate.add(day);
            }
        }

        // 5. 批量更新
        if (!daysToUpdate.isEmpty()) {
            log.info("[jw] 本次将更新drs记录数={}，范围={}~{}", daysToUpdate.size(), DateUtil.formatDate(startDate), DateUtil.formatDate(endDate));
            jwDayService.updateBatchById(daysToUpdate);
        }

        // 同步重算模拟利润（日）
        log.info("[jw] 触发模拟利润重算，范围={}~{}", DateUtil.formatDate(startDate), DateUtil.formatDate(endDate));
        mnlrStatisticsDayService.recalcRange(startDate, endDate);
        log.info("[jw] 完成重算当日数与模拟利润，范围={}~{}", DateUtil.formatDate(startDate), DateUtil.formatDate(endDate));
    }

    // 泗选范围重算已改为直接调用 sxZhcbService.recalculateDrs(startDate, endDate)

    private BigDecimal calculateDrs(CwJwDay day, BigDecimal cllyc, BigDecimal jwCll, List<CwJwMonth> months) {
        String name = day.getName();
        Date recordTime = day.getRecordTime();
        BigDecimal yyc = null;
        BigDecimal pjdj = null;

        // 若当月任意项目存在“月预测”，则整月统一按月预测计算；否则若当月任意项目存在“月预算”，整月按月预算口径计算
        boolean monthForecastExists = months.stream().anyMatch(m -> m.getSecondHalfForecast() != null);
        boolean monthBudgetExists = months.stream().anyMatch(m -> m.getYys() != null);
        for (CwJwMonth month : months) {
            if (name.equals(month.getName())) {
                pjdj = month.getPjdj();
                if (monthForecastExists) {
                    // 全月使用月预测；若本项目无月预测，视为0参与计算
                    yyc = month.getSecondHalfForecast() != null ? month.getSecondHalfForecast() : BigDecimal.ZERO;
                } else if (monthBudgetExists) {
                    // 当月存在任意项目的月预算 -> 整月按月预算口径计算，若本项目无月预算则视为0
                    yyc = month.getYys() != null ? month.getYys() : BigDecimal.ZERO;
                } else {
                    // 否则不使用月预算/预测，保留null以便回退到平均单价计算
                    yyc = null;
                }
                break;
            }
        }

        // 只有当月预算/预测、处理量预测和处理量都有值时，才计算当日数
        if (ObjectUtil.isNotEmpty(cllyc) && ObjectUtil.isNotEmpty(yyc) &&
                cllyc.compareTo(BigDecimal.ZERO) > 0 && ObjectUtil.isNotEmpty(jwCll)) {
            // 单位成本 = 月预测 / 处理量预测
            BigDecimal dwcb = yyc.divide(cllyc, 10, BigDecimal.ROUND_HALF_UP);
            // 当日数 = 单位成本 * 精尾厂处理量
            return dwcb.multiply(jwCll);
        }

        BigDecimal pjzh = day.getPjzh();
        if (ObjectUtil.isNotEmpty(pjzh) && ObjectUtil.isNotEmpty(pjdj)) {
            return pjzh.multiply(pjdj);
        }

        return BigDecimal.ZERO;
    }

    private Map<String, BigDecimal> getMonthCllycData(Date monthDate) {
        Map<String, BigDecimal> result = new HashMap<>();
        Date beginOfMonth = DateUtil.beginOfMonth(monthDate);
        String monthKey = DateUtil.format(beginOfMonth, "yyyy-MM");
        
        CwCllycData cllycData = cwCllycDataService.lambdaQuery()
                .eq(CwCllycData::getRecordTime, beginOfMonth)
                .eq(CwCllycData::getName, NAME_JW)
                .one();
        
        if (ObjectUtil.isNotEmpty(cllycData) && ObjectUtil.isNotEmpty(cllycData.getCllyc())) {
            result.put(monthKey, cllycData.getCllyc());
        }
        return result;
    }

    private Map<String, BigDecimal> getRangeCllData(Date startDate, Date endDate) {
        // 精尾厂的处理量应由大山厂(DSCLL)与泗选厂(SZCLL)合并而来
        Map<String, String> dsMap = cwCllDataService.getRangeCllData(CwCllDataName.DSCLL, startDate, endDate);
        Map<String, String> sxMap = cwCllDataService.getRangeCllData(CwCllDataName.SZCLL, startDate, endDate);

        Map<String, BigDecimal> result = new HashMap<>();

        if (ObjectUtil.isNotEmpty(dsMap)) {
            dsMap.forEach((k, v) -> {
                if (ObjectUtil.isNotEmpty(v)) {
                    result.put(k, new BigDecimal(v));
                }
            });
        }

        if (ObjectUtil.isNotEmpty(sxMap)) {
            sxMap.forEach((k, v) -> {
                if (ObjectUtil.isNotEmpty(v)) {
                    BigDecimal val = new BigDecimal(v);
                    result.merge(k, val, BigDecimal::add);
                }
            });
        }

        return result;
    }

    private void fillMonthData(CwJwRow row, CwJwMonth month, boolean isFirstHalf, boolean monthForecastExists) {
        row.setPjdj(month.getPjdj());
        row.setYys(month.getYys());
        if (monthForecastExists) {
            // 若当月任意项目存在月预测，则本项目月预测缺失时视为0
            row.setYyc(month.getSecondHalfForecast() != null ? month.getSecondHalfForecast() : BigDecimal.ZERO);
        } else {
            // 无任何项目月预测 -> 整月使用月预算
            row.setYyc(month.getYys());
        }
    }

    private void fillDayData(CwJwRow row, CwJwDay day) {
        row.setPjzh(day.getPjzh());
        row.setRemark(day.getRemark());
    }

    private void addToDailyTotal(String type, BigDecimal value, 
                            CwKrbRow clRow, CwKrbRow bjRow, CwKrbRow rlRow, 
                            CwKrbRow sRow, CwKrbRow dRow, CwKrbRow zzfyRow) {
        if (ObjectUtil.isEmpty(value)) {
            return;
        }
        switch (type) {
            case CwKrbRow.CL:
                clRow.setDrs(clRow.getDrs().add(value));
                break;
            case CwKrbRow.BJ:
                bjRow.setDrs(bjRow.getDrs().add(value));
                break;
            case CwKrbRow.RL:
                rlRow.setDrs(rlRow.getDrs().add(value));
                break;
            case CwKrbRow.S:
                sRow.setDrs(sRow.getDrs().add(value));
                break;
            case CwKrbRow.D:
                dRow.setDrs(dRow.getDrs().add(value));
                break;
            case CwKrbRow.ZZFY:
                zzfyRow.setDrs(zzfyRow.getDrs().add(value));
                break;
            default:
                break;
        }
    }

    private void addToMonthlyTotal(String type, BigDecimal value, 
                              CwKrbRow clRow, CwKrbRow bjRow, CwKrbRow rlRow, 
                              CwKrbRow sRow, CwKrbRow dRow, CwKrbRow zzfyRow) {
        if (ObjectUtil.isEmpty(value)) {
            return;
        }
        switch (type) {
            case CwKrbRow.CL:
                clRow.setRlj(clRow.getRlj().add(value));
                break;
            case CwKrbRow.BJ:
                bjRow.setRlj(bjRow.getRlj().add(value));
                break;
            case CwKrbRow.RL:
                rlRow.setRlj(rlRow.getRlj().add(value));
                break;
            case CwKrbRow.S:
                sRow.setRlj(sRow.getRlj().add(value));
                break;
            case CwKrbRow.D:
                dRow.setRlj(dRow.getRlj().add(value));
                break;
            case CwKrbRow.ZZFY:
                zzfyRow.setRlj(zzfyRow.getRlj().add(value));
                break;
            default:
                break;
        }
    }

    private void addToBudget(String type, BigDecimal value, 
                        CwKrbRow clRow, CwKrbRow bjRow, CwKrbRow rlRow, 
                        CwKrbRow sRow, CwKrbRow dRow, CwKrbRow zzfyRow) {
        if (ObjectUtil.isEmpty(value)) {
            return;
        }
        switch (type) {
            case CwKrbRow.CL:
                clRow.setYys(clRow.getYys().add(value));
                break;
            case CwKrbRow.BJ:
                bjRow.setYys(bjRow.getYys().add(value));
                break;
            case CwKrbRow.RL:
                rlRow.setYys(rlRow.getYys().add(value));
                break;
            case CwKrbRow.S:
                sRow.setYys(sRow.getYys().add(value));
                break;
            case CwKrbRow.D:
                dRow.setYys(dRow.getYys().add(value));
                break;
            case CwKrbRow.ZZFY:
                zzfyRow.setYys(zzfyRow.getYys().add(value));
                break;
            default:
                break;
        }
    }

    private List<CwJwDay> autoFillMissingDays(Date beginOfMonth, Date queryDate,
                                             List<CwNameDict> dict,
                                             BigDecimal monthCllyc,
                                             List<CwJwMonth> months) {
        Date endOfDay = DateUtil.endOfDay(queryDate);
        List<CwJwDay> existing = jwDayService.lambdaQuery()
                .between(CwJwDay::getRecordTime, beginOfMonth, endOfDay)
                .list();
        Set<String> existKeys = existing.stream()
                .map(d -> d.getName() + "#" + DateUtil.format(d.getRecordTime(), "yyyy-MM-dd"))
                .collect(Collectors.toSet());

        Map<String, BigDecimal> jwCllMap = getRangeCllData(beginOfMonth, endOfDay);

        List<CwJwDay> needInsert = new ArrayList<>();
        Date iter = beginOfMonth;
        while (!iter.after(queryDate)) {
            String dateKey = DateUtil.format(iter, "yyyy-MM-dd");
            BigDecimal jwCll = jwCllMap.get(dateKey);
            if (ObjectUtil.isEmpty(jwCll)) { iter = DateUtil.offsetDay(iter,1); continue; }
            for (CwNameDict d : dict) {
                String k = d.getName()+"#"+dateKey;
                if (existKeys.contains(k)) continue;
                CwJwDay nd = new CwJwDay();
                BeanUtil.copyProperties(d, nd);
                nd.resetForCopy();
                nd.setRecordTime(iter);
                BigDecimal drsVal = calculateDrs(nd, monthCllyc, jwCll, months);
                nd.setDrs(drsVal);
                needInsert.add(nd);
            }
            iter = DateUtil.offsetDay(iter,1);
        }
        if (!needInsert.isEmpty()) jwDayService.saveBatch(needInsert);
        return needInsert;
    }
}
