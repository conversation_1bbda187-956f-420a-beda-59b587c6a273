import { defHttp } from '/@/utils/http/axios';

// 统计相关接口
// 说明：
// 1. 所有入参均为对象形式，保持灵活扩展
// 2. 返回值类型暂用 any，如有需要可补充对应的 TS 类型
// 3. 与后端 org.jeecg.modules.cw.statistics.controller.CwStatisticsController 中的接口保持一致

// 接口地址枚举
enum Api {
  MetalPriceMonth = '/statistics/metalPriceMonth',
  ProfitStatistics = '/statistics/profitStatistics',
  ProfitTrend = '/statistics/profitTrend',
  CostStatistics = '/statistics/costStatistics',
  CostTrend = '/statistics/costTrend',
  CostTrendByUnit = '/statistics/costTrendByUnit',
  CostTrendByUnitWithTypes = '/statistics/costTrendByUnitWithTypes',
  CostExpandBar = '/statistics/costExpandBar',
  MetalProfitBar = '/statistics/metalProfitBar',
}

/**
 * 金属价格（月）
 */
export function metalPriceMonth(params?: { date?: string; recentDays?: number }) {
	return defHttp.get<any[]>({ url: Api.MetalPriceMonth, params });
}

/**
 * 利润统计（日/月/年）
 */
export function profitStatistics(params?: { date?: string; dimension?: 'day' | 'month' | 'year' | 'recent'; recentDays?: number }) {
  return defHttp.get<any>({ url: Api.ProfitStatistics, params });
}

/**
 * 利润趋势（日 7 天 / 月全年）
 */
export function profitTrend(params?: { date?: string; dimension?: 'day' | 'month' | 'year' | 'recent'; recentDays?: number }) {
  return defHttp.get<any[]>({ url: Api.ProfitTrend, params });
}

/**
 * 成本统计（月/年）
 */
export function costStatistics(params?: { date?: string; dimension?: 'month' | 'year' }) {
  return defHttp.get<any>({ url: Api.CostStatistics, params });
}

/**
 * 成本趋势（月度折线，从1月到当前月）
 */
export function costTrend(params?: { date?: string }) {
  return defHttp.get<any[]>({ url: Api.CostTrend, params });
}

/**
 * 按单位成本趋势（月度折线，包含实际和计划成本）
 */
export function costTrendByUnit(params?: { date?: string; unit?: 'all' | 'ckc' | 'ds' | 'sx' | 'jw'; trendType?: 'total' | 'ton' | 'metal' }) {
  return defHttp.get<any[]>({ url: Api.CostTrendByUnit, params });
}

/**
 * 按单位和类型成本趋势（月度堆叠折线图）
 */
export function costTrendByUnitWithTypes(params: { date?: string; unit: 'ckc' | 'ds' | 'sx' | 'jw' }) {
  return defHttp.get<any[]>({ url: Api.CostTrendByUnitWithTypes, params });
}

/**
 * 单位成本柱状图
 */
export function costExpandBar(params?: { date?: string; dimension?: 'month' | 'year' }) {
  return defHttp.get<any[]>({ url: Api.CostExpandBar, params });
}

/**
 * 金属利润柱状图（月/年）
 */
export function metalProfitBar(params?: { date?: string; dimension?: 'month' | 'year' }) {
  return defHttp.get<any[]>({ url: Api.MetalProfitBar, params });
} 