import {
  require_codemirror
} from "./chunk-J32C3BWF.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/.pnpm/codemirror@5.65.18/node_modules/codemirror/addon/fold/indent-fold.js
var require_indent_fold = __commonJS({
  "node_modules/.pnpm/codemirror@5.65.18/node_modules/codemirror/addon/fold/indent-fold.js"(exports, module) {
    (function(mod) {
      if (typeof exports == "object" && typeof module == "object")
        mod(require_codemirror());
      else if (typeof define == "function" && define.amd)
        define(["../../lib/codemirror"], mod);
      else
        mod(CodeMirror);
    })(function(CodeMirror2) {
      "use strict";
      function lineIndent(cm, lineNo) {
        var text = cm.getLine(lineNo);
        var spaceTo = text.search(/\S/);
        if (spaceTo == -1 || /\bcomment\b/.test(cm.getTokenTypeAt(CodeMirror2.Pos(lineNo, spaceTo + 1))))
          return -1;
        return CodeMirror2.countColumn(text, null, cm.getOption("tabSize"));
      }
      CodeMirror2.registerHelper("fold", "indent", function(cm, start) {
        var myIndent = lineIndent(cm, start.line);
        if (myIndent < 0) return;
        var lastLineInFold = null;
        for (var i = start.line + 1, end = cm.lastLine(); i <= end; ++i) {
          var indent = lineIndent(cm, i);
          if (indent == -1) {
          } else if (indent > myIndent) {
            lastLineInFold = i;
          } else {
            break;
          }
        }
        if (lastLineInFold) return {
          from: CodeMirror2.Pos(start.line, cm.getLine(start.line).length),
          to: CodeMirror2.Pos(lastLineInFold, cm.getLine(lastLineInFold).length)
        };
      });
    });
  }
});
export default require_indent_fold();
//# sourceMappingURL=codemirror_addon_fold_indent-fold__js.js.map
