<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8" />
  <title>Waterfall — 跨越 0 轴颜色一致</title>
  <script src="https://cdn.jsdelivr.net/npm/echarts@5/dist/echarts.min.js"></script>
  <style>
    body { font-family: system-ui, -apple-system, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial; margin:0; }
    .wrap { display: grid; grid-template-columns: 380px 1fr; gap:16px; height:100vh; padding:12px; box-sizing:border-box; }
    .panel { display:flex; flex-direction:column; gap:8px; }
    textarea { width:100%; height:60vh; padding:10px; box-sizing:border-box; font-family: monospace; font-size:13px; }
    button { padding:8px 12px; border-radius:8px; border:0; cursor:pointer; }
    #main { width:100%; height:100%; }
    .hint { color:#666; font-size:13px; }
    .error { color:#c00; font-size:13px; white-space:pre-wrap; }
  </style>
</head>
<body>
  <div class="wrap">
    <div class="panel">
      <h3>可编辑数据（name + change）</h3>
      <textarea id="dataInput">[
  { "name": "Start", "change": 500 },
  { "name": "Nov 1",  "change": 900  },
  { "name": "Nov 2",  "change": 345  },
  { "name": "Nov 3",  "change": 393  },
  { "name": "Nov 4",  "change": -108 },
  { "name": "Nov 5",  "change": -154 },
  { "name": "Nov 6",  "change": 135  },
  { "name": "Nov 7",  "change": 178  },
  { "name": "Nov 8",  "change": 286  },
  { "name": "Nov 9",  "change": -119 },
  { "name": "Nov 10", "change": -361 },
  { "name": "Nov 11", "change": -203 }
]</textarea>
      <div>
        <button id="applyBtn">应用到图表</button>
        <button id="demoBtn">载入示例（跨越 0）</button>
      </div>
      <div class="hint">跨越 0 轴时，整个柱子颜色一致（正值绿色，负值红色）。</div>
      <div id="err" class="error"></div>
    </div>

    <div id="main"></div>
  </div>

<script>
  const chart = echarts.init(document.getElementById('main'));

  // 计算每一步的 prev、next
  function prepareData(raw) {
    let cum = 0;
    return raw.map(it => {
      const change = Number(it.change) || 0;
      const prev = cum;
      const next = prev + change;
      cum = next;
      return { name: String(it.name), prev, next, change };
    });
  }

  function buildOption(items) {
    const seriesData = items.map(d => [d.name, d.prev, d.next, d.change]);

    return {
      title: { text: 'Accumulated Waterfall (Color Consistent Across 0)' },
      tooltip: {
        trigger: 'item',
        formatter: function(params) {
          const d = params.data;
          if (!d) return '';
          return `${d[0]}<br/>起始: ${d[1]}<br/>变动: ${d[3]}<br/>结束: ${d[2]}`;
        }
      },
      xAxis: {
        type: 'category',
        data: items.map(d => d.name)
      },
      yAxis: { type: 'value' },
      grid: { left: '6%', right: '6%', bottom: '8%', top: '10%' },
      series: [{
        type: 'custom',
        renderItem: function (params, api) {
          const name = api.value(0);
          const prev = api.value(1);
          const next = api.value(2);
          const change = api.value(3);

          const pPrev = api.coord([name, prev]);
          const pNext = api.coord([name, next]);
          const pZero = api.coord([name, 0]);

          const band = Math.max(api.size([1, 0])[0], 20);
          const barW = Math.max(band * 0.6, 8);
          const x = pPrev[0] - barW / 2;

          const children = [];
          const minH = 2;

          // 统一颜色
          const mainColor = change >= 0 ? '#4caf50' : '#f44336';

          if ((prev >= 0 && next >= 0) || (prev <= 0 && next <= 0)) {
            const topY = Math.min(pPrev[1], pNext[1]);
            const h = Math.max(Math.abs(pNext[1] - pPrev[1]), minH);
            children.push({
              type: 'rect',
              shape: { x: x, y: topY, width: barW, height: h, r: 3 },
              style: api.style({ fill: mainColor })
            });
          } else {
            // 跨越 0 轴 — 拆成两段，但颜色相同
            const topY = Math.min(pPrev[1], pZero[1]);
            const hTop = Math.max(Math.abs(pZero[1] - pPrev[1]), minH);
            children.push({
              type: 'rect',
              shape: { x: x, y: topY, width: barW, height: hTop, r: 3 },
              style: api.style({ fill: mainColor })
            });
            const topY2 = Math.min(pZero[1], pNext[1]);
            const hBot = Math.max(Math.abs(pNext[1] - pZero[1]), minH);
            children.push({
              type: 'rect',
              shape: { x: x, y: topY2, width: barW, height: hBot, r: 3 },
              style: api.style({ fill: mainColor })
            });
          }

          // 标签
          const labelY = Math.min(pPrev[1], pNext[1]) - 6;
          children.push({
            type: 'text',
            style: {
              text: (change > 0 ? '+' + change : '' + change),
              x: x + barW / 2,
              y: labelY,
              textAlign: 'center',
              textVerticalAlign: 'bottom',
              fill: '#000',
              font: '12px sans-serif'
            }
          });

          return { type: 'group', children };
        },
        data: seriesData
      }]
    };
  }

  function renderFromTextarea() {
    const err = document.getElementById('err');
    err.textContent = '';
    try {
      const txt = document.getElementById('dataInput').value;
      const parsed = JSON.parse(txt);
      if (!Array.isArray(parsed)) throw new Error('数据必须为数组');
      for (const it of parsed) {
        if (!('name' in it) || !('change' in it)) throw new Error('每项需包含 name 和 change');
      }
      const items = prepareData(parsed);
      chart.setOption(buildOption(items), true);
    } catch (e) {
      err.textContent = '数据解析失败：' + e.message;
    }
  }

  document.getElementById('applyBtn').onclick = renderFromTextarea;
  document.getElementById('demoBtn').onclick = () => {
    document.getElementById('dataInput').value = JSON.stringify([
      { name: 'Start', change: 500 },
      { name: 'A', change: 300 },
      { name: 'B', change: -900 }, // 跨越 0
      { name: 'C', change: 200 },
      { name: 'D', change: -50 },
      { name: 'E', change: 400 }
    ], null, 2);
    renderFromTextarea();
  };

  renderFromTextarea();
  window.addEventListener('resize', () => chart.resize());
</script>
</body>
</html>
