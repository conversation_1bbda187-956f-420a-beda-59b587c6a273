{"version": 3, "sources": ["../../.pnpm/codemirror@5.65.18/node_modules/codemirror/addon/fold/indent-fold.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n\"use strict\";\n\nfunction lineIndent(cm, lineNo) {\n  var text = cm.getLine(lineNo)\n  var spaceTo = text.search(/\\S/)\n  if (spaceTo == -1 || /\\bcomment\\b/.test(cm.getTokenTypeAt(CodeMirror.Pos(lineNo, spaceTo + 1))))\n    return -1\n  return CodeMirror.countColumn(text, null, cm.getOption(\"tabSize\"))\n}\n\nCodeMirror.registerHelper(\"fold\", \"indent\", function(cm, start) {\n  var myIndent = lineIndent(cm, start.line)\n  if (myIndent < 0) return\n  var lastLineInFold = null\n\n  // Go through lines until we find a line that definitely doesn't belong in\n  // the block we're folding, or to the end.\n  for (var i = start.line + 1, end = cm.lastLine(); i <= end; ++i) {\n    var indent = lineIndent(cm, i)\n    if (indent == -1) {\n    } else if (indent > myIndent) {\n      // Lines with a greater indent are considered part of the block.\n      lastLineInFold = i;\n    } else {\n      // If this line has non-space, non-comment content, and is\n      // indented less or equal to the start line, it is the start of\n      // another block.\n      break;\n    }\n  }\n  if (lastLineInFold) return {\n    from: CodeMirror.Pos(start.line, cm.getLine(start.line).length),\n    to: CodeMirror.Pos(lastLineInFold, cm.getLine(lastLineInFold).length)\n  };\n});\n\n});\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACxB;AAEA,eAAS,WAAW,IAAI,QAAQ;AAC9B,YAAI,OAAO,GAAG,QAAQ,MAAM;AAC5B,YAAI,UAAU,KAAK,OAAO,IAAI;AAC9B,YAAI,WAAW,MAAM,cAAc,KAAK,GAAG,eAAeA,YAAW,IAAI,QAAQ,UAAU,CAAC,CAAC,CAAC;AAC5F,iBAAO;AACT,eAAOA,YAAW,YAAY,MAAM,MAAM,GAAG,UAAU,SAAS,CAAC;AAAA,MACnE;AAEA,MAAAA,YAAW,eAAe,QAAQ,UAAU,SAAS,IAAI,OAAO;AAC9D,YAAI,WAAW,WAAW,IAAI,MAAM,IAAI;AACxC,YAAI,WAAW,EAAG;AAClB,YAAI,iBAAiB;AAIrB,iBAAS,IAAI,MAAM,OAAO,GAAG,MAAM,GAAG,SAAS,GAAG,KAAK,KAAK,EAAE,GAAG;AAC/D,cAAI,SAAS,WAAW,IAAI,CAAC;AAC7B,cAAI,UAAU,IAAI;AAAA,UAClB,WAAW,SAAS,UAAU;AAE5B,6BAAiB;AAAA,UACnB,OAAO;AAIL;AAAA,UACF;AAAA,QACF;AACA,YAAI,eAAgB,QAAO;AAAA,UACzB,MAAMA,YAAW,IAAI,MAAM,MAAM,GAAG,QAAQ,MAAM,IAAI,EAAE,MAAM;AAAA,UAC9D,IAAIA,YAAW,IAAI,gBAAgB,GAAG,QAAQ,cAAc,EAAE,MAAM;AAAA,QACtE;AAAA,MACF,CAAC;AAAA,IAED,CAAC;AAAA;AAAA;", "names": ["CodeMirror"]}