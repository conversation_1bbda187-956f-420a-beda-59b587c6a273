{"version": 3, "sources": ["../../.pnpm/showdown@2.1.0/node_modules/showdown/src/options.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/showdown.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/helpers.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/converter.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/anchors.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/autoLinks.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/blockGamut.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/blockQuotes.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/codeBlocks.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/codeSpans.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/completeHTMLDocument.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/detab.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/ellipsis.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/emoji.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/encodeAmpsAndAngles.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/encodeBackslashEscapes.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/encodeCode.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/escapeSpecialCharsWithinTagAttributes.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/githubCodeBlocks.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/hashBlock.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/hashCodeTags.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/hashElement.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/hashHTMLBlocks.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/hashHTMLSpans.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/hashPreCodeTags.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/headers.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/horizontalRule.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/images.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/italicsAndBold.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/lists.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/metadata.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/outdent.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/paragraphs.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/runExtension.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/spanGamut.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/strikethrough.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/stripLinkDefinitions.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/tables.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/underline.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/unescapeSpecialChars.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/makeMarkdown/blockquote.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/makeMarkdown/codeBlock.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/makeMarkdown/codeSpan.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/makeMarkdown/emphasis.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/makeMarkdown/header.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/makeMarkdown/hr.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/makeMarkdown/image.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/makeMarkdown/links.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/makeMarkdown/list.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/makeMarkdown/listItem.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/makeMarkdown/node.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/makeMarkdown/paragraph.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/makeMarkdown/pre.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/makeMarkdown/strikethrough.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/makeMarkdown/strong.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/makeMarkdown/table.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/makeMarkdown/tableCell.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/subParsers/makeMarkdown/txt.js", "../../.pnpm/showdown@2.1.0/node_modules/showdown/src/loader.js"], "sourcesContent": ["/**\n * Created by <PERSON><PERSON><PERSON> on 13-07-2015.\n */\n\nfunction getDefaultOpts (simple) {\n  'use strict';\n\n  var defaultOptions = {\n    omitExtraWLInCodeBlocks: {\n      defaultValue: false,\n      describe: 'Omit the default extra whiteline added to code blocks',\n      type: 'boolean'\n    },\n    noHeaderId: {\n      defaultValue: false,\n      describe: 'Turn on/off generated header id',\n      type: 'boolean'\n    },\n    prefixHeaderId: {\n      defaultValue: false,\n      describe: 'Add a prefix to the generated header ids. Passing a string will prefix that string to the header id. Setting to true will add a generic \\'section-\\' prefix',\n      type: 'string'\n    },\n    rawPrefixHeaderId: {\n      defaultValue: false,\n      describe: 'Setting this option to true will prevent showdown from modifying the prefix. This might result in malformed IDs (if, for instance, the \" char is used in the prefix)',\n      type: 'boolean'\n    },\n    ghCompatibleHeaderId: {\n      defaultValue: false,\n      describe: 'Generate header ids compatible with github style (spaces are replaced with dashes, a bunch of non alphanumeric chars are removed)',\n      type: 'boolean'\n    },\n    rawHeaderId: {\n      defaultValue: false,\n      describe: 'Remove only spaces, \\' and \" from generated header ids (including prefixes), replacing them with dashes (-). WARNING: This might result in malformed ids',\n      type: 'boolean'\n    },\n    headerLevelStart: {\n      defaultValue: false,\n      describe: 'The header blocks level start',\n      type: 'integer'\n    },\n    parseImgDimensions: {\n      defaultValue: false,\n      describe: 'Turn on/off image dimension parsing',\n      type: 'boolean'\n    },\n    simplifiedAutoLink: {\n      defaultValue: false,\n      describe: 'Turn on/off GFM autolink style',\n      type: 'boolean'\n    },\n    excludeTrailingPunctuationFromURLs: {\n      defaultValue: false,\n      describe: 'Excludes trailing punctuation from links generated with autoLinking',\n      type: 'boolean'\n    },\n    literalMidWordUnderscores: {\n      defaultValue: false,\n      describe: 'Parse midword underscores as literal underscores',\n      type: 'boolean'\n    },\n    literalMidWordAsterisks: {\n      defaultValue: false,\n      describe: 'Parse midword asterisks as literal asterisks',\n      type: 'boolean'\n    },\n    strikethrough: {\n      defaultValue: false,\n      describe: 'Turn on/off strikethrough support',\n      type: 'boolean'\n    },\n    tables: {\n      defaultValue: false,\n      describe: 'Turn on/off tables support',\n      type: 'boolean'\n    },\n    tablesHeaderId: {\n      defaultValue: false,\n      describe: 'Add an id to table headers',\n      type: 'boolean'\n    },\n    ghCodeBlocks: {\n      defaultValue: true,\n      describe: 'Turn on/off GFM fenced code blocks support',\n      type: 'boolean'\n    },\n    tasklists: {\n      defaultValue: false,\n      describe: 'Turn on/off GFM tasklist support',\n      type: 'boolean'\n    },\n    smoothLivePreview: {\n      defaultValue: false,\n      describe: 'Prevents weird effects in live previews due to incomplete input',\n      type: 'boolean'\n    },\n    smartIndentationFix: {\n      defaultValue: false,\n      describe: 'Tries to smartly fix indentation in es6 strings',\n      type: 'boolean'\n    },\n    disableForced4SpacesIndentedSublists: {\n      defaultValue: false,\n      describe: 'Disables the requirement of indenting nested sublists by 4 spaces',\n      type: 'boolean'\n    },\n    simpleLineBreaks: {\n      defaultValue: false,\n      describe: 'Parses simple line breaks as <br> (GFM Style)',\n      type: 'boolean'\n    },\n    requireSpaceBeforeHeadingText: {\n      defaultValue: false,\n      describe: 'Makes adding a space between `#` and the header text mandatory (GFM Style)',\n      type: 'boolean'\n    },\n    ghMentions: {\n      defaultValue: false,\n      describe: 'Enables github @mentions',\n      type: 'boolean'\n    },\n    ghMentionsLink: {\n      defaultValue: 'https://github.com/{u}',\n      describe: 'Changes the link generated by @mentions. Only applies if ghMentions option is enabled.',\n      type: 'string'\n    },\n    encodeEmails: {\n      defaultValue: true,\n      describe: 'Encode e-mail addresses through the use of Character Entities, transforming ASCII e-mail addresses into its equivalent decimal entities',\n      type: 'boolean'\n    },\n    openLinksInNewWindow: {\n      defaultValue: false,\n      describe: 'Open all links in new windows',\n      type: 'boolean'\n    },\n    backslashEscapesHTMLTags: {\n      defaultValue: false,\n      describe: 'Support for HTML Tag escaping. ex: \\<div>foo\\</div>',\n      type: 'boolean'\n    },\n    emoji: {\n      defaultValue: false,\n      describe: 'Enable emoji support. Ex: `this is a :smile: emoji`',\n      type: 'boolean'\n    },\n    underline: {\n      defaultValue: false,\n      describe: 'Enable support for underline. Syntax is double or triple underscores: `__underline word__`. With this option enabled, underscores no longer parses into `<em>` and `<strong>`',\n      type: 'boolean'\n    },\n    ellipsis: {\n      defaultValue: true,\n      describe: 'Replaces three dots with the ellipsis unicode character',\n      type: 'boolean'\n    },\n    completeHTMLDocument: {\n      defaultValue: false,\n      describe: 'Outputs a complete html document, including `<html>`, `<head>` and `<body>` tags',\n      type: 'boolean'\n    },\n    metadata: {\n      defaultValue: false,\n      describe: 'Enable support for document metadata (defined at the top of the document between `«««` and `»»»` or between `---` and `---`).',\n      type: 'boolean'\n    },\n    splitAdjacentBlockquotes: {\n      defaultValue: false,\n      describe: 'Split adjacent blockquote blocks',\n      type: 'boolean'\n    }\n  };\n  if (simple === false) {\n    return JSON.parse(JSON.stringify(defaultOptions));\n  }\n  var ret = {};\n  for (var opt in defaultOptions) {\n    if (defaultOptions.hasOwnProperty(opt)) {\n      ret[opt] = defaultOptions[opt].defaultValue;\n    }\n  }\n  return ret;\n}\n\nfunction allOptionsOn () {\n  'use strict';\n  var options = getDefaultOpts(true),\n      ret = {};\n  for (var opt in options) {\n    if (options.hasOwnProperty(opt)) {\n      ret[opt] = true;\n    }\n  }\n  return ret;\n}\n", "/**\n * Created by <PERSON><PERSON><PERSON> on 06-01-2015.\n */\n\n// Private properties\nvar showdown = {},\n    parsers = {},\n    extensions = {},\n    globalOptions = getDefaultOpts(true),\n    setFlavor = 'vanilla',\n    flavor = {\n      github: {\n        omitExtraWLInCodeBlocks:              true,\n        simplifiedAutoLink:                   true,\n        excludeTrailingPunctuationFromURLs:   true,\n        literalMidWordUnderscores:            true,\n        strikethrough:                        true,\n        tables:                               true,\n        tablesHeaderId:                       true,\n        ghCodeBlocks:                         true,\n        tasklists:                            true,\n        disableForced4SpacesIndentedSublists: true,\n        simpleLineBreaks:                     true,\n        requireSpaceBeforeHeadingText:        true,\n        ghCompatibleHeaderId:                 true,\n        ghMentions:                           true,\n        backslashEscapesHTMLTags:             true,\n        emoji:                                true,\n        splitAdjacentBlockquotes:             true\n      },\n      original: {\n        noHeaderId:                           true,\n        ghCodeBlocks:                         false\n      },\n      ghost: {\n        omitExtraWLInCodeBlocks:              true,\n        parseImgDimensions:                   true,\n        simplifiedAutoLink:                   true,\n        excludeTrailingPunctuationFromURLs:   true,\n        literalMidWordUnderscores:            true,\n        strikethrough:                        true,\n        tables:                               true,\n        tablesHeaderId:                       true,\n        ghCodeBlocks:                         true,\n        tasklists:                            true,\n        smoothLivePreview:                    true,\n        simpleLineBreaks:                     true,\n        requireSpaceBeforeHeadingText:        true,\n        ghMentions:                           false,\n        encodeEmails:                         true\n      },\n      vanilla: getDefaultOpts(true),\n      allOn: allOptionsOn()\n    };\n\n/**\n * helper namespace\n * @type {{}}\n */\nshowdown.helper = {};\n\n/**\n * TODO LEGACY SUPPORT CODE\n * @type {{}}\n */\nshowdown.extensions = {};\n\n/**\n * Set a global option\n * @static\n * @param {string} key\n * @param {*} value\n * @returns {showdown}\n */\nshowdown.setOption = function (key, value) {\n  'use strict';\n  globalOptions[key] = value;\n  return this;\n};\n\n/**\n * Get a global option\n * @static\n * @param {string} key\n * @returns {*}\n */\nshowdown.getOption = function (key) {\n  'use strict';\n  return globalOptions[key];\n};\n\n/**\n * Get the global options\n * @static\n * @returns {{}}\n */\nshowdown.getOptions = function () {\n  'use strict';\n  return globalOptions;\n};\n\n/**\n * Reset global options to the default values\n * @static\n */\nshowdown.resetOptions = function () {\n  'use strict';\n  globalOptions = getDefaultOpts(true);\n};\n\n/**\n * Set the flavor showdown should use as default\n * @param {string} name\n */\nshowdown.setFlavor = function (name) {\n  'use strict';\n  if (!flavor.hasOwnProperty(name)) {\n    throw Error(name + ' flavor was not found');\n  }\n  showdown.resetOptions();\n  var preset = flavor[name];\n  setFlavor = name;\n  for (var option in preset) {\n    if (preset.hasOwnProperty(option)) {\n      globalOptions[option] = preset[option];\n    }\n  }\n};\n\n/**\n * Get the currently set flavor\n * @returns {string}\n */\nshowdown.getFlavor = function () {\n  'use strict';\n  return setFlavor;\n};\n\n/**\n * Get the options of a specified flavor. Returns undefined if the flavor was not found\n * @param {string} name Name of the flavor\n * @returns {{}|undefined}\n */\nshowdown.getFlavorOptions = function (name) {\n  'use strict';\n  if (flavor.hasOwnProperty(name)) {\n    return flavor[name];\n  }\n};\n\n/**\n * Get the default options\n * @static\n * @param {boolean} [simple=true]\n * @returns {{}}\n */\nshowdown.getDefaultOptions = function (simple) {\n  'use strict';\n  return getDefaultOpts(simple);\n};\n\n/**\n * Get or set a subParser\n *\n * subParser(name)       - Get a registered subParser\n * subParser(name, func) - Register a subParser\n * @static\n * @param {string} name\n * @param {function} [func]\n * @returns {*}\n */\nshowdown.subParser = function (name, func) {\n  'use strict';\n  if (showdown.helper.isString(name)) {\n    if (typeof func !== 'undefined') {\n      parsers[name] = func;\n    } else {\n      if (parsers.hasOwnProperty(name)) {\n        return parsers[name];\n      } else {\n        throw Error('SubParser named ' + name + ' not registered!');\n      }\n    }\n  }\n};\n\n/**\n * Gets or registers an extension\n * @static\n * @param {string} name\n * @param {object|object[]|function=} ext\n * @returns {*}\n */\nshowdown.extension = function (name, ext) {\n  'use strict';\n\n  if (!showdown.helper.isString(name)) {\n    throw Error('Extension \\'name\\' must be a string');\n  }\n\n  name = showdown.helper.stdExtName(name);\n\n  // Getter\n  if (showdown.helper.isUndefined(ext)) {\n    if (!extensions.hasOwnProperty(name)) {\n      throw Error('Extension named ' + name + ' is not registered!');\n    }\n    return extensions[name];\n\n    // Setter\n  } else {\n    // Expand extension if it's wrapped in a function\n    if (typeof ext === 'function') {\n      ext = ext();\n    }\n\n    // Ensure extension is an array\n    if (!showdown.helper.isArray(ext)) {\n      ext = [ext];\n    }\n\n    var validExtension = validate(ext, name);\n\n    if (validExtension.valid) {\n      extensions[name] = ext;\n    } else {\n      throw Error(validExtension.error);\n    }\n  }\n};\n\n/**\n * Gets all extensions registered\n * @returns {{}}\n */\nshowdown.getAllExtensions = function () {\n  'use strict';\n  return extensions;\n};\n\n/**\n * Remove an extension\n * @param {string} name\n */\nshowdown.removeExtension = function (name) {\n  'use strict';\n  delete extensions[name];\n};\n\n/**\n * Removes all extensions\n */\nshowdown.resetExtensions = function () {\n  'use strict';\n  extensions = {};\n};\n\n/**\n * Validate extension\n * @param {array} extension\n * @param {string} name\n * @returns {{valid: boolean, error: string}}\n */\nfunction validate (extension, name) {\n  'use strict';\n\n  var errMsg = (name) ? 'Error in ' + name + ' extension->' : 'Error in unnamed extension',\n      ret = {\n        valid: true,\n        error: ''\n      };\n\n  if (!showdown.helper.isArray(extension)) {\n    extension = [extension];\n  }\n\n  for (var i = 0; i < extension.length; ++i) {\n    var baseMsg = errMsg + ' sub-extension ' + i + ': ',\n        ext = extension[i];\n    if (typeof ext !== 'object') {\n      ret.valid = false;\n      ret.error = baseMsg + 'must be an object, but ' + typeof ext + ' given';\n      return ret;\n    }\n\n    if (!showdown.helper.isString(ext.type)) {\n      ret.valid = false;\n      ret.error = baseMsg + 'property \"type\" must be a string, but ' + typeof ext.type + ' given';\n      return ret;\n    }\n\n    var type = ext.type = ext.type.toLowerCase();\n\n    // normalize extension type\n    if (type === 'language') {\n      type = ext.type = 'lang';\n    }\n\n    if (type === 'html') {\n      type = ext.type = 'output';\n    }\n\n    if (type !== 'lang' && type !== 'output' && type !== 'listener') {\n      ret.valid = false;\n      ret.error = baseMsg + 'type ' + type + ' is not recognized. Valid values: \"lang/language\", \"output/html\" or \"listener\"';\n      return ret;\n    }\n\n    if (type === 'listener') {\n      if (showdown.helper.isUndefined(ext.listeners)) {\n        ret.valid = false;\n        ret.error = baseMsg + '. Extensions of type \"listener\" must have a property called \"listeners\"';\n        return ret;\n      }\n    } else {\n      if (showdown.helper.isUndefined(ext.filter) && showdown.helper.isUndefined(ext.regex)) {\n        ret.valid = false;\n        ret.error = baseMsg + type + ' extensions must define either a \"regex\" property or a \"filter\" method';\n        return ret;\n      }\n    }\n\n    if (ext.listeners) {\n      if (typeof ext.listeners !== 'object') {\n        ret.valid = false;\n        ret.error = baseMsg + '\"listeners\" property must be an object but ' + typeof ext.listeners + ' given';\n        return ret;\n      }\n      for (var ln in ext.listeners) {\n        if (ext.listeners.hasOwnProperty(ln)) {\n          if (typeof ext.listeners[ln] !== 'function') {\n            ret.valid = false;\n            ret.error = baseMsg + '\"listeners\" property must be an hash of [event name]: [callback]. listeners.' + ln +\n              ' must be a function but ' + typeof ext.listeners[ln] + ' given';\n            return ret;\n          }\n        }\n      }\n    }\n\n    if (ext.filter) {\n      if (typeof ext.filter !== 'function') {\n        ret.valid = false;\n        ret.error = baseMsg + '\"filter\" must be a function, but ' + typeof ext.filter + ' given';\n        return ret;\n      }\n    } else if (ext.regex) {\n      if (showdown.helper.isString(ext.regex)) {\n        ext.regex = new RegExp(ext.regex, 'g');\n      }\n      if (!(ext.regex instanceof RegExp)) {\n        ret.valid = false;\n        ret.error = baseMsg + '\"regex\" property must either be a string or a RegExp object, but ' + typeof ext.regex + ' given';\n        return ret;\n      }\n      if (showdown.helper.isUndefined(ext.replace)) {\n        ret.valid = false;\n        ret.error = baseMsg + '\"regex\" extensions must implement a replace string or function';\n        return ret;\n      }\n    }\n  }\n  return ret;\n}\n\n/**\n * Validate extension\n * @param {object} ext\n * @returns {boolean}\n */\nshowdown.validateExtension = function (ext) {\n  'use strict';\n\n  var validateExtension = validate(ext, null);\n  if (!validateExtension.valid) {\n    console.warn(validateExtension.error);\n    return false;\n  }\n  return true;\n};\n", "/**\n * showdownjs helper functions\n */\n\nif (!showdown.hasOwnProperty('helper')) {\n  showdown.helper = {};\n}\n\n/**\n * Check if var is string\n * @static\n * @param {string} a\n * @returns {boolean}\n */\nshowdown.helper.isString = function (a) {\n  'use strict';\n  return (typeof a === 'string' || a instanceof String);\n};\n\n/**\n * Check if var is a function\n * @static\n * @param {*} a\n * @returns {boolean}\n */\nshowdown.helper.isFunction = function (a) {\n  'use strict';\n  var getType = {};\n  return a && getType.toString.call(a) === '[object Function]';\n};\n\n/**\n * isArray helper function\n * @static\n * @param {*} a\n * @returns {boolean}\n */\nshowdown.helper.isArray = function (a) {\n  'use strict';\n  return Array.isArray(a);\n};\n\n/**\n * Check if value is undefined\n * @static\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is `undefined`, else `false`.\n */\nshowdown.helper.isUndefined = function (value) {\n  'use strict';\n  return typeof value === 'undefined';\n};\n\n/**\n * ForEach helper function\n * Iterates over Arrays and Objects (own properties only)\n * @static\n * @param {*} obj\n * @param {function} callback Accepts 3 params: 1. value, 2. key, 3. the original array/object\n */\nshowdown.helper.forEach = function (obj, callback) {\n  'use strict';\n  // check if obj is defined\n  if (showdown.helper.isUndefined(obj)) {\n    throw new Error('obj param is required');\n  }\n\n  if (showdown.helper.isUndefined(callback)) {\n    throw new Error('callback param is required');\n  }\n\n  if (!showdown.helper.isFunction(callback)) {\n    throw new Error('callback param must be a function/closure');\n  }\n\n  if (typeof obj.forEach === 'function') {\n    obj.forEach(callback);\n  } else if (showdown.helper.isArray(obj)) {\n    for (var i = 0; i < obj.length; i++) {\n      callback(obj[i], i, obj);\n    }\n  } else if (typeof (obj) === 'object') {\n    for (var prop in obj) {\n      if (obj.hasOwnProperty(prop)) {\n        callback(obj[prop], prop, obj);\n      }\n    }\n  } else {\n    throw new Error('obj does not seem to be an array or an iterable object');\n  }\n};\n\n/**\n * Standardidize extension name\n * @static\n * @param {string} s extension name\n * @returns {string}\n */\nshowdown.helper.stdExtName = function (s) {\n  'use strict';\n  return s.replace(/[_?*+\\/\\\\.^-]/g, '').replace(/\\s/g, '').toLowerCase();\n};\n\nfunction escapeCharactersCallback (wholeMatch, m1) {\n  'use strict';\n  var charCodeToEscape = m1.charCodeAt(0);\n  return '¨E' + charCodeToEscape + 'E';\n}\n\n/**\n * Callback used to escape characters when passing through String.replace\n * @static\n * @param {string} wholeMatch\n * @param {string} m1\n * @returns {string}\n */\nshowdown.helper.escapeCharactersCallback = escapeCharactersCallback;\n\n/**\n * Escape characters in a string\n * @static\n * @param {string} text\n * @param {string} charsToEscape\n * @param {boolean} afterBackslash\n * @returns {XML|string|void|*}\n */\nshowdown.helper.escapeCharacters = function (text, charsToEscape, afterBackslash) {\n  'use strict';\n  // First we have to escape the escape characters so that\n  // we can build a character class out of them\n  var regexString = '([' + charsToEscape.replace(/([\\[\\]\\\\])/g, '\\\\$1') + '])';\n\n  if (afterBackslash) {\n    regexString = '\\\\\\\\' + regexString;\n  }\n\n  var regex = new RegExp(regexString, 'g');\n  text = text.replace(regex, escapeCharactersCallback);\n\n  return text;\n};\n\n/**\n * Unescape HTML entities\n * @param txt\n * @returns {string}\n */\nshowdown.helper.unescapeHTMLEntities = function (txt) {\n  'use strict';\n\n  return txt\n    .replace(/&quot;/g, '\"')\n    .replace(/&lt;/g, '<')\n    .replace(/&gt;/g, '>')\n    .replace(/&amp;/g, '&');\n};\n\nvar rgxFindMatchPos = function (str, left, right, flags) {\n  'use strict';\n  var f = flags || '',\n      g = f.indexOf('g') > -1,\n      x = new RegExp(left + '|' + right, 'g' + f.replace(/g/g, '')),\n      l = new RegExp(left, f.replace(/g/g, '')),\n      pos = [],\n      t, s, m, start, end;\n\n  do {\n    t = 0;\n    while ((m = x.exec(str))) {\n      if (l.test(m[0])) {\n        if (!(t++)) {\n          s = x.lastIndex;\n          start = s - m[0].length;\n        }\n      } else if (t) {\n        if (!--t) {\n          end = m.index + m[0].length;\n          var obj = {\n            left: {start: start, end: s},\n            match: {start: s, end: m.index},\n            right: {start: m.index, end: end},\n            wholeMatch: {start: start, end: end}\n          };\n          pos.push(obj);\n          if (!g) {\n            return pos;\n          }\n        }\n      }\n    }\n  } while (t && (x.lastIndex = s));\n\n  return pos;\n};\n\n/**\n * matchRecursiveRegExp\n *\n * (c) 2007 Steven Levithan <stevenlevithan.com>\n * MIT License\n *\n * Accepts a string to search, a left and right format delimiter\n * as regex patterns, and optional regex flags. Returns an array\n * of matches, allowing nested instances of left/right delimiters.\n * Use the \"g\" flag to return all matches, otherwise only the\n * first is returned. Be careful to ensure that the left and\n * right format delimiters produce mutually exclusive matches.\n * Backreferences are not supported within the right delimiter\n * due to how it is internally combined with the left delimiter.\n * When matching strings whose format delimiters are unbalanced\n * to the left or right, the output is intentionally as a\n * conventional regex library with recursion support would\n * produce, e.g. \"<<x>\" and \"<x>>\" both produce [\"x\"] when using\n * \"<\" and \">\" as the delimiters (both strings contain a single,\n * balanced instance of \"<x>\").\n *\n * examples:\n * matchRecursiveRegExp(\"test\", \"\\\\(\", \"\\\\)\")\n * returns: []\n * matchRecursiveRegExp(\"<t<<e>><s>>t<>\", \"<\", \">\", \"g\")\n * returns: [\"t<<e>><s>\", \"\"]\n * matchRecursiveRegExp(\"<div id=\\\"x\\\">test</div>\", \"<div\\\\b[^>]*>\", \"</div>\", \"gi\")\n * returns: [\"test\"]\n */\nshowdown.helper.matchRecursiveRegExp = function (str, left, right, flags) {\n  'use strict';\n\n  var matchPos = rgxFindMatchPos (str, left, right, flags),\n      results = [];\n\n  for (var i = 0; i < matchPos.length; ++i) {\n    results.push([\n      str.slice(matchPos[i].wholeMatch.start, matchPos[i].wholeMatch.end),\n      str.slice(matchPos[i].match.start, matchPos[i].match.end),\n      str.slice(matchPos[i].left.start, matchPos[i].left.end),\n      str.slice(matchPos[i].right.start, matchPos[i].right.end)\n    ]);\n  }\n  return results;\n};\n\n/**\n *\n * @param {string} str\n * @param {string|function} replacement\n * @param {string} left\n * @param {string} right\n * @param {string} flags\n * @returns {string}\n */\nshowdown.helper.replaceRecursiveRegExp = function (str, replacement, left, right, flags) {\n  'use strict';\n\n  if (!showdown.helper.isFunction(replacement)) {\n    var repStr = replacement;\n    replacement = function () {\n      return repStr;\n    };\n  }\n\n  var matchPos = rgxFindMatchPos(str, left, right, flags),\n      finalStr = str,\n      lng = matchPos.length;\n\n  if (lng > 0) {\n    var bits = [];\n    if (matchPos[0].wholeMatch.start !== 0) {\n      bits.push(str.slice(0, matchPos[0].wholeMatch.start));\n    }\n    for (var i = 0; i < lng; ++i) {\n      bits.push(\n        replacement(\n          str.slice(matchPos[i].wholeMatch.start, matchPos[i].wholeMatch.end),\n          str.slice(matchPos[i].match.start, matchPos[i].match.end),\n          str.slice(matchPos[i].left.start, matchPos[i].left.end),\n          str.slice(matchPos[i].right.start, matchPos[i].right.end)\n        )\n      );\n      if (i < lng - 1) {\n        bits.push(str.slice(matchPos[i].wholeMatch.end, matchPos[i + 1].wholeMatch.start));\n      }\n    }\n    if (matchPos[lng - 1].wholeMatch.end < str.length) {\n      bits.push(str.slice(matchPos[lng - 1].wholeMatch.end));\n    }\n    finalStr = bits.join('');\n  }\n  return finalStr;\n};\n\n/**\n * Returns the index within the passed String object of the first occurrence of the specified regex,\n * starting the search at fromIndex. Returns -1 if the value is not found.\n *\n * @param {string} str string to search\n * @param {RegExp} regex Regular expression to search\n * @param {int} [fromIndex = 0] Index to start the search\n * @returns {Number}\n * @throws InvalidArgumentError\n */\nshowdown.helper.regexIndexOf = function (str, regex, fromIndex) {\n  'use strict';\n  if (!showdown.helper.isString(str)) {\n    throw 'InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string';\n  }\n  if (regex instanceof RegExp === false) {\n    throw 'InvalidArgumentError: second parameter of showdown.helper.regexIndexOf function must be an instance of RegExp';\n  }\n  var indexOf = str.substring(fromIndex || 0).search(regex);\n  return (indexOf >= 0) ? (indexOf + (fromIndex || 0)) : indexOf;\n};\n\n/**\n * Splits the passed string object at the defined index, and returns an array composed of the two substrings\n * @param {string} str string to split\n * @param {int} index index to split string at\n * @returns {[string,string]}\n * @throws InvalidArgumentError\n */\nshowdown.helper.splitAtIndex = function (str, index) {\n  'use strict';\n  if (!showdown.helper.isString(str)) {\n    throw 'InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string';\n  }\n  return [str.substring(0, index), str.substring(index)];\n};\n\n/**\n * Obfuscate an e-mail address through the use of Character Entities,\n * transforming ASCII characters into their equivalent decimal or hex entities.\n *\n * Since it has a random component, subsequent calls to this function produce different results\n *\n * @param {string} mail\n * @returns {string}\n */\nshowdown.helper.encodeEmailAddress = function (mail) {\n  'use strict';\n  var encode = [\n    function (ch) {\n      return '&#' + ch.charCodeAt(0) + ';';\n    },\n    function (ch) {\n      return '&#x' + ch.charCodeAt(0).toString(16) + ';';\n    },\n    function (ch) {\n      return ch;\n    }\n  ];\n\n  mail = mail.replace(/./g, function (ch) {\n    if (ch === '@') {\n      // this *must* be encoded. I insist.\n      ch = encode[Math.floor(Math.random() * 2)](ch);\n    } else {\n      var r = Math.random();\n      // roughly 10% raw, 45% hex, 45% dec\n      ch = (\n        r > 0.9 ? encode[2](ch) : r > 0.45 ? encode[1](ch) : encode[0](ch)\n      );\n    }\n    return ch;\n  });\n\n  return mail;\n};\n\n/**\n *\n * @param str\n * @param targetLength\n * @param padString\n * @returns {string}\n */\nshowdown.helper.padEnd = function padEnd (str, targetLength, padString) {\n  'use strict';\n  /*jshint bitwise: false*/\n  // eslint-disable-next-line space-infix-ops\n  targetLength = targetLength>>0; //floor if number or convert non-number to 0;\n  /*jshint bitwise: true*/\n  padString = String(padString || ' ');\n  if (str.length > targetLength) {\n    return String(str);\n  } else {\n    targetLength = targetLength - str.length;\n    if (targetLength > padString.length) {\n      padString += padString.repeat(targetLength / padString.length); //append to original to ensure we are longer than needed\n    }\n    return String(str) + padString.slice(0,targetLength);\n  }\n};\n\n/**\n * POLYFILLS\n */\n// use this instead of builtin is undefined for IE8 compatibility\nif (typeof (console) === 'undefined') {\n  console = {\n    warn: function (msg) {\n      'use strict';\n      alert(msg);\n    },\n    log: function (msg) {\n      'use strict';\n      alert(msg);\n    },\n    error: function (msg) {\n      'use strict';\n      throw msg;\n    }\n  };\n}\n\n/**\n * Common regexes.\n * We declare some common regexes to improve performance\n */\nshowdown.helper.regexes = {\n  asteriskDashAndColon: /([*_:~])/g\n};\n\n/**\n * EMOJIS LIST\n */\nshowdown.helper.emojis = {\n  '+1':'\\ud83d\\udc4d',\n  '-1':'\\ud83d\\udc4e',\n  '100':'\\ud83d\\udcaf',\n  '1234':'\\ud83d\\udd22',\n  '1st_place_medal':'\\ud83e\\udd47',\n  '2nd_place_medal':'\\ud83e\\udd48',\n  '3rd_place_medal':'\\ud83e\\udd49',\n  '8ball':'\\ud83c\\udfb1',\n  'a':'\\ud83c\\udd70\\ufe0f',\n  'ab':'\\ud83c\\udd8e',\n  'abc':'\\ud83d\\udd24',\n  'abcd':'\\ud83d\\udd21',\n  'accept':'\\ud83c\\ude51',\n  'aerial_tramway':'\\ud83d\\udea1',\n  'airplane':'\\u2708\\ufe0f',\n  'alarm_clock':'\\u23f0',\n  'alembic':'\\u2697\\ufe0f',\n  'alien':'\\ud83d\\udc7d',\n  'ambulance':'\\ud83d\\ude91',\n  'amphora':'\\ud83c\\udffa',\n  'anchor':'\\u2693\\ufe0f',\n  'angel':'\\ud83d\\udc7c',\n  'anger':'\\ud83d\\udca2',\n  'angry':'\\ud83d\\ude20',\n  'anguished':'\\ud83d\\ude27',\n  'ant':'\\ud83d\\udc1c',\n  'apple':'\\ud83c\\udf4e',\n  'aquarius':'\\u2652\\ufe0f',\n  'aries':'\\u2648\\ufe0f',\n  'arrow_backward':'\\u25c0\\ufe0f',\n  'arrow_double_down':'\\u23ec',\n  'arrow_double_up':'\\u23eb',\n  'arrow_down':'\\u2b07\\ufe0f',\n  'arrow_down_small':'\\ud83d\\udd3d',\n  'arrow_forward':'\\u25b6\\ufe0f',\n  'arrow_heading_down':'\\u2935\\ufe0f',\n  'arrow_heading_up':'\\u2934\\ufe0f',\n  'arrow_left':'\\u2b05\\ufe0f',\n  'arrow_lower_left':'\\u2199\\ufe0f',\n  'arrow_lower_right':'\\u2198\\ufe0f',\n  'arrow_right':'\\u27a1\\ufe0f',\n  'arrow_right_hook':'\\u21aa\\ufe0f',\n  'arrow_up':'\\u2b06\\ufe0f',\n  'arrow_up_down':'\\u2195\\ufe0f',\n  'arrow_up_small':'\\ud83d\\udd3c',\n  'arrow_upper_left':'\\u2196\\ufe0f',\n  'arrow_upper_right':'\\u2197\\ufe0f',\n  'arrows_clockwise':'\\ud83d\\udd03',\n  'arrows_counterclockwise':'\\ud83d\\udd04',\n  'art':'\\ud83c\\udfa8',\n  'articulated_lorry':'\\ud83d\\ude9b',\n  'artificial_satellite':'\\ud83d\\udef0',\n  'astonished':'\\ud83d\\ude32',\n  'athletic_shoe':'\\ud83d\\udc5f',\n  'atm':'\\ud83c\\udfe7',\n  'atom_symbol':'\\u269b\\ufe0f',\n  'avocado':'\\ud83e\\udd51',\n  'b':'\\ud83c\\udd71\\ufe0f',\n  'baby':'\\ud83d\\udc76',\n  'baby_bottle':'\\ud83c\\udf7c',\n  'baby_chick':'\\ud83d\\udc24',\n  'baby_symbol':'\\ud83d\\udebc',\n  'back':'\\ud83d\\udd19',\n  'bacon':'\\ud83e\\udd53',\n  'badminton':'\\ud83c\\udff8',\n  'baggage_claim':'\\ud83d\\udec4',\n  'baguette_bread':'\\ud83e\\udd56',\n  'balance_scale':'\\u2696\\ufe0f',\n  'balloon':'\\ud83c\\udf88',\n  'ballot_box':'\\ud83d\\uddf3',\n  'ballot_box_with_check':'\\u2611\\ufe0f',\n  'bamboo':'\\ud83c\\udf8d',\n  'banana':'\\ud83c\\udf4c',\n  'bangbang':'\\u203c\\ufe0f',\n  'bank':'\\ud83c\\udfe6',\n  'bar_chart':'\\ud83d\\udcca',\n  'barber':'\\ud83d\\udc88',\n  'baseball':'\\u26be\\ufe0f',\n  'basketball':'\\ud83c\\udfc0',\n  'basketball_man':'\\u26f9\\ufe0f',\n  'basketball_woman':'\\u26f9\\ufe0f&zwj;\\u2640\\ufe0f',\n  'bat':'\\ud83e\\udd87',\n  'bath':'\\ud83d\\udec0',\n  'bathtub':'\\ud83d\\udec1',\n  'battery':'\\ud83d\\udd0b',\n  'beach_umbrella':'\\ud83c\\udfd6',\n  'bear':'\\ud83d\\udc3b',\n  'bed':'\\ud83d\\udecf',\n  'bee':'\\ud83d\\udc1d',\n  'beer':'\\ud83c\\udf7a',\n  'beers':'\\ud83c\\udf7b',\n  'beetle':'\\ud83d\\udc1e',\n  'beginner':'\\ud83d\\udd30',\n  'bell':'\\ud83d\\udd14',\n  'bellhop_bell':'\\ud83d\\udece',\n  'bento':'\\ud83c\\udf71',\n  'biking_man':'\\ud83d\\udeb4',\n  'bike':'\\ud83d\\udeb2',\n  'biking_woman':'\\ud83d\\udeb4&zwj;\\u2640\\ufe0f',\n  'bikini':'\\ud83d\\udc59',\n  'biohazard':'\\u2623\\ufe0f',\n  'bird':'\\ud83d\\udc26',\n  'birthday':'\\ud83c\\udf82',\n  'black_circle':'\\u26ab\\ufe0f',\n  'black_flag':'\\ud83c\\udff4',\n  'black_heart':'\\ud83d\\udda4',\n  'black_joker':'\\ud83c\\udccf',\n  'black_large_square':'\\u2b1b\\ufe0f',\n  'black_medium_small_square':'\\u25fe\\ufe0f',\n  'black_medium_square':'\\u25fc\\ufe0f',\n  'black_nib':'\\u2712\\ufe0f',\n  'black_small_square':'\\u25aa\\ufe0f',\n  'black_square_button':'\\ud83d\\udd32',\n  'blonde_man':'\\ud83d\\udc71',\n  'blonde_woman':'\\ud83d\\udc71&zwj;\\u2640\\ufe0f',\n  'blossom':'\\ud83c\\udf3c',\n  'blowfish':'\\ud83d\\udc21',\n  'blue_book':'\\ud83d\\udcd8',\n  'blue_car':'\\ud83d\\ude99',\n  'blue_heart':'\\ud83d\\udc99',\n  'blush':'\\ud83d\\ude0a',\n  'boar':'\\ud83d\\udc17',\n  'boat':'\\u26f5\\ufe0f',\n  'bomb':'\\ud83d\\udca3',\n  'book':'\\ud83d\\udcd6',\n  'bookmark':'\\ud83d\\udd16',\n  'bookmark_tabs':'\\ud83d\\udcd1',\n  'books':'\\ud83d\\udcda',\n  'boom':'\\ud83d\\udca5',\n  'boot':'\\ud83d\\udc62',\n  'bouquet':'\\ud83d\\udc90',\n  'bowing_man':'\\ud83d\\ude47',\n  'bow_and_arrow':'\\ud83c\\udff9',\n  'bowing_woman':'\\ud83d\\ude47&zwj;\\u2640\\ufe0f',\n  'bowling':'\\ud83c\\udfb3',\n  'boxing_glove':'\\ud83e\\udd4a',\n  'boy':'\\ud83d\\udc66',\n  'bread':'\\ud83c\\udf5e',\n  'bride_with_veil':'\\ud83d\\udc70',\n  'bridge_at_night':'\\ud83c\\udf09',\n  'briefcase':'\\ud83d\\udcbc',\n  'broken_heart':'\\ud83d\\udc94',\n  'bug':'\\ud83d\\udc1b',\n  'building_construction':'\\ud83c\\udfd7',\n  'bulb':'\\ud83d\\udca1',\n  'bullettrain_front':'\\ud83d\\ude85',\n  'bullettrain_side':'\\ud83d\\ude84',\n  'burrito':'\\ud83c\\udf2f',\n  'bus':'\\ud83d\\ude8c',\n  'business_suit_levitating':'\\ud83d\\udd74',\n  'busstop':'\\ud83d\\ude8f',\n  'bust_in_silhouette':'\\ud83d\\udc64',\n  'busts_in_silhouette':'\\ud83d\\udc65',\n  'butterfly':'\\ud83e\\udd8b',\n  'cactus':'\\ud83c\\udf35',\n  'cake':'\\ud83c\\udf70',\n  'calendar':'\\ud83d\\udcc6',\n  'call_me_hand':'\\ud83e\\udd19',\n  'calling':'\\ud83d\\udcf2',\n  'camel':'\\ud83d\\udc2b',\n  'camera':'\\ud83d\\udcf7',\n  'camera_flash':'\\ud83d\\udcf8',\n  'camping':'\\ud83c\\udfd5',\n  'cancer':'\\u264b\\ufe0f',\n  'candle':'\\ud83d\\udd6f',\n  'candy':'\\ud83c\\udf6c',\n  'canoe':'\\ud83d\\udef6',\n  'capital_abcd':'\\ud83d\\udd20',\n  'capricorn':'\\u2651\\ufe0f',\n  'car':'\\ud83d\\ude97',\n  'card_file_box':'\\ud83d\\uddc3',\n  'card_index':'\\ud83d\\udcc7',\n  'card_index_dividers':'\\ud83d\\uddc2',\n  'carousel_horse':'\\ud83c\\udfa0',\n  'carrot':'\\ud83e\\udd55',\n  'cat':'\\ud83d\\udc31',\n  'cat2':'\\ud83d\\udc08',\n  'cd':'\\ud83d\\udcbf',\n  'chains':'\\u26d3',\n  'champagne':'\\ud83c\\udf7e',\n  'chart':'\\ud83d\\udcb9',\n  'chart_with_downwards_trend':'\\ud83d\\udcc9',\n  'chart_with_upwards_trend':'\\ud83d\\udcc8',\n  'checkered_flag':'\\ud83c\\udfc1',\n  'cheese':'\\ud83e\\uddc0',\n  'cherries':'\\ud83c\\udf52',\n  'cherry_blossom':'\\ud83c\\udf38',\n  'chestnut':'\\ud83c\\udf30',\n  'chicken':'\\ud83d\\udc14',\n  'children_crossing':'\\ud83d\\udeb8',\n  'chipmunk':'\\ud83d\\udc3f',\n  'chocolate_bar':'\\ud83c\\udf6b',\n  'christmas_tree':'\\ud83c\\udf84',\n  'church':'\\u26ea\\ufe0f',\n  'cinema':'\\ud83c\\udfa6',\n  'circus_tent':'\\ud83c\\udfaa',\n  'city_sunrise':'\\ud83c\\udf07',\n  'city_sunset':'\\ud83c\\udf06',\n  'cityscape':'\\ud83c\\udfd9',\n  'cl':'\\ud83c\\udd91',\n  'clamp':'\\ud83d\\udddc',\n  'clap':'\\ud83d\\udc4f',\n  'clapper':'\\ud83c\\udfac',\n  'classical_building':'\\ud83c\\udfdb',\n  'clinking_glasses':'\\ud83e\\udd42',\n  'clipboard':'\\ud83d\\udccb',\n  'clock1':'\\ud83d\\udd50',\n  'clock10':'\\ud83d\\udd59',\n  'clock1030':'\\ud83d\\udd65',\n  'clock11':'\\ud83d\\udd5a',\n  'clock1130':'\\ud83d\\udd66',\n  'clock12':'\\ud83d\\udd5b',\n  'clock1230':'\\ud83d\\udd67',\n  'clock130':'\\ud83d\\udd5c',\n  'clock2':'\\ud83d\\udd51',\n  'clock230':'\\ud83d\\udd5d',\n  'clock3':'\\ud83d\\udd52',\n  'clock330':'\\ud83d\\udd5e',\n  'clock4':'\\ud83d\\udd53',\n  'clock430':'\\ud83d\\udd5f',\n  'clock5':'\\ud83d\\udd54',\n  'clock530':'\\ud83d\\udd60',\n  'clock6':'\\ud83d\\udd55',\n  'clock630':'\\ud83d\\udd61',\n  'clock7':'\\ud83d\\udd56',\n  'clock730':'\\ud83d\\udd62',\n  'clock8':'\\ud83d\\udd57',\n  'clock830':'\\ud83d\\udd63',\n  'clock9':'\\ud83d\\udd58',\n  'clock930':'\\ud83d\\udd64',\n  'closed_book':'\\ud83d\\udcd5',\n  'closed_lock_with_key':'\\ud83d\\udd10',\n  'closed_umbrella':'\\ud83c\\udf02',\n  'cloud':'\\u2601\\ufe0f',\n  'cloud_with_lightning':'\\ud83c\\udf29',\n  'cloud_with_lightning_and_rain':'\\u26c8',\n  'cloud_with_rain':'\\ud83c\\udf27',\n  'cloud_with_snow':'\\ud83c\\udf28',\n  'clown_face':'\\ud83e\\udd21',\n  'clubs':'\\u2663\\ufe0f',\n  'cocktail':'\\ud83c\\udf78',\n  'coffee':'\\u2615\\ufe0f',\n  'coffin':'\\u26b0\\ufe0f',\n  'cold_sweat':'\\ud83d\\ude30',\n  'comet':'\\u2604\\ufe0f',\n  'computer':'\\ud83d\\udcbb',\n  'computer_mouse':'\\ud83d\\uddb1',\n  'confetti_ball':'\\ud83c\\udf8a',\n  'confounded':'\\ud83d\\ude16',\n  'confused':'\\ud83d\\ude15',\n  'congratulations':'\\u3297\\ufe0f',\n  'construction':'\\ud83d\\udea7',\n  'construction_worker_man':'\\ud83d\\udc77',\n  'construction_worker_woman':'\\ud83d\\udc77&zwj;\\u2640\\ufe0f',\n  'control_knobs':'\\ud83c\\udf9b',\n  'convenience_store':'\\ud83c\\udfea',\n  'cookie':'\\ud83c\\udf6a',\n  'cool':'\\ud83c\\udd92',\n  'policeman':'\\ud83d\\udc6e',\n  'copyright':'\\u00a9\\ufe0f',\n  'corn':'\\ud83c\\udf3d',\n  'couch_and_lamp':'\\ud83d\\udecb',\n  'couple':'\\ud83d\\udc6b',\n  'couple_with_heart_woman_man':'\\ud83d\\udc91',\n  'couple_with_heart_man_man':'\\ud83d\\udc68&zwj;\\u2764\\ufe0f&zwj;\\ud83d\\udc68',\n  'couple_with_heart_woman_woman':'\\ud83d\\udc69&zwj;\\u2764\\ufe0f&zwj;\\ud83d\\udc69',\n  'couplekiss_man_man':'\\ud83d\\udc68&zwj;\\u2764\\ufe0f&zwj;\\ud83d\\udc8b&zwj;\\ud83d\\udc68',\n  'couplekiss_man_woman':'\\ud83d\\udc8f',\n  'couplekiss_woman_woman':'\\ud83d\\udc69&zwj;\\u2764\\ufe0f&zwj;\\ud83d\\udc8b&zwj;\\ud83d\\udc69',\n  'cow':'\\ud83d\\udc2e',\n  'cow2':'\\ud83d\\udc04',\n  'cowboy_hat_face':'\\ud83e\\udd20',\n  'crab':'\\ud83e\\udd80',\n  'crayon':'\\ud83d\\udd8d',\n  'credit_card':'\\ud83d\\udcb3',\n  'crescent_moon':'\\ud83c\\udf19',\n  'cricket':'\\ud83c\\udfcf',\n  'crocodile':'\\ud83d\\udc0a',\n  'croissant':'\\ud83e\\udd50',\n  'crossed_fingers':'\\ud83e\\udd1e',\n  'crossed_flags':'\\ud83c\\udf8c',\n  'crossed_swords':'\\u2694\\ufe0f',\n  'crown':'\\ud83d\\udc51',\n  'cry':'\\ud83d\\ude22',\n  'crying_cat_face':'\\ud83d\\ude3f',\n  'crystal_ball':'\\ud83d\\udd2e',\n  'cucumber':'\\ud83e\\udd52',\n  'cupid':'\\ud83d\\udc98',\n  'curly_loop':'\\u27b0',\n  'currency_exchange':'\\ud83d\\udcb1',\n  'curry':'\\ud83c\\udf5b',\n  'custard':'\\ud83c\\udf6e',\n  'customs':'\\ud83d\\udec3',\n  'cyclone':'\\ud83c\\udf00',\n  'dagger':'\\ud83d\\udde1',\n  'dancer':'\\ud83d\\udc83',\n  'dancing_women':'\\ud83d\\udc6f',\n  'dancing_men':'\\ud83d\\udc6f&zwj;\\u2642\\ufe0f',\n  'dango':'\\ud83c\\udf61',\n  'dark_sunglasses':'\\ud83d\\udd76',\n  'dart':'\\ud83c\\udfaf',\n  'dash':'\\ud83d\\udca8',\n  'date':'\\ud83d\\udcc5',\n  'deciduous_tree':'\\ud83c\\udf33',\n  'deer':'\\ud83e\\udd8c',\n  'department_store':'\\ud83c\\udfec',\n  'derelict_house':'\\ud83c\\udfda',\n  'desert':'\\ud83c\\udfdc',\n  'desert_island':'\\ud83c\\udfdd',\n  'desktop_computer':'\\ud83d\\udda5',\n  'male_detective':'\\ud83d\\udd75\\ufe0f',\n  'diamond_shape_with_a_dot_inside':'\\ud83d\\udca0',\n  'diamonds':'\\u2666\\ufe0f',\n  'disappointed':'\\ud83d\\ude1e',\n  'disappointed_relieved':'\\ud83d\\ude25',\n  'dizzy':'\\ud83d\\udcab',\n  'dizzy_face':'\\ud83d\\ude35',\n  'do_not_litter':'\\ud83d\\udeaf',\n  'dog':'\\ud83d\\udc36',\n  'dog2':'\\ud83d\\udc15',\n  'dollar':'\\ud83d\\udcb5',\n  'dolls':'\\ud83c\\udf8e',\n  'dolphin':'\\ud83d\\udc2c',\n  'door':'\\ud83d\\udeaa',\n  'doughnut':'\\ud83c\\udf69',\n  'dove':'\\ud83d\\udd4a',\n  'dragon':'\\ud83d\\udc09',\n  'dragon_face':'\\ud83d\\udc32',\n  'dress':'\\ud83d\\udc57',\n  'dromedary_camel':'\\ud83d\\udc2a',\n  'drooling_face':'\\ud83e\\udd24',\n  'droplet':'\\ud83d\\udca7',\n  'drum':'\\ud83e\\udd41',\n  'duck':'\\ud83e\\udd86',\n  'dvd':'\\ud83d\\udcc0',\n  'e-mail':'\\ud83d\\udce7',\n  'eagle':'\\ud83e\\udd85',\n  'ear':'\\ud83d\\udc42',\n  'ear_of_rice':'\\ud83c\\udf3e',\n  'earth_africa':'\\ud83c\\udf0d',\n  'earth_americas':'\\ud83c\\udf0e',\n  'earth_asia':'\\ud83c\\udf0f',\n  'egg':'\\ud83e\\udd5a',\n  'eggplant':'\\ud83c\\udf46',\n  'eight_pointed_black_star':'\\u2734\\ufe0f',\n  'eight_spoked_asterisk':'\\u2733\\ufe0f',\n  'electric_plug':'\\ud83d\\udd0c',\n  'elephant':'\\ud83d\\udc18',\n  'email':'\\u2709\\ufe0f',\n  'end':'\\ud83d\\udd1a',\n  'envelope_with_arrow':'\\ud83d\\udce9',\n  'euro':'\\ud83d\\udcb6',\n  'european_castle':'\\ud83c\\udff0',\n  'european_post_office':'\\ud83c\\udfe4',\n  'evergreen_tree':'\\ud83c\\udf32',\n  'exclamation':'\\u2757\\ufe0f',\n  'expressionless':'\\ud83d\\ude11',\n  'eye':'\\ud83d\\udc41',\n  'eye_speech_bubble':'\\ud83d\\udc41&zwj;\\ud83d\\udde8',\n  'eyeglasses':'\\ud83d\\udc53',\n  'eyes':'\\ud83d\\udc40',\n  'face_with_head_bandage':'\\ud83e\\udd15',\n  'face_with_thermometer':'\\ud83e\\udd12',\n  'fist_oncoming':'\\ud83d\\udc4a',\n  'factory':'\\ud83c\\udfed',\n  'fallen_leaf':'\\ud83c\\udf42',\n  'family_man_woman_boy':'\\ud83d\\udc6a',\n  'family_man_boy':'\\ud83d\\udc68&zwj;\\ud83d\\udc66',\n  'family_man_boy_boy':'\\ud83d\\udc68&zwj;\\ud83d\\udc66&zwj;\\ud83d\\udc66',\n  'family_man_girl':'\\ud83d\\udc68&zwj;\\ud83d\\udc67',\n  'family_man_girl_boy':'\\ud83d\\udc68&zwj;\\ud83d\\udc67&zwj;\\ud83d\\udc66',\n  'family_man_girl_girl':'\\ud83d\\udc68&zwj;\\ud83d\\udc67&zwj;\\ud83d\\udc67',\n  'family_man_man_boy':'\\ud83d\\udc68&zwj;\\ud83d\\udc68&zwj;\\ud83d\\udc66',\n  'family_man_man_boy_boy':'\\ud83d\\udc68&zwj;\\ud83d\\udc68&zwj;\\ud83d\\udc66&zwj;\\ud83d\\udc66',\n  'family_man_man_girl':'\\ud83d\\udc68&zwj;\\ud83d\\udc68&zwj;\\ud83d\\udc67',\n  'family_man_man_girl_boy':'\\ud83d\\udc68&zwj;\\ud83d\\udc68&zwj;\\ud83d\\udc67&zwj;\\ud83d\\udc66',\n  'family_man_man_girl_girl':'\\ud83d\\udc68&zwj;\\ud83d\\udc68&zwj;\\ud83d\\udc67&zwj;\\ud83d\\udc67',\n  'family_man_woman_boy_boy':'\\ud83d\\udc68&zwj;\\ud83d\\udc69&zwj;\\ud83d\\udc66&zwj;\\ud83d\\udc66',\n  'family_man_woman_girl':'\\ud83d\\udc68&zwj;\\ud83d\\udc69&zwj;\\ud83d\\udc67',\n  'family_man_woman_girl_boy':'\\ud83d\\udc68&zwj;\\ud83d\\udc69&zwj;\\ud83d\\udc67&zwj;\\ud83d\\udc66',\n  'family_man_woman_girl_girl':'\\ud83d\\udc68&zwj;\\ud83d\\udc69&zwj;\\ud83d\\udc67&zwj;\\ud83d\\udc67',\n  'family_woman_boy':'\\ud83d\\udc69&zwj;\\ud83d\\udc66',\n  'family_woman_boy_boy':'\\ud83d\\udc69&zwj;\\ud83d\\udc66&zwj;\\ud83d\\udc66',\n  'family_woman_girl':'\\ud83d\\udc69&zwj;\\ud83d\\udc67',\n  'family_woman_girl_boy':'\\ud83d\\udc69&zwj;\\ud83d\\udc67&zwj;\\ud83d\\udc66',\n  'family_woman_girl_girl':'\\ud83d\\udc69&zwj;\\ud83d\\udc67&zwj;\\ud83d\\udc67',\n  'family_woman_woman_boy':'\\ud83d\\udc69&zwj;\\ud83d\\udc69&zwj;\\ud83d\\udc66',\n  'family_woman_woman_boy_boy':'\\ud83d\\udc69&zwj;\\ud83d\\udc69&zwj;\\ud83d\\udc66&zwj;\\ud83d\\udc66',\n  'family_woman_woman_girl':'\\ud83d\\udc69&zwj;\\ud83d\\udc69&zwj;\\ud83d\\udc67',\n  'family_woman_woman_girl_boy':'\\ud83d\\udc69&zwj;\\ud83d\\udc69&zwj;\\ud83d\\udc67&zwj;\\ud83d\\udc66',\n  'family_woman_woman_girl_girl':'\\ud83d\\udc69&zwj;\\ud83d\\udc69&zwj;\\ud83d\\udc67&zwj;\\ud83d\\udc67',\n  'fast_forward':'\\u23e9',\n  'fax':'\\ud83d\\udce0',\n  'fearful':'\\ud83d\\ude28',\n  'feet':'\\ud83d\\udc3e',\n  'female_detective':'\\ud83d\\udd75\\ufe0f&zwj;\\u2640\\ufe0f',\n  'ferris_wheel':'\\ud83c\\udfa1',\n  'ferry':'\\u26f4',\n  'field_hockey':'\\ud83c\\udfd1',\n  'file_cabinet':'\\ud83d\\uddc4',\n  'file_folder':'\\ud83d\\udcc1',\n  'film_projector':'\\ud83d\\udcfd',\n  'film_strip':'\\ud83c\\udf9e',\n  'fire':'\\ud83d\\udd25',\n  'fire_engine':'\\ud83d\\ude92',\n  'fireworks':'\\ud83c\\udf86',\n  'first_quarter_moon':'\\ud83c\\udf13',\n  'first_quarter_moon_with_face':'\\ud83c\\udf1b',\n  'fish':'\\ud83d\\udc1f',\n  'fish_cake':'\\ud83c\\udf65',\n  'fishing_pole_and_fish':'\\ud83c\\udfa3',\n  'fist_raised':'\\u270a',\n  'fist_left':'\\ud83e\\udd1b',\n  'fist_right':'\\ud83e\\udd1c',\n  'flags':'\\ud83c\\udf8f',\n  'flashlight':'\\ud83d\\udd26',\n  'fleur_de_lis':'\\u269c\\ufe0f',\n  'flight_arrival':'\\ud83d\\udeec',\n  'flight_departure':'\\ud83d\\udeeb',\n  'floppy_disk':'\\ud83d\\udcbe',\n  'flower_playing_cards':'\\ud83c\\udfb4',\n  'flushed':'\\ud83d\\ude33',\n  'fog':'\\ud83c\\udf2b',\n  'foggy':'\\ud83c\\udf01',\n  'football':'\\ud83c\\udfc8',\n  'footprints':'\\ud83d\\udc63',\n  'fork_and_knife':'\\ud83c\\udf74',\n  'fountain':'\\u26f2\\ufe0f',\n  'fountain_pen':'\\ud83d\\udd8b',\n  'four_leaf_clover':'\\ud83c\\udf40',\n  'fox_face':'\\ud83e\\udd8a',\n  'framed_picture':'\\ud83d\\uddbc',\n  'free':'\\ud83c\\udd93',\n  'fried_egg':'\\ud83c\\udf73',\n  'fried_shrimp':'\\ud83c\\udf64',\n  'fries':'\\ud83c\\udf5f',\n  'frog':'\\ud83d\\udc38',\n  'frowning':'\\ud83d\\ude26',\n  'frowning_face':'\\u2639\\ufe0f',\n  'frowning_man':'\\ud83d\\ude4d&zwj;\\u2642\\ufe0f',\n  'frowning_woman':'\\ud83d\\ude4d',\n  'middle_finger':'\\ud83d\\udd95',\n  'fuelpump':'\\u26fd\\ufe0f',\n  'full_moon':'\\ud83c\\udf15',\n  'full_moon_with_face':'\\ud83c\\udf1d',\n  'funeral_urn':'\\u26b1\\ufe0f',\n  'game_die':'\\ud83c\\udfb2',\n  'gear':'\\u2699\\ufe0f',\n  'gem':'\\ud83d\\udc8e',\n  'gemini':'\\u264a\\ufe0f',\n  'ghost':'\\ud83d\\udc7b',\n  'gift':'\\ud83c\\udf81',\n  'gift_heart':'\\ud83d\\udc9d',\n  'girl':'\\ud83d\\udc67',\n  'globe_with_meridians':'\\ud83c\\udf10',\n  'goal_net':'\\ud83e\\udd45',\n  'goat':'\\ud83d\\udc10',\n  'golf':'\\u26f3\\ufe0f',\n  'golfing_man':'\\ud83c\\udfcc\\ufe0f',\n  'golfing_woman':'\\ud83c\\udfcc\\ufe0f&zwj;\\u2640\\ufe0f',\n  'gorilla':'\\ud83e\\udd8d',\n  'grapes':'\\ud83c\\udf47',\n  'green_apple':'\\ud83c\\udf4f',\n  'green_book':'\\ud83d\\udcd7',\n  'green_heart':'\\ud83d\\udc9a',\n  'green_salad':'\\ud83e\\udd57',\n  'grey_exclamation':'\\u2755',\n  'grey_question':'\\u2754',\n  'grimacing':'\\ud83d\\ude2c',\n  'grin':'\\ud83d\\ude01',\n  'grinning':'\\ud83d\\ude00',\n  'guardsman':'\\ud83d\\udc82',\n  'guardswoman':'\\ud83d\\udc82&zwj;\\u2640\\ufe0f',\n  'guitar':'\\ud83c\\udfb8',\n  'gun':'\\ud83d\\udd2b',\n  'haircut_woman':'\\ud83d\\udc87',\n  'haircut_man':'\\ud83d\\udc87&zwj;\\u2642\\ufe0f',\n  'hamburger':'\\ud83c\\udf54',\n  'hammer':'\\ud83d\\udd28',\n  'hammer_and_pick':'\\u2692',\n  'hammer_and_wrench':'\\ud83d\\udee0',\n  'hamster':'\\ud83d\\udc39',\n  'hand':'\\u270b',\n  'handbag':'\\ud83d\\udc5c',\n  'handshake':'\\ud83e\\udd1d',\n  'hankey':'\\ud83d\\udca9',\n  'hatched_chick':'\\ud83d\\udc25',\n  'hatching_chick':'\\ud83d\\udc23',\n  'headphones':'\\ud83c\\udfa7',\n  'hear_no_evil':'\\ud83d\\ude49',\n  'heart':'\\u2764\\ufe0f',\n  'heart_decoration':'\\ud83d\\udc9f',\n  'heart_eyes':'\\ud83d\\ude0d',\n  'heart_eyes_cat':'\\ud83d\\ude3b',\n  'heartbeat':'\\ud83d\\udc93',\n  'heartpulse':'\\ud83d\\udc97',\n  'hearts':'\\u2665\\ufe0f',\n  'heavy_check_mark':'\\u2714\\ufe0f',\n  'heavy_division_sign':'\\u2797',\n  'heavy_dollar_sign':'\\ud83d\\udcb2',\n  'heavy_heart_exclamation':'\\u2763\\ufe0f',\n  'heavy_minus_sign':'\\u2796',\n  'heavy_multiplication_x':'\\u2716\\ufe0f',\n  'heavy_plus_sign':'\\u2795',\n  'helicopter':'\\ud83d\\ude81',\n  'herb':'\\ud83c\\udf3f',\n  'hibiscus':'\\ud83c\\udf3a',\n  'high_brightness':'\\ud83d\\udd06',\n  'high_heel':'\\ud83d\\udc60',\n  'hocho':'\\ud83d\\udd2a',\n  'hole':'\\ud83d\\udd73',\n  'honey_pot':'\\ud83c\\udf6f',\n  'horse':'\\ud83d\\udc34',\n  'horse_racing':'\\ud83c\\udfc7',\n  'hospital':'\\ud83c\\udfe5',\n  'hot_pepper':'\\ud83c\\udf36',\n  'hotdog':'\\ud83c\\udf2d',\n  'hotel':'\\ud83c\\udfe8',\n  'hotsprings':'\\u2668\\ufe0f',\n  'hourglass':'\\u231b\\ufe0f',\n  'hourglass_flowing_sand':'\\u23f3',\n  'house':'\\ud83c\\udfe0',\n  'house_with_garden':'\\ud83c\\udfe1',\n  'houses':'\\ud83c\\udfd8',\n  'hugs':'\\ud83e\\udd17',\n  'hushed':'\\ud83d\\ude2f',\n  'ice_cream':'\\ud83c\\udf68',\n  'ice_hockey':'\\ud83c\\udfd2',\n  'ice_skate':'\\u26f8',\n  'icecream':'\\ud83c\\udf66',\n  'id':'\\ud83c\\udd94',\n  'ideograph_advantage':'\\ud83c\\ude50',\n  'imp':'\\ud83d\\udc7f',\n  'inbox_tray':'\\ud83d\\udce5',\n  'incoming_envelope':'\\ud83d\\udce8',\n  'tipping_hand_woman':'\\ud83d\\udc81',\n  'information_source':'\\u2139\\ufe0f',\n  'innocent':'\\ud83d\\ude07',\n  'interrobang':'\\u2049\\ufe0f',\n  'iphone':'\\ud83d\\udcf1',\n  'izakaya_lantern':'\\ud83c\\udfee',\n  'jack_o_lantern':'\\ud83c\\udf83',\n  'japan':'\\ud83d\\uddfe',\n  'japanese_castle':'\\ud83c\\udfef',\n  'japanese_goblin':'\\ud83d\\udc7a',\n  'japanese_ogre':'\\ud83d\\udc79',\n  'jeans':'\\ud83d\\udc56',\n  'joy':'\\ud83d\\ude02',\n  'joy_cat':'\\ud83d\\ude39',\n  'joystick':'\\ud83d\\udd79',\n  'kaaba':'\\ud83d\\udd4b',\n  'key':'\\ud83d\\udd11',\n  'keyboard':'\\u2328\\ufe0f',\n  'keycap_ten':'\\ud83d\\udd1f',\n  'kick_scooter':'\\ud83d\\udef4',\n  'kimono':'\\ud83d\\udc58',\n  'kiss':'\\ud83d\\udc8b',\n  'kissing':'\\ud83d\\ude17',\n  'kissing_cat':'\\ud83d\\ude3d',\n  'kissing_closed_eyes':'\\ud83d\\ude1a',\n  'kissing_heart':'\\ud83d\\ude18',\n  'kissing_smiling_eyes':'\\ud83d\\ude19',\n  'kiwi_fruit':'\\ud83e\\udd5d',\n  'koala':'\\ud83d\\udc28',\n  'koko':'\\ud83c\\ude01',\n  'label':'\\ud83c\\udff7',\n  'large_blue_circle':'\\ud83d\\udd35',\n  'large_blue_diamond':'\\ud83d\\udd37',\n  'large_orange_diamond':'\\ud83d\\udd36',\n  'last_quarter_moon':'\\ud83c\\udf17',\n  'last_quarter_moon_with_face':'\\ud83c\\udf1c',\n  'latin_cross':'\\u271d\\ufe0f',\n  'laughing':'\\ud83d\\ude06',\n  'leaves':'\\ud83c\\udf43',\n  'ledger':'\\ud83d\\udcd2',\n  'left_luggage':'\\ud83d\\udec5',\n  'left_right_arrow':'\\u2194\\ufe0f',\n  'leftwards_arrow_with_hook':'\\u21a9\\ufe0f',\n  'lemon':'\\ud83c\\udf4b',\n  'leo':'\\u264c\\ufe0f',\n  'leopard':'\\ud83d\\udc06',\n  'level_slider':'\\ud83c\\udf9a',\n  'libra':'\\u264e\\ufe0f',\n  'light_rail':'\\ud83d\\ude88',\n  'link':'\\ud83d\\udd17',\n  'lion':'\\ud83e\\udd81',\n  'lips':'\\ud83d\\udc44',\n  'lipstick':'\\ud83d\\udc84',\n  'lizard':'\\ud83e\\udd8e',\n  'lock':'\\ud83d\\udd12',\n  'lock_with_ink_pen':'\\ud83d\\udd0f',\n  'lollipop':'\\ud83c\\udf6d',\n  'loop':'\\u27bf',\n  'loud_sound':'\\ud83d\\udd0a',\n  'loudspeaker':'\\ud83d\\udce2',\n  'love_hotel':'\\ud83c\\udfe9',\n  'love_letter':'\\ud83d\\udc8c',\n  'low_brightness':'\\ud83d\\udd05',\n  'lying_face':'\\ud83e\\udd25',\n  'm':'\\u24c2\\ufe0f',\n  'mag':'\\ud83d\\udd0d',\n  'mag_right':'\\ud83d\\udd0e',\n  'mahjong':'\\ud83c\\udc04\\ufe0f',\n  'mailbox':'\\ud83d\\udceb',\n  'mailbox_closed':'\\ud83d\\udcea',\n  'mailbox_with_mail':'\\ud83d\\udcec',\n  'mailbox_with_no_mail':'\\ud83d\\udced',\n  'man':'\\ud83d\\udc68',\n  'man_artist':'\\ud83d\\udc68&zwj;\\ud83c\\udfa8',\n  'man_astronaut':'\\ud83d\\udc68&zwj;\\ud83d\\ude80',\n  'man_cartwheeling':'\\ud83e\\udd38&zwj;\\u2642\\ufe0f',\n  'man_cook':'\\ud83d\\udc68&zwj;\\ud83c\\udf73',\n  'man_dancing':'\\ud83d\\udd7a',\n  'man_facepalming':'\\ud83e\\udd26&zwj;\\u2642\\ufe0f',\n  'man_factory_worker':'\\ud83d\\udc68&zwj;\\ud83c\\udfed',\n  'man_farmer':'\\ud83d\\udc68&zwj;\\ud83c\\udf3e',\n  'man_firefighter':'\\ud83d\\udc68&zwj;\\ud83d\\ude92',\n  'man_health_worker':'\\ud83d\\udc68&zwj;\\u2695\\ufe0f',\n  'man_in_tuxedo':'\\ud83e\\udd35',\n  'man_judge':'\\ud83d\\udc68&zwj;\\u2696\\ufe0f',\n  'man_juggling':'\\ud83e\\udd39&zwj;\\u2642\\ufe0f',\n  'man_mechanic':'\\ud83d\\udc68&zwj;\\ud83d\\udd27',\n  'man_office_worker':'\\ud83d\\udc68&zwj;\\ud83d\\udcbc',\n  'man_pilot':'\\ud83d\\udc68&zwj;\\u2708\\ufe0f',\n  'man_playing_handball':'\\ud83e\\udd3e&zwj;\\u2642\\ufe0f',\n  'man_playing_water_polo':'\\ud83e\\udd3d&zwj;\\u2642\\ufe0f',\n  'man_scientist':'\\ud83d\\udc68&zwj;\\ud83d\\udd2c',\n  'man_shrugging':'\\ud83e\\udd37&zwj;\\u2642\\ufe0f',\n  'man_singer':'\\ud83d\\udc68&zwj;\\ud83c\\udfa4',\n  'man_student':'\\ud83d\\udc68&zwj;\\ud83c\\udf93',\n  'man_teacher':'\\ud83d\\udc68&zwj;\\ud83c\\udfeb',\n  'man_technologist':'\\ud83d\\udc68&zwj;\\ud83d\\udcbb',\n  'man_with_gua_pi_mao':'\\ud83d\\udc72',\n  'man_with_turban':'\\ud83d\\udc73',\n  'tangerine':'\\ud83c\\udf4a',\n  'mans_shoe':'\\ud83d\\udc5e',\n  'mantelpiece_clock':'\\ud83d\\udd70',\n  'maple_leaf':'\\ud83c\\udf41',\n  'martial_arts_uniform':'\\ud83e\\udd4b',\n  'mask':'\\ud83d\\ude37',\n  'massage_woman':'\\ud83d\\udc86',\n  'massage_man':'\\ud83d\\udc86&zwj;\\u2642\\ufe0f',\n  'meat_on_bone':'\\ud83c\\udf56',\n  'medal_military':'\\ud83c\\udf96',\n  'medal_sports':'\\ud83c\\udfc5',\n  'mega':'\\ud83d\\udce3',\n  'melon':'\\ud83c\\udf48',\n  'memo':'\\ud83d\\udcdd',\n  'men_wrestling':'\\ud83e\\udd3c&zwj;\\u2642\\ufe0f',\n  'menorah':'\\ud83d\\udd4e',\n  'mens':'\\ud83d\\udeb9',\n  'metal':'\\ud83e\\udd18',\n  'metro':'\\ud83d\\ude87',\n  'microphone':'\\ud83c\\udfa4',\n  'microscope':'\\ud83d\\udd2c',\n  'milk_glass':'\\ud83e\\udd5b',\n  'milky_way':'\\ud83c\\udf0c',\n  'minibus':'\\ud83d\\ude90',\n  'minidisc':'\\ud83d\\udcbd',\n  'mobile_phone_off':'\\ud83d\\udcf4',\n  'money_mouth_face':'\\ud83e\\udd11',\n  'money_with_wings':'\\ud83d\\udcb8',\n  'moneybag':'\\ud83d\\udcb0',\n  'monkey':'\\ud83d\\udc12',\n  'monkey_face':'\\ud83d\\udc35',\n  'monorail':'\\ud83d\\ude9d',\n  'moon':'\\ud83c\\udf14',\n  'mortar_board':'\\ud83c\\udf93',\n  'mosque':'\\ud83d\\udd4c',\n  'motor_boat':'\\ud83d\\udee5',\n  'motor_scooter':'\\ud83d\\udef5',\n  'motorcycle':'\\ud83c\\udfcd',\n  'motorway':'\\ud83d\\udee3',\n  'mount_fuji':'\\ud83d\\uddfb',\n  'mountain':'\\u26f0',\n  'mountain_biking_man':'\\ud83d\\udeb5',\n  'mountain_biking_woman':'\\ud83d\\udeb5&zwj;\\u2640\\ufe0f',\n  'mountain_cableway':'\\ud83d\\udea0',\n  'mountain_railway':'\\ud83d\\ude9e',\n  'mountain_snow':'\\ud83c\\udfd4',\n  'mouse':'\\ud83d\\udc2d',\n  'mouse2':'\\ud83d\\udc01',\n  'movie_camera':'\\ud83c\\udfa5',\n  'moyai':'\\ud83d\\uddff',\n  'mrs_claus':'\\ud83e\\udd36',\n  'muscle':'\\ud83d\\udcaa',\n  'mushroom':'\\ud83c\\udf44',\n  'musical_keyboard':'\\ud83c\\udfb9',\n  'musical_note':'\\ud83c\\udfb5',\n  'musical_score':'\\ud83c\\udfbc',\n  'mute':'\\ud83d\\udd07',\n  'nail_care':'\\ud83d\\udc85',\n  'name_badge':'\\ud83d\\udcdb',\n  'national_park':'\\ud83c\\udfde',\n  'nauseated_face':'\\ud83e\\udd22',\n  'necktie':'\\ud83d\\udc54',\n  'negative_squared_cross_mark':'\\u274e',\n  'nerd_face':'\\ud83e\\udd13',\n  'neutral_face':'\\ud83d\\ude10',\n  'new':'\\ud83c\\udd95',\n  'new_moon':'\\ud83c\\udf11',\n  'new_moon_with_face':'\\ud83c\\udf1a',\n  'newspaper':'\\ud83d\\udcf0',\n  'newspaper_roll':'\\ud83d\\uddde',\n  'next_track_button':'\\u23ed',\n  'ng':'\\ud83c\\udd96',\n  'no_good_man':'\\ud83d\\ude45&zwj;\\u2642\\ufe0f',\n  'no_good_woman':'\\ud83d\\ude45',\n  'night_with_stars':'\\ud83c\\udf03',\n  'no_bell':'\\ud83d\\udd15',\n  'no_bicycles':'\\ud83d\\udeb3',\n  'no_entry':'\\u26d4\\ufe0f',\n  'no_entry_sign':'\\ud83d\\udeab',\n  'no_mobile_phones':'\\ud83d\\udcf5',\n  'no_mouth':'\\ud83d\\ude36',\n  'no_pedestrians':'\\ud83d\\udeb7',\n  'no_smoking':'\\ud83d\\udead',\n  'non-potable_water':'\\ud83d\\udeb1',\n  'nose':'\\ud83d\\udc43',\n  'notebook':'\\ud83d\\udcd3',\n  'notebook_with_decorative_cover':'\\ud83d\\udcd4',\n  'notes':'\\ud83c\\udfb6',\n  'nut_and_bolt':'\\ud83d\\udd29',\n  'o':'\\u2b55\\ufe0f',\n  'o2':'\\ud83c\\udd7e\\ufe0f',\n  'ocean':'\\ud83c\\udf0a',\n  'octopus':'\\ud83d\\udc19',\n  'oden':'\\ud83c\\udf62',\n  'office':'\\ud83c\\udfe2',\n  'oil_drum':'\\ud83d\\udee2',\n  'ok':'\\ud83c\\udd97',\n  'ok_hand':'\\ud83d\\udc4c',\n  'ok_man':'\\ud83d\\ude46&zwj;\\u2642\\ufe0f',\n  'ok_woman':'\\ud83d\\ude46',\n  'old_key':'\\ud83d\\udddd',\n  'older_man':'\\ud83d\\udc74',\n  'older_woman':'\\ud83d\\udc75',\n  'om':'\\ud83d\\udd49',\n  'on':'\\ud83d\\udd1b',\n  'oncoming_automobile':'\\ud83d\\ude98',\n  'oncoming_bus':'\\ud83d\\ude8d',\n  'oncoming_police_car':'\\ud83d\\ude94',\n  'oncoming_taxi':'\\ud83d\\ude96',\n  'open_file_folder':'\\ud83d\\udcc2',\n  'open_hands':'\\ud83d\\udc50',\n  'open_mouth':'\\ud83d\\ude2e',\n  'open_umbrella':'\\u2602\\ufe0f',\n  'ophiuchus':'\\u26ce',\n  'orange_book':'\\ud83d\\udcd9',\n  'orthodox_cross':'\\u2626\\ufe0f',\n  'outbox_tray':'\\ud83d\\udce4',\n  'owl':'\\ud83e\\udd89',\n  'ox':'\\ud83d\\udc02',\n  'package':'\\ud83d\\udce6',\n  'page_facing_up':'\\ud83d\\udcc4',\n  'page_with_curl':'\\ud83d\\udcc3',\n  'pager':'\\ud83d\\udcdf',\n  'paintbrush':'\\ud83d\\udd8c',\n  'palm_tree':'\\ud83c\\udf34',\n  'pancakes':'\\ud83e\\udd5e',\n  'panda_face':'\\ud83d\\udc3c',\n  'paperclip':'\\ud83d\\udcce',\n  'paperclips':'\\ud83d\\udd87',\n  'parasol_on_ground':'\\u26f1',\n  'parking':'\\ud83c\\udd7f\\ufe0f',\n  'part_alternation_mark':'\\u303d\\ufe0f',\n  'partly_sunny':'\\u26c5\\ufe0f',\n  'passenger_ship':'\\ud83d\\udef3',\n  'passport_control':'\\ud83d\\udec2',\n  'pause_button':'\\u23f8',\n  'peace_symbol':'\\u262e\\ufe0f',\n  'peach':'\\ud83c\\udf51',\n  'peanuts':'\\ud83e\\udd5c',\n  'pear':'\\ud83c\\udf50',\n  'pen':'\\ud83d\\udd8a',\n  'pencil2':'\\u270f\\ufe0f',\n  'penguin':'\\ud83d\\udc27',\n  'pensive':'\\ud83d\\ude14',\n  'performing_arts':'\\ud83c\\udfad',\n  'persevere':'\\ud83d\\ude23',\n  'person_fencing':'\\ud83e\\udd3a',\n  'pouting_woman':'\\ud83d\\ude4e',\n  'phone':'\\u260e\\ufe0f',\n  'pick':'\\u26cf',\n  'pig':'\\ud83d\\udc37',\n  'pig2':'\\ud83d\\udc16',\n  'pig_nose':'\\ud83d\\udc3d',\n  'pill':'\\ud83d\\udc8a',\n  'pineapple':'\\ud83c\\udf4d',\n  'ping_pong':'\\ud83c\\udfd3',\n  'pisces':'\\u2653\\ufe0f',\n  'pizza':'\\ud83c\\udf55',\n  'place_of_worship':'\\ud83d\\uded0',\n  'plate_with_cutlery':'\\ud83c\\udf7d',\n  'play_or_pause_button':'\\u23ef',\n  'point_down':'\\ud83d\\udc47',\n  'point_left':'\\ud83d\\udc48',\n  'point_right':'\\ud83d\\udc49',\n  'point_up':'\\u261d\\ufe0f',\n  'point_up_2':'\\ud83d\\udc46',\n  'police_car':'\\ud83d\\ude93',\n  'policewoman':'\\ud83d\\udc6e&zwj;\\u2640\\ufe0f',\n  'poodle':'\\ud83d\\udc29',\n  'popcorn':'\\ud83c\\udf7f',\n  'post_office':'\\ud83c\\udfe3',\n  'postal_horn':'\\ud83d\\udcef',\n  'postbox':'\\ud83d\\udcee',\n  'potable_water':'\\ud83d\\udeb0',\n  'potato':'\\ud83e\\udd54',\n  'pouch':'\\ud83d\\udc5d',\n  'poultry_leg':'\\ud83c\\udf57',\n  'pound':'\\ud83d\\udcb7',\n  'rage':'\\ud83d\\ude21',\n  'pouting_cat':'\\ud83d\\ude3e',\n  'pouting_man':'\\ud83d\\ude4e&zwj;\\u2642\\ufe0f',\n  'pray':'\\ud83d\\ude4f',\n  'prayer_beads':'\\ud83d\\udcff',\n  'pregnant_woman':'\\ud83e\\udd30',\n  'previous_track_button':'\\u23ee',\n  'prince':'\\ud83e\\udd34',\n  'princess':'\\ud83d\\udc78',\n  'printer':'\\ud83d\\udda8',\n  'purple_heart':'\\ud83d\\udc9c',\n  'purse':'\\ud83d\\udc5b',\n  'pushpin':'\\ud83d\\udccc',\n  'put_litter_in_its_place':'\\ud83d\\udeae',\n  'question':'\\u2753',\n  'rabbit':'\\ud83d\\udc30',\n  'rabbit2':'\\ud83d\\udc07',\n  'racehorse':'\\ud83d\\udc0e',\n  'racing_car':'\\ud83c\\udfce',\n  'radio':'\\ud83d\\udcfb',\n  'radio_button':'\\ud83d\\udd18',\n  'radioactive':'\\u2622\\ufe0f',\n  'railway_car':'\\ud83d\\ude83',\n  'railway_track':'\\ud83d\\udee4',\n  'rainbow':'\\ud83c\\udf08',\n  'rainbow_flag':'\\ud83c\\udff3\\ufe0f&zwj;\\ud83c\\udf08',\n  'raised_back_of_hand':'\\ud83e\\udd1a',\n  'raised_hand_with_fingers_splayed':'\\ud83d\\udd90',\n  'raised_hands':'\\ud83d\\ude4c',\n  'raising_hand_woman':'\\ud83d\\ude4b',\n  'raising_hand_man':'\\ud83d\\ude4b&zwj;\\u2642\\ufe0f',\n  'ram':'\\ud83d\\udc0f',\n  'ramen':'\\ud83c\\udf5c',\n  'rat':'\\ud83d\\udc00',\n  'record_button':'\\u23fa',\n  'recycle':'\\u267b\\ufe0f',\n  'red_circle':'\\ud83d\\udd34',\n  'registered':'\\u00ae\\ufe0f',\n  'relaxed':'\\u263a\\ufe0f',\n  'relieved':'\\ud83d\\ude0c',\n  'reminder_ribbon':'\\ud83c\\udf97',\n  'repeat':'\\ud83d\\udd01',\n  'repeat_one':'\\ud83d\\udd02',\n  'rescue_worker_helmet':'\\u26d1',\n  'restroom':'\\ud83d\\udebb',\n  'revolving_hearts':'\\ud83d\\udc9e',\n  'rewind':'\\u23ea',\n  'rhinoceros':'\\ud83e\\udd8f',\n  'ribbon':'\\ud83c\\udf80',\n  'rice':'\\ud83c\\udf5a',\n  'rice_ball':'\\ud83c\\udf59',\n  'rice_cracker':'\\ud83c\\udf58',\n  'rice_scene':'\\ud83c\\udf91',\n  'right_anger_bubble':'\\ud83d\\uddef',\n  'ring':'\\ud83d\\udc8d',\n  'robot':'\\ud83e\\udd16',\n  'rocket':'\\ud83d\\ude80',\n  'rofl':'\\ud83e\\udd23',\n  'roll_eyes':'\\ud83d\\ude44',\n  'roller_coaster':'\\ud83c\\udfa2',\n  'rooster':'\\ud83d\\udc13',\n  'rose':'\\ud83c\\udf39',\n  'rosette':'\\ud83c\\udff5',\n  'rotating_light':'\\ud83d\\udea8',\n  'round_pushpin':'\\ud83d\\udccd',\n  'rowing_man':'\\ud83d\\udea3',\n  'rowing_woman':'\\ud83d\\udea3&zwj;\\u2640\\ufe0f',\n  'rugby_football':'\\ud83c\\udfc9',\n  'running_man':'\\ud83c\\udfc3',\n  'running_shirt_with_sash':'\\ud83c\\udfbd',\n  'running_woman':'\\ud83c\\udfc3&zwj;\\u2640\\ufe0f',\n  'sa':'\\ud83c\\ude02\\ufe0f',\n  'sagittarius':'\\u2650\\ufe0f',\n  'sake':'\\ud83c\\udf76',\n  'sandal':'\\ud83d\\udc61',\n  'santa':'\\ud83c\\udf85',\n  'satellite':'\\ud83d\\udce1',\n  'saxophone':'\\ud83c\\udfb7',\n  'school':'\\ud83c\\udfeb',\n  'school_satchel':'\\ud83c\\udf92',\n  'scissors':'\\u2702\\ufe0f',\n  'scorpion':'\\ud83e\\udd82',\n  'scorpius':'\\u264f\\ufe0f',\n  'scream':'\\ud83d\\ude31',\n  'scream_cat':'\\ud83d\\ude40',\n  'scroll':'\\ud83d\\udcdc',\n  'seat':'\\ud83d\\udcba',\n  'secret':'\\u3299\\ufe0f',\n  'see_no_evil':'\\ud83d\\ude48',\n  'seedling':'\\ud83c\\udf31',\n  'selfie':'\\ud83e\\udd33',\n  'shallow_pan_of_food':'\\ud83e\\udd58',\n  'shamrock':'\\u2618\\ufe0f',\n  'shark':'\\ud83e\\udd88',\n  'shaved_ice':'\\ud83c\\udf67',\n  'sheep':'\\ud83d\\udc11',\n  'shell':'\\ud83d\\udc1a',\n  'shield':'\\ud83d\\udee1',\n  'shinto_shrine':'\\u26e9',\n  'ship':'\\ud83d\\udea2',\n  'shirt':'\\ud83d\\udc55',\n  'shopping':'\\ud83d\\udecd',\n  'shopping_cart':'\\ud83d\\uded2',\n  'shower':'\\ud83d\\udebf',\n  'shrimp':'\\ud83e\\udd90',\n  'signal_strength':'\\ud83d\\udcf6',\n  'six_pointed_star':'\\ud83d\\udd2f',\n  'ski':'\\ud83c\\udfbf',\n  'skier':'\\u26f7',\n  'skull':'\\ud83d\\udc80',\n  'skull_and_crossbones':'\\u2620\\ufe0f',\n  'sleeping':'\\ud83d\\ude34',\n  'sleeping_bed':'\\ud83d\\udecc',\n  'sleepy':'\\ud83d\\ude2a',\n  'slightly_frowning_face':'\\ud83d\\ude41',\n  'slightly_smiling_face':'\\ud83d\\ude42',\n  'slot_machine':'\\ud83c\\udfb0',\n  'small_airplane':'\\ud83d\\udee9',\n  'small_blue_diamond':'\\ud83d\\udd39',\n  'small_orange_diamond':'\\ud83d\\udd38',\n  'small_red_triangle':'\\ud83d\\udd3a',\n  'small_red_triangle_down':'\\ud83d\\udd3b',\n  'smile':'\\ud83d\\ude04',\n  'smile_cat':'\\ud83d\\ude38',\n  'smiley':'\\ud83d\\ude03',\n  'smiley_cat':'\\ud83d\\ude3a',\n  'smiling_imp':'\\ud83d\\ude08',\n  'smirk':'\\ud83d\\ude0f',\n  'smirk_cat':'\\ud83d\\ude3c',\n  'smoking':'\\ud83d\\udeac',\n  'snail':'\\ud83d\\udc0c',\n  'snake':'\\ud83d\\udc0d',\n  'sneezing_face':'\\ud83e\\udd27',\n  'snowboarder':'\\ud83c\\udfc2',\n  'snowflake':'\\u2744\\ufe0f',\n  'snowman':'\\u26c4\\ufe0f',\n  'snowman_with_snow':'\\u2603\\ufe0f',\n  'sob':'\\ud83d\\ude2d',\n  'soccer':'\\u26bd\\ufe0f',\n  'soon':'\\ud83d\\udd1c',\n  'sos':'\\ud83c\\udd98',\n  'sound':'\\ud83d\\udd09',\n  'space_invader':'\\ud83d\\udc7e',\n  'spades':'\\u2660\\ufe0f',\n  'spaghetti':'\\ud83c\\udf5d',\n  'sparkle':'\\u2747\\ufe0f',\n  'sparkler':'\\ud83c\\udf87',\n  'sparkles':'\\u2728',\n  'sparkling_heart':'\\ud83d\\udc96',\n  'speak_no_evil':'\\ud83d\\ude4a',\n  'speaker':'\\ud83d\\udd08',\n  'speaking_head':'\\ud83d\\udde3',\n  'speech_balloon':'\\ud83d\\udcac',\n  'speedboat':'\\ud83d\\udea4',\n  'spider':'\\ud83d\\udd77',\n  'spider_web':'\\ud83d\\udd78',\n  'spiral_calendar':'\\ud83d\\uddd3',\n  'spiral_notepad':'\\ud83d\\uddd2',\n  'spoon':'\\ud83e\\udd44',\n  'squid':'\\ud83e\\udd91',\n  'stadium':'\\ud83c\\udfdf',\n  'star':'\\u2b50\\ufe0f',\n  'star2':'\\ud83c\\udf1f',\n  'star_and_crescent':'\\u262a\\ufe0f',\n  'star_of_david':'\\u2721\\ufe0f',\n  'stars':'\\ud83c\\udf20',\n  'station':'\\ud83d\\ude89',\n  'statue_of_liberty':'\\ud83d\\uddfd',\n  'steam_locomotive':'\\ud83d\\ude82',\n  'stew':'\\ud83c\\udf72',\n  'stop_button':'\\u23f9',\n  'stop_sign':'\\ud83d\\uded1',\n  'stopwatch':'\\u23f1',\n  'straight_ruler':'\\ud83d\\udccf',\n  'strawberry':'\\ud83c\\udf53',\n  'stuck_out_tongue':'\\ud83d\\ude1b',\n  'stuck_out_tongue_closed_eyes':'\\ud83d\\ude1d',\n  'stuck_out_tongue_winking_eye':'\\ud83d\\ude1c',\n  'studio_microphone':'\\ud83c\\udf99',\n  'stuffed_flatbread':'\\ud83e\\udd59',\n  'sun_behind_large_cloud':'\\ud83c\\udf25',\n  'sun_behind_rain_cloud':'\\ud83c\\udf26',\n  'sun_behind_small_cloud':'\\ud83c\\udf24',\n  'sun_with_face':'\\ud83c\\udf1e',\n  'sunflower':'\\ud83c\\udf3b',\n  'sunglasses':'\\ud83d\\ude0e',\n  'sunny':'\\u2600\\ufe0f',\n  'sunrise':'\\ud83c\\udf05',\n  'sunrise_over_mountains':'\\ud83c\\udf04',\n  'surfing_man':'\\ud83c\\udfc4',\n  'surfing_woman':'\\ud83c\\udfc4&zwj;\\u2640\\ufe0f',\n  'sushi':'\\ud83c\\udf63',\n  'suspension_railway':'\\ud83d\\ude9f',\n  'sweat':'\\ud83d\\ude13',\n  'sweat_drops':'\\ud83d\\udca6',\n  'sweat_smile':'\\ud83d\\ude05',\n  'sweet_potato':'\\ud83c\\udf60',\n  'swimming_man':'\\ud83c\\udfca',\n  'swimming_woman':'\\ud83c\\udfca&zwj;\\u2640\\ufe0f',\n  'symbols':'\\ud83d\\udd23',\n  'synagogue':'\\ud83d\\udd4d',\n  'syringe':'\\ud83d\\udc89',\n  'taco':'\\ud83c\\udf2e',\n  'tada':'\\ud83c\\udf89',\n  'tanabata_tree':'\\ud83c\\udf8b',\n  'taurus':'\\u2649\\ufe0f',\n  'taxi':'\\ud83d\\ude95',\n  'tea':'\\ud83c\\udf75',\n  'telephone_receiver':'\\ud83d\\udcde',\n  'telescope':'\\ud83d\\udd2d',\n  'tennis':'\\ud83c\\udfbe',\n  'tent':'\\u26fa\\ufe0f',\n  'thermometer':'\\ud83c\\udf21',\n  'thinking':'\\ud83e\\udd14',\n  'thought_balloon':'\\ud83d\\udcad',\n  'ticket':'\\ud83c\\udfab',\n  'tickets':'\\ud83c\\udf9f',\n  'tiger':'\\ud83d\\udc2f',\n  'tiger2':'\\ud83d\\udc05',\n  'timer_clock':'\\u23f2',\n  'tipping_hand_man':'\\ud83d\\udc81&zwj;\\u2642\\ufe0f',\n  'tired_face':'\\ud83d\\ude2b',\n  'tm':'\\u2122\\ufe0f',\n  'toilet':'\\ud83d\\udebd',\n  'tokyo_tower':'\\ud83d\\uddfc',\n  'tomato':'\\ud83c\\udf45',\n  'tongue':'\\ud83d\\udc45',\n  'top':'\\ud83d\\udd1d',\n  'tophat':'\\ud83c\\udfa9',\n  'tornado':'\\ud83c\\udf2a',\n  'trackball':'\\ud83d\\uddb2',\n  'tractor':'\\ud83d\\ude9c',\n  'traffic_light':'\\ud83d\\udea5',\n  'train':'\\ud83d\\ude8b',\n  'train2':'\\ud83d\\ude86',\n  'tram':'\\ud83d\\ude8a',\n  'triangular_flag_on_post':'\\ud83d\\udea9',\n  'triangular_ruler':'\\ud83d\\udcd0',\n  'trident':'\\ud83d\\udd31',\n  'triumph':'\\ud83d\\ude24',\n  'trolleybus':'\\ud83d\\ude8e',\n  'trophy':'\\ud83c\\udfc6',\n  'tropical_drink':'\\ud83c\\udf79',\n  'tropical_fish':'\\ud83d\\udc20',\n  'truck':'\\ud83d\\ude9a',\n  'trumpet':'\\ud83c\\udfba',\n  'tulip':'\\ud83c\\udf37',\n  'tumbler_glass':'\\ud83e\\udd43',\n  'turkey':'\\ud83e\\udd83',\n  'turtle':'\\ud83d\\udc22',\n  'tv':'\\ud83d\\udcfa',\n  'twisted_rightwards_arrows':'\\ud83d\\udd00',\n  'two_hearts':'\\ud83d\\udc95',\n  'two_men_holding_hands':'\\ud83d\\udc6c',\n  'two_women_holding_hands':'\\ud83d\\udc6d',\n  'u5272':'\\ud83c\\ude39',\n  'u5408':'\\ud83c\\ude34',\n  'u55b6':'\\ud83c\\ude3a',\n  'u6307':'\\ud83c\\ude2f\\ufe0f',\n  'u6708':'\\ud83c\\ude37\\ufe0f',\n  'u6709':'\\ud83c\\ude36',\n  'u6e80':'\\ud83c\\ude35',\n  'u7121':'\\ud83c\\ude1a\\ufe0f',\n  'u7533':'\\ud83c\\ude38',\n  'u7981':'\\ud83c\\ude32',\n  'u7a7a':'\\ud83c\\ude33',\n  'umbrella':'\\u2614\\ufe0f',\n  'unamused':'\\ud83d\\ude12',\n  'underage':'\\ud83d\\udd1e',\n  'unicorn':'\\ud83e\\udd84',\n  'unlock':'\\ud83d\\udd13',\n  'up':'\\ud83c\\udd99',\n  'upside_down_face':'\\ud83d\\ude43',\n  'v':'\\u270c\\ufe0f',\n  'vertical_traffic_light':'\\ud83d\\udea6',\n  'vhs':'\\ud83d\\udcfc',\n  'vibration_mode':'\\ud83d\\udcf3',\n  'video_camera':'\\ud83d\\udcf9',\n  'video_game':'\\ud83c\\udfae',\n  'violin':'\\ud83c\\udfbb',\n  'virgo':'\\u264d\\ufe0f',\n  'volcano':'\\ud83c\\udf0b',\n  'volleyball':'\\ud83c\\udfd0',\n  'vs':'\\ud83c\\udd9a',\n  'vulcan_salute':'\\ud83d\\udd96',\n  'walking_man':'\\ud83d\\udeb6',\n  'walking_woman':'\\ud83d\\udeb6&zwj;\\u2640\\ufe0f',\n  'waning_crescent_moon':'\\ud83c\\udf18',\n  'waning_gibbous_moon':'\\ud83c\\udf16',\n  'warning':'\\u26a0\\ufe0f',\n  'wastebasket':'\\ud83d\\uddd1',\n  'watch':'\\u231a\\ufe0f',\n  'water_buffalo':'\\ud83d\\udc03',\n  'watermelon':'\\ud83c\\udf49',\n  'wave':'\\ud83d\\udc4b',\n  'wavy_dash':'\\u3030\\ufe0f',\n  'waxing_crescent_moon':'\\ud83c\\udf12',\n  'wc':'\\ud83d\\udebe',\n  'weary':'\\ud83d\\ude29',\n  'wedding':'\\ud83d\\udc92',\n  'weight_lifting_man':'\\ud83c\\udfcb\\ufe0f',\n  'weight_lifting_woman':'\\ud83c\\udfcb\\ufe0f&zwj;\\u2640\\ufe0f',\n  'whale':'\\ud83d\\udc33',\n  'whale2':'\\ud83d\\udc0b',\n  'wheel_of_dharma':'\\u2638\\ufe0f',\n  'wheelchair':'\\u267f\\ufe0f',\n  'white_check_mark':'\\u2705',\n  'white_circle':'\\u26aa\\ufe0f',\n  'white_flag':'\\ud83c\\udff3\\ufe0f',\n  'white_flower':'\\ud83d\\udcae',\n  'white_large_square':'\\u2b1c\\ufe0f',\n  'white_medium_small_square':'\\u25fd\\ufe0f',\n  'white_medium_square':'\\u25fb\\ufe0f',\n  'white_small_square':'\\u25ab\\ufe0f',\n  'white_square_button':'\\ud83d\\udd33',\n  'wilted_flower':'\\ud83e\\udd40',\n  'wind_chime':'\\ud83c\\udf90',\n  'wind_face':'\\ud83c\\udf2c',\n  'wine_glass':'\\ud83c\\udf77',\n  'wink':'\\ud83d\\ude09',\n  'wolf':'\\ud83d\\udc3a',\n  'woman':'\\ud83d\\udc69',\n  'woman_artist':'\\ud83d\\udc69&zwj;\\ud83c\\udfa8',\n  'woman_astronaut':'\\ud83d\\udc69&zwj;\\ud83d\\ude80',\n  'woman_cartwheeling':'\\ud83e\\udd38&zwj;\\u2640\\ufe0f',\n  'woman_cook':'\\ud83d\\udc69&zwj;\\ud83c\\udf73',\n  'woman_facepalming':'\\ud83e\\udd26&zwj;\\u2640\\ufe0f',\n  'woman_factory_worker':'\\ud83d\\udc69&zwj;\\ud83c\\udfed',\n  'woman_farmer':'\\ud83d\\udc69&zwj;\\ud83c\\udf3e',\n  'woman_firefighter':'\\ud83d\\udc69&zwj;\\ud83d\\ude92',\n  'woman_health_worker':'\\ud83d\\udc69&zwj;\\u2695\\ufe0f',\n  'woman_judge':'\\ud83d\\udc69&zwj;\\u2696\\ufe0f',\n  'woman_juggling':'\\ud83e\\udd39&zwj;\\u2640\\ufe0f',\n  'woman_mechanic':'\\ud83d\\udc69&zwj;\\ud83d\\udd27',\n  'woman_office_worker':'\\ud83d\\udc69&zwj;\\ud83d\\udcbc',\n  'woman_pilot':'\\ud83d\\udc69&zwj;\\u2708\\ufe0f',\n  'woman_playing_handball':'\\ud83e\\udd3e&zwj;\\u2640\\ufe0f',\n  'woman_playing_water_polo':'\\ud83e\\udd3d&zwj;\\u2640\\ufe0f',\n  'woman_scientist':'\\ud83d\\udc69&zwj;\\ud83d\\udd2c',\n  'woman_shrugging':'\\ud83e\\udd37&zwj;\\u2640\\ufe0f',\n  'woman_singer':'\\ud83d\\udc69&zwj;\\ud83c\\udfa4',\n  'woman_student':'\\ud83d\\udc69&zwj;\\ud83c\\udf93',\n  'woman_teacher':'\\ud83d\\udc69&zwj;\\ud83c\\udfeb',\n  'woman_technologist':'\\ud83d\\udc69&zwj;\\ud83d\\udcbb',\n  'woman_with_turban':'\\ud83d\\udc73&zwj;\\u2640\\ufe0f',\n  'womans_clothes':'\\ud83d\\udc5a',\n  'womans_hat':'\\ud83d\\udc52',\n  'women_wrestling':'\\ud83e\\udd3c&zwj;\\u2640\\ufe0f',\n  'womens':'\\ud83d\\udeba',\n  'world_map':'\\ud83d\\uddfa',\n  'worried':'\\ud83d\\ude1f',\n  'wrench':'\\ud83d\\udd27',\n  'writing_hand':'\\u270d\\ufe0f',\n  'x':'\\u274c',\n  'yellow_heart':'\\ud83d\\udc9b',\n  'yen':'\\ud83d\\udcb4',\n  'yin_yang':'\\u262f\\ufe0f',\n  'yum':'\\ud83d\\ude0b',\n  'zap':'\\u26a1\\ufe0f',\n  'zipper_mouth_face':'\\ud83e\\udd10',\n  'zzz':'\\ud83d\\udca4',\n\n  /* special emojis :P */\n  'octocat':  '<img alt=\":octocat:\" height=\"20\" width=\"20\" align=\"absmiddle\" src=\"https://assets-cdn.github.com/images/icons/emoji/octocat.png\">',\n  'showdown': '<span style=\"font-family: \\'Anonymous Pro\\', monospace; text-decoration: underline; text-decoration-style: dashed; text-decoration-color: #3e8b8a;text-underline-position: under;\">S</span>'\n};\n", "/**\n * Created by Estevao on 31-05-2015.\n */\n\n/**\n * Showdown Converter class\n * @class\n * @param {object} [converterOptions]\n * @returns {Converter}\n */\nshowdown.Converter = function (converterOptions) {\n  'use strict';\n\n  var\n      /**\n       * Options used by this converter\n       * @private\n       * @type {{}}\n       */\n      options = {},\n\n      /**\n       * Language extensions used by this converter\n       * @private\n       * @type {Array}\n       */\n      langExtensions = [],\n\n      /**\n       * Output modifiers extensions used by this converter\n       * @private\n       * @type {Array}\n       */\n      outputModifiers = [],\n\n      /**\n       * Event listeners\n       * @private\n       * @type {{}}\n       */\n      listeners = {},\n\n      /**\n       * The flavor set in this converter\n       */\n      setConvFlavor = setFlavor,\n\n      /**\n       * Metadata of the document\n       * @type {{parsed: {}, raw: string, format: string}}\n       */\n      metadata = {\n        parsed: {},\n        raw: '',\n        format: ''\n      };\n\n  _constructor();\n\n  /**\n   * Converter constructor\n   * @private\n   */\n  function _constructor () {\n    converterOptions = converterOptions || {};\n\n    for (var gOpt in globalOptions) {\n      if (globalOptions.hasOwnProperty(gOpt)) {\n        options[gOpt] = globalOptions[gOpt];\n      }\n    }\n\n    // Merge options\n    if (typeof converterOptions === 'object') {\n      for (var opt in converterOptions) {\n        if (converterOptions.hasOwnProperty(opt)) {\n          options[opt] = converterOptions[opt];\n        }\n      }\n    } else {\n      throw Error('Converter expects the passed parameter to be an object, but ' + typeof converterOptions +\n      ' was passed instead.');\n    }\n\n    if (options.extensions) {\n      showdown.helper.forEach(options.extensions, _parseExtension);\n    }\n  }\n\n  /**\n   * Parse extension\n   * @param {*} ext\n   * @param {string} [name='']\n   * @private\n   */\n  function _parseExtension (ext, name) {\n\n    name = name || null;\n    // If it's a string, the extension was previously loaded\n    if (showdown.helper.isString(ext)) {\n      ext = showdown.helper.stdExtName(ext);\n      name = ext;\n\n      // LEGACY_SUPPORT CODE\n      if (showdown.extensions[ext]) {\n        console.warn('DEPRECATION WARNING: ' + ext + ' is an old extension that uses a deprecated loading method.' +\n          'Please inform the developer that the extension should be updated!');\n        legacyExtensionLoading(showdown.extensions[ext], ext);\n        return;\n        // END LEGACY SUPPORT CODE\n\n      } else if (!showdown.helper.isUndefined(extensions[ext])) {\n        ext = extensions[ext];\n\n      } else {\n        throw Error('Extension \"' + ext + '\" could not be loaded. It was either not found or is not a valid extension.');\n      }\n    }\n\n    if (typeof ext === 'function') {\n      ext = ext();\n    }\n\n    if (!showdown.helper.isArray(ext)) {\n      ext = [ext];\n    }\n\n    var validExt = validate(ext, name);\n    if (!validExt.valid) {\n      throw Error(validExt.error);\n    }\n\n    for (var i = 0; i < ext.length; ++i) {\n      switch (ext[i].type) {\n\n        case 'lang':\n          langExtensions.push(ext[i]);\n          break;\n\n        case 'output':\n          outputModifiers.push(ext[i]);\n          break;\n      }\n      if (ext[i].hasOwnProperty('listeners')) {\n        for (var ln in ext[i].listeners) {\n          if (ext[i].listeners.hasOwnProperty(ln)) {\n            listen(ln, ext[i].listeners[ln]);\n          }\n        }\n      }\n    }\n\n  }\n\n  /**\n   * LEGACY_SUPPORT\n   * @param {*} ext\n   * @param {string} name\n   */\n  function legacyExtensionLoading (ext, name) {\n    if (typeof ext === 'function') {\n      ext = ext(new showdown.Converter());\n    }\n    if (!showdown.helper.isArray(ext)) {\n      ext = [ext];\n    }\n    var valid = validate(ext, name);\n\n    if (!valid.valid) {\n      throw Error(valid.error);\n    }\n\n    for (var i = 0; i < ext.length; ++i) {\n      switch (ext[i].type) {\n        case 'lang':\n          langExtensions.push(ext[i]);\n          break;\n        case 'output':\n          outputModifiers.push(ext[i]);\n          break;\n        default:// should never reach here\n          throw Error('Extension loader error: Type unrecognized!!!');\n      }\n    }\n  }\n\n  /**\n   * Listen to an event\n   * @param {string} name\n   * @param {function} callback\n   */\n  function listen (name, callback) {\n    if (!showdown.helper.isString(name)) {\n      throw Error('Invalid argument in converter.listen() method: name must be a string, but ' + typeof name + ' given');\n    }\n\n    if (typeof callback !== 'function') {\n      throw Error('Invalid argument in converter.listen() method: callback must be a function, but ' + typeof callback + ' given');\n    }\n\n    if (!listeners.hasOwnProperty(name)) {\n      listeners[name] = [];\n    }\n    listeners[name].push(callback);\n  }\n\n  function rTrimInputText (text) {\n    var rsp = text.match(/^\\s*/)[0].length,\n        rgx = new RegExp('^\\\\s{0,' + rsp + '}', 'gm');\n    return text.replace(rgx, '');\n  }\n\n  /**\n   * Dispatch an event\n   * @private\n   * @param {string} evtName Event name\n   * @param {string} text Text\n   * @param {{}} options Converter Options\n   * @param {{}} globals\n   * @returns {string}\n   */\n  this._dispatch = function dispatch (evtName, text, options, globals) {\n    if (listeners.hasOwnProperty(evtName)) {\n      for (var ei = 0; ei < listeners[evtName].length; ++ei) {\n        var nText = listeners[evtName][ei](evtName, text, this, options, globals);\n        if (nText && typeof nText !== 'undefined') {\n          text = nText;\n        }\n      }\n    }\n    return text;\n  };\n\n  /**\n   * Listen to an event\n   * @param {string} name\n   * @param {function} callback\n   * @returns {showdown.Converter}\n   */\n  this.listen = function (name, callback) {\n    listen(name, callback);\n    return this;\n  };\n\n  /**\n   * Converts a markdown string into HTML\n   * @param {string} text\n   * @returns {*}\n   */\n  this.makeHtml = function (text) {\n    //check if text is not falsy\n    if (!text) {\n      return text;\n    }\n\n    var globals = {\n      gHtmlBlocks:     [],\n      gHtmlMdBlocks:   [],\n      gHtmlSpans:      [],\n      gUrls:           {},\n      gTitles:         {},\n      gDimensions:     {},\n      gListLevel:      0,\n      hashLinkCounts:  {},\n      langExtensions:  langExtensions,\n      outputModifiers: outputModifiers,\n      converter:       this,\n      ghCodeBlocks:    [],\n      metadata: {\n        parsed: {},\n        raw: '',\n        format: ''\n      }\n    };\n\n    // This lets us use ¨ trema as an escape char to avoid md5 hashes\n    // The choice of character is arbitrary; anything that isn't\n    // magic in Markdown will work.\n    text = text.replace(/¨/g, '¨T');\n\n    // Replace $ with ¨D\n    // RegExp interprets $ as a special character\n    // when it's in a replacement string\n    text = text.replace(/\\$/g, '¨D');\n\n    // Standardize line endings\n    text = text.replace(/\\r\\n/g, '\\n'); // DOS to Unix\n    text = text.replace(/\\r/g, '\\n'); // Mac to Unix\n\n    // Stardardize line spaces\n    text = text.replace(/\\u00A0/g, '&nbsp;');\n\n    if (options.smartIndentationFix) {\n      text = rTrimInputText(text);\n    }\n\n    // Make sure text begins and ends with a couple of newlines:\n    text = '\\n\\n' + text + '\\n\\n';\n\n    // detab\n    text = showdown.subParser('detab')(text, options, globals);\n\n    /**\n     * Strip any lines consisting only of spaces and tabs.\n     * This makes subsequent regexs easier to write, because we can\n     * match consecutive blank lines with /\\n+/ instead of something\n     * contorted like /[ \\t]*\\n+/\n     */\n    text = text.replace(/^[ \\t]+$/mg, '');\n\n    //run languageExtensions\n    showdown.helper.forEach(langExtensions, function (ext) {\n      text = showdown.subParser('runExtension')(ext, text, options, globals);\n    });\n\n    // run the sub parsers\n    text = showdown.subParser('metadata')(text, options, globals);\n    text = showdown.subParser('hashPreCodeTags')(text, options, globals);\n    text = showdown.subParser('githubCodeBlocks')(text, options, globals);\n    text = showdown.subParser('hashHTMLBlocks')(text, options, globals);\n    text = showdown.subParser('hashCodeTags')(text, options, globals);\n    text = showdown.subParser('stripLinkDefinitions')(text, options, globals);\n    text = showdown.subParser('blockGamut')(text, options, globals);\n    text = showdown.subParser('unhashHTMLSpans')(text, options, globals);\n    text = showdown.subParser('unescapeSpecialChars')(text, options, globals);\n\n    // attacklab: Restore dollar signs\n    text = text.replace(/¨D/g, '$$');\n\n    // attacklab: Restore tremas\n    text = text.replace(/¨T/g, '¨');\n\n    // render a complete html document instead of a partial if the option is enabled\n    text = showdown.subParser('completeHTMLDocument')(text, options, globals);\n\n    // Run output modifiers\n    showdown.helper.forEach(outputModifiers, function (ext) {\n      text = showdown.subParser('runExtension')(ext, text, options, globals);\n    });\n\n    // update metadata\n    metadata = globals.metadata;\n    return text;\n  };\n\n  /**\n   * Converts an HTML string into a markdown string\n   * @param src\n   * @param [HTMLParser] A WHATWG DOM and HTML parser, such as JSDOM. If none is supplied, window.document will be used.\n   * @returns {string}\n   */\n  this.makeMarkdown = this.makeMd = function (src, HTMLParser) {\n\n    // replace \\r\\n with \\n\n    src = src.replace(/\\r\\n/g, '\\n');\n    src = src.replace(/\\r/g, '\\n'); // old macs\n\n    // due to an edge case, we need to find this: > <\n    // to prevent removing of non silent white spaces\n    // ex: <em>this is</em> <strong>sparta</strong>\n    src = src.replace(/>[ \\t]+</, '>¨NBSP;<');\n\n    if (!HTMLParser) {\n      if (window && window.document) {\n        HTMLParser = window.document;\n      } else {\n        throw new Error('HTMLParser is undefined. If in a webworker or nodejs environment, you need to provide a WHATWG DOM and HTML such as JSDOM');\n      }\n    }\n\n    var doc = HTMLParser.createElement('div');\n    doc.innerHTML = src;\n\n    var globals = {\n      preList: substitutePreCodeTags(doc)\n    };\n\n    // remove all newlines and collapse spaces\n    clean(doc);\n\n    // some stuff, like accidental reference links must now be escaped\n    // TODO\n    // doc.innerHTML = doc.innerHTML.replace(/\\[[\\S\\t ]]/);\n\n    var nodes = doc.childNodes,\n        mdDoc = '';\n\n    for (var i = 0; i < nodes.length; i++) {\n      mdDoc += showdown.subParser('makeMarkdown.node')(nodes[i], globals);\n    }\n\n    function clean (node) {\n      for (var n = 0; n < node.childNodes.length; ++n) {\n        var child = node.childNodes[n];\n        if (child.nodeType === 3) {\n          if (!/\\S/.test(child.nodeValue) && !/^[ ]+$/.test(child.nodeValue)) {\n            node.removeChild(child);\n            --n;\n          } else {\n            child.nodeValue = child.nodeValue.split('\\n').join(' ');\n            child.nodeValue = child.nodeValue.replace(/(\\s)+/g, '$1');\n          }\n        } else if (child.nodeType === 1) {\n          clean(child);\n        }\n      }\n    }\n\n    // find all pre tags and replace contents with placeholder\n    // we need this so that we can remove all indentation from html\n    // to ease up parsing\n    function substitutePreCodeTags (doc) {\n\n      var pres = doc.querySelectorAll('pre'),\n          presPH = [];\n\n      for (var i = 0; i < pres.length; ++i) {\n\n        if (pres[i].childElementCount === 1 && pres[i].firstChild.tagName.toLowerCase() === 'code') {\n          var content = pres[i].firstChild.innerHTML.trim(),\n              language = pres[i].firstChild.getAttribute('data-language') || '';\n\n          // if data-language attribute is not defined, then we look for class language-*\n          if (language === '') {\n            var classes = pres[i].firstChild.className.split(' ');\n            for (var c = 0; c < classes.length; ++c) {\n              var matches = classes[c].match(/^language-(.+)$/);\n              if (matches !== null) {\n                language = matches[1];\n                break;\n              }\n            }\n          }\n\n          // unescape html entities in content\n          content = showdown.helper.unescapeHTMLEntities(content);\n\n          presPH.push(content);\n          pres[i].outerHTML = '<precode language=\"' + language + '\" precodenum=\"' + i.toString() + '\"></precode>';\n        } else {\n          presPH.push(pres[i].innerHTML);\n          pres[i].innerHTML = '';\n          pres[i].setAttribute('prenum', i.toString());\n        }\n      }\n      return presPH;\n    }\n\n    return mdDoc;\n  };\n\n  /**\n   * Set an option of this Converter instance\n   * @param {string} key\n   * @param {*} value\n   */\n  this.setOption = function (key, value) {\n    options[key] = value;\n  };\n\n  /**\n   * Get the option of this Converter instance\n   * @param {string} key\n   * @returns {*}\n   */\n  this.getOption = function (key) {\n    return options[key];\n  };\n\n  /**\n   * Get the options of this Converter instance\n   * @returns {{}}\n   */\n  this.getOptions = function () {\n    return options;\n  };\n\n  /**\n   * Add extension to THIS converter\n   * @param {{}} extension\n   * @param {string} [name=null]\n   */\n  this.addExtension = function (extension, name) {\n    name = name || null;\n    _parseExtension(extension, name);\n  };\n\n  /**\n   * Use a global registered extension with THIS converter\n   * @param {string} extensionName Name of the previously registered extension\n   */\n  this.useExtension = function (extensionName) {\n    _parseExtension(extensionName);\n  };\n\n  /**\n   * Set the flavor THIS converter should use\n   * @param {string} name\n   */\n  this.setFlavor = function (name) {\n    if (!flavor.hasOwnProperty(name)) {\n      throw Error(name + ' flavor was not found');\n    }\n    var preset = flavor[name];\n    setConvFlavor = name;\n    for (var option in preset) {\n      if (preset.hasOwnProperty(option)) {\n        options[option] = preset[option];\n      }\n    }\n  };\n\n  /**\n   * Get the currently set flavor of this converter\n   * @returns {string}\n   */\n  this.getFlavor = function () {\n    return setConvFlavor;\n  };\n\n  /**\n   * Remove an extension from THIS converter.\n   * Note: This is a costly operation. It's better to initialize a new converter\n   * and specify the extensions you wish to use\n   * @param {Array} extension\n   */\n  this.removeExtension = function (extension) {\n    if (!showdown.helper.isArray(extension)) {\n      extension = [extension];\n    }\n    for (var a = 0; a < extension.length; ++a) {\n      var ext = extension[a];\n      for (var i = 0; i < langExtensions.length; ++i) {\n        if (langExtensions[i] === ext) {\n          langExtensions.splice(i, 1);\n        }\n      }\n      for (var ii = 0; ii < outputModifiers.length; ++ii) {\n        if (outputModifiers[ii] === ext) {\n          outputModifiers.splice(ii, 1);\n        }\n      }\n    }\n  };\n\n  /**\n   * Get all extension of THIS converter\n   * @returns {{language: Array, output: Array}}\n   */\n  this.getAllExtensions = function () {\n    return {\n      language: langExtensions,\n      output: outputModifiers\n    };\n  };\n\n  /**\n   * Get the metadata of the previously parsed document\n   * @param raw\n   * @returns {string|{}}\n   */\n  this.getMetadata = function (raw) {\n    if (raw) {\n      return metadata.raw;\n    } else {\n      return metadata.parsed;\n    }\n  };\n\n  /**\n   * Get the metadata format of the previously parsed document\n   * @returns {string}\n   */\n  this.getMetadataFormat = function () {\n    return metadata.format;\n  };\n\n  /**\n   * Private: set a single key, value metadata pair\n   * @param {string} key\n   * @param {string} value\n   */\n  this._setMetadataPair = function (key, value) {\n    metadata.parsed[key] = value;\n  };\n\n  /**\n   * Private: set metadata format\n   * @param {string} format\n   */\n  this._setMetadataFormat = function (format) {\n    metadata.format = format;\n  };\n\n  /**\n   * Private: set metadata raw text\n   * @param {string} raw\n   */\n  this._setMetadataRaw = function (raw) {\n    metadata.raw = raw;\n  };\n};\n", "/**\n * Turn Markdown link shortcuts into XHTML <a> tags.\n */\nshowdown.subParser('anchors', function (text, options, globals) {\n  'use strict';\n\n  text = globals.converter._dispatch('anchors.before', text, options, globals);\n\n  var writeAnchorTag = function (wholeMatch, linkText, linkId, url, m5, m6, title) {\n    if (showdown.helper.isUndefined(title)) {\n      title = '';\n    }\n    linkId = linkId.toLowerCase();\n\n    // Special case for explicit empty url\n    if (wholeMatch.search(/\\(<?\\s*>? ?(['\"].*['\"])?\\)$/m) > -1) {\n      url = '';\n    } else if (!url) {\n      if (!linkId) {\n        // lower-case and turn embedded newlines into spaces\n        linkId = linkText.toLowerCase().replace(/ ?\\n/g, ' ');\n      }\n      url = '#' + linkId;\n\n      if (!showdown.helper.isUndefined(globals.gUrls[linkId])) {\n        url = globals.gUrls[linkId];\n        if (!showdown.helper.isUndefined(globals.gTitles[linkId])) {\n          title = globals.gTitles[linkId];\n        }\n      } else {\n        return wholeMatch;\n      }\n    }\n\n    //url = showdown.helper.escapeCharacters(url, '*_', false); // replaced line to improve performance\n    url = url.replace(showdown.helper.regexes.asteriskDashAndColon, showdown.helper.escapeCharactersCallback);\n\n    var result = '<a href=\"' + url + '\"';\n\n    if (title !== '' && title !== null) {\n      title = title.replace(/\"/g, '&quot;');\n      //title = showdown.helper.escapeCharacters(title, '*_', false); // replaced line to improve performance\n      title = title.replace(showdown.helper.regexes.asteriskDashAndColon, showdown.helper.escapeCharactersCallback);\n      result += ' title=\"' + title + '\"';\n    }\n\n    // optionLinksInNewWindow only applies\n    // to external links. Hash links (#) open in same page\n    if (options.openLinksInNewWindow && !/^#/.test(url)) {\n      // escaped _\n      result += ' rel=\"noopener noreferrer\" target=\"¨E95Eblank\"';\n    }\n\n    result += '>' + linkText + '</a>';\n\n    return result;\n  };\n\n  // First, handle reference-style links: [link text] [id]\n  text = text.replace(/\\[((?:\\[[^\\]]*]|[^\\[\\]])*)] ?(?:\\n *)?\\[(.*?)]()()()()/g, writeAnchorTag);\n\n  // Next, inline-style links: [link text](url \"optional title\")\n  // cases with crazy urls like ./image/cat1).png\n  text = text.replace(/\\[((?:\\[[^\\]]*]|[^\\[\\]])*)]()[ \\t]*\\([ \\t]?<([^>]*)>(?:[ \\t]*(([\"'])([^\"]*?)\\5))?[ \\t]?\\)/g,\n    writeAnchorTag);\n\n  // normal cases\n  text = text.replace(/\\[((?:\\[[^\\]]*]|[^\\[\\]])*)]()[ \\t]*\\([ \\t]?<?([\\S]+?(?:\\([\\S]*?\\)[\\S]*?)?)>?(?:[ \\t]*(([\"'])([^\"]*?)\\5))?[ \\t]?\\)/g,\n    writeAnchorTag);\n\n  // handle reference-style shortcuts: [link text]\n  // These must come last in case you've also got [link test][1]\n  // or [link test](/foo)\n  text = text.replace(/\\[([^\\[\\]]+)]()()()()()/g, writeAnchorTag);\n\n  // Lastly handle GithubMentions if option is enabled\n  if (options.ghMentions) {\n    text = text.replace(/(^|\\s)(\\\\)?(@([a-z\\d]+(?:[a-z\\d.-]+?[a-z\\d]+)*))/gmi, function (wm, st, escape, mentions, username) {\n      if (escape === '\\\\') {\n        return st + mentions;\n      }\n\n      //check if options.ghMentionsLink is a string\n      if (!showdown.helper.isString(options.ghMentionsLink)) {\n        throw new Error('ghMentionsLink option must be a string');\n      }\n      var lnk = options.ghMentionsLink.replace(/\\{u}/g, username),\n          target = '';\n      if (options.openLinksInNewWindow) {\n        target = ' rel=\"noopener noreferrer\" target=\"¨E95Eblank\"';\n      }\n      return st + '<a href=\"' + lnk + '\"' + target + '>' + mentions + '</a>';\n    });\n  }\n\n  text = globals.converter._dispatch('anchors.after', text, options, globals);\n  return text;\n});\n", "// url allowed chars [a-z\\d_.~:/?#[]@!$&'()*+,;=-]\n\nvar simpleURLRegex  = /([*~_]+|\\b)(((https?|ftp|dict):\\/\\/|www\\.)[^'\">\\s]+?\\.[^'\">\\s]+?)()(\\1)?(?=\\s|$)(?![\"<>])/gi,\n    simpleURLRegex2 = /([*~_]+|\\b)(((https?|ftp|dict):\\/\\/|www\\.)[^'\">\\s]+\\.[^'\">\\s]+?)([.!?,()\\[\\]])?(\\1)?(?=\\s|$)(?![\"<>])/gi,\n    delimUrlRegex   = /()<(((https?|ftp|dict):\\/\\/|www\\.)[^'\">\\s]+)()>()/gi,\n    simpleMailRegex = /(^|\\s)(?:mailto:)?([A-Za-z0-9!#$%&'*+-/=?^_`{|}~.]+@[-a-z0-9]+(\\.[-a-z0-9]+)*\\.[a-z]+)(?=$|\\s)/gmi,\n    delimMailRegex  = /<()(?:mailto:)?([-.\\w]+@[-a-z0-9]+(\\.[-a-z0-9]+)*\\.[a-z]+)>/gi,\n\n    replaceLink = function (options) {\n      'use strict';\n      return function (wm, leadingMagicChars, link, m2, m3, trailingPunctuation, trailingMagicChars) {\n        link = link.replace(showdown.helper.regexes.asteriskDashAndColon, showdown.helper.escapeCharactersCallback);\n        var lnkTxt = link,\n            append = '',\n            target = '',\n            lmc    = leadingMagicChars || '',\n            tmc    = trailingMagicChars || '';\n        if (/^www\\./i.test(link)) {\n          link = link.replace(/^www\\./i, 'http://www.');\n        }\n        if (options.excludeTrailingPunctuationFromURLs && trailingPunctuation) {\n          append = trailingPunctuation;\n        }\n        if (options.openLinksInNewWindow) {\n          target = ' rel=\"noopener noreferrer\" target=\"¨E95Eblank\"';\n        }\n        return lmc + '<a href=\"' + link + '\"' + target + '>' + lnkTxt + '</a>' + append + tmc;\n      };\n    },\n\n    replaceMail = function (options, globals) {\n      'use strict';\n      return function (wholeMatch, b, mail) {\n        var href = 'mailto:';\n        b = b || '';\n        mail = showdown.subParser('unescapeSpecialChars')(mail, options, globals);\n        if (options.encodeEmails) {\n          href = showdown.helper.encodeEmailAddress(href + mail);\n          mail = showdown.helper.encodeEmailAddress(mail);\n        } else {\n          href = href + mail;\n        }\n        return b + '<a href=\"' + href + '\">' + mail + '</a>';\n      };\n    };\n\nshowdown.subParser('autoLinks', function (text, options, globals) {\n  'use strict';\n\n  text = globals.converter._dispatch('autoLinks.before', text, options, globals);\n\n  text = text.replace(delimUrlRegex, replaceLink(options));\n  text = text.replace(delimMailRegex, replaceMail(options, globals));\n\n  text = globals.converter._dispatch('autoLinks.after', text, options, globals);\n\n  return text;\n});\n\nshowdown.subParser('simplifiedAutoLinks', function (text, options, globals) {\n  'use strict';\n\n  if (!options.simplifiedAutoLink) {\n    return text;\n  }\n\n  text = globals.converter._dispatch('simplifiedAutoLinks.before', text, options, globals);\n\n  if (options.excludeTrailingPunctuationFromURLs) {\n    text = text.replace(simpleURLRegex2, replaceLink(options));\n  } else {\n    text = text.replace(simpleURLRegex, replaceLink(options));\n  }\n  text = text.replace(simpleMailRegex, replaceMail(options, globals));\n\n  text = globals.converter._dispatch('simplifiedAutoLinks.after', text, options, globals);\n\n  return text;\n});\n", "/**\n * These are all the transformations that form block-level\n * tags like paragraphs, headers, and list items.\n */\nshowdown.subParser('blockGamut', function (text, options, globals) {\n  'use strict';\n\n  text = globals.converter._dispatch('blockGamut.before', text, options, globals);\n\n  // we parse blockquotes first so that we can have headings and hrs\n  // inside blockquotes\n  text = showdown.subParser('blockQuotes')(text, options, globals);\n  text = showdown.subParser('headers')(text, options, globals);\n\n  // Do Horizontal Rules:\n  text = showdown.subParser('horizontalRule')(text, options, globals);\n\n  text = showdown.subParser('lists')(text, options, globals);\n  text = showdown.subParser('codeBlocks')(text, options, globals);\n  text = showdown.subParser('tables')(text, options, globals);\n\n  // We already ran _HashHTMLBlocks() before, in Markdown(), but that\n  // was to escape raw HTML in the original Markdown source. This time,\n  // we're escaping the markup we've just created, so that we don't wrap\n  // <p> tags around block-level tags.\n  text = showdown.subParser('hashHTMLBlocks')(text, options, globals);\n  text = showdown.subParser('paragraphs')(text, options, globals);\n\n  text = globals.converter._dispatch('blockGamut.after', text, options, globals);\n\n  return text;\n});\n", "showdown.subParser('blockQuotes', function (text, options, globals) {\n  'use strict';\n\n  text = globals.converter._dispatch('blockQuotes.before', text, options, globals);\n\n  // add a couple extra lines after the text and endtext mark\n  text = text + '\\n\\n';\n\n  var rgx = /(^ {0,3}>[ \\t]?.+\\n(.+\\n)*\\n*)+/gm;\n\n  if (options.splitAdjacentBlockquotes) {\n    rgx = /^ {0,3}>[\\s\\S]*?(?:\\n\\n)/gm;\n  }\n\n  text = text.replace(rgx, function (bq) {\n    // attacklab: hack around Konqueror 3.5.4 bug:\n    // \"----------bug\".replace(/^-/g,\"\") == \"bug\"\n    bq = bq.replace(/^[ \\t]*>[ \\t]?/gm, ''); // trim one level of quoting\n\n    // attacklab: clean up hack\n    bq = bq.replace(/¨0/g, '');\n\n    bq = bq.replace(/^[ \\t]+$/gm, ''); // trim whitespace-only lines\n    bq = showdown.subParser('githubCodeBlocks')(bq, options, globals);\n    bq = showdown.subParser('blockGamut')(bq, options, globals); // recurse\n\n    bq = bq.replace(/(^|\\n)/g, '$1  ');\n    // These leading spaces screw with <pre> content, so we need to fix that:\n    bq = bq.replace(/(\\s*<pre>[^\\r]+?<\\/pre>)/gm, function (wholeMatch, m1) {\n      var pre = m1;\n      // attacklab: hack around Konqueror 3.5.4 bug:\n      pre = pre.replace(/^  /mg, '¨0');\n      pre = pre.replace(/¨0/g, '');\n      return pre;\n    });\n\n    return showdown.subParser('hashBlock')('<blockquote>\\n' + bq + '\\n</blockquote>', options, globals);\n  });\n\n  text = globals.converter._dispatch('blockQuotes.after', text, options, globals);\n  return text;\n});\n", "/**\n * Process Markdown `<pre><code>` blocks.\n */\nshowdown.subParser('codeBlocks', function (text, options, globals) {\n  'use strict';\n\n  text = globals.converter._dispatch('codeBlocks.before', text, options, globals);\n\n  // sentinel workarounds for lack of \\A and \\Z, safari\\khtml bug\n  text += '¨0';\n\n  var pattern = /(?:\\n\\n|^)((?:(?:[ ]{4}|\\t).*\\n+)+)(\\n*[ ]{0,3}[^ \\t\\n]|(?=¨0))/g;\n  text = text.replace(pattern, function (wholeMatch, m1, m2) {\n    var codeblock = m1,\n        nextChar = m2,\n        end = '\\n';\n\n    codeblock = showdown.subParser('outdent')(codeblock, options, globals);\n    codeblock = showdown.subParser('encodeCode')(codeblock, options, globals);\n    codeblock = showdown.subParser('detab')(codeblock, options, globals);\n    codeblock = codeblock.replace(/^\\n+/g, ''); // trim leading newlines\n    codeblock = codeblock.replace(/\\n+$/g, ''); // trim trailing newlines\n\n    if (options.omitExtraWLInCodeBlocks) {\n      end = '';\n    }\n\n    codeblock = '<pre><code>' + codeblock + end + '</code></pre>';\n\n    return showdown.subParser('hashBlock')(codeblock, options, globals) + nextChar;\n  });\n\n  // strip sentinel\n  text = text.replace(/¨0/, '');\n\n  text = globals.converter._dispatch('codeBlocks.after', text, options, globals);\n  return text;\n});\n", "/**\n *\n *   *  Backtick quotes are used for <code></code> spans.\n *\n *   *  You can use multiple backticks as the delimiters if you want to\n *     include literal backticks in the code span. So, this input:\n *\n *         Just type ``foo `bar` baz`` at the prompt.\n *\n *       Will translate to:\n *\n *         <p>Just type <code>foo `bar` baz</code> at the prompt.</p>\n *\n *    There's no arbitrary limit to the number of backticks you\n *    can use as delimters. If you need three consecutive backticks\n *    in your code, use four for delimiters, etc.\n *\n *  *  You can use spaces to get literal backticks at the edges:\n *\n *         ... type `` `bar` `` ...\n *\n *       Turns to:\n *\n *         ... type <code>`bar`</code> ...\n */\nshowdown.subParser('codeSpans', function (text, options, globals) {\n  'use strict';\n\n  text = globals.converter._dispatch('codeSpans.before', text, options, globals);\n\n  if (typeof (text) === 'undefined') {\n    text = '';\n  }\n  text = text.replace(/(^|[^\\\\])(`+)([^\\r]*?[^`])\\2(?!`)/gm,\n    function (wholeMatch, m1, m2, m3) {\n      var c = m3;\n      c = c.replace(/^([ \\t]*)/g, '');\t// leading whitespace\n      c = c.replace(/[ \\t]*$/g, '');\t// trailing whitespace\n      c = showdown.subParser('encodeCode')(c, options, globals);\n      c = m1 + '<code>' + c + '</code>';\n      c = showdown.subParser('hashHTMLSpans')(c, options, globals);\n      return c;\n    }\n  );\n\n  text = globals.converter._dispatch('codeSpans.after', text, options, globals);\n  return text;\n});\n", "/**\n * Create a full HTML document from the processed markdown\n */\nshowdown.subParser('completeHTMLDocument', function (text, options, globals) {\n  'use strict';\n\n  if (!options.completeHTMLDocument) {\n    return text;\n  }\n\n  text = globals.converter._dispatch('completeHTMLDocument.before', text, options, globals);\n\n  var doctype = 'html',\n      doctypeParsed = '<!DOCTYPE HTML>\\n',\n      title = '',\n      charset = '<meta charset=\"utf-8\">\\n',\n      lang = '',\n      metadata = '';\n\n  if (typeof globals.metadata.parsed.doctype !== 'undefined') {\n    doctypeParsed = '<!DOCTYPE ' +  globals.metadata.parsed.doctype + '>\\n';\n    doctype = globals.metadata.parsed.doctype.toString().toLowerCase();\n    if (doctype === 'html' || doctype === 'html5') {\n      charset = '<meta charset=\"utf-8\">';\n    }\n  }\n\n  for (var meta in globals.metadata.parsed) {\n    if (globals.metadata.parsed.hasOwnProperty(meta)) {\n      switch (meta.toLowerCase()) {\n        case 'doctype':\n          break;\n\n        case 'title':\n          title = '<title>' +  globals.metadata.parsed.title + '</title>\\n';\n          break;\n\n        case 'charset':\n          if (doctype === 'html' || doctype === 'html5') {\n            charset = '<meta charset=\"' + globals.metadata.parsed.charset + '\">\\n';\n          } else {\n            charset = '<meta name=\"charset\" content=\"' + globals.metadata.parsed.charset + '\">\\n';\n          }\n          break;\n\n        case 'language':\n        case 'lang':\n          lang = ' lang=\"' + globals.metadata.parsed[meta] + '\"';\n          metadata += '<meta name=\"' + meta + '\" content=\"' + globals.metadata.parsed[meta] + '\">\\n';\n          break;\n\n        default:\n          metadata += '<meta name=\"' + meta + '\" content=\"' + globals.metadata.parsed[meta] + '\">\\n';\n      }\n    }\n  }\n\n  text = doctypeParsed + '<html' + lang + '>\\n<head>\\n' + title + charset + metadata + '</head>\\n<body>\\n' + text.trim() + '\\n</body>\\n</html>';\n\n  text = globals.converter._dispatch('completeHTMLDocument.after', text, options, globals);\n  return text;\n});\n", "/**\n * Convert all tabs to spaces\n */\nshowdown.subParser('detab', function (text, options, globals) {\n  'use strict';\n  text = globals.converter._dispatch('detab.before', text, options, globals);\n\n  // expand first n-1 tabs\n  text = text.replace(/\\t(?=\\t)/g, '    '); // g_tab_width\n\n  // replace the nth with two sentinels\n  text = text.replace(/\\t/g, '¨A¨B');\n\n  // use the sentinel to anchor our regex so it doesn't explode\n  text = text.replace(/¨B(.+?)¨A/g, function (wholeMatch, m1) {\n    var leadingText = m1,\n        numSpaces = 4 - leadingText.length % 4;  // g_tab_width\n\n    // there *must* be a better way to do this:\n    for (var i = 0; i < numSpaces; i++) {\n      leadingText += ' ';\n    }\n\n    return leadingText;\n  });\n\n  // clean up sentinels\n  text = text.replace(/¨A/g, '    ');  // g_tab_width\n  text = text.replace(/¨B/g, '');\n\n  text = globals.converter._dispatch('detab.after', text, options, globals);\n  return text;\n});\n", "showdown.subParser('ellipsis', function (text, options, globals) {\n  'use strict';\n\n  if (!options.ellipsis) {\n    return text;\n  }\n\n  text = globals.converter._dispatch('ellipsis.before', text, options, globals);\n\n  text = text.replace(/\\.\\.\\./g, '…');\n\n  text = globals.converter._dispatch('ellipsis.after', text, options, globals);\n\n  return text;\n});\n", "/**\n * Turn emoji codes into emojis\n *\n * List of supported emojis: https://github.com/showdownjs/showdown/wiki/Emojis\n */\nshowdown.subParser('emoji', function (text, options, globals) {\n  'use strict';\n\n  if (!options.emoji) {\n    return text;\n  }\n\n  text = globals.converter._dispatch('emoji.before', text, options, globals);\n\n  var emojiRgx = /:([\\S]+?):/g;\n\n  text = text.replace(emojiRgx, function (wm, emojiCode) {\n    if (showdown.helper.emojis.hasOwnProperty(emojiCode)) {\n      return showdown.helper.emojis[emojiCode];\n    }\n    return wm;\n  });\n\n  text = globals.converter._dispatch('emoji.after', text, options, globals);\n\n  return text;\n});\n", "/**\n * Smart processing for ampersands and angle brackets that need to be encoded.\n */\nshowdown.subParser('encodeAmpsAndAngles', function (text, options, globals) {\n  'use strict';\n  text = globals.converter._dispatch('encodeAmpsAndAngles.before', text, options, globals);\n\n  // Ampersand-encoding based entirely on <PERSON> Irons's Amputator MT plugin:\n  // http://bumppo.net/projects/amputator/\n  text = text.replace(/&(?!#?[xX]?(?:[0-9a-fA-F]+|\\w+);)/g, '&amp;');\n\n  // Encode naked <'s\n  text = text.replace(/<(?![a-z\\/?$!])/gi, '&lt;');\n\n  // Encode <\n  text = text.replace(/</g, '&lt;');\n\n  // Encode >\n  text = text.replace(/>/g, '&gt;');\n\n  text = globals.converter._dispatch('encodeAmpsAndAngles.after', text, options, globals);\n  return text;\n});\n", "/**\n * Returns the string, with after processing the following backslash escape sequences.\n *\n * attacklab: The polite way to do this is with the new escapeCharacters() function:\n *\n *    text = escapeCharacters(text,\"\\\\\",true);\n *    text = escapeCharacters(text,\"`*_{}[]()>#+-.!\",true);\n *\n * ...but we're sidestepping its use of the (slow) RegExp constructor\n * as an optimization for Firefox.  This function gets called a LOT.\n */\nshowdown.subParser('encodeBackslashEscapes', function (text, options, globals) {\n  'use strict';\n  text = globals.converter._dispatch('encodeBackslashEscapes.before', text, options, globals);\n\n  text = text.replace(/\\\\(\\\\)/g, showdown.helper.escapeCharactersCallback);\n  text = text.replace(/\\\\([`*_{}\\[\\]()>#+.!~=|:-])/g, showdown.helper.escapeCharactersCallback);\n\n  text = globals.converter._dispatch('encodeBackslashEscapes.after', text, options, globals);\n  return text;\n});\n", "/**\n * Encode/escape certain characters inside Markdown code runs.\n * The point is that in code, these characters are literals,\n * and lose their special Markdown meanings.\n */\nshowdown.subParser('encodeCode', function (text, options, globals) {\n  'use strict';\n\n  text = globals.converter._dispatch('encodeCode.before', text, options, globals);\n\n  // Encode all ampersands; HTML entities are not\n  // entities within a Markdown code span.\n  text = text\n    .replace(/&/g, '&amp;')\n  // Do the angle bracket song and dance:\n    .replace(/</g, '&lt;')\n    .replace(/>/g, '&gt;')\n  // Now, escape characters that are magic in Markdown:\n    .replace(/([*_{}\\[\\]\\\\=~-])/g, showdown.helper.escapeCharactersCallback);\n\n  text = globals.converter._dispatch('encodeCode.after', text, options, globals);\n  return text;\n});\n", "/**\n * Within tags -- meaning between < and > -- encode [\\ ` * _ ~ =] so they\n * don't conflict with their use in Markdown for code, italics and strong.\n */\nshowdown.subParser('escapeSpecialCharsWithinTagAttributes', function (text, options, globals) {\n  'use strict';\n  text = globals.converter._dispatch('escapeSpecialCharsWithinTagAttributes.before', text, options, globals);\n\n  // Build a regex to find HTML tags.\n  var tags     = /<\\/?[a-z\\d_:-]+(?:[\\s]+[\\s\\S]+?)?>/gi,\n      comments = /<!(--(?:(?:[^>-]|-[^>])(?:[^-]|-[^-])*)--)>/gi;\n\n  text = text.replace(tags, function (wholeMatch) {\n    return wholeMatch\n      .replace(/(.)<\\/?code>(?=.)/g, '$1`')\n      .replace(/([\\\\`*_~=|])/g, showdown.helper.escapeCharactersCallback);\n  });\n\n  text = text.replace(comments, function (wholeMatch) {\n    return wholeMatch\n      .replace(/([\\\\`*_~=|])/g, showdown.helper.escapeCharactersCallback);\n  });\n\n  text = globals.converter._dispatch('escapeSpecialCharsWithinTagAttributes.after', text, options, globals);\n  return text;\n});\n", "/**\n * Handle github codeblocks prior to running HashHTML so that\n * HTML contained within the codeblock gets escaped properly\n * Example:\n * ```ruby\n *     def hello_world(x)\n *       puts \"Hello, #{x}\"\n *     end\n * ```\n */\nshowdown.subParser('githubCodeBlocks', function (text, options, globals) {\n  'use strict';\n\n  // early exit if option is not enabled\n  if (!options.ghCodeBlocks) {\n    return text;\n  }\n\n  text = globals.converter._dispatch('githubCodeBlocks.before', text, options, globals);\n\n  text += '¨0';\n\n  text = text.replace(/(?:^|\\n)(?: {0,3})(```+|~~~+)(?: *)([^\\s`~]*)\\n([\\s\\S]*?)\\n(?: {0,3})\\1/g, function (wholeMatch, delim, language, codeblock) {\n    var end = (options.omitExtraWLInCodeBlocks) ? '' : '\\n';\n\n    // First parse the github code block\n    codeblock = showdown.subParser('encodeCode')(codeblock, options, globals);\n    codeblock = showdown.subParser('detab')(codeblock, options, globals);\n    codeblock = codeblock.replace(/^\\n+/g, ''); // trim leading newlines\n    codeblock = codeblock.replace(/\\n+$/g, ''); // trim trailing whitespace\n\n    codeblock = '<pre><code' + (language ? ' class=\"' + language + ' language-' + language + '\"' : '') + '>' + codeblock + end + '</code></pre>';\n\n    codeblock = showdown.subParser('hashBlock')(codeblock, options, globals);\n\n    // Since GHCodeblocks can be false positives, we need to\n    // store the primitive text and the parsed text in a global var,\n    // and then return a token\n    return '\\n\\n¨G' + (globals.ghCodeBlocks.push({text: wholeMatch, codeblock: codeblock}) - 1) + 'G\\n\\n';\n  });\n\n  // attacklab: strip sentinel\n  text = text.replace(/¨0/, '');\n\n  return globals.converter._dispatch('githubCodeBlocks.after', text, options, globals);\n});\n", "showdown.subParser('hashBlock', function (text, options, globals) {\n  'use strict';\n  text = globals.converter._dispatch('hashBlock.before', text, options, globals);\n  text = text.replace(/(^\\n+|\\n+$)/g, '');\n  text = '\\n\\n¨K' + (globals.gHtmlBlocks.push(text) - 1) + 'K\\n\\n';\n  text = globals.converter._dispatch('hashBlock.after', text, options, globals);\n  return text;\n});\n", "/**\n * Hash and escape <code> elements that should not be parsed as markdown\n */\nshowdown.subParser('hashCodeTags', function (text, options, globals) {\n  'use strict';\n  text = globals.converter._dispatch('hashCodeTags.before', text, options, globals);\n\n  var repFunc = function (wholeMatch, match, left, right) {\n    var codeblock = left + showdown.subParser('encodeCode')(match, options, globals) + right;\n    return '¨C' + (globals.gHtmlSpans.push(codeblock) - 1) + 'C';\n  };\n\n  // Hash naked <code>\n  text = showdown.helper.replaceRecursiveRegExp(text, repFunc, '<code\\\\b[^>]*>', '</code>', 'gim');\n\n  text = globals.converter._dispatch('hashCodeTags.after', text, options, globals);\n  return text;\n});\n", "showdown.subParser('hashElement', function (text, options, globals) {\n  'use strict';\n\n  return function (wholeMatch, m1) {\n    var blockText = m1;\n\n    // Undo double lines\n    blockText = blockText.replace(/\\n\\n/g, '\\n');\n    blockText = blockText.replace(/^\\n/, '');\n\n    // strip trailing blank lines\n    blockText = blockText.replace(/\\n+$/g, '');\n\n    // Replace the element text with a marker (\"¨KxK\" where x is its key)\n    blockText = '\\n\\n¨K' + (globals.gHtmlBlocks.push(blockText) - 1) + 'K\\n\\n';\n\n    return blockText;\n  };\n});\n", "showdown.subParser('hashHTMLBlocks', function (text, options, globals) {\n  'use strict';\n  text = globals.converter._dispatch('hashHTMLBlocks.before', text, options, globals);\n\n  var blockTags = [\n        'pre',\n        'div',\n        'h1',\n        'h2',\n        'h3',\n        'h4',\n        'h5',\n        'h6',\n        'blockquote',\n        'table',\n        'dl',\n        'ol',\n        'ul',\n        'script',\n        'noscript',\n        'form',\n        'fieldset',\n        'iframe',\n        'math',\n        'style',\n        'section',\n        'header',\n        'footer',\n        'nav',\n        'article',\n        'aside',\n        'address',\n        'audio',\n        'canvas',\n        'figure',\n        'hgroup',\n        'output',\n        'video',\n        'p'\n      ],\n      repFunc = function (wholeMatch, match, left, right) {\n        var txt = wholeMatch;\n        // check if this html element is marked as markdown\n        // if so, it's contents should be parsed as markdown\n        if (left.search(/\\bmarkdown\\b/) !== -1) {\n          txt = left + globals.converter.makeHtml(match) + right;\n        }\n        return '\\n\\n¨K' + (globals.gHtmlBlocks.push(txt) - 1) + 'K\\n\\n';\n      };\n\n  if (options.backslashEscapesHTMLTags) {\n    // encode backslash escaped HTML tags\n    text = text.replace(/\\\\<(\\/?[^>]+?)>/g, function (wm, inside) {\n      return '&lt;' + inside + '&gt;';\n    });\n  }\n\n  // hash HTML Blocks\n  for (var i = 0; i < blockTags.length; ++i) {\n\n    var opTagPos,\n        rgx1     = new RegExp('^ {0,3}(<' + blockTags[i] + '\\\\b[^>]*>)', 'im'),\n        patLeft  = '<' + blockTags[i] + '\\\\b[^>]*>',\n        patRight = '</' + blockTags[i] + '>';\n    // 1. Look for the first position of the first opening HTML tag in the text\n    while ((opTagPos = showdown.helper.regexIndexOf(text, rgx1)) !== -1) {\n\n      // if the HTML tag is \\ escaped, we need to escape it and break\n\n\n      //2. Split the text in that position\n      var subTexts = showdown.helper.splitAtIndex(text, opTagPos),\n          //3. Match recursively\n          newSubText1 = showdown.helper.replaceRecursiveRegExp(subTexts[1], repFunc, patLeft, patRight, 'im');\n\n      // prevent an infinite loop\n      if (newSubText1 === subTexts[1]) {\n        break;\n      }\n      text = subTexts[0].concat(newSubText1);\n    }\n  }\n  // HR SPECIAL CASE\n  text = text.replace(/(\\n {0,3}(<(hr)\\b([^<>])*?\\/?>)[ \\t]*(?=\\n{2,}))/g,\n    showdown.subParser('hashElement')(text, options, globals));\n\n  // Special case for standalone HTML comments\n  text = showdown.helper.replaceRecursiveRegExp(text, function (txt) {\n    return '\\n\\n¨K' + (globals.gHtmlBlocks.push(txt) - 1) + 'K\\n\\n';\n  }, '^ {0,3}<!--', '-->', 'gm');\n\n  // PHP and ASP-style processor instructions (<?...?> and <%...%>)\n  text = text.replace(/(?:\\n\\n)( {0,3}(?:<([?%])[^\\r]*?\\2>)[ \\t]*(?=\\n{2,}))/g,\n    showdown.subParser('hashElement')(text, options, globals));\n\n  text = globals.converter._dispatch('hashHTMLBlocks.after', text, options, globals);\n  return text;\n});\n", "/**\n * Hash span elements that should not be parsed as markdown\n */\nshowdown.subParser('hashHTMLSpans', function (text, options, globals) {\n  'use strict';\n  text = globals.converter._dispatch('hashHTMLSpans.before', text, options, globals);\n\n  function hashHTMLSpan (html) {\n    return '¨C' + (globals.gHtmlSpans.push(html) - 1) + 'C';\n  }\n\n  // Hash Self Closing tags\n  text = text.replace(/<[^>]+?\\/>/gi, function (wm) {\n    return hashHTMLSpan(wm);\n  });\n\n  // Hash tags without properties\n  text = text.replace(/<([^>]+?)>[\\s\\S]*?<\\/\\1>/g, function (wm) {\n    return hashHTMLSpan(wm);\n  });\n\n  // Hash tags with properties\n  text = text.replace(/<([^>]+?)\\s[^>]+?>[\\s\\S]*?<\\/\\1>/g, function (wm) {\n    return hashHTMLSpan(wm);\n  });\n\n  // Hash self closing tags without />\n  text = text.replace(/<[^>]+?>/gi, function (wm) {\n    return hashHTMLSpan(wm);\n  });\n\n  /*showdown.helper.matchRecursiveRegExp(text, '<code\\\\b[^>]*>', '</code>', 'gi');*/\n\n  text = globals.converter._dispatch('hashHTMLSpans.after', text, options, globals);\n  return text;\n});\n\n/**\n * Unhash HTML spans\n */\nshowdown.subParser('unhashHTMLSpans', function (text, options, globals) {\n  'use strict';\n  text = globals.converter._dispatch('unhashHTMLSpans.before', text, options, globals);\n\n  for (var i = 0; i < globals.gHtmlSpans.length; ++i) {\n    var repText = globals.gHtmlSpans[i],\n        // limiter to prevent infinite loop (assume 10 as limit for recurse)\n        limit = 0;\n\n    while (/¨C(\\d+)C/.test(repText)) {\n      var num = RegExp.$1;\n      repText = repText.replace('¨C' + num + 'C', globals.gHtmlSpans[num]);\n      if (limit === 10) {\n        console.error('maximum nesting of 10 spans reached!!!');\n        break;\n      }\n      ++limit;\n    }\n    text = text.replace('¨C' + i + 'C', repText);\n  }\n\n  text = globals.converter._dispatch('unhashHTMLSpans.after', text, options, globals);\n  return text;\n});\n", "/**\n * Hash and escape <pre><code> elements that should not be parsed as markdown\n */\nshowdown.subParser('hashPreCodeTags', function (text, options, globals) {\n  'use strict';\n  text = globals.converter._dispatch('hashPreCodeTags.before', text, options, globals);\n\n  var repFunc = function (wholeMatch, match, left, right) {\n    // encode html entities\n    var codeblock = left + showdown.subParser('encodeCode')(match, options, globals) + right;\n    return '\\n\\n¨G' + (globals.ghCodeBlocks.push({text: wholeMatch, codeblock: codeblock}) - 1) + 'G\\n\\n';\n  };\n\n  // Hash <pre><code>\n  text = showdown.helper.replaceRecursiveRegExp(text, repFunc, '^ {0,3}<pre\\\\b[^>]*>\\\\s*<code\\\\b[^>]*>', '^ {0,3}</code>\\\\s*</pre>', 'gim');\n\n  text = globals.converter._dispatch('hashPreCodeTags.after', text, options, globals);\n  return text;\n});\n", "showdown.subParser('headers', function (text, options, globals) {\n  'use strict';\n\n  text = globals.converter._dispatch('headers.before', text, options, globals);\n\n  var headerLevelStart = (isNaN(parseInt(options.headerLevelStart))) ? 1 : parseInt(options.headerLevelStart),\n\n      // Set text-style headers:\n      //\tHeader 1\n      //\t========\n      //\n      //\tHeader 2\n      //\t--------\n      //\n      setextRegexH1 = (options.smoothLivePreview) ? /^(.+)[ \\t]*\\n={2,}[ \\t]*\\n+/gm : /^(.+)[ \\t]*\\n=+[ \\t]*\\n+/gm,\n      setextRegexH2 = (options.smoothLivePreview) ? /^(.+)[ \\t]*\\n-{2,}[ \\t]*\\n+/gm : /^(.+)[ \\t]*\\n-+[ \\t]*\\n+/gm;\n\n  text = text.replace(setextRegexH1, function (wholeMatch, m1) {\n\n    var spanGamut = showdown.subParser('spanGamut')(m1, options, globals),\n        hID = (options.noHeaderId) ? '' : ' id=\"' + headerId(m1) + '\"',\n        hLevel = headerLevelStart,\n        hashBlock = '<h' + hLevel + hID + '>' + spanGamut + '</h' + hLevel + '>';\n    return showdown.subParser('hashBlock')(hashBlock, options, globals);\n  });\n\n  text = text.replace(setextRegexH2, function (matchFound, m1) {\n    var spanGamut = showdown.subParser('spanGamut')(m1, options, globals),\n        hID = (options.noHeaderId) ? '' : ' id=\"' + headerId(m1) + '\"',\n        hLevel = headerLevelStart + 1,\n        hashBlock = '<h' + hLevel + hID + '>' + spanGamut + '</h' + hLevel + '>';\n    return showdown.subParser('hashBlock')(hashBlock, options, globals);\n  });\n\n  // atx-style headers:\n  //  # Header 1\n  //  ## Header 2\n  //  ## Header 2 with closing hashes ##\n  //  ...\n  //  ###### Header 6\n  //\n  var atxStyle = (options.requireSpaceBeforeHeadingText) ? /^(#{1,6})[ \\t]+(.+?)[ \\t]*#*\\n+/gm : /^(#{1,6})[ \\t]*(.+?)[ \\t]*#*\\n+/gm;\n\n  text = text.replace(atxStyle, function (wholeMatch, m1, m2) {\n    var hText = m2;\n    if (options.customizedHeaderId) {\n      hText = m2.replace(/\\s?\\{([^{]+?)}\\s*$/, '');\n    }\n\n    var span = showdown.subParser('spanGamut')(hText, options, globals),\n        hID = (options.noHeaderId) ? '' : ' id=\"' + headerId(m2) + '\"',\n        hLevel = headerLevelStart - 1 + m1.length,\n        header = '<h' + hLevel + hID + '>' + span + '</h' + hLevel + '>';\n\n    return showdown.subParser('hashBlock')(header, options, globals);\n  });\n\n  function headerId (m) {\n    var title,\n        prefix;\n\n    // It is separate from other options to allow combining prefix and customized\n    if (options.customizedHeaderId) {\n      var match = m.match(/\\{([^{]+?)}\\s*$/);\n      if (match && match[1]) {\n        m = match[1];\n      }\n    }\n\n    title = m;\n\n    // Prefix id to prevent causing inadvertent pre-existing style matches.\n    if (showdown.helper.isString(options.prefixHeaderId)) {\n      prefix = options.prefixHeaderId;\n    } else if (options.prefixHeaderId === true) {\n      prefix = 'section-';\n    } else {\n      prefix = '';\n    }\n\n    if (!options.rawPrefixHeaderId) {\n      title = prefix + title;\n    }\n\n    if (options.ghCompatibleHeaderId) {\n      title = title\n        .replace(/ /g, '-')\n        // replace previously escaped chars (&, ¨ and $)\n        .replace(/&amp;/g, '')\n        .replace(/¨T/g, '')\n        .replace(/¨D/g, '')\n        // replace rest of the chars (&~$ are repeated as they might have been escaped)\n        // borrowed from github's redcarpet (some they should produce similar results)\n        .replace(/[&+$,\\/:;=?@\"#{}|^¨~\\[\\]`\\\\*)(%.!'<>]/g, '')\n        .toLowerCase();\n    } else if (options.rawHeaderId) {\n      title = title\n        .replace(/ /g, '-')\n        // replace previously escaped chars (&, ¨ and $)\n        .replace(/&amp;/g, '&')\n        .replace(/¨T/g, '¨')\n        .replace(/¨D/g, '$')\n        // replace \" and '\n        .replace(/[\"']/g, '-')\n        .toLowerCase();\n    } else {\n      title = title\n        .replace(/[^\\w]/g, '')\n        .toLowerCase();\n    }\n\n    if (options.rawPrefixHeaderId) {\n      title = prefix + title;\n    }\n\n    if (globals.hashLinkCounts[title]) {\n      title = title + '-' + (globals.hashLinkCounts[title]++);\n    } else {\n      globals.hashLinkCounts[title] = 1;\n    }\n    return title;\n  }\n\n  text = globals.converter._dispatch('headers.after', text, options, globals);\n  return text;\n});\n", "/**\n * Turn Markdown link shortcuts into XHTML <a> tags.\n */\nshowdown.subParser('horizontalRule', function (text, options, globals) {\n  'use strict';\n  text = globals.converter._dispatch('horizontalRule.before', text, options, globals);\n\n  var key = showdown.subParser('hashBlock')('<hr />', options, globals);\n  text = text.replace(/^ {0,2}( ?-){3,}[ \\t]*$/gm, key);\n  text = text.replace(/^ {0,2}( ?\\*){3,}[ \\t]*$/gm, key);\n  text = text.replace(/^ {0,2}( ?_){3,}[ \\t]*$/gm, key);\n\n  text = globals.converter._dispatch('horizontalRule.after', text, options, globals);\n  return text;\n});\n", "/**\n * Turn Markdown image shortcuts into <img> tags.\n */\nshowdown.subParser('images', function (text, options, globals) {\n  'use strict';\n\n  text = globals.converter._dispatch('images.before', text, options, globals);\n\n  var inlineRegExp      = /!\\[([^\\]]*?)][ \\t]*()\\([ \\t]?<?([\\S]+?(?:\\([\\S]*?\\)[\\S]*?)?)>?(?: =([*\\d]+[A-Za-z%]{0,4})x([*\\d]+[A-Za-z%]{0,4}))?[ \\t]*(?:([\"'])([^\"]*?)\\6)?[ \\t]?\\)/g,\n      crazyRegExp       = /!\\[([^\\]]*?)][ \\t]*()\\([ \\t]?<([^>]*)>(?: =([*\\d]+[A-Za-z%]{0,4})x([*\\d]+[A-Za-z%]{0,4}))?[ \\t]*(?:(?:([\"'])([^\"]*?)\\6))?[ \\t]?\\)/g,\n      base64RegExp      = /!\\[([^\\]]*?)][ \\t]*()\\([ \\t]?<?(data:.+?\\/.+?;base64,[A-Za-z0-9+/=\\n]+?)>?(?: =([*\\d]+[A-Za-z%]{0,4})x([*\\d]+[A-Za-z%]{0,4}))?[ \\t]*(?:([\"'])([^\"]*?)\\6)?[ \\t]?\\)/g,\n      referenceRegExp   = /!\\[([^\\]]*?)] ?(?:\\n *)?\\[([\\s\\S]*?)]()()()()()/g,\n      refShortcutRegExp = /!\\[([^\\[\\]]+)]()()()()()/g;\n\n  function writeImageTagBase64 (wholeMatch, altText, linkId, url, width, height, m5, title) {\n    url = url.replace(/\\s/g, '');\n    return writeImageTag (wholeMatch, altText, linkId, url, width, height, m5, title);\n  }\n\n  function writeImageTag (wholeMatch, altText, linkId, url, width, height, m5, title) {\n\n    var gUrls   = globals.gUrls,\n        gTitles = globals.gTitles,\n        gDims   = globals.gDimensions;\n\n    linkId = linkId.toLowerCase();\n\n    if (!title) {\n      title = '';\n    }\n    // Special case for explicit empty url\n    if (wholeMatch.search(/\\(<?\\s*>? ?(['\"].*['\"])?\\)$/m) > -1) {\n      url = '';\n\n    } else if (url === '' || url === null) {\n      if (linkId === '' || linkId === null) {\n        // lower-case and turn embedded newlines into spaces\n        linkId = altText.toLowerCase().replace(/ ?\\n/g, ' ');\n      }\n      url = '#' + linkId;\n\n      if (!showdown.helper.isUndefined(gUrls[linkId])) {\n        url = gUrls[linkId];\n        if (!showdown.helper.isUndefined(gTitles[linkId])) {\n          title = gTitles[linkId];\n        }\n        if (!showdown.helper.isUndefined(gDims[linkId])) {\n          width = gDims[linkId].width;\n          height = gDims[linkId].height;\n        }\n      } else {\n        return wholeMatch;\n      }\n    }\n\n    altText = altText\n      .replace(/\"/g, '&quot;')\n    //altText = showdown.helper.escapeCharacters(altText, '*_', false);\n      .replace(showdown.helper.regexes.asteriskDashAndColon, showdown.helper.escapeCharactersCallback);\n    //url = showdown.helper.escapeCharacters(url, '*_', false);\n    url = url.replace(showdown.helper.regexes.asteriskDashAndColon, showdown.helper.escapeCharactersCallback);\n    var result = '<img src=\"' + url + '\" alt=\"' + altText + '\"';\n\n    if (title && showdown.helper.isString(title)) {\n      title = title\n        .replace(/\"/g, '&quot;')\n      //title = showdown.helper.escapeCharacters(title, '*_', false);\n        .replace(showdown.helper.regexes.asteriskDashAndColon, showdown.helper.escapeCharactersCallback);\n      result += ' title=\"' + title + '\"';\n    }\n\n    if (width && height) {\n      width  = (width === '*') ? 'auto' : width;\n      height = (height === '*') ? 'auto' : height;\n\n      result += ' width=\"' + width + '\"';\n      result += ' height=\"' + height + '\"';\n    }\n\n    result += ' />';\n\n    return result;\n  }\n\n  // First, handle reference-style labeled images: ![alt text][id]\n  text = text.replace(referenceRegExp, writeImageTag);\n\n  // Next, handle inline images:  ![alt text](url =<width>x<height> \"optional title\")\n\n  // base64 encoded images\n  text = text.replace(base64RegExp, writeImageTagBase64);\n\n  // cases with crazy urls like ./image/cat1).png\n  text = text.replace(crazyRegExp, writeImageTag);\n\n  // normal cases\n  text = text.replace(inlineRegExp, writeImageTag);\n\n  // handle reference-style shortcuts: ![img text]\n  text = text.replace(refShortcutRegExp, writeImageTag);\n\n  text = globals.converter._dispatch('images.after', text, options, globals);\n  return text;\n});\n", "showdown.subParser('italicsAndBold', function (text, options, globals) {\n  'use strict';\n\n  text = globals.converter._dispatch('italicsAndBold.before', text, options, globals);\n\n  // it's faster to have 3 separate regexes for each case than have just one\n  // because of backtracing, in some cases, it could lead to an exponential effect\n  // called \"catastrophic backtrace\". Ominous!\n\n  function parseInside (txt, left, right) {\n    /*\n    if (options.simplifiedAutoLink) {\n      txt = showdown.subParser('simplifiedAutoLinks')(txt, options, globals);\n    }\n    */\n    return left + txt + right;\n  }\n\n  // Parse underscores\n  if (options.literalMidWordUnderscores) {\n    text = text.replace(/\\b___(\\S[\\s\\S]*?)___\\b/g, function (wm, txt) {\n      return parseInside (txt, '<strong><em>', '</em></strong>');\n    });\n    text = text.replace(/\\b__(\\S[\\s\\S]*?)__\\b/g, function (wm, txt) {\n      return parseInside (txt, '<strong>', '</strong>');\n    });\n    text = text.replace(/\\b_(\\S[\\s\\S]*?)_\\b/g, function (wm, txt) {\n      return parseInside (txt, '<em>', '</em>');\n    });\n  } else {\n    text = text.replace(/___(\\S[\\s\\S]*?)___/g, function (wm, m) {\n      return (/\\S$/.test(m)) ? parseInside (m, '<strong><em>', '</em></strong>') : wm;\n    });\n    text = text.replace(/__(\\S[\\s\\S]*?)__/g, function (wm, m) {\n      return (/\\S$/.test(m)) ? parseInside (m, '<strong>', '</strong>') : wm;\n    });\n    text = text.replace(/_([^\\s_][\\s\\S]*?)_/g, function (wm, m) {\n      // !/^_[^_]/.test(m) - test if it doesn't start with __ (since it seems redundant, we removed it)\n      return (/\\S$/.test(m)) ? parseInside (m, '<em>', '</em>') : wm;\n    });\n  }\n\n  // Now parse asterisks\n  if (options.literalMidWordAsterisks) {\n    text = text.replace(/([^*]|^)\\B\\*\\*\\*(\\S[\\s\\S]*?)\\*\\*\\*\\B(?!\\*)/g, function (wm, lead, txt) {\n      return parseInside (txt, lead + '<strong><em>', '</em></strong>');\n    });\n    text = text.replace(/([^*]|^)\\B\\*\\*(\\S[\\s\\S]*?)\\*\\*\\B(?!\\*)/g, function (wm, lead, txt) {\n      return parseInside (txt, lead + '<strong>', '</strong>');\n    });\n    text = text.replace(/([^*]|^)\\B\\*(\\S[\\s\\S]*?)\\*\\B(?!\\*)/g, function (wm, lead, txt) {\n      return parseInside (txt, lead + '<em>', '</em>');\n    });\n  } else {\n    text = text.replace(/\\*\\*\\*(\\S[\\s\\S]*?)\\*\\*\\*/g, function (wm, m) {\n      return (/\\S$/.test(m)) ? parseInside (m, '<strong><em>', '</em></strong>') : wm;\n    });\n    text = text.replace(/\\*\\*(\\S[\\s\\S]*?)\\*\\*/g, function (wm, m) {\n      return (/\\S$/.test(m)) ? parseInside (m, '<strong>', '</strong>') : wm;\n    });\n    text = text.replace(/\\*([^\\s*][\\s\\S]*?)\\*/g, function (wm, m) {\n      // !/^\\*[^*]/.test(m) - test if it doesn't start with ** (since it seems redundant, we removed it)\n      return (/\\S$/.test(m)) ? parseInside (m, '<em>', '</em>') : wm;\n    });\n  }\n\n\n  text = globals.converter._dispatch('italicsAndBold.after', text, options, globals);\n  return text;\n});\n", "/**\n * Form HTML ordered (numbered) and unordered (bulleted) lists.\n */\nshowdown.subParser('lists', function (text, options, globals) {\n  'use strict';\n\n  /**\n   * Process the contents of a single ordered or unordered list, splitting it\n   * into individual list items.\n   * @param {string} listStr\n   * @param {boolean} trimTrailing\n   * @returns {string}\n   */\n  function processListItems (listStr, trimTrailing) {\n    // The $g_list_level global keeps track of when we're inside a list.\n    // Each time we enter a list, we increment it; when we leave a list,\n    // we decrement. If it's zero, we're not in a list anymore.\n    //\n    // We do this because when we're not inside a list, we want to treat\n    // something like this:\n    //\n    //    I recommend upgrading to version\n    //    8. Oops, now this line is treated\n    //    as a sub-list.\n    //\n    // As a single paragraph, despite the fact that the second line starts\n    // with a digit-period-space sequence.\n    //\n    // Whereas when we're inside a list (or sub-list), that line will be\n    // treated as the start of a sub-list. What a kludge, huh? This is\n    // an aspect of <PERSON><PERSON>'s syntax that's hard to parse perfectly\n    // without resorting to mind-reading. Perhaps the solution is to\n    // change the syntax rules such that sub-lists must start with a\n    // starting cardinal number; e.g. \"1.\" or \"a.\".\n    globals.gListLevel++;\n\n    // trim trailing blank lines:\n    listStr = listStr.replace(/\\n{2,}$/, '\\n');\n\n    // attacklab: add sentinel to emulate \\z\n    listStr += '¨0';\n\n    var rgx = /(\\n)?(^ {0,3})([*+-]|\\d+[.])[ \\t]+((\\[(x|X| )?])?[ \\t]*[^\\r]+?(\\n{1,2}))(?=\\n*(¨0| {0,3}([*+-]|\\d+[.])[ \\t]+))/gm,\n        isParagraphed = (/\\n[ \\t]*\\n(?!¨0)/.test(listStr));\n\n    // Since version 1.5, nesting sublists requires 4 spaces (or 1 tab) indentation,\n    // which is a syntax breaking change\n    // activating this option reverts to old behavior\n    if (options.disableForced4SpacesIndentedSublists) {\n      rgx = /(\\n)?(^ {0,3})([*+-]|\\d+[.])[ \\t]+((\\[(x|X| )?])?[ \\t]*[^\\r]+?(\\n{1,2}))(?=\\n*(¨0|\\2([*+-]|\\d+[.])[ \\t]+))/gm;\n    }\n\n    listStr = listStr.replace(rgx, function (wholeMatch, m1, m2, m3, m4, taskbtn, checked) {\n      checked = (checked && checked.trim() !== '');\n\n      var item = showdown.subParser('outdent')(m4, options, globals),\n          bulletStyle = '';\n\n      // Support for github tasklists\n      if (taskbtn && options.tasklists) {\n        bulletStyle = ' class=\"task-list-item\" style=\"list-style-type: none;\"';\n        item = item.replace(/^[ \\t]*\\[(x|X| )?]/m, function () {\n          var otp = '<input type=\"checkbox\" disabled style=\"margin: 0px 0.35em 0.25em -1.6em; vertical-align: middle;\"';\n          if (checked) {\n            otp += ' checked';\n          }\n          otp += '>';\n          return otp;\n        });\n      }\n\n      // ISSUE #312\n      // This input: - - - a\n      // causes trouble to the parser, since it interprets it as:\n      // <ul><li><li><li>a</li></li></li></ul>\n      // instead of:\n      // <ul><li>- - a</li></ul>\n      // So, to prevent it, we will put a marker (¨A)in the beginning of the line\n      // Kind of hackish/monkey patching, but seems more effective than overcomplicating the list parser\n      item = item.replace(/^([-*+]|\\d\\.)[ \\t]+[\\S\\n ]*/g, function (wm2) {\n        return '¨A' + wm2;\n      });\n\n      // m1 - Leading line or\n      // Has a double return (multi paragraph) or\n      // Has sublist\n      if (m1 || (item.search(/\\n{2,}/) > -1)) {\n        item = showdown.subParser('githubCodeBlocks')(item, options, globals);\n        item = showdown.subParser('blockGamut')(item, options, globals);\n      } else {\n        // Recursion for sub-lists:\n        item = showdown.subParser('lists')(item, options, globals);\n        item = item.replace(/\\n$/, ''); // chomp(item)\n        item = showdown.subParser('hashHTMLBlocks')(item, options, globals);\n\n        // Colapse double linebreaks\n        item = item.replace(/\\n\\n+/g, '\\n\\n');\n        if (isParagraphed) {\n          item = showdown.subParser('paragraphs')(item, options, globals);\n        } else {\n          item = showdown.subParser('spanGamut')(item, options, globals);\n        }\n      }\n\n      // now we need to remove the marker (¨A)\n      item = item.replace('¨A', '');\n      // we can finally wrap the line in list item tags\n      item =  '<li' + bulletStyle + '>' + item + '</li>\\n';\n\n      return item;\n    });\n\n    // attacklab: strip sentinel\n    listStr = listStr.replace(/¨0/g, '');\n\n    globals.gListLevel--;\n\n    if (trimTrailing) {\n      listStr = listStr.replace(/\\s+$/, '');\n    }\n\n    return listStr;\n  }\n\n  function styleStartNumber (list, listType) {\n    // check if ol and starts by a number different than 1\n    if (listType === 'ol') {\n      var res = list.match(/^ *(\\d+)\\./);\n      if (res && res[1] !== '1') {\n        return ' start=\"' + res[1] + '\"';\n      }\n    }\n    return '';\n  }\n\n  /**\n   * Check and parse consecutive lists (better fix for issue #142)\n   * @param {string} list\n   * @param {string} listType\n   * @param {boolean} trimTrailing\n   * @returns {string}\n   */\n  function parseConsecutiveLists (list, listType, trimTrailing) {\n    // check if we caught 2 or more consecutive lists by mistake\n    // we use the counterRgx, meaning if listType is UL we look for OL and vice versa\n    var olRgx = (options.disableForced4SpacesIndentedSublists) ? /^ ?\\d+\\.[ \\t]/gm : /^ {0,3}\\d+\\.[ \\t]/gm,\n        ulRgx = (options.disableForced4SpacesIndentedSublists) ? /^ ?[*+-][ \\t]/gm : /^ {0,3}[*+-][ \\t]/gm,\n        counterRxg = (listType === 'ul') ? olRgx : ulRgx,\n        result = '';\n\n    if (list.search(counterRxg) !== -1) {\n      (function parseCL (txt) {\n        var pos = txt.search(counterRxg),\n            style = styleStartNumber(list, listType);\n        if (pos !== -1) {\n          // slice\n          result += '\\n\\n<' + listType + style + '>\\n' + processListItems(txt.slice(0, pos), !!trimTrailing) + '</' + listType + '>\\n';\n\n          // invert counterType and listType\n          listType = (listType === 'ul') ? 'ol' : 'ul';\n          counterRxg = (listType === 'ul') ? olRgx : ulRgx;\n\n          //recurse\n          parseCL(txt.slice(pos));\n        } else {\n          result += '\\n\\n<' + listType + style + '>\\n' + processListItems(txt, !!trimTrailing) + '</' + listType + '>\\n';\n        }\n      })(list);\n    } else {\n      var style = styleStartNumber(list, listType);\n      result = '\\n\\n<' + listType + style + '>\\n' + processListItems(list, !!trimTrailing) + '</' + listType + '>\\n';\n    }\n\n    return result;\n  }\n\n  /** Start of list parsing **/\n  text = globals.converter._dispatch('lists.before', text, options, globals);\n  // add sentinel to hack around khtml/safari bug:\n  // http://bugs.webkit.org/show_bug.cgi?id=11231\n  text += '¨0';\n\n  if (globals.gListLevel) {\n    text = text.replace(/^(( {0,3}([*+-]|\\d+[.])[ \\t]+)[^\\r]+?(¨0|\\n{2,}(?=\\S)(?![ \\t]*(?:[*+-]|\\d+[.])[ \\t]+)))/gm,\n      function (wholeMatch, list, m2) {\n        var listType = (m2.search(/[*+-]/g) > -1) ? 'ul' : 'ol';\n        return parseConsecutiveLists(list, listType, true);\n      }\n    );\n  } else {\n    text = text.replace(/(\\n\\n|^\\n?)(( {0,3}([*+-]|\\d+[.])[ \\t]+)[^\\r]+?(¨0|\\n{2,}(?=\\S)(?![ \\t]*(?:[*+-]|\\d+[.])[ \\t]+)))/gm,\n      function (wholeMatch, m1, list, m3) {\n        var listType = (m3.search(/[*+-]/g) > -1) ? 'ul' : 'ol';\n        return parseConsecutiveLists(list, listType, false);\n      }\n    );\n  }\n\n  // strip sentinel\n  text = text.replace(/¨0/, '');\n  text = globals.converter._dispatch('lists.after', text, options, globals);\n  return text;\n});\n", "/**\n * Parse metadata at the top of the document\n */\nshowdown.subParser('metadata', function (text, options, globals) {\n  'use strict';\n\n  if (!options.metadata) {\n    return text;\n  }\n\n  text = globals.converter._dispatch('metadata.before', text, options, globals);\n\n  function parseMetadataContents (content) {\n    // raw is raw so it's not changed in any way\n    globals.metadata.raw = content;\n\n    // escape chars forbidden in html attributes\n    // double quotes\n    content = content\n      // ampersand first\n      .replace(/&/g, '&amp;')\n      // double quotes\n      .replace(/\"/g, '&quot;');\n\n    content = content.replace(/\\n {4}/g, ' ');\n    content.replace(/^([\\S ]+): +([\\s\\S]+?)$/gm, function (wm, key, value) {\n      globals.metadata.parsed[key] = value;\n      return '';\n    });\n  }\n\n  text = text.replace(/^\\s*«««+(\\S*?)\\n([\\s\\S]+?)\\n»»»+\\n/, function (wholematch, format, content) {\n    parseMetadataContents(content);\n    return '¨M';\n  });\n\n  text = text.replace(/^\\s*---+(\\S*?)\\n([\\s\\S]+?)\\n---+\\n/, function (wholematch, format, content) {\n    if (format) {\n      globals.metadata.format = format;\n    }\n    parseMetadataContents(content);\n    return '¨M';\n  });\n\n  text = text.replace(/¨M/g, '');\n\n  text = globals.converter._dispatch('metadata.after', text, options, globals);\n  return text;\n});\n", "/**\n * Remove one level of line-leading tabs or spaces\n */\nshowdown.subParser('outdent', function (text, options, globals) {\n  'use strict';\n  text = globals.converter._dispatch('outdent.before', text, options, globals);\n\n  // attacklab: hack around Konqueror 3.5.4 bug:\n  // \"----------bug\".replace(/^-/g,\"\") == \"bug\"\n  text = text.replace(/^(\\t|[ ]{1,4})/gm, '¨0'); // attacklab: g_tab_width\n\n  // attacklab: clean up hack\n  text = text.replace(/¨0/g, '');\n\n  text = globals.converter._dispatch('outdent.after', text, options, globals);\n  return text;\n});\n", "/**\n *\n */\nshowdown.subParser('paragraphs', function (text, options, globals) {\n  'use strict';\n\n  text = globals.converter._dispatch('paragraphs.before', text, options, globals);\n  // Strip leading and trailing lines:\n  text = text.replace(/^\\n+/g, '');\n  text = text.replace(/\\n+$/g, '');\n\n  var grafs = text.split(/\\n{2,}/g),\n      grafsOut = [],\n      end = grafs.length; // Wrap <p> tags\n\n  for (var i = 0; i < end; i++) {\n    var str = grafs[i];\n    // if this is an HTML marker, copy it\n    if (str.search(/¨(K|G)(\\d+)\\1/g) >= 0) {\n      grafsOut.push(str);\n\n    // test for presence of characters to prevent empty lines being parsed\n    // as paragraphs (resulting in undesired extra empty paragraphs)\n    } else if (str.search(/\\S/) >= 0) {\n      str = showdown.subParser('spanGamut')(str, options, globals);\n      str = str.replace(/^([ \\t]*)/g, '<p>');\n      str += '</p>';\n      grafsOut.push(str);\n    }\n  }\n\n  /** Unhashify HTML blocks */\n  end = grafsOut.length;\n  for (i = 0; i < end; i++) {\n    var blockText = '',\n        grafsOutIt = grafsOut[i],\n        codeFlag = false;\n    // if this is a marker for an html block...\n    // use RegExp.test instead of string.search because of QML bug\n    while (/¨(K|G)(\\d+)\\1/.test(grafsOutIt)) {\n      var delim = RegExp.$1,\n          num   = RegExp.$2;\n\n      if (delim === 'K') {\n        blockText = globals.gHtmlBlocks[num];\n      } else {\n        // we need to check if ghBlock is a false positive\n        if (codeFlag) {\n          // use encoded version of all text\n          blockText = showdown.subParser('encodeCode')(globals.ghCodeBlocks[num].text, options, globals);\n        } else {\n          blockText = globals.ghCodeBlocks[num].codeblock;\n        }\n      }\n      blockText = blockText.replace(/\\$/g, '$$$$'); // Escape any dollar signs\n\n      grafsOutIt = grafsOutIt.replace(/(\\n\\n)?¨(K|G)\\d+\\2(\\n\\n)?/, blockText);\n      // Check if grafsOutIt is a pre->code\n      if (/^<pre\\b[^>]*>\\s*<code\\b[^>]*>/.test(grafsOutIt)) {\n        codeFlag = true;\n      }\n    }\n    grafsOut[i] = grafsOutIt;\n  }\n  text = grafsOut.join('\\n');\n  // Strip leading and trailing lines:\n  text = text.replace(/^\\n+/g, '');\n  text = text.replace(/\\n+$/g, '');\n  return globals.converter._dispatch('paragraphs.after', text, options, globals);\n});\n", "/**\n * Run extension\n */\nshowdown.subParser('runExtension', function (ext, text, options, globals) {\n  'use strict';\n\n  if (ext.filter) {\n    text = ext.filter(text, globals.converter, options);\n\n  } else if (ext.regex) {\n    // TODO remove this when old extension loading mechanism is deprecated\n    var re = ext.regex;\n    if (!(re instanceof RegExp)) {\n      re = new RegExp(re, 'g');\n    }\n    text = text.replace(re, ext.replace);\n  }\n\n  return text;\n});\n", "/**\n * These are all the transformations that occur *within* block-level\n * tags like paragraphs, headers, and list items.\n */\nshowdown.subParser('spanGamut', function (text, options, globals) {\n  'use strict';\n\n  text = globals.converter._dispatch('spanGamut.before', text, options, globals);\n  text = showdown.subParser('codeSpans')(text, options, globals);\n  text = showdown.subParser('escapeSpecialCharsWithinTagAttributes')(text, options, globals);\n  text = showdown.subParser('encodeBackslashEscapes')(text, options, globals);\n\n  // Process anchor and image tags. Images must come first,\n  // because ![foo][f] looks like an anchor.\n  text = showdown.subParser('images')(text, options, globals);\n  text = showdown.subParser('anchors')(text, options, globals);\n\n  // Make links out of things like `<http://example.com/>`\n  // Must come after anchors, because you can use < and >\n  // delimiters in inline links like [this](<url>).\n  text = showdown.subParser('autoLinks')(text, options, globals);\n  text = showdown.subParser('simplifiedAutoLinks')(text, options, globals);\n  text = showdown.subParser('emoji')(text, options, globals);\n  text = showdown.subParser('underline')(text, options, globals);\n  text = showdown.subParser('italicsAndBold')(text, options, globals);\n  text = showdown.subParser('strikethrough')(text, options, globals);\n  text = showdown.subParser('ellipsis')(text, options, globals);\n\n  // we need to hash HTML tags inside spans\n  text = showdown.subParser('hashHTMLSpans')(text, options, globals);\n\n  // now we encode amps and angles\n  text = showdown.subParser('encodeAmpsAndAngles')(text, options, globals);\n\n  // Do hard breaks\n  if (options.simpleLineBreaks) {\n    // GFM style hard breaks\n    // only add line breaks if the text does not contain a block (special case for lists)\n    if (!/\\n\\n¨K/.test(text)) {\n      text = text.replace(/\\n+/g, '<br />\\n');\n    }\n  } else {\n    // Vanilla hard breaks\n    text = text.replace(/  +\\n/g, '<br />\\n');\n  }\n\n  text = globals.converter._dispatch('spanGamut.after', text, options, globals);\n  return text;\n});\n", "showdown.subParser('strikethrough', function (text, options, globals) {\n  'use strict';\n\n  function parseInside (txt) {\n    if (options.simplifiedAutoLink) {\n      txt = showdown.subParser('simplifiedAutoLinks')(txt, options, globals);\n    }\n    return '<del>' + txt + '</del>';\n  }\n\n  if (options.strikethrough) {\n    text = globals.converter._dispatch('strikethrough.before', text, options, globals);\n    text = text.replace(/(?:~){2}([\\s\\S]+?)(?:~){2}/g, function (wm, txt) { return parseInside(txt); });\n    text = globals.converter._dispatch('strikethrough.after', text, options, globals);\n  }\n\n  return text;\n});\n", "/**\n * Strips link definitions from text, stores the URLs and titles in\n * hash references.\n * Link defs are in the form: ^[id]: url \"optional title\"\n */\nshowdown.subParser('stripLinkDefinitions', function (text, options, globals) {\n  'use strict';\n\n  var regex       = /^ {0,3}\\[([^\\]]+)]:[ \\t]*\\n?[ \\t]*<?([^>\\s]+)>?(?: =([*\\d]+[A-Za-z%]{0,4})x([*\\d]+[A-Za-z%]{0,4}))?[ \\t]*\\n?[ \\t]*(?:(\\n*)[\"|'(](.+?)[\"|')][ \\t]*)?(?:\\n+|(?=¨0))/gm,\n      base64Regex = /^ {0,3}\\[([^\\]]+)]:[ \\t]*\\n?[ \\t]*<?(data:.+?\\/.+?;base64,[A-Za-z0-9+/=\\n]+?)>?(?: =([*\\d]+[A-Za-z%]{0,4})x([*\\d]+[A-Za-z%]{0,4}))?[ \\t]*\\n?[ \\t]*(?:(\\n*)[\"|'(](.+?)[\"|')][ \\t]*)?(?:\\n\\n|(?=¨0)|(?=\\n\\[))/gm;\n\n  // attacklab: sentinel workarounds for lack of \\A and \\Z, safari\\khtml bug\n  text += '¨0';\n\n  var replaceFunc = function (wholeMatch, linkId, url, width, height, blankLines, title) {\n\n    // if there aren't two instances of linkId it must not be a reference link so back out\n    linkId = linkId.toLowerCase();\n    if (text.toLowerCase().split(linkId).length - 1 < 2) {\n      return wholeMatch;\n    }\n    if (url.match(/^data:.+?\\/.+?;base64,/)) {\n      // remove newlines\n      globals.gUrls[linkId] = url.replace(/\\s/g, '');\n    } else {\n      globals.gUrls[linkId] = showdown.subParser('encodeAmpsAndAngles')(url, options, globals);  // Link IDs are case-insensitive\n    }\n\n    if (blankLines) {\n      // Oops, found blank lines, so it's not a title.\n      // Put back the parenthetical statement we stole.\n      return blankLines + title;\n\n    } else {\n      if (title) {\n        globals.gTitles[linkId] = title.replace(/\"|'/g, '&quot;');\n      }\n      if (options.parseImgDimensions && width && height) {\n        globals.gDimensions[linkId] = {\n          width:  width,\n          height: height\n        };\n      }\n    }\n    // Completely remove the definition from the text\n    return '';\n  };\n\n  // first we try to find base64 link references\n  text = text.replace(base64Regex, replaceFunc);\n\n  text = text.replace(regex, replaceFunc);\n\n  // attacklab: strip sentinel\n  text = text.replace(/¨0/, '');\n\n  return text;\n});\n", "showdown.subParser('tables', function (text, options, globals) {\n  'use strict';\n\n  if (!options.tables) {\n    return text;\n  }\n\n  var tableRgx       = /^ {0,3}\\|?.+\\|.+\\n {0,3}\\|?[ \\t]*:?[ \\t]*(?:[-=]){2,}[ \\t]*:?[ \\t]*\\|[ \\t]*:?[ \\t]*(?:[-=]){2,}[\\s\\S]+?(?:\\n\\n|¨0)/gm,\n      //singeColTblRgx = /^ {0,3}\\|.+\\|\\n {0,3}\\|[ \\t]*:?[ \\t]*(?:[-=]){2,}[ \\t]*:?[ \\t]*\\|[ \\t]*\\n(?: {0,3}\\|.+\\|\\n)+(?:\\n\\n|¨0)/gm;\n      singeColTblRgx = /^ {0,3}\\|.+\\|[ \\t]*\\n {0,3}\\|[ \\t]*:?[ \\t]*(?:[-=]){2,}[ \\t]*:?[ \\t]*\\|[ \\t]*\\n( {0,3}\\|.+\\|[ \\t]*\\n)*(?:\\n|¨0)/gm;\n\n  function parseStyles (sLine) {\n    if (/^:[ \\t]*--*$/.test(sLine)) {\n      return ' style=\"text-align:left;\"';\n    } else if (/^--*[ \\t]*:[ \\t]*$/.test(sLine)) {\n      return ' style=\"text-align:right;\"';\n    } else if (/^:[ \\t]*--*[ \\t]*:$/.test(sLine)) {\n      return ' style=\"text-align:center;\"';\n    } else {\n      return '';\n    }\n  }\n\n  function parseHeaders (header, style) {\n    var id = '';\n    header = header.trim();\n    // support both tablesHeaderId and tableHeaderId due to error in documentation so we don't break backwards compatibility\n    if (options.tablesHeaderId || options.tableHeaderId) {\n      id = ' id=\"' + header.replace(/ /g, '_').toLowerCase() + '\"';\n    }\n    header = showdown.subParser('spanGamut')(header, options, globals);\n\n    return '<th' + id + style + '>' + header + '</th>\\n';\n  }\n\n  function parseCells (cell, style) {\n    var subText = showdown.subParser('spanGamut')(cell, options, globals);\n    return '<td' + style + '>' + subText + '</td>\\n';\n  }\n\n  function buildTable (headers, cells) {\n    var tb = '<table>\\n<thead>\\n<tr>\\n',\n        tblLgn = headers.length;\n\n    for (var i = 0; i < tblLgn; ++i) {\n      tb += headers[i];\n    }\n    tb += '</tr>\\n</thead>\\n<tbody>\\n';\n\n    for (i = 0; i < cells.length; ++i) {\n      tb += '<tr>\\n';\n      for (var ii = 0; ii < tblLgn; ++ii) {\n        tb += cells[i][ii];\n      }\n      tb += '</tr>\\n';\n    }\n    tb += '</tbody>\\n</table>\\n';\n    return tb;\n  }\n\n  function parseTable (rawTable) {\n    var i, tableLines = rawTable.split('\\n');\n\n    for (i = 0; i < tableLines.length; ++i) {\n      // strip wrong first and last column if wrapped tables are used\n      if (/^ {0,3}\\|/.test(tableLines[i])) {\n        tableLines[i] = tableLines[i].replace(/^ {0,3}\\|/, '');\n      }\n      if (/\\|[ \\t]*$/.test(tableLines[i])) {\n        tableLines[i] = tableLines[i].replace(/\\|[ \\t]*$/, '');\n      }\n      // parse code spans first, but we only support one line code spans\n      tableLines[i] = showdown.subParser('codeSpans')(tableLines[i], options, globals);\n    }\n\n    var rawHeaders = tableLines[0].split('|').map(function (s) { return s.trim();}),\n        rawStyles = tableLines[1].split('|').map(function (s) { return s.trim();}),\n        rawCells = [],\n        headers = [],\n        styles = [],\n        cells = [];\n\n    tableLines.shift();\n    tableLines.shift();\n\n    for (i = 0; i < tableLines.length; ++i) {\n      if (tableLines[i].trim() === '') {\n        continue;\n      }\n      rawCells.push(\n        tableLines[i]\n          .split('|')\n          .map(function (s) {\n            return s.trim();\n          })\n      );\n    }\n\n    if (rawHeaders.length < rawStyles.length) {\n      return rawTable;\n    }\n\n    for (i = 0; i < rawStyles.length; ++i) {\n      styles.push(parseStyles(rawStyles[i]));\n    }\n\n    for (i = 0; i < rawHeaders.length; ++i) {\n      if (showdown.helper.isUndefined(styles[i])) {\n        styles[i] = '';\n      }\n      headers.push(parseHeaders(rawHeaders[i], styles[i]));\n    }\n\n    for (i = 0; i < rawCells.length; ++i) {\n      var row = [];\n      for (var ii = 0; ii < headers.length; ++ii) {\n        if (showdown.helper.isUndefined(rawCells[i][ii])) {\n\n        }\n        row.push(parseCells(rawCells[i][ii], styles[ii]));\n      }\n      cells.push(row);\n    }\n\n    return buildTable(headers, cells);\n  }\n\n  text = globals.converter._dispatch('tables.before', text, options, globals);\n\n  // find escaped pipe characters\n  text = text.replace(/\\\\(\\|)/g, showdown.helper.escapeCharactersCallback);\n\n  // parse multi column tables\n  text = text.replace(tableRgx, parseTable);\n\n  // parse one column tables\n  text = text.replace(singeColTblRgx, parseTable);\n\n  text = globals.converter._dispatch('tables.after', text, options, globals);\n\n  return text;\n});\n", "showdown.subParser('underline', function (text, options, globals) {\n  'use strict';\n\n  if (!options.underline) {\n    return text;\n  }\n\n  text = globals.converter._dispatch('underline.before', text, options, globals);\n\n  if (options.literalMidWordUnderscores) {\n    text = text.replace(/\\b___(\\S[\\s\\S]*?)___\\b/g, function (wm, txt) {\n      return '<u>' + txt + '</u>';\n    });\n    text = text.replace(/\\b__(\\S[\\s\\S]*?)__\\b/g, function (wm, txt) {\n      return '<u>' + txt + '</u>';\n    });\n  } else {\n    text = text.replace(/___(\\S[\\s\\S]*?)___/g, function (wm, m) {\n      return (/\\S$/.test(m)) ? '<u>' + m + '</u>' : wm;\n    });\n    text = text.replace(/__(\\S[\\s\\S]*?)__/g, function (wm, m) {\n      return (/\\S$/.test(m)) ? '<u>' + m + '</u>' : wm;\n    });\n  }\n\n  // escape remaining underscores to prevent them being parsed by italic and bold\n  text = text.replace(/(_)/g, showdown.helper.escapeCharactersCallback);\n\n  text = globals.converter._dispatch('underline.after', text, options, globals);\n\n  return text;\n});\n", "/**\n * Swap back in all the special characters we've hidden.\n */\nshowdown.subParser('unescapeSpecialChars', function (text, options, globals) {\n  'use strict';\n  text = globals.converter._dispatch('unescapeSpecialChars.before', text, options, globals);\n\n  text = text.replace(/¨E(\\d+)E/g, function (wholeMatch, m1) {\n    var charCodeToReplace = parseInt(m1);\n    return String.fromCharCode(charCodeToReplace);\n  });\n\n  text = globals.converter._dispatch('unescapeSpecialChars.after', text, options, globals);\n  return text;\n});\n", "showdown.subParser('makeMarkdown.blockquote', function (node, globals) {\n  'use strict';\n\n  var txt = '';\n  if (node.hasChildNodes()) {\n    var children = node.childNodes,\n        childrenLength = children.length;\n\n    for (var i = 0; i < childrenLength; ++i) {\n      var innerTxt = showdown.subParser('makeMarkdown.node')(children[i], globals);\n\n      if (innerTxt === '') {\n        continue;\n      }\n      txt += innerTxt;\n    }\n  }\n  // cleanup\n  txt = txt.trim();\n  txt = '> ' + txt.split('\\n').join('\\n> ');\n  return txt;\n});\n", "showdown.subParser('makeMarkdown.codeBlock', function (node, globals) {\n  'use strict';\n\n  var lang = node.getAttribute('language'),\n      num  = node.getAttribute('precodenum');\n  return '```' + lang + '\\n' + globals.preList[num] + '\\n```';\n});\n", "showdown.subParser('makeMarkdown.codeSpan', function (node) {\n  'use strict';\n\n  return '`' + node.innerHTML + '`';\n});\n", "showdown.subParser('makeMarkdown.emphasis', function (node, globals) {\n  'use strict';\n\n  var txt = '';\n  if (node.hasChildNodes()) {\n    txt += '*';\n    var children = node.childNodes,\n        childrenLength = children.length;\n    for (var i = 0; i < childrenLength; ++i) {\n      txt += showdown.subParser('makeMarkdown.node')(children[i], globals);\n    }\n    txt += '*';\n  }\n  return txt;\n});\n", "showdown.subParser('makeMarkdown.header', function (node, globals, headerLevel) {\n  'use strict';\n\n  var headerMark = new Array(headerLevel + 1).join('#'),\n      txt = '';\n\n  if (node.hasChildNodes()) {\n    txt = headerMark + ' ';\n    var children = node.childNodes,\n        childrenLength = children.length;\n\n    for (var i = 0; i < childrenLength; ++i) {\n      txt += showdown.subParser('makeMarkdown.node')(children[i], globals);\n    }\n  }\n  return txt;\n});\n", "showdown.subParser('makeMarkdown.hr', function () {\n  'use strict';\n\n  return '---';\n});\n", "showdown.subParser('makeMarkdown.image', function (node) {\n  'use strict';\n\n  var txt = '';\n  if (node.hasAttribute('src')) {\n    txt += '![' + node.getAttribute('alt') + '](';\n    txt += '<' + node.getAttribute('src') + '>';\n    if (node.hasAttribute('width') && node.hasAttribute('height')) {\n      txt += ' =' + node.getAttribute('width') + 'x' + node.getAttribute('height');\n    }\n\n    if (node.hasAttribute('title')) {\n      txt += ' \"' + node.getAttribute('title') + '\"';\n    }\n    txt += ')';\n  }\n  return txt;\n});\n", "showdown.subParser('makeMarkdown.links', function (node, globals) {\n  'use strict';\n\n  var txt = '';\n  if (node.hasChildNodes() && node.hasAttribute('href')) {\n    var children = node.childNodes,\n        childrenLength = children.length;\n    txt = '[';\n    for (var i = 0; i < childrenLength; ++i) {\n      txt += showdown.subParser('makeMarkdown.node')(children[i], globals);\n    }\n    txt += '](';\n    txt += '<' + node.getAttribute('href') + '>';\n    if (node.hasAttribute('title')) {\n      txt += ' \"' + node.getAttribute('title') + '\"';\n    }\n    txt += ')';\n  }\n  return txt;\n});\n", "showdown.subParser('makeMarkdown.list', function (node, globals, type) {\n  'use strict';\n\n  var txt = '';\n  if (!node.hasChildNodes()) {\n    return '';\n  }\n  var listItems       = node.childNodes,\n      listItemsLenght = listItems.length,\n      listNum = node.getAttribute('start') || 1;\n\n  for (var i = 0; i < listItemsLenght; ++i) {\n    if (typeof listItems[i].tagName === 'undefined' || listItems[i].tagName.toLowerCase() !== 'li') {\n      continue;\n    }\n\n    // define the bullet to use in list\n    var bullet = '';\n    if (type === 'ol') {\n      bullet = listNum.toString() + '. ';\n    } else {\n      bullet = '- ';\n    }\n\n    // parse list item\n    txt += bullet + showdown.subParser('makeMarkdown.listItem')(listItems[i], globals);\n    ++listNum;\n  }\n\n  // add comment at the end to prevent consecutive lists to be parsed as one\n  txt += '\\n<!-- -->\\n';\n  return txt.trim();\n});\n", "showdown.subParser('makeMarkdown.listItem', function (node, globals) {\n  'use strict';\n\n  var listItemTxt = '';\n\n  var children = node.childNodes,\n      childrenLenght = children.length;\n\n  for (var i = 0; i < childrenLenght; ++i) {\n    listItemTxt += showdown.subParser('makeMarkdown.node')(children[i], globals);\n  }\n  // if it's only one liner, we need to add a newline at the end\n  if (!/\\n$/.test(listItemTxt)) {\n    listItemTxt += '\\n';\n  } else {\n    // it's multiparagraph, so we need to indent\n    listItemTxt = listItemTxt\n      .split('\\n')\n      .join('\\n    ')\n      .replace(/^ {4}$/gm, '')\n      .replace(/\\n\\n+/g, '\\n\\n');\n  }\n\n  return listItemTxt;\n});\n", "\n\nshowdown.subParser('makeMarkdown.node', function (node, globals, spansOnly) {\n  'use strict';\n\n  spansOnly = spansOnly || false;\n\n  var txt = '';\n\n  // edge case of text without wrapper paragraph\n  if (node.nodeType === 3) {\n    return showdown.subParser('makeMarkdown.txt')(node, globals);\n  }\n\n  // HTML comment\n  if (node.nodeType === 8) {\n    return '<!--' + node.data + '-->\\n\\n';\n  }\n\n  // process only node elements\n  if (node.nodeType !== 1) {\n    return '';\n  }\n\n  var tagName = node.tagName.toLowerCase();\n\n  switch (tagName) {\n\n    //\n    // BLOCKS\n    //\n    case 'h1':\n      if (!spansOnly) { txt = showdown.subParser('makeMarkdown.header')(node, globals, 1) + '\\n\\n'; }\n      break;\n    case 'h2':\n      if (!spansOnly) { txt = showdown.subParser('makeMarkdown.header')(node, globals, 2) + '\\n\\n'; }\n      break;\n    case 'h3':\n      if (!spansOnly) { txt = showdown.subParser('makeMarkdown.header')(node, globals, 3) + '\\n\\n'; }\n      break;\n    case 'h4':\n      if (!spansOnly) { txt = showdown.subParser('makeMarkdown.header')(node, globals, 4) + '\\n\\n'; }\n      break;\n    case 'h5':\n      if (!spansOnly) { txt = showdown.subParser('makeMarkdown.header')(node, globals, 5) + '\\n\\n'; }\n      break;\n    case 'h6':\n      if (!spansOnly) { txt = showdown.subParser('makeMarkdown.header')(node, globals, 6) + '\\n\\n'; }\n      break;\n\n    case 'p':\n      if (!spansOnly) { txt = showdown.subParser('makeMarkdown.paragraph')(node, globals) + '\\n\\n'; }\n      break;\n\n    case 'blockquote':\n      if (!spansOnly) { txt = showdown.subParser('makeMarkdown.blockquote')(node, globals) + '\\n\\n'; }\n      break;\n\n    case 'hr':\n      if (!spansOnly) { txt = showdown.subParser('makeMarkdown.hr')(node, globals) + '\\n\\n'; }\n      break;\n\n    case 'ol':\n      if (!spansOnly) { txt = showdown.subParser('makeMarkdown.list')(node, globals, 'ol') + '\\n\\n'; }\n      break;\n\n    case 'ul':\n      if (!spansOnly) { txt = showdown.subParser('makeMarkdown.list')(node, globals, 'ul') + '\\n\\n'; }\n      break;\n\n    case 'precode':\n      if (!spansOnly) { txt = showdown.subParser('makeMarkdown.codeBlock')(node, globals) + '\\n\\n'; }\n      break;\n\n    case 'pre':\n      if (!spansOnly) { txt = showdown.subParser('makeMarkdown.pre')(node, globals) + '\\n\\n'; }\n      break;\n\n    case 'table':\n      if (!spansOnly) { txt = showdown.subParser('makeMarkdown.table')(node, globals) + '\\n\\n'; }\n      break;\n\n    //\n    // SPANS\n    //\n    case 'code':\n      txt = showdown.subParser('makeMarkdown.codeSpan')(node, globals);\n      break;\n\n    case 'em':\n    case 'i':\n      txt = showdown.subParser('makeMarkdown.emphasis')(node, globals);\n      break;\n\n    case 'strong':\n    case 'b':\n      txt = showdown.subParser('makeMarkdown.strong')(node, globals);\n      break;\n\n    case 'del':\n      txt = showdown.subParser('makeMarkdown.strikethrough')(node, globals);\n      break;\n\n    case 'a':\n      txt = showdown.subParser('makeMarkdown.links')(node, globals);\n      break;\n\n    case 'img':\n      txt = showdown.subParser('makeMarkdown.image')(node, globals);\n      break;\n\n    default:\n      txt = node.outerHTML + '\\n\\n';\n  }\n\n  // common normalization\n  // TODO eventually\n\n  return txt;\n});\n", "showdown.subParser('makeMarkdown.paragraph', function (node, globals) {\n  'use strict';\n\n  var txt = '';\n  if (node.hasChildNodes()) {\n    var children = node.childNodes,\n        childrenLength = children.length;\n    for (var i = 0; i < childrenLength; ++i) {\n      txt += showdown.subParser('makeMarkdown.node')(children[i], globals);\n    }\n  }\n\n  // some text normalization\n  txt = txt.trim();\n\n  return txt;\n});\n", "showdown.subParser('makeMarkdown.pre', function (node, globals) {\n  'use strict';\n\n  var num  = node.getAttribute('prenum');\n  return '<pre>' + globals.preList[num] + '</pre>';\n});\n", "showdown.subParser('makeMarkdown.strikethrough', function (node, globals) {\n  'use strict';\n\n  var txt = '';\n  if (node.hasChildNodes()) {\n    txt += '~~';\n    var children = node.childNodes,\n        childrenLength = children.length;\n    for (var i = 0; i < childrenLength; ++i) {\n      txt += showdown.subParser('makeMarkdown.node')(children[i], globals);\n    }\n    txt += '~~';\n  }\n  return txt;\n});\n", "showdown.subParser('makeMarkdown.strong', function (node, globals) {\n  'use strict';\n\n  var txt = '';\n  if (node.hasChildNodes()) {\n    txt += '**';\n    var children = node.childNodes,\n        childrenLength = children.length;\n    for (var i = 0; i < childrenLength; ++i) {\n      txt += showdown.subParser('makeMarkdown.node')(children[i], globals);\n    }\n    txt += '**';\n  }\n  return txt;\n});\n", "showdown.subParser('makeMarkdown.table', function (node, globals) {\n  'use strict';\n\n  var txt = '',\n      tableArray = [[], []],\n      headings   = node.querySelectorAll('thead>tr>th'),\n      rows       = node.querySelectorAll('tbody>tr'),\n      i, ii;\n  for (i = 0; i < headings.length; ++i) {\n    var headContent = showdown.subParser('makeMarkdown.tableCell')(headings[i], globals),\n        allign = '---';\n\n    if (headings[i].hasAttribute('style')) {\n      var style = headings[i].getAttribute('style').toLowerCase().replace(/\\s/g, '');\n      switch (style) {\n        case 'text-align:left;':\n          allign = ':---';\n          break;\n        case 'text-align:right;':\n          allign = '---:';\n          break;\n        case 'text-align:center;':\n          allign = ':---:';\n          break;\n      }\n    }\n    tableArray[0][i] = headContent.trim();\n    tableArray[1][i] = allign;\n  }\n\n  for (i = 0; i < rows.length; ++i) {\n    var r = tableArray.push([]) - 1,\n        cols = rows[i].getElementsByTagName('td');\n\n    for (ii = 0; ii < headings.length; ++ii) {\n      var cellContent = ' ';\n      if (typeof cols[ii] !== 'undefined') {\n        cellContent = showdown.subParser('makeMarkdown.tableCell')(cols[ii], globals);\n      }\n      tableArray[r].push(cellContent);\n    }\n  }\n\n  var cellSpacesCount = 3;\n  for (i = 0; i < tableArray.length; ++i) {\n    for (ii = 0; ii < tableArray[i].length; ++ii) {\n      var strLen = tableArray[i][ii].length;\n      if (strLen > cellSpacesCount) {\n        cellSpacesCount = strLen;\n      }\n    }\n  }\n\n  for (i = 0; i < tableArray.length; ++i) {\n    for (ii = 0; ii < tableArray[i].length; ++ii) {\n      if (i === 1) {\n        if (tableArray[i][ii].slice(-1) === ':') {\n          tableArray[i][ii] = showdown.helper.padEnd(tableArray[i][ii].slice(-1), cellSpacesCount - 1, '-') + ':';\n        } else {\n          tableArray[i][ii] = showdown.helper.padEnd(tableArray[i][ii], cellSpacesCount, '-');\n        }\n      } else {\n        tableArray[i][ii] = showdown.helper.padEnd(tableArray[i][ii], cellSpacesCount);\n      }\n    }\n    txt += '| ' + tableArray[i].join(' | ') + ' |\\n';\n  }\n\n  return txt.trim();\n});\n", "showdown.subParser('makeMarkdown.tableCell', function (node, globals) {\n  'use strict';\n\n  var txt = '';\n  if (!node.hasChildNodes()) {\n    return '';\n  }\n  var children = node.childNodes,\n      childrenLength = children.length;\n\n  for (var i = 0; i < childrenLength; ++i) {\n    txt += showdown.subParser('makeMarkdown.node')(children[i], globals, true);\n  }\n  return txt.trim();\n});\n", "showdown.subParser('makeMarkdown.txt', function (node) {\n  'use strict';\n\n  var txt = node.nodeValue;\n\n  // multiple spaces are collapsed\n  txt = txt.replace(/ +/g, ' ');\n\n  // replace the custom ¨NBSP; with a space\n  txt = txt.replace(/¨NBSP;/g, ' ');\n\n  // \", <, > and & should replace escaped html entities\n  txt = showdown.helper.unescapeHTMLEntities(txt);\n\n  // escape markdown magic characters\n  // emphasis, strong and strikethrough - can appear everywhere\n  // we also escape pipe (|) because of tables\n  // and escape ` because of code blocks and spans\n  txt = txt.replace(/([*_~|`])/g, '\\\\$1');\n\n  // escape > because of blockquotes\n  txt = txt.replace(/^(\\s*)>/g, '\\\\$1>');\n\n  // hash character, only troublesome at the beginning of a line because of headers\n  txt = txt.replace(/^#/gm, '\\\\#');\n\n  // horizontal rules\n  txt = txt.replace(/^(\\s*)([-=]{3,})(\\s*)$/, '$1\\\\$2$3');\n\n  // dot, because of ordered lists, only troublesome at the beginning of a line when preceded by an integer\n  txt = txt.replace(/^( {0,3}\\d+)\\./gm, '$1\\\\.');\n\n  // +, * and -, at the beginning of a line becomes a list, so we need to escape them also (asterisk was already escaped)\n  txt = txt.replace(/^( {0,3})([+-])/gm, '$1\\\\$2');\n\n  // images and links, ] followed by ( is problematic, so we escape it\n  txt = txt.replace(/]([\\s]*)\\(/g, '\\\\]$1\\\\(');\n\n  // reference URIs must also be escaped\n  txt = txt.replace(/^ {0,3}\\[([\\S \\t]*?)]:/gm, '\\\\[$1]:');\n\n  return txt;\n});\n", "var root = this;\n\n// AMD Loader\nif (typeof define === 'function' && define.amd) {\n  define(function () {\n    'use strict';\n    return showdown;\n  });\n\n// CommonJS/nodeJS Loader\n} else if (typeof module !== 'undefined' && module.exports) {\n  module.exports = showdown;\n\n// Regular Browser loader\n} else {\n  root.showdown = showdown;\n}\n"], "mappings": ";;;;;;;;AAIA,eAAS,eAAgB,QAAQ;AAC/B;AAEA,YAAI,iBAAiB;UACnB,yBAAyB;YACvB,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,YAAY;YACV,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,gBAAgB;YACd,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,mBAAmB;YACjB,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,sBAAsB;YACpB,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,aAAa;YACX,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,kBAAkB;YAChB,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,oBAAoB;YAClB,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,oBAAoB;YAClB,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,oCAAoC;YAClC,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,2BAA2B;YACzB,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,yBAAyB;YACvB,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,eAAe;YACb,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,QAAQ;YACN,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,gBAAgB;YACd,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,cAAc;YACZ,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,WAAW;YACT,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,mBAAmB;YACjB,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,qBAAqB;YACnB,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,sCAAsC;YACpC,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,kBAAkB;YAChB,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,+BAA+B;YAC7B,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,YAAY;YACV,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,gBAAgB;YACd,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,cAAc;YACZ,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,sBAAsB;YACpB,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,0BAA0B;YACxB,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,OAAO;YACL,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,WAAW;YACT,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,UAAU;YACR,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,sBAAsB;YACpB,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,UAAU;YACR,cAAc;YACd,UAAU;YACV,MAAM;UACR;UACA,0BAA0B;YACxB,cAAc;YACd,UAAU;YACV,MAAM;UACR;QACF;AACA,YAAI,WAAW,OAAO;AACpB,iBAAO,KAAK,MAAM,KAAK,UAAU,cAAc,CAAA;QACjD;AACA,YAAI,MAAM,CAAA;AACV,iBAAS,OAAO,gBAAgB;AAC9B,cAAI,eAAe,eAAe,GAAG,GAAG;AACtC,gBAAI,GAAG,IAAI,eAAe,GAAG,EAAE;UACjC;QACF;AACA,eAAO;MACT;AAEA,eAAS,eAAgB;AACvB;AACA,YAAI,UAAU,eAAe,IAAI,GAC7B,MAAM,CAAA;AACV,iBAAS,OAAO,SAAS;AACvB,cAAI,QAAQ,eAAe,GAAG,GAAG;AAC/B,gBAAI,GAAG,IAAI;UACb;QACF;AACA,eAAO;MACT;AC/LA,UAAI,WAAW,CAAA,GACX,UAAU,CAAA,GACV,aAAa,CAAA,GACb,gBAAgB,eAAe,IAAI,GACnC,YAAY,WACZ,SAAS;QACP,QAAQ;UACN,yBAAsC;UACtC,oBAAsC;UACtC,oCAAsC;UACtC,2BAAsC;UACtC,eAAsC;UACtC,QAAsC;UACtC,gBAAsC;UACtC,cAAsC;UACtC,WAAsC;UACtC,sCAAsC;UACtC,kBAAsC;UACtC,+BAAsC;UACtC,sBAAsC;UACtC,YAAsC;UACtC,0BAAsC;UACtC,OAAsC;UACtC,0BAAsC;QACxC;QACA,UAAU;UACR,YAAsC;UACtC,cAAsC;QACxC;QACA,OAAO;UACL,yBAAsC;UACtC,oBAAsC;UACtC,oBAAsC;UACtC,oCAAsC;UACtC,2BAAsC;UACtC,eAAsC;UACtC,QAAsC;UACtC,gBAAsC;UACtC,cAAsC;UACtC,WAAsC;UACtC,mBAAsC;UACtC,kBAAsC;UACtC,+BAAsC;UACtC,YAAsC;UACtC,cAAsC;QACxC;QACA,SAAS,eAAe,IAAI;QAC5B,OAAO,aAAY;MACrB;AAMJ,eAAS,SAAS,CAAA;AAMlB,eAAS,aAAa,CAAA;AAStB,eAAS,YAAY,SAAU,KAAK,OAAO;AACzC;AACA,sBAAc,GAAG,IAAI;AACrB,eAAO;MACT;AAQA,eAAS,YAAY,SAAU,KAAK;AAClC;AACA,eAAO,cAAc,GAAG;MAC1B;AAOA,eAAS,aAAa,WAAY;AAChC;AACA,eAAO;MACT;AAMA,eAAS,eAAe,WAAY;AAClC;AACA,wBAAgB,eAAe,IAAI;MACrC;AAMA,eAAS,YAAY,SAAU,MAAM;AACnC;AACA,YAAG,CAAE,OAAO,eAAe,IAAI,GAAG;AAChC,gBAAM,MAAM,OAAO,uBAAsB;QAC3C;AACA,iBAAS,aAAY;AACrB,YAAI,SAAS,OAAO,IAAI;AACxB,oBAAY;AACZ,iBAAS,UAAU,QAAQ;AACzB,cAAI,OAAO,eAAe,MAAM,GAAG;AACjC,0BAAc,MAAM,IAAI,OAAO,MAAM;UACvC;QACF;MACF;AAMA,eAAS,YAAY,WAAY;AAC/B;AACA,eAAO;MACT;AAOA,eAAS,mBAAmB,SAAU,MAAM;AAC1C;AACA,YAAI,OAAO,eAAe,IAAI,GAAG;AAC/B,iBAAO,OAAO,IAAI;QACpB;MACF;AAQA,eAAS,oBAAoB,SAAU,QAAQ;AAC7C;AACA,eAAO,eAAe,MAAM;MAC9B;AAYA,eAAS,YAAY,SAAU,MAAM,MAAM;AACzC;AACA,YAAI,SAAS,OAAO,SAAS,IAAI,GAAG;AAClC,cAAI,OAAO,SAAS,aAAa;AAC/B,oBAAQ,IAAI,IAAI;UAClB,OAAO;AACL,gBAAI,QAAQ,eAAe,IAAI,GAAG;AAChC,qBAAO,QAAQ,IAAI;YACrB,OAAO;AACL,oBAAM,MAAK,qBAAsB,OAAO,kBAAgB;YAC1D;UACF;QACF;MACF;AASA,eAAS,YAAY,SAAU,MAAM,KAAK;AACxC;AAEA,YAAG,CAAE,SAAS,OAAO,SAAS,IAAI,GAAG;AACnC,gBAAM,MAAK,mCAAqC;QAClD;AAEA,eAAO,SAAS,OAAO,WAAW,IAAI;AAGtC,YAAI,SAAS,OAAO,YAAY,GAAG,GAAG;AACpC,cAAG,CAAE,WAAW,eAAe,IAAI,GAAG;AACpC,kBAAM,MAAK,qBAAsB,OAAO,qBAAmB;UAC7D;AACA,iBAAO,WAAW,IAAI;QAGxB,OAAO;AAEL,cAAI,OAAO,QAAQ,YAAY;AAC7B,kBAAM,IAAG;UACX;AAGA,cAAG,CAAE,SAAS,OAAO,QAAQ,GAAG,GAAG;AACjC,kBAAM,CAAC,GAAG;UACZ;AAEA,cAAI,iBAAiB,SAAS,KAAK,IAAI;AAEvC,cAAI,eAAe,OAAO;AACxB,uBAAW,IAAI,IAAI;UACrB,OAAO;AACL,kBAAM,MAAM,eAAe,KAAK;UAClC;QACF;MACF;AAMA,eAAS,mBAAmB,WAAY;AACtC;AACA,eAAO;MACT;AAMA,eAAS,kBAAkB,SAAU,MAAM;AACzC;AACA,eAAO,WAAW,IAAI;MACxB;AAKA,eAAS,kBAAkB,WAAY;AACrC;AACA,qBAAa,CAAA;MACf;AAQA,eAAS,SAAU,WAAW,MAAM;AAClC;AAEA,YAAI,SAAU,OAAQ,cAAc,OAAO,iBAAiB,8BACxD,MAAM;UACJ,OAAO;UACP,OAAO;QACT;AAEJ,YAAG,CAAE,SAAS,OAAO,QAAQ,SAAS,GAAG;AACvC,sBAAY,CAAC,SAAS;QACxB;AAEA,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,EAAE,GAAG;AACzC,cAAI,UAAU,SAAS,oBAAoB,IAAI,MAC3C,MAAM,UAAU,CAAC;AACrB,cAAI,OAAO,QAAQ,UAAU;AAC3B,gBAAI,QAAQ;AACZ,gBAAI,QAAQ,UAAU,4BAA4B,OAAO,MAAM;AAC/D,mBAAO;UACT;AAEA,cAAG,CAAE,SAAS,OAAO,SAAS,IAAI,IAAI,GAAG;AACvC,gBAAI,QAAQ;AACZ,gBAAI,QAAQ,UAAU,2CAA2C,OAAO,IAAI,OAAO;AACnF,mBAAO;UACT;AAEA,cAAI,OAAO,IAAI,OAAO,IAAI,KAAK,YAAW;AAG1C,cAAI,SAAS,YAAY;AACvB,mBAAO,IAAI,OAAO;UACpB;AAEA,cAAI,SAAS,QAAQ;AACnB,mBAAO,IAAI,OAAO;UACpB;AAEA,cAAI,SAAS,UAAU,SAAS,YAAY,SAAS,YAAY;AAC/D,gBAAI,QAAQ;AACZ,gBAAI,QAAQ,UAAU,UAAU,OAAO;AACvC,mBAAO;UACT;AAEA,cAAI,SAAS,YAAY;AACvB,gBAAI,SAAS,OAAO,YAAY,IAAI,SAAS,GAAG;AAC9C,kBAAI,QAAQ;AACZ,kBAAI,QAAQ,UAAU;AACtB,qBAAO;YACT;UACF,OAAO;AACL,gBAAI,SAAS,OAAO,YAAY,IAAI,MAAM,KAAK,SAAS,OAAO,YAAY,IAAI,KAAK,GAAG;AACrF,kBAAI,QAAQ;AACZ,kBAAI,QAAQ,UAAU,OAAO;AAC7B,qBAAO;YACT;UACF;AAEA,cAAI,IAAI,WAAW;AACjB,gBAAI,OAAO,IAAI,cAAc,UAAU;AACrC,kBAAI,QAAQ;AACZ,kBAAI,QAAQ,UAAU,gDAAgD,OAAO,IAAI,YAAY;AAC7F,qBAAO;YACT;AACA,qBAAS,MAAM,IAAI,WAAW;AAC5B,kBAAI,IAAI,UAAU,eAAe,EAAE,GAAG;AACpC,oBAAI,OAAO,IAAI,UAAU,EAAE,MAAM,YAAY;AAC3C,sBAAI,QAAQ;AACZ,sBAAI,QAAQ,UAAU,iFAAiF,KACrG,6BAA6B,OAAO,IAAI,UAAU,EAAE,IAAI;AAC1D,yBAAO;gBACT;cACF;YACF;UACF;AAEA,cAAI,IAAI,QAAQ;AACd,gBAAI,OAAO,IAAI,WAAW,YAAY;AACpC,kBAAI,QAAQ;AACZ,kBAAI,QAAQ,UAAU,sCAAsC,OAAO,IAAI,SAAS;AAChF,qBAAO;YACT;UACF,WAAW,IAAI,OAAO;AACpB,gBAAI,SAAS,OAAO,SAAS,IAAI,KAAK,GAAG;AACvC,kBAAI,QAAQ,IAAI,OAAO,IAAI,OAAO,GAAE;YACtC;AACA,gBAAG,EAAG,IAAI,iBAAiB,SAAS;AAClC,kBAAI,QAAQ;AACZ,kBAAI,QAAQ,UAAU,sEAAsE,OAAO,IAAI,QAAQ;AAC/G,qBAAO;YACT;AACA,gBAAI,SAAS,OAAO,YAAY,IAAI,OAAO,GAAG;AAC5C,kBAAI,QAAQ;AACZ,kBAAI,QAAQ,UAAU;AACtB,qBAAO;YACT;UACF;QACF;AACA,eAAO;MACT;AAOA,eAAS,oBAAoB,SAAU,KAAK;AAC1C;AAEA,YAAI,oBAAoB,SAAS,KAAK,IAAI;AAC1C,YAAG,CAAE,kBAAkB,OAAO;AAC5B,kBAAQ,KAAK,kBAAkB,KAAK;AACpC,iBAAO;QACT;AACA,eAAO;MACT;ACvXA,UAAG,CAAE,SAAS,eAAc,QAAQ,GAAI;AACtC,iBAAS,SAAS,CAAA;MACpB;AAQA,eAAS,OAAO,WAAW,SAAU,GAAG;AACtC;AACA,eAAQ,OAAO,MAAM,YAAY,aAAa;MAChD;AAQA,eAAS,OAAO,aAAa,SAAU,GAAG;AACxC;AACA,YAAI,UAAU,CAAA;AACd,eAAO,KAAK,QAAQ,SAAS,KAAK,CAAC,MAAM;MAC3C;AAQA,eAAS,OAAO,UAAU,SAAU,GAAG;AACrC;AACA,eAAO,MAAM,QAAQ,CAAC;MACxB;AAQA,eAAS,OAAO,cAAc,SAAU,OAAO;AAC7C;AACA,eAAO,OAAO,UAAU;MAC1B;AASA,eAAS,OAAO,UAAU,SAAU,KAAK,UAAU;AACjD;AAEA,YAAI,SAAS,OAAO,YAAY,GAAG,GAAG;AACpC,gBAAM,IAAI,MAAK,uBAAuB;QACxC;AAEA,YAAI,SAAS,OAAO,YAAY,QAAQ,GAAG;AACzC,gBAAM,IAAI,MAAK,4BAA4B;QAC7C;AAEA,YAAG,CAAE,SAAS,OAAO,WAAW,QAAQ,GAAG;AACzC,gBAAM,IAAI,MAAK,2CAA2C;QAC5D;AAEA,YAAI,OAAO,IAAI,YAAY,YAAY;AACrC,cAAI,QAAQ,QAAQ;QACtB,WAAW,SAAS,OAAO,QAAQ,GAAG,GAAG;AACvC,mBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,qBAAS,IAAI,CAAC,GAAG,GAAG,GAAG;UACzB;QACF,WAAW,OAAQ,QAAS,UAAU;AACpC,mBAAS,QAAQ,KAAK;AACpB,gBAAI,IAAI,eAAe,IAAI,GAAG;AAC5B,uBAAS,IAAI,IAAI,GAAG,MAAM,GAAG;YAC/B;UACF;QACF,OAAO;AACL,gBAAM,IAAI,MAAK,wDAAwD;QACzE;MACF;AAQA,eAAS,OAAO,aAAa,SAAU,GAAG;AACxC;AACA,eAAO,EAAE,QAAO,kBAAmB,EAAA,EAAI,QAAO,OAAQ,EAAA,EAAI,YAAW;MACvE;AAEA,eAAS,yBAA0B,YAAY,IAAI;AACjD;AACA,YAAI,mBAAmB,GAAG,WAAW,CAAC;AACtC,eAAO,OAAO,mBAAmB;MACnC;AASA,eAAS,OAAO,2BAA2B;AAU3C,eAAS,OAAO,mBAAmB,SAAU,MAAM,eAAe,gBAAgB;AAChF;AAGA,YAAI,cAAc,OAAO,cAAc,QAAO,eAAgB,MAAK,IAAK;AAExE,YAAI,gBAAgB;AAClB,wBAAc,SAAS;QACzB;AAEA,YAAI,QAAQ,IAAI,OAAO,aAAa,GAAE;AACtC,eAAO,KAAK,QAAQ,OAAO,wBAAwB;AAEnD,eAAO;MACT;AAOA,eAAS,OAAO,uBAAuB,SAAU,KAAK;AACpD;AAEA,eAAO,IACJ,QAAO,WAAY,GAAA,EACnB,QAAO,SAAU,GAAA,EACjB,QAAO,SAAU,GAAA,EACjB,QAAO,UAAW,GAAA;MACvB;AAEA,UAAI,kBAAkB,SAAU,KAAK,MAAM,OAAO,OAAO;AACvD;AACA,YAAI,IAAI,SAAS,IACb,IAAI,EAAE,QAAO,GAAG,IAAK,IACrB,IAAI,IAAI,OAAO,OAAO,MAAM,OAAO,MAAM,EAAE,QAAO,MAAO,EAAA,CAAA,GACzD,IAAI,IAAI,OAAO,MAAM,EAAE,QAAO,MAAO,EAAA,CAAA,GACrC,MAAM,CAAA,GACN,GAAG,GAAG,GAAG,OAAO;AAEpB,WAAG;AACD,cAAI;AACJ,iBAAQ,IAAI,EAAE,KAAK,GAAG,GAAI;AACxB,gBAAI,EAAE,KAAK,EAAE,CAAC,CAAA,GAAI;AAChB,kBAAG,CAAG,KAAM;AACV,oBAAI,EAAE;AACN,wBAAQ,IAAI,EAAE,CAAC,EAAE;cACnB;YACF,WAAW,GAAG;AACZ,kBAAG,CAAA,EAAI,GAAG;AACR,sBAAM,EAAE,QAAQ,EAAE,CAAC,EAAE;AACrB,oBAAI,MAAM;kBACR,MAAM,EAAC,OAAc,KAAK,EAAC;kBAC3B,OAAO,EAAC,OAAO,GAAG,KAAK,EAAE,MAAK;kBAC9B,OAAO,EAAC,OAAO,EAAE,OAAO,IAAQ;kBAChC,YAAY,EAAC,OAAc,IAAQ;gBACrC;AACA,oBAAI,KAAK,GAAG;AACZ,oBAAG,CAAE,GAAG;AACN,yBAAO;gBACT;cACF;YACF;UACF;QACF,SAAS,MAAM,EAAE,YAAY;AAE7B,eAAO;MACT;AA+BA,eAAS,OAAO,uBAAuB,SAAU,KAAK,MAAM,OAAO,OAAO;AACxE;AAEA,YAAI,WAAW,gBAAiB,KAAK,MAAM,OAAO,KAAK,GACnD,UAAU,CAAA;AAEd,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,EAAE,GAAG;AACxC,kBAAQ,KAAI;YACV,IAAI,MAAM,SAAS,CAAC,EAAE,WAAW,OAAO,SAAS,CAAC,EAAE,WAAW,GAAG;YAClE,IAAI,MAAM,SAAS,CAAC,EAAE,MAAM,OAAO,SAAS,CAAC,EAAE,MAAM,GAAG;YACxD,IAAI,MAAM,SAAS,CAAC,EAAE,KAAK,OAAO,SAAS,CAAC,EAAE,KAAK,GAAG;YACtD,IAAI,MAAM,SAAS,CAAC,EAAE,MAAM,OAAO,SAAS,CAAC,EAAE,MAAM,GAAG;UAC1D,CAAA;QACF;AACA,eAAO;MACT;AAWA,eAAS,OAAO,yBAAyB,SAAU,KAAK,aAAa,MAAM,OAAO,OAAO;AACvF;AAEA,YAAG,CAAE,SAAS,OAAO,WAAW,WAAW,GAAG;AAC5C,cAAI,SAAS;AACb,wBAAc,WAAY;AACxB,mBAAO;UACT;QACF;AAEA,YAAI,WAAW,gBAAgB,KAAK,MAAM,OAAO,KAAK,GAClD,WAAW,KACX,MAAM,SAAS;AAEnB,YAAI,MAAM,GAAG;AACX,cAAI,OAAO,CAAA;AACX,cAAI,SAAS,CAAC,EAAE,WAAW,UAAU,GAAG;AACtC,iBAAK,KAAK,IAAI,MAAM,GAAG,SAAS,CAAC,EAAE,WAAW,KAAK,CAAA;UACrD;AACA,mBAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,iBAAK;cACH;gBACE,IAAI,MAAM,SAAS,CAAC,EAAE,WAAW,OAAO,SAAS,CAAC,EAAE,WAAW,GAAG;gBAClE,IAAI,MAAM,SAAS,CAAC,EAAE,MAAM,OAAO,SAAS,CAAC,EAAE,MAAM,GAAG;gBACxD,IAAI,MAAM,SAAS,CAAC,EAAE,KAAK,OAAO,SAAS,CAAC,EAAE,KAAK,GAAG;gBACtD,IAAI,MAAM,SAAS,CAAC,EAAE,MAAM,OAAO,SAAS,CAAC,EAAE,MAAM,GAAG;cAC1D;YACF;AACA,gBAAI,IAAI,MAAM,GAAG;AACf,mBAAK,KAAK,IAAI,MAAM,SAAS,CAAC,EAAE,WAAW,KAAK,SAAS,IAAI,CAAC,EAAE,WAAW,KAAK,CAAA;YAClF;UACF;AACA,cAAI,SAAS,MAAM,CAAC,EAAE,WAAW,MAAM,IAAI,QAAQ;AACjD,iBAAK,KAAK,IAAI,MAAM,SAAS,MAAM,CAAC,EAAE,WAAW,GAAG,CAAA;UACtD;AACA,qBAAW,KAAK,KAAI,EAAA;QACtB;AACA,eAAO;MACT;AAYA,eAAS,OAAO,eAAe,SAAU,KAAK,OAAO,WAAW;AAC9D;AACA,YAAG,CAAE,SAAS,OAAO,SAAS,GAAG,GAAG;AAClC,gBAAM;QACR;AACA,YAAI,iBAAiB,WAAW,OAAO;AACrC,gBAAM;QACR;AACA,YAAI,UAAU,IAAI,UAAU,aAAa,CAAC,EAAE,OAAO,KAAK;AACxD,eAAQ,WAAW,IAAM,WAAW,aAAa,KAAM;MACzD;AASA,eAAS,OAAO,eAAe,SAAU,KAAK,OAAO;AACnD;AACA,YAAG,CAAE,SAAS,OAAO,SAAS,GAAG,GAAG;AAClC,gBAAM;QACR;AACA,eAAO,CAAC,IAAI,UAAU,GAAG,KAAK,GAAG,IAAI,UAAU,KAAK,CAAA;MACtD;AAWA,eAAS,OAAO,qBAAqB,SAAU,MAAM;AACnD;AACA,YAAI,SAAS;UACX,SAAU,IAAI;AACZ,mBAAO,OAAO,GAAG,WAAW,CAAC,IAAI;UACnC;UACA,SAAU,IAAI;AACZ,mBAAO,QAAQ,GAAG,WAAW,CAAC,EAAE,SAAS,EAAE,IAAI;UACjD;UACA,SAAU,IAAI;AACZ,mBAAO;UACT;QACF;AAEA,eAAO,KAAK,QAAO,MAAO,SAAU,IAAI;AACtC,cAAI,OAAO,KAAK;AAEd,iBAAK,OAAO,KAAK,MAAM,KAAK,OAAM,IAAK,CAAC,CAAA,EAAG,EAAE;UAC/C,OAAO;AACL,gBAAI,IAAI,KAAK,OAAM;AAEnB,iBACE,IAAI,MAAM,OAAO,CAAC,EAAE,EAAE,IAAI,IAAI,OAAO,OAAO,CAAC,EAAE,EAAE,IAAI,OAAO,CAAC,EAAE,EAAE;UAErE;AACA,iBAAO;QACT,CAAA;AAEA,eAAO;MACT;AASA,eAAS,OAAO,SAAS,SAAS,OAAQ,KAAK,cAAc,WAAW;AACtE;AAGA,uBAAe,gBAAc;AAE7B,oBAAY,OAAO,aAAa,GAAE;AAClC,YAAI,IAAI,SAAS,cAAc;AAC7B,iBAAO,OAAO,GAAG;QACnB,OAAO;AACL,yBAAe,eAAe,IAAI;AAClC,cAAI,eAAe,UAAU,QAAQ;AACnC,yBAAa,UAAU,OAAO,eAAe,UAAU,MAAM;UAC/D;AACA,iBAAO,OAAO,GAAG,IAAI,UAAU,MAAM,GAAE,YAAY;QACrD;MACF;AAMA,UAAI,OAAQ,YAAa,aAAa;AACpC,kBAAU;UACR,MAAM,SAAU,KAAK;AACnB;AACA,kBAAM,GAAG;UACX;UACA,KAAK,SAAU,KAAK;AAClB;AACA,kBAAM,GAAG;UACX;UACA,OAAO,SAAU,KAAK;AACpB;AACA,kBAAM;UACR;QACF;MACF;AAMA,eAAS,OAAO,UAAU;QACxB,sBAAsB;MACxB;AAKA,eAAS,OAAO,SAAS;QACvB,MAAG;QACH,MAAG;QACH,OAAI;QACJ,QAAK;QACL,mBAAgB;QAChB,mBAAgB;QAChB,mBAAgB;QAChB,SAAM;QACN,KAAE;QACF,MAAG;QACH,OAAI;QACJ,QAAK;QACL,UAAO;QACP,kBAAe;QACf,YAAS;QACT,eAAY;QACZ,WAAQ;QACR,SAAM;QACN,aAAU;QACV,WAAQ;QACR,UAAO;QACP,SAAM;QACN,SAAM;QACN,SAAM;QACN,aAAU;QACV,OAAI;QACJ,SAAM;QACN,YAAS;QACT,SAAM;QACN,kBAAe;QACf,qBAAkB;QAClB,mBAAgB;QAChB,cAAW;QACX,oBAAiB;QACjB,iBAAc;QACd,sBAAmB;QACnB,oBAAiB;QACjB,cAAW;QACX,oBAAiB;QACjB,qBAAkB;QAClB,eAAY;QACZ,oBAAiB;QACjB,YAAS;QACT,iBAAc;QACd,kBAAe;QACf,oBAAiB;QACjB,qBAAkB;QAClB,oBAAiB;QACjB,2BAAwB;QACxB,OAAI;QACJ,qBAAkB;QAClB,wBAAqB;QACrB,cAAW;QACX,iBAAc;QACd,OAAI;QACJ,eAAY;QACZ,WAAQ;QACR,KAAE;QACF,QAAK;QACL,eAAY;QACZ,cAAW;QACX,eAAY;QACZ,QAAK;QACL,SAAM;QACN,aAAU;QACV,iBAAc;QACd,kBAAe;QACf,iBAAc;QACd,WAAQ;QACR,cAAW;QACX,yBAAsB;QACtB,UAAO;QACP,UAAO;QACP,YAAS;QACT,QAAK;QACL,aAAU;QACV,UAAO;QACP,YAAS;QACT,cAAW;QACX,kBAAe;QACf,oBAAiB;QACjB,OAAI;QACJ,QAAK;QACL,WAAQ;QACR,WAAQ;QACR,kBAAe;QACf,QAAK;QACL,OAAI;QACJ,OAAI;QACJ,QAAK;QACL,SAAM;QACN,UAAO;QACP,YAAS;QACT,QAAK;QACL,gBAAa;QACb,SAAM;QACN,cAAW;QACX,QAAK;QACL,gBAAa;QACb,UAAO;QACP,aAAU;QACV,QAAK;QACL,YAAS;QACT,gBAAa;QACb,cAAW;QACX,eAAY;QACZ,eAAY;QACZ,sBAAmB;QACnB,6BAA0B;QAC1B,uBAAoB;QACpB,aAAU;QACV,sBAAmB;QACnB,uBAAoB;QACpB,cAAW;QACX,gBAAa;QACb,WAAQ;QACR,YAAS;QACT,aAAU;QACV,YAAS;QACT,cAAW;QACX,SAAM;QACN,QAAK;QACL,QAAK;QACL,QAAK;QACL,QAAK;QACL,YAAS;QACT,iBAAc;QACd,SAAM;QACN,QAAK;QACL,QAAK;QACL,WAAQ;QACR,cAAW;QACX,iBAAc;QACd,gBAAa;QACb,WAAQ;QACR,gBAAa;QACb,OAAI;QACJ,SAAM;QACN,mBAAgB;QAChB,mBAAgB;QAChB,aAAU;QACV,gBAAa;QACb,OAAI;QACJ,yBAAsB;QACtB,QAAK;QACL,qBAAkB;QAClB,oBAAiB;QACjB,WAAQ;QACR,OAAI;QACJ,4BAAyB;QACzB,WAAQ;QACR,sBAAmB;QACnB,uBAAoB;QACpB,aAAU;QACV,UAAO;QACP,QAAK;QACL,YAAS;QACT,gBAAa;QACb,WAAQ;QACR,SAAM;QACN,UAAO;QACP,gBAAa;QACb,WAAQ;QACR,UAAO;QACP,UAAO;QACP,SAAM;QACN,SAAM;QACN,gBAAa;QACb,aAAU;QACV,OAAI;QACJ,iBAAc;QACd,cAAW;QACX,uBAAoB;QACpB,kBAAe;QACf,UAAO;QACP,OAAI;QACJ,QAAK;QACL,MAAG;QACH,UAAO;QACP,aAAU;QACV,SAAM;QACN,8BAA2B;QAC3B,4BAAyB;QACzB,kBAAe;QACf,UAAO;QACP,YAAS;QACT,kBAAe;QACf,YAAS;QACT,WAAQ;QACR,qBAAkB;QAClB,YAAS;QACT,iBAAc;QACd,kBAAe;QACf,UAAO;QACP,UAAO;QACP,eAAY;QACZ,gBAAa;QACb,eAAY;QACZ,aAAU;QACV,MAAG;QACH,SAAM;QACN,QAAK;QACL,WAAQ;QACR,sBAAmB;QACnB,oBAAiB;QACjB,aAAU;QACV,UAAO;QACP,WAAQ;QACR,aAAU;QACV,WAAQ;QACR,aAAU;QACV,WAAQ;QACR,aAAU;QACV,YAAS;QACT,UAAO;QACP,YAAS;QACT,UAAO;QACP,YAAS;QACT,UAAO;QACP,YAAS;QACT,UAAO;QACP,YAAS;QACT,UAAO;QACP,YAAS;QACT,UAAO;QACP,YAAS;QACT,UAAO;QACP,YAAS;QACT,UAAO;QACP,YAAS;QACT,eAAY;QACZ,wBAAqB;QACrB,mBAAgB;QAChB,SAAM;QACN,wBAAqB;QACrB,iCAA8B;QAC9B,mBAAgB;QAChB,mBAAgB;QAChB,cAAW;QACX,SAAM;QACN,YAAS;QACT,UAAO;QACP,UAAO;QACP,cAAW;QACX,SAAM;QACN,YAAS;QACT,kBAAe;QACf,iBAAc;QACd,cAAW;QACX,YAAS;QACT,mBAAgB;QAChB,gBAAa;QACb,2BAAwB;QACxB,6BAA0B;QAC1B,iBAAc;QACd,qBAAkB;QAClB,UAAO;QACP,QAAK;QACL,aAAU;QACV,aAAU;QACV,QAAK;QACL,kBAAe;QACf,UAAO;QACP,+BAA4B;QAC5B,6BAA0B;QAC1B,iCAA8B;QAC9B,sBAAmB;QACnB,wBAAqB;QACrB,0BAAuB;QACvB,OAAI;QACJ,QAAK;QACL,mBAAgB;QAChB,QAAK;QACL,UAAO;QACP,eAAY;QACZ,iBAAc;QACd,WAAQ;QACR,aAAU;QACV,aAAU;QACV,mBAAgB;QAChB,iBAAc;QACd,kBAAe;QACf,SAAM;QACN,OAAI;QACJ,mBAAgB;QAChB,gBAAa;QACb,YAAS;QACT,SAAM;QACN,cAAW;QACX,qBAAkB;QAClB,SAAM;QACN,WAAQ;QACR,WAAQ;QACR,WAAQ;QACR,UAAO;QACP,UAAO;QACP,iBAAc;QACd,eAAY;QACZ,SAAM;QACN,mBAAgB;QAChB,QAAK;QACL,QAAK;QACL,QAAK;QACL,kBAAe;QACf,QAAK;QACL,oBAAiB;QACjB,kBAAe;QACf,UAAO;QACP,iBAAc;QACd,oBAAiB;QACjB,kBAAe;QACf,mCAAgC;QAChC,YAAS;QACT,gBAAa;QACb,yBAAsB;QACtB,SAAM;QACN,cAAW;QACX,iBAAc;QACd,OAAI;QACJ,QAAK;QACL,UAAO;QACP,SAAM;QACN,WAAQ;QACR,QAAK;QACL,YAAS;QACT,QAAK;QACL,UAAO;QACP,eAAY;QACZ,SAAM;QACN,mBAAgB;QAChB,iBAAc;QACd,WAAQ;QACR,QAAK;QACL,QAAK;QACL,OAAI;QACJ,UAAO;QACP,SAAM;QACN,OAAI;QACJ,eAAY;QACZ,gBAAa;QACb,kBAAe;QACf,cAAW;QACX,OAAI;QACJ,YAAS;QACT,4BAAyB;QACzB,yBAAsB;QACtB,iBAAc;QACd,YAAS;QACT,SAAM;QACN,OAAI;QACJ,uBAAoB;QACpB,QAAK;QACL,mBAAgB;QAChB,wBAAqB;QACrB,kBAAe;QACf,eAAY;QACZ,kBAAe;QACf,OAAI;QACJ,qBAAkB;QAClB,cAAW;QACX,QAAK;QACL,0BAAuB;QACvB,yBAAsB;QACtB,iBAAc;QACd,WAAQ;QACR,eAAY;QACZ,wBAAqB;QACrB,kBAAe;QACf,sBAAmB;QACnB,mBAAgB;QAChB,uBAAoB;QACpB,wBAAqB;QACrB,sBAAmB;QACnB,0BAAuB;QACvB,uBAAoB;QACpB,2BAAwB;QACxB,4BAAyB;QACzB,4BAAyB;QACzB,yBAAsB;QACtB,6BAA0B;QAC1B,8BAA2B;QAC3B,oBAAiB;QACjB,wBAAqB;QACrB,qBAAkB;QAClB,yBAAsB;QACtB,0BAAuB;QACvB,0BAAuB;QACvB,8BAA2B;QAC3B,2BAAwB;QACxB,+BAA4B;QAC5B,gCAA6B;QAC7B,gBAAa;QACb,OAAI;QACJ,WAAQ;QACR,QAAK;QACL,oBAAiB;QACjB,gBAAa;QACb,SAAM;QACN,gBAAa;QACb,gBAAa;QACb,eAAY;QACZ,kBAAe;QACf,cAAW;QACX,QAAK;QACL,eAAY;QACZ,aAAU;QACV,sBAAmB;QACnB,gCAA6B;QAC7B,QAAK;QACL,aAAU;QACV,yBAAsB;QACtB,eAAY;QACZ,aAAU;QACV,cAAW;QACX,SAAM;QACN,cAAW;QACX,gBAAa;QACb,kBAAe;QACf,oBAAiB;QACjB,eAAY;QACZ,wBAAqB;QACrB,WAAQ;QACR,OAAI;QACJ,SAAM;QACN,YAAS;QACT,cAAW;QACX,kBAAe;QACf,YAAS;QACT,gBAAa;QACb,oBAAiB;QACjB,YAAS;QACT,kBAAe;QACf,QAAK;QACL,aAAU;QACV,gBAAa;QACb,SAAM;QACN,QAAK;QACL,YAAS;QACT,iBAAc;QACd,gBAAa;QACb,kBAAe;QACf,iBAAc;QACd,YAAS;QACT,aAAU;QACV,uBAAoB;QACpB,eAAY;QACZ,YAAS;QACT,QAAK;QACL,OAAI;QACJ,UAAO;QACP,SAAM;QACN,QAAK;QACL,cAAW;QACX,QAAK;QACL,wBAAqB;QACrB,YAAS;QACT,QAAK;QACL,QAAK;QACL,eAAY;QACZ,iBAAc;QACd,WAAQ;QACR,UAAO;QACP,eAAY;QACZ,cAAW;QACX,eAAY;QACZ,eAAY;QACZ,oBAAiB;QACjB,iBAAc;QACd,aAAU;QACV,QAAK;QACL,YAAS;QACT,aAAU;QACV,eAAY;QACZ,UAAO;QACP,OAAI;QACJ,iBAAc;QACd,eAAY;QACZ,aAAU;QACV,UAAO;QACP,mBAAgB;QAChB,qBAAkB;QAClB,WAAQ;QACR,QAAK;QACL,WAAQ;QACR,aAAU;QACV,UAAO;QACP,iBAAc;QACd,kBAAe;QACf,cAAW;QACX,gBAAa;QACb,SAAM;QACN,oBAAiB;QACjB,cAAW;QACX,kBAAe;QACf,aAAU;QACV,cAAW;QACX,UAAO;QACP,oBAAiB;QACjB,uBAAoB;QACpB,qBAAkB;QAClB,2BAAwB;QACxB,oBAAiB;QACjB,0BAAuB;QACvB,mBAAgB;QAChB,cAAW;QACX,QAAK;QACL,YAAS;QACT,mBAAgB;QAChB,aAAU;QACV,SAAM;QACN,QAAK;QACL,aAAU;QACV,SAAM;QACN,gBAAa;QACb,YAAS;QACT,cAAW;QACX,UAAO;QACP,SAAM;QACN,cAAW;QACX,aAAU;QACV,0BAAuB;QACvB,SAAM;QACN,qBAAkB;QAClB,UAAO;QACP,QAAK;QACL,UAAO;QACP,aAAU;QACV,cAAW;QACX,aAAU;QACV,YAAS;QACT,MAAG;QACH,uBAAoB;QACpB,OAAI;QACJ,cAAW;QACX,qBAAkB;QAClB,sBAAmB;QACnB,sBAAmB;QACnB,YAAS;QACT,eAAY;QACZ,UAAO;QACP,mBAAgB;QAChB,kBAAe;QACf,SAAM;QACN,mBAAgB;QAChB,mBAAgB;QAChB,iBAAc;QACd,SAAM;QACN,OAAI;QACJ,WAAQ;QACR,YAAS;QACT,SAAM;QACN,OAAI;QACJ,YAAS;QACT,cAAW;QACX,gBAAa;QACb,UAAO;QACP,QAAK;QACL,WAAQ;QACR,eAAY;QACZ,uBAAoB;QACpB,iBAAc;QACd,wBAAqB;QACrB,cAAW;QACX,SAAM;QACN,QAAK;QACL,SAAM;QACN,qBAAkB;QAClB,sBAAmB;QACnB,wBAAqB;QACrB,qBAAkB;QAClB,+BAA4B;QAC5B,eAAY;QACZ,YAAS;QACT,UAAO;QACP,UAAO;QACP,gBAAa;QACb,oBAAiB;QACjB,6BAA0B;QAC1B,SAAM;QACN,OAAI;QACJ,WAAQ;QACR,gBAAa;QACb,SAAM;QACN,cAAW;QACX,QAAK;QACL,QAAK;QACL,QAAK;QACL,YAAS;QACT,UAAO;QACP,QAAK;QACL,qBAAkB;QAClB,YAAS;QACT,QAAK;QACL,cAAW;QACX,eAAY;QACZ,cAAW;QACX,eAAY;QACZ,kBAAe;QACf,cAAW;QACX,KAAE;QACF,OAAI;QACJ,aAAU;QACV,WAAQ;QACR,WAAQ;QACR,kBAAe;QACf,qBAAkB;QAClB,wBAAqB;QACrB,OAAI;QACJ,cAAW;QACX,iBAAc;QACd,oBAAiB;QACjB,YAAS;QACT,eAAY;QACZ,mBAAgB;QAChB,sBAAmB;QACnB,cAAW;QACX,mBAAgB;QAChB,qBAAkB;QAClB,iBAAc;QACd,aAAU;QACV,gBAAa;QACb,gBAAa;QACb,qBAAkB;QAClB,aAAU;QACV,wBAAqB;QACrB,0BAAuB;QACvB,iBAAc;QACd,iBAAc;QACd,cAAW;QACX,eAAY;QACZ,eAAY;QACZ,oBAAiB;QACjB,uBAAoB;QACpB,mBAAgB;QAChB,aAAU;QACV,aAAU;QACV,qBAAkB;QAClB,cAAW;QACX,wBAAqB;QACrB,QAAK;QACL,iBAAc;QACd,eAAY;QACZ,gBAAa;QACb,kBAAe;QACf,gBAAa;QACb,QAAK;QACL,SAAM;QACN,QAAK;QACL,iBAAc;QACd,WAAQ;QACR,QAAK;QACL,SAAM;QACN,SAAM;QACN,cAAW;QACX,cAAW;QACX,cAAW;QACX,aAAU;QACV,WAAQ;QACR,YAAS;QACT,oBAAiB;QACjB,oBAAiB;QACjB,oBAAiB;QACjB,YAAS;QACT,UAAO;QACP,eAAY;QACZ,YAAS;QACT,QAAK;QACL,gBAAa;QACb,UAAO;QACP,cAAW;QACX,iBAAc;QACd,cAAW;QACX,YAAS;QACT,cAAW;QACX,YAAS;QACT,uBAAoB;QACpB,yBAAsB;QACtB,qBAAkB;QAClB,oBAAiB;QACjB,iBAAc;QACd,SAAM;QACN,UAAO;QACP,gBAAa;QACb,SAAM;QACN,aAAU;QACV,UAAO;QACP,YAAS;QACT,oBAAiB;QACjB,gBAAa;QACb,iBAAc;QACd,QAAK;QACL,aAAU;QACV,cAAW;QACX,iBAAc;QACd,kBAAe;QACf,WAAQ;QACR,+BAA4B;QAC5B,aAAU;QACV,gBAAa;QACb,OAAI;QACJ,YAAS;QACT,sBAAmB;QACnB,aAAU;QACV,kBAAe;QACf,qBAAkB;QAClB,MAAG;QACH,eAAY;QACZ,iBAAc;QACd,oBAAiB;QACjB,WAAQ;QACR,eAAY;QACZ,YAAS;QACT,iBAAc;QACd,oBAAiB;QACjB,YAAS;QACT,kBAAe;QACf,cAAW;QACX,qBAAkB;QAClB,QAAK;QACL,YAAS;QACT,kCAA+B;QAC/B,SAAM;QACN,gBAAa;QACb,KAAE;QACF,MAAG;QACH,SAAM;QACN,WAAQ;QACR,QAAK;QACL,UAAO;QACP,YAAS;QACT,MAAG;QACH,WAAQ;QACR,UAAO;QACP,YAAS;QACT,WAAQ;QACR,aAAU;QACV,eAAY;QACZ,MAAG;QACH,MAAG;QACH,uBAAoB;QACpB,gBAAa;QACb,uBAAoB;QACpB,iBAAc;QACd,oBAAiB;QACjB,cAAW;QACX,cAAW;QACX,iBAAc;QACd,aAAU;QACV,eAAY;QACZ,kBAAe;QACf,eAAY;QACZ,OAAI;QACJ,MAAG;QACH,WAAQ;QACR,kBAAe;QACf,kBAAe;QACf,SAAM;QACN,cAAW;QACX,aAAU;QACV,YAAS;QACT,cAAW;QACX,aAAU;QACV,cAAW;QACX,qBAAkB;QAClB,WAAQ;QACR,yBAAsB;QACtB,gBAAa;QACb,kBAAe;QACf,oBAAiB;QACjB,gBAAa;QACb,gBAAa;QACb,SAAM;QACN,WAAQ;QACR,QAAK;QACL,OAAI;QACJ,WAAQ;QACR,WAAQ;QACR,WAAQ;QACR,mBAAgB;QAChB,aAAU;QACV,kBAAe;QACf,iBAAc;QACd,SAAM;QACN,QAAK;QACL,OAAI;QACJ,QAAK;QACL,YAAS;QACT,QAAK;QACL,aAAU;QACV,aAAU;QACV,UAAO;QACP,SAAM;QACN,oBAAiB;QACjB,sBAAmB;QACnB,wBAAqB;QACrB,cAAW;QACX,cAAW;QACX,eAAY;QACZ,YAAS;QACT,cAAW;QACX,cAAW;QACX,eAAY;QACZ,UAAO;QACP,WAAQ;QACR,eAAY;QACZ,eAAY;QACZ,WAAQ;QACR,iBAAc;QACd,UAAO;QACP,SAAM;QACN,eAAY;QACZ,SAAM;QACN,QAAK;QACL,eAAY;QACZ,eAAY;QACZ,QAAK;QACL,gBAAa;QACb,kBAAe;QACf,yBAAsB;QACtB,UAAO;QACP,YAAS;QACT,WAAQ;QACR,gBAAa;QACb,SAAM;QACN,WAAQ;QACR,2BAAwB;QACxB,YAAS;QACT,UAAO;QACP,WAAQ;QACR,aAAU;QACV,cAAW;QACX,SAAM;QACN,gBAAa;QACb,eAAY;QACZ,eAAY;QACZ,iBAAc;QACd,WAAQ;QACR,gBAAa;QACb,uBAAoB;QACpB,oCAAiC;QACjC,gBAAa;QACb,sBAAmB;QACnB,oBAAiB;QACjB,OAAI;QACJ,SAAM;QACN,OAAI;QACJ,iBAAc;QACd,WAAQ;QACR,cAAW;QACX,cAAW;QACX,WAAQ;QACR,YAAS;QACT,mBAAgB;QAChB,UAAO;QACP,cAAW;QACX,wBAAqB;QACrB,YAAS;QACT,oBAAiB;QACjB,UAAO;QACP,cAAW;QACX,UAAO;QACP,QAAK;QACL,aAAU;QACV,gBAAa;QACb,cAAW;QACX,sBAAmB;QACnB,QAAK;QACL,SAAM;QACN,UAAO;QACP,QAAK;QACL,aAAU;QACV,kBAAe;QACf,WAAQ;QACR,QAAK;QACL,WAAQ;QACR,kBAAe;QACf,iBAAc;QACd,cAAW;QACX,gBAAa;QACb,kBAAe;QACf,eAAY;QACZ,2BAAwB;QACxB,iBAAc;QACd,MAAG;QACH,eAAY;QACZ,QAAK;QACL,UAAO;QACP,SAAM;QACN,aAAU;QACV,aAAU;QACV,UAAO;QACP,kBAAe;QACf,YAAS;QACT,YAAS;QACT,YAAS;QACT,UAAO;QACP,cAAW;QACX,UAAO;QACP,QAAK;QACL,UAAO;QACP,eAAY;QACZ,YAAS;QACT,UAAO;QACP,uBAAoB;QACpB,YAAS;QACT,SAAM;QACN,cAAW;QACX,SAAM;QACN,SAAM;QACN,UAAO;QACP,iBAAc;QACd,QAAK;QACL,SAAM;QACN,YAAS;QACT,iBAAc;QACd,UAAO;QACP,UAAO;QACP,mBAAgB;QAChB,oBAAiB;QACjB,OAAI;QACJ,SAAM;QACN,SAAM;QACN,wBAAqB;QACrB,YAAS;QACT,gBAAa;QACb,UAAO;QACP,0BAAuB;QACvB,yBAAsB;QACtB,gBAAa;QACb,kBAAe;QACf,sBAAmB;QACnB,wBAAqB;QACrB,sBAAmB;QACnB,2BAAwB;QACxB,SAAM;QACN,aAAU;QACV,UAAO;QACP,cAAW;QACX,eAAY;QACZ,SAAM;QACN,aAAU;QACV,WAAQ;QACR,SAAM;QACN,SAAM;QACN,iBAAc;QACd,eAAY;QACZ,aAAU;QACV,WAAQ;QACR,qBAAkB;QAClB,OAAI;QACJ,UAAO;QACP,QAAK;QACL,OAAI;QACJ,SAAM;QACN,iBAAc;QACd,UAAO;QACP,aAAU;QACV,WAAQ;QACR,YAAS;QACT,YAAS;QACT,mBAAgB;QAChB,iBAAc;QACd,WAAQ;QACR,iBAAc;QACd,kBAAe;QACf,aAAU;QACV,UAAO;QACP,cAAW;QACX,mBAAgB;QAChB,kBAAe;QACf,SAAM;QACN,SAAM;QACN,WAAQ;QACR,QAAK;QACL,SAAM;QACN,qBAAkB;QAClB,iBAAc;QACd,SAAM;QACN,WAAQ;QACR,qBAAkB;QAClB,oBAAiB;QACjB,QAAK;QACL,eAAY;QACZ,aAAU;QACV,aAAU;QACV,kBAAe;QACf,cAAW;QACX,oBAAiB;QACjB,gCAA6B;QAC7B,gCAA6B;QAC7B,qBAAkB;QAClB,qBAAkB;QAClB,0BAAuB;QACvB,yBAAsB;QACtB,0BAAuB;QACvB,iBAAc;QACd,aAAU;QACV,cAAW;QACX,SAAM;QACN,WAAQ;QACR,0BAAuB;QACvB,eAAY;QACZ,iBAAc;QACd,SAAM;QACN,sBAAmB;QACnB,SAAM;QACN,eAAY;QACZ,eAAY;QACZ,gBAAa;QACb,gBAAa;QACb,kBAAe;QACf,WAAQ;QACR,aAAU;QACV,WAAQ;QACR,QAAK;QACL,QAAK;QACL,iBAAc;QACd,UAAO;QACP,QAAK;QACL,OAAI;QACJ,sBAAmB;QACnB,aAAU;QACV,UAAO;QACP,QAAK;QACL,eAAY;QACZ,YAAS;QACT,mBAAgB;QAChB,UAAO;QACP,WAAQ;QACR,SAAM;QACN,UAAO;QACP,eAAY;QACZ,oBAAiB;QACjB,cAAW;QACX,MAAG;QACH,UAAO;QACP,eAAY;QACZ,UAAO;QACP,UAAO;QACP,OAAI;QACJ,UAAO;QACP,WAAQ;QACR,aAAU;QACV,WAAQ;QACR,iBAAc;QACd,SAAM;QACN,UAAO;QACP,QAAK;QACL,2BAAwB;QACxB,oBAAiB;QACjB,WAAQ;QACR,WAAQ;QACR,cAAW;QACX,UAAO;QACP,kBAAe;QACf,iBAAc;QACd,SAAM;QACN,WAAQ;QACR,SAAM;QACN,iBAAc;QACd,UAAO;QACP,UAAO;QACP,MAAG;QACH,6BAA0B;QAC1B,cAAW;QACX,yBAAsB;QACtB,2BAAwB;QACxB,SAAM;QACN,SAAM;QACN,SAAM;QACN,SAAM;QACN,SAAM;QACN,SAAM;QACN,SAAM;QACN,SAAM;QACN,SAAM;QACN,SAAM;QACN,SAAM;QACN,YAAS;QACT,YAAS;QACT,YAAS;QACT,WAAQ;QACR,UAAO;QACP,MAAG;QACH,oBAAiB;QACjB,KAAE;QACF,0BAAuB;QACvB,OAAI;QACJ,kBAAe;QACf,gBAAa;QACb,cAAW;QACX,UAAO;QACP,SAAM;QACN,WAAQ;QACR,cAAW;QACX,MAAG;QACH,iBAAc;QACd,eAAY;QACZ,iBAAc;QACd,wBAAqB;QACrB,uBAAoB;QACpB,WAAQ;QACR,eAAY;QACZ,SAAM;QACN,iBAAc;QACd,cAAW;QACX,QAAK;QACL,aAAU;QACV,wBAAqB;QACrB,MAAG;QACH,SAAM;QACN,WAAQ;QACR,sBAAmB;QACnB,wBAAqB;QACrB,SAAM;QACN,UAAO;QACP,mBAAgB;QAChB,cAAW;QACX,oBAAiB;QACjB,gBAAa;QACb,cAAW;QACX,gBAAa;QACb,sBAAmB;QACnB,6BAA0B;QAC1B,uBAAoB;QACpB,sBAAmB;QACnB,uBAAoB;QACpB,iBAAc;QACd,cAAW;QACX,aAAU;QACV,cAAW;QACX,QAAK;QACL,QAAK;QACL,SAAM;QACN,gBAAa;QACb,mBAAgB;QAChB,sBAAmB;QACnB,cAAW;QACX,qBAAkB;QAClB,wBAAqB;QACrB,gBAAa;QACb,qBAAkB;QAClB,uBAAoB;QACpB,eAAY;QACZ,kBAAe;QACf,kBAAe;QACf,uBAAoB;QACpB,eAAY;QACZ,0BAAuB;QACvB,4BAAyB;QACzB,mBAAgB;QAChB,mBAAgB;QAChB,gBAAa;QACb,iBAAc;QACd,iBAAc;QACd,sBAAmB;QACnB,qBAAkB;QAClB,kBAAe;QACf,cAAW;QACX,mBAAgB;QAChB,UAAO;QACP,aAAU;QACV,WAAQ;QACR,UAAO;QACP,gBAAa;QACb,KAAE;QACF,gBAAa;QACb,OAAI;QACJ,YAAS;QACT,OAAI;QACJ,OAAI;QACJ,qBAAkB;QAClB,OAAI;;QAGJ,WAAY;QACZ,YAAY;MACd;ACxjDA,eAAS,YAAY,SAAU,kBAAkB;AAC/C;AAEA,YAMI,UAAU,CAAA,GAOV,iBAAiB,CAAA,GAOjB,kBAAkB,CAAA,GAOlB,YAAY,CAAA,GAKZ,gBAAgB,WAMhB,WAAW;UACT,QAAQ,CAAA;UACR,KAAK;UACL,QAAQ;QACV;AAEJ,qBAAY;AAMZ,iBAAS,eAAgB;AACvB,6BAAmB,oBAAoB,CAAA;AAEvC,mBAAS,QAAQ,eAAe;AAC9B,gBAAI,cAAc,eAAe,IAAI,GAAG;AACtC,sBAAQ,IAAI,IAAI,cAAc,IAAI;YACpC;UACF;AAGA,cAAI,OAAO,qBAAqB,UAAU;AACxC,qBAAS,OAAO,kBAAkB;AAChC,kBAAI,iBAAiB,eAAe,GAAG,GAAG;AACxC,wBAAQ,GAAG,IAAI,iBAAiB,GAAG;cACrC;YACF;UACF,OAAO;AACL,kBAAM,MAAK,iEAAkE,OAAO,mBACpF,sBAAoB;UACtB;AAEA,cAAI,QAAQ,YAAY;AACtB,qBAAS,OAAO,QAAQ,QAAQ,YAAY,eAAe;UAC7D;QACF;AAQA,iBAAS,gBAAiB,KAAK,MAAM;AAEnC,iBAAO,QAAQ;AAEf,cAAI,SAAS,OAAO,SAAS,GAAG,GAAG;AACjC,kBAAM,SAAS,OAAO,WAAW,GAAG;AACpC,mBAAO;AAGP,gBAAI,SAAS,WAAW,GAAG,GAAG;AAC5B,sBAAQ,KAAI,0BAA2B,MAAM,8HACsB;AACnE,qCAAuB,SAAS,WAAW,GAAG,GAAG,GAAG;AACpD;YAGF,WAAU,CAAE,SAAS,OAAO,YAAY,WAAW,GAAG,CAAA,GAAI;AACxD,oBAAM,WAAW,GAAG;YAEtB,OAAO;AACL,oBAAM,MAAK,gBAAiB,MAAM,6EAA2E;YAC/G;UACF;AAEA,cAAI,OAAO,QAAQ,YAAY;AAC7B,kBAAM,IAAG;UACX;AAEA,cAAG,CAAE,SAAS,OAAO,QAAQ,GAAG,GAAG;AACjC,kBAAM,CAAC,GAAG;UACZ;AAEA,cAAI,WAAW,SAAS,KAAK,IAAI;AACjC,cAAG,CAAE,SAAS,OAAO;AACnB,kBAAM,MAAM,SAAS,KAAK;UAC5B;AAEA,mBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,oBAAQ,IAAI,CAAC,EAAE,MAAM;cAEnB,KAAK;AACH,+BAAe,KAAK,IAAI,CAAC,CAAA;AACzB;cAEF,KAAK;AACH,gCAAgB,KAAK,IAAI,CAAC,CAAA;AAC1B;YACJ;AACA,gBAAI,IAAI,CAAC,EAAE,eAAc,WAAW,GAAI;AACtC,uBAAS,MAAM,IAAI,CAAC,EAAE,WAAW;AAC/B,oBAAI,IAAI,CAAC,EAAE,UAAU,eAAe,EAAE,GAAG;AACvC,yBAAO,IAAI,IAAI,CAAC,EAAE,UAAU,EAAE,CAAA;gBAChC;cACF;YACF;UACF;QAEF;AAOA,iBAAS,uBAAwB,KAAK,MAAM;AAC1C,cAAI,OAAO,QAAQ,YAAY;AAC7B,kBAAM,IAAI,IAAI,SAAS,UAAS,CAAA;UAClC;AACA,cAAG,CAAE,SAAS,OAAO,QAAQ,GAAG,GAAG;AACjC,kBAAM,CAAC,GAAG;UACZ;AACA,cAAI,QAAQ,SAAS,KAAK,IAAI;AAE9B,cAAG,CAAE,MAAM,OAAO;AAChB,kBAAM,MAAM,MAAM,KAAK;UACzB;AAEA,mBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,oBAAQ,IAAI,CAAC,EAAE,MAAM;cACnB,KAAK;AACH,+BAAe,KAAK,IAAI,CAAC,CAAA;AACzB;cACF,KAAK;AACH,gCAAgB,KAAK,IAAI,CAAC,CAAA;AAC1B;cACF;AACE,sBAAM,MAAK,8CAA2C;YAC1D;UACF;QACF;AAOA,iBAAS,OAAQ,MAAM,UAAU;AAC/B,cAAG,CAAE,SAAS,OAAO,SAAS,IAAI,GAAG;AACnC,kBAAM,MAAK,+EAAgF,OAAO,OAAO,QAAO;UAClH;AAEA,cAAI,OAAO,aAAa,YAAY;AAClC,kBAAM,MAAK,qFAAsF,OAAO,WAAW,QAAO;UAC5H;AAEA,cAAG,CAAE,UAAU,eAAe,IAAI,GAAG;AACnC,sBAAU,IAAI,IAAI,CAAA;UACpB;AACA,oBAAU,IAAI,EAAE,KAAK,QAAQ;QAC/B;AAEA,iBAAS,eAAgB,MAAM;AAC7B,cAAI,MAAM,KAAK,MAAK,MAAK,EAAI,CAAC,EAAE,QAC5B,MAAM,IAAI,OAAM,YAAa,MAAM,KAAK,IAAG;AAC/C,iBAAO,KAAK,QAAQ,KAAK,EAAA;QAC3B;AAWA,aAAK,YAAY,SAAS,SAAU,SAAS,MAAMA,UAAS,SAAS;AACnE,cAAI,UAAU,eAAe,OAAO,GAAG;AACrC,qBAAS,KAAK,GAAG,KAAK,UAAU,OAAO,EAAE,QAAQ,EAAE,IAAI;AACrD,kBAAI,QAAQ,UAAU,OAAO,EAAE,EAAE,EAAE,SAAS,MAAM,MAAMA,UAAS,OAAO;AACxE,kBAAI,SAAS,OAAO,UAAU,aAAa;AACzC,uBAAO;cACT;YACF;UACF;AACA,iBAAO;QACT;AAQA,aAAK,SAAS,SAAU,MAAM,UAAU;AACtC,iBAAO,MAAM,QAAQ;AACrB,iBAAO;QACT;AAOA,aAAK,WAAW,SAAU,MAAM;AAE9B,cAAG,CAAE,MAAM;AACT,mBAAO;UACT;AAEA,cAAI,UAAU;YACZ,aAAiB,CAAA;YACjB,eAAiB,CAAA;YACjB,YAAiB,CAAA;YACjB,OAAiB,CAAA;YACjB,SAAiB,CAAA;YACjB,aAAiB,CAAA;YACjB,YAAiB;YACjB,gBAAiB,CAAA;YACjB;YACA;YACA,WAAiB;YACjB,cAAiB,CAAA;YACjB,UAAU;cACR,QAAQ,CAAA;cACR,KAAK;cACL,QAAQ;YACV;UACF;AAKA,iBAAO,KAAK,QAAO,MAAO,IAAG;AAK7B,iBAAO,KAAK,QAAO,OAAQ,IAAG;AAG9B,iBAAO,KAAK,QAAO,SAAU,IAAG;AAChC,iBAAO,KAAK,QAAO,OAAQ,IAAG;AAG9B,iBAAO,KAAK,QAAO,WAAY,QAAM;AAErC,cAAI,QAAQ,qBAAqB;AAC/B,mBAAO,eAAe,IAAI;UAC5B;AAGA,iBAAO,SAAS,OAAO;AAGvB,iBAAO,SAAS,UAAS,OAAO,EAAG,MAAM,SAAS,OAAO;AAQzD,iBAAO,KAAK,QAAO,cAAe,EAAA;AAGlC,mBAAS,OAAO,QAAQ,gBAAgB,SAAU,KAAK;AACrD,mBAAO,SAAS,UAAS,cAAc,EAAG,KAAK,MAAM,SAAS,OAAO;UACvE,CAAA;AAGA,iBAAO,SAAS,UAAS,UAAU,EAAG,MAAM,SAAS,OAAO;AAC5D,iBAAO,SAAS,UAAS,iBAAiB,EAAG,MAAM,SAAS,OAAO;AACnE,iBAAO,SAAS,UAAS,kBAAkB,EAAG,MAAM,SAAS,OAAO;AACpE,iBAAO,SAAS,UAAS,gBAAgB,EAAG,MAAM,SAAS,OAAO;AAClE,iBAAO,SAAS,UAAS,cAAc,EAAG,MAAM,SAAS,OAAO;AAChE,iBAAO,SAAS,UAAS,sBAAsB,EAAG,MAAM,SAAS,OAAO;AACxE,iBAAO,SAAS,UAAS,YAAY,EAAG,MAAM,SAAS,OAAO;AAC9D,iBAAO,SAAS,UAAS,iBAAiB,EAAG,MAAM,SAAS,OAAO;AACnE,iBAAO,SAAS,UAAS,sBAAsB,EAAG,MAAM,SAAS,OAAO;AAGxE,iBAAO,KAAK,QAAO,OAAQ,IAAA;AAG3B,iBAAO,KAAK,QAAO,OAAQ,GAAA;AAG3B,iBAAO,SAAS,UAAS,sBAAsB,EAAG,MAAM,SAAS,OAAO;AAGxE,mBAAS,OAAO,QAAQ,iBAAiB,SAAU,KAAK;AACtD,mBAAO,SAAS,UAAS,cAAc,EAAG,KAAK,MAAM,SAAS,OAAO;UACvE,CAAA;AAGA,qBAAW,QAAQ;AACnB,iBAAO;QACT;AAQA,aAAK,eAAe,KAAK,SAAS,SAAU,KAAK,YAAY;AAG3D,gBAAM,IAAI,QAAO,SAAU,IAAG;AAC9B,gBAAM,IAAI,QAAO,OAAQ,IAAG;AAK5B,gBAAM,IAAI,QAAO,YAAa,UAAO;AAErC,cAAG,CAAE,YAAY;AACf,gBAAI,UAAU,OAAO,UAAU;AAC7B,2BAAa,OAAO;YACtB,OAAO;AACL,oBAAM,IAAI,MAAK,2HAA2H;YAC5I;UACF;AAEA,cAAI,MAAM,WAAW,cAAa,KAAK;AACvC,cAAI,YAAY;AAEhB,cAAI,UAAU;YACZ,SAAS,sBAAsB,GAAG;UACpC;AAGA,gBAAM,GAAG;AAMT,cAAI,QAAQ,IAAI,YACZ,QAAQ;AAEZ,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,qBAAS,SAAS,UAAS,mBAAmB,EAAG,MAAM,CAAC,GAAG,OAAO;UACpE;AAEA,mBAAS,MAAO,MAAM;AACpB,qBAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,EAAE,GAAG;AAC/C,kBAAI,QAAQ,KAAK,WAAW,CAAC;AAC7B,kBAAI,MAAM,aAAa,GAAG;AACxB,oBAAG,CAAA,KAAO,KAAK,MAAM,SAAS,KAAK,CAAA,SAAU,KAAK,MAAM,SAAS,GAAG;AAClE,uBAAK,YAAY,KAAK;AACtB,oBAAE;gBACJ,OAAO;AACL,wBAAM,YAAY,MAAM,UAAU,MAAK,IAAI,EAAG,KAAI,GAAG;AACrD,wBAAM,YAAY,MAAM,UAAU,QAAO,UAAW,IAAG;gBACzD;cACF,WAAW,MAAM,aAAa,GAAG;AAC/B,sBAAM,KAAK;cACb;YACF;UACF;AAKA,mBAAS,sBAAuBC,MAAK;AAEnC,gBAAI,OAAOA,KAAI,iBAAgB,KAAK,GAChC,SAAS,CAAA;AAEb,qBAASC,KAAI,GAAGA,KAAI,KAAK,QAAQ,EAAEA,IAAG;AAEpC,kBAAI,KAAKA,EAAC,EAAE,sBAAsB,KAAK,KAAKA,EAAC,EAAE,WAAW,QAAQ,YAAW,MAAO,QAAQ;AAC1F,oBAAI,UAAU,KAAKA,EAAC,EAAE,WAAW,UAAU,KAAI,GAC3C,WAAW,KAAKA,EAAC,EAAE,WAAW,aAAY,eAAe,KAAM;AAGnE,oBAAI,aAAa,IAAI;AACnB,sBAAI,UAAU,KAAKA,EAAC,EAAE,WAAW,UAAU,MAAK,GAAG;AACnD,2BAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE,GAAG;AACvC,wBAAI,UAAU,QAAQ,CAAC,EAAE,MAAK,iBAAW;AACzC,wBAAI,YAAY,MAAM;AACpB,iCAAW,QAAQ,CAAC;AACpB;oBACF;kBACF;gBACF;AAGA,0BAAU,SAAS,OAAO,qBAAqB,OAAO;AAEtD,uBAAO,KAAK,OAAO;AACnB,qBAAKA,EAAC,EAAE,YAAY,wBAAwB,WAAW,mBAAmBA,GAAE,SAAQ,IAAK;cAC3F,OAAO;AACL,uBAAO,KAAK,KAAKA,EAAC,EAAE,SAAS;AAC7B,qBAAKA,EAAC,EAAE,YAAY;AACpB,qBAAKA,EAAC,EAAE,aAAY,UAAWA,GAAE,SAAQ,CAAA;cAC3C;YACF;AACA,mBAAO;UACT;AAEA,iBAAO;QACT;AAOA,aAAK,YAAY,SAAU,KAAK,OAAO;AACrC,kBAAQ,GAAG,IAAI;QACjB;AAOA,aAAK,YAAY,SAAU,KAAK;AAC9B,iBAAO,QAAQ,GAAG;QACpB;AAMA,aAAK,aAAa,WAAY;AAC5B,iBAAO;QACT;AAOA,aAAK,eAAe,SAAU,WAAW,MAAM;AAC7C,iBAAO,QAAQ;AACf,0BAAgB,WAAW,IAAI;QACjC;AAMA,aAAK,eAAe,SAAU,eAAe;AAC3C,0BAAgB,aAAa;QAC/B;AAMA,aAAK,YAAY,SAAU,MAAM;AAC/B,cAAG,CAAE,OAAO,eAAe,IAAI,GAAG;AAChC,kBAAM,MAAM,OAAO,uBAAsB;UAC3C;AACA,cAAI,SAAS,OAAO,IAAI;AACxB,0BAAgB;AAChB,mBAAS,UAAU,QAAQ;AACzB,gBAAI,OAAO,eAAe,MAAM,GAAG;AACjC,sBAAQ,MAAM,IAAI,OAAO,MAAM;YACjC;UACF;QACF;AAMA,aAAK,YAAY,WAAY;AAC3B,iBAAO;QACT;AAQA,aAAK,kBAAkB,SAAU,WAAW;AAC1C,cAAG,CAAE,SAAS,OAAO,QAAQ,SAAS,GAAG;AACvC,wBAAY,CAAC,SAAS;UACxB;AACA,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,EAAE,GAAG;AACzC,gBAAI,MAAM,UAAU,CAAC;AACrB,qBAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,EAAE,GAAG;AAC9C,kBAAI,eAAe,CAAC,MAAM,KAAK;AAC7B,+BAAe,OAAO,GAAG,CAAC;cAC5B;YACF;AACA,qBAAS,KAAK,GAAG,KAAK,gBAAgB,QAAQ,EAAE,IAAI;AAClD,kBAAI,gBAAgB,EAAE,MAAM,KAAK;AAC/B,gCAAgB,OAAO,IAAI,CAAC;cAC9B;YACF;UACF;QACF;AAMA,aAAK,mBAAmB,WAAY;AAClC,iBAAO;YACL,UAAU;YACV,QAAQ;UACV;QACF;AAOA,aAAK,cAAc,SAAU,KAAK;AAChC,cAAI,KAAK;AACP,mBAAO,SAAS;UAClB,OAAO;AACL,mBAAO,SAAS;UAClB;QACF;AAMA,aAAK,oBAAoB,WAAY;AACnC,iBAAO,SAAS;QAClB;AAOA,aAAK,mBAAmB,SAAU,KAAK,OAAO;AAC5C,mBAAS,OAAO,GAAG,IAAI;QACzB;AAMA,aAAK,qBAAqB,SAAU,QAAQ;AAC1C,mBAAS,SAAS;QACpB;AAMA,aAAK,kBAAkB,SAAU,KAAK;AACpC,mBAAS,MAAM;QACjB;MACF;ACtlBA,eAAS,UAAS,WAAY,SAAU,MAAM,SAAS,SAAS;AAC9D;AAEA,eAAO,QAAQ,UAAU,UAAS,kBAAmB,MAAM,SAAS,OAAO;AAE3E,YAAI,iBAAiB,SAAU,YAAY,UAAU,QAAQ,KAAK,IAAI,IAAI,OAAO;AAC/E,cAAI,SAAS,OAAO,YAAY,KAAK,GAAG;AACtC,oBAAQ;UACV;AACA,mBAAS,OAAO,YAAW;AAG3B,cAAI,WAAW,OAAM,8BAA+B,IAAI,IAAI;AAC1D,kBAAM;UACR,WAAU,CAAE,KAAK;AACf,gBAAG,CAAE,QAAQ;AAEX,uBAAS,SAAS,YAAW,EAAG,QAAO,SAAU,GAAE;YACrD;AACA,kBAAM,MAAM;AAEZ,gBAAG,CAAE,SAAS,OAAO,YAAY,QAAQ,MAAM,MAAM,CAAA,GAAI;AACvD,oBAAM,QAAQ,MAAM,MAAM;AAC1B,kBAAG,CAAE,SAAS,OAAO,YAAY,QAAQ,QAAQ,MAAM,CAAA,GAAI;AACzD,wBAAQ,QAAQ,QAAQ,MAAM;cAChC;YACF,OAAO;AACL,qBAAO;YACT;UACF;AAGA,gBAAM,IAAI,QAAQ,SAAS,OAAO,QAAQ,sBAAsB,SAAS,OAAO,wBAAwB;AAExG,cAAI,SAAS,cAAc,MAAM;AAEjC,cAAI,UAAU,MAAM,UAAU,MAAM;AAClC,oBAAQ,MAAM,QAAO,MAAO,QAAM;AAElC,oBAAQ,MAAM,QAAQ,SAAS,OAAO,QAAQ,sBAAsB,SAAS,OAAO,wBAAwB;AAC5G,sBAAU,aAAa,QAAQ;UACjC;AAIA,cAAI,QAAQ,wBAAwB,CAAA,KAAM,KAAK,GAAG,GAAG;AAEnD,sBAAU;UACZ;AAEA,oBAAU,MAAM,WAAW;AAE3B,iBAAO;QACT;AAGA,eAAO,KAAK,QAAO,2DAA4D,cAAc;AAI7F,eAAO,KAAK;UAAO;UACjB;QAAc;AAGhB,eAAO,KAAK;UAAO;UACjB;QAAc;AAKhB,eAAO,KAAK,QAAO,4BAA6B,cAAc;AAG9D,YAAI,QAAQ,YAAY;AACtB,iBAAO,KAAK,QAAO,uDAAwD,SAAU,IAAI,IAAI,QAAQ,UAAU,UAAU;AACvH,gBAAI,WAAW,MAAM;AACnB,qBAAO,KAAK;YACd;AAGA,gBAAG,CAAE,SAAS,OAAO,SAAS,QAAQ,cAAc,GAAG;AACrD,oBAAM,IAAI,MAAK,wCAAwC;YACzD;AACA,gBAAI,MAAM,QAAQ,eAAe,QAAO,SAAU,QAAQ,GACtD,SAAS;AACb,gBAAI,QAAQ,sBAAsB;AAChC,uBAAS;YACX;AACA,mBAAO,KAAK,cAAc,MAAM,MAAM,SAAS,MAAM,WAAW;UAClE,CAAA;QACF;AAEA,eAAO,QAAQ,UAAU,UAAS,iBAAkB,MAAM,SAAS,OAAO;AAC1E,eAAO;MACT,CAAA;AC/FA,UAAI,iBAAkB,+FAClB,kBAAkB,2GAClB,gBAAkB,uDAClB,kBAAkB,qGAClB,iBAAkB,iEAElB,cAAc,SAAU,SAAS;AAC/B;AACA,eAAO,SAAU,IAAI,mBAAmB,MAAM,IAAI,IAAI,qBAAqB,oBAAoB;AAC7F,iBAAO,KAAK,QAAQ,SAAS,OAAO,QAAQ,sBAAsB,SAAS,OAAO,wBAAwB;AAC1G,cAAI,SAAS,MACT,SAAS,IACT,SAAS,IACT,MAAS,qBAAqB,IAC9B,MAAS,sBAAsB;AACnC,cAAG,UAAW,KAAK,IAAI,GAAG;AACxB,mBAAO,KAAK,QAAO,WAAY,aAAW;UAC5C;AACA,cAAI,QAAQ,sCAAsC,qBAAqB;AACrE,qBAAS;UACX;AACA,cAAI,QAAQ,sBAAsB;AAChC,qBAAS;UACX;AACA,iBAAO,MAAM,cAAc,OAAO,MAAM,SAAS,MAAM,SAAS,SAAS,SAAS;QACpF;MACF,GAEA,cAAc,SAAU,SAAS,SAAS;AACxC;AACA,eAAO,SAAU,YAAY,GAAG,MAAM;AACpC,cAAI,OAAO;AACX,cAAI,KAAK;AACT,iBAAO,SAAS,UAAS,sBAAsB,EAAG,MAAM,SAAS,OAAO;AACxE,cAAI,QAAQ,cAAc;AACxB,mBAAO,SAAS,OAAO,mBAAmB,OAAO,IAAI;AACrD,mBAAO,SAAS,OAAO,mBAAmB,IAAI;UAChD,OAAO;AACL,mBAAO,OAAO;UAChB;AACA,iBAAO,IAAI,cAAc,OAAO,OAAO,OAAO;QAChD;MACF;AAEJ,eAAS,UAAS,aAAc,SAAU,MAAM,SAAS,SAAS;AAChE;AAEA,eAAO,QAAQ,UAAU,UAAS,oBAAqB,MAAM,SAAS,OAAO;AAE7E,eAAO,KAAK,QAAQ,eAAe,YAAY,OAAO,CAAA;AACtD,eAAO,KAAK,QAAQ,gBAAgB,YAAY,SAAS,OAAO,CAAA;AAEhE,eAAO,QAAQ,UAAU,UAAS,mBAAoB,MAAM,SAAS,OAAO;AAE5E,eAAO;MACT,CAAA;AAEA,eAAS,UAAS,uBAAwB,SAAU,MAAM,SAAS,SAAS;AAC1E;AAEA,YAAG,CAAE,QAAQ,oBAAoB;AAC/B,iBAAO;QACT;AAEA,eAAO,QAAQ,UAAU,UAAS,8BAA+B,MAAM,SAAS,OAAO;AAEvF,YAAI,QAAQ,oCAAoC;AAC9C,iBAAO,KAAK,QAAQ,iBAAiB,YAAY,OAAO,CAAA;QAC1D,OAAO;AACL,iBAAO,KAAK,QAAQ,gBAAgB,YAAY,OAAO,CAAA;QACzD;AACA,eAAO,KAAK,QAAQ,iBAAiB,YAAY,SAAS,OAAO,CAAA;AAEjE,eAAO,QAAQ,UAAU,UAAS,6BAA8B,MAAM,SAAS,OAAO;AAEtF,eAAO;MACT,CAAA;AC1EA,eAAS,UAAS,cAAe,SAAU,MAAM,SAAS,SAAS;AACjE;AAEA,eAAO,QAAQ,UAAU,UAAS,qBAAsB,MAAM,SAAS,OAAO;AAI9E,eAAO,SAAS,UAAS,aAAa,EAAG,MAAM,SAAS,OAAO;AAC/D,eAAO,SAAS,UAAS,SAAS,EAAG,MAAM,SAAS,OAAO;AAG3D,eAAO,SAAS,UAAS,gBAAgB,EAAG,MAAM,SAAS,OAAO;AAElE,eAAO,SAAS,UAAS,OAAO,EAAG,MAAM,SAAS,OAAO;AACzD,eAAO,SAAS,UAAS,YAAY,EAAG,MAAM,SAAS,OAAO;AAC9D,eAAO,SAAS,UAAS,QAAQ,EAAG,MAAM,SAAS,OAAO;AAM1D,eAAO,SAAS,UAAS,gBAAgB,EAAG,MAAM,SAAS,OAAO;AAClE,eAAO,SAAS,UAAS,YAAY,EAAG,MAAM,SAAS,OAAO;AAE9D,eAAO,QAAQ,UAAU,UAAS,oBAAqB,MAAM,SAAS,OAAO;AAE7E,eAAO;MACT,CAAA;AC/BA,eAAS,UAAS,eAAgB,SAAU,MAAM,SAAS,SAAS;AAClE;AAEA,eAAO,QAAQ,UAAU,UAAS,sBAAuB,MAAM,SAAS,OAAO;AAG/E,eAAO,OAAO;AAEd,YAAI,MAAM;AAEV,YAAI,QAAQ,0BAA0B;AACpC,gBAAM;QACR;AAEA,eAAO,KAAK,QAAQ,KAAK,SAAU,IAAI;AAGrC,eAAK,GAAG,QAAO,oBAAqB,EAAA;AAGpC,eAAK,GAAG,QAAO,OAAQ,EAAA;AAEvB,eAAK,GAAG,QAAO,cAAe,EAAA;AAC9B,eAAK,SAAS,UAAS,kBAAkB,EAAG,IAAI,SAAS,OAAO;AAChE,eAAK,SAAS,UAAS,YAAY,EAAG,IAAI,SAAS,OAAO;AAE1D,eAAK,GAAG,QAAO,WAAY,MAAK;AAEhC,eAAK,GAAG,QAAO,8BAA+B,SAAU,YAAY,IAAI;AACtE,gBAAI,MAAM;AAEV,kBAAM,IAAI,QAAO,SAAU,IAAG;AAC9B,kBAAM,IAAI,QAAO,OAAQ,EAAA;AACzB,mBAAO;UACT,CAAA;AAEA,iBAAO,SAAS,UAAS,WAAW,EAAA,mBAAsB,KAAK,mBAAmB,SAAS,OAAO;QACpG,CAAA;AAEA,eAAO,QAAQ,UAAU,UAAS,qBAAsB,MAAM,SAAS,OAAO;AAC9E,eAAO;MACT,CAAA;ACtCA,eAAS,UAAS,cAAe,SAAU,MAAM,SAAS,SAAS;AACjE;AAEA,eAAO,QAAQ,UAAU,UAAS,qBAAsB,MAAM,SAAS,OAAO;AAG9E,gBAAQ;AAER,YAAI,UAAU;AACd,eAAO,KAAK,QAAQ,SAAS,SAAU,YAAY,IAAI,IAAI;AACzD,cAAI,YAAY,IACZ,WAAW,IACX,MAAM;AAEV,sBAAY,SAAS,UAAS,SAAS,EAAG,WAAW,SAAS,OAAO;AACrE,sBAAY,SAAS,UAAS,YAAY,EAAG,WAAW,SAAS,OAAO;AACxE,sBAAY,SAAS,UAAS,OAAO,EAAG,WAAW,SAAS,OAAO;AACnE,sBAAY,UAAU,QAAO,SAAU,EAAA;AACvC,sBAAY,UAAU,QAAO,SAAU,EAAA;AAEvC,cAAI,QAAQ,yBAAyB;AACnC,kBAAM;UACR;AAEA,sBAAY,gBAAgB,YAAY,MAAM;AAE9C,iBAAO,SAAS,UAAS,WAAW,EAAG,WAAW,SAAS,OAAO,IAAI;QACxE,CAAA;AAGA,eAAO,KAAK,QAAO,MAAO,EAAA;AAE1B,eAAO,QAAQ,UAAU,UAAS,oBAAqB,MAAM,SAAS,OAAO;AAC7E,eAAO;MACT,CAAA;ACZA,eAAS,UAAS,aAAc,SAAU,MAAM,SAAS,SAAS;AAChE;AAEA,eAAO,QAAQ,UAAU,UAAS,oBAAqB,MAAM,SAAS,OAAO;AAE7E,YAAI,OAAQ,SAAU,aAAa;AACjC,iBAAO;QACT;AACA,eAAO,KAAK;UAAO;UACjB,SAAU,YAAY,IAAI,IAAI,IAAI;AAChC,gBAAI,IAAI;AACR,gBAAI,EAAE,QAAO,cAAe,EAAA;AAC5B,gBAAI,EAAE,QAAO,YAAa,EAAA;AAC1B,gBAAI,SAAS,UAAS,YAAY,EAAG,GAAG,SAAS,OAAO;AACxD,gBAAI,KAAK,WAAW,IAAI;AACxB,gBAAI,SAAS,UAAS,eAAe,EAAG,GAAG,SAAS,OAAO;AAC3D,mBAAO;UACT;QACF;AAEA,eAAO,QAAQ,UAAU,UAAS,mBAAoB,MAAM,SAAS,OAAO;AAC5E,eAAO;MACT,CAAA;AC5CA,eAAS,UAAS,wBAAyB,SAAU,MAAM,SAAS,SAAS;AAC3E;AAEA,YAAG,CAAE,QAAQ,sBAAsB;AACjC,iBAAO;QACT;AAEA,eAAO,QAAQ,UAAU,UAAS,+BAAgC,MAAM,SAAS,OAAO;AAExF,YAAI,UAAU,QACV,gBAAgB,qBAChB,QAAQ,IACR,UAAU,4BACV,OAAO,IACP,WAAW;AAEf,YAAI,OAAO,QAAQ,SAAS,OAAO,YAAY,aAAa;AAC1D,0BAAgB,eAAgB,QAAQ,SAAS,OAAO,UAAU;AAClE,oBAAU,QAAQ,SAAS,OAAO,QAAQ,SAAQ,EAAG,YAAW;AAChE,cAAI,YAAY,UAAU,YAAY,SAAS;AAC7C,sBAAU;UACZ;QACF;AAEA,iBAAS,QAAQ,QAAQ,SAAS,QAAQ;AACxC,cAAI,QAAQ,SAAS,OAAO,eAAe,IAAI,GAAG;AAChD,oBAAQ,KAAK,YAAW,GAAI;cAC1B,KAAK;AACH;cAEF,KAAK;AACH,wBAAQ,YAAa,QAAQ,SAAS,OAAO,QAAQ;AACrD;cAEF,KAAK;AACH,oBAAI,YAAY,UAAU,YAAY,SAAS;AAC7C,4BAAU,oBAAoB,QAAQ,SAAS,OAAO,UAAU;gBAClE,OAAO;AACL,4BAAU,mCAAmC,QAAQ,SAAS,OAAO,UAAU;gBACjF;AACA;cAEF,KAAK;cACL,KAAK;AACH,uBAAO,YAAY,QAAQ,SAAS,OAAO,IAAI,IAAI;AACnD,4BAAY,iBAAiB,OAAO,gBAAgB,QAAQ,SAAS,OAAO,IAAI,IAAI;AACpF;cAEF;AACE,4BAAY,iBAAiB,OAAO,gBAAgB,QAAQ,SAAS,OAAO,IAAI,IAAI;YACxF;UACF;QACF;AAEA,eAAO,gBAAgB,UAAU,OAAO,gBAAgB,QAAQ,UAAU,WAAW,sBAAsB,KAAK,KAAI,IAAK;AAEzH,eAAO,QAAQ,UAAU,UAAS,8BAA+B,MAAM,SAAS,OAAO;AACvF,eAAO;MACT,CAAA;AC1DA,eAAS,UAAS,SAAU,SAAU,MAAM,SAAS,SAAS;AAC5D;AACA,eAAO,QAAQ,UAAU,UAAS,gBAAiB,MAAM,SAAS,OAAO;AAGzE,eAAO,KAAK,QAAO,aAAc,MAAK;AAGtC,eAAO,KAAK,QAAO,OAAQ,MAAK;AAGhC,eAAO,KAAK,QAAO,cAAe,SAAU,YAAY,IAAI;AAC1D,cAAI,cAAc,IACd,YAAY,IAAI,YAAY,SAAS;AAGzC,mBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,2BAAe;UACjB;AAEA,iBAAO;QACT,CAAA;AAGA,eAAO,KAAK,QAAO,OAAQ,MAAK;AAChC,eAAO,KAAK,QAAO,OAAQ,EAAA;AAE3B,eAAO,QAAQ,UAAU,UAAS,eAAgB,MAAM,SAAS,OAAO;AACxE,eAAO;MACT,CAAA;AChCA,eAAS,UAAS,YAAa,SAAU,MAAM,SAAS,SAAS;AAC/D;AAEA,YAAG,CAAE,QAAQ,UAAU;AACrB,iBAAO;QACT;AAEA,eAAO,QAAQ,UAAU,UAAS,mBAAoB,MAAM,SAAS,OAAO;AAE5E,eAAO,KAAK,QAAO,WAAY,GAAA;AAE/B,eAAO,QAAQ,UAAU,UAAS,kBAAmB,MAAM,SAAS,OAAO;AAE3E,eAAO;MACT,CAAA;ACTA,eAAS,UAAS,SAAU,SAAU,MAAM,SAAS,SAAS;AAC5D;AAEA,YAAG,CAAE,QAAQ,OAAO;AAClB,iBAAO;QACT;AAEA,eAAO,QAAQ,UAAU,UAAS,gBAAiB,MAAM,SAAS,OAAO;AAEzE,YAAI,WAAW;AAEf,eAAO,KAAK,QAAQ,UAAU,SAAU,IAAI,WAAW;AACrD,cAAI,SAAS,OAAO,OAAO,eAAe,SAAS,GAAG;AACpD,mBAAO,SAAS,OAAO,OAAO,SAAS;UACzC;AACA,iBAAO;QACT,CAAA;AAEA,eAAO,QAAQ,UAAU,UAAS,eAAgB,MAAM,SAAS,OAAO;AAExE,eAAO;MACT,CAAA;ACvBA,eAAS,UAAS,uBAAwB,SAAU,MAAM,SAAS,SAAS;AAC1E;AACA,eAAO,QAAQ,UAAU,UAAS,8BAA+B,MAAM,SAAS,OAAO;AAIvF,eAAO,KAAK,QAAO,sCAAuC,OAAK;AAG/D,eAAO,KAAK,QAAO,qBAAsB,MAAI;AAG7C,eAAO,KAAK,QAAO,MAAO,MAAI;AAG9B,eAAO,KAAK,QAAO,MAAO,MAAI;AAE9B,eAAO,QAAQ,UAAU,UAAS,6BAA8B,MAAM,SAAS,OAAO;AACtF,eAAO;MACT,CAAA;ACXA,eAAS,UAAS,0BAA2B,SAAU,MAAM,SAAS,SAAS;AAC7E;AACA,eAAO,QAAQ,UAAU,UAAS,iCAAkC,MAAM,SAAS,OAAO;AAE1F,eAAO,KAAK,QAAO,WAAY,SAAS,OAAO,wBAAwB;AACvE,eAAO,KAAK,QAAO,gCAAiC,SAAS,OAAO,wBAAwB;AAE5F,eAAO,QAAQ,UAAU,UAAS,gCAAiC,MAAM,SAAS,OAAO;AACzF,eAAO;MACT,CAAA;ACfA,eAAS,UAAS,cAAe,SAAU,MAAM,SAAS,SAAS;AACjE;AAEA,eAAO,QAAQ,UAAU,UAAS,qBAAsB,MAAM,SAAS,OAAO;AAI9E,eAAO,KACJ,QAAO,MAAO,OAAK,EAEnB,QAAO,MAAO,MAAI,EAClB,QAAO,MAAO,MAAI,EAElB,QAAO,sBAAuB,SAAS,OAAO,wBAAwB;AAEzE,eAAO,QAAQ,UAAU,UAAS,oBAAqB,MAAM,SAAS,OAAO;AAC7E,eAAO;MACT,CAAA;AClBA,eAAS,UAAS,yCAA0C,SAAU,MAAM,SAAS,SAAS;AAC5F;AACA,eAAO,QAAQ,UAAU,UAAS,gDAAiD,MAAM,SAAS,OAAO;AAGzG,YAAI,OAAW,wCACX,WAAW;AAEf,eAAO,KAAK,QAAQ,MAAM,SAAU,YAAY;AAC9C,iBAAO,WACJ,QAAO,sBAAuB,KAAG,EACjC,QAAO,iBAAkB,SAAS,OAAO,wBAAwB;QACtE,CAAA;AAEA,eAAO,KAAK,QAAQ,UAAU,SAAU,YAAY;AAClD,iBAAO,WACJ,QAAO,iBAAkB,SAAS,OAAO,wBAAwB;QACtE,CAAA;AAEA,eAAO,QAAQ,UAAU,UAAS,+CAAgD,MAAM,SAAS,OAAO;AACxG,eAAO;MACT,CAAA;ACfA,eAAS,UAAS,oBAAqB,SAAU,MAAM,SAAS,SAAS;AACvE;AAGA,YAAG,CAAE,QAAQ,cAAc;AACzB,iBAAO;QACT;AAEA,eAAO,QAAQ,UAAU,UAAS,2BAA4B,MAAM,SAAS,OAAO;AAEpF,gBAAQ;AAER,eAAO,KAAK,QAAO,4EAA6E,SAAU,YAAY,OAAO,UAAU,WAAW;AAChJ,cAAI,MAAO,QAAQ,0BAA2B,KAAK;AAGnD,sBAAY,SAAS,UAAS,YAAY,EAAG,WAAW,SAAS,OAAO;AACxE,sBAAY,SAAS,UAAS,OAAO,EAAG,WAAW,SAAS,OAAO;AACnE,sBAAY,UAAU,QAAO,SAAU,EAAA;AACvC,sBAAY,UAAU,QAAO,SAAU,EAAA;AAEvC,sBAAY,gBAAgB,WAAW,aAAa,WAAW,eAAe,WAAW,MAAM,MAAM,MAAM,YAAY,MAAM;AAE7H,sBAAY,SAAS,UAAS,WAAW,EAAG,WAAW,SAAS,OAAO;AAKvE,iBAAO,YAAY,QAAQ,aAAa,KAAI,EAAE,MAAM,YAAY,UAAoB,CAAA,IAAK,KAAK;QAChG,CAAA;AAGA,eAAO,KAAK,QAAO,MAAO,EAAA;AAE1B,eAAO,QAAQ,UAAU,UAAS,0BAA2B,MAAM,SAAS,OAAO;MACrF,CAAA;AC7CA,eAAS,UAAS,aAAc,SAAU,MAAM,SAAS,SAAS;AAChE;AACA,eAAO,QAAQ,UAAU,UAAS,oBAAqB,MAAM,SAAS,OAAO;AAC7E,eAAO,KAAK,QAAO,gBAAiB,EAAA;AACpC,eAAO,YAAY,QAAQ,YAAY,KAAK,IAAI,IAAI,KAAK;AACzD,eAAO,QAAQ,UAAU,UAAS,mBAAoB,MAAM,SAAS,OAAO;AAC5E,eAAO;MACT,CAAA;ACJA,eAAS,UAAS,gBAAiB,SAAU,MAAM,SAAS,SAAS;AACnE;AACA,eAAO,QAAQ,UAAU,UAAS,uBAAwB,MAAM,SAAS,OAAO;AAEhF,YAAI,UAAU,SAAU,YAAY,OAAO,MAAM,OAAO;AACtD,cAAI,YAAY,OAAO,SAAS,UAAS,YAAY,EAAG,OAAO,SAAS,OAAO,IAAI;AACnF,iBAAO,QAAQ,QAAQ,WAAW,KAAK,SAAS,IAAI,KAAK;QAC3D;AAGA,eAAO,SAAS,OAAO,uBAAuB,MAAM,SAAS,kBAAkB,WAAW,KAAI;AAE9F,eAAO,QAAQ,UAAU,UAAS,sBAAuB,MAAM,SAAS,OAAO;AAC/E,eAAO;MACT,CAAA;ACjBA,eAAS,UAAS,eAAgB,SAAU,MAAM,SAAS,SAAS;AAClE;AAEA,eAAO,SAAU,YAAY,IAAI;AAC/B,cAAI,YAAY;AAGhB,sBAAY,UAAU,QAAO,SAAU,IAAG;AAC1C,sBAAY,UAAU,QAAO,OAAQ,EAAA;AAGrC,sBAAY,UAAU,QAAO,SAAU,EAAA;AAGvC,sBAAY,YAAY,QAAQ,YAAY,KAAK,SAAS,IAAI,KAAK;AAEnE,iBAAO;QACT;MACF,CAAA;AClBA,eAAS,UAAS,kBAAmB,SAAU,MAAM,SAAS,SAAS;AACrE;AACA,eAAO,QAAQ,UAAU,UAAS,yBAA0B,MAAM,SAAS,OAAO;AAElF,YAAI,YAAY;UACV;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACF,GACA,UAAU,SAAU,YAAY,OAAO,MAAM,OAAO;AAClD,cAAI,MAAM;AAGV,cAAI,KAAK,OAAM,cAAc,MAAO,IAAI;AACtC,kBAAM,OAAO,QAAQ,UAAU,SAAS,KAAK,IAAI;UACnD;AACA,iBAAO,YAAY,QAAQ,YAAY,KAAK,GAAG,IAAI,KAAK;QAC1D;AAEJ,YAAI,QAAQ,0BAA0B;AAEpC,iBAAO,KAAK,QAAO,oBAAqB,SAAU,IAAI,QAAQ;AAC5D,mBAAO,SAAS,SAAS;UAC3B,CAAA;QACF;AAGA,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,EAAE,GAAG;AAEzC,cAAI,UACA,OAAW,IAAI,OAAM,cAAe,UAAU,CAAC,IAAI,cAAc,IAAG,GACpE,UAAW,MAAM,UAAU,CAAC,IAAI,aAChC,WAAW,OAAO,UAAU,CAAC,IAAI;AAErC,kBAAQ,WAAW,SAAS,OAAO,aAAa,MAAM,IAAI,OAAO,IAAI;AAMnE,gBAAI,WAAW,SAAS,OAAO,aAAa,MAAM,QAAQ,GAEtD,cAAc,SAAS,OAAO,uBAAuB,SAAS,CAAC,GAAG,SAAS,SAAS,UAAU,IAAG;AAGrG,gBAAI,gBAAgB,SAAS,CAAC,GAAG;AAC/B;YACF;AACA,mBAAO,SAAS,CAAC,EAAE,OAAO,WAAW;UACvC;QACF;AAEA,eAAO,KAAK;UAAO;UACjB,SAAS,UAAS,aAAa,EAAG,MAAM,SAAS,OAAO;QAAA;AAG1D,eAAO,SAAS,OAAO,uBAAuB,MAAM,SAAU,KAAK;AACjE,iBAAO,YAAY,QAAQ,YAAY,KAAK,GAAG,IAAI,KAAK;QAC1D,GAAG,eAAe,OAAO,IAAG;AAG5B,eAAO,KAAK;UAAO;UACjB,SAAS,UAAS,aAAa,EAAG,MAAM,SAAS,OAAO;QAAA;AAE1D,eAAO,QAAQ,UAAU,UAAS,wBAAyB,MAAM,SAAS,OAAO;AACjF,eAAO;MACT,CAAA;AC9FA,eAAS,UAAS,iBAAkB,SAAU,MAAM,SAAS,SAAS;AACpE;AACA,eAAO,QAAQ,UAAU,UAAS,wBAAyB,MAAM,SAAS,OAAO;AAEjF,iBAAS,aAAc,MAAM;AAC3B,iBAAO,QAAQ,QAAQ,WAAW,KAAK,IAAI,IAAI,KAAK;QACtD;AAGA,eAAO,KAAK,QAAO,gBAAiB,SAAU,IAAI;AAChD,iBAAO,aAAa,EAAE;QACxB,CAAA;AAGA,eAAO,KAAK,QAAO,6BAA8B,SAAU,IAAI;AAC7D,iBAAO,aAAa,EAAE;QACxB,CAAA;AAGA,eAAO,KAAK,QAAO,qCAAsC,SAAU,IAAI;AACrE,iBAAO,aAAa,EAAE;QACxB,CAAA;AAGA,eAAO,KAAK,QAAO,cAAe,SAAU,IAAI;AAC9C,iBAAO,aAAa,EAAE;QACxB,CAAA;AAIA,eAAO,QAAQ,UAAU,UAAS,uBAAwB,MAAM,SAAS,OAAO;AAChF,eAAO;MACT,CAAA;AAKA,eAAS,UAAS,mBAAoB,SAAU,MAAM,SAAS,SAAS;AACtE;AACA,eAAO,QAAQ,UAAU,UAAS,0BAA2B,MAAM,SAAS,OAAO;AAEnF,iBAAS,IAAI,GAAG,IAAI,QAAQ,WAAW,QAAQ,EAAE,GAAG;AAClD,cAAI,UAAU,QAAQ,WAAW,CAAC,GAE9B,QAAQ;AAEZ,iBAAM,WAAY,KAAK,OAAO,GAAG;AAC/B,gBAAI,MAAM,OAAM;AAChB,sBAAU,QAAQ,QAAO,OAAQ,MAAM,KAAK,QAAQ,WAAW,GAAG,CAAA;AAClE,gBAAI,UAAU,IAAI;AAChB,sBAAQ,MAAK,wCAAqC;AAClD;YACF;AACA,cAAE;UACJ;AACA,iBAAO,KAAK,QAAO,OAAQ,IAAI,KAAK,OAAO;QAC7C;AAEA,eAAO,QAAQ,UAAU,UAAS,yBAA0B,MAAM,SAAS,OAAO;AAClF,eAAO;MACT,CAAA;AC5DA,eAAS,UAAS,mBAAoB,SAAU,MAAM,SAAS,SAAS;AACtE;AACA,eAAO,QAAQ,UAAU,UAAS,0BAA2B,MAAM,SAAS,OAAO;AAEnF,YAAI,UAAU,SAAU,YAAY,OAAO,MAAM,OAAO;AAEtD,cAAI,YAAY,OAAO,SAAS,UAAS,YAAY,EAAG,OAAO,SAAS,OAAO,IAAI;AACnF,iBAAO,YAAY,QAAQ,aAAa,KAAI,EAAE,MAAM,YAAY,UAAoB,CAAA,IAAK,KAAK;QAChG;AAGA,eAAO,SAAS,OAAO,uBAAuB,MAAM,SAAS,0CAA0C,4BAA4B,KAAI;AAEvI,eAAO,QAAQ,UAAU,UAAS,yBAA0B,MAAM,SAAS,OAAO;AAClF,eAAO;MACT,CAAA;AClBA,eAAS,UAAS,WAAY,SAAU,MAAM,SAAS,SAAS;AAC9D;AAEA,eAAO,QAAQ,UAAU,UAAS,kBAAmB,MAAM,SAAS,OAAO;AAE3E,YAAI,mBAAoB,MAAM,SAAS,QAAQ,gBAAgB,CAAA,IAAM,IAAI,SAAS,QAAQ,gBAAgB,GAStG,gBAAiB,QAAQ,oBAAqB,kCAAkC,8BAChF,gBAAiB,QAAQ,oBAAqB,kCAAkC;AAEpF,eAAO,KAAK,QAAQ,eAAe,SAAU,YAAY,IAAI;AAE3D,cAAI,YAAY,SAAS,UAAS,WAAW,EAAG,IAAI,SAAS,OAAO,GAChE,MAAO,QAAQ,aAAc,KAAK,UAAU,SAAS,EAAE,IAAI,KAC3D,SAAS,kBACT,YAAY,OAAO,SAAS,MAAM,MAAM,YAAY,QAAQ,SAAS;AACzE,iBAAO,SAAS,UAAS,WAAW,EAAG,WAAW,SAAS,OAAO;QACpE,CAAA;AAEA,eAAO,KAAK,QAAQ,eAAe,SAAU,YAAY,IAAI;AAC3D,cAAI,YAAY,SAAS,UAAS,WAAW,EAAG,IAAI,SAAS,OAAO,GAChE,MAAO,QAAQ,aAAc,KAAK,UAAU,SAAS,EAAE,IAAI,KAC3D,SAAS,mBAAmB,GAC5B,YAAY,OAAO,SAAS,MAAM,MAAM,YAAY,QAAQ,SAAS;AACzE,iBAAO,SAAS,UAAS,WAAW,EAAG,WAAW,SAAS,OAAO;QACpE,CAAA;AASA,YAAI,WAAY,QAAQ,gCAAiC,sCAAsC;AAE/F,eAAO,KAAK,QAAQ,UAAU,SAAU,YAAY,IAAI,IAAI;AAC1D,cAAI,QAAQ;AACZ,cAAI,QAAQ,oBAAoB;AAC9B,oBAAQ,GAAG,QAAO,sBAAuB,EAAA;UAC3C;AAEA,cAAI,OAAO,SAAS,UAAS,WAAW,EAAG,OAAO,SAAS,OAAO,GAC9D,MAAO,QAAQ,aAAc,KAAK,UAAU,SAAS,EAAE,IAAI,KAC3D,SAAS,mBAAmB,IAAI,GAAG,QACnC,SAAS,OAAO,SAAS,MAAM,MAAM,OAAO,QAAQ,SAAS;AAEjE,iBAAO,SAAS,UAAS,WAAW,EAAG,QAAQ,SAAS,OAAO;QACjE,CAAA;AAEA,iBAAS,SAAU,GAAG;AACpB,cAAI,OACA;AAGJ,cAAI,QAAQ,oBAAoB;AAC9B,gBAAI,QAAQ,EAAE,MAAK,iBAAe;AAClC,gBAAI,SAAS,MAAM,CAAC,GAAG;AACrB,kBAAI,MAAM,CAAC;YACb;UACF;AAEA,kBAAQ;AAGR,cAAI,SAAS,OAAO,SAAS,QAAQ,cAAc,GAAG;AACpD,qBAAS,QAAQ;UACnB,WAAW,QAAQ,mBAAmB,MAAM;AAC1C,qBAAS;UACX,OAAO;AACL,qBAAS;UACX;AAEA,cAAG,CAAE,QAAQ,mBAAmB;AAC9B,oBAAQ,SAAS;UACnB;AAEA,cAAI,QAAQ,sBAAsB;AAChC,oBAAQ,MACL,QAAO,MAAO,GAAA,EAEd,QAAO,UAAW,EAAA,EAClB,QAAO,OAAQ,EAAA,EACf,QAAO,OAAQ,EAAA,EAGf,QAAO,0CAA2C,EAAA,EAClD,YAAW;UAChB,WAAW,QAAQ,aAAa;AAC9B,oBAAQ,MACL,QAAO,MAAO,GAAA,EAEd,QAAO,UAAW,GAAA,EAClB,QAAO,OAAQ,GAAA,EACf,QAAO,OAAQ,GAAA,EAEf,QAAO,SAAU,GAAA,EACjB,YAAW;UAChB,OAAO;AACL,oBAAQ,MACL,QAAO,UAAW,EAAA,EAClB,YAAW;UAChB;AAEA,cAAI,QAAQ,mBAAmB;AAC7B,oBAAQ,SAAS;UACnB;AAEA,cAAI,QAAQ,eAAe,KAAK,GAAG;AACjC,oBAAQ,QAAQ,MAAO,QAAQ,eAAe,KAAK;UACrD,OAAO;AACL,oBAAQ,eAAe,KAAK,IAAI;UAClC;AACA,iBAAO;QACT;AAEA,eAAO,QAAQ,UAAU,UAAS,iBAAkB,MAAM,SAAS,OAAO;AAC1E,eAAO;MACT,CAAA;AC1HA,eAAS,UAAS,kBAAmB,SAAU,MAAM,SAAS,SAAS;AACrE;AACA,eAAO,QAAQ,UAAU,UAAS,yBAA0B,MAAM,SAAS,OAAO;AAElF,YAAI,MAAM,SAAS,UAAS,WAAW,EAAA,UAAa,SAAS,OAAO;AACpE,eAAO,KAAK,QAAO,6BAA8B,GAAG;AACpD,eAAO,KAAK,QAAO,8BAA+B,GAAG;AACrD,eAAO,KAAK,QAAO,6BAA8B,GAAG;AAEpD,eAAO,QAAQ,UAAU,UAAS,wBAAyB,MAAM,SAAS,OAAO;AACjF,eAAO;MACT,CAAA;ACXA,eAAS,UAAS,UAAW,SAAU,MAAM,SAAS,SAAS;AAC7D;AAEA,eAAO,QAAQ,UAAU,UAAS,iBAAkB,MAAM,SAAS,OAAO;AAE1E,YAAI,eAAoB,0JACpB,cAAoB,sIACpB,eAAoB,sKACpB,kBAAoB,oDACpB,oBAAoB;AAExB,iBAAS,oBAAqB,YAAY,SAAS,QAAQ,KAAK,OAAO,QAAQ,IAAI,OAAO;AACxF,gBAAM,IAAI,QAAO,OAAQ,EAAA;AACzB,iBAAO,cAAe,YAAY,SAAS,QAAQ,KAAK,OAAO,QAAQ,IAAI,KAAK;QAClF;AAEA,iBAAS,cAAe,YAAY,SAAS,QAAQ,KAAK,OAAO,QAAQ,IAAI,OAAO;AAElF,cAAI,QAAU,QAAQ,OAClB,UAAU,QAAQ,SAClB,QAAU,QAAQ;AAEtB,mBAAS,OAAO,YAAW;AAE3B,cAAG,CAAE,OAAO;AACV,oBAAQ;UACV;AAEA,cAAI,WAAW,OAAM,8BAA+B,IAAI,IAAI;AAC1D,kBAAM;UAER,WAAW,QAAQ,MAAM,QAAQ,MAAM;AACrC,gBAAI,WAAW,MAAM,WAAW,MAAM;AAEpC,uBAAS,QAAQ,YAAW,EAAG,QAAO,SAAU,GAAE;YACpD;AACA,kBAAM,MAAM;AAEZ,gBAAG,CAAE,SAAS,OAAO,YAAY,MAAM,MAAM,CAAA,GAAI;AAC/C,oBAAM,MAAM,MAAM;AAClB,kBAAG,CAAE,SAAS,OAAO,YAAY,QAAQ,MAAM,CAAA,GAAI;AACjD,wBAAQ,QAAQ,MAAM;cACxB;AACA,kBAAG,CAAE,SAAS,OAAO,YAAY,MAAM,MAAM,CAAA,GAAI;AAC/C,wBAAQ,MAAM,MAAM,EAAE;AACtB,yBAAS,MAAM,MAAM,EAAE;cACzB;YACF,OAAO;AACL,qBAAO;YACT;UACF;AAEA,oBAAU,QACP,QAAO,MAAO,QAAM,EAEpB,QAAQ,SAAS,OAAO,QAAQ,sBAAsB,SAAS,OAAO,wBAAwB;AAEjG,gBAAM,IAAI,QAAQ,SAAS,OAAO,QAAQ,sBAAsB,SAAS,OAAO,wBAAwB;AACxG,cAAI,SAAS,eAAe,MAAM,YAAY,UAAU;AAExD,cAAI,SAAS,SAAS,OAAO,SAAS,KAAK,GAAG;AAC5C,oBAAQ,MACL,QAAO,MAAO,QAAM,EAEpB,QAAQ,SAAS,OAAO,QAAQ,sBAAsB,SAAS,OAAO,wBAAwB;AACjG,sBAAU,aAAa,QAAQ;UACjC;AAEA,cAAI,SAAS,QAAQ;AACnB,oBAAU,UAAU,MAAO,SAAS;AACpC,qBAAU,WAAW,MAAO,SAAS;AAErC,sBAAU,aAAa,QAAQ;AAC/B,sBAAU,cAAc,SAAS;UACnC;AAEA,oBAAU;AAEV,iBAAO;QACT;AAGA,eAAO,KAAK,QAAQ,iBAAiB,aAAa;AAKlD,eAAO,KAAK,QAAQ,cAAc,mBAAmB;AAGrD,eAAO,KAAK,QAAQ,aAAa,aAAa;AAG9C,eAAO,KAAK,QAAQ,cAAc,aAAa;AAG/C,eAAO,KAAK,QAAQ,mBAAmB,aAAa;AAEpD,eAAO,QAAQ,UAAU,UAAS,gBAAiB,MAAM,SAAS,OAAO;AACzE,eAAO;MACT,CAAA;ACvGA,eAAS,UAAS,kBAAmB,SAAU,MAAM,SAAS,SAAS;AACrE;AAEA,eAAO,QAAQ,UAAU,UAAS,yBAA0B,MAAM,SAAS,OAAO;AAMlF,iBAAS,YAAa,KAAK,MAAM,OAAO;AAMtC,iBAAO,OAAO,MAAM;QACtB;AAGA,YAAI,QAAQ,2BAA2B;AACrC,iBAAO,KAAK,QAAO,2BAA4B,SAAU,IAAI,KAAK;AAChE,mBAAO,YAAa,KAAK,gBAAgB,gBAAc;UACzD,CAAA;AACA,iBAAO,KAAK,QAAO,yBAA0B,SAAU,IAAI,KAAK;AAC9D,mBAAO,YAAa,KAAK,YAAY,WAAS;UAChD,CAAA;AACA,iBAAO,KAAK,QAAO,uBAAwB,SAAU,IAAI,KAAK;AAC5D,mBAAO,YAAa,KAAK,QAAQ,OAAK;UACxC,CAAA;QACF,OAAO;AACL,iBAAO,KAAK,QAAO,uBAAwB,SAAU,IAAI,GAAG;AAC1D,mBAAO,MAAO,KAAK,CAAC,IAAK,YAAa,GAAG,gBAAgB,gBAAc,IAAM;UAC/E,CAAA;AACA,iBAAO,KAAK,QAAO,qBAAsB,SAAU,IAAI,GAAG;AACxD,mBAAO,MAAO,KAAK,CAAC,IAAK,YAAa,GAAG,YAAY,WAAS,IAAM;UACtE,CAAA;AACA,iBAAO,KAAK,QAAO,uBAAwB,SAAU,IAAI,GAAG;AAE1D,mBAAO,MAAO,KAAK,CAAC,IAAK,YAAa,GAAG,QAAQ,OAAK,IAAM;UAC9D,CAAA;QACF;AAGA,YAAI,QAAQ,yBAAyB;AACnC,iBAAO,KAAK,QAAO,+CAAgD,SAAU,IAAI,MAAM,KAAK;AAC1F,mBAAO,YAAa,KAAK,OAAO,gBAAgB,gBAAc;UAChE,CAAA;AACA,iBAAO,KAAK,QAAO,2CAA4C,SAAU,IAAI,MAAM,KAAK;AACtF,mBAAO,YAAa,KAAK,OAAO,YAAY,WAAS;UACvD,CAAA;AACA,iBAAO,KAAK,QAAO,uCAAwC,SAAU,IAAI,MAAM,KAAK;AAClF,mBAAO,YAAa,KAAK,OAAO,QAAQ,OAAK;UAC/C,CAAA;QACF,OAAO;AACL,iBAAO,KAAK,QAAO,6BAA8B,SAAU,IAAI,GAAG;AAChE,mBAAO,MAAO,KAAK,CAAC,IAAK,YAAa,GAAG,gBAAgB,gBAAc,IAAM;UAC/E,CAAA;AACA,iBAAO,KAAK,QAAO,yBAA0B,SAAU,IAAI,GAAG;AAC5D,mBAAO,MAAO,KAAK,CAAC,IAAK,YAAa,GAAG,YAAY,WAAS,IAAM;UACtE,CAAA;AACA,iBAAO,KAAK,QAAO,yBAA0B,SAAU,IAAI,GAAG;AAE5D,mBAAO,MAAO,KAAK,CAAC,IAAK,YAAa,GAAG,QAAQ,OAAK,IAAM;UAC9D,CAAA;QACF;AAGA,eAAO,QAAQ,UAAU,UAAS,wBAAyB,MAAM,SAAS,OAAO;AACjF,eAAO;MACT,CAAA;AClEA,eAAS,UAAS,SAAU,SAAU,MAAM,SAAS,SAAS;AAC5D;AASA,iBAAS,iBAAkB,SAAS,cAAc;AAqBhD,kBAAQ;AAGR,oBAAU,QAAQ,QAAO,WAAY,IAAG;AAGxC,qBAAW;AAEX,cAAI,MAAM,oHACN,gBAAgB,mBAAoB,KAAK,OAAO;AAKpD,cAAI,QAAQ,sCAAsC;AAChD,kBAAM;UACR;AAEA,oBAAU,QAAQ,QAAQ,KAAK,SAAU,YAAY,IAAI,IAAI,IAAI,IAAI,SAAS,SAAS;AACrF,sBAAW,WAAW,QAAQ,KAAI,MAAO;AAEzC,gBAAI,OAAO,SAAS,UAAS,SAAS,EAAG,IAAI,SAAS,OAAO,GACzD,cAAc;AAGlB,gBAAI,WAAW,QAAQ,WAAW;AAChC,4BAAc;AACd,qBAAO,KAAK,QAAO,uBAAwB,WAAY;AACrD,oBAAI,MAAM;AACV,oBAAI,SAAS;AACX,yBAAO;gBACT;AACA,uBAAO;AACP,uBAAO;cACT,CAAA;YACF;AAUA,mBAAO,KAAK,QAAO,gCAAiC,SAAU,KAAK;AACjE,qBAAO,OAAO;YAChB,CAAA;AAKA,gBAAI,MAAO,KAAK,OAAM,QAAM,IAAO,IAAK;AACtC,qBAAO,SAAS,UAAS,kBAAkB,EAAG,MAAM,SAAS,OAAO;AACpE,qBAAO,SAAS,UAAS,YAAY,EAAG,MAAM,SAAS,OAAO;YAChE,OAAO;AAEL,qBAAO,SAAS,UAAS,OAAO,EAAG,MAAM,SAAS,OAAO;AACzD,qBAAO,KAAK,QAAO,OAAQ,EAAA;AAC3B,qBAAO,SAAS,UAAS,gBAAgB,EAAG,MAAM,SAAS,OAAO;AAGlE,qBAAO,KAAK,QAAO,UAAW,MAAK;AACnC,kBAAI,eAAe;AACjB,uBAAO,SAAS,UAAS,YAAY,EAAG,MAAM,SAAS,OAAO;cAChE,OAAO;AACL,uBAAO,SAAS,UAAS,WAAW,EAAG,MAAM,SAAS,OAAO;cAC/D;YACF;AAGA,mBAAO,KAAK,QAAO,MAAO,EAAA;AAE1B,mBAAQ,QAAQ,cAAc,MAAM,OAAO;AAE3C,mBAAO;UACT,CAAA;AAGA,oBAAU,QAAQ,QAAO,OAAQ,EAAA;AAEjC,kBAAQ;AAER,cAAI,cAAc;AAChB,sBAAU,QAAQ,QAAO,QAAS,EAAA;UACpC;AAEA,iBAAO;QACT;AAEA,iBAAS,iBAAkB,MAAM,UAAU;AAEzC,cAAI,aAAa,MAAM;AACrB,gBAAI,MAAM,KAAK,MAAK,YAAQ;AAC5B,gBAAI,OAAO,IAAI,CAAC,MAAM,KAAK;AACzB,qBAAO,aAAa,IAAI,CAAC,IAAI;YAC/B;UACF;AACA,iBAAO;QACT;AASA,iBAAS,sBAAuB,MAAM,UAAU,cAAc;AAG5D,cAAI,QAAS,QAAQ,uCAAwC,oBAAoB,uBAC7E,QAAS,QAAQ,uCAAwC,oBAAoB,uBAC7E,aAAc,aAAa,OAAQ,QAAQ,OAC3C,SAAS;AAEb,cAAI,KAAK,OAAO,UAAU,MAAM,IAAI;AAClC,aAAC,SAAS,QAAS,KAAK;AACtB,kBAAI,MAAM,IAAI,OAAO,UAAU,GAC3BC,SAAQ,iBAAiB,MAAM,QAAQ;AAC3C,kBAAI,QAAQ,IAAI;AAEd,0BAAU,UAAU,WAAWA,SAAQ,QAAQ,iBAAiB,IAAI,MAAM,GAAG,GAAG,GAAG,CAAA,CAAE,YAAY,IAAI,OAAO,WAAW;AAGvH,2BAAY,aAAa,OAAQ,OAAO;AACxC,6BAAc,aAAa,OAAQ,QAAQ;AAG3C,wBAAQ,IAAI,MAAM,GAAG,CAAA;cACvB,OAAO;AACL,0BAAU,UAAU,WAAWA,SAAQ,QAAQ,iBAAiB,KAAK,CAAA,CAAE,YAAY,IAAI,OAAO,WAAW;cAC3G;YACF,GAAG,IAAI;UACT,OAAO;AACL,gBAAI,QAAQ,iBAAiB,MAAM,QAAQ;AAC3C,qBAAS,UAAU,WAAW,QAAQ,QAAQ,iBAAiB,MAAM,CAAA,CAAE,YAAY,IAAI,OAAO,WAAW;UAC3G;AAEA,iBAAO;QACT;AAGA,eAAO,QAAQ,UAAU,UAAS,gBAAiB,MAAM,SAAS,OAAO;AAGzE,gBAAQ;AAER,YAAI,QAAQ,YAAY;AACtB,iBAAO,KAAK;YAAO;YACjB,SAAU,YAAY,MAAM,IAAI;AAC9B,kBAAI,WAAY,GAAG,OAAM,QAAS,IAAI,KAAM,OAAO;AACnD,qBAAO,sBAAsB,MAAM,UAAU,IAAI;YACnD;UACF;QACF,OAAO;AACL,iBAAO,KAAK;YAAO;YACjB,SAAU,YAAY,IAAI,MAAM,IAAI;AAClC,kBAAI,WAAY,GAAG,OAAM,QAAS,IAAI,KAAM,OAAO;AACnD,qBAAO,sBAAsB,MAAM,UAAU,KAAK;YACpD;UACF;QACF;AAGA,eAAO,KAAK,QAAO,MAAO,EAAA;AAC1B,eAAO,QAAQ,UAAU,UAAS,eAAgB,MAAM,SAAS,OAAO;AACxE,eAAO;MACT,CAAA;ACvMA,eAAS,UAAS,YAAa,SAAU,MAAM,SAAS,SAAS;AAC/D;AAEA,YAAG,CAAE,QAAQ,UAAU;AACrB,iBAAO;QACT;AAEA,eAAO,QAAQ,UAAU,UAAS,mBAAoB,MAAM,SAAS,OAAO;AAE5E,iBAAS,sBAAuB,SAAS;AAEvC,kBAAQ,SAAS,MAAM;AAIvB,oBAAU,QAEP,QAAO,MAAO,OAAK,EAEnB,QAAO,MAAO,QAAM;AAEvB,oBAAU,QAAQ,QAAO,WAAY,GAAE;AACvC,kBAAQ,QAAO,6BAA8B,SAAU,IAAI,KAAK,OAAO;AACrE,oBAAQ,SAAS,OAAO,GAAG,IAAI;AAC/B,mBAAO;UACT,CAAA;QACF;AAEA,eAAO,KAAK,QAAO,sCAAuC,SAAU,YAAY,QAAQ,SAAS;AAC/F,gCAAsB,OAAO;AAC7B,iBAAO;QACT,CAAA;AAEA,eAAO,KAAK,QAAO,sCAAuC,SAAU,YAAY,QAAQ,SAAS;AAC/F,cAAI,QAAQ;AACV,oBAAQ,SAAS,SAAS;UAC5B;AACA,gCAAsB,OAAO;AAC7B,iBAAO;QACT,CAAA;AAEA,eAAO,KAAK,QAAO,OAAQ,EAAA;AAE3B,eAAO,QAAQ,UAAU,UAAS,kBAAmB,MAAM,SAAS,OAAO;AAC3E,eAAO;MACT,CAAA;AC7CA,eAAS,UAAS,WAAY,SAAU,MAAM,SAAS,SAAS;AAC9D;AACA,eAAO,QAAQ,UAAU,UAAS,kBAAmB,MAAM,SAAS,OAAO;AAI3E,eAAO,KAAK,QAAO,oBAAqB,IAAG;AAG3C,eAAO,KAAK,QAAO,OAAQ,EAAA;AAE3B,eAAO,QAAQ,UAAU,UAAS,iBAAkB,MAAM,SAAS,OAAO;AAC1E,eAAO;MACT,CAAA;ACbA,eAAS,UAAS,cAAe,SAAU,MAAM,SAAS,SAAS;AACjE;AAEA,eAAO,QAAQ,UAAU,UAAS,qBAAsB,MAAM,SAAS,OAAO;AAE9E,eAAO,KAAK,QAAO,SAAU,EAAA;AAC7B,eAAO,KAAK,QAAO,SAAU,EAAA;AAE7B,YAAI,QAAQ,KAAK,MAAK,SAAU,GAC5B,WAAW,CAAA,GACX,MAAM,MAAM;AAEhB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,cAAI,MAAM,MAAM,CAAC;AAEjB,cAAI,IAAI,OAAM,gBAAiB,KAAK,GAAG;AACrC,qBAAS,KAAK,GAAG;UAInB,WAAW,IAAI,OAAM,IAAI,KAAM,GAAG;AAChC,kBAAM,SAAS,UAAS,WAAW,EAAG,KAAK,SAAS,OAAO;AAC3D,kBAAM,IAAI,QAAO,cAAe,KAAG;AACnC,mBAAO;AACP,qBAAS,KAAK,GAAG;UACnB;QACF;AAGA,cAAM,SAAS;AACf,aAAK,IAAI,GAAG,IAAI,KAAK,KAAK;AACxB,cAAI,YAAY,IACZ,aAAa,SAAS,CAAC,GACvB,WAAW;AAGf,iBAAM,gBAAiB,KAAK,UAAU,GAAG;AACvC,gBAAI,QAAQ,OAAM,IACd,MAAQ,OAAM;AAElB,gBAAI,UAAU,KAAK;AACjB,0BAAY,QAAQ,YAAY,GAAG;YACrC,OAAO;AAEL,kBAAI,UAAU;AAEZ,4BAAY,SAAS,UAAS,YAAY,EAAG,QAAQ,aAAa,GAAG,EAAE,MAAM,SAAS,OAAO;cAC/F,OAAO;AACL,4BAAY,QAAQ,aAAa,GAAG,EAAE;cACxC;YACF;AACA,wBAAY,UAAU,QAAO,OAAQ,MAAA;AAErC,yBAAa,WAAW,QAAO,6BAA8B,SAAS;AAEtE,gBAAG,gCAAiC,KAAK,UAAU,GAAG;AACpD,yBAAW;YACb;UACF;AACA,mBAAS,CAAC,IAAI;QAChB;AACA,eAAO,SAAS,KAAI,IAAI;AAExB,eAAO,KAAK,QAAO,SAAU,EAAA;AAC7B,eAAO,KAAK,QAAO,SAAU,EAAA;AAC7B,eAAO,QAAQ,UAAU,UAAS,oBAAqB,MAAM,SAAS,OAAO;MAC/E,CAAA;AClEA,eAAS,UAAS,gBAAiB,SAAU,KAAK,MAAM,SAAS,SAAS;AACxE;AAEA,YAAI,IAAI,QAAQ;AACd,iBAAO,IAAI,OAAO,MAAM,QAAQ,WAAW,OAAO;QAEpD,WAAW,IAAI,OAAO;AAEpB,cAAI,KAAK,IAAI;AACb,cAAG,EAAG,cAAc,SAAS;AAC3B,iBAAK,IAAI,OAAO,IAAI,GAAE;UACxB;AACA,iBAAO,KAAK,QAAQ,IAAI,IAAI,OAAO;QACrC;AAEA,eAAO;MACT,CAAA;ACfA,eAAS,UAAS,aAAc,SAAU,MAAM,SAAS,SAAS;AAChE;AAEA,eAAO,QAAQ,UAAU,UAAS,oBAAqB,MAAM,SAAS,OAAO;AAC7E,eAAO,SAAS,UAAS,WAAW,EAAG,MAAM,SAAS,OAAO;AAC7D,eAAO,SAAS,UAAS,uCAAuC,EAAG,MAAM,SAAS,OAAO;AACzF,eAAO,SAAS,UAAS,wBAAwB,EAAG,MAAM,SAAS,OAAO;AAI1E,eAAO,SAAS,UAAS,QAAQ,EAAG,MAAM,SAAS,OAAO;AAC1D,eAAO,SAAS,UAAS,SAAS,EAAG,MAAM,SAAS,OAAO;AAK3D,eAAO,SAAS,UAAS,WAAW,EAAG,MAAM,SAAS,OAAO;AAC7D,eAAO,SAAS,UAAS,qBAAqB,EAAG,MAAM,SAAS,OAAO;AACvE,eAAO,SAAS,UAAS,OAAO,EAAG,MAAM,SAAS,OAAO;AACzD,eAAO,SAAS,UAAS,WAAW,EAAG,MAAM,SAAS,OAAO;AAC7D,eAAO,SAAS,UAAS,gBAAgB,EAAG,MAAM,SAAS,OAAO;AAClE,eAAO,SAAS,UAAS,eAAe,EAAG,MAAM,SAAS,OAAO;AACjE,eAAO,SAAS,UAAS,UAAU,EAAG,MAAM,SAAS,OAAO;AAG5D,eAAO,SAAS,UAAS,eAAe,EAAG,MAAM,SAAS,OAAO;AAGjE,eAAO,SAAS,UAAS,qBAAqB,EAAG,MAAM,SAAS,OAAO;AAGvE,YAAI,QAAQ,kBAAkB;AAG5B,cAAG,CAAA,SAAW,KAAK,IAAI,GAAG;AACxB,mBAAO,KAAK,QAAO,QAAS,UAAS;UACvC;QACF,OAAO;AAEL,iBAAO,KAAK,QAAO,UAAW,UAAS;QACzC;AAEA,eAAO,QAAQ,UAAU,UAAS,mBAAoB,MAAM,SAAS,OAAO;AAC5E,eAAO;MACT,CAAA;AChDA,eAAS,UAAS,iBAAkB,SAAU,MAAM,SAAS,SAAS;AACpE;AAEA,iBAAS,YAAa,KAAK;AACzB,cAAI,QAAQ,oBAAoB;AAC9B,kBAAM,SAAS,UAAS,qBAAqB,EAAG,KAAK,SAAS,OAAO;UACvE;AACA,iBAAO,UAAU,MAAM;QACzB;AAEA,YAAI,QAAQ,eAAe;AACzB,iBAAO,QAAQ,UAAU,UAAS,wBAAyB,MAAM,SAAS,OAAO;AACjF,iBAAO,KAAK,QAAO,+BAAgC,SAAU,IAAI,KAAK;AAAE,mBAAO,YAAY,GAAG;UAAG,CAAA;AACjG,iBAAO,QAAQ,UAAU,UAAS,uBAAwB,MAAM,SAAS,OAAO;QAClF;AAEA,eAAO;MACT,CAAA;ACZA,eAAS,UAAS,wBAAyB,SAAU,MAAM,SAAS,SAAS;AAC3E;AAEA,YAAI,QAAc,uKACd,cAAc;AAGlB,gBAAQ;AAER,YAAI,cAAc,SAAU,YAAY,QAAQ,KAAK,OAAO,QAAQ,YAAY,OAAO;AAGrF,mBAAS,OAAO,YAAW;AAC3B,cAAI,KAAK,YAAW,EAAG,MAAM,MAAM,EAAE,SAAS,IAAI,GAAG;AACnD,mBAAO;UACT;AACA,cAAI,IAAI,MAAK,wBAAuB,GAAK;AAEvC,oBAAQ,MAAM,MAAM,IAAI,IAAI,QAAO,OAAQ,EAAA;UAC7C,OAAO;AACL,oBAAQ,MAAM,MAAM,IAAI,SAAS,UAAS,qBAAqB,EAAG,KAAK,SAAS,OAAO;UACzF;AAEA,cAAI,YAAY;AAGd,mBAAO,aAAa;UAEtB,OAAO;AACL,gBAAI,OAAO;AACT,sBAAQ,QAAQ,MAAM,IAAI,MAAM,QAAO,QAAS,QAAM;YACxD;AACA,gBAAI,QAAQ,sBAAsB,SAAS,QAAQ;AACjD,sBAAQ,YAAY,MAAM,IAAI;gBAC5B;gBACA;cACF;YACF;UACF;AAEA,iBAAO;QACT;AAGA,eAAO,KAAK,QAAQ,aAAa,WAAW;AAE5C,eAAO,KAAK,QAAQ,OAAO,WAAW;AAGtC,eAAO,KAAK,QAAO,MAAO,EAAA;AAE1B,eAAO;MACT,CAAA;ACzDA,eAAS,UAAS,UAAW,SAAU,MAAM,SAAS,SAAS;AAC7D;AAEA,YAAG,CAAE,QAAQ,QAAQ;AACnB,iBAAO;QACT;AAEA,YAAI,WAAiB,wHAEjB,iBAAiB;AAErB,iBAAS,YAAa,OAAO;AAC3B,cAAG,eAAgB,KAAK,KAAK,GAAG;AAC9B,mBAAO;UACT,WAAU,qBAAsB,KAAK,KAAK,GAAG;AAC3C,mBAAO;UACT,WAAU,sBAAuB,KAAK,KAAK,GAAG;AAC5C,mBAAO;UACT,OAAO;AACL,mBAAO;UACT;QACF;AAEA,iBAAS,aAAc,QAAQ,OAAO;AACpC,cAAI,KAAK;AACT,mBAAS,OAAO,KAAI;AAEpB,cAAI,QAAQ,kBAAkB,QAAQ,eAAe;AACnD,iBAAK,UAAU,OAAO,QAAO,MAAO,GAAE,EAAG,YAAW,IAAK;UAC3D;AACA,mBAAS,SAAS,UAAS,WAAW,EAAG,QAAQ,SAAS,OAAO;AAEjE,iBAAO,QAAQ,KAAK,QAAQ,MAAM,SAAS;QAC7C;AAEA,iBAAS,WAAY,MAAM,OAAO;AAChC,cAAI,UAAU,SAAS,UAAS,WAAW,EAAG,MAAM,SAAS,OAAO;AACpE,iBAAO,QAAQ,QAAQ,MAAM,UAAU;QACzC;AAEA,iBAAS,WAAY,SAAS,OAAO;AACnC,cAAI,KAAK,4BACL,SAAS,QAAQ;AAErB,mBAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,kBAAM,QAAQ,CAAC;UACjB;AACA,gBAAM;AAEN,eAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACjC,kBAAM;AACN,qBAAS,KAAK,GAAG,KAAK,QAAQ,EAAE,IAAI;AAClC,oBAAM,MAAM,CAAC,EAAE,EAAE;YACnB;AACA,kBAAM;UACR;AACA,gBAAM;AACN,iBAAO;QACT;AAEA,iBAAS,WAAY,UAAU;AAC7B,cAAI,GAAG,aAAa,SAAS,MAAK,IAAI;AAEtC,eAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE,GAAG;AAEtC,gBAAG,YAAa,KAAK,WAAW,CAAC,CAAA,GAAI;AACnC,yBAAW,CAAC,IAAI,WAAW,CAAC,EAAE,QAAO,aAAc,EAAA;YACrD;AACA,gBAAG,YAAa,KAAK,WAAW,CAAC,CAAA,GAAI;AACnC,yBAAW,CAAC,IAAI,WAAW,CAAC,EAAE,QAAO,aAAc,EAAA;YACrD;AAEA,uBAAW,CAAC,IAAI,SAAS,UAAS,WAAW,EAAG,WAAW,CAAC,GAAG,SAAS,OAAO;UACjF;AAEA,cAAI,aAAa,WAAW,CAAC,EAAE,MAAK,GAAA,EAAM,IAAI,SAAU,GAAG;AAAE,mBAAO,EAAE,KAAI;UAAA,CAAA,GACtE,YAAY,WAAW,CAAC,EAAE,MAAK,GAAA,EAAM,IAAI,SAAU,GAAG;AAAE,mBAAO,EAAE,KAAI;UAAA,CAAA,GACrE,WAAW,CAAA,GACX,UAAU,CAAA,GACV,SAAS,CAAA,GACT,QAAQ,CAAA;AAEZ,qBAAW,MAAK;AAChB,qBAAW,MAAK;AAEhB,eAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE,GAAG;AACtC,gBAAI,WAAW,CAAC,EAAE,KAAI,MAAO,IAAI;AAC/B;YACF;AACA,qBAAS;cACP,WAAW,CAAC,EACT,MAAK,GAAA,EACL,IAAI,SAAU,GAAG;AAChB,uBAAO,EAAE,KAAI;cACf,CAAA;YACJ;UACF;AAEA,cAAI,WAAW,SAAS,UAAU,QAAQ;AACxC,mBAAO;UACT;AAEA,eAAK,IAAI,GAAG,IAAI,UAAU,QAAQ,EAAE,GAAG;AACrC,mBAAO,KAAK,YAAY,UAAU,CAAC,CAAA,CAAA;UACrC;AAEA,eAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE,GAAG;AACtC,gBAAI,SAAS,OAAO,YAAY,OAAO,CAAC,CAAA,GAAI;AAC1C,qBAAO,CAAC,IAAI;YACd;AACA,oBAAQ,KAAK,aAAa,WAAW,CAAC,GAAG,OAAO,CAAC,CAAA,CAAA;UACnD;AAEA,eAAK,IAAI,GAAG,IAAI,SAAS,QAAQ,EAAE,GAAG;AACpC,gBAAI,MAAM,CAAA;AACV,qBAAS,KAAK,GAAG,KAAK,QAAQ,QAAQ,EAAE,IAAI;AAC1C,kBAAI,SAAS,OAAO,YAAY,SAAS,CAAC,EAAE,EAAE,CAAA,GAAI;cAElD;AACA,kBAAI,KAAK,WAAW,SAAS,CAAC,EAAE,EAAE,GAAG,OAAO,EAAE,CAAA,CAAA;YAChD;AACA,kBAAM,KAAK,GAAG;UAChB;AAEA,iBAAO,WAAW,SAAS,KAAK;QAClC;AAEA,eAAO,QAAQ,UAAU,UAAS,iBAAkB,MAAM,SAAS,OAAO;AAG1E,eAAO,KAAK,QAAO,WAAY,SAAS,OAAO,wBAAwB;AAGvE,eAAO,KAAK,QAAQ,UAAU,UAAU;AAGxC,eAAO,KAAK,QAAQ,gBAAgB,UAAU;AAE9C,eAAO,QAAQ,UAAU,UAAS,gBAAiB,MAAM,SAAS,OAAO;AAEzE,eAAO;MACT,CAAA;AC7IA,eAAS,UAAS,aAAc,SAAU,MAAM,SAAS,SAAS;AAChE;AAEA,YAAG,CAAE,QAAQ,WAAW;AACtB,iBAAO;QACT;AAEA,eAAO,QAAQ,UAAU,UAAS,oBAAqB,MAAM,SAAS,OAAO;AAE7E,YAAI,QAAQ,2BAA2B;AACrC,iBAAO,KAAK,QAAO,2BAA4B,SAAU,IAAI,KAAK;AAChE,mBAAO,QAAQ,MAAM;UACvB,CAAA;AACA,iBAAO,KAAK,QAAO,yBAA0B,SAAU,IAAI,KAAK;AAC9D,mBAAO,QAAQ,MAAM;UACvB,CAAA;QACF,OAAO;AACL,iBAAO,KAAK,QAAO,uBAAwB,SAAU,IAAI,GAAG;AAC1D,mBAAO,MAAO,KAAK,CAAC,IAAK,QAAQ,IAAI,SAAS;UAChD,CAAA;AACA,iBAAO,KAAK,QAAO,qBAAsB,SAAU,IAAI,GAAG;AACxD,mBAAO,MAAO,KAAK,CAAC,IAAK,QAAQ,IAAI,SAAS;UAChD,CAAA;QACF;AAGA,eAAO,KAAK,QAAO,QAAS,SAAS,OAAO,wBAAwB;AAEpE,eAAO,QAAQ,UAAU,UAAS,mBAAoB,MAAM,SAAS,OAAO;AAE5E,eAAO;MACT,CAAA;AC5BA,eAAS,UAAS,wBAAyB,SAAU,MAAM,SAAS,SAAS;AAC3E;AACA,eAAO,QAAQ,UAAU,UAAS,+BAAgC,MAAM,SAAS,OAAO;AAExF,eAAO,KAAK,QAAO,aAAc,SAAU,YAAY,IAAI;AACzD,cAAI,oBAAoB,SAAS,EAAE;AACnC,iBAAO,OAAO,aAAa,iBAAiB;QAC9C,CAAA;AAEA,eAAO,QAAQ,UAAU,UAAS,8BAA+B,MAAM,SAAS,OAAO;AACvF,eAAO;MACT,CAAA;ACdA,eAAS,UAAS,2BAA4B,SAAU,MAAM,SAAS;AACrE;AAEA,YAAI,MAAM;AACV,YAAI,KAAK,cAAa,GAAI;AACxB,cAAI,WAAW,KAAK,YAChB,iBAAiB,SAAS;AAE9B,mBAAS,IAAI,GAAG,IAAI,gBAAgB,EAAE,GAAG;AACvC,gBAAI,WAAW,SAAS,UAAS,mBAAmB,EAAG,SAAS,CAAC,GAAG,OAAO;AAE3E,gBAAI,aAAa,IAAI;AACnB;YACF;AACA,mBAAO;UACT;QACF;AAEA,cAAM,IAAI,KAAI;AACd,cAAM,OAAO,IAAI,MAAK,IAAI,EAAG,KAAI,MAAM;AACvC,eAAO;MACT,CAAA;ACrBA,eAAS,UAAS,0BAA2B,SAAU,MAAM,SAAS;AACpE;AAEA,YAAI,OAAO,KAAK,aAAY,UAAU,GAClC,MAAO,KAAK,aAAY,YAAY;AACxC,eAAO,QAAQ,OAAO,OAAO,QAAQ,QAAQ,GAAG,IAAI;MACtD,CAAA;ACNA,eAAS,UAAS,yBAA0B,SAAU,MAAM;AAC1D;AAEA,eAAO,MAAM,KAAK,YAAY;MAChC,CAAA;ACJA,eAAS,UAAS,yBAA0B,SAAU,MAAM,SAAS;AACnE;AAEA,YAAI,MAAM;AACV,YAAI,KAAK,cAAa,GAAI;AACxB,iBAAO;AACP,cAAI,WAAW,KAAK,YAChB,iBAAiB,SAAS;AAC9B,mBAAS,IAAI,GAAG,IAAI,gBAAgB,EAAE,GAAG;AACvC,mBAAO,SAAS,UAAS,mBAAmB,EAAG,SAAS,CAAC,GAAG,OAAO;UACrE;AACA,iBAAO;QACT;AACA,eAAO;MACT,CAAA;ACdA,eAAS,UAAS,uBAAwB,SAAU,MAAM,SAAS,aAAa;AAC9E;AAEA,YAAI,aAAa,IAAI,MAAM,cAAc,CAAC,EAAE,KAAI,GAAA,GAC5C,MAAM;AAEV,YAAI,KAAK,cAAa,GAAI;AACxB,gBAAM,aAAa;AACnB,cAAI,WAAW,KAAK,YAChB,iBAAiB,SAAS;AAE9B,mBAAS,IAAI,GAAG,IAAI,gBAAgB,EAAE,GAAG;AACvC,mBAAO,SAAS,UAAS,mBAAmB,EAAG,SAAS,CAAC,GAAG,OAAO;UACrE;QACF;AACA,eAAO;MACT,CAAA;AChBA,eAAS,UAAS,mBAAoB,WAAY;AAChD;AAEA,eAAO;MACT,CAAA;ACJA,eAAS,UAAS,sBAAuB,SAAU,MAAM;AACvD;AAEA,YAAI,MAAM;AACV,YAAI,KAAK,aAAY,KAAK,GAAI;AAC5B,iBAAO,OAAO,KAAK,aAAY,KAAK,IAAK;AACzC,iBAAO,MAAM,KAAK,aAAY,KAAK,IAAK;AACxC,cAAI,KAAK,aAAY,OAAO,KAAM,KAAK,aAAY,QAAQ,GAAI;AAC7D,mBAAO,OAAO,KAAK,aAAY,OAAO,IAAK,MAAM,KAAK,aAAY,QAAQ;UAC5E;AAEA,cAAI,KAAK,aAAY,OAAO,GAAI;AAC9B,mBAAO,OAAO,KAAK,aAAY,OAAO,IAAK;UAC7C;AACA,iBAAO;QACT;AACA,eAAO;MACT,CAAA;ACjBA,eAAS,UAAS,sBAAuB,SAAU,MAAM,SAAS;AAChE;AAEA,YAAI,MAAM;AACV,YAAI,KAAK,cAAa,KAAM,KAAK,aAAY,MAAM,GAAI;AACrD,cAAI,WAAW,KAAK,YAChB,iBAAiB,SAAS;AAC9B,gBAAM;AACN,mBAAS,IAAI,GAAG,IAAI,gBAAgB,EAAE,GAAG;AACvC,mBAAO,SAAS,UAAS,mBAAmB,EAAG,SAAS,CAAC,GAAG,OAAO;UACrE;AACA,iBAAO;AACP,iBAAO,MAAM,KAAK,aAAY,MAAM,IAAK;AACzC,cAAI,KAAK,aAAY,OAAO,GAAI;AAC9B,mBAAO,OAAO,KAAK,aAAY,OAAO,IAAK;UAC7C;AACA,iBAAO;QACT;AACA,eAAO;MACT,CAAA;ACnBA,eAAS,UAAS,qBAAsB,SAAU,MAAM,SAAS,MAAM;AACrE;AAEA,YAAI,MAAM;AACV,YAAG,CAAE,KAAK,cAAa,GAAI;AACzB,iBAAO;QACT;AACA,YAAI,YAAkB,KAAK,YACvB,kBAAkB,UAAU,QAC5B,UAAU,KAAK,aAAY,OAAO,KAAM;AAE5C,iBAAS,IAAI,GAAG,IAAI,iBAAiB,EAAE,GAAG;AACxC,cAAI,OAAO,UAAU,CAAC,EAAE,YAAY,eAAe,UAAU,CAAC,EAAE,QAAQ,YAAW,MAAO,MAAM;AAC9F;UACF;AAGA,cAAI,SAAS;AACb,cAAI,SAAS,MAAM;AACjB,qBAAS,QAAQ,SAAQ,IAAK;UAChC,OAAO;AACL,qBAAS;UACX;AAGA,iBAAO,SAAS,SAAS,UAAS,uBAAuB,EAAG,UAAU,CAAC,GAAG,OAAO;AACjF,YAAE;QACJ;AAGA,eAAO;AACP,eAAO,IAAI,KAAI;MACjB,CAAA;AChCA,eAAS,UAAS,yBAA0B,SAAU,MAAM,SAAS;AACnE;AAEA,YAAI,cAAc;AAElB,YAAI,WAAW,KAAK,YAChB,iBAAiB,SAAS;AAE9B,iBAAS,IAAI,GAAG,IAAI,gBAAgB,EAAE,GAAG;AACvC,yBAAe,SAAS,UAAS,mBAAmB,EAAG,SAAS,CAAC,GAAG,OAAO;QAC7E;AAEA,YAAG,CAAA,MAAQ,KAAK,WAAW,GAAG;AAC5B,yBAAe;QACjB,OAAO;AAEL,wBAAc,YACX,MAAK,IAAI,EACT,KAAI,QAAQ,EACZ,QAAO,YAAa,EAAA,EACpB,QAAO,UAAW,MAAK;QAC5B;AAEA,eAAO;MACT,CAAA;ACtBA,eAAS,UAAS,qBAAsB,SAAU,MAAM,SAAS,WAAW;AAC1E;AAEA,oBAAY,aAAa;AAEzB,YAAI,MAAM;AAGV,YAAI,KAAK,aAAa,GAAG;AACvB,iBAAO,SAAS,UAAS,kBAAkB,EAAG,MAAM,OAAO;QAC7D;AAGA,YAAI,KAAK,aAAa,GAAG;AACvB,iBAAO,SAAS,KAAK,OAAO;QAC9B;AAGA,YAAI,KAAK,aAAa,GAAG;AACvB,iBAAO;QACT;AAEA,YAAI,UAAU,KAAK,QAAQ,YAAW;AAEtC,gBAAQ,SAAS;UAKf,KAAK;AACH,gBAAG,CAAE,WAAW;AAAE,oBAAM,SAAS,UAAS,qBAAqB,EAAG,MAAM,SAAS,CAAC,IAAI;YAAQ;AAC9F;UACF,KAAK;AACH,gBAAG,CAAE,WAAW;AAAE,oBAAM,SAAS,UAAS,qBAAqB,EAAG,MAAM,SAAS,CAAC,IAAI;YAAQ;AAC9F;UACF,KAAK;AACH,gBAAG,CAAE,WAAW;AAAE,oBAAM,SAAS,UAAS,qBAAqB,EAAG,MAAM,SAAS,CAAC,IAAI;YAAQ;AAC9F;UACF,KAAK;AACH,gBAAG,CAAE,WAAW;AAAE,oBAAM,SAAS,UAAS,qBAAqB,EAAG,MAAM,SAAS,CAAC,IAAI;YAAQ;AAC9F;UACF,KAAK;AACH,gBAAG,CAAE,WAAW;AAAE,oBAAM,SAAS,UAAS,qBAAqB,EAAG,MAAM,SAAS,CAAC,IAAI;YAAQ;AAC9F;UACF,KAAK;AACH,gBAAG,CAAE,WAAW;AAAE,oBAAM,SAAS,UAAS,qBAAqB,EAAG,MAAM,SAAS,CAAC,IAAI;YAAQ;AAC9F;UAEF,KAAK;AACH,gBAAG,CAAE,WAAW;AAAE,oBAAM,SAAS,UAAS,wBAAwB,EAAG,MAAM,OAAO,IAAI;YAAQ;AAC9F;UAEF,KAAK;AACH,gBAAG,CAAE,WAAW;AAAE,oBAAM,SAAS,UAAS,yBAAyB,EAAG,MAAM,OAAO,IAAI;YAAQ;AAC/F;UAEF,KAAK;AACH,gBAAG,CAAE,WAAW;AAAE,oBAAM,SAAS,UAAS,iBAAiB,EAAG,MAAM,OAAO,IAAI;YAAQ;AACvF;UAEF,KAAK;AACH,gBAAG,CAAE,WAAW;AAAE,oBAAM,SAAS,UAAS,mBAAmB,EAAG,MAAM,SAAS,IAAG,IAAK;YAAQ;AAC/F;UAEF,KAAK;AACH,gBAAG,CAAE,WAAW;AAAE,oBAAM,SAAS,UAAS,mBAAmB,EAAG,MAAM,SAAS,IAAG,IAAK;YAAQ;AAC/F;UAEF,KAAK;AACH,gBAAG,CAAE,WAAW;AAAE,oBAAM,SAAS,UAAS,wBAAwB,EAAG,MAAM,OAAO,IAAI;YAAQ;AAC9F;UAEF,KAAK;AACH,gBAAG,CAAE,WAAW;AAAE,oBAAM,SAAS,UAAS,kBAAkB,EAAG,MAAM,OAAO,IAAI;YAAQ;AACxF;UAEF,KAAK;AACH,gBAAG,CAAE,WAAW;AAAE,oBAAM,SAAS,UAAS,oBAAoB,EAAG,MAAM,OAAO,IAAI;YAAQ;AAC1F;UAKF,KAAK;AACH,kBAAM,SAAS,UAAS,uBAAuB,EAAG,MAAM,OAAO;AAC/D;UAEF,KAAK;UACL,KAAK;AACH,kBAAM,SAAS,UAAS,uBAAuB,EAAG,MAAM,OAAO;AAC/D;UAEF,KAAK;UACL,KAAK;AACH,kBAAM,SAAS,UAAS,qBAAqB,EAAG,MAAM,OAAO;AAC7D;UAEF,KAAK;AACH,kBAAM,SAAS,UAAS,4BAA4B,EAAG,MAAM,OAAO;AACpE;UAEF,KAAK;AACH,kBAAM,SAAS,UAAS,oBAAoB,EAAG,MAAM,OAAO;AAC5D;UAEF,KAAK;AACH,kBAAM,SAAS,UAAS,oBAAoB,EAAG,MAAM,OAAO;AAC5D;UAEF;AACE,kBAAM,KAAK,YAAY;QAC3B;AAKA,eAAO;MACT,CAAA;ACvHA,eAAS,UAAS,0BAA2B,SAAU,MAAM,SAAS;AACpE;AAEA,YAAI,MAAM;AACV,YAAI,KAAK,cAAa,GAAI;AACxB,cAAI,WAAW,KAAK,YAChB,iBAAiB,SAAS;AAC9B,mBAAS,IAAI,GAAG,IAAI,gBAAgB,EAAE,GAAG;AACvC,mBAAO,SAAS,UAAS,mBAAmB,EAAG,SAAS,CAAC,GAAG,OAAO;UACrE;QACF;AAGA,cAAM,IAAI,KAAI;AAEd,eAAO;MACT,CAAA;AChBA,eAAS,UAAS,oBAAqB,SAAU,MAAM,SAAS;AAC9D;AAEA,YAAI,MAAO,KAAK,aAAY,QAAQ;AACpC,eAAO,UAAU,QAAQ,QAAQ,GAAG,IAAI;MAC1C,CAAA;ACLA,eAAS,UAAS,8BAA+B,SAAU,MAAM,SAAS;AACxE;AAEA,YAAI,MAAM;AACV,YAAI,KAAK,cAAa,GAAI;AACxB,iBAAO;AACP,cAAI,WAAW,KAAK,YAChB,iBAAiB,SAAS;AAC9B,mBAAS,IAAI,GAAG,IAAI,gBAAgB,EAAE,GAAG;AACvC,mBAAO,SAAS,UAAS,mBAAmB,EAAG,SAAS,CAAC,GAAG,OAAO;UACrE;AACA,iBAAO;QACT;AACA,eAAO;MACT,CAAA;ACdA,eAAS,UAAS,uBAAwB,SAAU,MAAM,SAAS;AACjE;AAEA,YAAI,MAAM;AACV,YAAI,KAAK,cAAa,GAAI;AACxB,iBAAO;AACP,cAAI,WAAW,KAAK,YAChB,iBAAiB,SAAS;AAC9B,mBAAS,IAAI,GAAG,IAAI,gBAAgB,EAAE,GAAG;AACvC,mBAAO,SAAS,UAAS,mBAAmB,EAAG,SAAS,CAAC,GAAG,OAAO;UACrE;AACA,iBAAO;QACT;AACA,eAAO;MACT,CAAA;ACdA,eAAS,UAAS,sBAAuB,SAAU,MAAM,SAAS;AAChE;AAEA,YAAI,MAAM,IACN,aAAa,CAAA,CAAA,GAAK,CAAA,CAAA,GAClB,WAAa,KAAK,iBAAgB,aAAa,GAC/C,OAAa,KAAK,iBAAgB,UAAU,GAC5C,GAAG;AACP,aAAK,IAAI,GAAG,IAAI,SAAS,QAAQ,EAAE,GAAG;AACpC,cAAI,cAAc,SAAS,UAAS,wBAAwB,EAAG,SAAS,CAAC,GAAG,OAAO,GAC/E,SAAS;AAEb,cAAI,SAAS,CAAC,EAAE,aAAY,OAAO,GAAI;AACrC,gBAAI,QAAQ,SAAS,CAAC,EAAE,aAAY,OAAO,EAAG,YAAW,EAAG,QAAO,OAAQ,EAAA;AAC3E,oBAAQ,OAAO;cACb,KAAK;AACH,yBAAS;AACT;cACF,KAAK;AACH,yBAAS;AACT;cACF,KAAK;AACH,yBAAS;AACT;YACJ;UACF;AACA,qBAAW,CAAC,EAAE,CAAC,IAAI,YAAY,KAAI;AACnC,qBAAW,CAAC,EAAE,CAAC,IAAI;QACrB;AAEA,aAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAChC,cAAI,IAAI,WAAW,KAAI,CAAA,CAAA,IAAO,GAC1B,OAAO,KAAK,CAAC,EAAE,qBAAoB,IAAI;AAE3C,eAAK,KAAK,GAAG,KAAK,SAAS,QAAQ,EAAE,IAAI;AACvC,gBAAI,cAAc;AAClB,gBAAI,OAAO,KAAK,EAAE,MAAM,aAAa;AACnC,4BAAc,SAAS,UAAS,wBAAwB,EAAG,KAAK,EAAE,GAAG,OAAO;YAC9E;AACA,uBAAW,CAAC,EAAE,KAAK,WAAW;UAChC;QACF;AAEA,YAAI,kBAAkB;AACtB,aAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE,GAAG;AACtC,eAAK,KAAK,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE,IAAI;AAC5C,gBAAI,SAAS,WAAW,CAAC,EAAE,EAAE,EAAE;AAC/B,gBAAI,SAAS,iBAAiB;AAC5B,gCAAkB;YACpB;UACF;QACF;AAEA,aAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE,GAAG;AACtC,eAAK,KAAK,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE,IAAI;AAC5C,gBAAI,MAAM,GAAG;AACX,kBAAI,WAAW,CAAC,EAAE,EAAE,EAAE,MAAK,EAAG,MAAM,KAAK;AACvC,2BAAW,CAAC,EAAE,EAAE,IAAI,SAAS,OAAO,OAAO,WAAW,CAAC,EAAE,EAAE,EAAE,MAAK,EAAG,GAAG,kBAAkB,GAAG,GAAA,IAAO;cACtG,OAAO;AACL,2BAAW,CAAC,EAAE,EAAE,IAAI,SAAS,OAAO,OAAO,WAAW,CAAC,EAAE,EAAE,GAAG,iBAAiB,GAAA;cACjF;YACF,OAAO;AACL,yBAAW,CAAC,EAAE,EAAE,IAAI,SAAS,OAAO,OAAO,WAAW,CAAC,EAAE,EAAE,GAAG,eAAe;YAC/E;UACF;AACA,iBAAO,OAAO,WAAW,CAAC,EAAE,KAAI,KAAK,IAAK;QAC5C;AAEA,eAAO,IAAI,KAAI;MACjB,CAAA;ACrEA,eAAS,UAAS,0BAA2B,SAAU,MAAM,SAAS;AACpE;AAEA,YAAI,MAAM;AACV,YAAG,CAAE,KAAK,cAAa,GAAI;AACzB,iBAAO;QACT;AACA,YAAI,WAAW,KAAK,YAChB,iBAAiB,SAAS;AAE9B,iBAAS,IAAI,GAAG,IAAI,gBAAgB,EAAE,GAAG;AACvC,iBAAO,SAAS,UAAS,mBAAmB,EAAG,SAAS,CAAC,GAAG,SAAS,IAAI;QAC3E;AACA,eAAO,IAAI,KAAI;MACjB,CAAA;ACdA,eAAS,UAAS,oBAAqB,SAAU,MAAM;AACrD;AAEA,YAAI,MAAM,KAAK;AAGf,cAAM,IAAI,QAAO,OAAQ,GAAE;AAG3B,cAAM,IAAI,QAAO,WAAY,GAAE;AAG/B,cAAM,SAAS,OAAO,qBAAqB,GAAG;AAM9C,cAAM,IAAI,QAAO,cAAe,MAAK;AAGrC,cAAM,IAAI,QAAO,YAAa,OAAK;AAGnC,cAAM,IAAI,QAAO,QAAS,KAAA;AAG1B,cAAM,IAAI,QAAO,0BAA2B,UAAS;AAGrD,cAAM,IAAI,QAAO,oBAAqB,OAAG;AAGzC,cAAM,IAAI,QAAO,qBAAsB,QAAO;AAG9C,cAAM,IAAI,QAAO,eAAgB,UAAM;AAGvC,cAAM,IAAI,QAAO,4BAA6B,SAAM;AAEpD,eAAO;MACT,CAAA;AC1CA,UAAI,OAAO;AAGX,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAC9C,eAAO,WAAY;AACjB;AACA,iBAAO;QACT,CAAA;MAGF,WAAW,OAAO,WAAW,eAAe,OAAO,SAAS;AAC1D,eAAO,UAAU;MAGnB,OAAO;AACL,aAAK,WAAW;MAClB;;;;", "names": ["options", "doc", "i", "style"]}