{"version": 3, "sources": ["../../.pnpm/@vue+compiler-core@3.5.13/node_modules/@vue/compiler-core/dist/compiler-core.esm-bundler.js", "../../.pnpm/@vue+compiler-dom@3.5.13/node_modules/@vue/compiler-dom/dist/compiler-dom.esm-bundler.js", "../../.pnpm/vue@3.5.13_typescript@4.9.5/node_modules/vue/dist/vue.cjs.js", "../../.pnpm/vue@3.5.13_typescript@4.9.5/node_modules/vue/index.js"], "sourcesContent": ["/**\n* @vue/compiler-core v3.5.13\n* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors\n* @license MIT\n**/\nimport { isString, NOOP, isObject, NO, extend, isSymbol, isArray, capitalize, camelize, EMPTY_OBJ, PatchFlagNames, slotFlagsText, isOn, isBuiltInDirective, isReservedProp, toHandlerKey } from '@vue/shared';\nexport { generateCodeFrame } from '@vue/shared';\n\nconst FRAGMENT = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `Fragment` : ``);\nconst TELEPORT = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `Teleport` : ``);\nconst SUSPENSE = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `Suspense` : ``);\nconst KEEP_ALIVE = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `KeepAlive` : ``);\nconst BASE_TRANSITION = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `BaseTransition` : ``\n);\nconst OPEN_BLOCK = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `openBlock` : ``);\nconst CREATE_BLOCK = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `createBlock` : ``);\nconst CREATE_ELEMENT_BLOCK = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `createElementBlock` : ``\n);\nconst CREATE_VNODE = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `createVNode` : ``);\nconst CREATE_ELEMENT_VNODE = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `createElementVNode` : ``\n);\nconst CREATE_COMMENT = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `createCommentVNode` : ``\n);\nconst CREATE_TEXT = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `createTextVNode` : ``\n);\nconst CREATE_STATIC = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `createStaticVNode` : ``\n);\nconst RESOLVE_COMPONENT = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `resolveComponent` : ``\n);\nconst RESOLVE_DYNAMIC_COMPONENT = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `resolveDynamicComponent` : ``\n);\nconst RESOLVE_DIRECTIVE = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `resolveDirective` : ``\n);\nconst RESOLVE_FILTER = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `resolveFilter` : ``\n);\nconst WITH_DIRECTIVES = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `withDirectives` : ``\n);\nconst RENDER_LIST = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `renderList` : ``);\nconst RENDER_SLOT = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `renderSlot` : ``);\nconst CREATE_SLOTS = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `createSlots` : ``);\nconst TO_DISPLAY_STRING = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `toDisplayString` : ``\n);\nconst MERGE_PROPS = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `mergeProps` : ``);\nconst NORMALIZE_CLASS = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `normalizeClass` : ``\n);\nconst NORMALIZE_STYLE = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `normalizeStyle` : ``\n);\nconst NORMALIZE_PROPS = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `normalizeProps` : ``\n);\nconst GUARD_REACTIVE_PROPS = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `guardReactiveProps` : ``\n);\nconst TO_HANDLERS = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `toHandlers` : ``);\nconst CAMELIZE = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `camelize` : ``);\nconst CAPITALIZE = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `capitalize` : ``);\nconst TO_HANDLER_KEY = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `toHandlerKey` : ``\n);\nconst SET_BLOCK_TRACKING = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `setBlockTracking` : ``\n);\nconst PUSH_SCOPE_ID = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `pushScopeId` : ``);\nconst POP_SCOPE_ID = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `popScopeId` : ``);\nconst WITH_CTX = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `withCtx` : ``);\nconst UNREF = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `unref` : ``);\nconst IS_REF = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `isRef` : ``);\nconst WITH_MEMO = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `withMemo` : ``);\nconst IS_MEMO_SAME = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `isMemoSame` : ``);\nconst helperNameMap = {\n  [FRAGMENT]: `Fragment`,\n  [TELEPORT]: `Teleport`,\n  [SUSPENSE]: `Suspense`,\n  [KEEP_ALIVE]: `KeepAlive`,\n  [BASE_TRANSITION]: `BaseTransition`,\n  [OPEN_BLOCK]: `openBlock`,\n  [CREATE_BLOCK]: `createBlock`,\n  [CREATE_ELEMENT_BLOCK]: `createElementBlock`,\n  [CREATE_VNODE]: `createVNode`,\n  [CREATE_ELEMENT_VNODE]: `createElementVNode`,\n  [CREATE_COMMENT]: `createCommentVNode`,\n  [CREATE_TEXT]: `createTextVNode`,\n  [CREATE_STATIC]: `createStaticVNode`,\n  [RESOLVE_COMPONENT]: `resolveComponent`,\n  [RESOLVE_DYNAMIC_COMPONENT]: `resolveDynamicComponent`,\n  [RESOLVE_DIRECTIVE]: `resolveDirective`,\n  [RESOLVE_FILTER]: `resolveFilter`,\n  [WITH_DIRECTIVES]: `withDirectives`,\n  [RENDER_LIST]: `renderList`,\n  [RENDER_SLOT]: `renderSlot`,\n  [CREATE_SLOTS]: `createSlots`,\n  [TO_DISPLAY_STRING]: `toDisplayString`,\n  [MERGE_PROPS]: `mergeProps`,\n  [NORMALIZE_CLASS]: `normalizeClass`,\n  [NORMALIZE_STYLE]: `normalizeStyle`,\n  [NORMALIZE_PROPS]: `normalizeProps`,\n  [GUARD_REACTIVE_PROPS]: `guardReactiveProps`,\n  [TO_HANDLERS]: `toHandlers`,\n  [CAMELIZE]: `camelize`,\n  [CAPITALIZE]: `capitalize`,\n  [TO_HANDLER_KEY]: `toHandlerKey`,\n  [SET_BLOCK_TRACKING]: `setBlockTracking`,\n  [PUSH_SCOPE_ID]: `pushScopeId`,\n  [POP_SCOPE_ID]: `popScopeId`,\n  [WITH_CTX]: `withCtx`,\n  [UNREF]: `unref`,\n  [IS_REF]: `isRef`,\n  [WITH_MEMO]: `withMemo`,\n  [IS_MEMO_SAME]: `isMemoSame`\n};\nfunction registerRuntimeHelpers(helpers) {\n  Object.getOwnPropertySymbols(helpers).forEach((s) => {\n    helperNameMap[s] = helpers[s];\n  });\n}\n\nconst Namespaces = {\n  \"HTML\": 0,\n  \"0\": \"HTML\",\n  \"SVG\": 1,\n  \"1\": \"SVG\",\n  \"MATH_ML\": 2,\n  \"2\": \"MATH_ML\"\n};\nconst NodeTypes = {\n  \"ROOT\": 0,\n  \"0\": \"ROOT\",\n  \"ELEMENT\": 1,\n  \"1\": \"ELEMENT\",\n  \"TEXT\": 2,\n  \"2\": \"TEXT\",\n  \"COMMENT\": 3,\n  \"3\": \"COMMENT\",\n  \"SIMPLE_EXPRESSION\": 4,\n  \"4\": \"SIMPLE_EXPRESSION\",\n  \"INTERPOLATION\": 5,\n  \"5\": \"INTERPOLATION\",\n  \"ATTRIBUTE\": 6,\n  \"6\": \"ATTRIBUTE\",\n  \"DIRECTIVE\": 7,\n  \"7\": \"DIRECTIVE\",\n  \"COMPOUND_EXPRESSION\": 8,\n  \"8\": \"COMPOUND_EXPRESSION\",\n  \"IF\": 9,\n  \"9\": \"IF\",\n  \"IF_BRANCH\": 10,\n  \"10\": \"IF_BRANCH\",\n  \"FOR\": 11,\n  \"11\": \"FOR\",\n  \"TEXT_CALL\": 12,\n  \"12\": \"TEXT_CALL\",\n  \"VNODE_CALL\": 13,\n  \"13\": \"VNODE_CALL\",\n  \"JS_CALL_EXPRESSION\": 14,\n  \"14\": \"JS_CALL_EXPRESSION\",\n  \"JS_OBJECT_EXPRESSION\": 15,\n  \"15\": \"JS_OBJECT_EXPRESSION\",\n  \"JS_PROPERTY\": 16,\n  \"16\": \"JS_PROPERTY\",\n  \"JS_ARRAY_EXPRESSION\": 17,\n  \"17\": \"JS_ARRAY_EXPRESSION\",\n  \"JS_FUNCTION_EXPRESSION\": 18,\n  \"18\": \"JS_FUNCTION_EXPRESSION\",\n  \"JS_CONDITIONAL_EXPRESSION\": 19,\n  \"19\": \"JS_CONDITIONAL_EXPRESSION\",\n  \"JS_CACHE_EXPRESSION\": 20,\n  \"20\": \"JS_CACHE_EXPRESSION\",\n  \"JS_BLOCK_STATEMENT\": 21,\n  \"21\": \"JS_BLOCK_STATEMENT\",\n  \"JS_TEMPLATE_LITERAL\": 22,\n  \"22\": \"JS_TEMPLATE_LITERAL\",\n  \"JS_IF_STATEMENT\": 23,\n  \"23\": \"JS_IF_STATEMENT\",\n  \"JS_ASSIGNMENT_EXPRESSION\": 24,\n  \"24\": \"JS_ASSIGNMENT_EXPRESSION\",\n  \"JS_SEQUENCE_EXPRESSION\": 25,\n  \"25\": \"JS_SEQUENCE_EXPRESSION\",\n  \"JS_RETURN_STATEMENT\": 26,\n  \"26\": \"JS_RETURN_STATEMENT\"\n};\nconst ElementTypes = {\n  \"ELEMENT\": 0,\n  \"0\": \"ELEMENT\",\n  \"COMPONENT\": 1,\n  \"1\": \"COMPONENT\",\n  \"SLOT\": 2,\n  \"2\": \"SLOT\",\n  \"TEMPLATE\": 3,\n  \"3\": \"TEMPLATE\"\n};\nconst ConstantTypes = {\n  \"NOT_CONSTANT\": 0,\n  \"0\": \"NOT_CONSTANT\",\n  \"CAN_SKIP_PATCH\": 1,\n  \"1\": \"CAN_SKIP_PATCH\",\n  \"CAN_CACHE\": 2,\n  \"2\": \"CAN_CACHE\",\n  \"CAN_STRINGIFY\": 3,\n  \"3\": \"CAN_STRINGIFY\"\n};\nconst locStub = {\n  start: { line: 1, column: 1, offset: 0 },\n  end: { line: 1, column: 1, offset: 0 },\n  source: \"\"\n};\nfunction createRoot(children, source = \"\") {\n  return {\n    type: 0,\n    source,\n    children,\n    helpers: /* @__PURE__ */ new Set(),\n    components: [],\n    directives: [],\n    hoists: [],\n    imports: [],\n    cached: [],\n    temps: 0,\n    codegenNode: void 0,\n    loc: locStub\n  };\n}\nfunction createVNodeCall(context, tag, props, children, patchFlag, dynamicProps, directives, isBlock = false, disableTracking = false, isComponent = false, loc = locStub) {\n  if (context) {\n    if (isBlock) {\n      context.helper(OPEN_BLOCK);\n      context.helper(getVNodeBlockHelper(context.inSSR, isComponent));\n    } else {\n      context.helper(getVNodeHelper(context.inSSR, isComponent));\n    }\n    if (directives) {\n      context.helper(WITH_DIRECTIVES);\n    }\n  }\n  return {\n    type: 13,\n    tag,\n    props,\n    children,\n    patchFlag,\n    dynamicProps,\n    directives,\n    isBlock,\n    disableTracking,\n    isComponent,\n    loc\n  };\n}\nfunction createArrayExpression(elements, loc = locStub) {\n  return {\n    type: 17,\n    loc,\n    elements\n  };\n}\nfunction createObjectExpression(properties, loc = locStub) {\n  return {\n    type: 15,\n    loc,\n    properties\n  };\n}\nfunction createObjectProperty(key, value) {\n  return {\n    type: 16,\n    loc: locStub,\n    key: isString(key) ? createSimpleExpression(key, true) : key,\n    value\n  };\n}\nfunction createSimpleExpression(content, isStatic = false, loc = locStub, constType = 0) {\n  return {\n    type: 4,\n    loc,\n    content,\n    isStatic,\n    constType: isStatic ? 3 : constType\n  };\n}\nfunction createInterpolation(content, loc) {\n  return {\n    type: 5,\n    loc,\n    content: isString(content) ? createSimpleExpression(content, false, loc) : content\n  };\n}\nfunction createCompoundExpression(children, loc = locStub) {\n  return {\n    type: 8,\n    loc,\n    children\n  };\n}\nfunction createCallExpression(callee, args = [], loc = locStub) {\n  return {\n    type: 14,\n    loc,\n    callee,\n    arguments: args\n  };\n}\nfunction createFunctionExpression(params, returns = void 0, newline = false, isSlot = false, loc = locStub) {\n  return {\n    type: 18,\n    params,\n    returns,\n    newline,\n    isSlot,\n    loc\n  };\n}\nfunction createConditionalExpression(test, consequent, alternate, newline = true) {\n  return {\n    type: 19,\n    test,\n    consequent,\n    alternate,\n    newline,\n    loc: locStub\n  };\n}\nfunction createCacheExpression(index, value, needPauseTracking = false, inVOnce = false) {\n  return {\n    type: 20,\n    index,\n    value,\n    needPauseTracking,\n    inVOnce,\n    needArraySpread: false,\n    loc: locStub\n  };\n}\nfunction createBlockStatement(body) {\n  return {\n    type: 21,\n    body,\n    loc: locStub\n  };\n}\nfunction createTemplateLiteral(elements) {\n  return {\n    type: 22,\n    elements,\n    loc: locStub\n  };\n}\nfunction createIfStatement(test, consequent, alternate) {\n  return {\n    type: 23,\n    test,\n    consequent,\n    alternate,\n    loc: locStub\n  };\n}\nfunction createAssignmentExpression(left, right) {\n  return {\n    type: 24,\n    left,\n    right,\n    loc: locStub\n  };\n}\nfunction createSequenceExpression(expressions) {\n  return {\n    type: 25,\n    expressions,\n    loc: locStub\n  };\n}\nfunction createReturnStatement(returns) {\n  return {\n    type: 26,\n    returns,\n    loc: locStub\n  };\n}\nfunction getVNodeHelper(ssr, isComponent) {\n  return ssr || isComponent ? CREATE_VNODE : CREATE_ELEMENT_VNODE;\n}\nfunction getVNodeBlockHelper(ssr, isComponent) {\n  return ssr || isComponent ? CREATE_BLOCK : CREATE_ELEMENT_BLOCK;\n}\nfunction convertToBlock(node, { helper, removeHelper, inSSR }) {\n  if (!node.isBlock) {\n    node.isBlock = true;\n    removeHelper(getVNodeHelper(inSSR, node.isComponent));\n    helper(OPEN_BLOCK);\n    helper(getVNodeBlockHelper(inSSR, node.isComponent));\n  }\n}\n\nconst defaultDelimitersOpen = new Uint8Array([123, 123]);\nconst defaultDelimitersClose = new Uint8Array([125, 125]);\nfunction isTagStartChar(c) {\n  return c >= 97 && c <= 122 || c >= 65 && c <= 90;\n}\nfunction isWhitespace(c) {\n  return c === 32 || c === 10 || c === 9 || c === 12 || c === 13;\n}\nfunction isEndOfTagSection(c) {\n  return c === 47 || c === 62 || isWhitespace(c);\n}\nfunction toCharCodes(str) {\n  const ret = new Uint8Array(str.length);\n  for (let i = 0; i < str.length; i++) {\n    ret[i] = str.charCodeAt(i);\n  }\n  return ret;\n}\nconst Sequences = {\n  Cdata: new Uint8Array([67, 68, 65, 84, 65, 91]),\n  // CDATA[\n  CdataEnd: new Uint8Array([93, 93, 62]),\n  // ]]>\n  CommentEnd: new Uint8Array([45, 45, 62]),\n  // `-->`\n  ScriptEnd: new Uint8Array([60, 47, 115, 99, 114, 105, 112, 116]),\n  // `<\\/script`\n  StyleEnd: new Uint8Array([60, 47, 115, 116, 121, 108, 101]),\n  // `</style`\n  TitleEnd: new Uint8Array([60, 47, 116, 105, 116, 108, 101]),\n  // `</title`\n  TextareaEnd: new Uint8Array([\n    60,\n    47,\n    116,\n    101,\n    120,\n    116,\n    97,\n    114,\n    101,\n    97\n  ])\n  // `</textarea\n};\nclass Tokenizer {\n  constructor(stack, cbs) {\n    this.stack = stack;\n    this.cbs = cbs;\n    /** The current state the tokenizer is in. */\n    this.state = 1;\n    /** The read buffer. */\n    this.buffer = \"\";\n    /** The beginning of the section that is currently being read. */\n    this.sectionStart = 0;\n    /** The index within the buffer that we are currently looking at. */\n    this.index = 0;\n    /** The start of the last entity. */\n    this.entityStart = 0;\n    /** Some behavior, eg. when decoding entities, is done while we are in another state. This keeps track of the other state type. */\n    this.baseState = 1;\n    /** For special parsing behavior inside of script and style tags. */\n    this.inRCDATA = false;\n    /** For disabling RCDATA tags handling */\n    this.inXML = false;\n    /** For disabling interpolation parsing in v-pre */\n    this.inVPre = false;\n    /** Record newline positions for fast line / column calculation */\n    this.newlines = [];\n    this.mode = 0;\n    this.delimiterOpen = defaultDelimitersOpen;\n    this.delimiterClose = defaultDelimitersClose;\n    this.delimiterIndex = -1;\n    this.currentSequence = void 0;\n    this.sequenceIndex = 0;\n  }\n  get inSFCRoot() {\n    return this.mode === 2 && this.stack.length === 0;\n  }\n  reset() {\n    this.state = 1;\n    this.mode = 0;\n    this.buffer = \"\";\n    this.sectionStart = 0;\n    this.index = 0;\n    this.baseState = 1;\n    this.inRCDATA = false;\n    this.currentSequence = void 0;\n    this.newlines.length = 0;\n    this.delimiterOpen = defaultDelimitersOpen;\n    this.delimiterClose = defaultDelimitersClose;\n  }\n  /**\n   * Generate Position object with line / column information using recorded\n   * newline positions. We know the index is always going to be an already\n   * processed index, so all the newlines up to this index should have been\n   * recorded.\n   */\n  getPos(index) {\n    let line = 1;\n    let column = index + 1;\n    for (let i = this.newlines.length - 1; i >= 0; i--) {\n      const newlineIndex = this.newlines[i];\n      if (index > newlineIndex) {\n        line = i + 2;\n        column = index - newlineIndex;\n        break;\n      }\n    }\n    return {\n      column,\n      line,\n      offset: index\n    };\n  }\n  peek() {\n    return this.buffer.charCodeAt(this.index + 1);\n  }\n  stateText(c) {\n    if (c === 60) {\n      if (this.index > this.sectionStart) {\n        this.cbs.ontext(this.sectionStart, this.index);\n      }\n      this.state = 5;\n      this.sectionStart = this.index;\n    } else if (!this.inVPre && c === this.delimiterOpen[0]) {\n      this.state = 2;\n      this.delimiterIndex = 0;\n      this.stateInterpolationOpen(c);\n    }\n  }\n  stateInterpolationOpen(c) {\n    if (c === this.delimiterOpen[this.delimiterIndex]) {\n      if (this.delimiterIndex === this.delimiterOpen.length - 1) {\n        const start = this.index + 1 - this.delimiterOpen.length;\n        if (start > this.sectionStart) {\n          this.cbs.ontext(this.sectionStart, start);\n        }\n        this.state = 3;\n        this.sectionStart = start;\n      } else {\n        this.delimiterIndex++;\n      }\n    } else if (this.inRCDATA) {\n      this.state = 32;\n      this.stateInRCDATA(c);\n    } else {\n      this.state = 1;\n      this.stateText(c);\n    }\n  }\n  stateInterpolation(c) {\n    if (c === this.delimiterClose[0]) {\n      this.state = 4;\n      this.delimiterIndex = 0;\n      this.stateInterpolationClose(c);\n    }\n  }\n  stateInterpolationClose(c) {\n    if (c === this.delimiterClose[this.delimiterIndex]) {\n      if (this.delimiterIndex === this.delimiterClose.length - 1) {\n        this.cbs.oninterpolation(this.sectionStart, this.index + 1);\n        if (this.inRCDATA) {\n          this.state = 32;\n        } else {\n          this.state = 1;\n        }\n        this.sectionStart = this.index + 1;\n      } else {\n        this.delimiterIndex++;\n      }\n    } else {\n      this.state = 3;\n      this.stateInterpolation(c);\n    }\n  }\n  stateSpecialStartSequence(c) {\n    const isEnd = this.sequenceIndex === this.currentSequence.length;\n    const isMatch = isEnd ? (\n      // If we are at the end of the sequence, make sure the tag name has ended\n      isEndOfTagSection(c)\n    ) : (\n      // Otherwise, do a case-insensitive comparison\n      (c | 32) === this.currentSequence[this.sequenceIndex]\n    );\n    if (!isMatch) {\n      this.inRCDATA = false;\n    } else if (!isEnd) {\n      this.sequenceIndex++;\n      return;\n    }\n    this.sequenceIndex = 0;\n    this.state = 6;\n    this.stateInTagName(c);\n  }\n  /** Look for an end tag. For <title> and <textarea>, also decode entities. */\n  stateInRCDATA(c) {\n    if (this.sequenceIndex === this.currentSequence.length) {\n      if (c === 62 || isWhitespace(c)) {\n        const endOfText = this.index - this.currentSequence.length;\n        if (this.sectionStart < endOfText) {\n          const actualIndex = this.index;\n          this.index = endOfText;\n          this.cbs.ontext(this.sectionStart, endOfText);\n          this.index = actualIndex;\n        }\n        this.sectionStart = endOfText + 2;\n        this.stateInClosingTagName(c);\n        this.inRCDATA = false;\n        return;\n      }\n      this.sequenceIndex = 0;\n    }\n    if ((c | 32) === this.currentSequence[this.sequenceIndex]) {\n      this.sequenceIndex += 1;\n    } else if (this.sequenceIndex === 0) {\n      if (this.currentSequence === Sequences.TitleEnd || this.currentSequence === Sequences.TextareaEnd && !this.inSFCRoot) {\n        if (!this.inVPre && c === this.delimiterOpen[0]) {\n          this.state = 2;\n          this.delimiterIndex = 0;\n          this.stateInterpolationOpen(c);\n        }\n      } else if (this.fastForwardTo(60)) {\n        this.sequenceIndex = 1;\n      }\n    } else {\n      this.sequenceIndex = Number(c === 60);\n    }\n  }\n  stateCDATASequence(c) {\n    if (c === Sequences.Cdata[this.sequenceIndex]) {\n      if (++this.sequenceIndex === Sequences.Cdata.length) {\n        this.state = 28;\n        this.currentSequence = Sequences.CdataEnd;\n        this.sequenceIndex = 0;\n        this.sectionStart = this.index + 1;\n      }\n    } else {\n      this.sequenceIndex = 0;\n      this.state = 23;\n      this.stateInDeclaration(c);\n    }\n  }\n  /**\n   * When we wait for one specific character, we can speed things up\n   * by skipping through the buffer until we find it.\n   *\n   * @returns Whether the character was found.\n   */\n  fastForwardTo(c) {\n    while (++this.index < this.buffer.length) {\n      const cc = this.buffer.charCodeAt(this.index);\n      if (cc === 10) {\n        this.newlines.push(this.index);\n      }\n      if (cc === c) {\n        return true;\n      }\n    }\n    this.index = this.buffer.length - 1;\n    return false;\n  }\n  /**\n   * Comments and CDATA end with `-->` and `]]>`.\n   *\n   * Their common qualities are:\n   * - Their end sequences have a distinct character they start with.\n   * - That character is then repeated, so we have to check multiple repeats.\n   * - All characters but the start character of the sequence can be skipped.\n   */\n  stateInCommentLike(c) {\n    if (c === this.currentSequence[this.sequenceIndex]) {\n      if (++this.sequenceIndex === this.currentSequence.length) {\n        if (this.currentSequence === Sequences.CdataEnd) {\n          this.cbs.oncdata(this.sectionStart, this.index - 2);\n        } else {\n          this.cbs.oncomment(this.sectionStart, this.index - 2);\n        }\n        this.sequenceIndex = 0;\n        this.sectionStart = this.index + 1;\n        this.state = 1;\n      }\n    } else if (this.sequenceIndex === 0) {\n      if (this.fastForwardTo(this.currentSequence[0])) {\n        this.sequenceIndex = 1;\n      }\n    } else if (c !== this.currentSequence[this.sequenceIndex - 1]) {\n      this.sequenceIndex = 0;\n    }\n  }\n  startSpecial(sequence, offset) {\n    this.enterRCDATA(sequence, offset);\n    this.state = 31;\n  }\n  enterRCDATA(sequence, offset) {\n    this.inRCDATA = true;\n    this.currentSequence = sequence;\n    this.sequenceIndex = offset;\n  }\n  stateBeforeTagName(c) {\n    if (c === 33) {\n      this.state = 22;\n      this.sectionStart = this.index + 1;\n    } else if (c === 63) {\n      this.state = 24;\n      this.sectionStart = this.index + 1;\n    } else if (isTagStartChar(c)) {\n      this.sectionStart = this.index;\n      if (this.mode === 0) {\n        this.state = 6;\n      } else if (this.inSFCRoot) {\n        this.state = 34;\n      } else if (!this.inXML) {\n        if (c === 116) {\n          this.state = 30;\n        } else {\n          this.state = c === 115 ? 29 : 6;\n        }\n      } else {\n        this.state = 6;\n      }\n    } else if (c === 47) {\n      this.state = 8;\n    } else {\n      this.state = 1;\n      this.stateText(c);\n    }\n  }\n  stateInTagName(c) {\n    if (isEndOfTagSection(c)) {\n      this.handleTagName(c);\n    }\n  }\n  stateInSFCRootTagName(c) {\n    if (isEndOfTagSection(c)) {\n      const tag = this.buffer.slice(this.sectionStart, this.index);\n      if (tag !== \"template\") {\n        this.enterRCDATA(toCharCodes(`</` + tag), 0);\n      }\n      this.handleTagName(c);\n    }\n  }\n  handleTagName(c) {\n    this.cbs.onopentagname(this.sectionStart, this.index);\n    this.sectionStart = -1;\n    this.state = 11;\n    this.stateBeforeAttrName(c);\n  }\n  stateBeforeClosingTagName(c) {\n    if (isWhitespace(c)) ; else if (c === 62) {\n      if (!!(process.env.NODE_ENV !== \"production\") || false) {\n        this.cbs.onerr(14, this.index);\n      }\n      this.state = 1;\n      this.sectionStart = this.index + 1;\n    } else {\n      this.state = isTagStartChar(c) ? 9 : 27;\n      this.sectionStart = this.index;\n    }\n  }\n  stateInClosingTagName(c) {\n    if (c === 62 || isWhitespace(c)) {\n      this.cbs.onclosetag(this.sectionStart, this.index);\n      this.sectionStart = -1;\n      this.state = 10;\n      this.stateAfterClosingTagName(c);\n    }\n  }\n  stateAfterClosingTagName(c) {\n    if (c === 62) {\n      this.state = 1;\n      this.sectionStart = this.index + 1;\n    }\n  }\n  stateBeforeAttrName(c) {\n    if (c === 62) {\n      this.cbs.onopentagend(this.index);\n      if (this.inRCDATA) {\n        this.state = 32;\n      } else {\n        this.state = 1;\n      }\n      this.sectionStart = this.index + 1;\n    } else if (c === 47) {\n      this.state = 7;\n      if ((!!(process.env.NODE_ENV !== \"production\") || false) && this.peek() !== 62) {\n        this.cbs.onerr(22, this.index);\n      }\n    } else if (c === 60 && this.peek() === 47) {\n      this.cbs.onopentagend(this.index);\n      this.state = 5;\n      this.sectionStart = this.index;\n    } else if (!isWhitespace(c)) {\n      if ((!!(process.env.NODE_ENV !== \"production\") || false) && c === 61) {\n        this.cbs.onerr(\n          19,\n          this.index\n        );\n      }\n      this.handleAttrStart(c);\n    }\n  }\n  handleAttrStart(c) {\n    if (c === 118 && this.peek() === 45) {\n      this.state = 13;\n      this.sectionStart = this.index;\n    } else if (c === 46 || c === 58 || c === 64 || c === 35) {\n      this.cbs.ondirname(this.index, this.index + 1);\n      this.state = 14;\n      this.sectionStart = this.index + 1;\n    } else {\n      this.state = 12;\n      this.sectionStart = this.index;\n    }\n  }\n  stateInSelfClosingTag(c) {\n    if (c === 62) {\n      this.cbs.onselfclosingtag(this.index);\n      this.state = 1;\n      this.sectionStart = this.index + 1;\n      this.inRCDATA = false;\n    } else if (!isWhitespace(c)) {\n      this.state = 11;\n      this.stateBeforeAttrName(c);\n    }\n  }\n  stateInAttrName(c) {\n    if (c === 61 || isEndOfTagSection(c)) {\n      this.cbs.onattribname(this.sectionStart, this.index);\n      this.handleAttrNameEnd(c);\n    } else if ((!!(process.env.NODE_ENV !== \"production\") || false) && (c === 34 || c === 39 || c === 60)) {\n      this.cbs.onerr(\n        17,\n        this.index\n      );\n    }\n  }\n  stateInDirName(c) {\n    if (c === 61 || isEndOfTagSection(c)) {\n      this.cbs.ondirname(this.sectionStart, this.index);\n      this.handleAttrNameEnd(c);\n    } else if (c === 58) {\n      this.cbs.ondirname(this.sectionStart, this.index);\n      this.state = 14;\n      this.sectionStart = this.index + 1;\n    } else if (c === 46) {\n      this.cbs.ondirname(this.sectionStart, this.index);\n      this.state = 16;\n      this.sectionStart = this.index + 1;\n    }\n  }\n  stateInDirArg(c) {\n    if (c === 61 || isEndOfTagSection(c)) {\n      this.cbs.ondirarg(this.sectionStart, this.index);\n      this.handleAttrNameEnd(c);\n    } else if (c === 91) {\n      this.state = 15;\n    } else if (c === 46) {\n      this.cbs.ondirarg(this.sectionStart, this.index);\n      this.state = 16;\n      this.sectionStart = this.index + 1;\n    }\n  }\n  stateInDynamicDirArg(c) {\n    if (c === 93) {\n      this.state = 14;\n    } else if (c === 61 || isEndOfTagSection(c)) {\n      this.cbs.ondirarg(this.sectionStart, this.index + 1);\n      this.handleAttrNameEnd(c);\n      if (!!(process.env.NODE_ENV !== \"production\") || false) {\n        this.cbs.onerr(\n          27,\n          this.index\n        );\n      }\n    }\n  }\n  stateInDirModifier(c) {\n    if (c === 61 || isEndOfTagSection(c)) {\n      this.cbs.ondirmodifier(this.sectionStart, this.index);\n      this.handleAttrNameEnd(c);\n    } else if (c === 46) {\n      this.cbs.ondirmodifier(this.sectionStart, this.index);\n      this.sectionStart = this.index + 1;\n    }\n  }\n  handleAttrNameEnd(c) {\n    this.sectionStart = this.index;\n    this.state = 17;\n    this.cbs.onattribnameend(this.index);\n    this.stateAfterAttrName(c);\n  }\n  stateAfterAttrName(c) {\n    if (c === 61) {\n      this.state = 18;\n    } else if (c === 47 || c === 62) {\n      this.cbs.onattribend(0, this.sectionStart);\n      this.sectionStart = -1;\n      this.state = 11;\n      this.stateBeforeAttrName(c);\n    } else if (!isWhitespace(c)) {\n      this.cbs.onattribend(0, this.sectionStart);\n      this.handleAttrStart(c);\n    }\n  }\n  stateBeforeAttrValue(c) {\n    if (c === 34) {\n      this.state = 19;\n      this.sectionStart = this.index + 1;\n    } else if (c === 39) {\n      this.state = 20;\n      this.sectionStart = this.index + 1;\n    } else if (!isWhitespace(c)) {\n      this.sectionStart = this.index;\n      this.state = 21;\n      this.stateInAttrValueNoQuotes(c);\n    }\n  }\n  handleInAttrValue(c, quote) {\n    if (c === quote || this.fastForwardTo(quote)) {\n      this.cbs.onattribdata(this.sectionStart, this.index);\n      this.sectionStart = -1;\n      this.cbs.onattribend(\n        quote === 34 ? 3 : 2,\n        this.index + 1\n      );\n      this.state = 11;\n    }\n  }\n  stateInAttrValueDoubleQuotes(c) {\n    this.handleInAttrValue(c, 34);\n  }\n  stateInAttrValueSingleQuotes(c) {\n    this.handleInAttrValue(c, 39);\n  }\n  stateInAttrValueNoQuotes(c) {\n    if (isWhitespace(c) || c === 62) {\n      this.cbs.onattribdata(this.sectionStart, this.index);\n      this.sectionStart = -1;\n      this.cbs.onattribend(1, this.index);\n      this.state = 11;\n      this.stateBeforeAttrName(c);\n    } else if ((!!(process.env.NODE_ENV !== \"production\") || false) && c === 34 || c === 39 || c === 60 || c === 61 || c === 96) {\n      this.cbs.onerr(\n        18,\n        this.index\n      );\n    } else ;\n  }\n  stateBeforeDeclaration(c) {\n    if (c === 91) {\n      this.state = 26;\n      this.sequenceIndex = 0;\n    } else {\n      this.state = c === 45 ? 25 : 23;\n    }\n  }\n  stateInDeclaration(c) {\n    if (c === 62 || this.fastForwardTo(62)) {\n      this.state = 1;\n      this.sectionStart = this.index + 1;\n    }\n  }\n  stateInProcessingInstruction(c) {\n    if (c === 62 || this.fastForwardTo(62)) {\n      this.cbs.onprocessinginstruction(this.sectionStart, this.index);\n      this.state = 1;\n      this.sectionStart = this.index + 1;\n    }\n  }\n  stateBeforeComment(c) {\n    if (c === 45) {\n      this.state = 28;\n      this.currentSequence = Sequences.CommentEnd;\n      this.sequenceIndex = 2;\n      this.sectionStart = this.index + 1;\n    } else {\n      this.state = 23;\n    }\n  }\n  stateInSpecialComment(c) {\n    if (c === 62 || this.fastForwardTo(62)) {\n      this.cbs.oncomment(this.sectionStart, this.index);\n      this.state = 1;\n      this.sectionStart = this.index + 1;\n    }\n  }\n  stateBeforeSpecialS(c) {\n    if (c === Sequences.ScriptEnd[3]) {\n      this.startSpecial(Sequences.ScriptEnd, 4);\n    } else if (c === Sequences.StyleEnd[3]) {\n      this.startSpecial(Sequences.StyleEnd, 4);\n    } else {\n      this.state = 6;\n      this.stateInTagName(c);\n    }\n  }\n  stateBeforeSpecialT(c) {\n    if (c === Sequences.TitleEnd[3]) {\n      this.startSpecial(Sequences.TitleEnd, 4);\n    } else if (c === Sequences.TextareaEnd[3]) {\n      this.startSpecial(Sequences.TextareaEnd, 4);\n    } else {\n      this.state = 6;\n      this.stateInTagName(c);\n    }\n  }\n  startEntity() {\n  }\n  stateInEntity() {\n  }\n  /**\n   * Iterates through the buffer, calling the function corresponding to the current state.\n   *\n   * States that are more likely to be hit are higher up, as a performance improvement.\n   */\n  parse(input) {\n    this.buffer = input;\n    while (this.index < this.buffer.length) {\n      const c = this.buffer.charCodeAt(this.index);\n      if (c === 10) {\n        this.newlines.push(this.index);\n      }\n      switch (this.state) {\n        case 1: {\n          this.stateText(c);\n          break;\n        }\n        case 2: {\n          this.stateInterpolationOpen(c);\n          break;\n        }\n        case 3: {\n          this.stateInterpolation(c);\n          break;\n        }\n        case 4: {\n          this.stateInterpolationClose(c);\n          break;\n        }\n        case 31: {\n          this.stateSpecialStartSequence(c);\n          break;\n        }\n        case 32: {\n          this.stateInRCDATA(c);\n          break;\n        }\n        case 26: {\n          this.stateCDATASequence(c);\n          break;\n        }\n        case 19: {\n          this.stateInAttrValueDoubleQuotes(c);\n          break;\n        }\n        case 12: {\n          this.stateInAttrName(c);\n          break;\n        }\n        case 13: {\n          this.stateInDirName(c);\n          break;\n        }\n        case 14: {\n          this.stateInDirArg(c);\n          break;\n        }\n        case 15: {\n          this.stateInDynamicDirArg(c);\n          break;\n        }\n        case 16: {\n          this.stateInDirModifier(c);\n          break;\n        }\n        case 28: {\n          this.stateInCommentLike(c);\n          break;\n        }\n        case 27: {\n          this.stateInSpecialComment(c);\n          break;\n        }\n        case 11: {\n          this.stateBeforeAttrName(c);\n          break;\n        }\n        case 6: {\n          this.stateInTagName(c);\n          break;\n        }\n        case 34: {\n          this.stateInSFCRootTagName(c);\n          break;\n        }\n        case 9: {\n          this.stateInClosingTagName(c);\n          break;\n        }\n        case 5: {\n          this.stateBeforeTagName(c);\n          break;\n        }\n        case 17: {\n          this.stateAfterAttrName(c);\n          break;\n        }\n        case 20: {\n          this.stateInAttrValueSingleQuotes(c);\n          break;\n        }\n        case 18: {\n          this.stateBeforeAttrValue(c);\n          break;\n        }\n        case 8: {\n          this.stateBeforeClosingTagName(c);\n          break;\n        }\n        case 10: {\n          this.stateAfterClosingTagName(c);\n          break;\n        }\n        case 29: {\n          this.stateBeforeSpecialS(c);\n          break;\n        }\n        case 30: {\n          this.stateBeforeSpecialT(c);\n          break;\n        }\n        case 21: {\n          this.stateInAttrValueNoQuotes(c);\n          break;\n        }\n        case 7: {\n          this.stateInSelfClosingTag(c);\n          break;\n        }\n        case 23: {\n          this.stateInDeclaration(c);\n          break;\n        }\n        case 22: {\n          this.stateBeforeDeclaration(c);\n          break;\n        }\n        case 25: {\n          this.stateBeforeComment(c);\n          break;\n        }\n        case 24: {\n          this.stateInProcessingInstruction(c);\n          break;\n        }\n        case 33: {\n          this.stateInEntity();\n          break;\n        }\n      }\n      this.index++;\n    }\n    this.cleanup();\n    this.finish();\n  }\n  /**\n   * Remove data that has already been consumed from the buffer.\n   */\n  cleanup() {\n    if (this.sectionStart !== this.index) {\n      if (this.state === 1 || this.state === 32 && this.sequenceIndex === 0) {\n        this.cbs.ontext(this.sectionStart, this.index);\n        this.sectionStart = this.index;\n      } else if (this.state === 19 || this.state === 20 || this.state === 21) {\n        this.cbs.onattribdata(this.sectionStart, this.index);\n        this.sectionStart = this.index;\n      }\n    }\n  }\n  finish() {\n    this.handleTrailingData();\n    this.cbs.onend();\n  }\n  /** Handle any trailing data. */\n  handleTrailingData() {\n    const endIndex = this.buffer.length;\n    if (this.sectionStart >= endIndex) {\n      return;\n    }\n    if (this.state === 28) {\n      if (this.currentSequence === Sequences.CdataEnd) {\n        this.cbs.oncdata(this.sectionStart, endIndex);\n      } else {\n        this.cbs.oncomment(this.sectionStart, endIndex);\n      }\n    } else if (this.state === 6 || this.state === 11 || this.state === 18 || this.state === 17 || this.state === 12 || this.state === 13 || this.state === 14 || this.state === 15 || this.state === 16 || this.state === 20 || this.state === 19 || this.state === 21 || this.state === 9) ; else {\n      this.cbs.ontext(this.sectionStart, endIndex);\n    }\n  }\n  emitCodePoint(cp, consumed) {\n  }\n}\n\nconst CompilerDeprecationTypes = {\n  \"COMPILER_IS_ON_ELEMENT\": \"COMPILER_IS_ON_ELEMENT\",\n  \"COMPILER_V_BIND_SYNC\": \"COMPILER_V_BIND_SYNC\",\n  \"COMPILER_V_BIND_OBJECT_ORDER\": \"COMPILER_V_BIND_OBJECT_ORDER\",\n  \"COMPILER_V_ON_NATIVE\": \"COMPILER_V_ON_NATIVE\",\n  \"COMPILER_V_IF_V_FOR_PRECEDENCE\": \"COMPILER_V_IF_V_FOR_PRECEDENCE\",\n  \"COMPILER_NATIVE_TEMPLATE\": \"COMPILER_NATIVE_TEMPLATE\",\n  \"COMPILER_INLINE_TEMPLATE\": \"COMPILER_INLINE_TEMPLATE\",\n  \"COMPILER_FILTERS\": \"COMPILER_FILTERS\"\n};\nconst deprecationData = {\n  [\"COMPILER_IS_ON_ELEMENT\"]: {\n    message: `Platform-native elements with \"is\" prop will no longer be treated as components in Vue 3 unless the \"is\" value is explicitly prefixed with \"vue:\".`,\n    link: `https://v3-migration.vuejs.org/breaking-changes/custom-elements-interop.html`\n  },\n  [\"COMPILER_V_BIND_SYNC\"]: {\n    message: (key) => `.sync modifier for v-bind has been removed. Use v-model with argument instead. \\`v-bind:${key}.sync\\` should be changed to \\`v-model:${key}\\`.`,\n    link: `https://v3-migration.vuejs.org/breaking-changes/v-model.html`\n  },\n  [\"COMPILER_V_BIND_OBJECT_ORDER\"]: {\n    message: `v-bind=\"obj\" usage is now order sensitive and behaves like JavaScript object spread: it will now overwrite an existing non-mergeable attribute that appears before v-bind in the case of conflict. To retain 2.x behavior, move v-bind to make it the first attribute. You can also suppress this warning if the usage is intended.`,\n    link: `https://v3-migration.vuejs.org/breaking-changes/v-bind.html`\n  },\n  [\"COMPILER_V_ON_NATIVE\"]: {\n    message: `.native modifier for v-on has been removed as is no longer necessary.`,\n    link: `https://v3-migration.vuejs.org/breaking-changes/v-on-native-modifier-removed.html`\n  },\n  [\"COMPILER_V_IF_V_FOR_PRECEDENCE\"]: {\n    message: `v-if / v-for precedence when used on the same element has changed in Vue 3: v-if now takes higher precedence and will no longer have access to v-for scope variables. It is best to avoid the ambiguity with <template> tags or use a computed property that filters v-for data source.`,\n    link: `https://v3-migration.vuejs.org/breaking-changes/v-if-v-for.html`\n  },\n  [\"COMPILER_NATIVE_TEMPLATE\"]: {\n    message: `<template> with no special directives will render as a native template element instead of its inner content in Vue 3.`\n  },\n  [\"COMPILER_INLINE_TEMPLATE\"]: {\n    message: `\"inline-template\" has been removed in Vue 3.`,\n    link: `https://v3-migration.vuejs.org/breaking-changes/inline-template-attribute.html`\n  },\n  [\"COMPILER_FILTERS\"]: {\n    message: `filters have been removed in Vue 3. The \"|\" symbol will be treated as native JavaScript bitwise OR operator. Use method calls or computed properties instead.`,\n    link: `https://v3-migration.vuejs.org/breaking-changes/filters.html`\n  }\n};\nfunction getCompatValue(key, { compatConfig }) {\n  const value = compatConfig && compatConfig[key];\n  if (key === \"MODE\") {\n    return value || 3;\n  } else {\n    return value;\n  }\n}\nfunction isCompatEnabled(key, context) {\n  const mode = getCompatValue(\"MODE\", context);\n  const value = getCompatValue(key, context);\n  return mode === 3 ? value === true : value !== false;\n}\nfunction checkCompatEnabled(key, context, loc, ...args) {\n  const enabled = isCompatEnabled(key, context);\n  if (!!(process.env.NODE_ENV !== \"production\") && enabled) {\n    warnDeprecation(key, context, loc, ...args);\n  }\n  return enabled;\n}\nfunction warnDeprecation(key, context, loc, ...args) {\n  const val = getCompatValue(key, context);\n  if (val === \"suppress-warning\") {\n    return;\n  }\n  const { message, link } = deprecationData[key];\n  const msg = `(deprecation ${key}) ${typeof message === \"function\" ? message(...args) : message}${link ? `\n  Details: ${link}` : ``}`;\n  const err = new SyntaxError(msg);\n  err.code = key;\n  if (loc) err.loc = loc;\n  context.onWarn(err);\n}\n\nfunction defaultOnError(error) {\n  throw error;\n}\nfunction defaultOnWarn(msg) {\n  !!(process.env.NODE_ENV !== \"production\") && console.warn(`[Vue warn] ${msg.message}`);\n}\nfunction createCompilerError(code, loc, messages, additionalMessage) {\n  const msg = !!(process.env.NODE_ENV !== \"production\") || false ? (messages || errorMessages)[code] + (additionalMessage || ``) : `https://vuejs.org/error-reference/#compiler-${code}`;\n  const error = new SyntaxError(String(msg));\n  error.code = code;\n  error.loc = loc;\n  return error;\n}\nconst ErrorCodes = {\n  \"ABRUPT_CLOSING_OF_EMPTY_COMMENT\": 0,\n  \"0\": \"ABRUPT_CLOSING_OF_EMPTY_COMMENT\",\n  \"CDATA_IN_HTML_CONTENT\": 1,\n  \"1\": \"CDATA_IN_HTML_CONTENT\",\n  \"DUPLICATE_ATTRIBUTE\": 2,\n  \"2\": \"DUPLICATE_ATTRIBUTE\",\n  \"END_TAG_WITH_ATTRIBUTES\": 3,\n  \"3\": \"END_TAG_WITH_ATTRIBUTES\",\n  \"END_TAG_WITH_TRAILING_SOLIDUS\": 4,\n  \"4\": \"END_TAG_WITH_TRAILING_SOLIDUS\",\n  \"EOF_BEFORE_TAG_NAME\": 5,\n  \"5\": \"EOF_BEFORE_TAG_NAME\",\n  \"EOF_IN_CDATA\": 6,\n  \"6\": \"EOF_IN_CDATA\",\n  \"EOF_IN_COMMENT\": 7,\n  \"7\": \"EOF_IN_COMMENT\",\n  \"EOF_IN_SCRIPT_HTML_COMMENT_LIKE_TEXT\": 8,\n  \"8\": \"EOF_IN_SCRIPT_HTML_COMMENT_LIKE_TEXT\",\n  \"EOF_IN_TAG\": 9,\n  \"9\": \"EOF_IN_TAG\",\n  \"INCORRECTLY_CLOSED_COMMENT\": 10,\n  \"10\": \"INCORRECTLY_CLOSED_COMMENT\",\n  \"INCORRECTLY_OPENED_COMMENT\": 11,\n  \"11\": \"INCORRECTLY_OPENED_COMMENT\",\n  \"INVALID_FIRST_CHARACTER_OF_TAG_NAME\": 12,\n  \"12\": \"INVALID_FIRST_CHARACTER_OF_TAG_NAME\",\n  \"MISSING_ATTRIBUTE_VALUE\": 13,\n  \"13\": \"MISSING_ATTRIBUTE_VALUE\",\n  \"MISSING_END_TAG_NAME\": 14,\n  \"14\": \"MISSING_END_TAG_NAME\",\n  \"MISSING_WHITESPACE_BETWEEN_ATTRIBUTES\": 15,\n  \"15\": \"MISSING_WHITESPACE_BETWEEN_ATTRIBUTES\",\n  \"NESTED_COMMENT\": 16,\n  \"16\": \"NESTED_COMMENT\",\n  \"UNEXPECTED_CHARACTER_IN_ATTRIBUTE_NAME\": 17,\n  \"17\": \"UNEXPECTED_CHARACTER_IN_ATTRIBUTE_NAME\",\n  \"UNEXPECTED_CHARACTER_IN_UNQUOTED_ATTRIBUTE_VALUE\": 18,\n  \"18\": \"UNEXPECTED_CHARACTER_IN_UNQUOTED_ATTRIBUTE_VALUE\",\n  \"UNEXPECTED_EQUALS_SIGN_BEFORE_ATTRIBUTE_NAME\": 19,\n  \"19\": \"UNEXPECTED_EQUALS_SIGN_BEFORE_ATTRIBUTE_NAME\",\n  \"UNEXPECTED_NULL_CHARACTER\": 20,\n  \"20\": \"UNEXPECTED_NULL_CHARACTER\",\n  \"UNEXPECTED_QUESTION_MARK_INSTEAD_OF_TAG_NAME\": 21,\n  \"21\": \"UNEXPECTED_QUESTION_MARK_INSTEAD_OF_TAG_NAME\",\n  \"UNEXPECTED_SOLIDUS_IN_TAG\": 22,\n  \"22\": \"UNEXPECTED_SOLIDUS_IN_TAG\",\n  \"X_INVALID_END_TAG\": 23,\n  \"23\": \"X_INVALID_END_TAG\",\n  \"X_MISSING_END_TAG\": 24,\n  \"24\": \"X_MISSING_END_TAG\",\n  \"X_MISSING_INTERPOLATION_END\": 25,\n  \"25\": \"X_MISSING_INTERPOLATION_END\",\n  \"X_MISSING_DIRECTIVE_NAME\": 26,\n  \"26\": \"X_MISSING_DIRECTIVE_NAME\",\n  \"X_MISSING_DYNAMIC_DIRECTIVE_ARGUMENT_END\": 27,\n  \"27\": \"X_MISSING_DYNAMIC_DIRECTIVE_ARGUMENT_END\",\n  \"X_V_IF_NO_EXPRESSION\": 28,\n  \"28\": \"X_V_IF_NO_EXPRESSION\",\n  \"X_V_IF_SAME_KEY\": 29,\n  \"29\": \"X_V_IF_SAME_KEY\",\n  \"X_V_ELSE_NO_ADJACENT_IF\": 30,\n  \"30\": \"X_V_ELSE_NO_ADJACENT_IF\",\n  \"X_V_FOR_NO_EXPRESSION\": 31,\n  \"31\": \"X_V_FOR_NO_EXPRESSION\",\n  \"X_V_FOR_MALFORMED_EXPRESSION\": 32,\n  \"32\": \"X_V_FOR_MALFORMED_EXPRESSION\",\n  \"X_V_FOR_TEMPLATE_KEY_PLACEMENT\": 33,\n  \"33\": \"X_V_FOR_TEMPLATE_KEY_PLACEMENT\",\n  \"X_V_BIND_NO_EXPRESSION\": 34,\n  \"34\": \"X_V_BIND_NO_EXPRESSION\",\n  \"X_V_ON_NO_EXPRESSION\": 35,\n  \"35\": \"X_V_ON_NO_EXPRESSION\",\n  \"X_V_SLOT_UNEXPECTED_DIRECTIVE_ON_SLOT_OUTLET\": 36,\n  \"36\": \"X_V_SLOT_UNEXPECTED_DIRECTIVE_ON_SLOT_OUTLET\",\n  \"X_V_SLOT_MIXED_SLOT_USAGE\": 37,\n  \"37\": \"X_V_SLOT_MIXED_SLOT_USAGE\",\n  \"X_V_SLOT_DUPLICATE_SLOT_NAMES\": 38,\n  \"38\": \"X_V_SLOT_DUPLICATE_SLOT_NAMES\",\n  \"X_V_SLOT_EXTRANEOUS_DEFAULT_SLOT_CHILDREN\": 39,\n  \"39\": \"X_V_SLOT_EXTRANEOUS_DEFAULT_SLOT_CHILDREN\",\n  \"X_V_SLOT_MISPLACED\": 40,\n  \"40\": \"X_V_SLOT_MISPLACED\",\n  \"X_V_MODEL_NO_EXPRESSION\": 41,\n  \"41\": \"X_V_MODEL_NO_EXPRESSION\",\n  \"X_V_MODEL_MALFORMED_EXPRESSION\": 42,\n  \"42\": \"X_V_MODEL_MALFORMED_EXPRESSION\",\n  \"X_V_MODEL_ON_SCOPE_VARIABLE\": 43,\n  \"43\": \"X_V_MODEL_ON_SCOPE_VARIABLE\",\n  \"X_V_MODEL_ON_PROPS\": 44,\n  \"44\": \"X_V_MODEL_ON_PROPS\",\n  \"X_INVALID_EXPRESSION\": 45,\n  \"45\": \"X_INVALID_EXPRESSION\",\n  \"X_KEEP_ALIVE_INVALID_CHILDREN\": 46,\n  \"46\": \"X_KEEP_ALIVE_INVALID_CHILDREN\",\n  \"X_PREFIX_ID_NOT_SUPPORTED\": 47,\n  \"47\": \"X_PREFIX_ID_NOT_SUPPORTED\",\n  \"X_MODULE_MODE_NOT_SUPPORTED\": 48,\n  \"48\": \"X_MODULE_MODE_NOT_SUPPORTED\",\n  \"X_CACHE_HANDLER_NOT_SUPPORTED\": 49,\n  \"49\": \"X_CACHE_HANDLER_NOT_SUPPORTED\",\n  \"X_SCOPE_ID_NOT_SUPPORTED\": 50,\n  \"50\": \"X_SCOPE_ID_NOT_SUPPORTED\",\n  \"X_VNODE_HOOKS\": 51,\n  \"51\": \"X_VNODE_HOOKS\",\n  \"X_V_BIND_INVALID_SAME_NAME_ARGUMENT\": 52,\n  \"52\": \"X_V_BIND_INVALID_SAME_NAME_ARGUMENT\",\n  \"__EXTEND_POINT__\": 53,\n  \"53\": \"__EXTEND_POINT__\"\n};\nconst errorMessages = {\n  // parse errors\n  [0]: \"Illegal comment.\",\n  [1]: \"CDATA section is allowed only in XML context.\",\n  [2]: \"Duplicate attribute.\",\n  [3]: \"End tag cannot have attributes.\",\n  [4]: \"Illegal '/' in tags.\",\n  [5]: \"Unexpected EOF in tag.\",\n  [6]: \"Unexpected EOF in CDATA section.\",\n  [7]: \"Unexpected EOF in comment.\",\n  [8]: \"Unexpected EOF in script.\",\n  [9]: \"Unexpected EOF in tag.\",\n  [10]: \"Incorrectly closed comment.\",\n  [11]: \"Incorrectly opened comment.\",\n  [12]: \"Illegal tag name. Use '&lt;' to print '<'.\",\n  [13]: \"Attribute value was expected.\",\n  [14]: \"End tag name was expected.\",\n  [15]: \"Whitespace was expected.\",\n  [16]: \"Unexpected '<!--' in comment.\",\n  [17]: `Attribute name cannot contain U+0022 (\"), U+0027 ('), and U+003C (<).`,\n  [18]: \"Unquoted attribute value cannot contain U+0022 (\\\"), U+0027 ('), U+003C (<), U+003D (=), and U+0060 (`).\",\n  [19]: \"Attribute name cannot start with '='.\",\n  [21]: \"'<?' is allowed only in XML context.\",\n  [20]: `Unexpected null character.`,\n  [22]: \"Illegal '/' in tags.\",\n  // Vue-specific parse errors\n  [23]: \"Invalid end tag.\",\n  [24]: \"Element is missing end tag.\",\n  [25]: \"Interpolation end sign was not found.\",\n  [27]: \"End bracket for dynamic directive argument was not found. Note that dynamic directive argument cannot contain spaces.\",\n  [26]: \"Legal directive name was expected.\",\n  // transform errors\n  [28]: `v-if/v-else-if is missing expression.`,\n  [29]: `v-if/else branches must use unique keys.`,\n  [30]: `v-else/v-else-if has no adjacent v-if or v-else-if.`,\n  [31]: `v-for is missing expression.`,\n  [32]: `v-for has invalid expression.`,\n  [33]: `<template v-for> key should be placed on the <template> tag.`,\n  [34]: `v-bind is missing expression.`,\n  [52]: `v-bind with same-name shorthand only allows static argument.`,\n  [35]: `v-on is missing expression.`,\n  [36]: `Unexpected custom directive on <slot> outlet.`,\n  [37]: `Mixed v-slot usage on both the component and nested <template>. When there are multiple named slots, all slots should use <template> syntax to avoid scope ambiguity.`,\n  [38]: `Duplicate slot names found. `,\n  [39]: `Extraneous children found when component already has explicitly named default slot. These children will be ignored.`,\n  [40]: `v-slot can only be used on components or <template> tags.`,\n  [41]: `v-model is missing expression.`,\n  [42]: `v-model value must be a valid JavaScript member expression.`,\n  [43]: `v-model cannot be used on v-for or v-slot scope variables because they are not writable.`,\n  [44]: `v-model cannot be used on a prop, because local prop bindings are not writable.\nUse a v-bind binding combined with a v-on listener that emits update:x event instead.`,\n  [45]: `Error parsing JavaScript expression: `,\n  [46]: `<KeepAlive> expects exactly one child component.`,\n  [51]: `@vnode-* hooks in templates are no longer supported. Use the vue: prefix instead. For example, @vnode-mounted should be changed to @vue:mounted. @vnode-* hooks support has been removed in 3.4.`,\n  // generic errors\n  [47]: `\"prefixIdentifiers\" option is not supported in this build of compiler.`,\n  [48]: `ES module mode is not supported in this build of compiler.`,\n  [49]: `\"cacheHandlers\" option is only supported when the \"prefixIdentifiers\" option is enabled.`,\n  [50]: `\"scopeId\" option is only supported in module mode.`,\n  // just to fulfill types\n  [53]: ``\n};\n\nfunction walkIdentifiers(root, onIdentifier, includeAll = false, parentStack = [], knownIds = /* @__PURE__ */ Object.create(null)) {\n  {\n    return;\n  }\n}\nfunction isReferencedIdentifier(id, parent, parentStack) {\n  {\n    return false;\n  }\n}\nfunction isInDestructureAssignment(parent, parentStack) {\n  if (parent && (parent.type === \"ObjectProperty\" || parent.type === \"ArrayPattern\")) {\n    let i = parentStack.length;\n    while (i--) {\n      const p = parentStack[i];\n      if (p.type === \"AssignmentExpression\") {\n        return true;\n      } else if (p.type !== \"ObjectProperty\" && !p.type.endsWith(\"Pattern\")) {\n        break;\n      }\n    }\n  }\n  return false;\n}\nfunction isInNewExpression(parentStack) {\n  let i = parentStack.length;\n  while (i--) {\n    const p = parentStack[i];\n    if (p.type === \"NewExpression\") {\n      return true;\n    } else if (p.type !== \"MemberExpression\") {\n      break;\n    }\n  }\n  return false;\n}\nfunction walkFunctionParams(node, onIdent) {\n  for (const p of node.params) {\n    for (const id of extractIdentifiers(p)) {\n      onIdent(id);\n    }\n  }\n}\nfunction walkBlockDeclarations(block, onIdent) {\n  for (const stmt of block.body) {\n    if (stmt.type === \"VariableDeclaration\") {\n      if (stmt.declare) continue;\n      for (const decl of stmt.declarations) {\n        for (const id of extractIdentifiers(decl.id)) {\n          onIdent(id);\n        }\n      }\n    } else if (stmt.type === \"FunctionDeclaration\" || stmt.type === \"ClassDeclaration\") {\n      if (stmt.declare || !stmt.id) continue;\n      onIdent(stmt.id);\n    } else if (isForStatement(stmt)) {\n      walkForStatement(stmt, true, onIdent);\n    }\n  }\n}\nfunction isForStatement(stmt) {\n  return stmt.type === \"ForOfStatement\" || stmt.type === \"ForInStatement\" || stmt.type === \"ForStatement\";\n}\nfunction walkForStatement(stmt, isVar, onIdent) {\n  const variable = stmt.type === \"ForStatement\" ? stmt.init : stmt.left;\n  if (variable && variable.type === \"VariableDeclaration\" && (variable.kind === \"var\" ? isVar : !isVar)) {\n    for (const decl of variable.declarations) {\n      for (const id of extractIdentifiers(decl.id)) {\n        onIdent(id);\n      }\n    }\n  }\n}\nfunction extractIdentifiers(param, nodes = []) {\n  switch (param.type) {\n    case \"Identifier\":\n      nodes.push(param);\n      break;\n    case \"MemberExpression\":\n      let object = param;\n      while (object.type === \"MemberExpression\") {\n        object = object.object;\n      }\n      nodes.push(object);\n      break;\n    case \"ObjectPattern\":\n      for (const prop of param.properties) {\n        if (prop.type === \"RestElement\") {\n          extractIdentifiers(prop.argument, nodes);\n        } else {\n          extractIdentifiers(prop.value, nodes);\n        }\n      }\n      break;\n    case \"ArrayPattern\":\n      param.elements.forEach((element) => {\n        if (element) extractIdentifiers(element, nodes);\n      });\n      break;\n    case \"RestElement\":\n      extractIdentifiers(param.argument, nodes);\n      break;\n    case \"AssignmentPattern\":\n      extractIdentifiers(param.left, nodes);\n      break;\n  }\n  return nodes;\n}\nconst isFunctionType = (node) => {\n  return /Function(?:Expression|Declaration)$|Method$/.test(node.type);\n};\nconst isStaticProperty = (node) => node && (node.type === \"ObjectProperty\" || node.type === \"ObjectMethod\") && !node.computed;\nconst isStaticPropertyKey = (node, parent) => isStaticProperty(parent) && parent.key === node;\nconst TS_NODE_TYPES = [\n  \"TSAsExpression\",\n  // foo as number\n  \"TSTypeAssertion\",\n  // (<number>foo)\n  \"TSNonNullExpression\",\n  // foo!\n  \"TSInstantiationExpression\",\n  // foo<string>\n  \"TSSatisfiesExpression\"\n  // foo satisfies T\n];\nfunction unwrapTSNode(node) {\n  if (TS_NODE_TYPES.includes(node.type)) {\n    return unwrapTSNode(node.expression);\n  } else {\n    return node;\n  }\n}\n\nconst isStaticExp = (p) => p.type === 4 && p.isStatic;\nfunction isCoreComponent(tag) {\n  switch (tag) {\n    case \"Teleport\":\n    case \"teleport\":\n      return TELEPORT;\n    case \"Suspense\":\n    case \"suspense\":\n      return SUSPENSE;\n    case \"KeepAlive\":\n    case \"keep-alive\":\n      return KEEP_ALIVE;\n    case \"BaseTransition\":\n    case \"base-transition\":\n      return BASE_TRANSITION;\n  }\n}\nconst nonIdentifierRE = /^\\d|[^\\$\\w\\xA0-\\uFFFF]/;\nconst isSimpleIdentifier = (name) => !nonIdentifierRE.test(name);\nconst validFirstIdentCharRE = /[A-Za-z_$\\xA0-\\uFFFF]/;\nconst validIdentCharRE = /[\\.\\?\\w$\\xA0-\\uFFFF]/;\nconst whitespaceRE = /\\s+[.[]\\s*|\\s*[.[]\\s+/g;\nconst getExpSource = (exp) => exp.type === 4 ? exp.content : exp.loc.source;\nconst isMemberExpressionBrowser = (exp) => {\n  const path = getExpSource(exp).trim().replace(whitespaceRE, (s) => s.trim());\n  let state = 0 /* inMemberExp */;\n  let stateStack = [];\n  let currentOpenBracketCount = 0;\n  let currentOpenParensCount = 0;\n  let currentStringType = null;\n  for (let i = 0; i < path.length; i++) {\n    const char = path.charAt(i);\n    switch (state) {\n      case 0 /* inMemberExp */:\n        if (char === \"[\") {\n          stateStack.push(state);\n          state = 1 /* inBrackets */;\n          currentOpenBracketCount++;\n        } else if (char === \"(\") {\n          stateStack.push(state);\n          state = 2 /* inParens */;\n          currentOpenParensCount++;\n        } else if (!(i === 0 ? validFirstIdentCharRE : validIdentCharRE).test(char)) {\n          return false;\n        }\n        break;\n      case 1 /* inBrackets */:\n        if (char === `'` || char === `\"` || char === \"`\") {\n          stateStack.push(state);\n          state = 3 /* inString */;\n          currentStringType = char;\n        } else if (char === `[`) {\n          currentOpenBracketCount++;\n        } else if (char === `]`) {\n          if (!--currentOpenBracketCount) {\n            state = stateStack.pop();\n          }\n        }\n        break;\n      case 2 /* inParens */:\n        if (char === `'` || char === `\"` || char === \"`\") {\n          stateStack.push(state);\n          state = 3 /* inString */;\n          currentStringType = char;\n        } else if (char === `(`) {\n          currentOpenParensCount++;\n        } else if (char === `)`) {\n          if (i === path.length - 1) {\n            return false;\n          }\n          if (!--currentOpenParensCount) {\n            state = stateStack.pop();\n          }\n        }\n        break;\n      case 3 /* inString */:\n        if (char === currentStringType) {\n          state = stateStack.pop();\n          currentStringType = null;\n        }\n        break;\n    }\n  }\n  return !currentOpenBracketCount && !currentOpenParensCount;\n};\nconst isMemberExpressionNode = NOOP ;\nconst isMemberExpression = isMemberExpressionBrowser ;\nconst fnExpRE = /^\\s*(async\\s*)?(\\([^)]*?\\)|[\\w$_]+)\\s*(:[^=]+)?=>|^\\s*(async\\s+)?function(?:\\s+[\\w$]+)?\\s*\\(/;\nconst isFnExpressionBrowser = (exp) => fnExpRE.test(getExpSource(exp));\nconst isFnExpressionNode = NOOP ;\nconst isFnExpression = isFnExpressionBrowser ;\nfunction advancePositionWithClone(pos, source, numberOfCharacters = source.length) {\n  return advancePositionWithMutation(\n    {\n      offset: pos.offset,\n      line: pos.line,\n      column: pos.column\n    },\n    source,\n    numberOfCharacters\n  );\n}\nfunction advancePositionWithMutation(pos, source, numberOfCharacters = source.length) {\n  let linesCount = 0;\n  let lastNewLinePos = -1;\n  for (let i = 0; i < numberOfCharacters; i++) {\n    if (source.charCodeAt(i) === 10) {\n      linesCount++;\n      lastNewLinePos = i;\n    }\n  }\n  pos.offset += numberOfCharacters;\n  pos.line += linesCount;\n  pos.column = lastNewLinePos === -1 ? pos.column + numberOfCharacters : numberOfCharacters - lastNewLinePos;\n  return pos;\n}\nfunction assert(condition, msg) {\n  if (!condition) {\n    throw new Error(msg || `unexpected compiler condition`);\n  }\n}\nfunction findDir(node, name, allowEmpty = false) {\n  for (let i = 0; i < node.props.length; i++) {\n    const p = node.props[i];\n    if (p.type === 7 && (allowEmpty || p.exp) && (isString(name) ? p.name === name : name.test(p.name))) {\n      return p;\n    }\n  }\n}\nfunction findProp(node, name, dynamicOnly = false, allowEmpty = false) {\n  for (let i = 0; i < node.props.length; i++) {\n    const p = node.props[i];\n    if (p.type === 6) {\n      if (dynamicOnly) continue;\n      if (p.name === name && (p.value || allowEmpty)) {\n        return p;\n      }\n    } else if (p.name === \"bind\" && (p.exp || allowEmpty) && isStaticArgOf(p.arg, name)) {\n      return p;\n    }\n  }\n}\nfunction isStaticArgOf(arg, name) {\n  return !!(arg && isStaticExp(arg) && arg.content === name);\n}\nfunction hasDynamicKeyVBind(node) {\n  return node.props.some(\n    (p) => p.type === 7 && p.name === \"bind\" && (!p.arg || // v-bind=\"obj\"\n    p.arg.type !== 4 || // v-bind:[_ctx.foo]\n    !p.arg.isStatic)\n    // v-bind:[foo]\n  );\n}\nfunction isText$1(node) {\n  return node.type === 5 || node.type === 2;\n}\nfunction isVSlot(p) {\n  return p.type === 7 && p.name === \"slot\";\n}\nfunction isTemplateNode(node) {\n  return node.type === 1 && node.tagType === 3;\n}\nfunction isSlotOutlet(node) {\n  return node.type === 1 && node.tagType === 2;\n}\nconst propsHelperSet = /* @__PURE__ */ new Set([NORMALIZE_PROPS, GUARD_REACTIVE_PROPS]);\nfunction getUnnormalizedProps(props, callPath = []) {\n  if (props && !isString(props) && props.type === 14) {\n    const callee = props.callee;\n    if (!isString(callee) && propsHelperSet.has(callee)) {\n      return getUnnormalizedProps(\n        props.arguments[0],\n        callPath.concat(props)\n      );\n    }\n  }\n  return [props, callPath];\n}\nfunction injectProp(node, prop, context) {\n  let propsWithInjection;\n  let props = node.type === 13 ? node.props : node.arguments[2];\n  let callPath = [];\n  let parentCall;\n  if (props && !isString(props) && props.type === 14) {\n    const ret = getUnnormalizedProps(props);\n    props = ret[0];\n    callPath = ret[1];\n    parentCall = callPath[callPath.length - 1];\n  }\n  if (props == null || isString(props)) {\n    propsWithInjection = createObjectExpression([prop]);\n  } else if (props.type === 14) {\n    const first = props.arguments[0];\n    if (!isString(first) && first.type === 15) {\n      if (!hasProp(prop, first)) {\n        first.properties.unshift(prop);\n      }\n    } else {\n      if (props.callee === TO_HANDLERS) {\n        propsWithInjection = createCallExpression(context.helper(MERGE_PROPS), [\n          createObjectExpression([prop]),\n          props\n        ]);\n      } else {\n        props.arguments.unshift(createObjectExpression([prop]));\n      }\n    }\n    !propsWithInjection && (propsWithInjection = props);\n  } else if (props.type === 15) {\n    if (!hasProp(prop, props)) {\n      props.properties.unshift(prop);\n    }\n    propsWithInjection = props;\n  } else {\n    propsWithInjection = createCallExpression(context.helper(MERGE_PROPS), [\n      createObjectExpression([prop]),\n      props\n    ]);\n    if (parentCall && parentCall.callee === GUARD_REACTIVE_PROPS) {\n      parentCall = callPath[callPath.length - 2];\n    }\n  }\n  if (node.type === 13) {\n    if (parentCall) {\n      parentCall.arguments[0] = propsWithInjection;\n    } else {\n      node.props = propsWithInjection;\n    }\n  } else {\n    if (parentCall) {\n      parentCall.arguments[0] = propsWithInjection;\n    } else {\n      node.arguments[2] = propsWithInjection;\n    }\n  }\n}\nfunction hasProp(prop, props) {\n  let result = false;\n  if (prop.key.type === 4) {\n    const propKeyName = prop.key.content;\n    result = props.properties.some(\n      (p) => p.key.type === 4 && p.key.content === propKeyName\n    );\n  }\n  return result;\n}\nfunction toValidAssetId(name, type) {\n  return `_${type}_${name.replace(/[^\\w]/g, (searchValue, replaceValue) => {\n    return searchValue === \"-\" ? \"_\" : name.charCodeAt(replaceValue).toString();\n  })}`;\n}\nfunction hasScopeRef(node, ids) {\n  if (!node || Object.keys(ids).length === 0) {\n    return false;\n  }\n  switch (node.type) {\n    case 1:\n      for (let i = 0; i < node.props.length; i++) {\n        const p = node.props[i];\n        if (p.type === 7 && (hasScopeRef(p.arg, ids) || hasScopeRef(p.exp, ids))) {\n          return true;\n        }\n      }\n      return node.children.some((c) => hasScopeRef(c, ids));\n    case 11:\n      if (hasScopeRef(node.source, ids)) {\n        return true;\n      }\n      return node.children.some((c) => hasScopeRef(c, ids));\n    case 9:\n      return node.branches.some((b) => hasScopeRef(b, ids));\n    case 10:\n      if (hasScopeRef(node.condition, ids)) {\n        return true;\n      }\n      return node.children.some((c) => hasScopeRef(c, ids));\n    case 4:\n      return !node.isStatic && isSimpleIdentifier(node.content) && !!ids[node.content];\n    case 8:\n      return node.children.some((c) => isObject(c) && hasScopeRef(c, ids));\n    case 5:\n    case 12:\n      return hasScopeRef(node.content, ids);\n    case 2:\n    case 3:\n    case 20:\n      return false;\n    default:\n      if (!!(process.env.NODE_ENV !== \"production\")) ;\n      return false;\n  }\n}\nfunction getMemoedVNodeCall(node) {\n  if (node.type === 14 && node.callee === WITH_MEMO) {\n    return node.arguments[1].returns;\n  } else {\n    return node;\n  }\n}\nconst forAliasRE = /([\\s\\S]*?)\\s+(?:in|of)\\s+(\\S[\\s\\S]*)/;\n\nconst defaultParserOptions = {\n  parseMode: \"base\",\n  ns: 0,\n  delimiters: [`{{`, `}}`],\n  getNamespace: () => 0,\n  isVoidTag: NO,\n  isPreTag: NO,\n  isIgnoreNewlineTag: NO,\n  isCustomElement: NO,\n  onError: defaultOnError,\n  onWarn: defaultOnWarn,\n  comments: !!(process.env.NODE_ENV !== \"production\"),\n  prefixIdentifiers: false\n};\nlet currentOptions = defaultParserOptions;\nlet currentRoot = null;\nlet currentInput = \"\";\nlet currentOpenTag = null;\nlet currentProp = null;\nlet currentAttrValue = \"\";\nlet currentAttrStartIndex = -1;\nlet currentAttrEndIndex = -1;\nlet inPre = 0;\nlet inVPre = false;\nlet currentVPreBoundary = null;\nconst stack = [];\nconst tokenizer = new Tokenizer(stack, {\n  onerr: emitError,\n  ontext(start, end) {\n    onText(getSlice(start, end), start, end);\n  },\n  ontextentity(char, start, end) {\n    onText(char, start, end);\n  },\n  oninterpolation(start, end) {\n    if (inVPre) {\n      return onText(getSlice(start, end), start, end);\n    }\n    let innerStart = start + tokenizer.delimiterOpen.length;\n    let innerEnd = end - tokenizer.delimiterClose.length;\n    while (isWhitespace(currentInput.charCodeAt(innerStart))) {\n      innerStart++;\n    }\n    while (isWhitespace(currentInput.charCodeAt(innerEnd - 1))) {\n      innerEnd--;\n    }\n    let exp = getSlice(innerStart, innerEnd);\n    if (exp.includes(\"&\")) {\n      {\n        exp = currentOptions.decodeEntities(exp, false);\n      }\n    }\n    addNode({\n      type: 5,\n      content: createExp(exp, false, getLoc(innerStart, innerEnd)),\n      loc: getLoc(start, end)\n    });\n  },\n  onopentagname(start, end) {\n    const name = getSlice(start, end);\n    currentOpenTag = {\n      type: 1,\n      tag: name,\n      ns: currentOptions.getNamespace(name, stack[0], currentOptions.ns),\n      tagType: 0,\n      // will be refined on tag close\n      props: [],\n      children: [],\n      loc: getLoc(start - 1, end),\n      codegenNode: void 0\n    };\n  },\n  onopentagend(end) {\n    endOpenTag(end);\n  },\n  onclosetag(start, end) {\n    const name = getSlice(start, end);\n    if (!currentOptions.isVoidTag(name)) {\n      let found = false;\n      for (let i = 0; i < stack.length; i++) {\n        const e = stack[i];\n        if (e.tag.toLowerCase() === name.toLowerCase()) {\n          found = true;\n          if (i > 0) {\n            emitError(24, stack[0].loc.start.offset);\n          }\n          for (let j = 0; j <= i; j++) {\n            const el = stack.shift();\n            onCloseTag(el, end, j < i);\n          }\n          break;\n        }\n      }\n      if (!found) {\n        emitError(23, backTrack(start, 60));\n      }\n    }\n  },\n  onselfclosingtag(end) {\n    const name = currentOpenTag.tag;\n    currentOpenTag.isSelfClosing = true;\n    endOpenTag(end);\n    if (stack[0] && stack[0].tag === name) {\n      onCloseTag(stack.shift(), end);\n    }\n  },\n  onattribname(start, end) {\n    currentProp = {\n      type: 6,\n      name: getSlice(start, end),\n      nameLoc: getLoc(start, end),\n      value: void 0,\n      loc: getLoc(start)\n    };\n  },\n  ondirname(start, end) {\n    const raw = getSlice(start, end);\n    const name = raw === \".\" || raw === \":\" ? \"bind\" : raw === \"@\" ? \"on\" : raw === \"#\" ? \"slot\" : raw.slice(2);\n    if (!inVPre && name === \"\") {\n      emitError(26, start);\n    }\n    if (inVPre || name === \"\") {\n      currentProp = {\n        type: 6,\n        name: raw,\n        nameLoc: getLoc(start, end),\n        value: void 0,\n        loc: getLoc(start)\n      };\n    } else {\n      currentProp = {\n        type: 7,\n        name,\n        rawName: raw,\n        exp: void 0,\n        arg: void 0,\n        modifiers: raw === \".\" ? [createSimpleExpression(\"prop\")] : [],\n        loc: getLoc(start)\n      };\n      if (name === \"pre\") {\n        inVPre = tokenizer.inVPre = true;\n        currentVPreBoundary = currentOpenTag;\n        const props = currentOpenTag.props;\n        for (let i = 0; i < props.length; i++) {\n          if (props[i].type === 7) {\n            props[i] = dirToAttr(props[i]);\n          }\n        }\n      }\n    }\n  },\n  ondirarg(start, end) {\n    if (start === end) return;\n    const arg = getSlice(start, end);\n    if (inVPre) {\n      currentProp.name += arg;\n      setLocEnd(currentProp.nameLoc, end);\n    } else {\n      const isStatic = arg[0] !== `[`;\n      currentProp.arg = createExp(\n        isStatic ? arg : arg.slice(1, -1),\n        isStatic,\n        getLoc(start, end),\n        isStatic ? 3 : 0\n      );\n    }\n  },\n  ondirmodifier(start, end) {\n    const mod = getSlice(start, end);\n    if (inVPre) {\n      currentProp.name += \".\" + mod;\n      setLocEnd(currentProp.nameLoc, end);\n    } else if (currentProp.name === \"slot\") {\n      const arg = currentProp.arg;\n      if (arg) {\n        arg.content += \".\" + mod;\n        setLocEnd(arg.loc, end);\n      }\n    } else {\n      const exp = createSimpleExpression(mod, true, getLoc(start, end));\n      currentProp.modifiers.push(exp);\n    }\n  },\n  onattribdata(start, end) {\n    currentAttrValue += getSlice(start, end);\n    if (currentAttrStartIndex < 0) currentAttrStartIndex = start;\n    currentAttrEndIndex = end;\n  },\n  onattribentity(char, start, end) {\n    currentAttrValue += char;\n    if (currentAttrStartIndex < 0) currentAttrStartIndex = start;\n    currentAttrEndIndex = end;\n  },\n  onattribnameend(end) {\n    const start = currentProp.loc.start.offset;\n    const name = getSlice(start, end);\n    if (currentProp.type === 7) {\n      currentProp.rawName = name;\n    }\n    if (currentOpenTag.props.some(\n      (p) => (p.type === 7 ? p.rawName : p.name) === name\n    )) {\n      emitError(2, start);\n    }\n  },\n  onattribend(quote, end) {\n    if (currentOpenTag && currentProp) {\n      setLocEnd(currentProp.loc, end);\n      if (quote !== 0) {\n        if (currentAttrValue.includes(\"&\")) {\n          currentAttrValue = currentOptions.decodeEntities(\n            currentAttrValue,\n            true\n          );\n        }\n        if (currentProp.type === 6) {\n          if (currentProp.name === \"class\") {\n            currentAttrValue = condense(currentAttrValue).trim();\n          }\n          if (quote === 1 && !currentAttrValue) {\n            emitError(13, end);\n          }\n          currentProp.value = {\n            type: 2,\n            content: currentAttrValue,\n            loc: quote === 1 ? getLoc(currentAttrStartIndex, currentAttrEndIndex) : getLoc(currentAttrStartIndex - 1, currentAttrEndIndex + 1)\n          };\n          if (tokenizer.inSFCRoot && currentOpenTag.tag === \"template\" && currentProp.name === \"lang\" && currentAttrValue && currentAttrValue !== \"html\") {\n            tokenizer.enterRCDATA(toCharCodes(`</template`), 0);\n          }\n        } else {\n          let expParseMode = 0 /* Normal */;\n          currentProp.exp = createExp(\n            currentAttrValue,\n            false,\n            getLoc(currentAttrStartIndex, currentAttrEndIndex),\n            0,\n            expParseMode\n          );\n          if (currentProp.name === \"for\") {\n            currentProp.forParseResult = parseForExpression(currentProp.exp);\n          }\n          let syncIndex = -1;\n          if (currentProp.name === \"bind\" && (syncIndex = currentProp.modifiers.findIndex(\n            (mod) => mod.content === \"sync\"\n          )) > -1 && checkCompatEnabled(\n            \"COMPILER_V_BIND_SYNC\",\n            currentOptions,\n            currentProp.loc,\n            currentProp.rawName\n          )) {\n            currentProp.name = \"model\";\n            currentProp.modifiers.splice(syncIndex, 1);\n          }\n        }\n      }\n      if (currentProp.type !== 7 || currentProp.name !== \"pre\") {\n        currentOpenTag.props.push(currentProp);\n      }\n    }\n    currentAttrValue = \"\";\n    currentAttrStartIndex = currentAttrEndIndex = -1;\n  },\n  oncomment(start, end) {\n    if (currentOptions.comments) {\n      addNode({\n        type: 3,\n        content: getSlice(start, end),\n        loc: getLoc(start - 4, end + 3)\n      });\n    }\n  },\n  onend() {\n    const end = currentInput.length;\n    if ((!!(process.env.NODE_ENV !== \"production\") || false) && tokenizer.state !== 1) {\n      switch (tokenizer.state) {\n        case 5:\n        case 8:\n          emitError(5, end);\n          break;\n        case 3:\n        case 4:\n          emitError(\n            25,\n            tokenizer.sectionStart\n          );\n          break;\n        case 28:\n          if (tokenizer.currentSequence === Sequences.CdataEnd) {\n            emitError(6, end);\n          } else {\n            emitError(7, end);\n          }\n          break;\n        case 6:\n        case 7:\n        case 9:\n        case 11:\n        case 12:\n        case 13:\n        case 14:\n        case 15:\n        case 16:\n        case 17:\n        case 18:\n        case 19:\n        // \"\n        case 20:\n        // '\n        case 21:\n          emitError(9, end);\n          break;\n      }\n    }\n    for (let index = 0; index < stack.length; index++) {\n      onCloseTag(stack[index], end - 1);\n      emitError(24, stack[index].loc.start.offset);\n    }\n  },\n  oncdata(start, end) {\n    if (stack[0].ns !== 0) {\n      onText(getSlice(start, end), start, end);\n    } else {\n      emitError(1, start - 9);\n    }\n  },\n  onprocessinginstruction(start) {\n    if ((stack[0] ? stack[0].ns : currentOptions.ns) === 0) {\n      emitError(\n        21,\n        start - 1\n      );\n    }\n  }\n});\nconst forIteratorRE = /,([^,\\}\\]]*)(?:,([^,\\}\\]]*))?$/;\nconst stripParensRE = /^\\(|\\)$/g;\nfunction parseForExpression(input) {\n  const loc = input.loc;\n  const exp = input.content;\n  const inMatch = exp.match(forAliasRE);\n  if (!inMatch) return;\n  const [, LHS, RHS] = inMatch;\n  const createAliasExpression = (content, offset, asParam = false) => {\n    const start = loc.start.offset + offset;\n    const end = start + content.length;\n    return createExp(\n      content,\n      false,\n      getLoc(start, end),\n      0,\n      asParam ? 1 /* Params */ : 0 /* Normal */\n    );\n  };\n  const result = {\n    source: createAliasExpression(RHS.trim(), exp.indexOf(RHS, LHS.length)),\n    value: void 0,\n    key: void 0,\n    index: void 0,\n    finalized: false\n  };\n  let valueContent = LHS.trim().replace(stripParensRE, \"\").trim();\n  const trimmedOffset = LHS.indexOf(valueContent);\n  const iteratorMatch = valueContent.match(forIteratorRE);\n  if (iteratorMatch) {\n    valueContent = valueContent.replace(forIteratorRE, \"\").trim();\n    const keyContent = iteratorMatch[1].trim();\n    let keyOffset;\n    if (keyContent) {\n      keyOffset = exp.indexOf(keyContent, trimmedOffset + valueContent.length);\n      result.key = createAliasExpression(keyContent, keyOffset, true);\n    }\n    if (iteratorMatch[2]) {\n      const indexContent = iteratorMatch[2].trim();\n      if (indexContent) {\n        result.index = createAliasExpression(\n          indexContent,\n          exp.indexOf(\n            indexContent,\n            result.key ? keyOffset + keyContent.length : trimmedOffset + valueContent.length\n          ),\n          true\n        );\n      }\n    }\n  }\n  if (valueContent) {\n    result.value = createAliasExpression(valueContent, trimmedOffset, true);\n  }\n  return result;\n}\nfunction getSlice(start, end) {\n  return currentInput.slice(start, end);\n}\nfunction endOpenTag(end) {\n  if (tokenizer.inSFCRoot) {\n    currentOpenTag.innerLoc = getLoc(end + 1, end + 1);\n  }\n  addNode(currentOpenTag);\n  const { tag, ns } = currentOpenTag;\n  if (ns === 0 && currentOptions.isPreTag(tag)) {\n    inPre++;\n  }\n  if (currentOptions.isVoidTag(tag)) {\n    onCloseTag(currentOpenTag, end);\n  } else {\n    stack.unshift(currentOpenTag);\n    if (ns === 1 || ns === 2) {\n      tokenizer.inXML = true;\n    }\n  }\n  currentOpenTag = null;\n}\nfunction onText(content, start, end) {\n  {\n    const tag = stack[0] && stack[0].tag;\n    if (tag !== \"script\" && tag !== \"style\" && content.includes(\"&\")) {\n      content = currentOptions.decodeEntities(content, false);\n    }\n  }\n  const parent = stack[0] || currentRoot;\n  const lastNode = parent.children[parent.children.length - 1];\n  if (lastNode && lastNode.type === 2) {\n    lastNode.content += content;\n    setLocEnd(lastNode.loc, end);\n  } else {\n    parent.children.push({\n      type: 2,\n      content,\n      loc: getLoc(start, end)\n    });\n  }\n}\nfunction onCloseTag(el, end, isImplied = false) {\n  if (isImplied) {\n    setLocEnd(el.loc, backTrack(end, 60));\n  } else {\n    setLocEnd(el.loc, lookAhead(end, 62) + 1);\n  }\n  if (tokenizer.inSFCRoot) {\n    if (el.children.length) {\n      el.innerLoc.end = extend({}, el.children[el.children.length - 1].loc.end);\n    } else {\n      el.innerLoc.end = extend({}, el.innerLoc.start);\n    }\n    el.innerLoc.source = getSlice(\n      el.innerLoc.start.offset,\n      el.innerLoc.end.offset\n    );\n  }\n  const { tag, ns, children } = el;\n  if (!inVPre) {\n    if (tag === \"slot\") {\n      el.tagType = 2;\n    } else if (isFragmentTemplate(el)) {\n      el.tagType = 3;\n    } else if (isComponent(el)) {\n      el.tagType = 1;\n    }\n  }\n  if (!tokenizer.inRCDATA) {\n    el.children = condenseWhitespace(children);\n  }\n  if (ns === 0 && currentOptions.isIgnoreNewlineTag(tag)) {\n    const first = children[0];\n    if (first && first.type === 2) {\n      first.content = first.content.replace(/^\\r?\\n/, \"\");\n    }\n  }\n  if (ns === 0 && currentOptions.isPreTag(tag)) {\n    inPre--;\n  }\n  if (currentVPreBoundary === el) {\n    inVPre = tokenizer.inVPre = false;\n    currentVPreBoundary = null;\n  }\n  if (tokenizer.inXML && (stack[0] ? stack[0].ns : currentOptions.ns) === 0) {\n    tokenizer.inXML = false;\n  }\n  {\n    const props = el.props;\n    if (!!(process.env.NODE_ENV !== \"production\") && isCompatEnabled(\n      \"COMPILER_V_IF_V_FOR_PRECEDENCE\",\n      currentOptions\n    )) {\n      let hasIf = false;\n      let hasFor = false;\n      for (let i = 0; i < props.length; i++) {\n        const p = props[i];\n        if (p.type === 7) {\n          if (p.name === \"if\") {\n            hasIf = true;\n          } else if (p.name === \"for\") {\n            hasFor = true;\n          }\n        }\n        if (hasIf && hasFor) {\n          warnDeprecation(\n            \"COMPILER_V_IF_V_FOR_PRECEDENCE\",\n            currentOptions,\n            el.loc\n          );\n          break;\n        }\n      }\n    }\n    if (!tokenizer.inSFCRoot && isCompatEnabled(\n      \"COMPILER_NATIVE_TEMPLATE\",\n      currentOptions\n    ) && el.tag === \"template\" && !isFragmentTemplate(el)) {\n      !!(process.env.NODE_ENV !== \"production\") && warnDeprecation(\n        \"COMPILER_NATIVE_TEMPLATE\",\n        currentOptions,\n        el.loc\n      );\n      const parent = stack[0] || currentRoot;\n      const index = parent.children.indexOf(el);\n      parent.children.splice(index, 1, ...el.children);\n    }\n    const inlineTemplateProp = props.find(\n      (p) => p.type === 6 && p.name === \"inline-template\"\n    );\n    if (inlineTemplateProp && checkCompatEnabled(\n      \"COMPILER_INLINE_TEMPLATE\",\n      currentOptions,\n      inlineTemplateProp.loc\n    ) && el.children.length) {\n      inlineTemplateProp.value = {\n        type: 2,\n        content: getSlice(\n          el.children[0].loc.start.offset,\n          el.children[el.children.length - 1].loc.end.offset\n        ),\n        loc: inlineTemplateProp.loc\n      };\n    }\n  }\n}\nfunction lookAhead(index, c) {\n  let i = index;\n  while (currentInput.charCodeAt(i) !== c && i < currentInput.length - 1) i++;\n  return i;\n}\nfunction backTrack(index, c) {\n  let i = index;\n  while (currentInput.charCodeAt(i) !== c && i >= 0) i--;\n  return i;\n}\nconst specialTemplateDir = /* @__PURE__ */ new Set([\"if\", \"else\", \"else-if\", \"for\", \"slot\"]);\nfunction isFragmentTemplate({ tag, props }) {\n  if (tag === \"template\") {\n    for (let i = 0; i < props.length; i++) {\n      if (props[i].type === 7 && specialTemplateDir.has(props[i].name)) {\n        return true;\n      }\n    }\n  }\n  return false;\n}\nfunction isComponent({ tag, props }) {\n  if (currentOptions.isCustomElement(tag)) {\n    return false;\n  }\n  if (tag === \"component\" || isUpperCase(tag.charCodeAt(0)) || isCoreComponent(tag) || currentOptions.isBuiltInComponent && currentOptions.isBuiltInComponent(tag) || currentOptions.isNativeTag && !currentOptions.isNativeTag(tag)) {\n    return true;\n  }\n  for (let i = 0; i < props.length; i++) {\n    const p = props[i];\n    if (p.type === 6) {\n      if (p.name === \"is\" && p.value) {\n        if (p.value.content.startsWith(\"vue:\")) {\n          return true;\n        } else if (checkCompatEnabled(\n          \"COMPILER_IS_ON_ELEMENT\",\n          currentOptions,\n          p.loc\n        )) {\n          return true;\n        }\n      }\n    } else if (// :is on plain element - only treat as component in compat mode\n    p.name === \"bind\" && isStaticArgOf(p.arg, \"is\") && checkCompatEnabled(\n      \"COMPILER_IS_ON_ELEMENT\",\n      currentOptions,\n      p.loc\n    )) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction isUpperCase(c) {\n  return c > 64 && c < 91;\n}\nconst windowsNewlineRE = /\\r\\n/g;\nfunction condenseWhitespace(nodes, tag) {\n  const shouldCondense = currentOptions.whitespace !== \"preserve\";\n  let removedWhitespace = false;\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n    if (node.type === 2) {\n      if (!inPre) {\n        if (isAllWhitespace(node.content)) {\n          const prev = nodes[i - 1] && nodes[i - 1].type;\n          const next = nodes[i + 1] && nodes[i + 1].type;\n          if (!prev || !next || shouldCondense && (prev === 3 && (next === 3 || next === 1) || prev === 1 && (next === 3 || next === 1 && hasNewlineChar(node.content)))) {\n            removedWhitespace = true;\n            nodes[i] = null;\n          } else {\n            node.content = \" \";\n          }\n        } else if (shouldCondense) {\n          node.content = condense(node.content);\n        }\n      } else {\n        node.content = node.content.replace(windowsNewlineRE, \"\\n\");\n      }\n    }\n  }\n  return removedWhitespace ? nodes.filter(Boolean) : nodes;\n}\nfunction isAllWhitespace(str) {\n  for (let i = 0; i < str.length; i++) {\n    if (!isWhitespace(str.charCodeAt(i))) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction hasNewlineChar(str) {\n  for (let i = 0; i < str.length; i++) {\n    const c = str.charCodeAt(i);\n    if (c === 10 || c === 13) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction condense(str) {\n  let ret = \"\";\n  let prevCharIsWhitespace = false;\n  for (let i = 0; i < str.length; i++) {\n    if (isWhitespace(str.charCodeAt(i))) {\n      if (!prevCharIsWhitespace) {\n        ret += \" \";\n        prevCharIsWhitespace = true;\n      }\n    } else {\n      ret += str[i];\n      prevCharIsWhitespace = false;\n    }\n  }\n  return ret;\n}\nfunction addNode(node) {\n  (stack[0] || currentRoot).children.push(node);\n}\nfunction getLoc(start, end) {\n  return {\n    start: tokenizer.getPos(start),\n    // @ts-expect-error allow late attachment\n    end: end == null ? end : tokenizer.getPos(end),\n    // @ts-expect-error allow late attachment\n    source: end == null ? end : getSlice(start, end)\n  };\n}\nfunction cloneLoc(loc) {\n  return getLoc(loc.start.offset, loc.end.offset);\n}\nfunction setLocEnd(loc, end) {\n  loc.end = tokenizer.getPos(end);\n  loc.source = getSlice(loc.start.offset, end);\n}\nfunction dirToAttr(dir) {\n  const attr = {\n    type: 6,\n    name: dir.rawName,\n    nameLoc: getLoc(\n      dir.loc.start.offset,\n      dir.loc.start.offset + dir.rawName.length\n    ),\n    value: void 0,\n    loc: dir.loc\n  };\n  if (dir.exp) {\n    const loc = dir.exp.loc;\n    if (loc.end.offset < dir.loc.end.offset) {\n      loc.start.offset--;\n      loc.start.column--;\n      loc.end.offset++;\n      loc.end.column++;\n    }\n    attr.value = {\n      type: 2,\n      content: dir.exp.content,\n      loc\n    };\n  }\n  return attr;\n}\nfunction createExp(content, isStatic = false, loc, constType = 0, parseMode = 0 /* Normal */) {\n  const exp = createSimpleExpression(content, isStatic, loc, constType);\n  return exp;\n}\nfunction emitError(code, index, message) {\n  currentOptions.onError(\n    createCompilerError(code, getLoc(index, index), void 0, message)\n  );\n}\nfunction reset() {\n  tokenizer.reset();\n  currentOpenTag = null;\n  currentProp = null;\n  currentAttrValue = \"\";\n  currentAttrStartIndex = -1;\n  currentAttrEndIndex = -1;\n  stack.length = 0;\n}\nfunction baseParse(input, options) {\n  reset();\n  currentInput = input;\n  currentOptions = extend({}, defaultParserOptions);\n  if (options) {\n    let key;\n    for (key in options) {\n      if (options[key] != null) {\n        currentOptions[key] = options[key];\n      }\n    }\n  }\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    if (!currentOptions.decodeEntities) {\n      throw new Error(\n        `[@vue/compiler-core] decodeEntities option is required in browser builds.`\n      );\n    }\n  }\n  tokenizer.mode = currentOptions.parseMode === \"html\" ? 1 : currentOptions.parseMode === \"sfc\" ? 2 : 0;\n  tokenizer.inXML = currentOptions.ns === 1 || currentOptions.ns === 2;\n  const delimiters = options && options.delimiters;\n  if (delimiters) {\n    tokenizer.delimiterOpen = toCharCodes(delimiters[0]);\n    tokenizer.delimiterClose = toCharCodes(delimiters[1]);\n  }\n  const root = currentRoot = createRoot([], input);\n  tokenizer.parse(currentInput);\n  root.loc = getLoc(0, input.length);\n  root.children = condenseWhitespace(root.children);\n  currentRoot = null;\n  return root;\n}\n\nfunction cacheStatic(root, context) {\n  walk(\n    root,\n    void 0,\n    context,\n    // Root node is unfortunately non-hoistable due to potential parent\n    // fallthrough attributes.\n    isSingleElementRoot(root, root.children[0])\n  );\n}\nfunction isSingleElementRoot(root, child) {\n  const { children } = root;\n  return children.length === 1 && child.type === 1 && !isSlotOutlet(child);\n}\nfunction walk(node, parent, context, doNotHoistNode = false, inFor = false) {\n  const { children } = node;\n  const toCache = [];\n  for (let i = 0; i < children.length; i++) {\n    const child = children[i];\n    if (child.type === 1 && child.tagType === 0) {\n      const constantType = doNotHoistNode ? 0 : getConstantType(child, context);\n      if (constantType > 0) {\n        if (constantType >= 2) {\n          child.codegenNode.patchFlag = -1;\n          toCache.push(child);\n          continue;\n        }\n      } else {\n        const codegenNode = child.codegenNode;\n        if (codegenNode.type === 13) {\n          const flag = codegenNode.patchFlag;\n          if ((flag === void 0 || flag === 512 || flag === 1) && getGeneratedPropsConstantType(child, context) >= 2) {\n            const props = getNodeProps(child);\n            if (props) {\n              codegenNode.props = context.hoist(props);\n            }\n          }\n          if (codegenNode.dynamicProps) {\n            codegenNode.dynamicProps = context.hoist(codegenNode.dynamicProps);\n          }\n        }\n      }\n    } else if (child.type === 12) {\n      const constantType = doNotHoistNode ? 0 : getConstantType(child, context);\n      if (constantType >= 2) {\n        toCache.push(child);\n        continue;\n      }\n    }\n    if (child.type === 1) {\n      const isComponent = child.tagType === 1;\n      if (isComponent) {\n        context.scopes.vSlot++;\n      }\n      walk(child, node, context, false, inFor);\n      if (isComponent) {\n        context.scopes.vSlot--;\n      }\n    } else if (child.type === 11) {\n      walk(child, node, context, child.children.length === 1, true);\n    } else if (child.type === 9) {\n      for (let i2 = 0; i2 < child.branches.length; i2++) {\n        walk(\n          child.branches[i2],\n          node,\n          context,\n          child.branches[i2].children.length === 1,\n          inFor\n        );\n      }\n    }\n  }\n  let cachedAsArray = false;\n  if (toCache.length === children.length && node.type === 1) {\n    if (node.tagType === 0 && node.codegenNode && node.codegenNode.type === 13 && isArray(node.codegenNode.children)) {\n      node.codegenNode.children = getCacheExpression(\n        createArrayExpression(node.codegenNode.children)\n      );\n      cachedAsArray = true;\n    } else if (node.tagType === 1 && node.codegenNode && node.codegenNode.type === 13 && node.codegenNode.children && !isArray(node.codegenNode.children) && node.codegenNode.children.type === 15) {\n      const slot = getSlotNode(node.codegenNode, \"default\");\n      if (slot) {\n        slot.returns = getCacheExpression(\n          createArrayExpression(slot.returns)\n        );\n        cachedAsArray = true;\n      }\n    } else if (node.tagType === 3 && parent && parent.type === 1 && parent.tagType === 1 && parent.codegenNode && parent.codegenNode.type === 13 && parent.codegenNode.children && !isArray(parent.codegenNode.children) && parent.codegenNode.children.type === 15) {\n      const slotName = findDir(node, \"slot\", true);\n      const slot = slotName && slotName.arg && getSlotNode(parent.codegenNode, slotName.arg);\n      if (slot) {\n        slot.returns = getCacheExpression(\n          createArrayExpression(slot.returns)\n        );\n        cachedAsArray = true;\n      }\n    }\n  }\n  if (!cachedAsArray) {\n    for (const child of toCache) {\n      child.codegenNode = context.cache(child.codegenNode);\n    }\n  }\n  function getCacheExpression(value) {\n    const exp = context.cache(value);\n    if (inFor && context.hmr) {\n      exp.needArraySpread = true;\n    }\n    return exp;\n  }\n  function getSlotNode(node2, name) {\n    if (node2.children && !isArray(node2.children) && node2.children.type === 15) {\n      const slot = node2.children.properties.find(\n        (p) => p.key === name || p.key.content === name\n      );\n      return slot && slot.value;\n    }\n  }\n  if (toCache.length && context.transformHoist) {\n    context.transformHoist(children, context, node);\n  }\n}\nfunction getConstantType(node, context) {\n  const { constantCache } = context;\n  switch (node.type) {\n    case 1:\n      if (node.tagType !== 0) {\n        return 0;\n      }\n      const cached = constantCache.get(node);\n      if (cached !== void 0) {\n        return cached;\n      }\n      const codegenNode = node.codegenNode;\n      if (codegenNode.type !== 13) {\n        return 0;\n      }\n      if (codegenNode.isBlock && node.tag !== \"svg\" && node.tag !== \"foreignObject\" && node.tag !== \"math\") {\n        return 0;\n      }\n      if (codegenNode.patchFlag === void 0) {\n        let returnType2 = 3;\n        const generatedPropsType = getGeneratedPropsConstantType(node, context);\n        if (generatedPropsType === 0) {\n          constantCache.set(node, 0);\n          return 0;\n        }\n        if (generatedPropsType < returnType2) {\n          returnType2 = generatedPropsType;\n        }\n        for (let i = 0; i < node.children.length; i++) {\n          const childType = getConstantType(node.children[i], context);\n          if (childType === 0) {\n            constantCache.set(node, 0);\n            return 0;\n          }\n          if (childType < returnType2) {\n            returnType2 = childType;\n          }\n        }\n        if (returnType2 > 1) {\n          for (let i = 0; i < node.props.length; i++) {\n            const p = node.props[i];\n            if (p.type === 7 && p.name === \"bind\" && p.exp) {\n              const expType = getConstantType(p.exp, context);\n              if (expType === 0) {\n                constantCache.set(node, 0);\n                return 0;\n              }\n              if (expType < returnType2) {\n                returnType2 = expType;\n              }\n            }\n          }\n        }\n        if (codegenNode.isBlock) {\n          for (let i = 0; i < node.props.length; i++) {\n            const p = node.props[i];\n            if (p.type === 7) {\n              constantCache.set(node, 0);\n              return 0;\n            }\n          }\n          context.removeHelper(OPEN_BLOCK);\n          context.removeHelper(\n            getVNodeBlockHelper(context.inSSR, codegenNode.isComponent)\n          );\n          codegenNode.isBlock = false;\n          context.helper(getVNodeHelper(context.inSSR, codegenNode.isComponent));\n        }\n        constantCache.set(node, returnType2);\n        return returnType2;\n      } else {\n        constantCache.set(node, 0);\n        return 0;\n      }\n    case 2:\n    case 3:\n      return 3;\n    case 9:\n    case 11:\n    case 10:\n      return 0;\n    case 5:\n    case 12:\n      return getConstantType(node.content, context);\n    case 4:\n      return node.constType;\n    case 8:\n      let returnType = 3;\n      for (let i = 0; i < node.children.length; i++) {\n        const child = node.children[i];\n        if (isString(child) || isSymbol(child)) {\n          continue;\n        }\n        const childType = getConstantType(child, context);\n        if (childType === 0) {\n          return 0;\n        } else if (childType < returnType) {\n          returnType = childType;\n        }\n      }\n      return returnType;\n    case 20:\n      return 2;\n    default:\n      if (!!(process.env.NODE_ENV !== \"production\")) ;\n      return 0;\n  }\n}\nconst allowHoistedHelperSet = /* @__PURE__ */ new Set([\n  NORMALIZE_CLASS,\n  NORMALIZE_STYLE,\n  NORMALIZE_PROPS,\n  GUARD_REACTIVE_PROPS\n]);\nfunction getConstantTypeOfHelperCall(value, context) {\n  if (value.type === 14 && !isString(value.callee) && allowHoistedHelperSet.has(value.callee)) {\n    const arg = value.arguments[0];\n    if (arg.type === 4) {\n      return getConstantType(arg, context);\n    } else if (arg.type === 14) {\n      return getConstantTypeOfHelperCall(arg, context);\n    }\n  }\n  return 0;\n}\nfunction getGeneratedPropsConstantType(node, context) {\n  let returnType = 3;\n  const props = getNodeProps(node);\n  if (props && props.type === 15) {\n    const { properties } = props;\n    for (let i = 0; i < properties.length; i++) {\n      const { key, value } = properties[i];\n      const keyType = getConstantType(key, context);\n      if (keyType === 0) {\n        return keyType;\n      }\n      if (keyType < returnType) {\n        returnType = keyType;\n      }\n      let valueType;\n      if (value.type === 4) {\n        valueType = getConstantType(value, context);\n      } else if (value.type === 14) {\n        valueType = getConstantTypeOfHelperCall(value, context);\n      } else {\n        valueType = 0;\n      }\n      if (valueType === 0) {\n        return valueType;\n      }\n      if (valueType < returnType) {\n        returnType = valueType;\n      }\n    }\n  }\n  return returnType;\n}\nfunction getNodeProps(node) {\n  const codegenNode = node.codegenNode;\n  if (codegenNode.type === 13) {\n    return codegenNode.props;\n  }\n}\n\nfunction createTransformContext(root, {\n  filename = \"\",\n  prefixIdentifiers = false,\n  hoistStatic = false,\n  hmr = false,\n  cacheHandlers = false,\n  nodeTransforms = [],\n  directiveTransforms = {},\n  transformHoist = null,\n  isBuiltInComponent = NOOP,\n  isCustomElement = NOOP,\n  expressionPlugins = [],\n  scopeId = null,\n  slotted = true,\n  ssr = false,\n  inSSR = false,\n  ssrCssVars = ``,\n  bindingMetadata = EMPTY_OBJ,\n  inline = false,\n  isTS = false,\n  onError = defaultOnError,\n  onWarn = defaultOnWarn,\n  compatConfig\n}) {\n  const nameMatch = filename.replace(/\\?.*$/, \"\").match(/([^/\\\\]+)\\.\\w+$/);\n  const context = {\n    // options\n    filename,\n    selfName: nameMatch && capitalize(camelize(nameMatch[1])),\n    prefixIdentifiers,\n    hoistStatic,\n    hmr,\n    cacheHandlers,\n    nodeTransforms,\n    directiveTransforms,\n    transformHoist,\n    isBuiltInComponent,\n    isCustomElement,\n    expressionPlugins,\n    scopeId,\n    slotted,\n    ssr,\n    inSSR,\n    ssrCssVars,\n    bindingMetadata,\n    inline,\n    isTS,\n    onError,\n    onWarn,\n    compatConfig,\n    // state\n    root,\n    helpers: /* @__PURE__ */ new Map(),\n    components: /* @__PURE__ */ new Set(),\n    directives: /* @__PURE__ */ new Set(),\n    hoists: [],\n    imports: [],\n    cached: [],\n    constantCache: /* @__PURE__ */ new WeakMap(),\n    temps: 0,\n    identifiers: /* @__PURE__ */ Object.create(null),\n    scopes: {\n      vFor: 0,\n      vSlot: 0,\n      vPre: 0,\n      vOnce: 0\n    },\n    parent: null,\n    grandParent: null,\n    currentNode: root,\n    childIndex: 0,\n    inVOnce: false,\n    // methods\n    helper(name) {\n      const count = context.helpers.get(name) || 0;\n      context.helpers.set(name, count + 1);\n      return name;\n    },\n    removeHelper(name) {\n      const count = context.helpers.get(name);\n      if (count) {\n        const currentCount = count - 1;\n        if (!currentCount) {\n          context.helpers.delete(name);\n        } else {\n          context.helpers.set(name, currentCount);\n        }\n      }\n    },\n    helperString(name) {\n      return `_${helperNameMap[context.helper(name)]}`;\n    },\n    replaceNode(node) {\n      if (!!(process.env.NODE_ENV !== \"production\")) {\n        if (!context.currentNode) {\n          throw new Error(`Node being replaced is already removed.`);\n        }\n        if (!context.parent) {\n          throw new Error(`Cannot replace root node.`);\n        }\n      }\n      context.parent.children[context.childIndex] = context.currentNode = node;\n    },\n    removeNode(node) {\n      if (!!(process.env.NODE_ENV !== \"production\") && !context.parent) {\n        throw new Error(`Cannot remove root node.`);\n      }\n      const list = context.parent.children;\n      const removalIndex = node ? list.indexOf(node) : context.currentNode ? context.childIndex : -1;\n      if (!!(process.env.NODE_ENV !== \"production\") && removalIndex < 0) {\n        throw new Error(`node being removed is not a child of current parent`);\n      }\n      if (!node || node === context.currentNode) {\n        context.currentNode = null;\n        context.onNodeRemoved();\n      } else {\n        if (context.childIndex > removalIndex) {\n          context.childIndex--;\n          context.onNodeRemoved();\n        }\n      }\n      context.parent.children.splice(removalIndex, 1);\n    },\n    onNodeRemoved: NOOP,\n    addIdentifiers(exp) {\n    },\n    removeIdentifiers(exp) {\n    },\n    hoist(exp) {\n      if (isString(exp)) exp = createSimpleExpression(exp);\n      context.hoists.push(exp);\n      const identifier = createSimpleExpression(\n        `_hoisted_${context.hoists.length}`,\n        false,\n        exp.loc,\n        2\n      );\n      identifier.hoisted = exp;\n      return identifier;\n    },\n    cache(exp, isVNode = false, inVOnce = false) {\n      const cacheExp = createCacheExpression(\n        context.cached.length,\n        exp,\n        isVNode,\n        inVOnce\n      );\n      context.cached.push(cacheExp);\n      return cacheExp;\n    }\n  };\n  {\n    context.filters = /* @__PURE__ */ new Set();\n  }\n  return context;\n}\nfunction transform(root, options) {\n  const context = createTransformContext(root, options);\n  traverseNode(root, context);\n  if (options.hoistStatic) {\n    cacheStatic(root, context);\n  }\n  if (!options.ssr) {\n    createRootCodegen(root, context);\n  }\n  root.helpers = /* @__PURE__ */ new Set([...context.helpers.keys()]);\n  root.components = [...context.components];\n  root.directives = [...context.directives];\n  root.imports = context.imports;\n  root.hoists = context.hoists;\n  root.temps = context.temps;\n  root.cached = context.cached;\n  root.transformed = true;\n  {\n    root.filters = [...context.filters];\n  }\n}\nfunction createRootCodegen(root, context) {\n  const { helper } = context;\n  const { children } = root;\n  if (children.length === 1) {\n    const child = children[0];\n    if (isSingleElementRoot(root, child) && child.codegenNode) {\n      const codegenNode = child.codegenNode;\n      if (codegenNode.type === 13) {\n        convertToBlock(codegenNode, context);\n      }\n      root.codegenNode = codegenNode;\n    } else {\n      root.codegenNode = child;\n    }\n  } else if (children.length > 1) {\n    let patchFlag = 64;\n    if (!!(process.env.NODE_ENV !== \"production\") && children.filter((c) => c.type !== 3).length === 1) {\n      patchFlag |= 2048;\n    }\n    root.codegenNode = createVNodeCall(\n      context,\n      helper(FRAGMENT),\n      void 0,\n      root.children,\n      patchFlag,\n      void 0,\n      void 0,\n      true,\n      void 0,\n      false\n    );\n  } else ;\n}\nfunction traverseChildren(parent, context) {\n  let i = 0;\n  const nodeRemoved = () => {\n    i--;\n  };\n  for (; i < parent.children.length; i++) {\n    const child = parent.children[i];\n    if (isString(child)) continue;\n    context.grandParent = context.parent;\n    context.parent = parent;\n    context.childIndex = i;\n    context.onNodeRemoved = nodeRemoved;\n    traverseNode(child, context);\n  }\n}\nfunction traverseNode(node, context) {\n  context.currentNode = node;\n  const { nodeTransforms } = context;\n  const exitFns = [];\n  for (let i2 = 0; i2 < nodeTransforms.length; i2++) {\n    const onExit = nodeTransforms[i2](node, context);\n    if (onExit) {\n      if (isArray(onExit)) {\n        exitFns.push(...onExit);\n      } else {\n        exitFns.push(onExit);\n      }\n    }\n    if (!context.currentNode) {\n      return;\n    } else {\n      node = context.currentNode;\n    }\n  }\n  switch (node.type) {\n    case 3:\n      if (!context.ssr) {\n        context.helper(CREATE_COMMENT);\n      }\n      break;\n    case 5:\n      if (!context.ssr) {\n        context.helper(TO_DISPLAY_STRING);\n      }\n      break;\n    // for container types, further traverse downwards\n    case 9:\n      for (let i2 = 0; i2 < node.branches.length; i2++) {\n        traverseNode(node.branches[i2], context);\n      }\n      break;\n    case 10:\n    case 11:\n    case 1:\n    case 0:\n      traverseChildren(node, context);\n      break;\n  }\n  context.currentNode = node;\n  let i = exitFns.length;\n  while (i--) {\n    exitFns[i]();\n  }\n}\nfunction createStructuralDirectiveTransform(name, fn) {\n  const matches = isString(name) ? (n) => n === name : (n) => name.test(n);\n  return (node, context) => {\n    if (node.type === 1) {\n      const { props } = node;\n      if (node.tagType === 3 && props.some(isVSlot)) {\n        return;\n      }\n      const exitFns = [];\n      for (let i = 0; i < props.length; i++) {\n        const prop = props[i];\n        if (prop.type === 7 && matches(prop.name)) {\n          props.splice(i, 1);\n          i--;\n          const onExit = fn(node, prop, context);\n          if (onExit) exitFns.push(onExit);\n        }\n      }\n      return exitFns;\n    }\n  };\n}\n\nconst PURE_ANNOTATION = `/*@__PURE__*/`;\nconst aliasHelper = (s) => `${helperNameMap[s]}: _${helperNameMap[s]}`;\nfunction createCodegenContext(ast, {\n  mode = \"function\",\n  prefixIdentifiers = mode === \"module\",\n  sourceMap = false,\n  filename = `template.vue.html`,\n  scopeId = null,\n  optimizeImports = false,\n  runtimeGlobalName = `Vue`,\n  runtimeModuleName = `vue`,\n  ssrRuntimeModuleName = \"vue/server-renderer\",\n  ssr = false,\n  isTS = false,\n  inSSR = false\n}) {\n  const context = {\n    mode,\n    prefixIdentifiers,\n    sourceMap,\n    filename,\n    scopeId,\n    optimizeImports,\n    runtimeGlobalName,\n    runtimeModuleName,\n    ssrRuntimeModuleName,\n    ssr,\n    isTS,\n    inSSR,\n    source: ast.source,\n    code: ``,\n    column: 1,\n    line: 1,\n    offset: 0,\n    indentLevel: 0,\n    pure: false,\n    map: void 0,\n    helper(key) {\n      return `_${helperNameMap[key]}`;\n    },\n    push(code, newlineIndex = -2 /* None */, node) {\n      context.code += code;\n    },\n    indent() {\n      newline(++context.indentLevel);\n    },\n    deindent(withoutNewLine = false) {\n      if (withoutNewLine) {\n        --context.indentLevel;\n      } else {\n        newline(--context.indentLevel);\n      }\n    },\n    newline() {\n      newline(context.indentLevel);\n    }\n  };\n  function newline(n) {\n    context.push(\"\\n\" + `  `.repeat(n), 0 /* Start */);\n  }\n  return context;\n}\nfunction generate(ast, options = {}) {\n  const context = createCodegenContext(ast, options);\n  if (options.onContextCreated) options.onContextCreated(context);\n  const {\n    mode,\n    push,\n    prefixIdentifiers,\n    indent,\n    deindent,\n    newline,\n    scopeId,\n    ssr\n  } = context;\n  const helpers = Array.from(ast.helpers);\n  const hasHelpers = helpers.length > 0;\n  const useWithBlock = !prefixIdentifiers && mode !== \"module\";\n  const preambleContext = context;\n  {\n    genFunctionPreamble(ast, preambleContext);\n  }\n  const functionName = ssr ? `ssrRender` : `render`;\n  const args = ssr ? [\"_ctx\", \"_push\", \"_parent\", \"_attrs\"] : [\"_ctx\", \"_cache\"];\n  const signature = args.join(\", \");\n  {\n    push(`function ${functionName}(${signature}) {`);\n  }\n  indent();\n  if (useWithBlock) {\n    push(`with (_ctx) {`);\n    indent();\n    if (hasHelpers) {\n      push(\n        `const { ${helpers.map(aliasHelper).join(\", \")} } = _Vue\n`,\n        -1 /* End */\n      );\n      newline();\n    }\n  }\n  if (ast.components.length) {\n    genAssets(ast.components, \"component\", context);\n    if (ast.directives.length || ast.temps > 0) {\n      newline();\n    }\n  }\n  if (ast.directives.length) {\n    genAssets(ast.directives, \"directive\", context);\n    if (ast.temps > 0) {\n      newline();\n    }\n  }\n  if (ast.filters && ast.filters.length) {\n    newline();\n    genAssets(ast.filters, \"filter\", context);\n    newline();\n  }\n  if (ast.temps > 0) {\n    push(`let `);\n    for (let i = 0; i < ast.temps; i++) {\n      push(`${i > 0 ? `, ` : ``}_temp${i}`);\n    }\n  }\n  if (ast.components.length || ast.directives.length || ast.temps) {\n    push(`\n`, 0 /* Start */);\n    newline();\n  }\n  if (!ssr) {\n    push(`return `);\n  }\n  if (ast.codegenNode) {\n    genNode(ast.codegenNode, context);\n  } else {\n    push(`null`);\n  }\n  if (useWithBlock) {\n    deindent();\n    push(`}`);\n  }\n  deindent();\n  push(`}`);\n  return {\n    ast,\n    code: context.code,\n    preamble: ``,\n    map: context.map ? context.map.toJSON() : void 0\n  };\n}\nfunction genFunctionPreamble(ast, context) {\n  const {\n    ssr,\n    prefixIdentifiers,\n    push,\n    newline,\n    runtimeModuleName,\n    runtimeGlobalName,\n    ssrRuntimeModuleName\n  } = context;\n  const VueBinding = runtimeGlobalName;\n  const helpers = Array.from(ast.helpers);\n  if (helpers.length > 0) {\n    {\n      push(`const _Vue = ${VueBinding}\n`, -1 /* End */);\n      if (ast.hoists.length) {\n        const staticHelpers = [\n          CREATE_VNODE,\n          CREATE_ELEMENT_VNODE,\n          CREATE_COMMENT,\n          CREATE_TEXT,\n          CREATE_STATIC\n        ].filter((helper) => helpers.includes(helper)).map(aliasHelper).join(\", \");\n        push(`const { ${staticHelpers} } = _Vue\n`, -1 /* End */);\n      }\n    }\n  }\n  genHoists(ast.hoists, context);\n  newline();\n  push(`return `);\n}\nfunction genAssets(assets, type, { helper, push, newline, isTS }) {\n  const resolver = helper(\n    type === \"filter\" ? RESOLVE_FILTER : type === \"component\" ? RESOLVE_COMPONENT : RESOLVE_DIRECTIVE\n  );\n  for (let i = 0; i < assets.length; i++) {\n    let id = assets[i];\n    const maybeSelfReference = id.endsWith(\"__self\");\n    if (maybeSelfReference) {\n      id = id.slice(0, -6);\n    }\n    push(\n      `const ${toValidAssetId(id, type)} = ${resolver}(${JSON.stringify(id)}${maybeSelfReference ? `, true` : ``})${isTS ? `!` : ``}`\n    );\n    if (i < assets.length - 1) {\n      newline();\n    }\n  }\n}\nfunction genHoists(hoists, context) {\n  if (!hoists.length) {\n    return;\n  }\n  context.pure = true;\n  const { push, newline } = context;\n  newline();\n  for (let i = 0; i < hoists.length; i++) {\n    const exp = hoists[i];\n    if (exp) {\n      push(`const _hoisted_${i + 1} = `);\n      genNode(exp, context);\n      newline();\n    }\n  }\n  context.pure = false;\n}\nfunction isText(n) {\n  return isString(n) || n.type === 4 || n.type === 2 || n.type === 5 || n.type === 8;\n}\nfunction genNodeListAsArray(nodes, context) {\n  const multilines = nodes.length > 3 || !!(process.env.NODE_ENV !== \"production\") && nodes.some((n) => isArray(n) || !isText(n));\n  context.push(`[`);\n  multilines && context.indent();\n  genNodeList(nodes, context, multilines);\n  multilines && context.deindent();\n  context.push(`]`);\n}\nfunction genNodeList(nodes, context, multilines = false, comma = true) {\n  const { push, newline } = context;\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n    if (isString(node)) {\n      push(node, -3 /* Unknown */);\n    } else if (isArray(node)) {\n      genNodeListAsArray(node, context);\n    } else {\n      genNode(node, context);\n    }\n    if (i < nodes.length - 1) {\n      if (multilines) {\n        comma && push(\",\");\n        newline();\n      } else {\n        comma && push(\", \");\n      }\n    }\n  }\n}\nfunction genNode(node, context) {\n  if (isString(node)) {\n    context.push(node, -3 /* Unknown */);\n    return;\n  }\n  if (isSymbol(node)) {\n    context.push(context.helper(node));\n    return;\n  }\n  switch (node.type) {\n    case 1:\n    case 9:\n    case 11:\n      !!(process.env.NODE_ENV !== \"production\") && assert(\n        node.codegenNode != null,\n        `Codegen node is missing for element/if/for node. Apply appropriate transforms first.`\n      );\n      genNode(node.codegenNode, context);\n      break;\n    case 2:\n      genText(node, context);\n      break;\n    case 4:\n      genExpression(node, context);\n      break;\n    case 5:\n      genInterpolation(node, context);\n      break;\n    case 12:\n      genNode(node.codegenNode, context);\n      break;\n    case 8:\n      genCompoundExpression(node, context);\n      break;\n    case 3:\n      genComment(node, context);\n      break;\n    case 13:\n      genVNodeCall(node, context);\n      break;\n    case 14:\n      genCallExpression(node, context);\n      break;\n    case 15:\n      genObjectExpression(node, context);\n      break;\n    case 17:\n      genArrayExpression(node, context);\n      break;\n    case 18:\n      genFunctionExpression(node, context);\n      break;\n    case 19:\n      genConditionalExpression(node, context);\n      break;\n    case 20:\n      genCacheExpression(node, context);\n      break;\n    case 21:\n      genNodeList(node.body, context, true, false);\n      break;\n    // SSR only types\n    case 22:\n      break;\n    case 23:\n      break;\n    case 24:\n      break;\n    case 25:\n      break;\n    case 26:\n      break;\n    /* v8 ignore start */\n    case 10:\n      break;\n    default:\n      if (!!(process.env.NODE_ENV !== \"production\")) {\n        assert(false, `unhandled codegen node type: ${node.type}`);\n        const exhaustiveCheck = node;\n        return exhaustiveCheck;\n      }\n  }\n}\nfunction genText(node, context) {\n  context.push(JSON.stringify(node.content), -3 /* Unknown */, node);\n}\nfunction genExpression(node, context) {\n  const { content, isStatic } = node;\n  context.push(\n    isStatic ? JSON.stringify(content) : content,\n    -3 /* Unknown */,\n    node\n  );\n}\nfunction genInterpolation(node, context) {\n  const { push, helper, pure } = context;\n  if (pure) push(PURE_ANNOTATION);\n  push(`${helper(TO_DISPLAY_STRING)}(`);\n  genNode(node.content, context);\n  push(`)`);\n}\nfunction genCompoundExpression(node, context) {\n  for (let i = 0; i < node.children.length; i++) {\n    const child = node.children[i];\n    if (isString(child)) {\n      context.push(child, -3 /* Unknown */);\n    } else {\n      genNode(child, context);\n    }\n  }\n}\nfunction genExpressionAsPropertyKey(node, context) {\n  const { push } = context;\n  if (node.type === 8) {\n    push(`[`);\n    genCompoundExpression(node, context);\n    push(`]`);\n  } else if (node.isStatic) {\n    const text = isSimpleIdentifier(node.content) ? node.content : JSON.stringify(node.content);\n    push(text, -2 /* None */, node);\n  } else {\n    push(`[${node.content}]`, -3 /* Unknown */, node);\n  }\n}\nfunction genComment(node, context) {\n  const { push, helper, pure } = context;\n  if (pure) {\n    push(PURE_ANNOTATION);\n  }\n  push(\n    `${helper(CREATE_COMMENT)}(${JSON.stringify(node.content)})`,\n    -3 /* Unknown */,\n    node\n  );\n}\nfunction genVNodeCall(node, context) {\n  const { push, helper, pure } = context;\n  const {\n    tag,\n    props,\n    children,\n    patchFlag,\n    dynamicProps,\n    directives,\n    isBlock,\n    disableTracking,\n    isComponent\n  } = node;\n  let patchFlagString;\n  if (patchFlag) {\n    if (!!(process.env.NODE_ENV !== \"production\")) {\n      if (patchFlag < 0) {\n        patchFlagString = patchFlag + ` /* ${PatchFlagNames[patchFlag]} */`;\n      } else {\n        const flagNames = Object.keys(PatchFlagNames).map(Number).filter((n) => n > 0 && patchFlag & n).map((n) => PatchFlagNames[n]).join(`, `);\n        patchFlagString = patchFlag + ` /* ${flagNames} */`;\n      }\n    } else {\n      patchFlagString = String(patchFlag);\n    }\n  }\n  if (directives) {\n    push(helper(WITH_DIRECTIVES) + `(`);\n  }\n  if (isBlock) {\n    push(`(${helper(OPEN_BLOCK)}(${disableTracking ? `true` : ``}), `);\n  }\n  if (pure) {\n    push(PURE_ANNOTATION);\n  }\n  const callHelper = isBlock ? getVNodeBlockHelper(context.inSSR, isComponent) : getVNodeHelper(context.inSSR, isComponent);\n  push(helper(callHelper) + `(`, -2 /* None */, node);\n  genNodeList(\n    genNullableArgs([tag, props, children, patchFlagString, dynamicProps]),\n    context\n  );\n  push(`)`);\n  if (isBlock) {\n    push(`)`);\n  }\n  if (directives) {\n    push(`, `);\n    genNode(directives, context);\n    push(`)`);\n  }\n}\nfunction genNullableArgs(args) {\n  let i = args.length;\n  while (i--) {\n    if (args[i] != null) break;\n  }\n  return args.slice(0, i + 1).map((arg) => arg || `null`);\n}\nfunction genCallExpression(node, context) {\n  const { push, helper, pure } = context;\n  const callee = isString(node.callee) ? node.callee : helper(node.callee);\n  if (pure) {\n    push(PURE_ANNOTATION);\n  }\n  push(callee + `(`, -2 /* None */, node);\n  genNodeList(node.arguments, context);\n  push(`)`);\n}\nfunction genObjectExpression(node, context) {\n  const { push, indent, deindent, newline } = context;\n  const { properties } = node;\n  if (!properties.length) {\n    push(`{}`, -2 /* None */, node);\n    return;\n  }\n  const multilines = properties.length > 1 || !!(process.env.NODE_ENV !== \"production\") && properties.some((p) => p.value.type !== 4);\n  push(multilines ? `{` : `{ `);\n  multilines && indent();\n  for (let i = 0; i < properties.length; i++) {\n    const { key, value } = properties[i];\n    genExpressionAsPropertyKey(key, context);\n    push(`: `);\n    genNode(value, context);\n    if (i < properties.length - 1) {\n      push(`,`);\n      newline();\n    }\n  }\n  multilines && deindent();\n  push(multilines ? `}` : ` }`);\n}\nfunction genArrayExpression(node, context) {\n  genNodeListAsArray(node.elements, context);\n}\nfunction genFunctionExpression(node, context) {\n  const { push, indent, deindent } = context;\n  const { params, returns, body, newline, isSlot } = node;\n  if (isSlot) {\n    push(`_${helperNameMap[WITH_CTX]}(`);\n  }\n  push(`(`, -2 /* None */, node);\n  if (isArray(params)) {\n    genNodeList(params, context);\n  } else if (params) {\n    genNode(params, context);\n  }\n  push(`) => `);\n  if (newline || body) {\n    push(`{`);\n    indent();\n  }\n  if (returns) {\n    if (newline) {\n      push(`return `);\n    }\n    if (isArray(returns)) {\n      genNodeListAsArray(returns, context);\n    } else {\n      genNode(returns, context);\n    }\n  } else if (body) {\n    genNode(body, context);\n  }\n  if (newline || body) {\n    deindent();\n    push(`}`);\n  }\n  if (isSlot) {\n    if (node.isNonScopedSlot) {\n      push(`, undefined, true`);\n    }\n    push(`)`);\n  }\n}\nfunction genConditionalExpression(node, context) {\n  const { test, consequent, alternate, newline: needNewline } = node;\n  const { push, indent, deindent, newline } = context;\n  if (test.type === 4) {\n    const needsParens = !isSimpleIdentifier(test.content);\n    needsParens && push(`(`);\n    genExpression(test, context);\n    needsParens && push(`)`);\n  } else {\n    push(`(`);\n    genNode(test, context);\n    push(`)`);\n  }\n  needNewline && indent();\n  context.indentLevel++;\n  needNewline || push(` `);\n  push(`? `);\n  genNode(consequent, context);\n  context.indentLevel--;\n  needNewline && newline();\n  needNewline || push(` `);\n  push(`: `);\n  const isNested = alternate.type === 19;\n  if (!isNested) {\n    context.indentLevel++;\n  }\n  genNode(alternate, context);\n  if (!isNested) {\n    context.indentLevel--;\n  }\n  needNewline && deindent(\n    true\n    /* without newline */\n  );\n}\nfunction genCacheExpression(node, context) {\n  const { push, helper, indent, deindent, newline } = context;\n  const { needPauseTracking, needArraySpread } = node;\n  if (needArraySpread) {\n    push(`[...(`);\n  }\n  push(`_cache[${node.index}] || (`);\n  if (needPauseTracking) {\n    indent();\n    push(`${helper(SET_BLOCK_TRACKING)}(-1`);\n    if (node.inVOnce) push(`, true`);\n    push(`),`);\n    newline();\n    push(`(`);\n  }\n  push(`_cache[${node.index}] = `);\n  genNode(node.value, context);\n  if (needPauseTracking) {\n    push(`).cacheIndex = ${node.index},`);\n    newline();\n    push(`${helper(SET_BLOCK_TRACKING)}(1),`);\n    newline();\n    push(`_cache[${node.index}]`);\n    deindent();\n  }\n  push(`)`);\n  if (needArraySpread) {\n    push(`)]`);\n  }\n}\n\nconst prohibitedKeywordRE = new RegExp(\n  \"\\\\b\" + \"arguments,await,break,case,catch,class,const,continue,debugger,default,delete,do,else,export,extends,finally,for,function,if,import,let,new,return,super,switch,throw,try,var,void,while,with,yield\".split(\",\").join(\"\\\\b|\\\\b\") + \"\\\\b\"\n);\nconst stripStringRE = /'(?:[^'\\\\]|\\\\.)*'|\"(?:[^\"\\\\]|\\\\.)*\"|`(?:[^`\\\\]|\\\\.)*\\$\\{|\\}(?:[^`\\\\]|\\\\.)*`|`(?:[^`\\\\]|\\\\.)*`/g;\nfunction validateBrowserExpression(node, context, asParams = false, asRawStatements = false) {\n  const exp = node.content;\n  if (!exp.trim()) {\n    return;\n  }\n  try {\n    new Function(\n      asRawStatements ? ` ${exp} ` : `return ${asParams ? `(${exp}) => {}` : `(${exp})`}`\n    );\n  } catch (e) {\n    let message = e.message;\n    const keywordMatch = exp.replace(stripStringRE, \"\").match(prohibitedKeywordRE);\n    if (keywordMatch) {\n      message = `avoid using JavaScript keyword as property name: \"${keywordMatch[0]}\"`;\n    }\n    context.onError(\n      createCompilerError(\n        45,\n        node.loc,\n        void 0,\n        message\n      )\n    );\n  }\n}\n\nconst transformExpression = (node, context) => {\n  if (node.type === 5) {\n    node.content = processExpression(\n      node.content,\n      context\n    );\n  } else if (node.type === 1) {\n    const memo = findDir(node, \"memo\");\n    for (let i = 0; i < node.props.length; i++) {\n      const dir = node.props[i];\n      if (dir.type === 7 && dir.name !== \"for\") {\n        const exp = dir.exp;\n        const arg = dir.arg;\n        if (exp && exp.type === 4 && !(dir.name === \"on\" && arg) && // key has been processed in transformFor(vMemo + vFor)\n        !(memo && arg && arg.type === 4 && arg.content === \"key\")) {\n          dir.exp = processExpression(\n            exp,\n            context,\n            // slot args must be processed as function params\n            dir.name === \"slot\"\n          );\n        }\n        if (arg && arg.type === 4 && !arg.isStatic) {\n          dir.arg = processExpression(arg, context);\n        }\n      }\n    }\n  }\n};\nfunction processExpression(node, context, asParams = false, asRawStatements = false, localVars = Object.create(context.identifiers)) {\n  {\n    if (!!(process.env.NODE_ENV !== \"production\")) {\n      validateBrowserExpression(node, context, asParams, asRawStatements);\n    }\n    return node;\n  }\n}\nfunction stringifyExpression(exp) {\n  if (isString(exp)) {\n    return exp;\n  } else if (exp.type === 4) {\n    return exp.content;\n  } else {\n    return exp.children.map(stringifyExpression).join(\"\");\n  }\n}\n\nconst transformIf = createStructuralDirectiveTransform(\n  /^(if|else|else-if)$/,\n  (node, dir, context) => {\n    return processIf(node, dir, context, (ifNode, branch, isRoot) => {\n      const siblings = context.parent.children;\n      let i = siblings.indexOf(ifNode);\n      let key = 0;\n      while (i-- >= 0) {\n        const sibling = siblings[i];\n        if (sibling && sibling.type === 9) {\n          key += sibling.branches.length;\n        }\n      }\n      return () => {\n        if (isRoot) {\n          ifNode.codegenNode = createCodegenNodeForBranch(\n            branch,\n            key,\n            context\n          );\n        } else {\n          const parentCondition = getParentCondition(ifNode.codegenNode);\n          parentCondition.alternate = createCodegenNodeForBranch(\n            branch,\n            key + ifNode.branches.length - 1,\n            context\n          );\n        }\n      };\n    });\n  }\n);\nfunction processIf(node, dir, context, processCodegen) {\n  if (dir.name !== \"else\" && (!dir.exp || !dir.exp.content.trim())) {\n    const loc = dir.exp ? dir.exp.loc : node.loc;\n    context.onError(\n      createCompilerError(28, dir.loc)\n    );\n    dir.exp = createSimpleExpression(`true`, false, loc);\n  }\n  if (!!(process.env.NODE_ENV !== \"production\") && true && dir.exp) {\n    validateBrowserExpression(dir.exp, context);\n  }\n  if (dir.name === \"if\") {\n    const branch = createIfBranch(node, dir);\n    const ifNode = {\n      type: 9,\n      loc: cloneLoc(node.loc),\n      branches: [branch]\n    };\n    context.replaceNode(ifNode);\n    if (processCodegen) {\n      return processCodegen(ifNode, branch, true);\n    }\n  } else {\n    const siblings = context.parent.children;\n    const comments = [];\n    let i = siblings.indexOf(node);\n    while (i-- >= -1) {\n      const sibling = siblings[i];\n      if (sibling && sibling.type === 3) {\n        context.removeNode(sibling);\n        !!(process.env.NODE_ENV !== \"production\") && comments.unshift(sibling);\n        continue;\n      }\n      if (sibling && sibling.type === 2 && !sibling.content.trim().length) {\n        context.removeNode(sibling);\n        continue;\n      }\n      if (sibling && sibling.type === 9) {\n        if (dir.name === \"else-if\" && sibling.branches[sibling.branches.length - 1].condition === void 0) {\n          context.onError(\n            createCompilerError(30, node.loc)\n          );\n        }\n        context.removeNode();\n        const branch = createIfBranch(node, dir);\n        if (!!(process.env.NODE_ENV !== \"production\") && comments.length && // #3619 ignore comments if the v-if is direct child of <transition>\n        !(context.parent && context.parent.type === 1 && (context.parent.tag === \"transition\" || context.parent.tag === \"Transition\"))) {\n          branch.children = [...comments, ...branch.children];\n        }\n        if (!!(process.env.NODE_ENV !== \"production\") || false) {\n          const key = branch.userKey;\n          if (key) {\n            sibling.branches.forEach(({ userKey }) => {\n              if (isSameKey(userKey, key)) {\n                context.onError(\n                  createCompilerError(\n                    29,\n                    branch.userKey.loc\n                  )\n                );\n              }\n            });\n          }\n        }\n        sibling.branches.push(branch);\n        const onExit = processCodegen && processCodegen(sibling, branch, false);\n        traverseNode(branch, context);\n        if (onExit) onExit();\n        context.currentNode = null;\n      } else {\n        context.onError(\n          createCompilerError(30, node.loc)\n        );\n      }\n      break;\n    }\n  }\n}\nfunction createIfBranch(node, dir) {\n  const isTemplateIf = node.tagType === 3;\n  return {\n    type: 10,\n    loc: node.loc,\n    condition: dir.name === \"else\" ? void 0 : dir.exp,\n    children: isTemplateIf && !findDir(node, \"for\") ? node.children : [node],\n    userKey: findProp(node, `key`),\n    isTemplateIf\n  };\n}\nfunction createCodegenNodeForBranch(branch, keyIndex, context) {\n  if (branch.condition) {\n    return createConditionalExpression(\n      branch.condition,\n      createChildrenCodegenNode(branch, keyIndex, context),\n      // make sure to pass in asBlock: true so that the comment node call\n      // closes the current block.\n      createCallExpression(context.helper(CREATE_COMMENT), [\n        !!(process.env.NODE_ENV !== \"production\") ? '\"v-if\"' : '\"\"',\n        \"true\"\n      ])\n    );\n  } else {\n    return createChildrenCodegenNode(branch, keyIndex, context);\n  }\n}\nfunction createChildrenCodegenNode(branch, keyIndex, context) {\n  const { helper } = context;\n  const keyProperty = createObjectProperty(\n    `key`,\n    createSimpleExpression(\n      `${keyIndex}`,\n      false,\n      locStub,\n      2\n    )\n  );\n  const { children } = branch;\n  const firstChild = children[0];\n  const needFragmentWrapper = children.length !== 1 || firstChild.type !== 1;\n  if (needFragmentWrapper) {\n    if (children.length === 1 && firstChild.type === 11) {\n      const vnodeCall = firstChild.codegenNode;\n      injectProp(vnodeCall, keyProperty, context);\n      return vnodeCall;\n    } else {\n      let patchFlag = 64;\n      if (!!(process.env.NODE_ENV !== \"production\") && !branch.isTemplateIf && children.filter((c) => c.type !== 3).length === 1) {\n        patchFlag |= 2048;\n      }\n      return createVNodeCall(\n        context,\n        helper(FRAGMENT),\n        createObjectExpression([keyProperty]),\n        children,\n        patchFlag,\n        void 0,\n        void 0,\n        true,\n        false,\n        false,\n        branch.loc\n      );\n    }\n  } else {\n    const ret = firstChild.codegenNode;\n    const vnodeCall = getMemoedVNodeCall(ret);\n    if (vnodeCall.type === 13) {\n      convertToBlock(vnodeCall, context);\n    }\n    injectProp(vnodeCall, keyProperty, context);\n    return ret;\n  }\n}\nfunction isSameKey(a, b) {\n  if (!a || a.type !== b.type) {\n    return false;\n  }\n  if (a.type === 6) {\n    if (a.value.content !== b.value.content) {\n      return false;\n    }\n  } else {\n    const exp = a.exp;\n    const branchExp = b.exp;\n    if (exp.type !== branchExp.type) {\n      return false;\n    }\n    if (exp.type !== 4 || exp.isStatic !== branchExp.isStatic || exp.content !== branchExp.content) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction getParentCondition(node) {\n  while (true) {\n    if (node.type === 19) {\n      if (node.alternate.type === 19) {\n        node = node.alternate;\n      } else {\n        return node;\n      }\n    } else if (node.type === 20) {\n      node = node.value;\n    }\n  }\n}\n\nconst transformBind = (dir, _node, context) => {\n  const { modifiers, loc } = dir;\n  const arg = dir.arg;\n  let { exp } = dir;\n  if (exp && exp.type === 4 && !exp.content.trim()) {\n    {\n      exp = void 0;\n    }\n  }\n  if (!exp) {\n    if (arg.type !== 4 || !arg.isStatic) {\n      context.onError(\n        createCompilerError(\n          52,\n          arg.loc\n        )\n      );\n      return {\n        props: [\n          createObjectProperty(arg, createSimpleExpression(\"\", true, loc))\n        ]\n      };\n    }\n    transformBindShorthand(dir);\n    exp = dir.exp;\n  }\n  if (arg.type !== 4) {\n    arg.children.unshift(`(`);\n    arg.children.push(`) || \"\"`);\n  } else if (!arg.isStatic) {\n    arg.content = `${arg.content} || \"\"`;\n  }\n  if (modifiers.some((mod) => mod.content === \"camel\")) {\n    if (arg.type === 4) {\n      if (arg.isStatic) {\n        arg.content = camelize(arg.content);\n      } else {\n        arg.content = `${context.helperString(CAMELIZE)}(${arg.content})`;\n      }\n    } else {\n      arg.children.unshift(`${context.helperString(CAMELIZE)}(`);\n      arg.children.push(`)`);\n    }\n  }\n  if (!context.inSSR) {\n    if (modifiers.some((mod) => mod.content === \"prop\")) {\n      injectPrefix(arg, \".\");\n    }\n    if (modifiers.some((mod) => mod.content === \"attr\")) {\n      injectPrefix(arg, \"^\");\n    }\n  }\n  return {\n    props: [createObjectProperty(arg, exp)]\n  };\n};\nconst transformBindShorthand = (dir, context) => {\n  const arg = dir.arg;\n  const propName = camelize(arg.content);\n  dir.exp = createSimpleExpression(propName, false, arg.loc);\n};\nconst injectPrefix = (arg, prefix) => {\n  if (arg.type === 4) {\n    if (arg.isStatic) {\n      arg.content = prefix + arg.content;\n    } else {\n      arg.content = `\\`${prefix}\\${${arg.content}}\\``;\n    }\n  } else {\n    arg.children.unshift(`'${prefix}' + (`);\n    arg.children.push(`)`);\n  }\n};\n\nconst transformFor = createStructuralDirectiveTransform(\n  \"for\",\n  (node, dir, context) => {\n    const { helper, removeHelper } = context;\n    return processFor(node, dir, context, (forNode) => {\n      const renderExp = createCallExpression(helper(RENDER_LIST), [\n        forNode.source\n      ]);\n      const isTemplate = isTemplateNode(node);\n      const memo = findDir(node, \"memo\");\n      const keyProp = findProp(node, `key`, false, true);\n      const isDirKey = keyProp && keyProp.type === 7;\n      if (isDirKey && !keyProp.exp) {\n        transformBindShorthand(keyProp);\n      }\n      let keyExp = keyProp && (keyProp.type === 6 ? keyProp.value ? createSimpleExpression(keyProp.value.content, true) : void 0 : keyProp.exp);\n      const keyProperty = keyProp && keyExp ? createObjectProperty(`key`, keyExp) : null;\n      const isStableFragment = forNode.source.type === 4 && forNode.source.constType > 0;\n      const fragmentFlag = isStableFragment ? 64 : keyProp ? 128 : 256;\n      forNode.codegenNode = createVNodeCall(\n        context,\n        helper(FRAGMENT),\n        void 0,\n        renderExp,\n        fragmentFlag,\n        void 0,\n        void 0,\n        true,\n        !isStableFragment,\n        false,\n        node.loc\n      );\n      return () => {\n        let childBlock;\n        const { children } = forNode;\n        if ((!!(process.env.NODE_ENV !== \"production\") || false) && isTemplate) {\n          node.children.some((c) => {\n            if (c.type === 1) {\n              const key = findProp(c, \"key\");\n              if (key) {\n                context.onError(\n                  createCompilerError(\n                    33,\n                    key.loc\n                  )\n                );\n                return true;\n              }\n            }\n          });\n        }\n        const needFragmentWrapper = children.length !== 1 || children[0].type !== 1;\n        const slotOutlet = isSlotOutlet(node) ? node : isTemplate && node.children.length === 1 && isSlotOutlet(node.children[0]) ? node.children[0] : null;\n        if (slotOutlet) {\n          childBlock = slotOutlet.codegenNode;\n          if (isTemplate && keyProperty) {\n            injectProp(childBlock, keyProperty, context);\n          }\n        } else if (needFragmentWrapper) {\n          childBlock = createVNodeCall(\n            context,\n            helper(FRAGMENT),\n            keyProperty ? createObjectExpression([keyProperty]) : void 0,\n            node.children,\n            64,\n            void 0,\n            void 0,\n            true,\n            void 0,\n            false\n          );\n        } else {\n          childBlock = children[0].codegenNode;\n          if (isTemplate && keyProperty) {\n            injectProp(childBlock, keyProperty, context);\n          }\n          if (childBlock.isBlock !== !isStableFragment) {\n            if (childBlock.isBlock) {\n              removeHelper(OPEN_BLOCK);\n              removeHelper(\n                getVNodeBlockHelper(context.inSSR, childBlock.isComponent)\n              );\n            } else {\n              removeHelper(\n                getVNodeHelper(context.inSSR, childBlock.isComponent)\n              );\n            }\n          }\n          childBlock.isBlock = !isStableFragment;\n          if (childBlock.isBlock) {\n            helper(OPEN_BLOCK);\n            helper(getVNodeBlockHelper(context.inSSR, childBlock.isComponent));\n          } else {\n            helper(getVNodeHelper(context.inSSR, childBlock.isComponent));\n          }\n        }\n        if (memo) {\n          const loop = createFunctionExpression(\n            createForLoopParams(forNode.parseResult, [\n              createSimpleExpression(`_cached`)\n            ])\n          );\n          loop.body = createBlockStatement([\n            createCompoundExpression([`const _memo = (`, memo.exp, `)`]),\n            createCompoundExpression([\n              `if (_cached`,\n              ...keyExp ? [` && _cached.key === `, keyExp] : [],\n              ` && ${context.helperString(\n                IS_MEMO_SAME\n              )}(_cached, _memo)) return _cached`\n            ]),\n            createCompoundExpression([`const _item = `, childBlock]),\n            createSimpleExpression(`_item.memo = _memo`),\n            createSimpleExpression(`return _item`)\n          ]);\n          renderExp.arguments.push(\n            loop,\n            createSimpleExpression(`_cache`),\n            createSimpleExpression(String(context.cached.length))\n          );\n          context.cached.push(null);\n        } else {\n          renderExp.arguments.push(\n            createFunctionExpression(\n              createForLoopParams(forNode.parseResult),\n              childBlock,\n              true\n            )\n          );\n        }\n      };\n    });\n  }\n);\nfunction processFor(node, dir, context, processCodegen) {\n  if (!dir.exp) {\n    context.onError(\n      createCompilerError(31, dir.loc)\n    );\n    return;\n  }\n  const parseResult = dir.forParseResult;\n  if (!parseResult) {\n    context.onError(\n      createCompilerError(32, dir.loc)\n    );\n    return;\n  }\n  finalizeForParseResult(parseResult, context);\n  const { addIdentifiers, removeIdentifiers, scopes } = context;\n  const { source, value, key, index } = parseResult;\n  const forNode = {\n    type: 11,\n    loc: dir.loc,\n    source,\n    valueAlias: value,\n    keyAlias: key,\n    objectIndexAlias: index,\n    parseResult,\n    children: isTemplateNode(node) ? node.children : [node]\n  };\n  context.replaceNode(forNode);\n  scopes.vFor++;\n  const onExit = processCodegen && processCodegen(forNode);\n  return () => {\n    scopes.vFor--;\n    if (onExit) onExit();\n  };\n}\nfunction finalizeForParseResult(result, context) {\n  if (result.finalized) return;\n  if (!!(process.env.NODE_ENV !== \"production\") && true) {\n    validateBrowserExpression(result.source, context);\n    if (result.key) {\n      validateBrowserExpression(\n        result.key,\n        context,\n        true\n      );\n    }\n    if (result.index) {\n      validateBrowserExpression(\n        result.index,\n        context,\n        true\n      );\n    }\n    if (result.value) {\n      validateBrowserExpression(\n        result.value,\n        context,\n        true\n      );\n    }\n  }\n  result.finalized = true;\n}\nfunction createForLoopParams({ value, key, index }, memoArgs = []) {\n  return createParamsList([value, key, index, ...memoArgs]);\n}\nfunction createParamsList(args) {\n  let i = args.length;\n  while (i--) {\n    if (args[i]) break;\n  }\n  return args.slice(0, i + 1).map((arg, i2) => arg || createSimpleExpression(`_`.repeat(i2 + 1), false));\n}\n\nconst defaultFallback = createSimpleExpression(`undefined`, false);\nconst trackSlotScopes = (node, context) => {\n  if (node.type === 1 && (node.tagType === 1 || node.tagType === 3)) {\n    const vSlot = findDir(node, \"slot\");\n    if (vSlot) {\n      vSlot.exp;\n      context.scopes.vSlot++;\n      return () => {\n        context.scopes.vSlot--;\n      };\n    }\n  }\n};\nconst trackVForSlotScopes = (node, context) => {\n  let vFor;\n  if (isTemplateNode(node) && node.props.some(isVSlot) && (vFor = findDir(node, \"for\"))) {\n    const result = vFor.forParseResult;\n    if (result) {\n      finalizeForParseResult(result, context);\n      const { value, key, index } = result;\n      const { addIdentifiers, removeIdentifiers } = context;\n      value && addIdentifiers(value);\n      key && addIdentifiers(key);\n      index && addIdentifiers(index);\n      return () => {\n        value && removeIdentifiers(value);\n        key && removeIdentifiers(key);\n        index && removeIdentifiers(index);\n      };\n    }\n  }\n};\nconst buildClientSlotFn = (props, _vForExp, children, loc) => createFunctionExpression(\n  props,\n  children,\n  false,\n  true,\n  children.length ? children[0].loc : loc\n);\nfunction buildSlots(node, context, buildSlotFn = buildClientSlotFn) {\n  context.helper(WITH_CTX);\n  const { children, loc } = node;\n  const slotsProperties = [];\n  const dynamicSlots = [];\n  let hasDynamicSlots = context.scopes.vSlot > 0 || context.scopes.vFor > 0;\n  const onComponentSlot = findDir(node, \"slot\", true);\n  if (onComponentSlot) {\n    const { arg, exp } = onComponentSlot;\n    if (arg && !isStaticExp(arg)) {\n      hasDynamicSlots = true;\n    }\n    slotsProperties.push(\n      createObjectProperty(\n        arg || createSimpleExpression(\"default\", true),\n        buildSlotFn(exp, void 0, children, loc)\n      )\n    );\n  }\n  let hasTemplateSlots = false;\n  let hasNamedDefaultSlot = false;\n  const implicitDefaultChildren = [];\n  const seenSlotNames = /* @__PURE__ */ new Set();\n  let conditionalBranchIndex = 0;\n  for (let i = 0; i < children.length; i++) {\n    const slotElement = children[i];\n    let slotDir;\n    if (!isTemplateNode(slotElement) || !(slotDir = findDir(slotElement, \"slot\", true))) {\n      if (slotElement.type !== 3) {\n        implicitDefaultChildren.push(slotElement);\n      }\n      continue;\n    }\n    if (onComponentSlot) {\n      context.onError(\n        createCompilerError(37, slotDir.loc)\n      );\n      break;\n    }\n    hasTemplateSlots = true;\n    const { children: slotChildren, loc: slotLoc } = slotElement;\n    const {\n      arg: slotName = createSimpleExpression(`default`, true),\n      exp: slotProps,\n      loc: dirLoc\n    } = slotDir;\n    let staticSlotName;\n    if (isStaticExp(slotName)) {\n      staticSlotName = slotName ? slotName.content : `default`;\n    } else {\n      hasDynamicSlots = true;\n    }\n    const vFor = findDir(slotElement, \"for\");\n    const slotFunction = buildSlotFn(slotProps, vFor, slotChildren, slotLoc);\n    let vIf;\n    let vElse;\n    if (vIf = findDir(slotElement, \"if\")) {\n      hasDynamicSlots = true;\n      dynamicSlots.push(\n        createConditionalExpression(\n          vIf.exp,\n          buildDynamicSlot(slotName, slotFunction, conditionalBranchIndex++),\n          defaultFallback\n        )\n      );\n    } else if (vElse = findDir(\n      slotElement,\n      /^else(-if)?$/,\n      true\n      /* allowEmpty */\n    )) {\n      let j = i;\n      let prev;\n      while (j--) {\n        prev = children[j];\n        if (prev.type !== 3) {\n          break;\n        }\n      }\n      if (prev && isTemplateNode(prev) && findDir(prev, /^(else-)?if$/)) {\n        let conditional = dynamicSlots[dynamicSlots.length - 1];\n        while (conditional.alternate.type === 19) {\n          conditional = conditional.alternate;\n        }\n        conditional.alternate = vElse.exp ? createConditionalExpression(\n          vElse.exp,\n          buildDynamicSlot(\n            slotName,\n            slotFunction,\n            conditionalBranchIndex++\n          ),\n          defaultFallback\n        ) : buildDynamicSlot(slotName, slotFunction, conditionalBranchIndex++);\n      } else {\n        context.onError(\n          createCompilerError(30, vElse.loc)\n        );\n      }\n    } else if (vFor) {\n      hasDynamicSlots = true;\n      const parseResult = vFor.forParseResult;\n      if (parseResult) {\n        finalizeForParseResult(parseResult, context);\n        dynamicSlots.push(\n          createCallExpression(context.helper(RENDER_LIST), [\n            parseResult.source,\n            createFunctionExpression(\n              createForLoopParams(parseResult),\n              buildDynamicSlot(slotName, slotFunction),\n              true\n            )\n          ])\n        );\n      } else {\n        context.onError(\n          createCompilerError(\n            32,\n            vFor.loc\n          )\n        );\n      }\n    } else {\n      if (staticSlotName) {\n        if (seenSlotNames.has(staticSlotName)) {\n          context.onError(\n            createCompilerError(\n              38,\n              dirLoc\n            )\n          );\n          continue;\n        }\n        seenSlotNames.add(staticSlotName);\n        if (staticSlotName === \"default\") {\n          hasNamedDefaultSlot = true;\n        }\n      }\n      slotsProperties.push(createObjectProperty(slotName, slotFunction));\n    }\n  }\n  if (!onComponentSlot) {\n    const buildDefaultSlotProperty = (props, children2) => {\n      const fn = buildSlotFn(props, void 0, children2, loc);\n      if (context.compatConfig) {\n        fn.isNonScopedSlot = true;\n      }\n      return createObjectProperty(`default`, fn);\n    };\n    if (!hasTemplateSlots) {\n      slotsProperties.push(buildDefaultSlotProperty(void 0, children));\n    } else if (implicitDefaultChildren.length && // #3766\n    // with whitespace: 'preserve', whitespaces between slots will end up in\n    // implicitDefaultChildren. Ignore if all implicit children are whitespaces.\n    implicitDefaultChildren.some((node2) => isNonWhitespaceContent(node2))) {\n      if (hasNamedDefaultSlot) {\n        context.onError(\n          createCompilerError(\n            39,\n            implicitDefaultChildren[0].loc\n          )\n        );\n      } else {\n        slotsProperties.push(\n          buildDefaultSlotProperty(void 0, implicitDefaultChildren)\n        );\n      }\n    }\n  }\n  const slotFlag = hasDynamicSlots ? 2 : hasForwardedSlots(node.children) ? 3 : 1;\n  let slots = createObjectExpression(\n    slotsProperties.concat(\n      createObjectProperty(\n        `_`,\n        // 2 = compiled but dynamic = can skip normalization, but must run diff\n        // 1 = compiled and static = can skip normalization AND diff as optimized\n        createSimpleExpression(\n          slotFlag + (!!(process.env.NODE_ENV !== \"production\") ? ` /* ${slotFlagsText[slotFlag]} */` : ``),\n          false\n        )\n      )\n    ),\n    loc\n  );\n  if (dynamicSlots.length) {\n    slots = createCallExpression(context.helper(CREATE_SLOTS), [\n      slots,\n      createArrayExpression(dynamicSlots)\n    ]);\n  }\n  return {\n    slots,\n    hasDynamicSlots\n  };\n}\nfunction buildDynamicSlot(name, fn, index) {\n  const props = [\n    createObjectProperty(`name`, name),\n    createObjectProperty(`fn`, fn)\n  ];\n  if (index != null) {\n    props.push(\n      createObjectProperty(`key`, createSimpleExpression(String(index), true))\n    );\n  }\n  return createObjectExpression(props);\n}\nfunction hasForwardedSlots(children) {\n  for (let i = 0; i < children.length; i++) {\n    const child = children[i];\n    switch (child.type) {\n      case 1:\n        if (child.tagType === 2 || hasForwardedSlots(child.children)) {\n          return true;\n        }\n        break;\n      case 9:\n        if (hasForwardedSlots(child.branches)) return true;\n        break;\n      case 10:\n      case 11:\n        if (hasForwardedSlots(child.children)) return true;\n        break;\n    }\n  }\n  return false;\n}\nfunction isNonWhitespaceContent(node) {\n  if (node.type !== 2 && node.type !== 12)\n    return true;\n  return node.type === 2 ? !!node.content.trim() : isNonWhitespaceContent(node.content);\n}\n\nconst directiveImportMap = /* @__PURE__ */ new WeakMap();\nconst transformElement = (node, context) => {\n  return function postTransformElement() {\n    node = context.currentNode;\n    if (!(node.type === 1 && (node.tagType === 0 || node.tagType === 1))) {\n      return;\n    }\n    const { tag, props } = node;\n    const isComponent = node.tagType === 1;\n    let vnodeTag = isComponent ? resolveComponentType(node, context) : `\"${tag}\"`;\n    const isDynamicComponent = isObject(vnodeTag) && vnodeTag.callee === RESOLVE_DYNAMIC_COMPONENT;\n    let vnodeProps;\n    let vnodeChildren;\n    let patchFlag = 0;\n    let vnodeDynamicProps;\n    let dynamicPropNames;\n    let vnodeDirectives;\n    let shouldUseBlock = (\n      // dynamic component may resolve to plain elements\n      isDynamicComponent || vnodeTag === TELEPORT || vnodeTag === SUSPENSE || !isComponent && // <svg> and <foreignObject> must be forced into blocks so that block\n      // updates inside get proper isSVG flag at runtime. (#639, #643)\n      // This is technically web-specific, but splitting the logic out of core\n      // leads to too much unnecessary complexity.\n      (tag === \"svg\" || tag === \"foreignObject\" || tag === \"math\")\n    );\n    if (props.length > 0) {\n      const propsBuildResult = buildProps(\n        node,\n        context,\n        void 0,\n        isComponent,\n        isDynamicComponent\n      );\n      vnodeProps = propsBuildResult.props;\n      patchFlag = propsBuildResult.patchFlag;\n      dynamicPropNames = propsBuildResult.dynamicPropNames;\n      const directives = propsBuildResult.directives;\n      vnodeDirectives = directives && directives.length ? createArrayExpression(\n        directives.map((dir) => buildDirectiveArgs(dir, context))\n      ) : void 0;\n      if (propsBuildResult.shouldUseBlock) {\n        shouldUseBlock = true;\n      }\n    }\n    if (node.children.length > 0) {\n      if (vnodeTag === KEEP_ALIVE) {\n        shouldUseBlock = true;\n        patchFlag |= 1024;\n        if (!!(process.env.NODE_ENV !== \"production\") && node.children.length > 1) {\n          context.onError(\n            createCompilerError(46, {\n              start: node.children[0].loc.start,\n              end: node.children[node.children.length - 1].loc.end,\n              source: \"\"\n            })\n          );\n        }\n      }\n      const shouldBuildAsSlots = isComponent && // Teleport is not a real component and has dedicated runtime handling\n      vnodeTag !== TELEPORT && // explained above.\n      vnodeTag !== KEEP_ALIVE;\n      if (shouldBuildAsSlots) {\n        const { slots, hasDynamicSlots } = buildSlots(node, context);\n        vnodeChildren = slots;\n        if (hasDynamicSlots) {\n          patchFlag |= 1024;\n        }\n      } else if (node.children.length === 1 && vnodeTag !== TELEPORT) {\n        const child = node.children[0];\n        const type = child.type;\n        const hasDynamicTextChild = type === 5 || type === 8;\n        if (hasDynamicTextChild && getConstantType(child, context) === 0) {\n          patchFlag |= 1;\n        }\n        if (hasDynamicTextChild || type === 2) {\n          vnodeChildren = child;\n        } else {\n          vnodeChildren = node.children;\n        }\n      } else {\n        vnodeChildren = node.children;\n      }\n    }\n    if (dynamicPropNames && dynamicPropNames.length) {\n      vnodeDynamicProps = stringifyDynamicPropNames(dynamicPropNames);\n    }\n    node.codegenNode = createVNodeCall(\n      context,\n      vnodeTag,\n      vnodeProps,\n      vnodeChildren,\n      patchFlag === 0 ? void 0 : patchFlag,\n      vnodeDynamicProps,\n      vnodeDirectives,\n      !!shouldUseBlock,\n      false,\n      isComponent,\n      node.loc\n    );\n  };\n};\nfunction resolveComponentType(node, context, ssr = false) {\n  let { tag } = node;\n  const isExplicitDynamic = isComponentTag(tag);\n  const isProp = findProp(\n    node,\n    \"is\",\n    false,\n    true\n    /* allow empty */\n  );\n  if (isProp) {\n    if (isExplicitDynamic || isCompatEnabled(\n      \"COMPILER_IS_ON_ELEMENT\",\n      context\n    )) {\n      let exp;\n      if (isProp.type === 6) {\n        exp = isProp.value && createSimpleExpression(isProp.value.content, true);\n      } else {\n        exp = isProp.exp;\n        if (!exp) {\n          exp = createSimpleExpression(`is`, false, isProp.arg.loc);\n        }\n      }\n      if (exp) {\n        return createCallExpression(context.helper(RESOLVE_DYNAMIC_COMPONENT), [\n          exp\n        ]);\n      }\n    } else if (isProp.type === 6 && isProp.value.content.startsWith(\"vue:\")) {\n      tag = isProp.value.content.slice(4);\n    }\n  }\n  const builtIn = isCoreComponent(tag) || context.isBuiltInComponent(tag);\n  if (builtIn) {\n    if (!ssr) context.helper(builtIn);\n    return builtIn;\n  }\n  context.helper(RESOLVE_COMPONENT);\n  context.components.add(tag);\n  return toValidAssetId(tag, `component`);\n}\nfunction buildProps(node, context, props = node.props, isComponent, isDynamicComponent, ssr = false) {\n  const { tag, loc: elementLoc, children } = node;\n  let properties = [];\n  const mergeArgs = [];\n  const runtimeDirectives = [];\n  const hasChildren = children.length > 0;\n  let shouldUseBlock = false;\n  let patchFlag = 0;\n  let hasRef = false;\n  let hasClassBinding = false;\n  let hasStyleBinding = false;\n  let hasHydrationEventBinding = false;\n  let hasDynamicKeys = false;\n  let hasVnodeHook = false;\n  const dynamicPropNames = [];\n  const pushMergeArg = (arg) => {\n    if (properties.length) {\n      mergeArgs.push(\n        createObjectExpression(dedupeProperties(properties), elementLoc)\n      );\n      properties = [];\n    }\n    if (arg) mergeArgs.push(arg);\n  };\n  const pushRefVForMarker = () => {\n    if (context.scopes.vFor > 0) {\n      properties.push(\n        createObjectProperty(\n          createSimpleExpression(\"ref_for\", true),\n          createSimpleExpression(\"true\")\n        )\n      );\n    }\n  };\n  const analyzePatchFlag = ({ key, value }) => {\n    if (isStaticExp(key)) {\n      const name = key.content;\n      const isEventHandler = isOn(name);\n      if (isEventHandler && (!isComponent || isDynamicComponent) && // omit the flag for click handlers because hydration gives click\n      // dedicated fast path.\n      name.toLowerCase() !== \"onclick\" && // omit v-model handlers\n      name !== \"onUpdate:modelValue\" && // omit onVnodeXXX hooks\n      !isReservedProp(name)) {\n        hasHydrationEventBinding = true;\n      }\n      if (isEventHandler && isReservedProp(name)) {\n        hasVnodeHook = true;\n      }\n      if (isEventHandler && value.type === 14) {\n        value = value.arguments[0];\n      }\n      if (value.type === 20 || (value.type === 4 || value.type === 8) && getConstantType(value, context) > 0) {\n        return;\n      }\n      if (name === \"ref\") {\n        hasRef = true;\n      } else if (name === \"class\") {\n        hasClassBinding = true;\n      } else if (name === \"style\") {\n        hasStyleBinding = true;\n      } else if (name !== \"key\" && !dynamicPropNames.includes(name)) {\n        dynamicPropNames.push(name);\n      }\n      if (isComponent && (name === \"class\" || name === \"style\") && !dynamicPropNames.includes(name)) {\n        dynamicPropNames.push(name);\n      }\n    } else {\n      hasDynamicKeys = true;\n    }\n  };\n  for (let i = 0; i < props.length; i++) {\n    const prop = props[i];\n    if (prop.type === 6) {\n      const { loc, name, nameLoc, value } = prop;\n      let isStatic = true;\n      if (name === \"ref\") {\n        hasRef = true;\n        pushRefVForMarker();\n      }\n      if (name === \"is\" && (isComponentTag(tag) || value && value.content.startsWith(\"vue:\") || isCompatEnabled(\n        \"COMPILER_IS_ON_ELEMENT\",\n        context\n      ))) {\n        continue;\n      }\n      properties.push(\n        createObjectProperty(\n          createSimpleExpression(name, true, nameLoc),\n          createSimpleExpression(\n            value ? value.content : \"\",\n            isStatic,\n            value ? value.loc : loc\n          )\n        )\n      );\n    } else {\n      const { name, arg, exp, loc, modifiers } = prop;\n      const isVBind = name === \"bind\";\n      const isVOn = name === \"on\";\n      if (name === \"slot\") {\n        if (!isComponent) {\n          context.onError(\n            createCompilerError(40, loc)\n          );\n        }\n        continue;\n      }\n      if (name === \"once\" || name === \"memo\") {\n        continue;\n      }\n      if (name === \"is\" || isVBind && isStaticArgOf(arg, \"is\") && (isComponentTag(tag) || isCompatEnabled(\n        \"COMPILER_IS_ON_ELEMENT\",\n        context\n      ))) {\n        continue;\n      }\n      if (isVOn && ssr) {\n        continue;\n      }\n      if (\n        // #938: elements with dynamic keys should be forced into blocks\n        isVBind && isStaticArgOf(arg, \"key\") || // inline before-update hooks need to force block so that it is invoked\n        // before children\n        isVOn && hasChildren && isStaticArgOf(arg, \"vue:before-update\")\n      ) {\n        shouldUseBlock = true;\n      }\n      if (isVBind && isStaticArgOf(arg, \"ref\")) {\n        pushRefVForMarker();\n      }\n      if (!arg && (isVBind || isVOn)) {\n        hasDynamicKeys = true;\n        if (exp) {\n          if (isVBind) {\n            pushRefVForMarker();\n            pushMergeArg();\n            {\n              if (!!(process.env.NODE_ENV !== \"production\")) {\n                const hasOverridableKeys = mergeArgs.some((arg2) => {\n                  if (arg2.type === 15) {\n                    return arg2.properties.some(({ key }) => {\n                      if (key.type !== 4 || !key.isStatic) {\n                        return true;\n                      }\n                      return key.content !== \"class\" && key.content !== \"style\" && !isOn(key.content);\n                    });\n                  } else {\n                    return true;\n                  }\n                });\n                if (hasOverridableKeys) {\n                  checkCompatEnabled(\n                    \"COMPILER_V_BIND_OBJECT_ORDER\",\n                    context,\n                    loc\n                  );\n                }\n              }\n              if (isCompatEnabled(\n                \"COMPILER_V_BIND_OBJECT_ORDER\",\n                context\n              )) {\n                mergeArgs.unshift(exp);\n                continue;\n              }\n            }\n            mergeArgs.push(exp);\n          } else {\n            pushMergeArg({\n              type: 14,\n              loc,\n              callee: context.helper(TO_HANDLERS),\n              arguments: isComponent ? [exp] : [exp, `true`]\n            });\n          }\n        } else {\n          context.onError(\n            createCompilerError(\n              isVBind ? 34 : 35,\n              loc\n            )\n          );\n        }\n        continue;\n      }\n      if (isVBind && modifiers.some((mod) => mod.content === \"prop\")) {\n        patchFlag |= 32;\n      }\n      const directiveTransform = context.directiveTransforms[name];\n      if (directiveTransform) {\n        const { props: props2, needRuntime } = directiveTransform(prop, node, context);\n        !ssr && props2.forEach(analyzePatchFlag);\n        if (isVOn && arg && !isStaticExp(arg)) {\n          pushMergeArg(createObjectExpression(props2, elementLoc));\n        } else {\n          properties.push(...props2);\n        }\n        if (needRuntime) {\n          runtimeDirectives.push(prop);\n          if (isSymbol(needRuntime)) {\n            directiveImportMap.set(prop, needRuntime);\n          }\n        }\n      } else if (!isBuiltInDirective(name)) {\n        runtimeDirectives.push(prop);\n        if (hasChildren) {\n          shouldUseBlock = true;\n        }\n      }\n    }\n  }\n  let propsExpression = void 0;\n  if (mergeArgs.length) {\n    pushMergeArg();\n    if (mergeArgs.length > 1) {\n      propsExpression = createCallExpression(\n        context.helper(MERGE_PROPS),\n        mergeArgs,\n        elementLoc\n      );\n    } else {\n      propsExpression = mergeArgs[0];\n    }\n  } else if (properties.length) {\n    propsExpression = createObjectExpression(\n      dedupeProperties(properties),\n      elementLoc\n    );\n  }\n  if (hasDynamicKeys) {\n    patchFlag |= 16;\n  } else {\n    if (hasClassBinding && !isComponent) {\n      patchFlag |= 2;\n    }\n    if (hasStyleBinding && !isComponent) {\n      patchFlag |= 4;\n    }\n    if (dynamicPropNames.length) {\n      patchFlag |= 8;\n    }\n    if (hasHydrationEventBinding) {\n      patchFlag |= 32;\n    }\n  }\n  if (!shouldUseBlock && (patchFlag === 0 || patchFlag === 32) && (hasRef || hasVnodeHook || runtimeDirectives.length > 0)) {\n    patchFlag |= 512;\n  }\n  if (!context.inSSR && propsExpression) {\n    switch (propsExpression.type) {\n      case 15:\n        let classKeyIndex = -1;\n        let styleKeyIndex = -1;\n        let hasDynamicKey = false;\n        for (let i = 0; i < propsExpression.properties.length; i++) {\n          const key = propsExpression.properties[i].key;\n          if (isStaticExp(key)) {\n            if (key.content === \"class\") {\n              classKeyIndex = i;\n            } else if (key.content === \"style\") {\n              styleKeyIndex = i;\n            }\n          } else if (!key.isHandlerKey) {\n            hasDynamicKey = true;\n          }\n        }\n        const classProp = propsExpression.properties[classKeyIndex];\n        const styleProp = propsExpression.properties[styleKeyIndex];\n        if (!hasDynamicKey) {\n          if (classProp && !isStaticExp(classProp.value)) {\n            classProp.value = createCallExpression(\n              context.helper(NORMALIZE_CLASS),\n              [classProp.value]\n            );\n          }\n          if (styleProp && // the static style is compiled into an object,\n          // so use `hasStyleBinding` to ensure that it is a dynamic style binding\n          (hasStyleBinding || styleProp.value.type === 4 && styleProp.value.content.trim()[0] === `[` || // v-bind:style and style both exist,\n          // v-bind:style with static literal object\n          styleProp.value.type === 17)) {\n            styleProp.value = createCallExpression(\n              context.helper(NORMALIZE_STYLE),\n              [styleProp.value]\n            );\n          }\n        } else {\n          propsExpression = createCallExpression(\n            context.helper(NORMALIZE_PROPS),\n            [propsExpression]\n          );\n        }\n        break;\n      case 14:\n        break;\n      default:\n        propsExpression = createCallExpression(\n          context.helper(NORMALIZE_PROPS),\n          [\n            createCallExpression(context.helper(GUARD_REACTIVE_PROPS), [\n              propsExpression\n            ])\n          ]\n        );\n        break;\n    }\n  }\n  return {\n    props: propsExpression,\n    directives: runtimeDirectives,\n    patchFlag,\n    dynamicPropNames,\n    shouldUseBlock\n  };\n}\nfunction dedupeProperties(properties) {\n  const knownProps = /* @__PURE__ */ new Map();\n  const deduped = [];\n  for (let i = 0; i < properties.length; i++) {\n    const prop = properties[i];\n    if (prop.key.type === 8 || !prop.key.isStatic) {\n      deduped.push(prop);\n      continue;\n    }\n    const name = prop.key.content;\n    const existing = knownProps.get(name);\n    if (existing) {\n      if (name === \"style\" || name === \"class\" || isOn(name)) {\n        mergeAsArray(existing, prop);\n      }\n    } else {\n      knownProps.set(name, prop);\n      deduped.push(prop);\n    }\n  }\n  return deduped;\n}\nfunction mergeAsArray(existing, incoming) {\n  if (existing.value.type === 17) {\n    existing.value.elements.push(incoming.value);\n  } else {\n    existing.value = createArrayExpression(\n      [existing.value, incoming.value],\n      existing.loc\n    );\n  }\n}\nfunction buildDirectiveArgs(dir, context) {\n  const dirArgs = [];\n  const runtime = directiveImportMap.get(dir);\n  if (runtime) {\n    dirArgs.push(context.helperString(runtime));\n  } else {\n    {\n      context.helper(RESOLVE_DIRECTIVE);\n      context.directives.add(dir.name);\n      dirArgs.push(toValidAssetId(dir.name, `directive`));\n    }\n  }\n  const { loc } = dir;\n  if (dir.exp) dirArgs.push(dir.exp);\n  if (dir.arg) {\n    if (!dir.exp) {\n      dirArgs.push(`void 0`);\n    }\n    dirArgs.push(dir.arg);\n  }\n  if (Object.keys(dir.modifiers).length) {\n    if (!dir.arg) {\n      if (!dir.exp) {\n        dirArgs.push(`void 0`);\n      }\n      dirArgs.push(`void 0`);\n    }\n    const trueExpression = createSimpleExpression(`true`, false, loc);\n    dirArgs.push(\n      createObjectExpression(\n        dir.modifiers.map(\n          (modifier) => createObjectProperty(modifier, trueExpression)\n        ),\n        loc\n      )\n    );\n  }\n  return createArrayExpression(dirArgs, dir.loc);\n}\nfunction stringifyDynamicPropNames(props) {\n  let propsNamesString = `[`;\n  for (let i = 0, l = props.length; i < l; i++) {\n    propsNamesString += JSON.stringify(props[i]);\n    if (i < l - 1) propsNamesString += \", \";\n  }\n  return propsNamesString + `]`;\n}\nfunction isComponentTag(tag) {\n  return tag === \"component\" || tag === \"Component\";\n}\n\nconst transformSlotOutlet = (node, context) => {\n  if (isSlotOutlet(node)) {\n    const { children, loc } = node;\n    const { slotName, slotProps } = processSlotOutlet(node, context);\n    const slotArgs = [\n      context.prefixIdentifiers ? `_ctx.$slots` : `$slots`,\n      slotName,\n      \"{}\",\n      \"undefined\",\n      \"true\"\n    ];\n    let expectedLen = 2;\n    if (slotProps) {\n      slotArgs[2] = slotProps;\n      expectedLen = 3;\n    }\n    if (children.length) {\n      slotArgs[3] = createFunctionExpression([], children, false, false, loc);\n      expectedLen = 4;\n    }\n    if (context.scopeId && !context.slotted) {\n      expectedLen = 5;\n    }\n    slotArgs.splice(expectedLen);\n    node.codegenNode = createCallExpression(\n      context.helper(RENDER_SLOT),\n      slotArgs,\n      loc\n    );\n  }\n};\nfunction processSlotOutlet(node, context) {\n  let slotName = `\"default\"`;\n  let slotProps = void 0;\n  const nonNameProps = [];\n  for (let i = 0; i < node.props.length; i++) {\n    const p = node.props[i];\n    if (p.type === 6) {\n      if (p.value) {\n        if (p.name === \"name\") {\n          slotName = JSON.stringify(p.value.content);\n        } else {\n          p.name = camelize(p.name);\n          nonNameProps.push(p);\n        }\n      }\n    } else {\n      if (p.name === \"bind\" && isStaticArgOf(p.arg, \"name\")) {\n        if (p.exp) {\n          slotName = p.exp;\n        } else if (p.arg && p.arg.type === 4) {\n          const name = camelize(p.arg.content);\n          slotName = p.exp = createSimpleExpression(name, false, p.arg.loc);\n        }\n      } else {\n        if (p.name === \"bind\" && p.arg && isStaticExp(p.arg)) {\n          p.arg.content = camelize(p.arg.content);\n        }\n        nonNameProps.push(p);\n      }\n    }\n  }\n  if (nonNameProps.length > 0) {\n    const { props, directives } = buildProps(\n      node,\n      context,\n      nonNameProps,\n      false,\n      false\n    );\n    slotProps = props;\n    if (directives.length) {\n      context.onError(\n        createCompilerError(\n          36,\n          directives[0].loc\n        )\n      );\n    }\n  }\n  return {\n    slotName,\n    slotProps\n  };\n}\n\nconst transformOn = (dir, node, context, augmentor) => {\n  const { loc, modifiers, arg } = dir;\n  if (!dir.exp && !modifiers.length) {\n    context.onError(createCompilerError(35, loc));\n  }\n  let eventName;\n  if (arg.type === 4) {\n    if (arg.isStatic) {\n      let rawName = arg.content;\n      if (!!(process.env.NODE_ENV !== \"production\") && rawName.startsWith(\"vnode\")) {\n        context.onError(createCompilerError(51, arg.loc));\n      }\n      if (rawName.startsWith(\"vue:\")) {\n        rawName = `vnode-${rawName.slice(4)}`;\n      }\n      const eventString = node.tagType !== 0 || rawName.startsWith(\"vnode\") || !/[A-Z]/.test(rawName) ? (\n        // for non-element and vnode lifecycle event listeners, auto convert\n        // it to camelCase. See issue #2249\n        toHandlerKey(camelize(rawName))\n      ) : (\n        // preserve case for plain element listeners that have uppercase\n        // letters, as these may be custom elements' custom events\n        `on:${rawName}`\n      );\n      eventName = createSimpleExpression(eventString, true, arg.loc);\n    } else {\n      eventName = createCompoundExpression([\n        `${context.helperString(TO_HANDLER_KEY)}(`,\n        arg,\n        `)`\n      ]);\n    }\n  } else {\n    eventName = arg;\n    eventName.children.unshift(`${context.helperString(TO_HANDLER_KEY)}(`);\n    eventName.children.push(`)`);\n  }\n  let exp = dir.exp;\n  if (exp && !exp.content.trim()) {\n    exp = void 0;\n  }\n  let shouldCache = context.cacheHandlers && !exp && !context.inVOnce;\n  if (exp) {\n    const isMemberExp = isMemberExpression(exp);\n    const isInlineStatement = !(isMemberExp || isFnExpression(exp));\n    const hasMultipleStatements = exp.content.includes(`;`);\n    if (!!(process.env.NODE_ENV !== \"production\") && true) {\n      validateBrowserExpression(\n        exp,\n        context,\n        false,\n        hasMultipleStatements\n      );\n    }\n    if (isInlineStatement || shouldCache && isMemberExp) {\n      exp = createCompoundExpression([\n        `${isInlineStatement ? `$event` : `${``}(...args)`} => ${hasMultipleStatements ? `{` : `(`}`,\n        exp,\n        hasMultipleStatements ? `}` : `)`\n      ]);\n    }\n  }\n  let ret = {\n    props: [\n      createObjectProperty(\n        eventName,\n        exp || createSimpleExpression(`() => {}`, false, loc)\n      )\n    ]\n  };\n  if (augmentor) {\n    ret = augmentor(ret);\n  }\n  if (shouldCache) {\n    ret.props[0].value = context.cache(ret.props[0].value);\n  }\n  ret.props.forEach((p) => p.key.isHandlerKey = true);\n  return ret;\n};\n\nconst transformText = (node, context) => {\n  if (node.type === 0 || node.type === 1 || node.type === 11 || node.type === 10) {\n    return () => {\n      const children = node.children;\n      let currentContainer = void 0;\n      let hasText = false;\n      for (let i = 0; i < children.length; i++) {\n        const child = children[i];\n        if (isText$1(child)) {\n          hasText = true;\n          for (let j = i + 1; j < children.length; j++) {\n            const next = children[j];\n            if (isText$1(next)) {\n              if (!currentContainer) {\n                currentContainer = children[i] = createCompoundExpression(\n                  [child],\n                  child.loc\n                );\n              }\n              currentContainer.children.push(` + `, next);\n              children.splice(j, 1);\n              j--;\n            } else {\n              currentContainer = void 0;\n              break;\n            }\n          }\n        }\n      }\n      if (!hasText || // if this is a plain element with a single text child, leave it\n      // as-is since the runtime has dedicated fast path for this by directly\n      // setting textContent of the element.\n      // for component root it's always normalized anyway.\n      children.length === 1 && (node.type === 0 || node.type === 1 && node.tagType === 0 && // #3756\n      // custom directives can potentially add DOM elements arbitrarily,\n      // we need to avoid setting textContent of the element at runtime\n      // to avoid accidentally overwriting the DOM elements added\n      // by the user through custom directives.\n      !node.props.find(\n        (p) => p.type === 7 && !context.directiveTransforms[p.name]\n      ) && // in compat mode, <template> tags with no special directives\n      // will be rendered as a fragment so its children must be\n      // converted into vnodes.\n      !(node.tag === \"template\"))) {\n        return;\n      }\n      for (let i = 0; i < children.length; i++) {\n        const child = children[i];\n        if (isText$1(child) || child.type === 8) {\n          const callArgs = [];\n          if (child.type !== 2 || child.content !== \" \") {\n            callArgs.push(child);\n          }\n          if (!context.ssr && getConstantType(child, context) === 0) {\n            callArgs.push(\n              1 + (!!(process.env.NODE_ENV !== \"production\") ? ` /* ${PatchFlagNames[1]} */` : ``)\n            );\n          }\n          children[i] = {\n            type: 12,\n            content: child,\n            loc: child.loc,\n            codegenNode: createCallExpression(\n              context.helper(CREATE_TEXT),\n              callArgs\n            )\n          };\n        }\n      }\n    };\n  }\n};\n\nconst seen$1 = /* @__PURE__ */ new WeakSet();\nconst transformOnce = (node, context) => {\n  if (node.type === 1 && findDir(node, \"once\", true)) {\n    if (seen$1.has(node) || context.inVOnce || context.inSSR) {\n      return;\n    }\n    seen$1.add(node);\n    context.inVOnce = true;\n    context.helper(SET_BLOCK_TRACKING);\n    return () => {\n      context.inVOnce = false;\n      const cur = context.currentNode;\n      if (cur.codegenNode) {\n        cur.codegenNode = context.cache(\n          cur.codegenNode,\n          true,\n          true\n        );\n      }\n    };\n  }\n};\n\nconst transformModel = (dir, node, context) => {\n  const { exp, arg } = dir;\n  if (!exp) {\n    context.onError(\n      createCompilerError(41, dir.loc)\n    );\n    return createTransformProps();\n  }\n  const rawExp = exp.loc.source.trim();\n  const expString = exp.type === 4 ? exp.content : rawExp;\n  const bindingType = context.bindingMetadata[rawExp];\n  if (bindingType === \"props\" || bindingType === \"props-aliased\") {\n    context.onError(createCompilerError(44, exp.loc));\n    return createTransformProps();\n  }\n  const maybeRef = false;\n  if (!expString.trim() || !isMemberExpression(exp) && !maybeRef) {\n    context.onError(\n      createCompilerError(42, exp.loc)\n    );\n    return createTransformProps();\n  }\n  const propName = arg ? arg : createSimpleExpression(\"modelValue\", true);\n  const eventName = arg ? isStaticExp(arg) ? `onUpdate:${camelize(arg.content)}` : createCompoundExpression(['\"onUpdate:\" + ', arg]) : `onUpdate:modelValue`;\n  let assignmentExp;\n  const eventArg = context.isTS ? `($event: any)` : `$event`;\n  {\n    assignmentExp = createCompoundExpression([\n      `${eventArg} => ((`,\n      exp,\n      `) = $event)`\n    ]);\n  }\n  const props = [\n    // modelValue: foo\n    createObjectProperty(propName, dir.exp),\n    // \"onUpdate:modelValue\": $event => (foo = $event)\n    createObjectProperty(eventName, assignmentExp)\n  ];\n  if (dir.modifiers.length && node.tagType === 1) {\n    const modifiers = dir.modifiers.map((m) => m.content).map((m) => (isSimpleIdentifier(m) ? m : JSON.stringify(m)) + `: true`).join(`, `);\n    const modifiersKey = arg ? isStaticExp(arg) ? `${arg.content}Modifiers` : createCompoundExpression([arg, ' + \"Modifiers\"']) : `modelModifiers`;\n    props.push(\n      createObjectProperty(\n        modifiersKey,\n        createSimpleExpression(\n          `{ ${modifiers} }`,\n          false,\n          dir.loc,\n          2\n        )\n      )\n    );\n  }\n  return createTransformProps(props);\n};\nfunction createTransformProps(props = []) {\n  return { props };\n}\n\nconst validDivisionCharRE = /[\\w).+\\-_$\\]]/;\nconst transformFilter = (node, context) => {\n  if (!isCompatEnabled(\"COMPILER_FILTERS\", context)) {\n    return;\n  }\n  if (node.type === 5) {\n    rewriteFilter(node.content, context);\n  } else if (node.type === 1) {\n    node.props.forEach((prop) => {\n      if (prop.type === 7 && prop.name !== \"for\" && prop.exp) {\n        rewriteFilter(prop.exp, context);\n      }\n    });\n  }\n};\nfunction rewriteFilter(node, context) {\n  if (node.type === 4) {\n    parseFilter(node, context);\n  } else {\n    for (let i = 0; i < node.children.length; i++) {\n      const child = node.children[i];\n      if (typeof child !== \"object\") continue;\n      if (child.type === 4) {\n        parseFilter(child, context);\n      } else if (child.type === 8) {\n        rewriteFilter(node, context);\n      } else if (child.type === 5) {\n        rewriteFilter(child.content, context);\n      }\n    }\n  }\n}\nfunction parseFilter(node, context) {\n  const exp = node.content;\n  let inSingle = false;\n  let inDouble = false;\n  let inTemplateString = false;\n  let inRegex = false;\n  let curly = 0;\n  let square = 0;\n  let paren = 0;\n  let lastFilterIndex = 0;\n  let c, prev, i, expression, filters = [];\n  for (i = 0; i < exp.length; i++) {\n    prev = c;\n    c = exp.charCodeAt(i);\n    if (inSingle) {\n      if (c === 39 && prev !== 92) inSingle = false;\n    } else if (inDouble) {\n      if (c === 34 && prev !== 92) inDouble = false;\n    } else if (inTemplateString) {\n      if (c === 96 && prev !== 92) inTemplateString = false;\n    } else if (inRegex) {\n      if (c === 47 && prev !== 92) inRegex = false;\n    } else if (c === 124 && // pipe\n    exp.charCodeAt(i + 1) !== 124 && exp.charCodeAt(i - 1) !== 124 && !curly && !square && !paren) {\n      if (expression === void 0) {\n        lastFilterIndex = i + 1;\n        expression = exp.slice(0, i).trim();\n      } else {\n        pushFilter();\n      }\n    } else {\n      switch (c) {\n        case 34:\n          inDouble = true;\n          break;\n        // \"\n        case 39:\n          inSingle = true;\n          break;\n        // '\n        case 96:\n          inTemplateString = true;\n          break;\n        // `\n        case 40:\n          paren++;\n          break;\n        // (\n        case 41:\n          paren--;\n          break;\n        // )\n        case 91:\n          square++;\n          break;\n        // [\n        case 93:\n          square--;\n          break;\n        // ]\n        case 123:\n          curly++;\n          break;\n        // {\n        case 125:\n          curly--;\n          break;\n      }\n      if (c === 47) {\n        let j = i - 1;\n        let p;\n        for (; j >= 0; j--) {\n          p = exp.charAt(j);\n          if (p !== \" \") break;\n        }\n        if (!p || !validDivisionCharRE.test(p)) {\n          inRegex = true;\n        }\n      }\n    }\n  }\n  if (expression === void 0) {\n    expression = exp.slice(0, i).trim();\n  } else if (lastFilterIndex !== 0) {\n    pushFilter();\n  }\n  function pushFilter() {\n    filters.push(exp.slice(lastFilterIndex, i).trim());\n    lastFilterIndex = i + 1;\n  }\n  if (filters.length) {\n    !!(process.env.NODE_ENV !== \"production\") && warnDeprecation(\n      \"COMPILER_FILTERS\",\n      context,\n      node.loc\n    );\n    for (i = 0; i < filters.length; i++) {\n      expression = wrapFilter(expression, filters[i], context);\n    }\n    node.content = expression;\n    node.ast = void 0;\n  }\n}\nfunction wrapFilter(exp, filter, context) {\n  context.helper(RESOLVE_FILTER);\n  const i = filter.indexOf(\"(\");\n  if (i < 0) {\n    context.filters.add(filter);\n    return `${toValidAssetId(filter, \"filter\")}(${exp})`;\n  } else {\n    const name = filter.slice(0, i);\n    const args = filter.slice(i + 1);\n    context.filters.add(name);\n    return `${toValidAssetId(name, \"filter\")}(${exp}${args !== \")\" ? \",\" + args : args}`;\n  }\n}\n\nconst seen = /* @__PURE__ */ new WeakSet();\nconst transformMemo = (node, context) => {\n  if (node.type === 1) {\n    const dir = findDir(node, \"memo\");\n    if (!dir || seen.has(node)) {\n      return;\n    }\n    seen.add(node);\n    return () => {\n      const codegenNode = node.codegenNode || context.currentNode.codegenNode;\n      if (codegenNode && codegenNode.type === 13) {\n        if (node.tagType !== 1) {\n          convertToBlock(codegenNode, context);\n        }\n        node.codegenNode = createCallExpression(context.helper(WITH_MEMO), [\n          dir.exp,\n          createFunctionExpression(void 0, codegenNode),\n          `_cache`,\n          String(context.cached.length)\n        ]);\n        context.cached.push(null);\n      }\n    };\n  }\n};\n\nfunction getBaseTransformPreset(prefixIdentifiers) {\n  return [\n    [\n      transformOnce,\n      transformIf,\n      transformMemo,\n      transformFor,\n      ...[transformFilter] ,\n      ...!!(process.env.NODE_ENV !== \"production\") ? [transformExpression] : [],\n      transformSlotOutlet,\n      transformElement,\n      trackSlotScopes,\n      transformText\n    ],\n    {\n      on: transformOn,\n      bind: transformBind,\n      model: transformModel\n    }\n  ];\n}\nfunction baseCompile(source, options = {}) {\n  const onError = options.onError || defaultOnError;\n  const isModuleMode = options.mode === \"module\";\n  {\n    if (options.prefixIdentifiers === true) {\n      onError(createCompilerError(47));\n    } else if (isModuleMode) {\n      onError(createCompilerError(48));\n    }\n  }\n  const prefixIdentifiers = false;\n  if (options.cacheHandlers) {\n    onError(createCompilerError(49));\n  }\n  if (options.scopeId && !isModuleMode) {\n    onError(createCompilerError(50));\n  }\n  const resolvedOptions = extend({}, options, {\n    prefixIdentifiers\n  });\n  const ast = isString(source) ? baseParse(source, resolvedOptions) : source;\n  const [nodeTransforms, directiveTransforms] = getBaseTransformPreset();\n  transform(\n    ast,\n    extend({}, resolvedOptions, {\n      nodeTransforms: [\n        ...nodeTransforms,\n        ...options.nodeTransforms || []\n        // user transforms\n      ],\n      directiveTransforms: extend(\n        {},\n        directiveTransforms,\n        options.directiveTransforms || {}\n        // user transforms\n      )\n    })\n  );\n  return generate(ast, resolvedOptions);\n}\n\nconst BindingTypes = {\n  \"DATA\": \"data\",\n  \"PROPS\": \"props\",\n  \"PROPS_ALIASED\": \"props-aliased\",\n  \"SETUP_LET\": \"setup-let\",\n  \"SETUP_CONST\": \"setup-const\",\n  \"SETUP_REACTIVE_CONST\": \"setup-reactive-const\",\n  \"SETUP_MAYBE_REF\": \"setup-maybe-ref\",\n  \"SETUP_REF\": \"setup-ref\",\n  \"OPTIONS\": \"options\",\n  \"LITERAL_CONST\": \"literal-const\"\n};\n\nconst noopDirectiveTransform = () => ({ props: [] });\n\nexport { BASE_TRANSITION, BindingTypes, CAMELIZE, CAPITALIZE, CREATE_BLOCK, CREATE_COMMENT, CREATE_ELEMENT_BLOCK, CREATE_ELEMENT_VNODE, CREATE_SLOTS, CREATE_STATIC, CREATE_TEXT, CREATE_VNODE, CompilerDeprecationTypes, ConstantTypes, ElementTypes, ErrorCodes, FRAGMENT, GUARD_REACTIVE_PROPS, IS_MEMO_SAME, IS_REF, KEEP_ALIVE, MERGE_PROPS, NORMALIZE_CLASS, NORMALIZE_PROPS, NORMALIZE_STYLE, Namespaces, NodeTypes, OPEN_BLOCK, POP_SCOPE_ID, PUSH_SCOPE_ID, RENDER_LIST, RENDER_SLOT, RESOLVE_COMPONENT, RESOLVE_DIRECTIVE, RESOLVE_DYNAMIC_COMPONENT, RESOLVE_FILTER, SET_BLOCK_TRACKING, SUSPENSE, TELEPORT, TO_DISPLAY_STRING, TO_HANDLERS, TO_HANDLER_KEY, TS_NODE_TYPES, UNREF, WITH_CTX, WITH_DIRECTIVES, WITH_MEMO, advancePositionWithClone, advancePositionWithMutation, assert, baseCompile, baseParse, buildDirectiveArgs, buildProps, buildSlots, checkCompatEnabled, convertToBlock, createArrayExpression, createAssignmentExpression, createBlockStatement, createCacheExpression, createCallExpression, createCompilerError, createCompoundExpression, createConditionalExpression, createForLoopParams, createFunctionExpression, createIfStatement, createInterpolation, createObjectExpression, createObjectProperty, createReturnStatement, createRoot, createSequenceExpression, createSimpleExpression, createStructuralDirectiveTransform, createTemplateLiteral, createTransformContext, createVNodeCall, errorMessages, extractIdentifiers, findDir, findProp, forAliasRE, generate, getBaseTransformPreset, getConstantType, getMemoedVNodeCall, getVNodeBlockHelper, getVNodeHelper, hasDynamicKeyVBind, hasScopeRef, helperNameMap, injectProp, isCoreComponent, isFnExpression, isFnExpressionBrowser, isFnExpressionNode, isFunctionType, isInDestructureAssignment, isInNewExpression, isMemberExpression, isMemberExpressionBrowser, isMemberExpressionNode, isReferencedIdentifier, isSimpleIdentifier, isSlotOutlet, isStaticArgOf, isStaticExp, isStaticProperty, isStaticPropertyKey, isTemplateNode, isText$1 as isText, isVSlot, locStub, noopDirectiveTransform, processExpression, processFor, processIf, processSlotOutlet, registerRuntimeHelpers, resolveComponentType, stringifyExpression, toValidAssetId, trackSlotScopes, trackVForSlotScopes, transform, transformBind, transformElement, transformExpression, transformModel, transformOn, traverseNode, unwrapTSNode, walkBlockDeclarations, walkFunctionParams, walkIdentifiers, warnDeprecation };\n", "/**\n* @vue/compiler-dom v3.5.13\n* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors\n* @license MIT\n**/\nimport { registerRuntimeHelpers, createSimpleExpression, createCompilerError, createObjectProperty, getConstantType, createCallExpression, TO_DISPLAY_STRING, transformModel as transformModel$1, findProp, hasDynamicKeyVBind, findDir, isStaticArgOf, transformOn as transformOn$1, isStaticExp, createCompoundExpression, checkCompatEnabled, noopDirectiveTransform, baseCompile, baseParse } from '@vue/compiler-core';\nexport * from '@vue/compiler-core';\nimport { isVoidTag, isHTMLTag, isSVGTag, isMathMLTag, parseStringStyle, capitalize, makeMap, extend } from '@vue/shared';\n\nconst V_MODEL_RADIO = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `vModelRadio` : ``);\nconst V_MODEL_CHECKBOX = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `vModelCheckbox` : ``\n);\nconst V_MODEL_TEXT = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `vModelText` : ``);\nconst V_MODEL_SELECT = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `vModelSelect` : ``\n);\nconst V_MODEL_DYNAMIC = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `vModelDynamic` : ``\n);\nconst V_ON_WITH_MODIFIERS = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `vOnModifiersGuard` : ``\n);\nconst V_ON_WITH_KEYS = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `vOnKeysGuard` : ``\n);\nconst V_SHOW = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `vShow` : ``);\nconst TRANSITION = Symbol(!!(process.env.NODE_ENV !== \"production\") ? `Transition` : ``);\nconst TRANSITION_GROUP = Symbol(\n  !!(process.env.NODE_ENV !== \"production\") ? `TransitionGroup` : ``\n);\nregisterRuntimeHelpers({\n  [V_MODEL_RADIO]: `vModelRadio`,\n  [V_MODEL_CHECKBOX]: `vModelCheckbox`,\n  [V_MODEL_TEXT]: `vModelText`,\n  [V_MODEL_SELECT]: `vModelSelect`,\n  [V_MODEL_DYNAMIC]: `vModelDynamic`,\n  [V_ON_WITH_MODIFIERS]: `withModifiers`,\n  [V_ON_WITH_KEYS]: `withKeys`,\n  [V_SHOW]: `vShow`,\n  [TRANSITION]: `Transition`,\n  [TRANSITION_GROUP]: `TransitionGroup`\n});\n\nlet decoder;\nfunction decodeHtmlBrowser(raw, asAttr = false) {\n  if (!decoder) {\n    decoder = document.createElement(\"div\");\n  }\n  if (asAttr) {\n    decoder.innerHTML = `<div foo=\"${raw.replace(/\"/g, \"&quot;\")}\">`;\n    return decoder.children[0].getAttribute(\"foo\");\n  } else {\n    decoder.innerHTML = raw;\n    return decoder.textContent;\n  }\n}\n\nconst parserOptions = {\n  parseMode: \"html\",\n  isVoidTag,\n  isNativeTag: (tag) => isHTMLTag(tag) || isSVGTag(tag) || isMathMLTag(tag),\n  isPreTag: (tag) => tag === \"pre\",\n  isIgnoreNewlineTag: (tag) => tag === \"pre\" || tag === \"textarea\",\n  decodeEntities: decodeHtmlBrowser ,\n  isBuiltInComponent: (tag) => {\n    if (tag === \"Transition\" || tag === \"transition\") {\n      return TRANSITION;\n    } else if (tag === \"TransitionGroup\" || tag === \"transition-group\") {\n      return TRANSITION_GROUP;\n    }\n  },\n  // https://html.spec.whatwg.org/multipage/parsing.html#tree-construction-dispatcher\n  getNamespace(tag, parent, rootNamespace) {\n    let ns = parent ? parent.ns : rootNamespace;\n    if (parent && ns === 2) {\n      if (parent.tag === \"annotation-xml\") {\n        if (tag === \"svg\") {\n          return 1;\n        }\n        if (parent.props.some(\n          (a) => a.type === 6 && a.name === \"encoding\" && a.value != null && (a.value.content === \"text/html\" || a.value.content === \"application/xhtml+xml\")\n        )) {\n          ns = 0;\n        }\n      } else if (/^m(?:[ions]|text)$/.test(parent.tag) && tag !== \"mglyph\" && tag !== \"malignmark\") {\n        ns = 0;\n      }\n    } else if (parent && ns === 1) {\n      if (parent.tag === \"foreignObject\" || parent.tag === \"desc\" || parent.tag === \"title\") {\n        ns = 0;\n      }\n    }\n    if (ns === 0) {\n      if (tag === \"svg\") {\n        return 1;\n      }\n      if (tag === \"math\") {\n        return 2;\n      }\n    }\n    return ns;\n  }\n};\n\nconst transformStyle = (node) => {\n  if (node.type === 1) {\n    node.props.forEach((p, i) => {\n      if (p.type === 6 && p.name === \"style\" && p.value) {\n        node.props[i] = {\n          type: 7,\n          name: `bind`,\n          arg: createSimpleExpression(`style`, true, p.loc),\n          exp: parseInlineCSS(p.value.content, p.loc),\n          modifiers: [],\n          loc: p.loc\n        };\n      }\n    });\n  }\n};\nconst parseInlineCSS = (cssText, loc) => {\n  const normalized = parseStringStyle(cssText);\n  return createSimpleExpression(\n    JSON.stringify(normalized),\n    false,\n    loc,\n    3\n  );\n};\n\nfunction createDOMCompilerError(code, loc) {\n  return createCompilerError(\n    code,\n    loc,\n    !!(process.env.NODE_ENV !== \"production\") || false ? DOMErrorMessages : void 0\n  );\n}\nconst DOMErrorCodes = {\n  \"X_V_HTML_NO_EXPRESSION\": 53,\n  \"53\": \"X_V_HTML_NO_EXPRESSION\",\n  \"X_V_HTML_WITH_CHILDREN\": 54,\n  \"54\": \"X_V_HTML_WITH_CHILDREN\",\n  \"X_V_TEXT_NO_EXPRESSION\": 55,\n  \"55\": \"X_V_TEXT_NO_EXPRESSION\",\n  \"X_V_TEXT_WITH_CHILDREN\": 56,\n  \"56\": \"X_V_TEXT_WITH_CHILDREN\",\n  \"X_V_MODEL_ON_INVALID_ELEMENT\": 57,\n  \"57\": \"X_V_MODEL_ON_INVALID_ELEMENT\",\n  \"X_V_MODEL_ARG_ON_ELEMENT\": 58,\n  \"58\": \"X_V_MODEL_ARG_ON_ELEMENT\",\n  \"X_V_MODEL_ON_FILE_INPUT_ELEMENT\": 59,\n  \"59\": \"X_V_MODEL_ON_FILE_INPUT_ELEMENT\",\n  \"X_V_MODEL_UNNECESSARY_VALUE\": 60,\n  \"60\": \"X_V_MODEL_UNNECESSARY_VALUE\",\n  \"X_V_SHOW_NO_EXPRESSION\": 61,\n  \"61\": \"X_V_SHOW_NO_EXPRESSION\",\n  \"X_TRANSITION_INVALID_CHILDREN\": 62,\n  \"62\": \"X_TRANSITION_INVALID_CHILDREN\",\n  \"X_IGNORED_SIDE_EFFECT_TAG\": 63,\n  \"63\": \"X_IGNORED_SIDE_EFFECT_TAG\",\n  \"__EXTEND_POINT__\": 64,\n  \"64\": \"__EXTEND_POINT__\"\n};\nconst DOMErrorMessages = {\n  [53]: `v-html is missing expression.`,\n  [54]: `v-html will override element children.`,\n  [55]: `v-text is missing expression.`,\n  [56]: `v-text will override element children.`,\n  [57]: `v-model can only be used on <input>, <textarea> and <select> elements.`,\n  [58]: `v-model argument is not supported on plain elements.`,\n  [59]: `v-model cannot be used on file inputs since they are read-only. Use a v-on:change listener instead.`,\n  [60]: `Unnecessary value binding used alongside v-model. It will interfere with v-model's behavior.`,\n  [61]: `v-show is missing expression.`,\n  [62]: `<Transition> expects exactly one child element or component.`,\n  [63]: `Tags with side effect (<script> and <style>) are ignored in client component templates.`\n};\n\nconst transformVHtml = (dir, node, context) => {\n  const { exp, loc } = dir;\n  if (!exp) {\n    context.onError(\n      createDOMCompilerError(53, loc)\n    );\n  }\n  if (node.children.length) {\n    context.onError(\n      createDOMCompilerError(54, loc)\n    );\n    node.children.length = 0;\n  }\n  return {\n    props: [\n      createObjectProperty(\n        createSimpleExpression(`innerHTML`, true, loc),\n        exp || createSimpleExpression(\"\", true)\n      )\n    ]\n  };\n};\n\nconst transformVText = (dir, node, context) => {\n  const { exp, loc } = dir;\n  if (!exp) {\n    context.onError(\n      createDOMCompilerError(55, loc)\n    );\n  }\n  if (node.children.length) {\n    context.onError(\n      createDOMCompilerError(56, loc)\n    );\n    node.children.length = 0;\n  }\n  return {\n    props: [\n      createObjectProperty(\n        createSimpleExpression(`textContent`, true),\n        exp ? getConstantType(exp, context) > 0 ? exp : createCallExpression(\n          context.helperString(TO_DISPLAY_STRING),\n          [exp],\n          loc\n        ) : createSimpleExpression(\"\", true)\n      )\n    ]\n  };\n};\n\nconst transformModel = (dir, node, context) => {\n  const baseResult = transformModel$1(dir, node, context);\n  if (!baseResult.props.length || node.tagType === 1) {\n    return baseResult;\n  }\n  if (dir.arg) {\n    context.onError(\n      createDOMCompilerError(\n        58,\n        dir.arg.loc\n      )\n    );\n  }\n  function checkDuplicatedValue() {\n    const value = findDir(node, \"bind\");\n    if (value && isStaticArgOf(value.arg, \"value\")) {\n      context.onError(\n        createDOMCompilerError(\n          60,\n          value.loc\n        )\n      );\n    }\n  }\n  const { tag } = node;\n  const isCustomElement = context.isCustomElement(tag);\n  if (tag === \"input\" || tag === \"textarea\" || tag === \"select\" || isCustomElement) {\n    let directiveToUse = V_MODEL_TEXT;\n    let isInvalidType = false;\n    if (tag === \"input\" || isCustomElement) {\n      const type = findProp(node, `type`);\n      if (type) {\n        if (type.type === 7) {\n          directiveToUse = V_MODEL_DYNAMIC;\n        } else if (type.value) {\n          switch (type.value.content) {\n            case \"radio\":\n              directiveToUse = V_MODEL_RADIO;\n              break;\n            case \"checkbox\":\n              directiveToUse = V_MODEL_CHECKBOX;\n              break;\n            case \"file\":\n              isInvalidType = true;\n              context.onError(\n                createDOMCompilerError(\n                  59,\n                  dir.loc\n                )\n              );\n              break;\n            default:\n              !!(process.env.NODE_ENV !== \"production\") && checkDuplicatedValue();\n              break;\n          }\n        }\n      } else if (hasDynamicKeyVBind(node)) {\n        directiveToUse = V_MODEL_DYNAMIC;\n      } else {\n        !!(process.env.NODE_ENV !== \"production\") && checkDuplicatedValue();\n      }\n    } else if (tag === \"select\") {\n      directiveToUse = V_MODEL_SELECT;\n    } else {\n      !!(process.env.NODE_ENV !== \"production\") && checkDuplicatedValue();\n    }\n    if (!isInvalidType) {\n      baseResult.needRuntime = context.helper(directiveToUse);\n    }\n  } else {\n    context.onError(\n      createDOMCompilerError(\n        57,\n        dir.loc\n      )\n    );\n  }\n  baseResult.props = baseResult.props.filter(\n    (p) => !(p.key.type === 4 && p.key.content === \"modelValue\")\n  );\n  return baseResult;\n};\n\nconst isEventOptionModifier = /* @__PURE__ */ makeMap(`passive,once,capture`);\nconst isNonKeyModifier = /* @__PURE__ */ makeMap(\n  // event propagation management\n  `stop,prevent,self,ctrl,shift,alt,meta,exact,middle`\n);\nconst maybeKeyModifier = /* @__PURE__ */ makeMap(\"left,right\");\nconst isKeyboardEvent = /* @__PURE__ */ makeMap(`onkeyup,onkeydown,onkeypress`);\nconst resolveModifiers = (key, modifiers, context, loc) => {\n  const keyModifiers = [];\n  const nonKeyModifiers = [];\n  const eventOptionModifiers = [];\n  for (let i = 0; i < modifiers.length; i++) {\n    const modifier = modifiers[i].content;\n    if (modifier === \"native\" && checkCompatEnabled(\n      \"COMPILER_V_ON_NATIVE\",\n      context,\n      loc\n    )) {\n      eventOptionModifiers.push(modifier);\n    } else if (isEventOptionModifier(modifier)) {\n      eventOptionModifiers.push(modifier);\n    } else {\n      if (maybeKeyModifier(modifier)) {\n        if (isStaticExp(key)) {\n          if (isKeyboardEvent(key.content.toLowerCase())) {\n            keyModifiers.push(modifier);\n          } else {\n            nonKeyModifiers.push(modifier);\n          }\n        } else {\n          keyModifiers.push(modifier);\n          nonKeyModifiers.push(modifier);\n        }\n      } else {\n        if (isNonKeyModifier(modifier)) {\n          nonKeyModifiers.push(modifier);\n        } else {\n          keyModifiers.push(modifier);\n        }\n      }\n    }\n  }\n  return {\n    keyModifiers,\n    nonKeyModifiers,\n    eventOptionModifiers\n  };\n};\nconst transformClick = (key, event) => {\n  const isStaticClick = isStaticExp(key) && key.content.toLowerCase() === \"onclick\";\n  return isStaticClick ? createSimpleExpression(event, true) : key.type !== 4 ? createCompoundExpression([\n    `(`,\n    key,\n    `) === \"onClick\" ? \"${event}\" : (`,\n    key,\n    `)`\n  ]) : key;\n};\nconst transformOn = (dir, node, context) => {\n  return transformOn$1(dir, node, context, (baseResult) => {\n    const { modifiers } = dir;\n    if (!modifiers.length) return baseResult;\n    let { key, value: handlerExp } = baseResult.props[0];\n    const { keyModifiers, nonKeyModifiers, eventOptionModifiers } = resolveModifiers(key, modifiers, context, dir.loc);\n    if (nonKeyModifiers.includes(\"right\")) {\n      key = transformClick(key, `onContextmenu`);\n    }\n    if (nonKeyModifiers.includes(\"middle\")) {\n      key = transformClick(key, `onMouseup`);\n    }\n    if (nonKeyModifiers.length) {\n      handlerExp = createCallExpression(context.helper(V_ON_WITH_MODIFIERS), [\n        handlerExp,\n        JSON.stringify(nonKeyModifiers)\n      ]);\n    }\n    if (keyModifiers.length && // if event name is dynamic, always wrap with keys guard\n    (!isStaticExp(key) || isKeyboardEvent(key.content.toLowerCase()))) {\n      handlerExp = createCallExpression(context.helper(V_ON_WITH_KEYS), [\n        handlerExp,\n        JSON.stringify(keyModifiers)\n      ]);\n    }\n    if (eventOptionModifiers.length) {\n      const modifierPostfix = eventOptionModifiers.map(capitalize).join(\"\");\n      key = isStaticExp(key) ? createSimpleExpression(`${key.content}${modifierPostfix}`, true) : createCompoundExpression([`(`, key, `) + \"${modifierPostfix}\"`]);\n    }\n    return {\n      props: [createObjectProperty(key, handlerExp)]\n    };\n  });\n};\n\nconst transformShow = (dir, node, context) => {\n  const { exp, loc } = dir;\n  if (!exp) {\n    context.onError(\n      createDOMCompilerError(61, loc)\n    );\n  }\n  return {\n    props: [],\n    needRuntime: context.helper(V_SHOW)\n  };\n};\n\nconst transformTransition = (node, context) => {\n  if (node.type === 1 && node.tagType === 1) {\n    const component = context.isBuiltInComponent(node.tag);\n    if (component === TRANSITION) {\n      return () => {\n        if (!node.children.length) {\n          return;\n        }\n        if (hasMultipleChildren(node)) {\n          context.onError(\n            createDOMCompilerError(\n              62,\n              {\n                start: node.children[0].loc.start,\n                end: node.children[node.children.length - 1].loc.end,\n                source: \"\"\n              }\n            )\n          );\n        }\n        const child = node.children[0];\n        if (child.type === 1) {\n          for (const p of child.props) {\n            if (p.type === 7 && p.name === \"show\") {\n              node.props.push({\n                type: 6,\n                name: \"persisted\",\n                nameLoc: node.loc,\n                value: void 0,\n                loc: node.loc\n              });\n            }\n          }\n        }\n      };\n    }\n  }\n};\nfunction hasMultipleChildren(node) {\n  const children = node.children = node.children.filter(\n    (c) => c.type !== 3 && !(c.type === 2 && !c.content.trim())\n  );\n  const child = children[0];\n  return children.length !== 1 || child.type === 11 || child.type === 9 && child.branches.some(hasMultipleChildren);\n}\n\nconst ignoreSideEffectTags = (node, context) => {\n  if (node.type === 1 && node.tagType === 0 && (node.tag === \"script\" || node.tag === \"style\")) {\n    !!(process.env.NODE_ENV !== \"production\") && context.onError(\n      createDOMCompilerError(\n        63,\n        node.loc\n      )\n    );\n    context.removeNode();\n  }\n};\n\nfunction isValidHTMLNesting(parent, child) {\n  if (parent in onlyValidChildren) {\n    return onlyValidChildren[parent].has(child);\n  }\n  if (child in onlyValidParents) {\n    return onlyValidParents[child].has(parent);\n  }\n  if (parent in knownInvalidChildren) {\n    if (knownInvalidChildren[parent].has(child)) return false;\n  }\n  if (child in knownInvalidParents) {\n    if (knownInvalidParents[child].has(parent)) return false;\n  }\n  return true;\n}\nconst headings = /* @__PURE__ */ new Set([\"h1\", \"h2\", \"h3\", \"h4\", \"h5\", \"h6\"]);\nconst emptySet = /* @__PURE__ */ new Set([]);\nconst onlyValidChildren = {\n  head: /* @__PURE__ */ new Set([\n    \"base\",\n    \"basefront\",\n    \"bgsound\",\n    \"link\",\n    \"meta\",\n    \"title\",\n    \"noscript\",\n    \"noframes\",\n    \"style\",\n    \"script\",\n    \"template\"\n  ]),\n  optgroup: /* @__PURE__ */ new Set([\"option\"]),\n  select: /* @__PURE__ */ new Set([\"optgroup\", \"option\", \"hr\"]),\n  // table\n  table: /* @__PURE__ */ new Set([\"caption\", \"colgroup\", \"tbody\", \"tfoot\", \"thead\"]),\n  tr: /* @__PURE__ */ new Set([\"td\", \"th\"]),\n  colgroup: /* @__PURE__ */ new Set([\"col\"]),\n  tbody: /* @__PURE__ */ new Set([\"tr\"]),\n  thead: /* @__PURE__ */ new Set([\"tr\"]),\n  tfoot: /* @__PURE__ */ new Set([\"tr\"]),\n  // these elements can not have any children elements\n  script: emptySet,\n  iframe: emptySet,\n  option: emptySet,\n  textarea: emptySet,\n  style: emptySet,\n  title: emptySet\n};\nconst onlyValidParents = {\n  // sections\n  html: emptySet,\n  body: /* @__PURE__ */ new Set([\"html\"]),\n  head: /* @__PURE__ */ new Set([\"html\"]),\n  // table\n  td: /* @__PURE__ */ new Set([\"tr\"]),\n  colgroup: /* @__PURE__ */ new Set([\"table\"]),\n  caption: /* @__PURE__ */ new Set([\"table\"]),\n  tbody: /* @__PURE__ */ new Set([\"table\"]),\n  tfoot: /* @__PURE__ */ new Set([\"table\"]),\n  col: /* @__PURE__ */ new Set([\"colgroup\"]),\n  th: /* @__PURE__ */ new Set([\"tr\"]),\n  thead: /* @__PURE__ */ new Set([\"table\"]),\n  tr: /* @__PURE__ */ new Set([\"tbody\", \"thead\", \"tfoot\"]),\n  // data list\n  dd: /* @__PURE__ */ new Set([\"dl\", \"div\"]),\n  dt: /* @__PURE__ */ new Set([\"dl\", \"div\"]),\n  // other\n  figcaption: /* @__PURE__ */ new Set([\"figure\"]),\n  // li: new Set([\"ul\", \"ol\"]),\n  summary: /* @__PURE__ */ new Set([\"details\"]),\n  area: /* @__PURE__ */ new Set([\"map\"])\n};\nconst knownInvalidChildren = {\n  p: /* @__PURE__ */ new Set([\n    \"address\",\n    \"article\",\n    \"aside\",\n    \"blockquote\",\n    \"center\",\n    \"details\",\n    \"dialog\",\n    \"dir\",\n    \"div\",\n    \"dl\",\n    \"fieldset\",\n    \"figure\",\n    \"footer\",\n    \"form\",\n    \"h1\",\n    \"h2\",\n    \"h3\",\n    \"h4\",\n    \"h5\",\n    \"h6\",\n    \"header\",\n    \"hgroup\",\n    \"hr\",\n    \"li\",\n    \"main\",\n    \"nav\",\n    \"menu\",\n    \"ol\",\n    \"p\",\n    \"pre\",\n    \"section\",\n    \"table\",\n    \"ul\"\n  ]),\n  svg: /* @__PURE__ */ new Set([\n    \"b\",\n    \"blockquote\",\n    \"br\",\n    \"code\",\n    \"dd\",\n    \"div\",\n    \"dl\",\n    \"dt\",\n    \"em\",\n    \"embed\",\n    \"h1\",\n    \"h2\",\n    \"h3\",\n    \"h4\",\n    \"h5\",\n    \"h6\",\n    \"hr\",\n    \"i\",\n    \"img\",\n    \"li\",\n    \"menu\",\n    \"meta\",\n    \"ol\",\n    \"p\",\n    \"pre\",\n    \"ruby\",\n    \"s\",\n    \"small\",\n    \"span\",\n    \"strong\",\n    \"sub\",\n    \"sup\",\n    \"table\",\n    \"u\",\n    \"ul\",\n    \"var\"\n  ])\n};\nconst knownInvalidParents = {\n  a: /* @__PURE__ */ new Set([\"a\"]),\n  button: /* @__PURE__ */ new Set([\"button\"]),\n  dd: /* @__PURE__ */ new Set([\"dd\", \"dt\"]),\n  dt: /* @__PURE__ */ new Set([\"dd\", \"dt\"]),\n  form: /* @__PURE__ */ new Set([\"form\"]),\n  li: /* @__PURE__ */ new Set([\"li\"]),\n  h1: headings,\n  h2: headings,\n  h3: headings,\n  h4: headings,\n  h5: headings,\n  h6: headings\n};\n\nconst validateHtmlNesting = (node, context) => {\n  if (node.type === 1 && node.tagType === 0 && context.parent && context.parent.type === 1 && context.parent.tagType === 0 && !isValidHTMLNesting(context.parent.tag, node.tag)) {\n    const error = new SyntaxError(\n      `<${node.tag}> cannot be child of <${context.parent.tag}>, according to HTML specifications. This can cause hydration errors or potentially disrupt future functionality.`\n    );\n    error.loc = node.loc;\n    context.onWarn(error);\n  }\n};\n\nconst DOMNodeTransforms = [\n  transformStyle,\n  ...!!(process.env.NODE_ENV !== \"production\") ? [transformTransition, validateHtmlNesting] : []\n];\nconst DOMDirectiveTransforms = {\n  cloak: noopDirectiveTransform,\n  html: transformVHtml,\n  text: transformVText,\n  model: transformModel,\n  // override compiler-core\n  on: transformOn,\n  // override compiler-core\n  show: transformShow\n};\nfunction compile(src, options = {}) {\n  return baseCompile(\n    src,\n    extend({}, parserOptions, options, {\n      nodeTransforms: [\n        // ignore <script> and <tag>\n        // this is not put inside DOMNodeTransforms because that list is used\n        // by compiler-ssr to generate vnode fallback branches\n        ignoreSideEffectTags,\n        ...DOMNodeTransforms,\n        ...options.nodeTransforms || []\n      ],\n      directiveTransforms: extend(\n        {},\n        DOMDirectiveTransforms,\n        options.directiveTransforms || {}\n      ),\n      transformHoist: null \n    })\n  );\n}\nfunction parse(template, options = {}) {\n  return baseParse(template, extend({}, parserOptions, options));\n}\n\nexport { DOMDirectiveTransforms, DOMErrorCodes, DOMErrorMessages, DOMNodeTransforms, TRANSITION, TRANSITION_GROUP, V_MODEL_CHECKBOX, V_MODEL_DYNAMIC, V_MODEL_RADIO, V_MODEL_SELECT, V_MODEL_TEXT, V_ON_WITH_KEYS, V_ON_WITH_MODIFIERS, V_SHOW, compile, createDOMCompilerError, parse, parserOptions, transformStyle };\n", "/**\n* vue v3.5.13\n* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors\n* @license MIT\n**/\n'use strict';\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\nvar compilerDom = require('@vue/compiler-dom');\nvar runtimeDom = require('@vue/runtime-dom');\nvar shared = require('@vue/shared');\n\nfunction _interopNamespaceDefault(e) {\n  var n = Object.create(null);\n  if (e) {\n    for (var k in e) {\n      n[k] = e[k];\n    }\n  }\n  n.default = e;\n  return Object.freeze(n);\n}\n\nvar runtimeDom__namespace = /*#__PURE__*/_interopNamespaceDefault(runtimeDom);\n\nconst compileCache = /* @__PURE__ */ Object.create(null);\nfunction compileToFunction(template, options) {\n  if (!shared.isString(template)) {\n    if (template.nodeType) {\n      template = template.innerHTML;\n    } else {\n      runtimeDom.warn(`invalid template option: `, template);\n      return shared.NOOP;\n    }\n  }\n  const key = shared.genCacheKey(template, options);\n  const cached = compileCache[key];\n  if (cached) {\n    return cached;\n  }\n  if (template[0] === \"#\") {\n    const el = document.querySelector(template);\n    if (!el) {\n      runtimeDom.warn(`Template element not found or is empty: ${template}`);\n    }\n    template = el ? el.innerHTML : ``;\n  }\n  const opts = shared.extend(\n    {\n      hoistStatic: true,\n      onError: onError ,\n      onWarn: (e) => onError(e, true) \n    },\n    options\n  );\n  if (!opts.isCustomElement && typeof customElements !== \"undefined\") {\n    opts.isCustomElement = (tag) => !!customElements.get(tag);\n  }\n  const { code } = compilerDom.compile(template, opts);\n  function onError(err, asWarning = false) {\n    const message = asWarning ? err.message : `Template compilation error: ${err.message}`;\n    const codeFrame = err.loc && shared.generateCodeFrame(\n      template,\n      err.loc.start.offset,\n      err.loc.end.offset\n    );\n    runtimeDom.warn(codeFrame ? `${message}\n${codeFrame}` : message);\n  }\n  const render = new Function(\"Vue\", code)(runtimeDom__namespace);\n  render._rc = true;\n  return compileCache[key] = render;\n}\nruntimeDom.registerRuntimeCompiler(compileToFunction);\n\nexports.compile = compileToFunction;\nObject.keys(runtimeDom).forEach(function (k) {\n  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) exports[k] = runtimeDom[k];\n});\n", "'use strict'\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./dist/vue.cjs.prod.js')\n} else {\n  module.exports = require('./dist/vue.cjs.js')\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4HA,SAAS,uBAAuB,SAAS;AACvC,SAAO,sBAAsB,OAAO,EAAE,QAAQ,CAAC,MAAM;AACnD,kBAAc,CAAC,IAAI,QAAQ,CAAC;AAAA,EAC9B,CAAC;AACH;AA2FA,SAAS,WAAW,UAAU,SAAS,IAAI;AACzC,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA,SAAyB,oBAAI,IAAI;AAAA,IACjC,YAAY,CAAC;AAAA,IACb,YAAY,CAAC;AAAA,IACb,QAAQ,CAAC;AAAA,IACT,SAAS,CAAC;AAAA,IACV,QAAQ,CAAC;AAAA,IACT,OAAO;AAAA,IACP,aAAa;AAAA,IACb,KAAK;AAAA,EACP;AACF;AACA,SAAS,gBAAgB,SAAS,KAAK,OAAO,UAAU,WAAW,cAAc,YAAY,UAAU,OAAO,kBAAkB,OAAOA,eAAc,OAAO,MAAM,SAAS;AACzK,MAAI,SAAS;AACX,QAAI,SAAS;AACX,cAAQ,OAAO,UAAU;AACzB,cAAQ,OAAO,oBAAoB,QAAQ,OAAOA,YAAW,CAAC;AAAA,IAChE,OAAO;AACL,cAAQ,OAAO,eAAe,QAAQ,OAAOA,YAAW,CAAC;AAAA,IAC3D;AACA,QAAI,YAAY;AACd,cAAQ,OAAO,eAAe;AAAA,IAChC;AAAA,EACF;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAAA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,sBAAsB,UAAU,MAAM,SAAS;AACtD,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,uBAAuB,YAAY,MAAM,SAAS;AACzD,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,qBAAqB,KAAK,OAAO;AACxC,SAAO;AAAA,IACL,MAAM;AAAA,IACN,KAAK;AAAA,IACL,KAAK,SAAS,GAAG,IAAI,uBAAuB,KAAK,IAAI,IAAI;AAAA,IACzD;AAAA,EACF;AACF;AACA,SAAS,uBAAuB,SAAS,WAAW,OAAO,MAAM,SAAS,YAAY,GAAG;AACvF,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW,WAAW,IAAI;AAAA,EAC5B;AACF;AACA,SAAS,oBAAoB,SAAS,KAAK;AACzC,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,SAAS,SAAS,OAAO,IAAI,uBAAuB,SAAS,OAAO,GAAG,IAAI;AAAA,EAC7E;AACF;AACA,SAAS,yBAAyB,UAAU,MAAM,SAAS;AACzD,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,qBAAqB,QAAQ,OAAO,CAAC,GAAG,MAAM,SAAS;AAC9D,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA,WAAW;AAAA,EACb;AACF;AACA,SAAS,yBAAyB,QAAQ,UAAU,QAAQ,UAAU,OAAO,SAAS,OAAO,MAAM,SAAS;AAC1G,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,4BAA4B,MAAM,YAAY,WAAW,UAAU,MAAM;AAChF,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK;AAAA,EACP;AACF;AACA,SAAS,sBAAsB,OAAO,OAAO,oBAAoB,OAAO,UAAU,OAAO;AACvF,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,iBAAiB;AAAA,IACjB,KAAK;AAAA,EACP;AACF;AACA,SAAS,qBAAqB,MAAM;AAClC,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,KAAK;AAAA,EACP;AACF;AACA,SAAS,sBAAsB,UAAU;AACvC,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,KAAK;AAAA,EACP;AACF;AACA,SAAS,kBAAkB,MAAM,YAAY,WAAW;AACtD,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK;AAAA,EACP;AACF;AACA,SAAS,2BAA2B,MAAM,OAAO;AAC/C,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA,KAAK;AAAA,EACP;AACF;AACA,SAAS,yBAAyB,aAAa;AAC7C,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,KAAK;AAAA,EACP;AACF;AACA,SAAS,sBAAsB,SAAS;AACtC,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,KAAK;AAAA,EACP;AACF;AACA,SAAS,eAAe,KAAKA,cAAa;AACxC,SAAO,OAAOA,eAAc,eAAe;AAC7C;AACA,SAAS,oBAAoB,KAAKA,cAAa;AAC7C,SAAO,OAAOA,eAAc,eAAe;AAC7C;AACA,SAAS,eAAe,MAAM,EAAE,QAAQ,cAAc,MAAM,GAAG;AAC7D,MAAI,CAAC,KAAK,SAAS;AACjB,SAAK,UAAU;AACf,iBAAa,eAAe,OAAO,KAAK,WAAW,CAAC;AACpD,WAAO,UAAU;AACjB,WAAO,oBAAoB,OAAO,KAAK,WAAW,CAAC;AAAA,EACrD;AACF;AAIA,SAAS,eAAe,GAAG;AACzB,SAAO,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,KAAK;AAChD;AACA,SAAS,aAAa,GAAG;AACvB,SAAO,MAAM,MAAM,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,MAAM;AAC9D;AACA,SAAS,kBAAkB,GAAG;AAC5B,SAAO,MAAM,MAAM,MAAM,MAAM,aAAa,CAAC;AAC/C;AACA,SAAS,YAAY,KAAK;AACxB,QAAM,MAAM,IAAI,WAAW,IAAI,MAAM;AACrC,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,QAAI,CAAC,IAAI,IAAI,WAAW,CAAC;AAAA,EAC3B;AACA,SAAO;AACT;AA8zBA,SAAS,eAAe,KAAK,EAAE,aAAa,GAAG;AAC7C,QAAM,QAAQ,gBAAgB,aAAa,GAAG;AAC9C,MAAI,QAAQ,QAAQ;AAClB,WAAO,SAAS;AAAA,EAClB,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,SAAS,gBAAgB,KAAK,SAAS;AACrC,QAAM,OAAO,eAAe,QAAQ,OAAO;AAC3C,QAAM,QAAQ,eAAe,KAAK,OAAO;AACzC,SAAO,SAAS,IAAI,UAAU,OAAO,UAAU;AACjD;AACA,SAAS,mBAAmB,KAAK,SAAS,QAAQ,MAAM;AACtD,QAAM,UAAU,gBAAgB,KAAK,OAAO;AAC5C,MAAiD,SAAS;AACxD,oBAAgB,KAAK,SAAS,KAAK,GAAG,IAAI;AAAA,EAC5C;AACA,SAAO;AACT;AACA,SAAS,gBAAgB,KAAK,SAAS,QAAQ,MAAM;AACnD,QAAM,MAAM,eAAe,KAAK,OAAO;AACvC,MAAI,QAAQ,oBAAoB;AAC9B;AAAA,EACF;AACA,QAAM,EAAE,SAAS,KAAK,IAAI,gBAAgB,GAAG;AAC7C,QAAM,MAAM,gBAAgB,GAAG,KAAK,OAAO,YAAY,aAAa,QAAQ,GAAG,IAAI,IAAI,OAAO,GAAG,OAAO;AAAA,aAC7F,IAAI,KAAK,EAAE;AACtB,QAAM,MAAM,IAAI,YAAY,GAAG;AAC/B,MAAI,OAAO;AACX,MAAI,IAAK,KAAI,MAAM;AACnB,UAAQ,OAAO,GAAG;AACpB;AAEA,SAAS,eAAe,OAAO;AAC7B,QAAM;AACR;AACA,SAAS,cAAc,KAAK;AAC1B,EAA6C,QAAQ,KAAK,cAAc,IAAI,OAAO,EAAE;AACvF;AACA,SAAS,oBAAoB,MAAM,KAAK,UAAU,mBAAmB;AACnE,QAAM,MAAM,QAAsD,YAAY,eAAe,IAAI,KAAK,qBAAqB,MAAM,+CAA+C,IAAI;AACpL,QAAM,QAAQ,IAAI,YAAY,OAAO,GAAG,CAAC;AACzC,QAAM,OAAO;AACb,QAAM,MAAM;AACZ,SAAO;AACT;AA8KA,SAAS,gBAAgB,MAAM,cAAc,aAAa,OAAO,cAAc,CAAC,GAAG,WAA2B,uBAAO,OAAO,IAAI,GAAG;AACjI;AACE;AAAA,EACF;AACF;AACA,SAAS,uBAAuB,IAAI,QAAQ,aAAa;AACvD;AACE,WAAO;AAAA,EACT;AACF;AACA,SAAS,0BAA0B,QAAQ,aAAa;AACtD,MAAI,WAAW,OAAO,SAAS,oBAAoB,OAAO,SAAS,iBAAiB;AAClF,QAAI,IAAI,YAAY;AACpB,WAAO,KAAK;AACV,YAAM,IAAI,YAAY,CAAC;AACvB,UAAI,EAAE,SAAS,wBAAwB;AACrC,eAAO;AAAA,MACT,WAAW,EAAE,SAAS,oBAAoB,CAAC,EAAE,KAAK,SAAS,SAAS,GAAG;AACrE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,aAAa;AACtC,MAAI,IAAI,YAAY;AACpB,SAAO,KAAK;AACV,UAAM,IAAI,YAAY,CAAC;AACvB,QAAI,EAAE,SAAS,iBAAiB;AAC9B,aAAO;AAAA,IACT,WAAW,EAAE,SAAS,oBAAoB;AACxC;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,MAAM,SAAS;AACzC,aAAW,KAAK,KAAK,QAAQ;AAC3B,eAAW,MAAM,mBAAmB,CAAC,GAAG;AACtC,cAAQ,EAAE;AAAA,IACZ;AAAA,EACF;AACF;AACA,SAAS,sBAAsB,OAAO,SAAS;AAC7C,aAAW,QAAQ,MAAM,MAAM;AAC7B,QAAI,KAAK,SAAS,uBAAuB;AACvC,UAAI,KAAK,QAAS;AAClB,iBAAW,QAAQ,KAAK,cAAc;AACpC,mBAAW,MAAM,mBAAmB,KAAK,EAAE,GAAG;AAC5C,kBAAQ,EAAE;AAAA,QACZ;AAAA,MACF;AAAA,IACF,WAAW,KAAK,SAAS,yBAAyB,KAAK,SAAS,oBAAoB;AAClF,UAAI,KAAK,WAAW,CAAC,KAAK,GAAI;AAC9B,cAAQ,KAAK,EAAE;AAAA,IACjB,WAAW,eAAe,IAAI,GAAG;AAC/B,uBAAiB,MAAM,MAAM,OAAO;AAAA,IACtC;AAAA,EACF;AACF;AACA,SAAS,eAAe,MAAM;AAC5B,SAAO,KAAK,SAAS,oBAAoB,KAAK,SAAS,oBAAoB,KAAK,SAAS;AAC3F;AACA,SAAS,iBAAiB,MAAM,OAAO,SAAS;AAC9C,QAAM,WAAW,KAAK,SAAS,iBAAiB,KAAK,OAAO,KAAK;AACjE,MAAI,YAAY,SAAS,SAAS,0BAA0B,SAAS,SAAS,QAAQ,QAAQ,CAAC,QAAQ;AACrG,eAAW,QAAQ,SAAS,cAAc;AACxC,iBAAW,MAAM,mBAAmB,KAAK,EAAE,GAAG;AAC5C,gBAAQ,EAAE;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,mBAAmB,OAAO,QAAQ,CAAC,GAAG;AAC7C,UAAQ,MAAM,MAAM;AAAA,IAClB,KAAK;AACH,YAAM,KAAK,KAAK;AAChB;AAAA,IACF,KAAK;AACH,UAAI,SAAS;AACb,aAAO,OAAO,SAAS,oBAAoB;AACzC,iBAAS,OAAO;AAAA,MAClB;AACA,YAAM,KAAK,MAAM;AACjB;AAAA,IACF,KAAK;AACH,iBAAW,QAAQ,MAAM,YAAY;AACnC,YAAI,KAAK,SAAS,eAAe;AAC/B,6BAAmB,KAAK,UAAU,KAAK;AAAA,QACzC,OAAO;AACL,6BAAmB,KAAK,OAAO,KAAK;AAAA,QACtC;AAAA,MACF;AACA;AAAA,IACF,KAAK;AACH,YAAM,SAAS,QAAQ,CAAC,YAAY;AAClC,YAAI,QAAS,oBAAmB,SAAS,KAAK;AAAA,MAChD,CAAC;AACD;AAAA,IACF,KAAK;AACH,yBAAmB,MAAM,UAAU,KAAK;AACxC;AAAA,IACF,KAAK;AACH,yBAAmB,MAAM,MAAM,KAAK;AACpC;AAAA,EACJ;AACA,SAAO;AACT;AAkBA,SAAS,aAAa,MAAM;AAC1B,MAAI,cAAc,SAAS,KAAK,IAAI,GAAG;AACrC,WAAO,aAAa,KAAK,UAAU;AAAA,EACrC,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAGA,SAAS,gBAAgB,KAAK;AAC5B,UAAQ,KAAK;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,EACX;AACF;AA2EA,SAAS,yBAAyB,KAAK,QAAQ,qBAAqB,OAAO,QAAQ;AACjF,SAAO;AAAA,IACL;AAAA,MACE,QAAQ,IAAI;AAAA,MACZ,MAAM,IAAI;AAAA,MACV,QAAQ,IAAI;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,4BAA4B,KAAK,QAAQ,qBAAqB,OAAO,QAAQ;AACpF,MAAI,aAAa;AACjB,MAAI,iBAAiB;AACrB,WAAS,IAAI,GAAG,IAAI,oBAAoB,KAAK;AAC3C,QAAI,OAAO,WAAW,CAAC,MAAM,IAAI;AAC/B;AACA,uBAAiB;AAAA,IACnB;AAAA,EACF;AACA,MAAI,UAAU;AACd,MAAI,QAAQ;AACZ,MAAI,SAAS,mBAAmB,KAAK,IAAI,SAAS,qBAAqB,qBAAqB;AAC5F,SAAO;AACT;AACA,SAAS,OAAO,WAAW,KAAK;AAC9B,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,OAAO,+BAA+B;AAAA,EACxD;AACF;AACA,SAAS,QAAQ,MAAM,MAAM,aAAa,OAAO;AAC/C,WAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,UAAM,IAAI,KAAK,MAAM,CAAC;AACtB,QAAI,EAAE,SAAS,MAAM,cAAc,EAAE,SAAS,SAAS,IAAI,IAAI,EAAE,SAAS,OAAO,KAAK,KAAK,EAAE,IAAI,IAAI;AACnG,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,SAAS,SAAS,MAAM,MAAM,cAAc,OAAO,aAAa,OAAO;AACrE,WAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,UAAM,IAAI,KAAK,MAAM,CAAC;AACtB,QAAI,EAAE,SAAS,GAAG;AAChB,UAAI,YAAa;AACjB,UAAI,EAAE,SAAS,SAAS,EAAE,SAAS,aAAa;AAC9C,eAAO;AAAA,MACT;AAAA,IACF,WAAW,EAAE,SAAS,WAAW,EAAE,OAAO,eAAe,cAAc,EAAE,KAAK,IAAI,GAAG;AACnF,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,SAAS,cAAc,KAAK,MAAM;AAChC,SAAO,CAAC,EAAE,OAAO,YAAY,GAAG,KAAK,IAAI,YAAY;AACvD;AACA,SAAS,mBAAmB,MAAM;AAChC,SAAO,KAAK,MAAM;AAAA,IAChB,CAAC,MAAM,EAAE,SAAS,KAAK,EAAE,SAAS,WAAW,CAAC,EAAE;AAAA,IAChD,EAAE,IAAI,SAAS;AAAA,IACf,CAAC,EAAE,IAAI;AAAA;AAAA,EAET;AACF;AACA,SAAS,SAAS,MAAM;AACtB,SAAO,KAAK,SAAS,KAAK,KAAK,SAAS;AAC1C;AACA,SAAS,QAAQ,GAAG;AAClB,SAAO,EAAE,SAAS,KAAK,EAAE,SAAS;AACpC;AACA,SAAS,eAAe,MAAM;AAC5B,SAAO,KAAK,SAAS,KAAK,KAAK,YAAY;AAC7C;AACA,SAAS,aAAa,MAAM;AAC1B,SAAO,KAAK,SAAS,KAAK,KAAK,YAAY;AAC7C;AAEA,SAAS,qBAAqB,OAAO,WAAW,CAAC,GAAG;AAClD,MAAI,SAAS,CAAC,SAAS,KAAK,KAAK,MAAM,SAAS,IAAI;AAClD,UAAM,SAAS,MAAM;AACrB,QAAI,CAAC,SAAS,MAAM,KAAK,eAAe,IAAI,MAAM,GAAG;AACnD,aAAO;AAAA,QACL,MAAM,UAAU,CAAC;AAAA,QACjB,SAAS,OAAO,KAAK;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AACA,SAAO,CAAC,OAAO,QAAQ;AACzB;AACA,SAAS,WAAW,MAAM,MAAM,SAAS;AACvC,MAAI;AACJ,MAAI,QAAQ,KAAK,SAAS,KAAK,KAAK,QAAQ,KAAK,UAAU,CAAC;AAC5D,MAAI,WAAW,CAAC;AAChB,MAAI;AACJ,MAAI,SAAS,CAAC,SAAS,KAAK,KAAK,MAAM,SAAS,IAAI;AAClD,UAAM,MAAM,qBAAqB,KAAK;AACtC,YAAQ,IAAI,CAAC;AACb,eAAW,IAAI,CAAC;AAChB,iBAAa,SAAS,SAAS,SAAS,CAAC;AAAA,EAC3C;AACA,MAAI,SAAS,QAAQ,SAAS,KAAK,GAAG;AACpC,yBAAqB,uBAAuB,CAAC,IAAI,CAAC;AAAA,EACpD,WAAW,MAAM,SAAS,IAAI;AAC5B,UAAM,QAAQ,MAAM,UAAU,CAAC;AAC/B,QAAI,CAAC,SAAS,KAAK,KAAK,MAAM,SAAS,IAAI;AACzC,UAAI,CAAC,QAAQ,MAAM,KAAK,GAAG;AACzB,cAAM,WAAW,QAAQ,IAAI;AAAA,MAC/B;AAAA,IACF,OAAO;AACL,UAAI,MAAM,WAAW,aAAa;AAChC,6BAAqB,qBAAqB,QAAQ,OAAO,WAAW,GAAG;AAAA,UACrE,uBAAuB,CAAC,IAAI,CAAC;AAAA,UAC7B;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,cAAM,UAAU,QAAQ,uBAAuB,CAAC,IAAI,CAAC,CAAC;AAAA,MACxD;AAAA,IACF;AACA,KAAC,uBAAuB,qBAAqB;AAAA,EAC/C,WAAW,MAAM,SAAS,IAAI;AAC5B,QAAI,CAAC,QAAQ,MAAM,KAAK,GAAG;AACzB,YAAM,WAAW,QAAQ,IAAI;AAAA,IAC/B;AACA,yBAAqB;AAAA,EACvB,OAAO;AACL,yBAAqB,qBAAqB,QAAQ,OAAO,WAAW,GAAG;AAAA,MACrE,uBAAuB,CAAC,IAAI,CAAC;AAAA,MAC7B;AAAA,IACF,CAAC;AACD,QAAI,cAAc,WAAW,WAAW,sBAAsB;AAC5D,mBAAa,SAAS,SAAS,SAAS,CAAC;AAAA,IAC3C;AAAA,EACF;AACA,MAAI,KAAK,SAAS,IAAI;AACpB,QAAI,YAAY;AACd,iBAAW,UAAU,CAAC,IAAI;AAAA,IAC5B,OAAO;AACL,WAAK,QAAQ;AAAA,IACf;AAAA,EACF,OAAO;AACL,QAAI,YAAY;AACd,iBAAW,UAAU,CAAC,IAAI;AAAA,IAC5B,OAAO;AACL,WAAK,UAAU,CAAC,IAAI;AAAA,IACtB;AAAA,EACF;AACF;AACA,SAAS,QAAQ,MAAM,OAAO;AAC5B,MAAI,SAAS;AACb,MAAI,KAAK,IAAI,SAAS,GAAG;AACvB,UAAM,cAAc,KAAK,IAAI;AAC7B,aAAS,MAAM,WAAW;AAAA,MACxB,CAAC,MAAM,EAAE,IAAI,SAAS,KAAK,EAAE,IAAI,YAAY;AAAA,IAC/C;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,eAAe,MAAM,MAAM;AAClC,SAAO,IAAI,IAAI,IAAI,KAAK,QAAQ,UAAU,CAAC,aAAa,iBAAiB;AACvE,WAAO,gBAAgB,MAAM,MAAM,KAAK,WAAW,YAAY,EAAE,SAAS;AAAA,EAC5E,CAAC,CAAC;AACJ;AACA,SAAS,YAAY,MAAM,KAAK;AAC9B,MAAI,CAAC,QAAQ,OAAO,KAAK,GAAG,EAAE,WAAW,GAAG;AAC1C,WAAO;AAAA,EACT;AACA,UAAQ,KAAK,MAAM;AAAA,IACjB,KAAK;AACH,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,cAAM,IAAI,KAAK,MAAM,CAAC;AACtB,YAAI,EAAE,SAAS,MAAM,YAAY,EAAE,KAAK,GAAG,KAAK,YAAY,EAAE,KAAK,GAAG,IAAI;AACxE,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO,KAAK,SAAS,KAAK,CAAC,MAAM,YAAY,GAAG,GAAG,CAAC;AAAA,IACtD,KAAK;AACH,UAAI,YAAY,KAAK,QAAQ,GAAG,GAAG;AACjC,eAAO;AAAA,MACT;AACA,aAAO,KAAK,SAAS,KAAK,CAAC,MAAM,YAAY,GAAG,GAAG,CAAC;AAAA,IACtD,KAAK;AACH,aAAO,KAAK,SAAS,KAAK,CAAC,MAAM,YAAY,GAAG,GAAG,CAAC;AAAA,IACtD,KAAK;AACH,UAAI,YAAY,KAAK,WAAW,GAAG,GAAG;AACpC,eAAO;AAAA,MACT;AACA,aAAO,KAAK,SAAS,KAAK,CAAC,MAAM,YAAY,GAAG,GAAG,CAAC;AAAA,IACtD,KAAK;AACH,aAAO,CAAC,KAAK,YAAY,mBAAmB,KAAK,OAAO,KAAK,CAAC,CAAC,IAAI,KAAK,OAAO;AAAA,IACjF,KAAK;AACH,aAAO,KAAK,SAAS,KAAK,CAAC,MAAM,SAAS,CAAC,KAAK,YAAY,GAAG,GAAG,CAAC;AAAA,IACrE,KAAK;AAAA,IACL,KAAK;AACH,aAAO,YAAY,KAAK,SAAS,GAAG;AAAA,IACtC,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT;AACE,UAAI,KAA2C;AAC/C,aAAO;AAAA,EACX;AACF;AACA,SAAS,mBAAmB,MAAM;AAChC,MAAI,KAAK,SAAS,MAAM,KAAK,WAAW,WAAW;AACjD,WAAO,KAAK,UAAU,CAAC,EAAE;AAAA,EAC3B,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAoVA,SAAS,mBAAmB,OAAO;AACjC,QAAM,MAAM,MAAM;AAClB,QAAM,MAAM,MAAM;AAClB,QAAM,UAAU,IAAI,MAAM,UAAU;AACpC,MAAI,CAAC,QAAS;AACd,QAAM,CAAC,EAAE,KAAK,GAAG,IAAI;AACrB,QAAM,wBAAwB,CAAC,SAAS,QAAQ,UAAU,UAAU;AAClE,UAAM,QAAQ,IAAI,MAAM,SAAS;AACjC,UAAM,MAAM,QAAQ,QAAQ;AAC5B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,OAAO,OAAO,GAAG;AAAA,MACjB;AAAA,MACA,UAAU,IAAiB;AAAA;AAAA,IAC7B;AAAA,EACF;AACA,QAAM,SAAS;AAAA,IACb,QAAQ,sBAAsB,IAAI,KAAK,GAAG,IAAI,QAAQ,KAAK,IAAI,MAAM,CAAC;AAAA,IACtE,OAAO;AAAA,IACP,KAAK;AAAA,IACL,OAAO;AAAA,IACP,WAAW;AAAA,EACb;AACA,MAAI,eAAe,IAAI,KAAK,EAAE,QAAQ,eAAe,EAAE,EAAE,KAAK;AAC9D,QAAM,gBAAgB,IAAI,QAAQ,YAAY;AAC9C,QAAM,gBAAgB,aAAa,MAAM,aAAa;AACtD,MAAI,eAAe;AACjB,mBAAe,aAAa,QAAQ,eAAe,EAAE,EAAE,KAAK;AAC5D,UAAM,aAAa,cAAc,CAAC,EAAE,KAAK;AACzC,QAAI;AACJ,QAAI,YAAY;AACd,kBAAY,IAAI,QAAQ,YAAY,gBAAgB,aAAa,MAAM;AACvE,aAAO,MAAM,sBAAsB,YAAY,WAAW,IAAI;AAAA,IAChE;AACA,QAAI,cAAc,CAAC,GAAG;AACpB,YAAM,eAAe,cAAc,CAAC,EAAE,KAAK;AAC3C,UAAI,cAAc;AAChB,eAAO,QAAQ;AAAA,UACb;AAAA,UACA,IAAI;AAAA,YACF;AAAA,YACA,OAAO,MAAM,YAAY,WAAW,SAAS,gBAAgB,aAAa;AAAA,UAC5E;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,cAAc;AAChB,WAAO,QAAQ,sBAAsB,cAAc,eAAe,IAAI;AAAA,EACxE;AACA,SAAO;AACT;AACA,SAAS,SAAS,OAAO,KAAK;AAC5B,SAAO,aAAa,MAAM,OAAO,GAAG;AACtC;AACA,SAAS,WAAW,KAAK;AACvB,MAAI,UAAU,WAAW;AACvB,mBAAe,WAAW,OAAO,MAAM,GAAG,MAAM,CAAC;AAAA,EACnD;AACA,UAAQ,cAAc;AACtB,QAAM,EAAE,KAAK,GAAG,IAAI;AACpB,MAAI,OAAO,KAAK,eAAe,SAAS,GAAG,GAAG;AAC5C;AAAA,EACF;AACA,MAAI,eAAe,UAAU,GAAG,GAAG;AACjC,eAAW,gBAAgB,GAAG;AAAA,EAChC,OAAO;AACL,UAAM,QAAQ,cAAc;AAC5B,QAAI,OAAO,KAAK,OAAO,GAAG;AACxB,gBAAU,QAAQ;AAAA,IACpB;AAAA,EACF;AACA,mBAAiB;AACnB;AACA,SAAS,OAAO,SAAS,OAAO,KAAK;AACnC;AACE,UAAM,MAAM,MAAM,CAAC,KAAK,MAAM,CAAC,EAAE;AACjC,QAAI,QAAQ,YAAY,QAAQ,WAAW,QAAQ,SAAS,GAAG,GAAG;AAChE,gBAAU,eAAe,eAAe,SAAS,KAAK;AAAA,IACxD;AAAA,EACF;AACA,QAAM,SAAS,MAAM,CAAC,KAAK;AAC3B,QAAM,WAAW,OAAO,SAAS,OAAO,SAAS,SAAS,CAAC;AAC3D,MAAI,YAAY,SAAS,SAAS,GAAG;AACnC,aAAS,WAAW;AACpB,cAAU,SAAS,KAAK,GAAG;AAAA,EAC7B,OAAO;AACL,WAAO,SAAS,KAAK;AAAA,MACnB,MAAM;AAAA,MACN;AAAA,MACA,KAAK,OAAO,OAAO,GAAG;AAAA,IACxB,CAAC;AAAA,EACH;AACF;AACA,SAAS,WAAW,IAAI,KAAK,YAAY,OAAO;AAC9C,MAAI,WAAW;AACb,cAAU,GAAG,KAAK,UAAU,KAAK,EAAE,CAAC;AAAA,EACtC,OAAO;AACL,cAAU,GAAG,KAAK,UAAU,KAAK,EAAE,IAAI,CAAC;AAAA,EAC1C;AACA,MAAI,UAAU,WAAW;AACvB,QAAI,GAAG,SAAS,QAAQ;AACtB,SAAG,SAAS,MAAM,OAAO,CAAC,GAAG,GAAG,SAAS,GAAG,SAAS,SAAS,CAAC,EAAE,IAAI,GAAG;AAAA,IAC1E,OAAO;AACL,SAAG,SAAS,MAAM,OAAO,CAAC,GAAG,GAAG,SAAS,KAAK;AAAA,IAChD;AACA,OAAG,SAAS,SAAS;AAAA,MACnB,GAAG,SAAS,MAAM;AAAA,MAClB,GAAG,SAAS,IAAI;AAAA,IAClB;AAAA,EACF;AACA,QAAM,EAAE,KAAK,IAAI,SAAS,IAAI;AAC9B,MAAI,CAAC,QAAQ;AACX,QAAI,QAAQ,QAAQ;AAClB,SAAG,UAAU;AAAA,IACf,WAAW,mBAAmB,EAAE,GAAG;AACjC,SAAG,UAAU;AAAA,IACf,WAAW,YAAY,EAAE,GAAG;AAC1B,SAAG,UAAU;AAAA,IACf;AAAA,EACF;AACA,MAAI,CAAC,UAAU,UAAU;AACvB,OAAG,WAAW,mBAAmB,QAAQ;AAAA,EAC3C;AACA,MAAI,OAAO,KAAK,eAAe,mBAAmB,GAAG,GAAG;AACtD,UAAM,QAAQ,SAAS,CAAC;AACxB,QAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,YAAM,UAAU,MAAM,QAAQ,QAAQ,UAAU,EAAE;AAAA,IACpD;AAAA,EACF;AACA,MAAI,OAAO,KAAK,eAAe,SAAS,GAAG,GAAG;AAC5C;AAAA,EACF;AACA,MAAI,wBAAwB,IAAI;AAC9B,aAAS,UAAU,SAAS;AAC5B,0BAAsB;AAAA,EACxB;AACA,MAAI,UAAU,UAAU,MAAM,CAAC,IAAI,MAAM,CAAC,EAAE,KAAK,eAAe,QAAQ,GAAG;AACzE,cAAU,QAAQ;AAAA,EACpB;AACA;AACE,UAAM,QAAQ,GAAG;AACjB,QAAiD;AAAA,MAC/C;AAAA,MACA;AAAA,IACF,GAAG;AACD,UAAI,QAAQ;AACZ,UAAI,SAAS;AACb,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAM,IAAI,MAAM,CAAC;AACjB,YAAI,EAAE,SAAS,GAAG;AAChB,cAAI,EAAE,SAAS,MAAM;AACnB,oBAAQ;AAAA,UACV,WAAW,EAAE,SAAS,OAAO;AAC3B,qBAAS;AAAA,UACX;AAAA,QACF;AACA,YAAI,SAAS,QAAQ;AACnB;AAAA,YACE;AAAA,YACA;AAAA,YACA,GAAG;AAAA,UACL;AACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,CAAC,UAAU,aAAa;AAAA,MAC1B;AAAA,MACA;AAAA,IACF,KAAK,GAAG,QAAQ,cAAc,CAAC,mBAAmB,EAAE,GAAG;AACrD,MAA6C;AAAA,QAC3C;AAAA,QACA;AAAA,QACA,GAAG;AAAA,MACL;AACA,YAAM,SAAS,MAAM,CAAC,KAAK;AAC3B,YAAM,QAAQ,OAAO,SAAS,QAAQ,EAAE;AACxC,aAAO,SAAS,OAAO,OAAO,GAAG,GAAG,GAAG,QAAQ;AAAA,IACjD;AACA,UAAM,qBAAqB,MAAM;AAAA,MAC/B,CAAC,MAAM,EAAE,SAAS,KAAK,EAAE,SAAS;AAAA,IACpC;AACA,QAAI,sBAAsB;AAAA,MACxB;AAAA,MACA;AAAA,MACA,mBAAmB;AAAA,IACrB,KAAK,GAAG,SAAS,QAAQ;AACvB,yBAAmB,QAAQ;AAAA,QACzB,MAAM;AAAA,QACN,SAAS;AAAA,UACP,GAAG,SAAS,CAAC,EAAE,IAAI,MAAM;AAAA,UACzB,GAAG,SAAS,GAAG,SAAS,SAAS,CAAC,EAAE,IAAI,IAAI;AAAA,QAC9C;AAAA,QACA,KAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,UAAU,OAAO,GAAG;AAC3B,MAAI,IAAI;AACR,SAAO,aAAa,WAAW,CAAC,MAAM,KAAK,IAAI,aAAa,SAAS,EAAG;AACxE,SAAO;AACT;AACA,SAAS,UAAU,OAAO,GAAG;AAC3B,MAAI,IAAI;AACR,SAAO,aAAa,WAAW,CAAC,MAAM,KAAK,KAAK,EAAG;AACnD,SAAO;AACT;AAEA,SAAS,mBAAmB,EAAE,KAAK,MAAM,GAAG;AAC1C,MAAI,QAAQ,YAAY;AACtB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAI,MAAM,CAAC,EAAE,SAAS,KAAK,mBAAmB,IAAI,MAAM,CAAC,EAAE,IAAI,GAAG;AAChE,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,YAAY,EAAE,KAAK,MAAM,GAAG;AACnC,MAAI,eAAe,gBAAgB,GAAG,GAAG;AACvC,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,eAAe,YAAY,IAAI,WAAW,CAAC,CAAC,KAAK,gBAAgB,GAAG,KAAK,eAAe,sBAAsB,eAAe,mBAAmB,GAAG,KAAK,eAAe,eAAe,CAAC,eAAe,YAAY,GAAG,GAAG;AAClO,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,IAAI,MAAM,CAAC;AACjB,QAAI,EAAE,SAAS,GAAG;AAChB,UAAI,EAAE,SAAS,QAAQ,EAAE,OAAO;AAC9B,YAAI,EAAE,MAAM,QAAQ,WAAW,MAAM,GAAG;AACtC,iBAAO;AAAA,QACT,WAAW;AAAA,UACT;AAAA,UACA;AAAA,UACA,EAAE;AAAA,QACJ,GAAG;AACD,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA;AAAA,MACA,EAAE,SAAS,UAAU,cAAc,EAAE,KAAK,IAAI,KAAK;AAAA,QACjD;AAAA,QACA;AAAA,QACA,EAAE;AAAA,MACJ;AAAA,MAAG;AACD,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,YAAY,GAAG;AACtB,SAAO,IAAI,MAAM,IAAI;AACvB;AAEA,SAAS,mBAAmB,OAAO,KAAK;AACtC,QAAM,iBAAiB,eAAe,eAAe;AACrD,MAAI,oBAAoB;AACxB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,OAAO,MAAM,CAAC;AACpB,QAAI,KAAK,SAAS,GAAG;AACnB,UAAI,CAAC,OAAO;AACV,YAAI,gBAAgB,KAAK,OAAO,GAAG;AACjC,gBAAM,OAAO,MAAM,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,EAAE;AAC1C,gBAAM,OAAO,MAAM,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,EAAE;AAC1C,cAAI,CAAC,QAAQ,CAAC,QAAQ,mBAAmB,SAAS,MAAM,SAAS,KAAK,SAAS,MAAM,SAAS,MAAM,SAAS,KAAK,SAAS,KAAK,eAAe,KAAK,OAAO,KAAK;AAC9J,gCAAoB;AACpB,kBAAM,CAAC,IAAI;AAAA,UACb,OAAO;AACL,iBAAK,UAAU;AAAA,UACjB;AAAA,QACF,WAAW,gBAAgB;AACzB,eAAK,UAAU,SAAS,KAAK,OAAO;AAAA,QACtC;AAAA,MACF,OAAO;AACL,aAAK,UAAU,KAAK,QAAQ,QAAQ,kBAAkB,IAAI;AAAA,MAC5D;AAAA,IACF;AAAA,EACF;AACA,SAAO,oBAAoB,MAAM,OAAO,OAAO,IAAI;AACrD;AACA,SAAS,gBAAgB,KAAK;AAC5B,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,QAAI,CAAC,aAAa,IAAI,WAAW,CAAC,CAAC,GAAG;AACpC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,eAAe,KAAK;AAC3B,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,UAAM,IAAI,IAAI,WAAW,CAAC;AAC1B,QAAI,MAAM,MAAM,MAAM,IAAI;AACxB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,SAAS,KAAK;AACrB,MAAI,MAAM;AACV,MAAI,uBAAuB;AAC3B,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,QAAI,aAAa,IAAI,WAAW,CAAC,CAAC,GAAG;AACnC,UAAI,CAAC,sBAAsB;AACzB,eAAO;AACP,+BAAuB;AAAA,MACzB;AAAA,IACF,OAAO;AACL,aAAO,IAAI,CAAC;AACZ,6BAAuB;AAAA,IACzB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,QAAQ,MAAM;AACrB,GAAC,MAAM,CAAC,KAAK,aAAa,SAAS,KAAK,IAAI;AAC9C;AACA,SAAS,OAAO,OAAO,KAAK;AAC1B,SAAO;AAAA,IACL,OAAO,UAAU,OAAO,KAAK;AAAA;AAAA,IAE7B,KAAK,OAAO,OAAO,MAAM,UAAU,OAAO,GAAG;AAAA;AAAA,IAE7C,QAAQ,OAAO,OAAO,MAAM,SAAS,OAAO,GAAG;AAAA,EACjD;AACF;AACA,SAAS,SAAS,KAAK;AACrB,SAAO,OAAO,IAAI,MAAM,QAAQ,IAAI,IAAI,MAAM;AAChD;AACA,SAAS,UAAU,KAAK,KAAK;AAC3B,MAAI,MAAM,UAAU,OAAO,GAAG;AAC9B,MAAI,SAAS,SAAS,IAAI,MAAM,QAAQ,GAAG;AAC7C;AACA,SAAS,UAAU,KAAK;AACtB,QAAM,OAAO;AAAA,IACX,MAAM;AAAA,IACN,MAAM,IAAI;AAAA,IACV,SAAS;AAAA,MACP,IAAI,IAAI,MAAM;AAAA,MACd,IAAI,IAAI,MAAM,SAAS,IAAI,QAAQ;AAAA,IACrC;AAAA,IACA,OAAO;AAAA,IACP,KAAK,IAAI;AAAA,EACX;AACA,MAAI,IAAI,KAAK;AACX,UAAM,MAAM,IAAI,IAAI;AACpB,QAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,QAAQ;AACvC,UAAI,MAAM;AACV,UAAI,MAAM;AACV,UAAI,IAAI;AACR,UAAI,IAAI;AAAA,IACV;AACA,SAAK,QAAQ;AAAA,MACX,MAAM;AAAA,MACN,SAAS,IAAI,IAAI;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,UAAU,SAAS,WAAW,OAAO,KAAK,YAAY,GAAG,YAAY,GAAgB;AAC5F,QAAM,MAAM,uBAAuB,SAAS,UAAU,KAAK,SAAS;AACpE,SAAO;AACT;AACA,SAAS,UAAU,MAAM,OAAO,SAAS;AACvC,iBAAe;AAAA,IACb,oBAAoB,MAAM,OAAO,OAAO,KAAK,GAAG,QAAQ,OAAO;AAAA,EACjE;AACF;AACA,SAAS,QAAQ;AACf,YAAU,MAAM;AAChB,mBAAiB;AACjB,gBAAc;AACd,qBAAmB;AACnB,0BAAwB;AACxB,wBAAsB;AACtB,QAAM,SAAS;AACjB;AACA,SAAS,UAAU,OAAO,SAAS;AACjC,QAAM;AACN,iBAAe;AACf,mBAAiB,OAAO,CAAC,GAAG,oBAAoB;AAChD,MAAI,SAAS;AACX,QAAI;AACJ,SAAK,OAAO,SAAS;AACnB,UAAI,QAAQ,GAAG,KAAK,MAAM;AACxB,uBAAe,GAAG,IAAI,QAAQ,GAAG;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AACA,MAAI,MAA2C;AAC7C,QAAI,CAAC,eAAe,gBAAgB;AAClC,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,YAAU,OAAO,eAAe,cAAc,SAAS,IAAI,eAAe,cAAc,QAAQ,IAAI;AACpG,YAAU,QAAQ,eAAe,OAAO,KAAK,eAAe,OAAO;AACnE,QAAM,aAAa,WAAW,QAAQ;AACtC,MAAI,YAAY;AACd,cAAU,gBAAgB,YAAY,WAAW,CAAC,CAAC;AACnD,cAAU,iBAAiB,YAAY,WAAW,CAAC,CAAC;AAAA,EACtD;AACA,QAAM,OAAO,cAAc,WAAW,CAAC,GAAG,KAAK;AAC/C,YAAU,MAAM,YAAY;AAC5B,OAAK,MAAM,OAAO,GAAG,MAAM,MAAM;AACjC,OAAK,WAAW,mBAAmB,KAAK,QAAQ;AAChD,gBAAc;AACd,SAAO;AACT;AAEA,SAAS,YAAY,MAAM,SAAS;AAClC;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA;AAAA;AAAA,IAGA,oBAAoB,MAAM,KAAK,SAAS,CAAC,CAAC;AAAA,EAC5C;AACF;AACA,SAAS,oBAAoB,MAAM,OAAO;AACxC,QAAM,EAAE,SAAS,IAAI;AACrB,SAAO,SAAS,WAAW,KAAK,MAAM,SAAS,KAAK,CAAC,aAAa,KAAK;AACzE;AACA,SAAS,KAAK,MAAM,QAAQ,SAAS,iBAAiB,OAAO,QAAQ,OAAO;AAC1E,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,UAAU,CAAC;AACjB,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,UAAM,QAAQ,SAAS,CAAC;AACxB,QAAI,MAAM,SAAS,KAAK,MAAM,YAAY,GAAG;AAC3C,YAAM,eAAe,iBAAiB,IAAI,gBAAgB,OAAO,OAAO;AACxE,UAAI,eAAe,GAAG;AACpB,YAAI,gBAAgB,GAAG;AACrB,gBAAM,YAAY,YAAY;AAC9B,kBAAQ,KAAK,KAAK;AAClB;AAAA,QACF;AAAA,MACF,OAAO;AACL,cAAM,cAAc,MAAM;AAC1B,YAAI,YAAY,SAAS,IAAI;AAC3B,gBAAM,OAAO,YAAY;AACzB,eAAK,SAAS,UAAU,SAAS,OAAO,SAAS,MAAM,8BAA8B,OAAO,OAAO,KAAK,GAAG;AACzG,kBAAM,QAAQ,aAAa,KAAK;AAChC,gBAAI,OAAO;AACT,0BAAY,QAAQ,QAAQ,MAAM,KAAK;AAAA,YACzC;AAAA,UACF;AACA,cAAI,YAAY,cAAc;AAC5B,wBAAY,eAAe,QAAQ,MAAM,YAAY,YAAY;AAAA,UACnE;AAAA,QACF;AAAA,MACF;AAAA,IACF,WAAW,MAAM,SAAS,IAAI;AAC5B,YAAM,eAAe,iBAAiB,IAAI,gBAAgB,OAAO,OAAO;AACxE,UAAI,gBAAgB,GAAG;AACrB,gBAAQ,KAAK,KAAK;AAClB;AAAA,MACF;AAAA,IACF;AACA,QAAI,MAAM,SAAS,GAAG;AACpB,YAAMA,eAAc,MAAM,YAAY;AACtC,UAAIA,cAAa;AACf,gBAAQ,OAAO;AAAA,MACjB;AACA,WAAK,OAAO,MAAM,SAAS,OAAO,KAAK;AACvC,UAAIA,cAAa;AACf,gBAAQ,OAAO;AAAA,MACjB;AAAA,IACF,WAAW,MAAM,SAAS,IAAI;AAC5B,WAAK,OAAO,MAAM,SAAS,MAAM,SAAS,WAAW,GAAG,IAAI;AAAA,IAC9D,WAAW,MAAM,SAAS,GAAG;AAC3B,eAAS,KAAK,GAAG,KAAK,MAAM,SAAS,QAAQ,MAAM;AACjD;AAAA,UACE,MAAM,SAAS,EAAE;AAAA,UACjB;AAAA,UACA;AAAA,UACA,MAAM,SAAS,EAAE,EAAE,SAAS,WAAW;AAAA,UACvC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,gBAAgB;AACpB,MAAI,QAAQ,WAAW,SAAS,UAAU,KAAK,SAAS,GAAG;AACzD,QAAI,KAAK,YAAY,KAAK,KAAK,eAAe,KAAK,YAAY,SAAS,MAAM,QAAQ,KAAK,YAAY,QAAQ,GAAG;AAChH,WAAK,YAAY,WAAW;AAAA,QAC1B,sBAAsB,KAAK,YAAY,QAAQ;AAAA,MACjD;AACA,sBAAgB;AAAA,IAClB,WAAW,KAAK,YAAY,KAAK,KAAK,eAAe,KAAK,YAAY,SAAS,MAAM,KAAK,YAAY,YAAY,CAAC,QAAQ,KAAK,YAAY,QAAQ,KAAK,KAAK,YAAY,SAAS,SAAS,IAAI;AAC9L,YAAM,OAAO,YAAY,KAAK,aAAa,SAAS;AACpD,UAAI,MAAM;AACR,aAAK,UAAU;AAAA,UACb,sBAAsB,KAAK,OAAO;AAAA,QACpC;AACA,wBAAgB;AAAA,MAClB;AAAA,IACF,WAAW,KAAK,YAAY,KAAK,UAAU,OAAO,SAAS,KAAK,OAAO,YAAY,KAAK,OAAO,eAAe,OAAO,YAAY,SAAS,MAAM,OAAO,YAAY,YAAY,CAAC,QAAQ,OAAO,YAAY,QAAQ,KAAK,OAAO,YAAY,SAAS,SAAS,IAAI;AAC/P,YAAM,WAAW,QAAQ,MAAM,QAAQ,IAAI;AAC3C,YAAM,OAAO,YAAY,SAAS,OAAO,YAAY,OAAO,aAAa,SAAS,GAAG;AACrF,UAAI,MAAM;AACR,aAAK,UAAU;AAAA,UACb,sBAAsB,KAAK,OAAO;AAAA,QACpC;AACA,wBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC,eAAe;AAClB,eAAW,SAAS,SAAS;AAC3B,YAAM,cAAc,QAAQ,MAAM,MAAM,WAAW;AAAA,IACrD;AAAA,EACF;AACA,WAAS,mBAAmB,OAAO;AACjC,UAAM,MAAM,QAAQ,MAAM,KAAK;AAC/B,QAAI,SAAS,QAAQ,KAAK;AACxB,UAAI,kBAAkB;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AACA,WAAS,YAAY,OAAO,MAAM;AAChC,QAAI,MAAM,YAAY,CAAC,QAAQ,MAAM,QAAQ,KAAK,MAAM,SAAS,SAAS,IAAI;AAC5E,YAAM,OAAO,MAAM,SAAS,WAAW;AAAA,QACrC,CAAC,MAAM,EAAE,QAAQ,QAAQ,EAAE,IAAI,YAAY;AAAA,MAC7C;AACA,aAAO,QAAQ,KAAK;AAAA,IACtB;AAAA,EACF;AACA,MAAI,QAAQ,UAAU,QAAQ,gBAAgB;AAC5C,YAAQ,eAAe,UAAU,SAAS,IAAI;AAAA,EAChD;AACF;AACA,SAAS,gBAAgB,MAAM,SAAS;AACtC,QAAM,EAAE,cAAc,IAAI;AAC1B,UAAQ,KAAK,MAAM;AAAA,IACjB,KAAK;AACH,UAAI,KAAK,YAAY,GAAG;AACtB,eAAO;AAAA,MACT;AACA,YAAM,SAAS,cAAc,IAAI,IAAI;AACrC,UAAI,WAAW,QAAQ;AACrB,eAAO;AAAA,MACT;AACA,YAAM,cAAc,KAAK;AACzB,UAAI,YAAY,SAAS,IAAI;AAC3B,eAAO;AAAA,MACT;AACA,UAAI,YAAY,WAAW,KAAK,QAAQ,SAAS,KAAK,QAAQ,mBAAmB,KAAK,QAAQ,QAAQ;AACpG,eAAO;AAAA,MACT;AACA,UAAI,YAAY,cAAc,QAAQ;AACpC,YAAI,cAAc;AAClB,cAAM,qBAAqB,8BAA8B,MAAM,OAAO;AACtE,YAAI,uBAAuB,GAAG;AAC5B,wBAAc,IAAI,MAAM,CAAC;AACzB,iBAAO;AAAA,QACT;AACA,YAAI,qBAAqB,aAAa;AACpC,wBAAc;AAAA,QAChB;AACA,iBAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,gBAAM,YAAY,gBAAgB,KAAK,SAAS,CAAC,GAAG,OAAO;AAC3D,cAAI,cAAc,GAAG;AACnB,0BAAc,IAAI,MAAM,CAAC;AACzB,mBAAO;AAAA,UACT;AACA,cAAI,YAAY,aAAa;AAC3B,0BAAc;AAAA,UAChB;AAAA,QACF;AACA,YAAI,cAAc,GAAG;AACnB,mBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,kBAAM,IAAI,KAAK,MAAM,CAAC;AACtB,gBAAI,EAAE,SAAS,KAAK,EAAE,SAAS,UAAU,EAAE,KAAK;AAC9C,oBAAM,UAAU,gBAAgB,EAAE,KAAK,OAAO;AAC9C,kBAAI,YAAY,GAAG;AACjB,8BAAc,IAAI,MAAM,CAAC;AACzB,uBAAO;AAAA,cACT;AACA,kBAAI,UAAU,aAAa;AACzB,8BAAc;AAAA,cAChB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,YAAY,SAAS;AACvB,mBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,kBAAM,IAAI,KAAK,MAAM,CAAC;AACtB,gBAAI,EAAE,SAAS,GAAG;AAChB,4BAAc,IAAI,MAAM,CAAC;AACzB,qBAAO;AAAA,YACT;AAAA,UACF;AACA,kBAAQ,aAAa,UAAU;AAC/B,kBAAQ;AAAA,YACN,oBAAoB,QAAQ,OAAO,YAAY,WAAW;AAAA,UAC5D;AACA,sBAAY,UAAU;AACtB,kBAAQ,OAAO,eAAe,QAAQ,OAAO,YAAY,WAAW,CAAC;AAAA,QACvE;AACA,sBAAc,IAAI,MAAM,WAAW;AACnC,eAAO;AAAA,MACT,OAAO;AACL,sBAAc,IAAI,MAAM,CAAC;AACzB,eAAO;AAAA,MACT;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO,gBAAgB,KAAK,SAAS,OAAO;AAAA,IAC9C,KAAK;AACH,aAAO,KAAK;AAAA,IACd,KAAK;AACH,UAAI,aAAa;AACjB,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,cAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,YAAI,SAAS,KAAK,KAAK,SAAS,KAAK,GAAG;AACtC;AAAA,QACF;AACA,cAAM,YAAY,gBAAgB,OAAO,OAAO;AAChD,YAAI,cAAc,GAAG;AACnB,iBAAO;AAAA,QACT,WAAW,YAAY,YAAY;AACjC,uBAAa;AAAA,QACf;AAAA,MACF;AACA,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,UAAI,KAA2C;AAC/C,aAAO;AAAA,EACX;AACF;AAOA,SAAS,4BAA4B,OAAO,SAAS;AACnD,MAAI,MAAM,SAAS,MAAM,CAAC,SAAS,MAAM,MAAM,KAAK,sBAAsB,IAAI,MAAM,MAAM,GAAG;AAC3F,UAAM,MAAM,MAAM,UAAU,CAAC;AAC7B,QAAI,IAAI,SAAS,GAAG;AAClB,aAAO,gBAAgB,KAAK,OAAO;AAAA,IACrC,WAAW,IAAI,SAAS,IAAI;AAC1B,aAAO,4BAA4B,KAAK,OAAO;AAAA,IACjD;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,8BAA8B,MAAM,SAAS;AACpD,MAAI,aAAa;AACjB,QAAM,QAAQ,aAAa,IAAI;AAC/B,MAAI,SAAS,MAAM,SAAS,IAAI;AAC9B,UAAM,EAAE,WAAW,IAAI;AACvB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,YAAM,EAAE,KAAK,MAAM,IAAI,WAAW,CAAC;AACnC,YAAM,UAAU,gBAAgB,KAAK,OAAO;AAC5C,UAAI,YAAY,GAAG;AACjB,eAAO;AAAA,MACT;AACA,UAAI,UAAU,YAAY;AACxB,qBAAa;AAAA,MACf;AACA,UAAI;AACJ,UAAI,MAAM,SAAS,GAAG;AACpB,oBAAY,gBAAgB,OAAO,OAAO;AAAA,MAC5C,WAAW,MAAM,SAAS,IAAI;AAC5B,oBAAY,4BAA4B,OAAO,OAAO;AAAA,MACxD,OAAO;AACL,oBAAY;AAAA,MACd;AACA,UAAI,cAAc,GAAG;AACnB,eAAO;AAAA,MACT;AACA,UAAI,YAAY,YAAY;AAC1B,qBAAa;AAAA,MACf;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,aAAa,MAAM;AAC1B,QAAM,cAAc,KAAK;AACzB,MAAI,YAAY,SAAS,IAAI;AAC3B,WAAO,YAAY;AAAA,EACrB;AACF;AAEA,SAAS,uBAAuB,MAAM;AAAA,EACpC,WAAW;AAAA,EACX,oBAAoB;AAAA,EACpB,cAAc;AAAA,EACd,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,iBAAiB,CAAC;AAAA,EAClB,sBAAsB,CAAC;AAAA,EACvB,iBAAiB;AAAA,EACjB,qBAAqB;AAAA,EACrB,kBAAkB;AAAA,EAClB,oBAAoB,CAAC;AAAA,EACrB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,SAAS;AAAA,EACT,OAAO;AAAA,EACP,UAAU;AAAA,EACV,SAAS;AAAA,EACT;AACF,GAAG;AACD,QAAM,YAAY,SAAS,QAAQ,SAAS,EAAE,EAAE,MAAM,iBAAiB;AACvE,QAAM,UAAU;AAAA;AAAA,IAEd;AAAA,IACA,UAAU,aAAa,WAAW,SAAS,UAAU,CAAC,CAAC,CAAC;AAAA,IACxD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA,SAAyB,oBAAI,IAAI;AAAA,IACjC,YAA4B,oBAAI,IAAI;AAAA,IACpC,YAA4B,oBAAI,IAAI;AAAA,IACpC,QAAQ,CAAC;AAAA,IACT,SAAS,CAAC;AAAA,IACV,QAAQ,CAAC;AAAA,IACT,eAA+B,oBAAI,QAAQ;AAAA,IAC3C,OAAO;AAAA,IACP,aAA6B,uBAAO,OAAO,IAAI;AAAA,IAC/C,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,SAAS;AAAA;AAAA,IAET,OAAO,MAAM;AACX,YAAM,QAAQ,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAC3C,cAAQ,QAAQ,IAAI,MAAM,QAAQ,CAAC;AACnC,aAAO;AAAA,IACT;AAAA,IACA,aAAa,MAAM;AACjB,YAAM,QAAQ,QAAQ,QAAQ,IAAI,IAAI;AACtC,UAAI,OAAO;AACT,cAAM,eAAe,QAAQ;AAC7B,YAAI,CAAC,cAAc;AACjB,kBAAQ,QAAQ,OAAO,IAAI;AAAA,QAC7B,OAAO;AACL,kBAAQ,QAAQ,IAAI,MAAM,YAAY;AAAA,QACxC;AAAA,MACF;AAAA,IACF;AAAA,IACA,aAAa,MAAM;AACjB,aAAO,IAAI,cAAc,QAAQ,OAAO,IAAI,CAAC,CAAC;AAAA,IAChD;AAAA,IACA,YAAY,MAAM;AAChB,UAAI,MAA2C;AAC7C,YAAI,CAAC,QAAQ,aAAa;AACxB,gBAAM,IAAI,MAAM,yCAAyC;AAAA,QAC3D;AACA,YAAI,CAAC,QAAQ,QAAQ;AACnB,gBAAM,IAAI,MAAM,2BAA2B;AAAA,QAC7C;AAAA,MACF;AACA,cAAQ,OAAO,SAAS,QAAQ,UAAU,IAAI,QAAQ,cAAc;AAAA,IACtE;AAAA,IACA,WAAW,MAAM;AACf,UAAiD,CAAC,QAAQ,QAAQ;AAChE,cAAM,IAAI,MAAM,0BAA0B;AAAA,MAC5C;AACA,YAAM,OAAO,QAAQ,OAAO;AAC5B,YAAM,eAAe,OAAO,KAAK,QAAQ,IAAI,IAAI,QAAQ,cAAc,QAAQ,aAAa;AAC5F,UAAiD,eAAe,GAAG;AACjE,cAAM,IAAI,MAAM,qDAAqD;AAAA,MACvE;AACA,UAAI,CAAC,QAAQ,SAAS,QAAQ,aAAa;AACzC,gBAAQ,cAAc;AACtB,gBAAQ,cAAc;AAAA,MACxB,OAAO;AACL,YAAI,QAAQ,aAAa,cAAc;AACrC,kBAAQ;AACR,kBAAQ,cAAc;AAAA,QACxB;AAAA,MACF;AACA,cAAQ,OAAO,SAAS,OAAO,cAAc,CAAC;AAAA,IAChD;AAAA,IACA,eAAe;AAAA,IACf,eAAe,KAAK;AAAA,IACpB;AAAA,IACA,kBAAkB,KAAK;AAAA,IACvB;AAAA,IACA,MAAM,KAAK;AACT,UAAI,SAAS,GAAG,EAAG,OAAM,uBAAuB,GAAG;AACnD,cAAQ,OAAO,KAAK,GAAG;AACvB,YAAM,aAAa;AAAA,QACjB,YAAY,QAAQ,OAAO,MAAM;AAAA,QACjC;AAAA,QACA,IAAI;AAAA,QACJ;AAAA,MACF;AACA,iBAAW,UAAU;AACrB,aAAO;AAAA,IACT;AAAA,IACA,MAAM,KAAK,UAAU,OAAO,UAAU,OAAO;AAC3C,YAAM,WAAW;AAAA,QACf,QAAQ,OAAO;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,cAAQ,OAAO,KAAK,QAAQ;AAC5B,aAAO;AAAA,IACT;AAAA,EACF;AACA;AACE,YAAQ,UAA0B,oBAAI,IAAI;AAAA,EAC5C;AACA,SAAO;AACT;AACA,SAAS,UAAU,MAAM,SAAS;AAChC,QAAM,UAAU,uBAAuB,MAAM,OAAO;AACpD,eAAa,MAAM,OAAO;AAC1B,MAAI,QAAQ,aAAa;AACvB,gBAAY,MAAM,OAAO;AAAA,EAC3B;AACA,MAAI,CAAC,QAAQ,KAAK;AAChB,sBAAkB,MAAM,OAAO;AAAA,EACjC;AACA,OAAK,UAA0B,oBAAI,IAAI,CAAC,GAAG,QAAQ,QAAQ,KAAK,CAAC,CAAC;AAClE,OAAK,aAAa,CAAC,GAAG,QAAQ,UAAU;AACxC,OAAK,aAAa,CAAC,GAAG,QAAQ,UAAU;AACxC,OAAK,UAAU,QAAQ;AACvB,OAAK,SAAS,QAAQ;AACtB,OAAK,QAAQ,QAAQ;AACrB,OAAK,SAAS,QAAQ;AACtB,OAAK,cAAc;AACnB;AACE,SAAK,UAAU,CAAC,GAAG,QAAQ,OAAO;AAAA,EACpC;AACF;AACA,SAAS,kBAAkB,MAAM,SAAS;AACxC,QAAM,EAAE,OAAO,IAAI;AACnB,QAAM,EAAE,SAAS,IAAI;AACrB,MAAI,SAAS,WAAW,GAAG;AACzB,UAAM,QAAQ,SAAS,CAAC;AACxB,QAAI,oBAAoB,MAAM,KAAK,KAAK,MAAM,aAAa;AACzD,YAAM,cAAc,MAAM;AAC1B,UAAI,YAAY,SAAS,IAAI;AAC3B,uBAAe,aAAa,OAAO;AAAA,MACrC;AACA,WAAK,cAAc;AAAA,IACrB,OAAO;AACL,WAAK,cAAc;AAAA,IACrB;AAAA,EACF,WAAW,SAAS,SAAS,GAAG;AAC9B,QAAI,YAAY;AAChB,QAAiD,SAAS,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,WAAW,GAAG;AAClG,mBAAa;AAAA,IACf;AACA,SAAK,cAAc;AAAA,MACjB;AAAA,MACA,OAAO,QAAQ;AAAA,MACf;AAAA,MACA,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,MAAO;AACT;AACA,SAAS,iBAAiB,QAAQ,SAAS;AACzC,MAAI,IAAI;AACR,QAAM,cAAc,MAAM;AACxB;AAAA,EACF;AACA,SAAO,IAAI,OAAO,SAAS,QAAQ,KAAK;AACtC,UAAM,QAAQ,OAAO,SAAS,CAAC;AAC/B,QAAI,SAAS,KAAK,EAAG;AACrB,YAAQ,cAAc,QAAQ;AAC9B,YAAQ,SAAS;AACjB,YAAQ,aAAa;AACrB,YAAQ,gBAAgB;AACxB,iBAAa,OAAO,OAAO;AAAA,EAC7B;AACF;AACA,SAAS,aAAa,MAAM,SAAS;AACnC,UAAQ,cAAc;AACtB,QAAM,EAAE,eAAe,IAAI;AAC3B,QAAM,UAAU,CAAC;AACjB,WAAS,KAAK,GAAG,KAAK,eAAe,QAAQ,MAAM;AACjD,UAAM,SAAS,eAAe,EAAE,EAAE,MAAM,OAAO;AAC/C,QAAI,QAAQ;AACV,UAAI,QAAQ,MAAM,GAAG;AACnB,gBAAQ,KAAK,GAAG,MAAM;AAAA,MACxB,OAAO;AACL,gBAAQ,KAAK,MAAM;AAAA,MACrB;AAAA,IACF;AACA,QAAI,CAAC,QAAQ,aAAa;AACxB;AAAA,IACF,OAAO;AACL,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AACA,UAAQ,KAAK,MAAM;AAAA,IACjB,KAAK;AACH,UAAI,CAAC,QAAQ,KAAK;AAChB,gBAAQ,OAAO,cAAc;AAAA,MAC/B;AACA;AAAA,IACF,KAAK;AACH,UAAI,CAAC,QAAQ,KAAK;AAChB,gBAAQ,OAAO,iBAAiB;AAAA,MAClC;AACA;AAAA,IAEF,KAAK;AACH,eAAS,KAAK,GAAG,KAAK,KAAK,SAAS,QAAQ,MAAM;AAChD,qBAAa,KAAK,SAAS,EAAE,GAAG,OAAO;AAAA,MACzC;AACA;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,uBAAiB,MAAM,OAAO;AAC9B;AAAA,EACJ;AACA,UAAQ,cAAc;AACtB,MAAI,IAAI,QAAQ;AAChB,SAAO,KAAK;AACV,YAAQ,CAAC,EAAE;AAAA,EACb;AACF;AACA,SAAS,mCAAmC,MAAM,IAAI;AACpD,QAAM,UAAU,SAAS,IAAI,IAAI,CAAC,MAAM,MAAM,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC;AACvE,SAAO,CAAC,MAAM,YAAY;AACxB,QAAI,KAAK,SAAS,GAAG;AACnB,YAAM,EAAE,MAAM,IAAI;AAClB,UAAI,KAAK,YAAY,KAAK,MAAM,KAAK,OAAO,GAAG;AAC7C;AAAA,MACF;AACA,YAAM,UAAU,CAAC;AACjB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAM,OAAO,MAAM,CAAC;AACpB,YAAI,KAAK,SAAS,KAAK,QAAQ,KAAK,IAAI,GAAG;AACzC,gBAAM,OAAO,GAAG,CAAC;AACjB;AACA,gBAAM,SAAS,GAAG,MAAM,MAAM,OAAO;AACrC,cAAI,OAAQ,SAAQ,KAAK,MAAM;AAAA,QACjC;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAIA,SAAS,qBAAqB,KAAK;AAAA,EACjC,OAAO;AAAA,EACP,oBAAoB,SAAS;AAAA,EAC7B,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AAAA,EACV,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AACV,GAAG;AACD,QAAM,UAAU;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ,IAAI;AAAA,IACZ,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,MAAM;AAAA,IACN,KAAK;AAAA,IACL,OAAO,KAAK;AACV,aAAO,IAAI,cAAc,GAAG,CAAC;AAAA,IAC/B;AAAA,IACA,KAAK,MAAM,eAAe,IAAe,MAAM;AAC7C,cAAQ,QAAQ;AAAA,IAClB;AAAA,IACA,SAAS;AACP,cAAQ,EAAE,QAAQ,WAAW;AAAA,IAC/B;AAAA,IACA,SAAS,iBAAiB,OAAO;AAC/B,UAAI,gBAAgB;AAClB,UAAE,QAAQ;AAAA,MACZ,OAAO;AACL,gBAAQ,EAAE,QAAQ,WAAW;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,UAAU;AACR,cAAQ,QAAQ,WAAW;AAAA,IAC7B;AAAA,EACF;AACA,WAAS,QAAQ,GAAG;AAClB,YAAQ;AAAA,MAAK,OAAO,KAAK,OAAO,CAAC;AAAA,MAAG;AAAA;AAAA,IAAa;AAAA,EACnD;AACA,SAAO;AACT;AACA,SAAS,SAAS,KAAK,UAAU,CAAC,GAAG;AACnC,QAAM,UAAU,qBAAqB,KAAK,OAAO;AACjD,MAAI,QAAQ,iBAAkB,SAAQ,iBAAiB,OAAO;AAC9D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,MAAM,KAAK,IAAI,OAAO;AACtC,QAAM,aAAa,QAAQ,SAAS;AACpC,QAAM,eAAe,CAAC,qBAAqB,SAAS;AACpD,QAAM,kBAAkB;AACxB;AACE,wBAAoB,KAAK,eAAe;AAAA,EAC1C;AACA,QAAM,eAAe,MAAM,cAAc;AACzC,QAAM,OAAO,MAAM,CAAC,QAAQ,SAAS,WAAW,QAAQ,IAAI,CAAC,QAAQ,QAAQ;AAC7E,QAAM,YAAY,KAAK,KAAK,IAAI;AAChC;AACE,SAAK,YAAY,YAAY,IAAI,SAAS,KAAK;AAAA,EACjD;AACA,SAAO;AACP,MAAI,cAAc;AAChB,SAAK,eAAe;AACpB,WAAO;AACP,QAAI,YAAY;AACd;AAAA,QACE,WAAW,QAAQ,IAAI,WAAW,EAAE,KAAK,IAAI,CAAC;AAAA;AAAA,QAE9C;AAAA;AAAA,MACF;AACA,cAAQ;AAAA,IACV;AAAA,EACF;AACA,MAAI,IAAI,WAAW,QAAQ;AACzB,cAAU,IAAI,YAAY,aAAa,OAAO;AAC9C,QAAI,IAAI,WAAW,UAAU,IAAI,QAAQ,GAAG;AAC1C,cAAQ;AAAA,IACV;AAAA,EACF;AACA,MAAI,IAAI,WAAW,QAAQ;AACzB,cAAU,IAAI,YAAY,aAAa,OAAO;AAC9C,QAAI,IAAI,QAAQ,GAAG;AACjB,cAAQ;AAAA,IACV;AAAA,EACF;AACA,MAAI,IAAI,WAAW,IAAI,QAAQ,QAAQ;AACrC,YAAQ;AACR,cAAU,IAAI,SAAS,UAAU,OAAO;AACxC,YAAQ;AAAA,EACV;AACA,MAAI,IAAI,QAAQ,GAAG;AACjB,SAAK,MAAM;AACX,aAAS,IAAI,GAAG,IAAI,IAAI,OAAO,KAAK;AAClC,WAAK,GAAG,IAAI,IAAI,OAAO,EAAE,QAAQ,CAAC,EAAE;AAAA,IACtC;AAAA,EACF;AACA,MAAI,IAAI,WAAW,UAAU,IAAI,WAAW,UAAU,IAAI,OAAO;AAC/D;AAAA,MAAK;AAAA;AAAA,MACN;AAAA;AAAA,IAAa;AACZ,YAAQ;AAAA,EACV;AACA,MAAI,CAAC,KAAK;AACR,SAAK,SAAS;AAAA,EAChB;AACA,MAAI,IAAI,aAAa;AACnB,YAAQ,IAAI,aAAa,OAAO;AAAA,EAClC,OAAO;AACL,SAAK,MAAM;AAAA,EACb;AACA,MAAI,cAAc;AAChB,aAAS;AACT,SAAK,GAAG;AAAA,EACV;AACA,WAAS;AACT,OAAK,GAAG;AACR,SAAO;AAAA,IACL;AAAA,IACA,MAAM,QAAQ;AAAA,IACd,UAAU;AAAA,IACV,KAAK,QAAQ,MAAM,QAAQ,IAAI,OAAO,IAAI;AAAA,EAC5C;AACF;AACA,SAAS,oBAAoB,KAAK,SAAS;AACzC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,aAAa;AACnB,QAAM,UAAU,MAAM,KAAK,IAAI,OAAO;AACtC,MAAI,QAAQ,SAAS,GAAG;AACtB;AACE;AAAA,QAAK,gBAAgB,UAAU;AAAA;AAAA,QAClC;AAAA;AAAA,MAAY;AACT,UAAI,IAAI,OAAO,QAAQ;AACrB,cAAM,gBAAgB;AAAA,UACpB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,EAAE,OAAO,CAAC,WAAW,QAAQ,SAAS,MAAM,CAAC,EAAE,IAAI,WAAW,EAAE,KAAK,IAAI;AACzE;AAAA,UAAK,WAAW,aAAa;AAAA;AAAA,UAClC;AAAA;AAAA,QAAY;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,YAAU,IAAI,QAAQ,OAAO;AAC7B,UAAQ;AACR,OAAK,SAAS;AAChB;AACA,SAAS,UAAU,QAAQ,MAAM,EAAE,QAAQ,MAAM,SAAS,KAAK,GAAG;AAChE,QAAM,WAAW;AAAA,IACf,SAAS,WAAW,iBAAiB,SAAS,cAAc,oBAAoB;AAAA,EAClF;AACA,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAI,KAAK,OAAO,CAAC;AACjB,UAAM,qBAAqB,GAAG,SAAS,QAAQ;AAC/C,QAAI,oBAAoB;AACtB,WAAK,GAAG,MAAM,GAAG,EAAE;AAAA,IACrB;AACA;AAAA,MACE,SAAS,eAAe,IAAI,IAAI,CAAC,MAAM,QAAQ,IAAI,KAAK,UAAU,EAAE,CAAC,GAAG,qBAAqB,WAAW,EAAE,IAAI,OAAO,MAAM,EAAE;AAAA,IAC/H;AACA,QAAI,IAAI,OAAO,SAAS,GAAG;AACzB,cAAQ;AAAA,IACV;AAAA,EACF;AACF;AACA,SAAS,UAAU,QAAQ,SAAS;AAClC,MAAI,CAAC,OAAO,QAAQ;AAClB;AAAA,EACF;AACA,UAAQ,OAAO;AACf,QAAM,EAAE,MAAM,QAAQ,IAAI;AAC1B,UAAQ;AACR,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAM,MAAM,OAAO,CAAC;AACpB,QAAI,KAAK;AACP,WAAK,kBAAkB,IAAI,CAAC,KAAK;AACjC,cAAQ,KAAK,OAAO;AACpB,cAAQ;AAAA,IACV;AAAA,EACF;AACA,UAAQ,OAAO;AACjB;AACA,SAAS,OAAO,GAAG;AACjB,SAAO,SAAS,CAAC,KAAK,EAAE,SAAS,KAAK,EAAE,SAAS,KAAK,EAAE,SAAS,KAAK,EAAE,SAAS;AACnF;AACA,SAAS,mBAAmB,OAAO,SAAS;AAC1C,QAAM,aAAa,MAAM,SAAS,KAAkD,MAAM,KAAK,CAAC,MAAM,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAC9H,UAAQ,KAAK,GAAG;AAChB,gBAAc,QAAQ,OAAO;AAC7B,cAAY,OAAO,SAAS,UAAU;AACtC,gBAAc,QAAQ,SAAS;AAC/B,UAAQ,KAAK,GAAG;AAClB;AACA,SAAS,YAAY,OAAO,SAAS,aAAa,OAAO,QAAQ,MAAM;AACrE,QAAM,EAAE,MAAM,QAAQ,IAAI;AAC1B,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,OAAO,MAAM,CAAC;AACpB,QAAI,SAAS,IAAI,GAAG;AAClB;AAAA,QAAK;AAAA,QAAM;AAAA;AAAA,MAAgB;AAAA,IAC7B,WAAW,QAAQ,IAAI,GAAG;AACxB,yBAAmB,MAAM,OAAO;AAAA,IAClC,OAAO;AACL,cAAQ,MAAM,OAAO;AAAA,IACvB;AACA,QAAI,IAAI,MAAM,SAAS,GAAG;AACxB,UAAI,YAAY;AACd,iBAAS,KAAK,GAAG;AACjB,gBAAQ;AAAA,MACV,OAAO;AACL,iBAAS,KAAK,IAAI;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,QAAQ,MAAM,SAAS;AAC9B,MAAI,SAAS,IAAI,GAAG;AAClB,YAAQ;AAAA,MAAK;AAAA,MAAM;AAAA;AAAA,IAAgB;AACnC;AAAA,EACF;AACA,MAAI,SAAS,IAAI,GAAG;AAClB,YAAQ,KAAK,QAAQ,OAAO,IAAI,CAAC;AACjC;AAAA,EACF;AACA,UAAQ,KAAK,MAAM;AAAA,IACjB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,MAA6C;AAAA,QAC3C,KAAK,eAAe;AAAA,QACpB;AAAA,MACF;AACA,cAAQ,KAAK,aAAa,OAAO;AACjC;AAAA,IACF,KAAK;AACH,cAAQ,MAAM,OAAO;AACrB;AAAA,IACF,KAAK;AACH,oBAAc,MAAM,OAAO;AAC3B;AAAA,IACF,KAAK;AACH,uBAAiB,MAAM,OAAO;AAC9B;AAAA,IACF,KAAK;AACH,cAAQ,KAAK,aAAa,OAAO;AACjC;AAAA,IACF,KAAK;AACH,4BAAsB,MAAM,OAAO;AACnC;AAAA,IACF,KAAK;AACH,iBAAW,MAAM,OAAO;AACxB;AAAA,IACF,KAAK;AACH,mBAAa,MAAM,OAAO;AAC1B;AAAA,IACF,KAAK;AACH,wBAAkB,MAAM,OAAO;AAC/B;AAAA,IACF,KAAK;AACH,0BAAoB,MAAM,OAAO;AACjC;AAAA,IACF,KAAK;AACH,yBAAmB,MAAM,OAAO;AAChC;AAAA,IACF,KAAK;AACH,4BAAsB,MAAM,OAAO;AACnC;AAAA,IACF,KAAK;AACH,+BAAyB,MAAM,OAAO;AACtC;AAAA,IACF,KAAK;AACH,yBAAmB,MAAM,OAAO;AAChC;AAAA,IACF,KAAK;AACH,kBAAY,KAAK,MAAM,SAAS,MAAM,KAAK;AAC3C;AAAA,IAEF,KAAK;AACH;AAAA,IACF,KAAK;AACH;AAAA,IACF,KAAK;AACH;AAAA,IACF,KAAK;AACH;AAAA,IACF,KAAK;AACH;AAAA,IAEF,KAAK;AACH;AAAA,IACF;AACE,UAAI,MAA2C;AAC7C,eAAO,OAAO,gCAAgC,KAAK,IAAI,EAAE;AACzD,cAAM,kBAAkB;AACxB,eAAO;AAAA,MACT;AAAA,EACJ;AACF;AACA,SAAS,QAAQ,MAAM,SAAS;AAC9B,UAAQ,KAAK,KAAK,UAAU,KAAK,OAAO,GAAG,IAAkB,IAAI;AACnE;AACA,SAAS,cAAc,MAAM,SAAS;AACpC,QAAM,EAAE,SAAS,SAAS,IAAI;AAC9B,UAAQ;AAAA,IACN,WAAW,KAAK,UAAU,OAAO,IAAI;AAAA,IACrC;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,iBAAiB,MAAM,SAAS;AACvC,QAAM,EAAE,MAAM,QAAQ,KAAK,IAAI;AAC/B,MAAI,KAAM,MAAK,eAAe;AAC9B,OAAK,GAAG,OAAO,iBAAiB,CAAC,GAAG;AACpC,UAAQ,KAAK,SAAS,OAAO;AAC7B,OAAK,GAAG;AACV;AACA,SAAS,sBAAsB,MAAM,SAAS;AAC5C,WAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,UAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,QAAI,SAAS,KAAK,GAAG;AACnB,cAAQ;AAAA,QAAK;AAAA,QAAO;AAAA;AAAA,MAAgB;AAAA,IACtC,OAAO;AACL,cAAQ,OAAO,OAAO;AAAA,IACxB;AAAA,EACF;AACF;AACA,SAAS,2BAA2B,MAAM,SAAS;AACjD,QAAM,EAAE,KAAK,IAAI;AACjB,MAAI,KAAK,SAAS,GAAG;AACnB,SAAK,GAAG;AACR,0BAAsB,MAAM,OAAO;AACnC,SAAK,GAAG;AAAA,EACV,WAAW,KAAK,UAAU;AACxB,UAAM,OAAO,mBAAmB,KAAK,OAAO,IAAI,KAAK,UAAU,KAAK,UAAU,KAAK,OAAO;AAC1F,SAAK,MAAM,IAAe,IAAI;AAAA,EAChC,OAAO;AACL,SAAK,IAAI,KAAK,OAAO,KAAK,IAAkB,IAAI;AAAA,EAClD;AACF;AACA,SAAS,WAAW,MAAM,SAAS;AACjC,QAAM,EAAE,MAAM,QAAQ,KAAK,IAAI;AAC/B,MAAI,MAAM;AACR,SAAK,eAAe;AAAA,EACtB;AACA;AAAA,IACE,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,UAAU,KAAK,OAAO,CAAC;AAAA,IACzD;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,aAAa,MAAM,SAAS;AACnC,QAAM,EAAE,MAAM,QAAQ,KAAK,IAAI;AAC/B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAAA;AAAA,EACF,IAAI;AACJ,MAAI;AACJ,MAAI,WAAW;AACb,QAAI,MAA2C;AAC7C,UAAI,YAAY,GAAG;AACjB,0BAAkB,YAAY,OAAO,eAAe,SAAS,CAAC;AAAA,MAChE,OAAO;AACL,cAAM,YAAY,OAAO,KAAK,cAAc,EAAE,IAAI,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,KAAK,YAAY,CAAC,EAAE,IAAI,CAAC,MAAM,eAAe,CAAC,CAAC,EAAE,KAAK,IAAI;AACvI,0BAAkB,YAAY,OAAO,SAAS;AAAA,MAChD;AAAA,IACF,OAAO;AACL,wBAAkB,OAAO,SAAS;AAAA,IACpC;AAAA,EACF;AACA,MAAI,YAAY;AACd,SAAK,OAAO,eAAe,IAAI,GAAG;AAAA,EACpC;AACA,MAAI,SAAS;AACX,SAAK,IAAI,OAAO,UAAU,CAAC,IAAI,kBAAkB,SAAS,EAAE,KAAK;AAAA,EACnE;AACA,MAAI,MAAM;AACR,SAAK,eAAe;AAAA,EACtB;AACA,QAAM,aAAa,UAAU,oBAAoB,QAAQ,OAAOA,YAAW,IAAI,eAAe,QAAQ,OAAOA,YAAW;AACxH,OAAK,OAAO,UAAU,IAAI,KAAK,IAAe,IAAI;AAClD;AAAA,IACE,gBAAgB,CAAC,KAAK,OAAO,UAAU,iBAAiB,YAAY,CAAC;AAAA,IACrE;AAAA,EACF;AACA,OAAK,GAAG;AACR,MAAI,SAAS;AACX,SAAK,GAAG;AAAA,EACV;AACA,MAAI,YAAY;AACd,SAAK,IAAI;AACT,YAAQ,YAAY,OAAO;AAC3B,SAAK,GAAG;AAAA,EACV;AACF;AACA,SAAS,gBAAgB,MAAM;AAC7B,MAAI,IAAI,KAAK;AACb,SAAO,KAAK;AACV,QAAI,KAAK,CAAC,KAAK,KAAM;AAAA,EACvB;AACA,SAAO,KAAK,MAAM,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,QAAQ,OAAO,MAAM;AACxD;AACA,SAAS,kBAAkB,MAAM,SAAS;AACxC,QAAM,EAAE,MAAM,QAAQ,KAAK,IAAI;AAC/B,QAAM,SAAS,SAAS,KAAK,MAAM,IAAI,KAAK,SAAS,OAAO,KAAK,MAAM;AACvE,MAAI,MAAM;AACR,SAAK,eAAe;AAAA,EACtB;AACA,OAAK,SAAS,KAAK,IAAe,IAAI;AACtC,cAAY,KAAK,WAAW,OAAO;AACnC,OAAK,GAAG;AACV;AACA,SAAS,oBAAoB,MAAM,SAAS;AAC1C,QAAM,EAAE,MAAM,QAAQ,UAAU,QAAQ,IAAI;AAC5C,QAAM,EAAE,WAAW,IAAI;AACvB,MAAI,CAAC,WAAW,QAAQ;AACtB,SAAK,MAAM,IAAe,IAAI;AAC9B;AAAA,EACF;AACA,QAAM,aAAa,WAAW,SAAS,KAAkD,WAAW,KAAK,CAAC,MAAM,EAAE,MAAM,SAAS,CAAC;AAClI,OAAK,aAAa,MAAM,IAAI;AAC5B,gBAAc,OAAO;AACrB,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,UAAM,EAAE,KAAK,MAAM,IAAI,WAAW,CAAC;AACnC,+BAA2B,KAAK,OAAO;AACvC,SAAK,IAAI;AACT,YAAQ,OAAO,OAAO;AACtB,QAAI,IAAI,WAAW,SAAS,GAAG;AAC7B,WAAK,GAAG;AACR,cAAQ;AAAA,IACV;AAAA,EACF;AACA,gBAAc,SAAS;AACvB,OAAK,aAAa,MAAM,IAAI;AAC9B;AACA,SAAS,mBAAmB,MAAM,SAAS;AACzC,qBAAmB,KAAK,UAAU,OAAO;AAC3C;AACA,SAAS,sBAAsB,MAAM,SAAS;AAC5C,QAAM,EAAE,MAAM,QAAQ,SAAS,IAAI;AACnC,QAAM,EAAE,QAAQ,SAAS,MAAM,SAAS,OAAO,IAAI;AACnD,MAAI,QAAQ;AACV,SAAK,IAAI,cAAc,QAAQ,CAAC,GAAG;AAAA,EACrC;AACA,OAAK,KAAK,IAAe,IAAI;AAC7B,MAAI,QAAQ,MAAM,GAAG;AACnB,gBAAY,QAAQ,OAAO;AAAA,EAC7B,WAAW,QAAQ;AACjB,YAAQ,QAAQ,OAAO;AAAA,EACzB;AACA,OAAK,OAAO;AACZ,MAAI,WAAW,MAAM;AACnB,SAAK,GAAG;AACR,WAAO;AAAA,EACT;AACA,MAAI,SAAS;AACX,QAAI,SAAS;AACX,WAAK,SAAS;AAAA,IAChB;AACA,QAAI,QAAQ,OAAO,GAAG;AACpB,yBAAmB,SAAS,OAAO;AAAA,IACrC,OAAO;AACL,cAAQ,SAAS,OAAO;AAAA,IAC1B;AAAA,EACF,WAAW,MAAM;AACf,YAAQ,MAAM,OAAO;AAAA,EACvB;AACA,MAAI,WAAW,MAAM;AACnB,aAAS;AACT,SAAK,GAAG;AAAA,EACV;AACA,MAAI,QAAQ;AACV,QAAI,KAAK,iBAAiB;AACxB,WAAK,mBAAmB;AAAA,IAC1B;AACA,SAAK,GAAG;AAAA,EACV;AACF;AACA,SAAS,yBAAyB,MAAM,SAAS;AAC/C,QAAM,EAAE,MAAM,YAAY,WAAW,SAAS,YAAY,IAAI;AAC9D,QAAM,EAAE,MAAM,QAAQ,UAAU,QAAQ,IAAI;AAC5C,MAAI,KAAK,SAAS,GAAG;AACnB,UAAM,cAAc,CAAC,mBAAmB,KAAK,OAAO;AACpD,mBAAe,KAAK,GAAG;AACvB,kBAAc,MAAM,OAAO;AAC3B,mBAAe,KAAK,GAAG;AAAA,EACzB,OAAO;AACL,SAAK,GAAG;AACR,YAAQ,MAAM,OAAO;AACrB,SAAK,GAAG;AAAA,EACV;AACA,iBAAe,OAAO;AACtB,UAAQ;AACR,iBAAe,KAAK,GAAG;AACvB,OAAK,IAAI;AACT,UAAQ,YAAY,OAAO;AAC3B,UAAQ;AACR,iBAAe,QAAQ;AACvB,iBAAe,KAAK,GAAG;AACvB,OAAK,IAAI;AACT,QAAM,WAAW,UAAU,SAAS;AACpC,MAAI,CAAC,UAAU;AACb,YAAQ;AAAA,EACV;AACA,UAAQ,WAAW,OAAO;AAC1B,MAAI,CAAC,UAAU;AACb,YAAQ;AAAA,EACV;AACA,iBAAe;AAAA,IACb;AAAA;AAAA,EAEF;AACF;AACA,SAAS,mBAAmB,MAAM,SAAS;AACzC,QAAM,EAAE,MAAM,QAAQ,QAAQ,UAAU,QAAQ,IAAI;AACpD,QAAM,EAAE,mBAAmB,gBAAgB,IAAI;AAC/C,MAAI,iBAAiB;AACnB,SAAK,OAAO;AAAA,EACd;AACA,OAAK,UAAU,KAAK,KAAK,QAAQ;AACjC,MAAI,mBAAmB;AACrB,WAAO;AACP,SAAK,GAAG,OAAO,kBAAkB,CAAC,KAAK;AACvC,QAAI,KAAK,QAAS,MAAK,QAAQ;AAC/B,SAAK,IAAI;AACT,YAAQ;AACR,SAAK,GAAG;AAAA,EACV;AACA,OAAK,UAAU,KAAK,KAAK,MAAM;AAC/B,UAAQ,KAAK,OAAO,OAAO;AAC3B,MAAI,mBAAmB;AACrB,SAAK,kBAAkB,KAAK,KAAK,GAAG;AACpC,YAAQ;AACR,SAAK,GAAG,OAAO,kBAAkB,CAAC,MAAM;AACxC,YAAQ;AACR,SAAK,UAAU,KAAK,KAAK,GAAG;AAC5B,aAAS;AAAA,EACX;AACA,OAAK,GAAG;AACR,MAAI,iBAAiB;AACnB,SAAK,IAAI;AAAA,EACX;AACF;AAMA,SAAS,0BAA0B,MAAM,SAAS,WAAW,OAAO,kBAAkB,OAAO;AAC3F,QAAM,MAAM,KAAK;AACjB,MAAI,CAAC,IAAI,KAAK,GAAG;AACf;AAAA,EACF;AACA,MAAI;AACF,QAAI;AAAA,MACF,kBAAkB,IAAI,GAAG,MAAM,UAAU,WAAW,IAAI,GAAG,YAAY,IAAI,GAAG,GAAG;AAAA,IACnF;AAAA,EACF,SAAS,GAAG;AACV,QAAI,UAAU,EAAE;AAChB,UAAM,eAAe,IAAI,QAAQ,eAAe,EAAE,EAAE,MAAM,mBAAmB;AAC7E,QAAI,cAAc;AAChB,gBAAU,qDAAqD,aAAa,CAAC,CAAC;AAAA,IAChF;AACA,YAAQ;AAAA,MACN;AAAA,QACE;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AA+BA,SAAS,kBAAkB,MAAM,SAAS,WAAW,OAAO,kBAAkB,OAAO,YAAY,OAAO,OAAO,QAAQ,WAAW,GAAG;AACnI;AACE,QAAI,MAA2C;AAC7C,gCAA0B,MAAM,SAAS,UAAU,eAAe;AAAA,IACpE;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,oBAAoB,KAAK;AAChC,MAAI,SAAS,GAAG,GAAG;AACjB,WAAO;AAAA,EACT,WAAW,IAAI,SAAS,GAAG;AACzB,WAAO,IAAI;AAAA,EACb,OAAO;AACL,WAAO,IAAI,SAAS,IAAI,mBAAmB,EAAE,KAAK,EAAE;AAAA,EACtD;AACF;AAkCA,SAAS,UAAU,MAAM,KAAK,SAAS,gBAAgB;AACrD,MAAI,IAAI,SAAS,WAAW,CAAC,IAAI,OAAO,CAAC,IAAI,IAAI,QAAQ,KAAK,IAAI;AAChE,UAAM,MAAM,IAAI,MAAM,IAAI,IAAI,MAAM,KAAK;AACzC,YAAQ;AAAA,MACN,oBAAoB,IAAI,IAAI,GAAG;AAAA,IACjC;AACA,QAAI,MAAM,uBAAuB,QAAQ,OAAO,GAAG;AAAA,EACrD;AACA,MAAyD,IAAI,KAAK;AAChE,8BAA0B,IAAI,KAAK,OAAO;AAAA,EAC5C;AACA,MAAI,IAAI,SAAS,MAAM;AACrB,UAAM,SAAS,eAAe,MAAM,GAAG;AACvC,UAAM,SAAS;AAAA,MACb,MAAM;AAAA,MACN,KAAK,SAAS,KAAK,GAAG;AAAA,MACtB,UAAU,CAAC,MAAM;AAAA,IACnB;AACA,YAAQ,YAAY,MAAM;AAC1B,QAAI,gBAAgB;AAClB,aAAO,eAAe,QAAQ,QAAQ,IAAI;AAAA,IAC5C;AAAA,EACF,OAAO;AACL,UAAM,WAAW,QAAQ,OAAO;AAChC,UAAM,WAAW,CAAC;AAClB,QAAI,IAAI,SAAS,QAAQ,IAAI;AAC7B,WAAO,OAAO,IAAI;AAChB,YAAM,UAAU,SAAS,CAAC;AAC1B,UAAI,WAAW,QAAQ,SAAS,GAAG;AACjC,gBAAQ,WAAW,OAAO;AAC1B,QAA6C,SAAS,QAAQ,OAAO;AACrE;AAAA,MACF;AACA,UAAI,WAAW,QAAQ,SAAS,KAAK,CAAC,QAAQ,QAAQ,KAAK,EAAE,QAAQ;AACnE,gBAAQ,WAAW,OAAO;AAC1B;AAAA,MACF;AACA,UAAI,WAAW,QAAQ,SAAS,GAAG;AACjC,YAAI,IAAI,SAAS,aAAa,QAAQ,SAAS,QAAQ,SAAS,SAAS,CAAC,EAAE,cAAc,QAAQ;AAChG,kBAAQ;AAAA,YACN,oBAAoB,IAAI,KAAK,GAAG;AAAA,UAClC;AAAA,QACF;AACA,gBAAQ,WAAW;AACnB,cAAM,SAAS,eAAe,MAAM,GAAG;AACvC,YAAiD,SAAS;AAAA,QAC1D,EAAE,QAAQ,UAAU,QAAQ,OAAO,SAAS,MAAM,QAAQ,OAAO,QAAQ,gBAAgB,QAAQ,OAAO,QAAQ,gBAAgB;AAC9H,iBAAO,WAAW,CAAC,GAAG,UAAU,GAAG,OAAO,QAAQ;AAAA,QACpD;AACA,YAAI,MAAoD;AACtD,gBAAM,MAAM,OAAO;AACnB,cAAI,KAAK;AACP,oBAAQ,SAAS,QAAQ,CAAC,EAAE,QAAQ,MAAM;AACxC,kBAAI,UAAU,SAAS,GAAG,GAAG;AAC3B,wBAAQ;AAAA,kBACN;AAAA,oBACE;AAAA,oBACA,OAAO,QAAQ;AAAA,kBACjB;AAAA,gBACF;AAAA,cACF;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AACA,gBAAQ,SAAS,KAAK,MAAM;AAC5B,cAAM,SAAS,kBAAkB,eAAe,SAAS,QAAQ,KAAK;AACtE,qBAAa,QAAQ,OAAO;AAC5B,YAAI,OAAQ,QAAO;AACnB,gBAAQ,cAAc;AAAA,MACxB,OAAO;AACL,gBAAQ;AAAA,UACN,oBAAoB,IAAI,KAAK,GAAG;AAAA,QAClC;AAAA,MACF;AACA;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,eAAe,MAAM,KAAK;AACjC,QAAM,eAAe,KAAK,YAAY;AACtC,SAAO;AAAA,IACL,MAAM;AAAA,IACN,KAAK,KAAK;AAAA,IACV,WAAW,IAAI,SAAS,SAAS,SAAS,IAAI;AAAA,IAC9C,UAAU,gBAAgB,CAAC,QAAQ,MAAM,KAAK,IAAI,KAAK,WAAW,CAAC,IAAI;AAAA,IACvE,SAAS,SAAS,MAAM,KAAK;AAAA,IAC7B;AAAA,EACF;AACF;AACA,SAAS,2BAA2B,QAAQ,UAAU,SAAS;AAC7D,MAAI,OAAO,WAAW;AACpB,WAAO;AAAA,MACL,OAAO;AAAA,MACP,0BAA0B,QAAQ,UAAU,OAAO;AAAA;AAAA;AAAA,MAGnD,qBAAqB,QAAQ,OAAO,cAAc,GAAG;AAAA,QACnD,OAA4C,WAAW;AAAA,QACvD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,OAAO;AACL,WAAO,0BAA0B,QAAQ,UAAU,OAAO;AAAA,EAC5D;AACF;AACA,SAAS,0BAA0B,QAAQ,UAAU,SAAS;AAC5D,QAAM,EAAE,OAAO,IAAI;AACnB,QAAM,cAAc;AAAA,IAClB;AAAA,IACA;AAAA,MACE,GAAG,QAAQ;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,aAAa,SAAS,CAAC;AAC7B,QAAM,sBAAsB,SAAS,WAAW,KAAK,WAAW,SAAS;AACzE,MAAI,qBAAqB;AACvB,QAAI,SAAS,WAAW,KAAK,WAAW,SAAS,IAAI;AACnD,YAAM,YAAY,WAAW;AAC7B,iBAAW,WAAW,aAAa,OAAO;AAC1C,aAAO;AAAA,IACT,OAAO;AACL,UAAI,YAAY;AAChB,UAAiD,CAAC,OAAO,gBAAgB,SAAS,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,WAAW,GAAG;AAC1H,qBAAa;AAAA,MACf;AACA,aAAO;AAAA,QACL;AAAA,QACA,OAAO,QAAQ;AAAA,QACf,uBAAuB,CAAC,WAAW,CAAC;AAAA,QACpC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,OAAO;AACL,UAAM,MAAM,WAAW;AACvB,UAAM,YAAY,mBAAmB,GAAG;AACxC,QAAI,UAAU,SAAS,IAAI;AACzB,qBAAe,WAAW,OAAO;AAAA,IACnC;AACA,eAAW,WAAW,aAAa,OAAO;AAC1C,WAAO;AAAA,EACT;AACF;AACA,SAAS,UAAU,GAAG,GAAG;AACvB,MAAI,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM;AAC3B,WAAO;AAAA,EACT;AACA,MAAI,EAAE,SAAS,GAAG;AAChB,QAAI,EAAE,MAAM,YAAY,EAAE,MAAM,SAAS;AACvC,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AACL,UAAM,MAAM,EAAE;AACd,UAAM,YAAY,EAAE;AACpB,QAAI,IAAI,SAAS,UAAU,MAAM;AAC/B,aAAO;AAAA,IACT;AACA,QAAI,IAAI,SAAS,KAAK,IAAI,aAAa,UAAU,YAAY,IAAI,YAAY,UAAU,SAAS;AAC9F,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,MAAM;AAChC,SAAO,MAAM;AACX,QAAI,KAAK,SAAS,IAAI;AACpB,UAAI,KAAK,UAAU,SAAS,IAAI;AAC9B,eAAO,KAAK;AAAA,MACd,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,WAAW,KAAK,SAAS,IAAI;AAC3B,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AACF;AAkNA,SAAS,WAAW,MAAM,KAAK,SAAS,gBAAgB;AACtD,MAAI,CAAC,IAAI,KAAK;AACZ,YAAQ;AAAA,MACN,oBAAoB,IAAI,IAAI,GAAG;AAAA,IACjC;AACA;AAAA,EACF;AACA,QAAM,cAAc,IAAI;AACxB,MAAI,CAAC,aAAa;AAChB,YAAQ;AAAA,MACN,oBAAoB,IAAI,IAAI,GAAG;AAAA,IACjC;AACA;AAAA,EACF;AACA,yBAAuB,aAAa,OAAO;AAC3C,QAAM,EAAE,gBAAgB,mBAAmB,OAAO,IAAI;AACtD,QAAM,EAAE,QAAQ,OAAO,KAAK,MAAM,IAAI;AACtC,QAAM,UAAU;AAAA,IACd,MAAM;AAAA,IACN,KAAK,IAAI;AAAA,IACT;AAAA,IACA,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB;AAAA,IACA,UAAU,eAAe,IAAI,IAAI,KAAK,WAAW,CAAC,IAAI;AAAA,EACxD;AACA,UAAQ,YAAY,OAAO;AAC3B,SAAO;AACP,QAAM,SAAS,kBAAkB,eAAe,OAAO;AACvD,SAAO,MAAM;AACX,WAAO;AACP,QAAI,OAAQ,QAAO;AAAA,EACrB;AACF;AACA,SAAS,uBAAuB,QAAQ,SAAS;AAC/C,MAAI,OAAO,UAAW;AACtB,MAAiD,MAAM;AACrD,8BAA0B,OAAO,QAAQ,OAAO;AAChD,QAAI,OAAO,KAAK;AACd;AAAA,QACE,OAAO;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,OAAO;AAChB;AAAA,QACE,OAAO;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,OAAO;AAChB;AAAA,QACE,OAAO;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO,YAAY;AACrB;AACA,SAAS,oBAAoB,EAAE,OAAO,KAAK,MAAM,GAAG,WAAW,CAAC,GAAG;AACjE,SAAO,iBAAiB,CAAC,OAAO,KAAK,OAAO,GAAG,QAAQ,CAAC;AAC1D;AACA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,IAAI,KAAK;AACb,SAAO,KAAK;AACV,QAAI,KAAK,CAAC,EAAG;AAAA,EACf;AACA,SAAO,KAAK,MAAM,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,KAAK,OAAO,OAAO,uBAAuB,IAAI,OAAO,KAAK,CAAC,GAAG,KAAK,CAAC;AACvG;AAyCA,SAAS,WAAW,MAAM,SAAS,cAAc,mBAAmB;AAClE,UAAQ,OAAO,QAAQ;AACvB,QAAM,EAAE,UAAU,IAAI,IAAI;AAC1B,QAAM,kBAAkB,CAAC;AACzB,QAAM,eAAe,CAAC;AACtB,MAAI,kBAAkB,QAAQ,OAAO,QAAQ,KAAK,QAAQ,OAAO,OAAO;AACxE,QAAM,kBAAkB,QAAQ,MAAM,QAAQ,IAAI;AAClD,MAAI,iBAAiB;AACnB,UAAM,EAAE,KAAK,IAAI,IAAI;AACrB,QAAI,OAAO,CAAC,YAAY,GAAG,GAAG;AAC5B,wBAAkB;AAAA,IACpB;AACA,oBAAgB;AAAA,MACd;AAAA,QACE,OAAO,uBAAuB,WAAW,IAAI;AAAA,QAC7C,YAAY,KAAK,QAAQ,UAAU,GAAG;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AACA,MAAI,mBAAmB;AACvB,MAAI,sBAAsB;AAC1B,QAAM,0BAA0B,CAAC;AACjC,QAAM,gBAAgC,oBAAI,IAAI;AAC9C,MAAI,yBAAyB;AAC7B,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,UAAM,cAAc,SAAS,CAAC;AAC9B,QAAI;AACJ,QAAI,CAAC,eAAe,WAAW,KAAK,EAAE,UAAU,QAAQ,aAAa,QAAQ,IAAI,IAAI;AACnF,UAAI,YAAY,SAAS,GAAG;AAC1B,gCAAwB,KAAK,WAAW;AAAA,MAC1C;AACA;AAAA,IACF;AACA,QAAI,iBAAiB;AACnB,cAAQ;AAAA,QACN,oBAAoB,IAAI,QAAQ,GAAG;AAAA,MACrC;AACA;AAAA,IACF;AACA,uBAAmB;AACnB,UAAM,EAAE,UAAU,cAAc,KAAK,QAAQ,IAAI;AACjD,UAAM;AAAA,MACJ,KAAK,WAAW,uBAAuB,WAAW,IAAI;AAAA,MACtD,KAAK;AAAA,MACL,KAAK;AAAA,IACP,IAAI;AACJ,QAAI;AACJ,QAAI,YAAY,QAAQ,GAAG;AACzB,uBAAiB,WAAW,SAAS,UAAU;AAAA,IACjD,OAAO;AACL,wBAAkB;AAAA,IACpB;AACA,UAAM,OAAO,QAAQ,aAAa,KAAK;AACvC,UAAM,eAAe,YAAY,WAAW,MAAM,cAAc,OAAO;AACvE,QAAI;AACJ,QAAI;AACJ,QAAI,MAAM,QAAQ,aAAa,IAAI,GAAG;AACpC,wBAAkB;AAClB,mBAAa;AAAA,QACX;AAAA,UACE,IAAI;AAAA,UACJ,iBAAiB,UAAU,cAAc,wBAAwB;AAAA,UACjE;AAAA,QACF;AAAA,MACF;AAAA,IACF,WAAW,QAAQ;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA;AAAA,IAEF,GAAG;AACD,UAAI,IAAI;AACR,UAAI;AACJ,aAAO,KAAK;AACV,eAAO,SAAS,CAAC;AACjB,YAAI,KAAK,SAAS,GAAG;AACnB;AAAA,QACF;AAAA,MACF;AACA,UAAI,QAAQ,eAAe,IAAI,KAAK,QAAQ,MAAM,cAAc,GAAG;AACjE,YAAI,cAAc,aAAa,aAAa,SAAS,CAAC;AACtD,eAAO,YAAY,UAAU,SAAS,IAAI;AACxC,wBAAc,YAAY;AAAA,QAC5B;AACA,oBAAY,YAAY,MAAM,MAAM;AAAA,UAClC,MAAM;AAAA,UACN;AAAA,YACE;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,UACA;AAAA,QACF,IAAI,iBAAiB,UAAU,cAAc,wBAAwB;AAAA,MACvE,OAAO;AACL,gBAAQ;AAAA,UACN,oBAAoB,IAAI,MAAM,GAAG;AAAA,QACnC;AAAA,MACF;AAAA,IACF,WAAW,MAAM;AACf,wBAAkB;AAClB,YAAM,cAAc,KAAK;AACzB,UAAI,aAAa;AACf,+BAAuB,aAAa,OAAO;AAC3C,qBAAa;AAAA,UACX,qBAAqB,QAAQ,OAAO,WAAW,GAAG;AAAA,YAChD,YAAY;AAAA,YACZ;AAAA,cACE,oBAAoB,WAAW;AAAA,cAC/B,iBAAiB,UAAU,YAAY;AAAA,cACvC;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AACL,gBAAQ;AAAA,UACN;AAAA,YACE;AAAA,YACA,KAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,IACF,OAAO;AACL,UAAI,gBAAgB;AAClB,YAAI,cAAc,IAAI,cAAc,GAAG;AACrC,kBAAQ;AAAA,YACN;AAAA,cACE;AAAA,cACA;AAAA,YACF;AAAA,UACF;AACA;AAAA,QACF;AACA,sBAAc,IAAI,cAAc;AAChC,YAAI,mBAAmB,WAAW;AAChC,gCAAsB;AAAA,QACxB;AAAA,MACF;AACA,sBAAgB,KAAK,qBAAqB,UAAU,YAAY,CAAC;AAAA,IACnE;AAAA,EACF;AACA,MAAI,CAAC,iBAAiB;AACpB,UAAM,2BAA2B,CAAC,OAAO,cAAc;AACrD,YAAM,KAAK,YAAY,OAAO,QAAQ,WAAW,GAAG;AACpD,UAAI,QAAQ,cAAc;AACxB,WAAG,kBAAkB;AAAA,MACvB;AACA,aAAO,qBAAqB,WAAW,EAAE;AAAA,IAC3C;AACA,QAAI,CAAC,kBAAkB;AACrB,sBAAgB,KAAK,yBAAyB,QAAQ,QAAQ,CAAC;AAAA,IACjE,WAAW,wBAAwB;AAAA;AAAA;AAAA,IAGnC,wBAAwB,KAAK,CAAC,UAAU,uBAAuB,KAAK,CAAC,GAAG;AACtE,UAAI,qBAAqB;AACvB,gBAAQ;AAAA,UACN;AAAA,YACE;AAAA,YACA,wBAAwB,CAAC,EAAE;AAAA,UAC7B;AAAA,QACF;AAAA,MACF,OAAO;AACL,wBAAgB;AAAA,UACd,yBAAyB,QAAQ,uBAAuB;AAAA,QAC1D;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,WAAW,kBAAkB,IAAI,kBAAkB,KAAK,QAAQ,IAAI,IAAI;AAC9E,MAAI,QAAQ;AAAA,IACV,gBAAgB;AAAA,MACd;AAAA,QACE;AAAA;AAAA;AAAA,QAGA;AAAA,UACE,YAAY,OAA4C,OAAO,cAAc,QAAQ,CAAC,QAAQ;AAAA,UAC9F;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,MAAI,aAAa,QAAQ;AACvB,YAAQ,qBAAqB,QAAQ,OAAO,YAAY,GAAG;AAAA,MACzD;AAAA,MACA,sBAAsB,YAAY;AAAA,IACpC,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,iBAAiB,MAAM,IAAI,OAAO;AACzC,QAAM,QAAQ;AAAA,IACZ,qBAAqB,QAAQ,IAAI;AAAA,IACjC,qBAAqB,MAAM,EAAE;AAAA,EAC/B;AACA,MAAI,SAAS,MAAM;AACjB,UAAM;AAAA,MACJ,qBAAqB,OAAO,uBAAuB,OAAO,KAAK,GAAG,IAAI,CAAC;AAAA,IACzE;AAAA,EACF;AACA,SAAO,uBAAuB,KAAK;AACrC;AACA,SAAS,kBAAkB,UAAU;AACnC,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,UAAM,QAAQ,SAAS,CAAC;AACxB,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK;AACH,YAAI,MAAM,YAAY,KAAK,kBAAkB,MAAM,QAAQ,GAAG;AAC5D,iBAAO;AAAA,QACT;AACA;AAAA,MACF,KAAK;AACH,YAAI,kBAAkB,MAAM,QAAQ,EAAG,QAAO;AAC9C;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,YAAI,kBAAkB,MAAM,QAAQ,EAAG,QAAO;AAC9C;AAAA,IACJ;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,uBAAuB,MAAM;AACpC,MAAI,KAAK,SAAS,KAAK,KAAK,SAAS;AACnC,WAAO;AACT,SAAO,KAAK,SAAS,IAAI,CAAC,CAAC,KAAK,QAAQ,KAAK,IAAI,uBAAuB,KAAK,OAAO;AACtF;AAuGA,SAAS,qBAAqB,MAAM,SAAS,MAAM,OAAO;AACxD,MAAI,EAAE,IAAI,IAAI;AACd,QAAM,oBAAoB,eAAe,GAAG;AAC5C,QAAM,SAAS;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,EAEF;AACA,MAAI,QAAQ;AACV,QAAI,qBAAqB;AAAA,MACvB;AAAA,MACA;AAAA,IACF,GAAG;AACD,UAAI;AACJ,UAAI,OAAO,SAAS,GAAG;AACrB,cAAM,OAAO,SAAS,uBAAuB,OAAO,MAAM,SAAS,IAAI;AAAA,MACzE,OAAO;AACL,cAAM,OAAO;AACb,YAAI,CAAC,KAAK;AACR,gBAAM,uBAAuB,MAAM,OAAO,OAAO,IAAI,GAAG;AAAA,QAC1D;AAAA,MACF;AACA,UAAI,KAAK;AACP,eAAO,qBAAqB,QAAQ,OAAO,yBAAyB,GAAG;AAAA,UACrE;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,WAAW,OAAO,SAAS,KAAK,OAAO,MAAM,QAAQ,WAAW,MAAM,GAAG;AACvE,YAAM,OAAO,MAAM,QAAQ,MAAM,CAAC;AAAA,IACpC;AAAA,EACF;AACA,QAAM,UAAU,gBAAgB,GAAG,KAAK,QAAQ,mBAAmB,GAAG;AACtE,MAAI,SAAS;AACX,QAAI,CAAC,IAAK,SAAQ,OAAO,OAAO;AAChC,WAAO;AAAA,EACT;AACA,UAAQ,OAAO,iBAAiB;AAChC,UAAQ,WAAW,IAAI,GAAG;AAC1B,SAAO,eAAe,KAAK,WAAW;AACxC;AACA,SAAS,WAAW,MAAM,SAAS,QAAQ,KAAK,OAAOA,cAAa,oBAAoB,MAAM,OAAO;AACnG,QAAM,EAAE,KAAK,KAAK,YAAY,SAAS,IAAI;AAC3C,MAAI,aAAa,CAAC;AAClB,QAAM,YAAY,CAAC;AACnB,QAAM,oBAAoB,CAAC;AAC3B,QAAM,cAAc,SAAS,SAAS;AACtC,MAAI,iBAAiB;AACrB,MAAI,YAAY;AAChB,MAAI,SAAS;AACb,MAAI,kBAAkB;AACtB,MAAI,kBAAkB;AACtB,MAAI,2BAA2B;AAC/B,MAAI,iBAAiB;AACrB,MAAI,eAAe;AACnB,QAAM,mBAAmB,CAAC;AAC1B,QAAM,eAAe,CAAC,QAAQ;AAC5B,QAAI,WAAW,QAAQ;AACrB,gBAAU;AAAA,QACR,uBAAuB,iBAAiB,UAAU,GAAG,UAAU;AAAA,MACjE;AACA,mBAAa,CAAC;AAAA,IAChB;AACA,QAAI,IAAK,WAAU,KAAK,GAAG;AAAA,EAC7B;AACA,QAAM,oBAAoB,MAAM;AAC9B,QAAI,QAAQ,OAAO,OAAO,GAAG;AAC3B,iBAAW;AAAA,QACT;AAAA,UACE,uBAAuB,WAAW,IAAI;AAAA,UACtC,uBAAuB,MAAM;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,mBAAmB,CAAC,EAAE,KAAK,MAAM,MAAM;AAC3C,QAAI,YAAY,GAAG,GAAG;AACpB,YAAM,OAAO,IAAI;AACjB,YAAM,iBAAiB,KAAK,IAAI;AAChC,UAAI,mBAAmB,CAACA,gBAAe;AAAA;AAAA,MAEvC,KAAK,YAAY,MAAM;AAAA,MACvB,SAAS;AAAA,MACT,CAAC,eAAe,IAAI,GAAG;AACrB,mCAA2B;AAAA,MAC7B;AACA,UAAI,kBAAkB,eAAe,IAAI,GAAG;AAC1C,uBAAe;AAAA,MACjB;AACA,UAAI,kBAAkB,MAAM,SAAS,IAAI;AACvC,gBAAQ,MAAM,UAAU,CAAC;AAAA,MAC3B;AACA,UAAI,MAAM,SAAS,OAAO,MAAM,SAAS,KAAK,MAAM,SAAS,MAAM,gBAAgB,OAAO,OAAO,IAAI,GAAG;AACtG;AAAA,MACF;AACA,UAAI,SAAS,OAAO;AAClB,iBAAS;AAAA,MACX,WAAW,SAAS,SAAS;AAC3B,0BAAkB;AAAA,MACpB,WAAW,SAAS,SAAS;AAC3B,0BAAkB;AAAA,MACpB,WAAW,SAAS,SAAS,CAAC,iBAAiB,SAAS,IAAI,GAAG;AAC7D,yBAAiB,KAAK,IAAI;AAAA,MAC5B;AACA,UAAIA,iBAAgB,SAAS,WAAW,SAAS,YAAY,CAAC,iBAAiB,SAAS,IAAI,GAAG;AAC7F,yBAAiB,KAAK,IAAI;AAAA,MAC5B;AAAA,IACF,OAAO;AACL,uBAAiB;AAAA,IACnB;AAAA,EACF;AACA,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,OAAO,MAAM,CAAC;AACpB,QAAI,KAAK,SAAS,GAAG;AACnB,YAAM,EAAE,KAAK,MAAM,SAAS,MAAM,IAAI;AACtC,UAAI,WAAW;AACf,UAAI,SAAS,OAAO;AAClB,iBAAS;AACT,0BAAkB;AAAA,MACpB;AACA,UAAI,SAAS,SAAS,eAAe,GAAG,KAAK,SAAS,MAAM,QAAQ,WAAW,MAAM,KAAK;AAAA,QACxF;AAAA,QACA;AAAA,MACF,IAAI;AACF;AAAA,MACF;AACA,iBAAW;AAAA,QACT;AAAA,UACE,uBAAuB,MAAM,MAAM,OAAO;AAAA,UAC1C;AAAA,YACE,QAAQ,MAAM,UAAU;AAAA,YACxB;AAAA,YACA,QAAQ,MAAM,MAAM;AAAA,UACtB;AAAA,QACF;AAAA,MACF;AAAA,IACF,OAAO;AACL,YAAM,EAAE,MAAM,KAAK,KAAK,KAAK,UAAU,IAAI;AAC3C,YAAM,UAAU,SAAS;AACzB,YAAM,QAAQ,SAAS;AACvB,UAAI,SAAS,QAAQ;AACnB,YAAI,CAACA,cAAa;AAChB,kBAAQ;AAAA,YACN,oBAAoB,IAAI,GAAG;AAAA,UAC7B;AAAA,QACF;AACA;AAAA,MACF;AACA,UAAI,SAAS,UAAU,SAAS,QAAQ;AACtC;AAAA,MACF;AACA,UAAI,SAAS,QAAQ,WAAW,cAAc,KAAK,IAAI,MAAM,eAAe,GAAG,KAAK;AAAA,QAClF;AAAA,QACA;AAAA,MACF,IAAI;AACF;AAAA,MACF;AACA,UAAI,SAAS,KAAK;AAChB;AAAA,MACF;AACA;AAAA;AAAA,QAEE,WAAW,cAAc,KAAK,KAAK;AAAA;AAAA,QAEnC,SAAS,eAAe,cAAc,KAAK,mBAAmB;AAAA,QAC9D;AACA,yBAAiB;AAAA,MACnB;AACA,UAAI,WAAW,cAAc,KAAK,KAAK,GAAG;AACxC,0BAAkB;AAAA,MACpB;AACA,UAAI,CAAC,QAAQ,WAAW,QAAQ;AAC9B,yBAAiB;AACjB,YAAI,KAAK;AACP,cAAI,SAAS;AACX,8BAAkB;AAClB,yBAAa;AACb;AACE,kBAAI,MAA2C;AAC7C,sBAAM,qBAAqB,UAAU,KAAK,CAAC,SAAS;AAClD,sBAAI,KAAK,SAAS,IAAI;AACpB,2BAAO,KAAK,WAAW,KAAK,CAAC,EAAE,IAAI,MAAM;AACvC,0BAAI,IAAI,SAAS,KAAK,CAAC,IAAI,UAAU;AACnC,+BAAO;AAAA,sBACT;AACA,6BAAO,IAAI,YAAY,WAAW,IAAI,YAAY,WAAW,CAAC,KAAK,IAAI,OAAO;AAAA,oBAChF,CAAC;AAAA,kBACH,OAAO;AACL,2BAAO;AAAA,kBACT;AAAA,gBACF,CAAC;AACD,oBAAI,oBAAoB;AACtB;AAAA,oBACE;AAAA,oBACA;AAAA,oBACA;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AACA,kBAAI;AAAA,gBACF;AAAA,gBACA;AAAA,cACF,GAAG;AACD,0BAAU,QAAQ,GAAG;AACrB;AAAA,cACF;AAAA,YACF;AACA,sBAAU,KAAK,GAAG;AAAA,UACpB,OAAO;AACL,yBAAa;AAAA,cACX,MAAM;AAAA,cACN;AAAA,cACA,QAAQ,QAAQ,OAAO,WAAW;AAAA,cAClC,WAAWA,eAAc,CAAC,GAAG,IAAI,CAAC,KAAK,MAAM;AAAA,YAC/C,CAAC;AAAA,UACH;AAAA,QACF,OAAO;AACL,kBAAQ;AAAA,YACN;AAAA,cACE,UAAU,KAAK;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF;AACA,UAAI,WAAW,UAAU,KAAK,CAAC,QAAQ,IAAI,YAAY,MAAM,GAAG;AAC9D,qBAAa;AAAA,MACf;AACA,YAAM,qBAAqB,QAAQ,oBAAoB,IAAI;AAC3D,UAAI,oBAAoB;AACtB,cAAM,EAAE,OAAO,QAAQ,YAAY,IAAI,mBAAmB,MAAM,MAAM,OAAO;AAC7E,SAAC,OAAO,OAAO,QAAQ,gBAAgB;AACvC,YAAI,SAAS,OAAO,CAAC,YAAY,GAAG,GAAG;AACrC,uBAAa,uBAAuB,QAAQ,UAAU,CAAC;AAAA,QACzD,OAAO;AACL,qBAAW,KAAK,GAAG,MAAM;AAAA,QAC3B;AACA,YAAI,aAAa;AACf,4BAAkB,KAAK,IAAI;AAC3B,cAAI,SAAS,WAAW,GAAG;AACzB,+BAAmB,IAAI,MAAM,WAAW;AAAA,UAC1C;AAAA,QACF;AAAA,MACF,WAAW,CAAC,mBAAmB,IAAI,GAAG;AACpC,0BAAkB,KAAK,IAAI;AAC3B,YAAI,aAAa;AACf,2BAAiB;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,kBAAkB;AACtB,MAAI,UAAU,QAAQ;AACpB,iBAAa;AACb,QAAI,UAAU,SAAS,GAAG;AACxB,wBAAkB;AAAA,QAChB,QAAQ,OAAO,WAAW;AAAA,QAC1B;AAAA,QACA;AAAA,MACF;AAAA,IACF,OAAO;AACL,wBAAkB,UAAU,CAAC;AAAA,IAC/B;AAAA,EACF,WAAW,WAAW,QAAQ;AAC5B,sBAAkB;AAAA,MAChB,iBAAiB,UAAU;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AACA,MAAI,gBAAgB;AAClB,iBAAa;AAAA,EACf,OAAO;AACL,QAAI,mBAAmB,CAACA,cAAa;AACnC,mBAAa;AAAA,IACf;AACA,QAAI,mBAAmB,CAACA,cAAa;AACnC,mBAAa;AAAA,IACf;AACA,QAAI,iBAAiB,QAAQ;AAC3B,mBAAa;AAAA,IACf;AACA,QAAI,0BAA0B;AAC5B,mBAAa;AAAA,IACf;AAAA,EACF;AACA,MAAI,CAAC,mBAAmB,cAAc,KAAK,cAAc,QAAQ,UAAU,gBAAgB,kBAAkB,SAAS,IAAI;AACxH,iBAAa;AAAA,EACf;AACA,MAAI,CAAC,QAAQ,SAAS,iBAAiB;AACrC,YAAQ,gBAAgB,MAAM;AAAA,MAC5B,KAAK;AACH,YAAI,gBAAgB;AACpB,YAAI,gBAAgB;AACpB,YAAI,gBAAgB;AACpB,iBAAS,IAAI,GAAG,IAAI,gBAAgB,WAAW,QAAQ,KAAK;AAC1D,gBAAM,MAAM,gBAAgB,WAAW,CAAC,EAAE;AAC1C,cAAI,YAAY,GAAG,GAAG;AACpB,gBAAI,IAAI,YAAY,SAAS;AAC3B,8BAAgB;AAAA,YAClB,WAAW,IAAI,YAAY,SAAS;AAClC,8BAAgB;AAAA,YAClB;AAAA,UACF,WAAW,CAAC,IAAI,cAAc;AAC5B,4BAAgB;AAAA,UAClB;AAAA,QACF;AACA,cAAM,YAAY,gBAAgB,WAAW,aAAa;AAC1D,cAAM,YAAY,gBAAgB,WAAW,aAAa;AAC1D,YAAI,CAAC,eAAe;AAClB,cAAI,aAAa,CAAC,YAAY,UAAU,KAAK,GAAG;AAC9C,sBAAU,QAAQ;AAAA,cAChB,QAAQ,OAAO,eAAe;AAAA,cAC9B,CAAC,UAAU,KAAK;AAAA,YAClB;AAAA,UACF;AACA,cAAI;AAAA;AAAA,WAEH,mBAAmB,UAAU,MAAM,SAAS,KAAK,UAAU,MAAM,QAAQ,KAAK,EAAE,CAAC,MAAM;AAAA;AAAA,UAExF,UAAU,MAAM,SAAS,KAAK;AAC5B,sBAAU,QAAQ;AAAA,cAChB,QAAQ,OAAO,eAAe;AAAA,cAC9B,CAAC,UAAU,KAAK;AAAA,YAClB;AAAA,UACF;AAAA,QACF,OAAO;AACL,4BAAkB;AAAA,YAChB,QAAQ,OAAO,eAAe;AAAA,YAC9B,CAAC,eAAe;AAAA,UAClB;AAAA,QACF;AACA;AAAA,MACF,KAAK;AACH;AAAA,MACF;AACE,0BAAkB;AAAA,UAChB,QAAQ,OAAO,eAAe;AAAA,UAC9B;AAAA,YACE,qBAAqB,QAAQ,OAAO,oBAAoB,GAAG;AAAA,cACzD;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AACA;AAAA,IACJ;AAAA,EACF;AACA,SAAO;AAAA,IACL,OAAO;AAAA,IACP,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,iBAAiB,YAAY;AACpC,QAAM,aAA6B,oBAAI,IAAI;AAC3C,QAAM,UAAU,CAAC;AACjB,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,UAAM,OAAO,WAAW,CAAC;AACzB,QAAI,KAAK,IAAI,SAAS,KAAK,CAAC,KAAK,IAAI,UAAU;AAC7C,cAAQ,KAAK,IAAI;AACjB;AAAA,IACF;AACA,UAAM,OAAO,KAAK,IAAI;AACtB,UAAM,WAAW,WAAW,IAAI,IAAI;AACpC,QAAI,UAAU;AACZ,UAAI,SAAS,WAAW,SAAS,WAAW,KAAK,IAAI,GAAG;AACtD,qBAAa,UAAU,IAAI;AAAA,MAC7B;AAAA,IACF,OAAO;AACL,iBAAW,IAAI,MAAM,IAAI;AACzB,cAAQ,KAAK,IAAI;AAAA,IACnB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,aAAa,UAAU,UAAU;AACxC,MAAI,SAAS,MAAM,SAAS,IAAI;AAC9B,aAAS,MAAM,SAAS,KAAK,SAAS,KAAK;AAAA,EAC7C,OAAO;AACL,aAAS,QAAQ;AAAA,MACf,CAAC,SAAS,OAAO,SAAS,KAAK;AAAA,MAC/B,SAAS;AAAA,IACX;AAAA,EACF;AACF;AACA,SAAS,mBAAmB,KAAK,SAAS;AACxC,QAAM,UAAU,CAAC;AACjB,QAAM,UAAU,mBAAmB,IAAI,GAAG;AAC1C,MAAI,SAAS;AACX,YAAQ,KAAK,QAAQ,aAAa,OAAO,CAAC;AAAA,EAC5C,OAAO;AACL;AACE,cAAQ,OAAO,iBAAiB;AAChC,cAAQ,WAAW,IAAI,IAAI,IAAI;AAC/B,cAAQ,KAAK,eAAe,IAAI,MAAM,WAAW,CAAC;AAAA,IACpD;AAAA,EACF;AACA,QAAM,EAAE,IAAI,IAAI;AAChB,MAAI,IAAI,IAAK,SAAQ,KAAK,IAAI,GAAG;AACjC,MAAI,IAAI,KAAK;AACX,QAAI,CAAC,IAAI,KAAK;AACZ,cAAQ,KAAK,QAAQ;AAAA,IACvB;AACA,YAAQ,KAAK,IAAI,GAAG;AAAA,EACtB;AACA,MAAI,OAAO,KAAK,IAAI,SAAS,EAAE,QAAQ;AACrC,QAAI,CAAC,IAAI,KAAK;AACZ,UAAI,CAAC,IAAI,KAAK;AACZ,gBAAQ,KAAK,QAAQ;AAAA,MACvB;AACA,cAAQ,KAAK,QAAQ;AAAA,IACvB;AACA,UAAM,iBAAiB,uBAAuB,QAAQ,OAAO,GAAG;AAChE,YAAQ;AAAA,MACN;AAAA,QACE,IAAI,UAAU;AAAA,UACZ,CAAC,aAAa,qBAAqB,UAAU,cAAc;AAAA,QAC7D;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO,sBAAsB,SAAS,IAAI,GAAG;AAC/C;AACA,SAAS,0BAA0B,OAAO;AACxC,MAAI,mBAAmB;AACvB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,wBAAoB,KAAK,UAAU,MAAM,CAAC,CAAC;AAC3C,QAAI,IAAI,IAAI,EAAG,qBAAoB;AAAA,EACrC;AACA,SAAO,mBAAmB;AAC5B;AACA,SAAS,eAAe,KAAK;AAC3B,SAAO,QAAQ,eAAe,QAAQ;AACxC;AAiCA,SAAS,kBAAkB,MAAM,SAAS;AACxC,MAAI,WAAW;AACf,MAAI,YAAY;AAChB,QAAM,eAAe,CAAC;AACtB,WAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,UAAM,IAAI,KAAK,MAAM,CAAC;AACtB,QAAI,EAAE,SAAS,GAAG;AAChB,UAAI,EAAE,OAAO;AACX,YAAI,EAAE,SAAS,QAAQ;AACrB,qBAAW,KAAK,UAAU,EAAE,MAAM,OAAO;AAAA,QAC3C,OAAO;AACL,YAAE,OAAO,SAAS,EAAE,IAAI;AACxB,uBAAa,KAAK,CAAC;AAAA,QACrB;AAAA,MACF;AAAA,IACF,OAAO;AACL,UAAI,EAAE,SAAS,UAAU,cAAc,EAAE,KAAK,MAAM,GAAG;AACrD,YAAI,EAAE,KAAK;AACT,qBAAW,EAAE;AAAA,QACf,WAAW,EAAE,OAAO,EAAE,IAAI,SAAS,GAAG;AACpC,gBAAM,OAAO,SAAS,EAAE,IAAI,OAAO;AACnC,qBAAW,EAAE,MAAM,uBAAuB,MAAM,OAAO,EAAE,IAAI,GAAG;AAAA,QAClE;AAAA,MACF,OAAO;AACL,YAAI,EAAE,SAAS,UAAU,EAAE,OAAO,YAAY,EAAE,GAAG,GAAG;AACpD,YAAE,IAAI,UAAU,SAAS,EAAE,IAAI,OAAO;AAAA,QACxC;AACA,qBAAa,KAAK,CAAC;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AACA,MAAI,aAAa,SAAS,GAAG;AAC3B,UAAM,EAAE,OAAO,WAAW,IAAI;AAAA,MAC5B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,gBAAY;AACZ,QAAI,WAAW,QAAQ;AACrB,cAAQ;AAAA,QACN;AAAA,UACE;AAAA,UACA,WAAW,CAAC,EAAE;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AA0OA,SAAS,qBAAqB,QAAQ,CAAC,GAAG;AACxC,SAAO,EAAE,MAAM;AACjB;AAiBA,SAAS,cAAc,MAAM,SAAS;AACpC,MAAI,KAAK,SAAS,GAAG;AACnB,gBAAY,MAAM,OAAO;AAAA,EAC3B,OAAO;AACL,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,YAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,UAAI,OAAO,UAAU,SAAU;AAC/B,UAAI,MAAM,SAAS,GAAG;AACpB,oBAAY,OAAO,OAAO;AAAA,MAC5B,WAAW,MAAM,SAAS,GAAG;AAC3B,sBAAc,MAAM,OAAO;AAAA,MAC7B,WAAW,MAAM,SAAS,GAAG;AAC3B,sBAAc,MAAM,SAAS,OAAO;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,YAAY,MAAM,SAAS;AAClC,QAAM,MAAM,KAAK;AACjB,MAAI,WAAW;AACf,MAAI,WAAW;AACf,MAAI,mBAAmB;AACvB,MAAI,UAAU;AACd,MAAI,QAAQ;AACZ,MAAI,SAAS;AACb,MAAI,QAAQ;AACZ,MAAI,kBAAkB;AACtB,MAAI,GAAG,MAAM,GAAG,YAAY,UAAU,CAAC;AACvC,OAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AAC/B,WAAO;AACP,QAAI,IAAI,WAAW,CAAC;AACpB,QAAI,UAAU;AACZ,UAAI,MAAM,MAAM,SAAS,GAAI,YAAW;AAAA,IAC1C,WAAW,UAAU;AACnB,UAAI,MAAM,MAAM,SAAS,GAAI,YAAW;AAAA,IAC1C,WAAW,kBAAkB;AAC3B,UAAI,MAAM,MAAM,SAAS,GAAI,oBAAmB;AAAA,IAClD,WAAW,SAAS;AAClB,UAAI,MAAM,MAAM,SAAS,GAAI,WAAU;AAAA,IACzC,WAAW,MAAM;AAAA,IACjB,IAAI,WAAW,IAAI,CAAC,MAAM,OAAO,IAAI,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO;AAC7F,UAAI,eAAe,QAAQ;AACzB,0BAAkB,IAAI;AACtB,qBAAa,IAAI,MAAM,GAAG,CAAC,EAAE,KAAK;AAAA,MACpC,OAAO;AACL,mBAAW;AAAA,MACb;AAAA,IACF,OAAO;AACL,cAAQ,GAAG;AAAA,QACT,KAAK;AACH,qBAAW;AACX;AAAA,QAEF,KAAK;AACH,qBAAW;AACX;AAAA,QAEF,KAAK;AACH,6BAAmB;AACnB;AAAA,QAEF,KAAK;AACH;AACA;AAAA,QAEF,KAAK;AACH;AACA;AAAA,QAEF,KAAK;AACH;AACA;AAAA,QAEF,KAAK;AACH;AACA;AAAA,QAEF,KAAK;AACH;AACA;AAAA,QAEF,KAAK;AACH;AACA;AAAA,MACJ;AACA,UAAI,MAAM,IAAI;AACZ,YAAI,IAAI,IAAI;AACZ,YAAI;AACJ,eAAO,KAAK,GAAG,KAAK;AAClB,cAAI,IAAI,OAAO,CAAC;AAChB,cAAI,MAAM,IAAK;AAAA,QACjB;AACA,YAAI,CAAC,KAAK,CAAC,oBAAoB,KAAK,CAAC,GAAG;AACtC,oBAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,eAAe,QAAQ;AACzB,iBAAa,IAAI,MAAM,GAAG,CAAC,EAAE,KAAK;AAAA,EACpC,WAAW,oBAAoB,GAAG;AAChC,eAAW;AAAA,EACb;AACA,WAAS,aAAa;AACpB,YAAQ,KAAK,IAAI,MAAM,iBAAiB,CAAC,EAAE,KAAK,CAAC;AACjD,sBAAkB,IAAI;AAAA,EACxB;AACA,MAAI,QAAQ,QAAQ;AAClB,IAA6C;AAAA,MAC3C;AAAA,MACA;AAAA,MACA,KAAK;AAAA,IACP;AACA,SAAK,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACnC,mBAAa,WAAW,YAAY,QAAQ,CAAC,GAAG,OAAO;AAAA,IACzD;AACA,SAAK,UAAU;AACf,SAAK,MAAM;AAAA,EACb;AACF;AACA,SAAS,WAAW,KAAK,QAAQ,SAAS;AACxC,UAAQ,OAAO,cAAc;AAC7B,QAAM,IAAI,OAAO,QAAQ,GAAG;AAC5B,MAAI,IAAI,GAAG;AACT,YAAQ,QAAQ,IAAI,MAAM;AAC1B,WAAO,GAAG,eAAe,QAAQ,QAAQ,CAAC,IAAI,GAAG;AAAA,EACnD,OAAO;AACL,UAAM,OAAO,OAAO,MAAM,GAAG,CAAC;AAC9B,UAAM,OAAO,OAAO,MAAM,IAAI,CAAC;AAC/B,YAAQ,QAAQ,IAAI,IAAI;AACxB,WAAO,GAAG,eAAe,MAAM,QAAQ,CAAC,IAAI,GAAG,GAAG,SAAS,MAAM,MAAM,OAAO,IAAI;AAAA,EACpF;AACF;AA4BA,SAAS,uBAAuB,mBAAmB;AACjD,SAAO;AAAA,IACL;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG,CAAC,eAAe;AAAA,MACnB,GAAG,OAA4C,CAAC,mBAAmB,IAAI,CAAC;AAAA,MACxE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,SAAS,YAAY,QAAQ,UAAU,CAAC,GAAG;AACzC,QAAM,UAAU,QAAQ,WAAW;AACnC,QAAM,eAAe,QAAQ,SAAS;AACtC;AACE,QAAI,QAAQ,sBAAsB,MAAM;AACtC,cAAQ,oBAAoB,EAAE,CAAC;AAAA,IACjC,WAAW,cAAc;AACvB,cAAQ,oBAAoB,EAAE,CAAC;AAAA,IACjC;AAAA,EACF;AACA,QAAM,oBAAoB;AAC1B,MAAI,QAAQ,eAAe;AACzB,YAAQ,oBAAoB,EAAE,CAAC;AAAA,EACjC;AACA,MAAI,QAAQ,WAAW,CAAC,cAAc;AACpC,YAAQ,oBAAoB,EAAE,CAAC;AAAA,EACjC;AACA,QAAM,kBAAkB,OAAO,CAAC,GAAG,SAAS;AAAA,IAC1C;AAAA,EACF,CAAC;AACD,QAAM,MAAM,SAAS,MAAM,IAAI,UAAU,QAAQ,eAAe,IAAI;AACpE,QAAM,CAAC,gBAAgB,mBAAmB,IAAI,uBAAuB;AACrE;AAAA,IACE;AAAA,IACA,OAAO,CAAC,GAAG,iBAAiB;AAAA,MAC1B,gBAAgB;AAAA,QACd,GAAG;AAAA,QACH,GAAG,QAAQ,kBAAkB,CAAC;AAAA;AAAA,MAEhC;AAAA,MACA,qBAAqB;AAAA,QACnB,CAAC;AAAA,QACD;AAAA,QACA,QAAQ,uBAAuB,CAAC;AAAA;AAAA,MAElC;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO,SAAS,KAAK,eAAe;AACtC;AA5oLA,IAQM,UACA,UACA,UACA,YACA,iBAGA,YACA,cACA,sBAGA,cACA,sBAGA,gBAGA,aAGA,eAGA,mBAGA,2BAGA,mBAGA,gBAGA,iBAGA,aACA,aACA,cACA,mBAGA,aACA,iBAGA,iBAGA,iBAGA,sBAGA,aACA,UACA,YACA,gBAGA,oBAGA,eACA,cACA,UACA,OACA,QACA,WACA,cACA,eA+CA,YAQA,WAwDA,cAUA,eAUA,SA+LA,uBACA,wBAiBA,WA2BA,WAuvBA,0BAUA,iBAgFA,YA8GA,eA2KA,gBAGA,kBACA,qBACA,eAoBA,aAiBA,iBACA,oBACA,uBACA,kBACA,cACA,cACA,2BA8DA,wBACA,oBACA,SACA,uBACA,oBACA,gBA2EA,gBAsIA,YAEA,sBAcF,gBACA,aACA,cACA,gBACA,aACA,kBACA,uBACA,qBACA,OACA,QACA,qBACE,OACA,WAqTA,eACA,eAoNA,oBA8CA,kBAoYA,uBAiWA,iBACA,aAwkBA,qBAGA,eA2BA,qBA+CA,aA2NA,eAwDA,wBAKA,cAaA,cAgNA,iBACA,iBAYA,qBAmBA,mBA+OA,oBACA,kBA2hBA,qBAsFA,aAgFA,eAyEA,QACA,eAsBA,gBA4DA,qBACA,iBAoJA,MACA,eAuFA,cAaA;AA3pLN;AAAA;AAKA;AACA;AAEA,IAAM,WAAW,OAAO,OAA4C,aAAa,EAAE;AACnF,IAAM,WAAW,OAAO,OAA4C,aAAa,EAAE;AACnF,IAAM,WAAW,OAAO,OAA4C,aAAa,EAAE;AACnF,IAAM,aAAa,OAAO,OAA4C,cAAc,EAAE;AACtF,IAAM,kBAAkB;AAAA,MACtB,OAA4C,mBAAmB;AAAA,IACjE;AACA,IAAM,aAAa,OAAO,OAA4C,cAAc,EAAE;AACtF,IAAM,eAAe,OAAO,OAA4C,gBAAgB,EAAE;AAC1F,IAAM,uBAAuB;AAAA,MAC3B,OAA4C,uBAAuB;AAAA,IACrE;AACA,IAAM,eAAe,OAAO,OAA4C,gBAAgB,EAAE;AAC1F,IAAM,uBAAuB;AAAA,MAC3B,OAA4C,uBAAuB;AAAA,IACrE;AACA,IAAM,iBAAiB;AAAA,MACrB,OAA4C,uBAAuB;AAAA,IACrE;AACA,IAAM,cAAc;AAAA,MAClB,OAA4C,oBAAoB;AAAA,IAClE;AACA,IAAM,gBAAgB;AAAA,MACpB,OAA4C,sBAAsB;AAAA,IACpE;AACA,IAAM,oBAAoB;AAAA,MACxB,OAA4C,qBAAqB;AAAA,IACnE;AACA,IAAM,4BAA4B;AAAA,MAChC,OAA4C,4BAA4B;AAAA,IAC1E;AACA,IAAM,oBAAoB;AAAA,MACxB,OAA4C,qBAAqB;AAAA,IACnE;AACA,IAAM,iBAAiB;AAAA,MACrB,OAA4C,kBAAkB;AAAA,IAChE;AACA,IAAM,kBAAkB;AAAA,MACtB,OAA4C,mBAAmB;AAAA,IACjE;AACA,IAAM,cAAc,OAAO,OAA4C,eAAe,EAAE;AACxF,IAAM,cAAc,OAAO,OAA4C,eAAe,EAAE;AACxF,IAAM,eAAe,OAAO,OAA4C,gBAAgB,EAAE;AAC1F,IAAM,oBAAoB;AAAA,MACxB,OAA4C,oBAAoB;AAAA,IAClE;AACA,IAAM,cAAc,OAAO,OAA4C,eAAe,EAAE;AACxF,IAAM,kBAAkB;AAAA,MACtB,OAA4C,mBAAmB;AAAA,IACjE;AACA,IAAM,kBAAkB;AAAA,MACtB,OAA4C,mBAAmB;AAAA,IACjE;AACA,IAAM,kBAAkB;AAAA,MACtB,OAA4C,mBAAmB;AAAA,IACjE;AACA,IAAM,uBAAuB;AAAA,MAC3B,OAA4C,uBAAuB;AAAA,IACrE;AACA,IAAM,cAAc,OAAO,OAA4C,eAAe,EAAE;AACxF,IAAM,WAAW,OAAO,OAA4C,aAAa,EAAE;AACnF,IAAM,aAAa,OAAO,OAA4C,eAAe,EAAE;AACvF,IAAM,iBAAiB;AAAA,MACrB,OAA4C,iBAAiB;AAAA,IAC/D;AACA,IAAM,qBAAqB;AAAA,MACzB,OAA4C,qBAAqB;AAAA,IACnE;AACA,IAAM,gBAAgB,OAAO,OAA4C,gBAAgB,EAAE;AAC3F,IAAM,eAAe,OAAO,OAA4C,eAAe,EAAE;AACzF,IAAM,WAAW,OAAO,OAA4C,YAAY,EAAE;AAClF,IAAM,QAAQ,OAAO,OAA4C,UAAU,EAAE;AAC7E,IAAM,SAAS,OAAO,OAA4C,UAAU,EAAE;AAC9E,IAAM,YAAY,OAAO,OAA4C,aAAa,EAAE;AACpF,IAAM,eAAe,OAAO,OAA4C,eAAe,EAAE;AACzF,IAAM,gBAAgB;AAAA,MACpB,CAAC,QAAQ,GAAG;AAAA,MACZ,CAAC,QAAQ,GAAG;AAAA,MACZ,CAAC,QAAQ,GAAG;AAAA,MACZ,CAAC,UAAU,GAAG;AAAA,MACd,CAAC,eAAe,GAAG;AAAA,MACnB,CAAC,UAAU,GAAG;AAAA,MACd,CAAC,YAAY,GAAG;AAAA,MAChB,CAAC,oBAAoB,GAAG;AAAA,MACxB,CAAC,YAAY,GAAG;AAAA,MAChB,CAAC,oBAAoB,GAAG;AAAA,MACxB,CAAC,cAAc,GAAG;AAAA,MAClB,CAAC,WAAW,GAAG;AAAA,MACf,CAAC,aAAa,GAAG;AAAA,MACjB,CAAC,iBAAiB,GAAG;AAAA,MACrB,CAAC,yBAAyB,GAAG;AAAA,MAC7B,CAAC,iBAAiB,GAAG;AAAA,MACrB,CAAC,cAAc,GAAG;AAAA,MAClB,CAAC,eAAe,GAAG;AAAA,MACnB,CAAC,WAAW,GAAG;AAAA,MACf,CAAC,WAAW,GAAG;AAAA,MACf,CAAC,YAAY,GAAG;AAAA,MAChB,CAAC,iBAAiB,GAAG;AAAA,MACrB,CAAC,WAAW,GAAG;AAAA,MACf,CAAC,eAAe,GAAG;AAAA,MACnB,CAAC,eAAe,GAAG;AAAA,MACnB,CAAC,eAAe,GAAG;AAAA,MACnB,CAAC,oBAAoB,GAAG;AAAA,MACxB,CAAC,WAAW,GAAG;AAAA,MACf,CAAC,QAAQ,GAAG;AAAA,MACZ,CAAC,UAAU,GAAG;AAAA,MACd,CAAC,cAAc,GAAG;AAAA,MAClB,CAAC,kBAAkB,GAAG;AAAA,MACtB,CAAC,aAAa,GAAG;AAAA,MACjB,CAAC,YAAY,GAAG;AAAA,MAChB,CAAC,QAAQ,GAAG;AAAA,MACZ,CAAC,KAAK,GAAG;AAAA,MACT,CAAC,MAAM,GAAG;AAAA,MACV,CAAC,SAAS,GAAG;AAAA,MACb,CAAC,YAAY,GAAG;AAAA,IAClB;AAOA,IAAM,aAAa;AAAA,MACjB,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,OAAO;AAAA,MACP,KAAK;AAAA,MACL,WAAW;AAAA,MACX,KAAK;AAAA,IACP;AACA,IAAM,YAAY;AAAA,MAChB,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,WAAW;AAAA,MACX,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,WAAW;AAAA,MACX,KAAK;AAAA,MACL,qBAAqB;AAAA,MACrB,KAAK;AAAA,MACL,iBAAiB;AAAA,MACjB,KAAK;AAAA,MACL,aAAa;AAAA,MACb,KAAK;AAAA,MACL,aAAa;AAAA,MACb,KAAK;AAAA,MACL,uBAAuB;AAAA,MACvB,KAAK;AAAA,MACL,MAAM;AAAA,MACN,KAAK;AAAA,MACL,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,aAAa;AAAA,MACb,MAAM;AAAA,MACN,cAAc;AAAA,MACd,MAAM;AAAA,MACN,sBAAsB;AAAA,MACtB,MAAM;AAAA,MACN,wBAAwB;AAAA,MACxB,MAAM;AAAA,MACN,eAAe;AAAA,MACf,MAAM;AAAA,MACN,uBAAuB;AAAA,MACvB,MAAM;AAAA,MACN,0BAA0B;AAAA,MAC1B,MAAM;AAAA,MACN,6BAA6B;AAAA,MAC7B,MAAM;AAAA,MACN,uBAAuB;AAAA,MACvB,MAAM;AAAA,MACN,sBAAsB;AAAA,MACtB,MAAM;AAAA,MACN,uBAAuB;AAAA,MACvB,MAAM;AAAA,MACN,mBAAmB;AAAA,MACnB,MAAM;AAAA,MACN,4BAA4B;AAAA,MAC5B,MAAM;AAAA,MACN,0BAA0B;AAAA,MAC1B,MAAM;AAAA,MACN,uBAAuB;AAAA,MACvB,MAAM;AAAA,IACR;AACA,IAAM,eAAe;AAAA,MACnB,WAAW;AAAA,MACX,KAAK;AAAA,MACL,aAAa;AAAA,MACb,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,YAAY;AAAA,MACZ,KAAK;AAAA,IACP;AACA,IAAM,gBAAgB;AAAA,MACpB,gBAAgB;AAAA,MAChB,KAAK;AAAA,MACL,kBAAkB;AAAA,MAClB,KAAK;AAAA,MACL,aAAa;AAAA,MACb,KAAK;AAAA,MACL,iBAAiB;AAAA,MACjB,KAAK;AAAA,IACP;AACA,IAAM,UAAU;AAAA,MACd,OAAO,EAAE,MAAM,GAAG,QAAQ,GAAG,QAAQ,EAAE;AAAA,MACvC,KAAK,EAAE,MAAM,GAAG,QAAQ,GAAG,QAAQ,EAAE;AAAA,MACrC,QAAQ;AAAA,IACV;AA2LA,IAAM,wBAAwB,IAAI,WAAW,CAAC,KAAK,GAAG,CAAC;AACvD,IAAM,yBAAyB,IAAI,WAAW,CAAC,KAAK,GAAG,CAAC;AAiBxD,IAAM,YAAY;AAAA,MAChB,OAAO,IAAI,WAAW,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;AAAA;AAAA,MAE9C,UAAU,IAAI,WAAW,CAAC,IAAI,IAAI,EAAE,CAAC;AAAA;AAAA,MAErC,YAAY,IAAI,WAAW,CAAC,IAAI,IAAI,EAAE,CAAC;AAAA;AAAA,MAEvC,WAAW,IAAI,WAAW,CAAC,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG,CAAC;AAAA;AAAA,MAE/D,UAAU,IAAI,WAAW,CAAC,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC;AAAA;AAAA,MAE1D,UAAU,IAAI,WAAW,CAAC,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC;AAAA;AAAA,MAE1D,aAAa,IAAI,WAAW;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA;AAAA,IAEH;AACA,IAAM,YAAN,MAAgB;AAAA,MACd,YAAYC,QAAO,KAAK;AACtB,aAAK,QAAQA;AACb,aAAK,MAAM;AAEX,aAAK,QAAQ;AAEb,aAAK,SAAS;AAEd,aAAK,eAAe;AAEpB,aAAK,QAAQ;AAEb,aAAK,cAAc;AAEnB,aAAK,YAAY;AAEjB,aAAK,WAAW;AAEhB,aAAK,QAAQ;AAEb,aAAK,SAAS;AAEd,aAAK,WAAW,CAAC;AACjB,aAAK,OAAO;AACZ,aAAK,gBAAgB;AACrB,aAAK,iBAAiB;AACtB,aAAK,iBAAiB;AACtB,aAAK,kBAAkB;AACvB,aAAK,gBAAgB;AAAA,MACvB;AAAA,MACA,IAAI,YAAY;AACd,eAAO,KAAK,SAAS,KAAK,KAAK,MAAM,WAAW;AAAA,MAClD;AAAA,MACA,QAAQ;AACN,aAAK,QAAQ;AACb,aAAK,OAAO;AACZ,aAAK,SAAS;AACd,aAAK,eAAe;AACpB,aAAK,QAAQ;AACb,aAAK,YAAY;AACjB,aAAK,WAAW;AAChB,aAAK,kBAAkB;AACvB,aAAK,SAAS,SAAS;AACvB,aAAK,gBAAgB;AACrB,aAAK,iBAAiB;AAAA,MACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,OAAO,OAAO;AACZ,YAAI,OAAO;AACX,YAAI,SAAS,QAAQ;AACrB,iBAAS,IAAI,KAAK,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAClD,gBAAM,eAAe,KAAK,SAAS,CAAC;AACpC,cAAI,QAAQ,cAAc;AACxB,mBAAO,IAAI;AACX,qBAAS,QAAQ;AACjB;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA,OAAO;AACL,eAAO,KAAK,OAAO,WAAW,KAAK,QAAQ,CAAC;AAAA,MAC9C;AAAA,MACA,UAAU,GAAG;AACX,YAAI,MAAM,IAAI;AACZ,cAAI,KAAK,QAAQ,KAAK,cAAc;AAClC,iBAAK,IAAI,OAAO,KAAK,cAAc,KAAK,KAAK;AAAA,UAC/C;AACA,eAAK,QAAQ;AACb,eAAK,eAAe,KAAK;AAAA,QAC3B,WAAW,CAAC,KAAK,UAAU,MAAM,KAAK,cAAc,CAAC,GAAG;AACtD,eAAK,QAAQ;AACb,eAAK,iBAAiB;AACtB,eAAK,uBAAuB,CAAC;AAAA,QAC/B;AAAA,MACF;AAAA,MACA,uBAAuB,GAAG;AACxB,YAAI,MAAM,KAAK,cAAc,KAAK,cAAc,GAAG;AACjD,cAAI,KAAK,mBAAmB,KAAK,cAAc,SAAS,GAAG;AACzD,kBAAM,QAAQ,KAAK,QAAQ,IAAI,KAAK,cAAc;AAClD,gBAAI,QAAQ,KAAK,cAAc;AAC7B,mBAAK,IAAI,OAAO,KAAK,cAAc,KAAK;AAAA,YAC1C;AACA,iBAAK,QAAQ;AACb,iBAAK,eAAe;AAAA,UACtB,OAAO;AACL,iBAAK;AAAA,UACP;AAAA,QACF,WAAW,KAAK,UAAU;AACxB,eAAK,QAAQ;AACb,eAAK,cAAc,CAAC;AAAA,QACtB,OAAO;AACL,eAAK,QAAQ;AACb,eAAK,UAAU,CAAC;AAAA,QAClB;AAAA,MACF;AAAA,MACA,mBAAmB,GAAG;AACpB,YAAI,MAAM,KAAK,eAAe,CAAC,GAAG;AAChC,eAAK,QAAQ;AACb,eAAK,iBAAiB;AACtB,eAAK,wBAAwB,CAAC;AAAA,QAChC;AAAA,MACF;AAAA,MACA,wBAAwB,GAAG;AACzB,YAAI,MAAM,KAAK,eAAe,KAAK,cAAc,GAAG;AAClD,cAAI,KAAK,mBAAmB,KAAK,eAAe,SAAS,GAAG;AAC1D,iBAAK,IAAI,gBAAgB,KAAK,cAAc,KAAK,QAAQ,CAAC;AAC1D,gBAAI,KAAK,UAAU;AACjB,mBAAK,QAAQ;AAAA,YACf,OAAO;AACL,mBAAK,QAAQ;AAAA,YACf;AACA,iBAAK,eAAe,KAAK,QAAQ;AAAA,UACnC,OAAO;AACL,iBAAK;AAAA,UACP;AAAA,QACF,OAAO;AACL,eAAK,QAAQ;AACb,eAAK,mBAAmB,CAAC;AAAA,QAC3B;AAAA,MACF;AAAA,MACA,0BAA0B,GAAG;AAC3B,cAAM,QAAQ,KAAK,kBAAkB,KAAK,gBAAgB;AAC1D,cAAM,UAAU;AAAA;AAAA,UAEd,kBAAkB,CAAC;AAAA;AAAA;AAAA,WAGlB,IAAI,QAAQ,KAAK,gBAAgB,KAAK,aAAa;AAAA;AAEtD,YAAI,CAAC,SAAS;AACZ,eAAK,WAAW;AAAA,QAClB,WAAW,CAAC,OAAO;AACjB,eAAK;AACL;AAAA,QACF;AACA,aAAK,gBAAgB;AACrB,aAAK,QAAQ;AACb,aAAK,eAAe,CAAC;AAAA,MACvB;AAAA;AAAA,MAEA,cAAc,GAAG;AACf,YAAI,KAAK,kBAAkB,KAAK,gBAAgB,QAAQ;AACtD,cAAI,MAAM,MAAM,aAAa,CAAC,GAAG;AAC/B,kBAAM,YAAY,KAAK,QAAQ,KAAK,gBAAgB;AACpD,gBAAI,KAAK,eAAe,WAAW;AACjC,oBAAM,cAAc,KAAK;AACzB,mBAAK,QAAQ;AACb,mBAAK,IAAI,OAAO,KAAK,cAAc,SAAS;AAC5C,mBAAK,QAAQ;AAAA,YACf;AACA,iBAAK,eAAe,YAAY;AAChC,iBAAK,sBAAsB,CAAC;AAC5B,iBAAK,WAAW;AAChB;AAAA,UACF;AACA,eAAK,gBAAgB;AAAA,QACvB;AACA,aAAK,IAAI,QAAQ,KAAK,gBAAgB,KAAK,aAAa,GAAG;AACzD,eAAK,iBAAiB;AAAA,QACxB,WAAW,KAAK,kBAAkB,GAAG;AACnC,cAAI,KAAK,oBAAoB,UAAU,YAAY,KAAK,oBAAoB,UAAU,eAAe,CAAC,KAAK,WAAW;AACpH,gBAAI,CAAC,KAAK,UAAU,MAAM,KAAK,cAAc,CAAC,GAAG;AAC/C,mBAAK,QAAQ;AACb,mBAAK,iBAAiB;AACtB,mBAAK,uBAAuB,CAAC;AAAA,YAC/B;AAAA,UACF,WAAW,KAAK,cAAc,EAAE,GAAG;AACjC,iBAAK,gBAAgB;AAAA,UACvB;AAAA,QACF,OAAO;AACL,eAAK,gBAAgB,OAAO,MAAM,EAAE;AAAA,QACtC;AAAA,MACF;AAAA,MACA,mBAAmB,GAAG;AACpB,YAAI,MAAM,UAAU,MAAM,KAAK,aAAa,GAAG;AAC7C,cAAI,EAAE,KAAK,kBAAkB,UAAU,MAAM,QAAQ;AACnD,iBAAK,QAAQ;AACb,iBAAK,kBAAkB,UAAU;AACjC,iBAAK,gBAAgB;AACrB,iBAAK,eAAe,KAAK,QAAQ;AAAA,UACnC;AAAA,QACF,OAAO;AACL,eAAK,gBAAgB;AACrB,eAAK,QAAQ;AACb,eAAK,mBAAmB,CAAC;AAAA,QAC3B;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,cAAc,GAAG;AACf,eAAO,EAAE,KAAK,QAAQ,KAAK,OAAO,QAAQ;AACxC,gBAAM,KAAK,KAAK,OAAO,WAAW,KAAK,KAAK;AAC5C,cAAI,OAAO,IAAI;AACb,iBAAK,SAAS,KAAK,KAAK,KAAK;AAAA,UAC/B;AACA,cAAI,OAAO,GAAG;AACZ,mBAAO;AAAA,UACT;AAAA,QACF;AACA,aAAK,QAAQ,KAAK,OAAO,SAAS;AAClC,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,mBAAmB,GAAG;AACpB,YAAI,MAAM,KAAK,gBAAgB,KAAK,aAAa,GAAG;AAClD,cAAI,EAAE,KAAK,kBAAkB,KAAK,gBAAgB,QAAQ;AACxD,gBAAI,KAAK,oBAAoB,UAAU,UAAU;AAC/C,mBAAK,IAAI,QAAQ,KAAK,cAAc,KAAK,QAAQ,CAAC;AAAA,YACpD,OAAO;AACL,mBAAK,IAAI,UAAU,KAAK,cAAc,KAAK,QAAQ,CAAC;AAAA,YACtD;AACA,iBAAK,gBAAgB;AACrB,iBAAK,eAAe,KAAK,QAAQ;AACjC,iBAAK,QAAQ;AAAA,UACf;AAAA,QACF,WAAW,KAAK,kBAAkB,GAAG;AACnC,cAAI,KAAK,cAAc,KAAK,gBAAgB,CAAC,CAAC,GAAG;AAC/C,iBAAK,gBAAgB;AAAA,UACvB;AAAA,QACF,WAAW,MAAM,KAAK,gBAAgB,KAAK,gBAAgB,CAAC,GAAG;AAC7D,eAAK,gBAAgB;AAAA,QACvB;AAAA,MACF;AAAA,MACA,aAAa,UAAU,QAAQ;AAC7B,aAAK,YAAY,UAAU,MAAM;AACjC,aAAK,QAAQ;AAAA,MACf;AAAA,MACA,YAAY,UAAU,QAAQ;AAC5B,aAAK,WAAW;AAChB,aAAK,kBAAkB;AACvB,aAAK,gBAAgB;AAAA,MACvB;AAAA,MACA,mBAAmB,GAAG;AACpB,YAAI,MAAM,IAAI;AACZ,eAAK,QAAQ;AACb,eAAK,eAAe,KAAK,QAAQ;AAAA,QACnC,WAAW,MAAM,IAAI;AACnB,eAAK,QAAQ;AACb,eAAK,eAAe,KAAK,QAAQ;AAAA,QACnC,WAAW,eAAe,CAAC,GAAG;AAC5B,eAAK,eAAe,KAAK;AACzB,cAAI,KAAK,SAAS,GAAG;AACnB,iBAAK,QAAQ;AAAA,UACf,WAAW,KAAK,WAAW;AACzB,iBAAK,QAAQ;AAAA,UACf,WAAW,CAAC,KAAK,OAAO;AACtB,gBAAI,MAAM,KAAK;AACb,mBAAK,QAAQ;AAAA,YACf,OAAO;AACL,mBAAK,QAAQ,MAAM,MAAM,KAAK;AAAA,YAChC;AAAA,UACF,OAAO;AACL,iBAAK,QAAQ;AAAA,UACf;AAAA,QACF,WAAW,MAAM,IAAI;AACnB,eAAK,QAAQ;AAAA,QACf,OAAO;AACL,eAAK,QAAQ;AACb,eAAK,UAAU,CAAC;AAAA,QAClB;AAAA,MACF;AAAA,MACA,eAAe,GAAG;AAChB,YAAI,kBAAkB,CAAC,GAAG;AACxB,eAAK,cAAc,CAAC;AAAA,QACtB;AAAA,MACF;AAAA,MACA,sBAAsB,GAAG;AACvB,YAAI,kBAAkB,CAAC,GAAG;AACxB,gBAAM,MAAM,KAAK,OAAO,MAAM,KAAK,cAAc,KAAK,KAAK;AAC3D,cAAI,QAAQ,YAAY;AACtB,iBAAK,YAAY,YAAY,OAAO,GAAG,GAAG,CAAC;AAAA,UAC7C;AACA,eAAK,cAAc,CAAC;AAAA,QACtB;AAAA,MACF;AAAA,MACA,cAAc,GAAG;AACf,aAAK,IAAI,cAAc,KAAK,cAAc,KAAK,KAAK;AACpD,aAAK,eAAe;AACpB,aAAK,QAAQ;AACb,aAAK,oBAAoB,CAAC;AAAA,MAC5B;AAAA,MACA,0BAA0B,GAAG;AAC3B,YAAI,aAAa,CAAC,EAAG;AAAA,iBAAW,MAAM,IAAI;AACxC,cAAI,MAAoD;AACtD,iBAAK,IAAI,MAAM,IAAI,KAAK,KAAK;AAAA,UAC/B;AACA,eAAK,QAAQ;AACb,eAAK,eAAe,KAAK,QAAQ;AAAA,QACnC,OAAO;AACL,eAAK,QAAQ,eAAe,CAAC,IAAI,IAAI;AACrC,eAAK,eAAe,KAAK;AAAA,QAC3B;AAAA,MACF;AAAA,MACA,sBAAsB,GAAG;AACvB,YAAI,MAAM,MAAM,aAAa,CAAC,GAAG;AAC/B,eAAK,IAAI,WAAW,KAAK,cAAc,KAAK,KAAK;AACjD,eAAK,eAAe;AACpB,eAAK,QAAQ;AACb,eAAK,yBAAyB,CAAC;AAAA,QACjC;AAAA,MACF;AAAA,MACA,yBAAyB,GAAG;AAC1B,YAAI,MAAM,IAAI;AACZ,eAAK,QAAQ;AACb,eAAK,eAAe,KAAK,QAAQ;AAAA,QACnC;AAAA,MACF;AAAA,MACA,oBAAoB,GAAG;AACrB,YAAI,MAAM,IAAI;AACZ,eAAK,IAAI,aAAa,KAAK,KAAK;AAChC,cAAI,KAAK,UAAU;AACjB,iBAAK,QAAQ;AAAA,UACf,OAAO;AACL,iBAAK,QAAQ;AAAA,UACf;AACA,eAAK,eAAe,KAAK,QAAQ;AAAA,QACnC,WAAW,MAAM,IAAI;AACnB,eAAK,QAAQ;AACb,cAA4D,KAAK,KAAK,MAAM,IAAI;AAC9E,iBAAK,IAAI,MAAM,IAAI,KAAK,KAAK;AAAA,UAC/B;AAAA,QACF,WAAW,MAAM,MAAM,KAAK,KAAK,MAAM,IAAI;AACzC,eAAK,IAAI,aAAa,KAAK,KAAK;AAChC,eAAK,QAAQ;AACb,eAAK,eAAe,KAAK;AAAA,QAC3B,WAAW,CAAC,aAAa,CAAC,GAAG;AAC3B,cAA4D,MAAM,IAAI;AACpE,iBAAK,IAAI;AAAA,cACP;AAAA,cACA,KAAK;AAAA,YACP;AAAA,UACF;AACA,eAAK,gBAAgB,CAAC;AAAA,QACxB;AAAA,MACF;AAAA,MACA,gBAAgB,GAAG;AACjB,YAAI,MAAM,OAAO,KAAK,KAAK,MAAM,IAAI;AACnC,eAAK,QAAQ;AACb,eAAK,eAAe,KAAK;AAAA,QAC3B,WAAW,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AACvD,eAAK,IAAI,UAAU,KAAK,OAAO,KAAK,QAAQ,CAAC;AAC7C,eAAK,QAAQ;AACb,eAAK,eAAe,KAAK,QAAQ;AAAA,QACnC,OAAO;AACL,eAAK,QAAQ;AACb,eAAK,eAAe,KAAK;AAAA,QAC3B;AAAA,MACF;AAAA,MACA,sBAAsB,GAAG;AACvB,YAAI,MAAM,IAAI;AACZ,eAAK,IAAI,iBAAiB,KAAK,KAAK;AACpC,eAAK,QAAQ;AACb,eAAK,eAAe,KAAK,QAAQ;AACjC,eAAK,WAAW;AAAA,QAClB,WAAW,CAAC,aAAa,CAAC,GAAG;AAC3B,eAAK,QAAQ;AACb,eAAK,oBAAoB,CAAC;AAAA,QAC5B;AAAA,MACF;AAAA,MACA,gBAAgB,GAAG;AACjB,YAAI,MAAM,MAAM,kBAAkB,CAAC,GAAG;AACpC,eAAK,IAAI,aAAa,KAAK,cAAc,KAAK,KAAK;AACnD,eAAK,kBAAkB,CAAC;AAAA,QAC1B,WAAoE,MAAM,MAAM,MAAM,MAAM,MAAM,IAAK;AACrG,eAAK,IAAI;AAAA,YACP;AAAA,YACA,KAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,MACA,eAAe,GAAG;AAChB,YAAI,MAAM,MAAM,kBAAkB,CAAC,GAAG;AACpC,eAAK,IAAI,UAAU,KAAK,cAAc,KAAK,KAAK;AAChD,eAAK,kBAAkB,CAAC;AAAA,QAC1B,WAAW,MAAM,IAAI;AACnB,eAAK,IAAI,UAAU,KAAK,cAAc,KAAK,KAAK;AAChD,eAAK,QAAQ;AACb,eAAK,eAAe,KAAK,QAAQ;AAAA,QACnC,WAAW,MAAM,IAAI;AACnB,eAAK,IAAI,UAAU,KAAK,cAAc,KAAK,KAAK;AAChD,eAAK,QAAQ;AACb,eAAK,eAAe,KAAK,QAAQ;AAAA,QACnC;AAAA,MACF;AAAA,MACA,cAAc,GAAG;AACf,YAAI,MAAM,MAAM,kBAAkB,CAAC,GAAG;AACpC,eAAK,IAAI,SAAS,KAAK,cAAc,KAAK,KAAK;AAC/C,eAAK,kBAAkB,CAAC;AAAA,QAC1B,WAAW,MAAM,IAAI;AACnB,eAAK,QAAQ;AAAA,QACf,WAAW,MAAM,IAAI;AACnB,eAAK,IAAI,SAAS,KAAK,cAAc,KAAK,KAAK;AAC/C,eAAK,QAAQ;AACb,eAAK,eAAe,KAAK,QAAQ;AAAA,QACnC;AAAA,MACF;AAAA,MACA,qBAAqB,GAAG;AACtB,YAAI,MAAM,IAAI;AACZ,eAAK,QAAQ;AAAA,QACf,WAAW,MAAM,MAAM,kBAAkB,CAAC,GAAG;AAC3C,eAAK,IAAI,SAAS,KAAK,cAAc,KAAK,QAAQ,CAAC;AACnD,eAAK,kBAAkB,CAAC;AACxB,cAAI,MAAoD;AACtD,iBAAK,IAAI;AAAA,cACP;AAAA,cACA,KAAK;AAAA,YACP;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,mBAAmB,GAAG;AACpB,YAAI,MAAM,MAAM,kBAAkB,CAAC,GAAG;AACpC,eAAK,IAAI,cAAc,KAAK,cAAc,KAAK,KAAK;AACpD,eAAK,kBAAkB,CAAC;AAAA,QAC1B,WAAW,MAAM,IAAI;AACnB,eAAK,IAAI,cAAc,KAAK,cAAc,KAAK,KAAK;AACpD,eAAK,eAAe,KAAK,QAAQ;AAAA,QACnC;AAAA,MACF;AAAA,MACA,kBAAkB,GAAG;AACnB,aAAK,eAAe,KAAK;AACzB,aAAK,QAAQ;AACb,aAAK,IAAI,gBAAgB,KAAK,KAAK;AACnC,aAAK,mBAAmB,CAAC;AAAA,MAC3B;AAAA,MACA,mBAAmB,GAAG;AACpB,YAAI,MAAM,IAAI;AACZ,eAAK,QAAQ;AAAA,QACf,WAAW,MAAM,MAAM,MAAM,IAAI;AAC/B,eAAK,IAAI,YAAY,GAAG,KAAK,YAAY;AACzC,eAAK,eAAe;AACpB,eAAK,QAAQ;AACb,eAAK,oBAAoB,CAAC;AAAA,QAC5B,WAAW,CAAC,aAAa,CAAC,GAAG;AAC3B,eAAK,IAAI,YAAY,GAAG,KAAK,YAAY;AACzC,eAAK,gBAAgB,CAAC;AAAA,QACxB;AAAA,MACF;AAAA,MACA,qBAAqB,GAAG;AACtB,YAAI,MAAM,IAAI;AACZ,eAAK,QAAQ;AACb,eAAK,eAAe,KAAK,QAAQ;AAAA,QACnC,WAAW,MAAM,IAAI;AACnB,eAAK,QAAQ;AACb,eAAK,eAAe,KAAK,QAAQ;AAAA,QACnC,WAAW,CAAC,aAAa,CAAC,GAAG;AAC3B,eAAK,eAAe,KAAK;AACzB,eAAK,QAAQ;AACb,eAAK,yBAAyB,CAAC;AAAA,QACjC;AAAA,MACF;AAAA,MACA,kBAAkB,GAAG,OAAO;AAC1B,YAAI,MAAM,SAAS,KAAK,cAAc,KAAK,GAAG;AAC5C,eAAK,IAAI,aAAa,KAAK,cAAc,KAAK,KAAK;AACnD,eAAK,eAAe;AACpB,eAAK,IAAI;AAAA,YACP,UAAU,KAAK,IAAI;AAAA,YACnB,KAAK,QAAQ;AAAA,UACf;AACA,eAAK,QAAQ;AAAA,QACf;AAAA,MACF;AAAA,MACA,6BAA6B,GAAG;AAC9B,aAAK,kBAAkB,GAAG,EAAE;AAAA,MAC9B;AAAA,MACA,6BAA6B,GAAG;AAC9B,aAAK,kBAAkB,GAAG,EAAE;AAAA,MAC9B;AAAA,MACA,yBAAyB,GAAG;AAC1B,YAAI,aAAa,CAAC,KAAK,MAAM,IAAI;AAC/B,eAAK,IAAI,aAAa,KAAK,cAAc,KAAK,KAAK;AACnD,eAAK,eAAe;AACpB,eAAK,IAAI,YAAY,GAAG,KAAK,KAAK;AAClC,eAAK,QAAQ;AACb,eAAK,oBAAoB,CAAC;AAAA,QAC5B,WAAmE,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAC3H,eAAK,IAAI;AAAA,YACP;AAAA,YACA,KAAK;AAAA,UACP;AAAA,QACF,MAAO;AAAA,MACT;AAAA,MACA,uBAAuB,GAAG;AACxB,YAAI,MAAM,IAAI;AACZ,eAAK,QAAQ;AACb,eAAK,gBAAgB;AAAA,QACvB,OAAO;AACL,eAAK,QAAQ,MAAM,KAAK,KAAK;AAAA,QAC/B;AAAA,MACF;AAAA,MACA,mBAAmB,GAAG;AACpB,YAAI,MAAM,MAAM,KAAK,cAAc,EAAE,GAAG;AACtC,eAAK,QAAQ;AACb,eAAK,eAAe,KAAK,QAAQ;AAAA,QACnC;AAAA,MACF;AAAA,MACA,6BAA6B,GAAG;AAC9B,YAAI,MAAM,MAAM,KAAK,cAAc,EAAE,GAAG;AACtC,eAAK,IAAI,wBAAwB,KAAK,cAAc,KAAK,KAAK;AAC9D,eAAK,QAAQ;AACb,eAAK,eAAe,KAAK,QAAQ;AAAA,QACnC;AAAA,MACF;AAAA,MACA,mBAAmB,GAAG;AACpB,YAAI,MAAM,IAAI;AACZ,eAAK,QAAQ;AACb,eAAK,kBAAkB,UAAU;AACjC,eAAK,gBAAgB;AACrB,eAAK,eAAe,KAAK,QAAQ;AAAA,QACnC,OAAO;AACL,eAAK,QAAQ;AAAA,QACf;AAAA,MACF;AAAA,MACA,sBAAsB,GAAG;AACvB,YAAI,MAAM,MAAM,KAAK,cAAc,EAAE,GAAG;AACtC,eAAK,IAAI,UAAU,KAAK,cAAc,KAAK,KAAK;AAChD,eAAK,QAAQ;AACb,eAAK,eAAe,KAAK,QAAQ;AAAA,QACnC;AAAA,MACF;AAAA,MACA,oBAAoB,GAAG;AACrB,YAAI,MAAM,UAAU,UAAU,CAAC,GAAG;AAChC,eAAK,aAAa,UAAU,WAAW,CAAC;AAAA,QAC1C,WAAW,MAAM,UAAU,SAAS,CAAC,GAAG;AACtC,eAAK,aAAa,UAAU,UAAU,CAAC;AAAA,QACzC,OAAO;AACL,eAAK,QAAQ;AACb,eAAK,eAAe,CAAC;AAAA,QACvB;AAAA,MACF;AAAA,MACA,oBAAoB,GAAG;AACrB,YAAI,MAAM,UAAU,SAAS,CAAC,GAAG;AAC/B,eAAK,aAAa,UAAU,UAAU,CAAC;AAAA,QACzC,WAAW,MAAM,UAAU,YAAY,CAAC,GAAG;AACzC,eAAK,aAAa,UAAU,aAAa,CAAC;AAAA,QAC5C,OAAO;AACL,eAAK,QAAQ;AACb,eAAK,eAAe,CAAC;AAAA,QACvB;AAAA,MACF;AAAA,MACA,cAAc;AAAA,MACd;AAAA,MACA,gBAAgB;AAAA,MAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,MAAM,OAAO;AACX,aAAK,SAAS;AACd,eAAO,KAAK,QAAQ,KAAK,OAAO,QAAQ;AACtC,gBAAM,IAAI,KAAK,OAAO,WAAW,KAAK,KAAK;AAC3C,cAAI,MAAM,IAAI;AACZ,iBAAK,SAAS,KAAK,KAAK,KAAK;AAAA,UAC/B;AACA,kBAAQ,KAAK,OAAO;AAAA,YAClB,KAAK,GAAG;AACN,mBAAK,UAAU,CAAC;AAChB;AAAA,YACF;AAAA,YACA,KAAK,GAAG;AACN,mBAAK,uBAAuB,CAAC;AAC7B;AAAA,YACF;AAAA,YACA,KAAK,GAAG;AACN,mBAAK,mBAAmB,CAAC;AACzB;AAAA,YACF;AAAA,YACA,KAAK,GAAG;AACN,mBAAK,wBAAwB,CAAC;AAC9B;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,0BAA0B,CAAC;AAChC;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,cAAc,CAAC;AACpB;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,mBAAmB,CAAC;AACzB;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,6BAA6B,CAAC;AACnC;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,gBAAgB,CAAC;AACtB;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,eAAe,CAAC;AACrB;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,cAAc,CAAC;AACpB;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,qBAAqB,CAAC;AAC3B;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,mBAAmB,CAAC;AACzB;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,mBAAmB,CAAC;AACzB;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,sBAAsB,CAAC;AAC5B;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,oBAAoB,CAAC;AAC1B;AAAA,YACF;AAAA,YACA,KAAK,GAAG;AACN,mBAAK,eAAe,CAAC;AACrB;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,sBAAsB,CAAC;AAC5B;AAAA,YACF;AAAA,YACA,KAAK,GAAG;AACN,mBAAK,sBAAsB,CAAC;AAC5B;AAAA,YACF;AAAA,YACA,KAAK,GAAG;AACN,mBAAK,mBAAmB,CAAC;AACzB;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,mBAAmB,CAAC;AACzB;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,6BAA6B,CAAC;AACnC;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,qBAAqB,CAAC;AAC3B;AAAA,YACF;AAAA,YACA,KAAK,GAAG;AACN,mBAAK,0BAA0B,CAAC;AAChC;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,yBAAyB,CAAC;AAC/B;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,oBAAoB,CAAC;AAC1B;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,oBAAoB,CAAC;AAC1B;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,yBAAyB,CAAC;AAC/B;AAAA,YACF;AAAA,YACA,KAAK,GAAG;AACN,mBAAK,sBAAsB,CAAC;AAC5B;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,mBAAmB,CAAC;AACzB;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,uBAAuB,CAAC;AAC7B;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,mBAAmB,CAAC;AACzB;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,6BAA6B,CAAC;AACnC;AAAA,YACF;AAAA,YACA,KAAK,IAAI;AACP,mBAAK,cAAc;AACnB;AAAA,YACF;AAAA,UACF;AACA,eAAK;AAAA,QACP;AACA,aAAK,QAAQ;AACb,aAAK,OAAO;AAAA,MACd;AAAA;AAAA;AAAA;AAAA,MAIA,UAAU;AACR,YAAI,KAAK,iBAAiB,KAAK,OAAO;AACpC,cAAI,KAAK,UAAU,KAAK,KAAK,UAAU,MAAM,KAAK,kBAAkB,GAAG;AACrE,iBAAK,IAAI,OAAO,KAAK,cAAc,KAAK,KAAK;AAC7C,iBAAK,eAAe,KAAK;AAAA,UAC3B,WAAW,KAAK,UAAU,MAAM,KAAK,UAAU,MAAM,KAAK,UAAU,IAAI;AACtE,iBAAK,IAAI,aAAa,KAAK,cAAc,KAAK,KAAK;AACnD,iBAAK,eAAe,KAAK;AAAA,UAC3B;AAAA,QACF;AAAA,MACF;AAAA,MACA,SAAS;AACP,aAAK,mBAAmB;AACxB,aAAK,IAAI,MAAM;AAAA,MACjB;AAAA;AAAA,MAEA,qBAAqB;AACnB,cAAM,WAAW,KAAK,OAAO;AAC7B,YAAI,KAAK,gBAAgB,UAAU;AACjC;AAAA,QACF;AACA,YAAI,KAAK,UAAU,IAAI;AACrB,cAAI,KAAK,oBAAoB,UAAU,UAAU;AAC/C,iBAAK,IAAI,QAAQ,KAAK,cAAc,QAAQ;AAAA,UAC9C,OAAO;AACL,iBAAK,IAAI,UAAU,KAAK,cAAc,QAAQ;AAAA,UAChD;AAAA,QACF,WAAW,KAAK,UAAU,KAAK,KAAK,UAAU,MAAM,KAAK,UAAU,MAAM,KAAK,UAAU,MAAM,KAAK,UAAU,MAAM,KAAK,UAAU,MAAM,KAAK,UAAU,MAAM,KAAK,UAAU,MAAM,KAAK,UAAU,MAAM,KAAK,UAAU,MAAM,KAAK,UAAU,MAAM,KAAK,UAAU,MAAM,KAAK,UAAU,EAAG;AAAA,aAAO;AAC7R,eAAK,IAAI,OAAO,KAAK,cAAc,QAAQ;AAAA,QAC7C;AAAA,MACF;AAAA,MACA,cAAc,IAAI,UAAU;AAAA,MAC5B;AAAA,IACF;AAEA,IAAM,2BAA2B;AAAA,MAC/B,0BAA0B;AAAA,MAC1B,wBAAwB;AAAA,MACxB,gCAAgC;AAAA,MAChC,wBAAwB;AAAA,MACxB,kCAAkC;AAAA,MAClC,4BAA4B;AAAA,MAC5B,4BAA4B;AAAA,MAC5B,oBAAoB;AAAA,IACtB;AACA,IAAM,kBAAkB;AAAA,MACtB,CAAC,wBAAwB,GAAG;AAAA,QAC1B,SAAS;AAAA,QACT,MAAM;AAAA,MACR;AAAA,MACA,CAAC,sBAAsB,GAAG;AAAA,QACxB,SAAS,CAAC,QAAQ,2FAA2F,GAAG,0CAA0C,GAAG;AAAA,QAC7J,MAAM;AAAA,MACR;AAAA,MACA,CAAC,8BAA8B,GAAG;AAAA,QAChC,SAAS;AAAA,QACT,MAAM;AAAA,MACR;AAAA,MACA,CAAC,sBAAsB,GAAG;AAAA,QACxB,SAAS;AAAA,QACT,MAAM;AAAA,MACR;AAAA,MACA,CAAC,gCAAgC,GAAG;AAAA,QAClC,SAAS;AAAA,QACT,MAAM;AAAA,MACR;AAAA,MACA,CAAC,0BAA0B,GAAG;AAAA,QAC5B,SAAS;AAAA,MACX;AAAA,MACA,CAAC,0BAA0B,GAAG;AAAA,QAC5B,SAAS;AAAA,QACT,MAAM;AAAA,MACR;AAAA,MACA,CAAC,kBAAkB,GAAG;AAAA,QACpB,SAAS;AAAA,QACT,MAAM;AAAA,MACR;AAAA,IACF;AAgDA,IAAM,aAAa;AAAA,MACjB,mCAAmC;AAAA,MACnC,KAAK;AAAA,MACL,yBAAyB;AAAA,MACzB,KAAK;AAAA,MACL,uBAAuB;AAAA,MACvB,KAAK;AAAA,MACL,2BAA2B;AAAA,MAC3B,KAAK;AAAA,MACL,iCAAiC;AAAA,MACjC,KAAK;AAAA,MACL,uBAAuB;AAAA,MACvB,KAAK;AAAA,MACL,gBAAgB;AAAA,MAChB,KAAK;AAAA,MACL,kBAAkB;AAAA,MAClB,KAAK;AAAA,MACL,wCAAwC;AAAA,MACxC,KAAK;AAAA,MACL,cAAc;AAAA,MACd,KAAK;AAAA,MACL,8BAA8B;AAAA,MAC9B,MAAM;AAAA,MACN,8BAA8B;AAAA,MAC9B,MAAM;AAAA,MACN,uCAAuC;AAAA,MACvC,MAAM;AAAA,MACN,2BAA2B;AAAA,MAC3B,MAAM;AAAA,MACN,wBAAwB;AAAA,MACxB,MAAM;AAAA,MACN,yCAAyC;AAAA,MACzC,MAAM;AAAA,MACN,kBAAkB;AAAA,MAClB,MAAM;AAAA,MACN,0CAA0C;AAAA,MAC1C,MAAM;AAAA,MACN,oDAAoD;AAAA,MACpD,MAAM;AAAA,MACN,gDAAgD;AAAA,MAChD,MAAM;AAAA,MACN,6BAA6B;AAAA,MAC7B,MAAM;AAAA,MACN,gDAAgD;AAAA,MAChD,MAAM;AAAA,MACN,6BAA6B;AAAA,MAC7B,MAAM;AAAA,MACN,qBAAqB;AAAA,MACrB,MAAM;AAAA,MACN,qBAAqB;AAAA,MACrB,MAAM;AAAA,MACN,+BAA+B;AAAA,MAC/B,MAAM;AAAA,MACN,4BAA4B;AAAA,MAC5B,MAAM;AAAA,MACN,4CAA4C;AAAA,MAC5C,MAAM;AAAA,MACN,wBAAwB;AAAA,MACxB,MAAM;AAAA,MACN,mBAAmB;AAAA,MACnB,MAAM;AAAA,MACN,2BAA2B;AAAA,MAC3B,MAAM;AAAA,MACN,yBAAyB;AAAA,MACzB,MAAM;AAAA,MACN,gCAAgC;AAAA,MAChC,MAAM;AAAA,MACN,kCAAkC;AAAA,MAClC,MAAM;AAAA,MACN,0BAA0B;AAAA,MAC1B,MAAM;AAAA,MACN,wBAAwB;AAAA,MACxB,MAAM;AAAA,MACN,gDAAgD;AAAA,MAChD,MAAM;AAAA,MACN,6BAA6B;AAAA,MAC7B,MAAM;AAAA,MACN,iCAAiC;AAAA,MACjC,MAAM;AAAA,MACN,6CAA6C;AAAA,MAC7C,MAAM;AAAA,MACN,sBAAsB;AAAA,MACtB,MAAM;AAAA,MACN,2BAA2B;AAAA,MAC3B,MAAM;AAAA,MACN,kCAAkC;AAAA,MAClC,MAAM;AAAA,MACN,+BAA+B;AAAA,MAC/B,MAAM;AAAA,MACN,sBAAsB;AAAA,MACtB,MAAM;AAAA,MACN,wBAAwB;AAAA,MACxB,MAAM;AAAA,MACN,iCAAiC;AAAA,MACjC,MAAM;AAAA,MACN,6BAA6B;AAAA,MAC7B,MAAM;AAAA,MACN,+BAA+B;AAAA,MAC/B,MAAM;AAAA,MACN,iCAAiC;AAAA,MACjC,MAAM;AAAA,MACN,4BAA4B;AAAA,MAC5B,MAAM;AAAA,MACN,iBAAiB;AAAA,MACjB,MAAM;AAAA,MACN,uCAAuC;AAAA,MACvC,MAAM;AAAA,MACN,oBAAoB;AAAA,MACpB,MAAM;AAAA,IACR;AACA,IAAM,gBAAgB;AAAA;AAAA,MAEpB,CAAC,CAAC,GAAG;AAAA,MACL,CAAC,CAAC,GAAG;AAAA,MACL,CAAC,CAAC,GAAG;AAAA,MACL,CAAC,CAAC,GAAG;AAAA,MACL,CAAC,CAAC,GAAG;AAAA,MACL,CAAC,CAAC,GAAG;AAAA,MACL,CAAC,CAAC,GAAG;AAAA,MACL,CAAC,CAAC,GAAG;AAAA,MACL,CAAC,CAAC,GAAG;AAAA,MACL,CAAC,CAAC,GAAG;AAAA,MACL,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA;AAAA,MAEN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA;AAAA,MAEN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA;AAAA,MAEN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA;AAAA,MAEN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA;AAAA,MAEN,CAAC,EAAE,GAAG;AAAA,IACR;AA8GA,IAAM,iBAAiB,CAAC,SAAS;AAC/B,aAAO,8CAA8C,KAAK,KAAK,IAAI;AAAA,IACrE;AACA,IAAM,mBAAmB,CAAC,SAAS,SAAS,KAAK,SAAS,oBAAoB,KAAK,SAAS,mBAAmB,CAAC,KAAK;AACrH,IAAM,sBAAsB,CAAC,MAAM,WAAW,iBAAiB,MAAM,KAAK,OAAO,QAAQ;AACzF,IAAM,gBAAgB;AAAA,MACpB;AAAA;AAAA,MAEA;AAAA;AAAA,MAEA;AAAA;AAAA,MAEA;AAAA;AAAA,MAEA;AAAA;AAAA,IAEF;AASA,IAAM,cAAc,CAAC,MAAM,EAAE,SAAS,KAAK,EAAE;AAiB7C,IAAM,kBAAkB;AACxB,IAAM,qBAAqB,CAAC,SAAS,CAAC,gBAAgB,KAAK,IAAI;AAC/D,IAAM,wBAAwB;AAC9B,IAAM,mBAAmB;AACzB,IAAM,eAAe;AACrB,IAAM,eAAe,CAAC,QAAQ,IAAI,SAAS,IAAI,IAAI,UAAU,IAAI,IAAI;AACrE,IAAM,4BAA4B,CAAC,QAAQ;AACzC,YAAM,OAAO,aAAa,GAAG,EAAE,KAAK,EAAE,QAAQ,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC;AAC3E,UAAI,QAAQ;AACZ,UAAI,aAAa,CAAC;AAClB,UAAI,0BAA0B;AAC9B,UAAI,yBAAyB;AAC7B,UAAI,oBAAoB;AACxB,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAM,OAAO,KAAK,OAAO,CAAC;AAC1B,gBAAQ,OAAO;AAAA,UACb,KAAK;AACH,gBAAI,SAAS,KAAK;AAChB,yBAAW,KAAK,KAAK;AACrB,sBAAQ;AACR;AAAA,YACF,WAAW,SAAS,KAAK;AACvB,yBAAW,KAAK,KAAK;AACrB,sBAAQ;AACR;AAAA,YACF,WAAW,EAAE,MAAM,IAAI,wBAAwB,kBAAkB,KAAK,IAAI,GAAG;AAC3E,qBAAO;AAAA,YACT;AACA;AAAA,UACF,KAAK;AACH,gBAAI,SAAS,OAAO,SAAS,OAAO,SAAS,KAAK;AAChD,yBAAW,KAAK,KAAK;AACrB,sBAAQ;AACR,kCAAoB;AAAA,YACtB,WAAW,SAAS,KAAK;AACvB;AAAA,YACF,WAAW,SAAS,KAAK;AACvB,kBAAI,CAAC,EAAE,yBAAyB;AAC9B,wBAAQ,WAAW,IAAI;AAAA,cACzB;AAAA,YACF;AACA;AAAA,UACF,KAAK;AACH,gBAAI,SAAS,OAAO,SAAS,OAAO,SAAS,KAAK;AAChD,yBAAW,KAAK,KAAK;AACrB,sBAAQ;AACR,kCAAoB;AAAA,YACtB,WAAW,SAAS,KAAK;AACvB;AAAA,YACF,WAAW,SAAS,KAAK;AACvB,kBAAI,MAAM,KAAK,SAAS,GAAG;AACzB,uBAAO;AAAA,cACT;AACA,kBAAI,CAAC,EAAE,wBAAwB;AAC7B,wBAAQ,WAAW,IAAI;AAAA,cACzB;AAAA,YACF;AACA;AAAA,UACF,KAAK;AACH,gBAAI,SAAS,mBAAmB;AAC9B,sBAAQ,WAAW,IAAI;AACvB,kCAAoB;AAAA,YACtB;AACA;AAAA,QACJ;AAAA,MACF;AACA,aAAO,CAAC,2BAA2B,CAAC;AAAA,IACtC;AACA,IAAM,yBAAyB;AAC/B,IAAM,qBAAqB;AAC3B,IAAM,UAAU;AAChB,IAAM,wBAAwB,CAAC,QAAQ,QAAQ,KAAK,aAAa,GAAG,CAAC;AACrE,IAAM,qBAAqB;AAC3B,IAAM,iBAAiB;AA2EvB,IAAM,iBAAiC,oBAAI,IAAI,CAAC,iBAAiB,oBAAoB,CAAC;AAsItF,IAAM,aAAa;AAEnB,IAAM,uBAAuB;AAAA,MAC3B,WAAW;AAAA,MACX,IAAI;AAAA,MACJ,YAAY,CAAC,MAAM,IAAI;AAAA,MACvB,cAAc,MAAM;AAAA,MACpB,WAAW;AAAA,MACX,UAAU;AAAA,MACV,oBAAoB;AAAA,MACpB,iBAAiB;AAAA,MACjB,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,mBAAmB;AAAA,IACrB;AACA,IAAI,iBAAiB;AACrB,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,iBAAiB;AACrB,IAAI,cAAc;AAClB,IAAI,mBAAmB;AACvB,IAAI,wBAAwB;AAC5B,IAAI,sBAAsB;AAC1B,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,sBAAsB;AAC1B,IAAM,QAAQ,CAAC;AACf,IAAM,YAAY,IAAI,UAAU,OAAO;AAAA,MACrC,OAAO;AAAA,MACP,OAAO,OAAO,KAAK;AACjB,eAAO,SAAS,OAAO,GAAG,GAAG,OAAO,GAAG;AAAA,MACzC;AAAA,MACA,aAAa,MAAM,OAAO,KAAK;AAC7B,eAAO,MAAM,OAAO,GAAG;AAAA,MACzB;AAAA,MACA,gBAAgB,OAAO,KAAK;AAC1B,YAAI,QAAQ;AACV,iBAAO,OAAO,SAAS,OAAO,GAAG,GAAG,OAAO,GAAG;AAAA,QAChD;AACA,YAAI,aAAa,QAAQ,UAAU,cAAc;AACjD,YAAI,WAAW,MAAM,UAAU,eAAe;AAC9C,eAAO,aAAa,aAAa,WAAW,UAAU,CAAC,GAAG;AACxD;AAAA,QACF;AACA,eAAO,aAAa,aAAa,WAAW,WAAW,CAAC,CAAC,GAAG;AAC1D;AAAA,QACF;AACA,YAAI,MAAM,SAAS,YAAY,QAAQ;AACvC,YAAI,IAAI,SAAS,GAAG,GAAG;AACrB;AACE,kBAAM,eAAe,eAAe,KAAK,KAAK;AAAA,UAChD;AAAA,QACF;AACA,gBAAQ;AAAA,UACN,MAAM;AAAA,UACN,SAAS,UAAU,KAAK,OAAO,OAAO,YAAY,QAAQ,CAAC;AAAA,UAC3D,KAAK,OAAO,OAAO,GAAG;AAAA,QACxB,CAAC;AAAA,MACH;AAAA,MACA,cAAc,OAAO,KAAK;AACxB,cAAM,OAAO,SAAS,OAAO,GAAG;AAChC,yBAAiB;AAAA,UACf,MAAM;AAAA,UACN,KAAK;AAAA,UACL,IAAI,eAAe,aAAa,MAAM,MAAM,CAAC,GAAG,eAAe,EAAE;AAAA,UACjE,SAAS;AAAA;AAAA,UAET,OAAO,CAAC;AAAA,UACR,UAAU,CAAC;AAAA,UACX,KAAK,OAAO,QAAQ,GAAG,GAAG;AAAA,UAC1B,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,aAAa,KAAK;AAChB,mBAAW,GAAG;AAAA,MAChB;AAAA,MACA,WAAW,OAAO,KAAK;AACrB,cAAM,OAAO,SAAS,OAAO,GAAG;AAChC,YAAI,CAAC,eAAe,UAAU,IAAI,GAAG;AACnC,cAAI,QAAQ;AACZ,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,kBAAM,IAAI,MAAM,CAAC;AACjB,gBAAI,EAAE,IAAI,YAAY,MAAM,KAAK,YAAY,GAAG;AAC9C,sBAAQ;AACR,kBAAI,IAAI,GAAG;AACT,0BAAU,IAAI,MAAM,CAAC,EAAE,IAAI,MAAM,MAAM;AAAA,cACzC;AACA,uBAAS,IAAI,GAAG,KAAK,GAAG,KAAK;AAC3B,sBAAM,KAAK,MAAM,MAAM;AACvB,2BAAW,IAAI,KAAK,IAAI,CAAC;AAAA,cAC3B;AACA;AAAA,YACF;AAAA,UACF;AACA,cAAI,CAAC,OAAO;AACV,sBAAU,IAAI,UAAU,OAAO,EAAE,CAAC;AAAA,UACpC;AAAA,QACF;AAAA,MACF;AAAA,MACA,iBAAiB,KAAK;AACpB,cAAM,OAAO,eAAe;AAC5B,uBAAe,gBAAgB;AAC/B,mBAAW,GAAG;AACd,YAAI,MAAM,CAAC,KAAK,MAAM,CAAC,EAAE,QAAQ,MAAM;AACrC,qBAAW,MAAM,MAAM,GAAG,GAAG;AAAA,QAC/B;AAAA,MACF;AAAA,MACA,aAAa,OAAO,KAAK;AACvB,sBAAc;AAAA,UACZ,MAAM;AAAA,UACN,MAAM,SAAS,OAAO,GAAG;AAAA,UACzB,SAAS,OAAO,OAAO,GAAG;AAAA,UAC1B,OAAO;AAAA,UACP,KAAK,OAAO,KAAK;AAAA,QACnB;AAAA,MACF;AAAA,MACA,UAAU,OAAO,KAAK;AACpB,cAAM,MAAM,SAAS,OAAO,GAAG;AAC/B,cAAM,OAAO,QAAQ,OAAO,QAAQ,MAAM,SAAS,QAAQ,MAAM,OAAO,QAAQ,MAAM,SAAS,IAAI,MAAM,CAAC;AAC1G,YAAI,CAAC,UAAU,SAAS,IAAI;AAC1B,oBAAU,IAAI,KAAK;AAAA,QACrB;AACA,YAAI,UAAU,SAAS,IAAI;AACzB,wBAAc;AAAA,YACZ,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS,OAAO,OAAO,GAAG;AAAA,YAC1B,OAAO;AAAA,YACP,KAAK,OAAO,KAAK;AAAA,UACnB;AAAA,QACF,OAAO;AACL,wBAAc;AAAA,YACZ,MAAM;AAAA,YACN;AAAA,YACA,SAAS;AAAA,YACT,KAAK;AAAA,YACL,KAAK;AAAA,YACL,WAAW,QAAQ,MAAM,CAAC,uBAAuB,MAAM,CAAC,IAAI,CAAC;AAAA,YAC7D,KAAK,OAAO,KAAK;AAAA,UACnB;AACA,cAAI,SAAS,OAAO;AAClB,qBAAS,UAAU,SAAS;AAC5B,kCAAsB;AACtB,kBAAM,QAAQ,eAAe;AAC7B,qBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,kBAAI,MAAM,CAAC,EAAE,SAAS,GAAG;AACvB,sBAAM,CAAC,IAAI,UAAU,MAAM,CAAC,CAAC;AAAA,cAC/B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,SAAS,OAAO,KAAK;AACnB,YAAI,UAAU,IAAK;AACnB,cAAM,MAAM,SAAS,OAAO,GAAG;AAC/B,YAAI,QAAQ;AACV,sBAAY,QAAQ;AACpB,oBAAU,YAAY,SAAS,GAAG;AAAA,QACpC,OAAO;AACL,gBAAM,WAAW,IAAI,CAAC,MAAM;AAC5B,sBAAY,MAAM;AAAA,YAChB,WAAW,MAAM,IAAI,MAAM,GAAG,EAAE;AAAA,YAChC;AAAA,YACA,OAAO,OAAO,GAAG;AAAA,YACjB,WAAW,IAAI;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AAAA,MACA,cAAc,OAAO,KAAK;AACxB,cAAM,MAAM,SAAS,OAAO,GAAG;AAC/B,YAAI,QAAQ;AACV,sBAAY,QAAQ,MAAM;AAC1B,oBAAU,YAAY,SAAS,GAAG;AAAA,QACpC,WAAW,YAAY,SAAS,QAAQ;AACtC,gBAAM,MAAM,YAAY;AACxB,cAAI,KAAK;AACP,gBAAI,WAAW,MAAM;AACrB,sBAAU,IAAI,KAAK,GAAG;AAAA,UACxB;AAAA,QACF,OAAO;AACL,gBAAM,MAAM,uBAAuB,KAAK,MAAM,OAAO,OAAO,GAAG,CAAC;AAChE,sBAAY,UAAU,KAAK,GAAG;AAAA,QAChC;AAAA,MACF;AAAA,MACA,aAAa,OAAO,KAAK;AACvB,4BAAoB,SAAS,OAAO,GAAG;AACvC,YAAI,wBAAwB,EAAG,yBAAwB;AACvD,8BAAsB;AAAA,MACxB;AAAA,MACA,eAAe,MAAM,OAAO,KAAK;AAC/B,4BAAoB;AACpB,YAAI,wBAAwB,EAAG,yBAAwB;AACvD,8BAAsB;AAAA,MACxB;AAAA,MACA,gBAAgB,KAAK;AACnB,cAAM,QAAQ,YAAY,IAAI,MAAM;AACpC,cAAM,OAAO,SAAS,OAAO,GAAG;AAChC,YAAI,YAAY,SAAS,GAAG;AAC1B,sBAAY,UAAU;AAAA,QACxB;AACA,YAAI,eAAe,MAAM;AAAA,UACvB,CAAC,OAAO,EAAE,SAAS,IAAI,EAAE,UAAU,EAAE,UAAU;AAAA,QACjD,GAAG;AACD,oBAAU,GAAG,KAAK;AAAA,QACpB;AAAA,MACF;AAAA,MACA,YAAY,OAAO,KAAK;AACtB,YAAI,kBAAkB,aAAa;AACjC,oBAAU,YAAY,KAAK,GAAG;AAC9B,cAAI,UAAU,GAAG;AACf,gBAAI,iBAAiB,SAAS,GAAG,GAAG;AAClC,iCAAmB,eAAe;AAAA,gBAChC;AAAA,gBACA;AAAA,cACF;AAAA,YACF;AACA,gBAAI,YAAY,SAAS,GAAG;AAC1B,kBAAI,YAAY,SAAS,SAAS;AAChC,mCAAmB,SAAS,gBAAgB,EAAE,KAAK;AAAA,cACrD;AACA,kBAAI,UAAU,KAAK,CAAC,kBAAkB;AACpC,0BAAU,IAAI,GAAG;AAAA,cACnB;AACA,0BAAY,QAAQ;AAAA,gBAClB,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,KAAK,UAAU,IAAI,OAAO,uBAAuB,mBAAmB,IAAI,OAAO,wBAAwB,GAAG,sBAAsB,CAAC;AAAA,cACnI;AACA,kBAAI,UAAU,aAAa,eAAe,QAAQ,cAAc,YAAY,SAAS,UAAU,oBAAoB,qBAAqB,QAAQ;AAC9I,0BAAU,YAAY,YAAY,YAAY,GAAG,CAAC;AAAA,cACpD;AAAA,YACF,OAAO;AACL,kBAAI,eAAe;AACnB,0BAAY,MAAM;AAAA,gBAChB;AAAA,gBACA;AAAA,gBACA,OAAO,uBAAuB,mBAAmB;AAAA,gBACjD;AAAA,gBACA;AAAA,cACF;AACA,kBAAI,YAAY,SAAS,OAAO;AAC9B,4BAAY,iBAAiB,mBAAmB,YAAY,GAAG;AAAA,cACjE;AACA,kBAAI,YAAY;AAChB,kBAAI,YAAY,SAAS,WAAW,YAAY,YAAY,UAAU;AAAA,gBACpE,CAAC,QAAQ,IAAI,YAAY;AAAA,cAC3B,KAAK,MAAM;AAAA,gBACT;AAAA,gBACA;AAAA,gBACA,YAAY;AAAA,gBACZ,YAAY;AAAA,cACd,GAAG;AACD,4BAAY,OAAO;AACnB,4BAAY,UAAU,OAAO,WAAW,CAAC;AAAA,cAC3C;AAAA,YACF;AAAA,UACF;AACA,cAAI,YAAY,SAAS,KAAK,YAAY,SAAS,OAAO;AACxD,2BAAe,MAAM,KAAK,WAAW;AAAA,UACvC;AAAA,QACF;AACA,2BAAmB;AACnB,gCAAwB,sBAAsB;AAAA,MAChD;AAAA,MACA,UAAU,OAAO,KAAK;AACpB,YAAI,eAAe,UAAU;AAC3B,kBAAQ;AAAA,YACN,MAAM;AAAA,YACN,SAAS,SAAS,OAAO,GAAG;AAAA,YAC5B,KAAK,OAAO,QAAQ,GAAG,MAAM,CAAC;AAAA,UAChC,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,QAAQ;AACN,cAAM,MAAM,aAAa;AACzB,YAA4D,UAAU,UAAU,GAAG;AACjF,kBAAQ,UAAU,OAAO;AAAA,YACvB,KAAK;AAAA,YACL,KAAK;AACH,wBAAU,GAAG,GAAG;AAChB;AAAA,YACF,KAAK;AAAA,YACL,KAAK;AACH;AAAA,gBACE;AAAA,gBACA,UAAU;AAAA,cACZ;AACA;AAAA,YACF,KAAK;AACH,kBAAI,UAAU,oBAAoB,UAAU,UAAU;AACpD,0BAAU,GAAG,GAAG;AAAA,cAClB,OAAO;AACL,0BAAU,GAAG,GAAG;AAAA,cAClB;AACA;AAAA,YACF,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YAEL,KAAK;AAAA,YAEL,KAAK;AACH,wBAAU,GAAG,GAAG;AAChB;AAAA,UACJ;AAAA,QACF;AACA,iBAAS,QAAQ,GAAG,QAAQ,MAAM,QAAQ,SAAS;AACjD,qBAAW,MAAM,KAAK,GAAG,MAAM,CAAC;AAChC,oBAAU,IAAI,MAAM,KAAK,EAAE,IAAI,MAAM,MAAM;AAAA,QAC7C;AAAA,MACF;AAAA,MACA,QAAQ,OAAO,KAAK;AAClB,YAAI,MAAM,CAAC,EAAE,OAAO,GAAG;AACrB,iBAAO,SAAS,OAAO,GAAG,GAAG,OAAO,GAAG;AAAA,QACzC,OAAO;AACL,oBAAU,GAAG,QAAQ,CAAC;AAAA,QACxB;AAAA,MACF;AAAA,MACA,wBAAwB,OAAO;AAC7B,aAAK,MAAM,CAAC,IAAI,MAAM,CAAC,EAAE,KAAK,eAAe,QAAQ,GAAG;AACtD;AAAA,YACE;AAAA,YACA,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,IAAM,gBAAgB;AACtB,IAAM,gBAAgB;AAoNtB,IAAM,qBAAqC,oBAAI,IAAI,CAAC,MAAM,QAAQ,WAAW,OAAO,MAAM,CAAC;AA8C3F,IAAM,mBAAmB;AAoYzB,IAAM,wBAAwC,oBAAI,IAAI;AAAA,MACpD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AA4VD,IAAM,kBAAkB;AACxB,IAAM,cAAc,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC,MAAM,cAAc,CAAC,CAAC;AAwkBpE,IAAM,sBAAsB,IAAI;AAAA,MAC9B,QAAQ,sMAAsM,MAAM,GAAG,EAAE,KAAK,SAAS,IAAI;AAAA,IAC7O;AACA,IAAM,gBAAgB;AA2BtB,IAAM,sBAAsB,CAAC,MAAM,YAAY;AAC7C,UAAI,KAAK,SAAS,GAAG;AACnB,aAAK,UAAU;AAAA,UACb,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF,WAAW,KAAK,SAAS,GAAG;AAC1B,cAAM,OAAO,QAAQ,MAAM,MAAM;AACjC,iBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,gBAAM,MAAM,KAAK,MAAM,CAAC;AACxB,cAAI,IAAI,SAAS,KAAK,IAAI,SAAS,OAAO;AACxC,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAChB,gBAAI,OAAO,IAAI,SAAS,KAAK,EAAE,IAAI,SAAS,QAAQ;AAAA,YACpD,EAAE,QAAQ,OAAO,IAAI,SAAS,KAAK,IAAI,YAAY,QAAQ;AACzD,kBAAI,MAAM;AAAA,gBACR;AAAA,gBACA;AAAA;AAAA,gBAEA,IAAI,SAAS;AAAA,cACf;AAAA,YACF;AACA,gBAAI,OAAO,IAAI,SAAS,KAAK,CAAC,IAAI,UAAU;AAC1C,kBAAI,MAAM,kBAAkB,KAAK,OAAO;AAAA,YAC1C;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAmBA,IAAM,cAAc;AAAA,MAClB;AAAA,MACA,CAAC,MAAM,KAAK,YAAY;AACtB,eAAO,UAAU,MAAM,KAAK,SAAS,CAAC,QAAQ,QAAQ,WAAW;AAC/D,gBAAM,WAAW,QAAQ,OAAO;AAChC,cAAI,IAAI,SAAS,QAAQ,MAAM;AAC/B,cAAI,MAAM;AACV,iBAAO,OAAO,GAAG;AACf,kBAAM,UAAU,SAAS,CAAC;AAC1B,gBAAI,WAAW,QAAQ,SAAS,GAAG;AACjC,qBAAO,QAAQ,SAAS;AAAA,YAC1B;AAAA,UACF;AACA,iBAAO,MAAM;AACX,gBAAI,QAAQ;AACV,qBAAO,cAAc;AAAA,gBACnB;AAAA,gBACA;AAAA,gBACA;AAAA,cACF;AAAA,YACF,OAAO;AACL,oBAAM,kBAAkB,mBAAmB,OAAO,WAAW;AAC7D,8BAAgB,YAAY;AAAA,gBAC1B;AAAA,gBACA,MAAM,OAAO,SAAS,SAAS;AAAA,gBAC/B;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AA4LA,IAAM,gBAAgB,CAAC,KAAK,OAAO,YAAY;AAC7C,YAAM,EAAE,WAAW,IAAI,IAAI;AAC3B,YAAM,MAAM,IAAI;AAChB,UAAI,EAAE,IAAI,IAAI;AACd,UAAI,OAAO,IAAI,SAAS,KAAK,CAAC,IAAI,QAAQ,KAAK,GAAG;AAChD;AACE,gBAAM;AAAA,QACR;AAAA,MACF;AACA,UAAI,CAAC,KAAK;AACR,YAAI,IAAI,SAAS,KAAK,CAAC,IAAI,UAAU;AACnC,kBAAQ;AAAA,YACN;AAAA,cACE;AAAA,cACA,IAAI;AAAA,YACN;AAAA,UACF;AACA,iBAAO;AAAA,YACL,OAAO;AAAA,cACL,qBAAqB,KAAK,uBAAuB,IAAI,MAAM,GAAG,CAAC;AAAA,YACjE;AAAA,UACF;AAAA,QACF;AACA,+BAAuB,GAAG;AAC1B,cAAM,IAAI;AAAA,MACZ;AACA,UAAI,IAAI,SAAS,GAAG;AAClB,YAAI,SAAS,QAAQ,GAAG;AACxB,YAAI,SAAS,KAAK,SAAS;AAAA,MAC7B,WAAW,CAAC,IAAI,UAAU;AACxB,YAAI,UAAU,GAAG,IAAI,OAAO;AAAA,MAC9B;AACA,UAAI,UAAU,KAAK,CAAC,QAAQ,IAAI,YAAY,OAAO,GAAG;AACpD,YAAI,IAAI,SAAS,GAAG;AAClB,cAAI,IAAI,UAAU;AAChB,gBAAI,UAAU,SAAS,IAAI,OAAO;AAAA,UACpC,OAAO;AACL,gBAAI,UAAU,GAAG,QAAQ,aAAa,QAAQ,CAAC,IAAI,IAAI,OAAO;AAAA,UAChE;AAAA,QACF,OAAO;AACL,cAAI,SAAS,QAAQ,GAAG,QAAQ,aAAa,QAAQ,CAAC,GAAG;AACzD,cAAI,SAAS,KAAK,GAAG;AAAA,QACvB;AAAA,MACF;AACA,UAAI,CAAC,QAAQ,OAAO;AAClB,YAAI,UAAU,KAAK,CAAC,QAAQ,IAAI,YAAY,MAAM,GAAG;AACnD,uBAAa,KAAK,GAAG;AAAA,QACvB;AACA,YAAI,UAAU,KAAK,CAAC,QAAQ,IAAI,YAAY,MAAM,GAAG;AACnD,uBAAa,KAAK,GAAG;AAAA,QACvB;AAAA,MACF;AACA,aAAO;AAAA,QACL,OAAO,CAAC,qBAAqB,KAAK,GAAG,CAAC;AAAA,MACxC;AAAA,IACF;AACA,IAAM,yBAAyB,CAAC,KAAK,YAAY;AAC/C,YAAM,MAAM,IAAI;AAChB,YAAM,WAAW,SAAS,IAAI,OAAO;AACrC,UAAI,MAAM,uBAAuB,UAAU,OAAO,IAAI,GAAG;AAAA,IAC3D;AACA,IAAM,eAAe,CAAC,KAAK,WAAW;AACpC,UAAI,IAAI,SAAS,GAAG;AAClB,YAAI,IAAI,UAAU;AAChB,cAAI,UAAU,SAAS,IAAI;AAAA,QAC7B,OAAO;AACL,cAAI,UAAU,KAAK,MAAM,MAAM,IAAI,OAAO;AAAA,QAC5C;AAAA,MACF,OAAO;AACL,YAAI,SAAS,QAAQ,IAAI,MAAM,OAAO;AACtC,YAAI,SAAS,KAAK,GAAG;AAAA,MACvB;AAAA,IACF;AAEA,IAAM,eAAe;AAAA,MACnB;AAAA,MACA,CAAC,MAAM,KAAK,YAAY;AACtB,cAAM,EAAE,QAAQ,aAAa,IAAI;AACjC,eAAO,WAAW,MAAM,KAAK,SAAS,CAAC,YAAY;AACjD,gBAAM,YAAY,qBAAqB,OAAO,WAAW,GAAG;AAAA,YAC1D,QAAQ;AAAA,UACV,CAAC;AACD,gBAAM,aAAa,eAAe,IAAI;AACtC,gBAAM,OAAO,QAAQ,MAAM,MAAM;AACjC,gBAAM,UAAU,SAAS,MAAM,OAAO,OAAO,IAAI;AACjD,gBAAM,WAAW,WAAW,QAAQ,SAAS;AAC7C,cAAI,YAAY,CAAC,QAAQ,KAAK;AAC5B,mCAAuB,OAAO;AAAA,UAChC;AACA,cAAI,SAAS,YAAY,QAAQ,SAAS,IAAI,QAAQ,QAAQ,uBAAuB,QAAQ,MAAM,SAAS,IAAI,IAAI,SAAS,QAAQ;AACrI,gBAAM,cAAc,WAAW,SAAS,qBAAqB,OAAO,MAAM,IAAI;AAC9E,gBAAM,mBAAmB,QAAQ,OAAO,SAAS,KAAK,QAAQ,OAAO,YAAY;AACjF,gBAAM,eAAe,mBAAmB,KAAK,UAAU,MAAM;AAC7D,kBAAQ,cAAc;AAAA,YACpB;AAAA,YACA,OAAO,QAAQ;AAAA,YACf;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,CAAC;AAAA,YACD;AAAA,YACA,KAAK;AAAA,UACP;AACA,iBAAO,MAAM;AACX,gBAAI;AACJ,kBAAM,EAAE,SAAS,IAAI;AACrB,gBAA4D,YAAY;AACtE,mBAAK,SAAS,KAAK,CAAC,MAAM;AACxB,oBAAI,EAAE,SAAS,GAAG;AAChB,wBAAM,MAAM,SAAS,GAAG,KAAK;AAC7B,sBAAI,KAAK;AACP,4BAAQ;AAAA,sBACN;AAAA,wBACE;AAAA,wBACA,IAAI;AAAA,sBACN;AAAA,oBACF;AACA,2BAAO;AAAA,kBACT;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AACA,kBAAM,sBAAsB,SAAS,WAAW,KAAK,SAAS,CAAC,EAAE,SAAS;AAC1E,kBAAM,aAAa,aAAa,IAAI,IAAI,OAAO,cAAc,KAAK,SAAS,WAAW,KAAK,aAAa,KAAK,SAAS,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI;AAC/I,gBAAI,YAAY;AACd,2BAAa,WAAW;AACxB,kBAAI,cAAc,aAAa;AAC7B,2BAAW,YAAY,aAAa,OAAO;AAAA,cAC7C;AAAA,YACF,WAAW,qBAAqB;AAC9B,2BAAa;AAAA,gBACX;AAAA,gBACA,OAAO,QAAQ;AAAA,gBACf,cAAc,uBAAuB,CAAC,WAAW,CAAC,IAAI;AAAA,gBACtD,KAAK;AAAA,gBACL;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACF;AAAA,YACF,OAAO;AACL,2BAAa,SAAS,CAAC,EAAE;AACzB,kBAAI,cAAc,aAAa;AAC7B,2BAAW,YAAY,aAAa,OAAO;AAAA,cAC7C;AACA,kBAAI,WAAW,YAAY,CAAC,kBAAkB;AAC5C,oBAAI,WAAW,SAAS;AACtB,+BAAa,UAAU;AACvB;AAAA,oBACE,oBAAoB,QAAQ,OAAO,WAAW,WAAW;AAAA,kBAC3D;AAAA,gBACF,OAAO;AACL;AAAA,oBACE,eAAe,QAAQ,OAAO,WAAW,WAAW;AAAA,kBACtD;AAAA,gBACF;AAAA,cACF;AACA,yBAAW,UAAU,CAAC;AACtB,kBAAI,WAAW,SAAS;AACtB,uBAAO,UAAU;AACjB,uBAAO,oBAAoB,QAAQ,OAAO,WAAW,WAAW,CAAC;AAAA,cACnE,OAAO;AACL,uBAAO,eAAe,QAAQ,OAAO,WAAW,WAAW,CAAC;AAAA,cAC9D;AAAA,YACF;AACA,gBAAI,MAAM;AACR,oBAAM,OAAO;AAAA,gBACX,oBAAoB,QAAQ,aAAa;AAAA,kBACvC,uBAAuB,SAAS;AAAA,gBAClC,CAAC;AAAA,cACH;AACA,mBAAK,OAAO,qBAAqB;AAAA,gBAC/B,yBAAyB,CAAC,mBAAmB,KAAK,KAAK,GAAG,CAAC;AAAA,gBAC3D,yBAAyB;AAAA,kBACvB;AAAA,kBACA,GAAG,SAAS,CAAC,wBAAwB,MAAM,IAAI,CAAC;AAAA,kBAChD,OAAO,QAAQ;AAAA,oBACb;AAAA,kBACF,CAAC;AAAA,gBACH,CAAC;AAAA,gBACD,yBAAyB,CAAC,kBAAkB,UAAU,CAAC;AAAA,gBACvD,uBAAuB,oBAAoB;AAAA,gBAC3C,uBAAuB,cAAc;AAAA,cACvC,CAAC;AACD,wBAAU,UAAU;AAAA,gBAClB;AAAA,gBACA,uBAAuB,QAAQ;AAAA,gBAC/B,uBAAuB,OAAO,QAAQ,OAAO,MAAM,CAAC;AAAA,cACtD;AACA,sBAAQ,OAAO,KAAK,IAAI;AAAA,YAC1B,OAAO;AACL,wBAAU,UAAU;AAAA,gBAClB;AAAA,kBACE,oBAAoB,QAAQ,WAAW;AAAA,kBACvC;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AA2EA,IAAM,kBAAkB,uBAAuB,aAAa,KAAK;AACjE,IAAM,kBAAkB,CAAC,MAAM,YAAY;AACzC,UAAI,KAAK,SAAS,MAAM,KAAK,YAAY,KAAK,KAAK,YAAY,IAAI;AACjE,cAAM,QAAQ,QAAQ,MAAM,MAAM;AAClC,YAAI,OAAO;AACT,gBAAM;AACN,kBAAQ,OAAO;AACf,iBAAO,MAAM;AACX,oBAAQ,OAAO;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,IAAM,sBAAsB,CAAC,MAAM,YAAY;AAC7C,UAAI;AACJ,UAAI,eAAe,IAAI,KAAK,KAAK,MAAM,KAAK,OAAO,MAAM,OAAO,QAAQ,MAAM,KAAK,IAAI;AACrF,cAAM,SAAS,KAAK;AACpB,YAAI,QAAQ;AACV,iCAAuB,QAAQ,OAAO;AACtC,gBAAM,EAAE,OAAO,KAAK,MAAM,IAAI;AAC9B,gBAAM,EAAE,gBAAgB,kBAAkB,IAAI;AAC9C,mBAAS,eAAe,KAAK;AAC7B,iBAAO,eAAe,GAAG;AACzB,mBAAS,eAAe,KAAK;AAC7B,iBAAO,MAAM;AACX,qBAAS,kBAAkB,KAAK;AAChC,mBAAO,kBAAkB,GAAG;AAC5B,qBAAS,kBAAkB,KAAK;AAAA,UAClC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,IAAM,oBAAoB,CAAC,OAAO,UAAU,UAAU,QAAQ;AAAA,MAC5D;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS,SAAS,SAAS,CAAC,EAAE,MAAM;AAAA,IACtC;AAyOA,IAAM,qBAAqC,oBAAI,QAAQ;AACvD,IAAM,mBAAmB,CAAC,MAAM,YAAY;AAC1C,aAAO,SAAS,uBAAuB;AACrC,eAAO,QAAQ;AACf,YAAI,EAAE,KAAK,SAAS,MAAM,KAAK,YAAY,KAAK,KAAK,YAAY,KAAK;AACpE;AAAA,QACF;AACA,cAAM,EAAE,KAAK,MAAM,IAAI;AACvB,cAAMD,eAAc,KAAK,YAAY;AACrC,YAAI,WAAWA,eAAc,qBAAqB,MAAM,OAAO,IAAI,IAAI,GAAG;AAC1E,cAAM,qBAAqB,SAAS,QAAQ,KAAK,SAAS,WAAW;AACrE,YAAI;AACJ,YAAI;AACJ,YAAI,YAAY;AAChB,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AAAA;AAAA,UAEF,sBAAsB,aAAa,YAAY,aAAa,YAAY,CAACA;AAAA;AAAA;AAAA;AAAA,WAIxE,QAAQ,SAAS,QAAQ,mBAAmB,QAAQ;AAAA;AAEvD,YAAI,MAAM,SAAS,GAAG;AACpB,gBAAM,mBAAmB;AAAA,YACvB;AAAA,YACA;AAAA,YACA;AAAA,YACAA;AAAA,YACA;AAAA,UACF;AACA,uBAAa,iBAAiB;AAC9B,sBAAY,iBAAiB;AAC7B,6BAAmB,iBAAiB;AACpC,gBAAM,aAAa,iBAAiB;AACpC,4BAAkB,cAAc,WAAW,SAAS;AAAA,YAClD,WAAW,IAAI,CAAC,QAAQ,mBAAmB,KAAK,OAAO,CAAC;AAAA,UAC1D,IAAI;AACJ,cAAI,iBAAiB,gBAAgB;AACnC,6BAAiB;AAAA,UACnB;AAAA,QACF;AACA,YAAI,KAAK,SAAS,SAAS,GAAG;AAC5B,cAAI,aAAa,YAAY;AAC3B,6BAAiB;AACjB,yBAAa;AACb,gBAAiD,KAAK,SAAS,SAAS,GAAG;AACzE,sBAAQ;AAAA,gBACN,oBAAoB,IAAI;AAAA,kBACtB,OAAO,KAAK,SAAS,CAAC,EAAE,IAAI;AAAA,kBAC5B,KAAK,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC,EAAE,IAAI;AAAA,kBACjD,QAAQ;AAAA,gBACV,CAAC;AAAA,cACH;AAAA,YACF;AAAA,UACF;AACA,gBAAM,qBAAqBA;AAAA,UAC3B,aAAa;AAAA,UACb,aAAa;AACb,cAAI,oBAAoB;AACtB,kBAAM,EAAE,OAAO,gBAAgB,IAAI,WAAW,MAAM,OAAO;AAC3D,4BAAgB;AAChB,gBAAI,iBAAiB;AACnB,2BAAa;AAAA,YACf;AAAA,UACF,WAAW,KAAK,SAAS,WAAW,KAAK,aAAa,UAAU;AAC9D,kBAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,kBAAM,OAAO,MAAM;AACnB,kBAAM,sBAAsB,SAAS,KAAK,SAAS;AACnD,gBAAI,uBAAuB,gBAAgB,OAAO,OAAO,MAAM,GAAG;AAChE,2BAAa;AAAA,YACf;AACA,gBAAI,uBAAuB,SAAS,GAAG;AACrC,8BAAgB;AAAA,YAClB,OAAO;AACL,8BAAgB,KAAK;AAAA,YACvB;AAAA,UACF,OAAO;AACL,4BAAgB,KAAK;AAAA,UACvB;AAAA,QACF;AACA,YAAI,oBAAoB,iBAAiB,QAAQ;AAC/C,8BAAoB,0BAA0B,gBAAgB;AAAA,QAChE;AACA,aAAK,cAAc;AAAA,UACjB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,cAAc,IAAI,SAAS;AAAA,UAC3B;AAAA,UACA;AAAA,UACA,CAAC,CAAC;AAAA,UACF;AAAA,UACAA;AAAA,UACA,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAwbA,IAAM,sBAAsB,CAAC,MAAM,YAAY;AAC7C,UAAI,aAAa,IAAI,GAAG;AACtB,cAAM,EAAE,UAAU,IAAI,IAAI;AAC1B,cAAM,EAAE,UAAU,UAAU,IAAI,kBAAkB,MAAM,OAAO;AAC/D,cAAM,WAAW;AAAA,UACf,QAAQ,oBAAoB,gBAAgB;AAAA,UAC5C;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,YAAI,cAAc;AAClB,YAAI,WAAW;AACb,mBAAS,CAAC,IAAI;AACd,wBAAc;AAAA,QAChB;AACA,YAAI,SAAS,QAAQ;AACnB,mBAAS,CAAC,IAAI,yBAAyB,CAAC,GAAG,UAAU,OAAO,OAAO,GAAG;AACtE,wBAAc;AAAA,QAChB;AACA,YAAI,QAAQ,WAAW,CAAC,QAAQ,SAAS;AACvC,wBAAc;AAAA,QAChB;AACA,iBAAS,OAAO,WAAW;AAC3B,aAAK,cAAc;AAAA,UACjB,QAAQ,OAAO,WAAW;AAAA,UAC1B;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAwDA,IAAM,cAAc,CAAC,KAAK,MAAM,SAAS,cAAc;AACrD,YAAM,EAAE,KAAK,WAAW,IAAI,IAAI;AAChC,UAAI,CAAC,IAAI,OAAO,CAAC,UAAU,QAAQ;AACjC,gBAAQ,QAAQ,oBAAoB,IAAI,GAAG,CAAC;AAAA,MAC9C;AACA,UAAI;AACJ,UAAI,IAAI,SAAS,GAAG;AAClB,YAAI,IAAI,UAAU;AAChB,cAAI,UAAU,IAAI;AAClB,cAAiD,QAAQ,WAAW,OAAO,GAAG;AAC5E,oBAAQ,QAAQ,oBAAoB,IAAI,IAAI,GAAG,CAAC;AAAA,UAClD;AACA,cAAI,QAAQ,WAAW,MAAM,GAAG;AAC9B,sBAAU,SAAS,QAAQ,MAAM,CAAC,CAAC;AAAA,UACrC;AACA,gBAAM,cAAc,KAAK,YAAY,KAAK,QAAQ,WAAW,OAAO,KAAK,CAAC,QAAQ,KAAK,OAAO;AAAA;AAAA;AAAA,YAG5F,aAAa,SAAS,OAAO,CAAC;AAAA;AAAA;AAAA;AAAA,YAI9B,MAAM,OAAO;AAAA;AAEf,sBAAY,uBAAuB,aAAa,MAAM,IAAI,GAAG;AAAA,QAC/D,OAAO;AACL,sBAAY,yBAAyB;AAAA,YACnC,GAAG,QAAQ,aAAa,cAAc,CAAC;AAAA,YACvC;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AACL,oBAAY;AACZ,kBAAU,SAAS,QAAQ,GAAG,QAAQ,aAAa,cAAc,CAAC,GAAG;AACrE,kBAAU,SAAS,KAAK,GAAG;AAAA,MAC7B;AACA,UAAI,MAAM,IAAI;AACd,UAAI,OAAO,CAAC,IAAI,QAAQ,KAAK,GAAG;AAC9B,cAAM;AAAA,MACR;AACA,UAAI,cAAc,QAAQ,iBAAiB,CAAC,OAAO,CAAC,QAAQ;AAC5D,UAAI,KAAK;AACP,cAAM,cAAc,mBAAmB,GAAG;AAC1C,cAAM,oBAAoB,EAAE,eAAe,eAAe,GAAG;AAC7D,cAAM,wBAAwB,IAAI,QAAQ,SAAS,GAAG;AACtD,YAAiD,MAAM;AACrD;AAAA,YACE;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,YAAI,qBAAqB,eAAe,aAAa;AACnD,gBAAM,yBAAyB;AAAA,YAC7B,GAAG,oBAAoB,WAAW,GAAG,EAAE,WAAW,OAAO,wBAAwB,MAAM,GAAG;AAAA,YAC1F;AAAA,YACA,wBAAwB,MAAM;AAAA,UAChC,CAAC;AAAA,QACH;AAAA,MACF;AACA,UAAI,MAAM;AAAA,QACR,OAAO;AAAA,UACL;AAAA,YACE;AAAA,YACA,OAAO,uBAAuB,YAAY,OAAO,GAAG;AAAA,UACtD;AAAA,QACF;AAAA,MACF;AACA,UAAI,WAAW;AACb,cAAM,UAAU,GAAG;AAAA,MACrB;AACA,UAAI,aAAa;AACf,YAAI,MAAM,CAAC,EAAE,QAAQ,QAAQ,MAAM,IAAI,MAAM,CAAC,EAAE,KAAK;AAAA,MACvD;AACA,UAAI,MAAM,QAAQ,CAAC,MAAM,EAAE,IAAI,eAAe,IAAI;AAClD,aAAO;AAAA,IACT;AAEA,IAAM,gBAAgB,CAAC,MAAM,YAAY;AACvC,UAAI,KAAK,SAAS,KAAK,KAAK,SAAS,KAAK,KAAK,SAAS,MAAM,KAAK,SAAS,IAAI;AAC9E,eAAO,MAAM;AACX,gBAAM,WAAW,KAAK;AACtB,cAAI,mBAAmB;AACvB,cAAI,UAAU;AACd,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,kBAAM,QAAQ,SAAS,CAAC;AACxB,gBAAI,SAAS,KAAK,GAAG;AACnB,wBAAU;AACV,uBAAS,IAAI,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AAC5C,sBAAM,OAAO,SAAS,CAAC;AACvB,oBAAI,SAAS,IAAI,GAAG;AAClB,sBAAI,CAAC,kBAAkB;AACrB,uCAAmB,SAAS,CAAC,IAAI;AAAA,sBAC/B,CAAC,KAAK;AAAA,sBACN,MAAM;AAAA,oBACR;AAAA,kBACF;AACA,mCAAiB,SAAS,KAAK,OAAO,IAAI;AAC1C,2BAAS,OAAO,GAAG,CAAC;AACpB;AAAA,gBACF,OAAO;AACL,qCAAmB;AACnB;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,CAAC;AAAA;AAAA;AAAA;AAAA,UAIL,SAAS,WAAW,MAAM,KAAK,SAAS,KAAK,KAAK,SAAS,KAAK,KAAK,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,UAKjF,CAAC,KAAK,MAAM;AAAA,YACV,CAAC,MAAM,EAAE,SAAS,KAAK,CAAC,QAAQ,oBAAoB,EAAE,IAAI;AAAA,UAC5D;AAAA;AAAA;AAAA,UAGA,EAAE,KAAK,QAAQ,cAAc;AAC3B;AAAA,UACF;AACA,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,kBAAM,QAAQ,SAAS,CAAC;AACxB,gBAAI,SAAS,KAAK,KAAK,MAAM,SAAS,GAAG;AACvC,oBAAM,WAAW,CAAC;AAClB,kBAAI,MAAM,SAAS,KAAK,MAAM,YAAY,KAAK;AAC7C,yBAAS,KAAK,KAAK;AAAA,cACrB;AACA,kBAAI,CAAC,QAAQ,OAAO,gBAAgB,OAAO,OAAO,MAAM,GAAG;AACzD,yBAAS;AAAA,kBACP,KAAK,OAA4C,OAAO,eAAe,CAAC,CAAC,QAAQ;AAAA,gBACnF;AAAA,cACF;AACA,uBAAS,CAAC,IAAI;AAAA,gBACZ,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,KAAK,MAAM;AAAA,gBACX,aAAa;AAAA,kBACX,QAAQ,OAAO,WAAW;AAAA,kBAC1B;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,IAAM,SAAyB,oBAAI,QAAQ;AAC3C,IAAM,gBAAgB,CAAC,MAAM,YAAY;AACvC,UAAI,KAAK,SAAS,KAAK,QAAQ,MAAM,QAAQ,IAAI,GAAG;AAClD,YAAI,OAAO,IAAI,IAAI,KAAK,QAAQ,WAAW,QAAQ,OAAO;AACxD;AAAA,QACF;AACA,eAAO,IAAI,IAAI;AACf,gBAAQ,UAAU;AAClB,gBAAQ,OAAO,kBAAkB;AACjC,eAAO,MAAM;AACX,kBAAQ,UAAU;AAClB,gBAAM,MAAM,QAAQ;AACpB,cAAI,IAAI,aAAa;AACnB,gBAAI,cAAc,QAAQ;AAAA,cACxB,IAAI;AAAA,cACJ;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,IAAM,iBAAiB,CAAC,KAAK,MAAM,YAAY;AAC7C,YAAM,EAAE,KAAK,IAAI,IAAI;AACrB,UAAI,CAAC,KAAK;AACR,gBAAQ;AAAA,UACN,oBAAoB,IAAI,IAAI,GAAG;AAAA,QACjC;AACA,eAAO,qBAAqB;AAAA,MAC9B;AACA,YAAM,SAAS,IAAI,IAAI,OAAO,KAAK;AACnC,YAAM,YAAY,IAAI,SAAS,IAAI,IAAI,UAAU;AACjD,YAAM,cAAc,QAAQ,gBAAgB,MAAM;AAClD,UAAI,gBAAgB,WAAW,gBAAgB,iBAAiB;AAC9D,gBAAQ,QAAQ,oBAAoB,IAAI,IAAI,GAAG,CAAC;AAChD,eAAO,qBAAqB;AAAA,MAC9B;AACA,YAAM,WAAW;AACjB,UAAI,CAAC,UAAU,KAAK,KAAK,CAAC,mBAAmB,GAAG,KAAK,CAAC,UAAU;AAC9D,gBAAQ;AAAA,UACN,oBAAoB,IAAI,IAAI,GAAG;AAAA,QACjC;AACA,eAAO,qBAAqB;AAAA,MAC9B;AACA,YAAM,WAAW,MAAM,MAAM,uBAAuB,cAAc,IAAI;AACtE,YAAM,YAAY,MAAM,YAAY,GAAG,IAAI,YAAY,SAAS,IAAI,OAAO,CAAC,KAAK,yBAAyB,CAAC,kBAAkB,GAAG,CAAC,IAAI;AACrI,UAAI;AACJ,YAAM,WAAW,QAAQ,OAAO,kBAAkB;AAClD;AACE,wBAAgB,yBAAyB;AAAA,UACvC,GAAG,QAAQ;AAAA,UACX;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,QAAQ;AAAA;AAAA,QAEZ,qBAAqB,UAAU,IAAI,GAAG;AAAA;AAAA,QAEtC,qBAAqB,WAAW,aAAa;AAAA,MAC/C;AACA,UAAI,IAAI,UAAU,UAAU,KAAK,YAAY,GAAG;AAC9C,cAAM,YAAY,IAAI,UAAU,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,mBAAmB,CAAC,IAAI,IAAI,KAAK,UAAU,CAAC,KAAK,QAAQ,EAAE,KAAK,IAAI;AACtI,cAAM,eAAe,MAAM,YAAY,GAAG,IAAI,GAAG,IAAI,OAAO,cAAc,yBAAyB,CAAC,KAAK,gBAAgB,CAAC,IAAI;AAC9H,cAAM;AAAA,UACJ;AAAA,YACE;AAAA,YACA;AAAA,cACE,KAAK,SAAS;AAAA,cACd;AAAA,cACA,IAAI;AAAA,cACJ;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,aAAO,qBAAqB,KAAK;AAAA,IACnC;AAKA,IAAM,sBAAsB;AAC5B,IAAM,kBAAkB,CAAC,MAAM,YAAY;AACzC,UAAI,CAAC,gBAAgB,oBAAoB,OAAO,GAAG;AACjD;AAAA,MACF;AACA,UAAI,KAAK,SAAS,GAAG;AACnB,sBAAc,KAAK,SAAS,OAAO;AAAA,MACrC,WAAW,KAAK,SAAS,GAAG;AAC1B,aAAK,MAAM,QAAQ,CAAC,SAAS;AAC3B,cAAI,KAAK,SAAS,KAAK,KAAK,SAAS,SAAS,KAAK,KAAK;AACtD,0BAAc,KAAK,KAAK,OAAO;AAAA,UACjC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAuIA,IAAM,OAAuB,oBAAI,QAAQ;AACzC,IAAM,gBAAgB,CAAC,MAAM,YAAY;AACvC,UAAI,KAAK,SAAS,GAAG;AACnB,cAAM,MAAM,QAAQ,MAAM,MAAM;AAChC,YAAI,CAAC,OAAO,KAAK,IAAI,IAAI,GAAG;AAC1B;AAAA,QACF;AACA,aAAK,IAAI,IAAI;AACb,eAAO,MAAM;AACX,gBAAM,cAAc,KAAK,eAAe,QAAQ,YAAY;AAC5D,cAAI,eAAe,YAAY,SAAS,IAAI;AAC1C,gBAAI,KAAK,YAAY,GAAG;AACtB,6BAAe,aAAa,OAAO;AAAA,YACrC;AACA,iBAAK,cAAc,qBAAqB,QAAQ,OAAO,SAAS,GAAG;AAAA,cACjE,IAAI;AAAA,cACJ,yBAAyB,QAAQ,WAAW;AAAA,cAC5C;AAAA,cACA,OAAO,QAAQ,OAAO,MAAM;AAAA,YAC9B,CAAC;AACD,oBAAQ,OAAO,KAAK,IAAI;AAAA,UAC1B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAgEA,IAAM,eAAe;AAAA,MACnB,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,eAAe;AAAA,MACf,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,aAAa;AAAA,MACb,WAAW;AAAA,MACX,iBAAiB;AAAA,IACnB;AAEA,IAAM,yBAAyB,OAAO,EAAE,OAAO,CAAC,EAAE;AAAA;AAAA;;;AC3pLlD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6CA,SAAS,kBAAkB,KAAK,SAAS,OAAO;AAC9C,MAAI,CAAC,SAAS;AACZ,cAAU,SAAS,cAAc,KAAK;AAAA,EACxC;AACA,MAAI,QAAQ;AACV,YAAQ,YAAY,aAAa,IAAI,QAAQ,MAAM,QAAQ,CAAC;AAC5D,WAAO,QAAQ,SAAS,CAAC,EAAE,aAAa,KAAK;AAAA,EAC/C,OAAO;AACL,YAAQ,YAAY;AACpB,WAAO,QAAQ;AAAA,EACjB;AACF;AA2EA,SAAS,uBAAuB,MAAM,KAAK;AACzC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,OAAqD,mBAAmB;AAAA,EAC1E;AACF;AA8TA,SAAS,oBAAoB,MAAM;AACjC,QAAM,WAAW,KAAK,WAAW,KAAK,SAAS;AAAA,IAC7C,CAAC,MAAM,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,KAAK,CAAC,EAAE,QAAQ,KAAK;AAAA,EAC3D;AACA,QAAM,QAAQ,SAAS,CAAC;AACxB,SAAO,SAAS,WAAW,KAAK,MAAM,SAAS,MAAM,MAAM,SAAS,KAAK,MAAM,SAAS,KAAK,mBAAmB;AAClH;AAcA,SAAS,mBAAmB,QAAQ,OAAO;AACzC,MAAI,UAAU,mBAAmB;AAC/B,WAAO,kBAAkB,MAAM,EAAE,IAAI,KAAK;AAAA,EAC5C;AACA,MAAI,SAAS,kBAAkB;AAC7B,WAAO,iBAAiB,KAAK,EAAE,IAAI,MAAM;AAAA,EAC3C;AACA,MAAI,UAAU,sBAAsB;AAClC,QAAI,qBAAqB,MAAM,EAAE,IAAI,KAAK,EAAG,QAAO;AAAA,EACtD;AACA,MAAI,SAAS,qBAAqB;AAChC,QAAI,oBAAoB,KAAK,EAAE,IAAI,MAAM,EAAG,QAAO;AAAA,EACrD;AACA,SAAO;AACT;AA4KA,SAAS,QAAQ,KAAK,UAAU,CAAC,GAAG;AAClC,SAAO;AAAA,IACL;AAAA,IACA,OAAO,CAAC,GAAG,eAAe,SAAS;AAAA,MACjC,gBAAgB;AAAA;AAAA;AAAA;AAAA,QAId;AAAA,QACA,GAAG;AAAA,QACH,GAAG,QAAQ,kBAAkB,CAAC;AAAA,MAChC;AAAA,MACA,qBAAqB;AAAA,QACnB,CAAC;AAAA,QACD;AAAA,QACA,QAAQ,uBAAuB,CAAC;AAAA,MAClC;AAAA,MACA,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACF;AACA,SAAS,MAAM,UAAU,UAAU,CAAC,GAAG;AACrC,SAAO,UAAU,UAAU,OAAO,CAAC,GAAG,eAAe,OAAO,CAAC;AAC/D;AA5qBA,IASM,eACA,kBAGA,cACA,gBAGA,iBAGA,qBAGA,gBAGA,QACA,YACA,kBAgBF,SAcE,eA+CA,gBAgBA,gBAiBA,eA0BA,kBAcA,gBAuBA,gBA2BAE,iBAmFA,uBACA,kBAIA,kBACA,iBACA,kBAyCA,gBAUAC,cAmCA,eAaA,qBA8CA,sBA2BA,UACA,UACA,mBA+BA,kBAwBA,sBA2EA,qBAeA,qBAUA,mBAIA;AA3oBN;AAAA;AAKA;AACA;AACA;AAEA,IAAM,gBAAgB,OAAO,OAA4C,gBAAgB,EAAE;AAC3F,IAAM,mBAAmB;AAAA,MACvB,OAA4C,mBAAmB;AAAA,IACjE;AACA,IAAM,eAAe,OAAO,OAA4C,eAAe,EAAE;AACzF,IAAM,iBAAiB;AAAA,MACrB,OAA4C,iBAAiB;AAAA,IAC/D;AACA,IAAM,kBAAkB;AAAA,MACtB,OAA4C,kBAAkB;AAAA,IAChE;AACA,IAAM,sBAAsB;AAAA,MAC1B,OAA4C,sBAAsB;AAAA,IACpE;AACA,IAAM,iBAAiB;AAAA,MACrB,OAA4C,iBAAiB;AAAA,IAC/D;AACA,IAAM,SAAS,OAAO,OAA4C,UAAU,EAAE;AAC9E,IAAM,aAAa,OAAO,OAA4C,eAAe,EAAE;AACvF,IAAM,mBAAmB;AAAA,MACvB,OAA4C,oBAAoB;AAAA,IAClE;AACA,2BAAuB;AAAA,MACrB,CAAC,aAAa,GAAG;AAAA,MACjB,CAAC,gBAAgB,GAAG;AAAA,MACpB,CAAC,YAAY,GAAG;AAAA,MAChB,CAAC,cAAc,GAAG;AAAA,MAClB,CAAC,eAAe,GAAG;AAAA,MACnB,CAAC,mBAAmB,GAAG;AAAA,MACvB,CAAC,cAAc,GAAG;AAAA,MAClB,CAAC,MAAM,GAAG;AAAA,MACV,CAAC,UAAU,GAAG;AAAA,MACd,CAAC,gBAAgB,GAAG;AAAA,IACtB,CAAC;AAgBD,IAAM,gBAAgB;AAAA,MACpB,WAAW;AAAA,MACX;AAAA,MACA,aAAa,CAAC,QAAQ,UAAU,GAAG,KAAK,SAAS,GAAG,KAAK,YAAY,GAAG;AAAA,MACxE,UAAU,CAAC,QAAQ,QAAQ;AAAA,MAC3B,oBAAoB,CAAC,QAAQ,QAAQ,SAAS,QAAQ;AAAA,MACtD,gBAAgB;AAAA,MAChB,oBAAoB,CAAC,QAAQ;AAC3B,YAAI,QAAQ,gBAAgB,QAAQ,cAAc;AAChD,iBAAO;AAAA,QACT,WAAW,QAAQ,qBAAqB,QAAQ,oBAAoB;AAClE,iBAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA,MAEA,aAAa,KAAK,QAAQ,eAAe;AACvC,YAAI,KAAK,SAAS,OAAO,KAAK;AAC9B,YAAI,UAAU,OAAO,GAAG;AACtB,cAAI,OAAO,QAAQ,kBAAkB;AACnC,gBAAI,QAAQ,OAAO;AACjB,qBAAO;AAAA,YACT;AACA,gBAAI,OAAO,MAAM;AAAA,cACf,CAAC,MAAM,EAAE,SAAS,KAAK,EAAE,SAAS,cAAc,EAAE,SAAS,SAAS,EAAE,MAAM,YAAY,eAAe,EAAE,MAAM,YAAY;AAAA,YAC7H,GAAG;AACD,mBAAK;AAAA,YACP;AAAA,UACF,WAAW,qBAAqB,KAAK,OAAO,GAAG,KAAK,QAAQ,YAAY,QAAQ,cAAc;AAC5F,iBAAK;AAAA,UACP;AAAA,QACF,WAAW,UAAU,OAAO,GAAG;AAC7B,cAAI,OAAO,QAAQ,mBAAmB,OAAO,QAAQ,UAAU,OAAO,QAAQ,SAAS;AACrF,iBAAK;AAAA,UACP;AAAA,QACF;AACA,YAAI,OAAO,GAAG;AACZ,cAAI,QAAQ,OAAO;AACjB,mBAAO;AAAA,UACT;AACA,cAAI,QAAQ,QAAQ;AAClB,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAEA,IAAM,iBAAiB,CAAC,SAAS;AAC/B,UAAI,KAAK,SAAS,GAAG;AACnB,aAAK,MAAM,QAAQ,CAAC,GAAG,MAAM;AAC3B,cAAI,EAAE,SAAS,KAAK,EAAE,SAAS,WAAW,EAAE,OAAO;AACjD,iBAAK,MAAM,CAAC,IAAI;AAAA,cACd,MAAM;AAAA,cACN,MAAM;AAAA,cACN,KAAK,uBAAuB,SAAS,MAAM,EAAE,GAAG;AAAA,cAChD,KAAK,eAAe,EAAE,MAAM,SAAS,EAAE,GAAG;AAAA,cAC1C,WAAW,CAAC;AAAA,cACZ,KAAK,EAAE;AAAA,YACT;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,IAAM,iBAAiB,CAAC,SAAS,QAAQ;AACvC,YAAM,aAAa,iBAAiB,OAAO;AAC3C,aAAO;AAAA,QACL,KAAK,UAAU,UAAU;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AASA,IAAM,gBAAgB;AAAA,MACpB,0BAA0B;AAAA,MAC1B,MAAM;AAAA,MACN,0BAA0B;AAAA,MAC1B,MAAM;AAAA,MACN,0BAA0B;AAAA,MAC1B,MAAM;AAAA,MACN,0BAA0B;AAAA,MAC1B,MAAM;AAAA,MACN,gCAAgC;AAAA,MAChC,MAAM;AAAA,MACN,4BAA4B;AAAA,MAC5B,MAAM;AAAA,MACN,mCAAmC;AAAA,MACnC,MAAM;AAAA,MACN,+BAA+B;AAAA,MAC/B,MAAM;AAAA,MACN,0BAA0B;AAAA,MAC1B,MAAM;AAAA,MACN,iCAAiC;AAAA,MACjC,MAAM;AAAA,MACN,6BAA6B;AAAA,MAC7B,MAAM;AAAA,MACN,oBAAoB;AAAA,MACpB,MAAM;AAAA,IACR;AACA,IAAM,mBAAmB;AAAA,MACvB,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,MACN,CAAC,EAAE,GAAG;AAAA,IACR;AAEA,IAAM,iBAAiB,CAAC,KAAK,MAAM,YAAY;AAC7C,YAAM,EAAE,KAAK,IAAI,IAAI;AACrB,UAAI,CAAC,KAAK;AACR,gBAAQ;AAAA,UACN,uBAAuB,IAAI,GAAG;AAAA,QAChC;AAAA,MACF;AACA,UAAI,KAAK,SAAS,QAAQ;AACxB,gBAAQ;AAAA,UACN,uBAAuB,IAAI,GAAG;AAAA,QAChC;AACA,aAAK,SAAS,SAAS;AAAA,MACzB;AACA,aAAO;AAAA,QACL,OAAO;AAAA,UACL;AAAA,YACE,uBAAuB,aAAa,MAAM,GAAG;AAAA,YAC7C,OAAO,uBAAuB,IAAI,IAAI;AAAA,UACxC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,IAAM,iBAAiB,CAAC,KAAK,MAAM,YAAY;AAC7C,YAAM,EAAE,KAAK,IAAI,IAAI;AACrB,UAAI,CAAC,KAAK;AACR,gBAAQ;AAAA,UACN,uBAAuB,IAAI,GAAG;AAAA,QAChC;AAAA,MACF;AACA,UAAI,KAAK,SAAS,QAAQ;AACxB,gBAAQ;AAAA,UACN,uBAAuB,IAAI,GAAG;AAAA,QAChC;AACA,aAAK,SAAS,SAAS;AAAA,MACzB;AACA,aAAO;AAAA,QACL,OAAO;AAAA,UACL;AAAA,YACE,uBAAuB,eAAe,IAAI;AAAA,YAC1C,MAAM,gBAAgB,KAAK,OAAO,IAAI,IAAI,MAAM;AAAA,cAC9C,QAAQ,aAAa,iBAAiB;AAAA,cACtC,CAAC,GAAG;AAAA,cACJ;AAAA,YACF,IAAI,uBAAuB,IAAI,IAAI;AAAA,UACrC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,IAAMD,kBAAiB,CAAC,KAAK,MAAM,YAAY;AAC7C,YAAM,aAAa,eAAiB,KAAK,MAAM,OAAO;AACtD,UAAI,CAAC,WAAW,MAAM,UAAU,KAAK,YAAY,GAAG;AAClD,eAAO;AAAA,MACT;AACA,UAAI,IAAI,KAAK;AACX,gBAAQ;AAAA,UACN;AAAA,YACE;AAAA,YACA,IAAI,IAAI;AAAA,UACV;AAAA,QACF;AAAA,MACF;AACA,eAAS,uBAAuB;AAC9B,cAAM,QAAQ,QAAQ,MAAM,MAAM;AAClC,YAAI,SAAS,cAAc,MAAM,KAAK,OAAO,GAAG;AAC9C,kBAAQ;AAAA,YACN;AAAA,cACE;AAAA,cACA,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,YAAM,EAAE,IAAI,IAAI;AAChB,YAAM,kBAAkB,QAAQ,gBAAgB,GAAG;AACnD,UAAI,QAAQ,WAAW,QAAQ,cAAc,QAAQ,YAAY,iBAAiB;AAChF,YAAI,iBAAiB;AACrB,YAAI,gBAAgB;AACpB,YAAI,QAAQ,WAAW,iBAAiB;AACtC,gBAAM,OAAO,SAAS,MAAM,MAAM;AAClC,cAAI,MAAM;AACR,gBAAI,KAAK,SAAS,GAAG;AACnB,+BAAiB;AAAA,YACnB,WAAW,KAAK,OAAO;AACrB,sBAAQ,KAAK,MAAM,SAAS;AAAA,gBAC1B,KAAK;AACH,mCAAiB;AACjB;AAAA,gBACF,KAAK;AACH,mCAAiB;AACjB;AAAA,gBACF,KAAK;AACH,kCAAgB;AAChB,0BAAQ;AAAA,oBACN;AAAA,sBACE;AAAA,sBACA,IAAI;AAAA,oBACN;AAAA,kBACF;AACA;AAAA,gBACF;AACE,kBAA6C,qBAAqB;AAClE;AAAA,cACJ;AAAA,YACF;AAAA,UACF,WAAW,mBAAmB,IAAI,GAAG;AACnC,6BAAiB;AAAA,UACnB,OAAO;AACL,YAA6C,qBAAqB;AAAA,UACpE;AAAA,QACF,WAAW,QAAQ,UAAU;AAC3B,2BAAiB;AAAA,QACnB,OAAO;AACL,UAA6C,qBAAqB;AAAA,QACpE;AACA,YAAI,CAAC,eAAe;AAClB,qBAAW,cAAc,QAAQ,OAAO,cAAc;AAAA,QACxD;AAAA,MACF,OAAO;AACL,gBAAQ;AAAA,UACN;AAAA,YACE;AAAA,YACA,IAAI;AAAA,UACN;AAAA,QACF;AAAA,MACF;AACA,iBAAW,QAAQ,WAAW,MAAM;AAAA,QAClC,CAAC,MAAM,EAAE,EAAE,IAAI,SAAS,KAAK,EAAE,IAAI,YAAY;AAAA,MACjD;AACA,aAAO;AAAA,IACT;AAEA,IAAM,wBAAwC,QAAQ,sBAAsB;AAC5E,IAAM,mBAAmC;AAAA;AAAA,MAEvC;AAAA,IACF;AACA,IAAM,mBAAmC,QAAQ,YAAY;AAC7D,IAAM,kBAAkC,QAAQ,8BAA8B;AAC9E,IAAM,mBAAmB,CAAC,KAAK,WAAW,SAAS,QAAQ;AACzD,YAAM,eAAe,CAAC;AACtB,YAAM,kBAAkB,CAAC;AACzB,YAAM,uBAAuB,CAAC;AAC9B,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,cAAM,WAAW,UAAU,CAAC,EAAE;AAC9B,YAAI,aAAa,YAAY;AAAA,UAC3B;AAAA,UACA;AAAA,UACA;AAAA,QACF,GAAG;AACD,+BAAqB,KAAK,QAAQ;AAAA,QACpC,WAAW,sBAAsB,QAAQ,GAAG;AAC1C,+BAAqB,KAAK,QAAQ;AAAA,QACpC,OAAO;AACL,cAAI,iBAAiB,QAAQ,GAAG;AAC9B,gBAAI,YAAY,GAAG,GAAG;AACpB,kBAAI,gBAAgB,IAAI,QAAQ,YAAY,CAAC,GAAG;AAC9C,6BAAa,KAAK,QAAQ;AAAA,cAC5B,OAAO;AACL,gCAAgB,KAAK,QAAQ;AAAA,cAC/B;AAAA,YACF,OAAO;AACL,2BAAa,KAAK,QAAQ;AAC1B,8BAAgB,KAAK,QAAQ;AAAA,YAC/B;AAAA,UACF,OAAO;AACL,gBAAI,iBAAiB,QAAQ,GAAG;AAC9B,8BAAgB,KAAK,QAAQ;AAAA,YAC/B,OAAO;AACL,2BAAa,KAAK,QAAQ;AAAA,YAC5B;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,IAAM,iBAAiB,CAAC,KAAK,UAAU;AACrC,YAAM,gBAAgB,YAAY,GAAG,KAAK,IAAI,QAAQ,YAAY,MAAM;AACxE,aAAO,gBAAgB,uBAAuB,OAAO,IAAI,IAAI,IAAI,SAAS,IAAI,yBAAyB;AAAA,QACrG;AAAA,QACA;AAAA,QACA,sBAAsB,KAAK;AAAA,QAC3B;AAAA,QACA;AAAA,MACF,CAAC,IAAI;AAAA,IACP;AACA,IAAMC,eAAc,CAAC,KAAK,MAAM,YAAY;AAC1C,aAAO,YAAc,KAAK,MAAM,SAAS,CAAC,eAAe;AACvD,cAAM,EAAE,UAAU,IAAI;AACtB,YAAI,CAAC,UAAU,OAAQ,QAAO;AAC9B,YAAI,EAAE,KAAK,OAAO,WAAW,IAAI,WAAW,MAAM,CAAC;AACnD,cAAM,EAAE,cAAc,iBAAiB,qBAAqB,IAAI,iBAAiB,KAAK,WAAW,SAAS,IAAI,GAAG;AACjH,YAAI,gBAAgB,SAAS,OAAO,GAAG;AACrC,gBAAM,eAAe,KAAK,eAAe;AAAA,QAC3C;AACA,YAAI,gBAAgB,SAAS,QAAQ,GAAG;AACtC,gBAAM,eAAe,KAAK,WAAW;AAAA,QACvC;AACA,YAAI,gBAAgB,QAAQ;AAC1B,uBAAa,qBAAqB,QAAQ,OAAO,mBAAmB,GAAG;AAAA,YACrE;AAAA,YACA,KAAK,UAAU,eAAe;AAAA,UAChC,CAAC;AAAA,QACH;AACA,YAAI,aAAa;AAAA,SAChB,CAAC,YAAY,GAAG,KAAK,gBAAgB,IAAI,QAAQ,YAAY,CAAC,IAAI;AACjE,uBAAa,qBAAqB,QAAQ,OAAO,cAAc,GAAG;AAAA,YAChE;AAAA,YACA,KAAK,UAAU,YAAY;AAAA,UAC7B,CAAC;AAAA,QACH;AACA,YAAI,qBAAqB,QAAQ;AAC/B,gBAAM,kBAAkB,qBAAqB,IAAI,UAAU,EAAE,KAAK,EAAE;AACpE,gBAAM,YAAY,GAAG,IAAI,uBAAuB,GAAG,IAAI,OAAO,GAAG,eAAe,IAAI,IAAI,IAAI,yBAAyB,CAAC,KAAK,KAAK,QAAQ,eAAe,GAAG,CAAC;AAAA,QAC7J;AACA,eAAO;AAAA,UACL,OAAO,CAAC,qBAAqB,KAAK,UAAU,CAAC;AAAA,QAC/C;AAAA,MACF,CAAC;AAAA,IACH;AAEA,IAAM,gBAAgB,CAAC,KAAK,MAAM,YAAY;AAC5C,YAAM,EAAE,KAAK,IAAI,IAAI;AACrB,UAAI,CAAC,KAAK;AACR,gBAAQ;AAAA,UACN,uBAAuB,IAAI,GAAG;AAAA,QAChC;AAAA,MACF;AACA,aAAO;AAAA,QACL,OAAO,CAAC;AAAA,QACR,aAAa,QAAQ,OAAO,MAAM;AAAA,MACpC;AAAA,IACF;AAEA,IAAM,sBAAsB,CAAC,MAAM,YAAY;AAC7C,UAAI,KAAK,SAAS,KAAK,KAAK,YAAY,GAAG;AACzC,cAAM,YAAY,QAAQ,mBAAmB,KAAK,GAAG;AACrD,YAAI,cAAc,YAAY;AAC5B,iBAAO,MAAM;AACX,gBAAI,CAAC,KAAK,SAAS,QAAQ;AACzB;AAAA,YACF;AACA,gBAAI,oBAAoB,IAAI,GAAG;AAC7B,sBAAQ;AAAA,gBACN;AAAA,kBACE;AAAA,kBACA;AAAA,oBACE,OAAO,KAAK,SAAS,CAAC,EAAE,IAAI;AAAA,oBAC5B,KAAK,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC,EAAE,IAAI;AAAA,oBACjD,QAAQ;AAAA,kBACV;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AACA,kBAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,gBAAI,MAAM,SAAS,GAAG;AACpB,yBAAW,KAAK,MAAM,OAAO;AAC3B,oBAAI,EAAE,SAAS,KAAK,EAAE,SAAS,QAAQ;AACrC,uBAAK,MAAM,KAAK;AAAA,oBACd,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,SAAS,KAAK;AAAA,oBACd,OAAO;AAAA,oBACP,KAAK,KAAK;AAAA,kBACZ,CAAC;AAAA,gBACH;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AASA,IAAM,uBAAuB,CAAC,MAAM,YAAY;AAC9C,UAAI,KAAK,SAAS,KAAK,KAAK,YAAY,MAAM,KAAK,QAAQ,YAAY,KAAK,QAAQ,UAAU;AAC5F,QAA6C,QAAQ;AAAA,UACnD;AAAA,YACE;AAAA,YACA,KAAK;AAAA,UACP;AAAA,QACF;AACA,gBAAQ,WAAW;AAAA,MACrB;AAAA,IACF;AAiBA,IAAM,WAA2B,oBAAI,IAAI,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,CAAC;AAC7E,IAAM,WAA2B,oBAAI,IAAI,CAAC,CAAC;AAC3C,IAAM,oBAAoB;AAAA,MACxB,MAAsB,oBAAI,IAAI;AAAA,QAC5B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,MACD,UAA0B,oBAAI,IAAI,CAAC,QAAQ,CAAC;AAAA,MAC5C,QAAwB,oBAAI,IAAI,CAAC,YAAY,UAAU,IAAI,CAAC;AAAA;AAAA,MAE5D,OAAuB,oBAAI,IAAI,CAAC,WAAW,YAAY,SAAS,SAAS,OAAO,CAAC;AAAA,MACjF,IAAoB,oBAAI,IAAI,CAAC,MAAM,IAAI,CAAC;AAAA,MACxC,UAA0B,oBAAI,IAAI,CAAC,KAAK,CAAC;AAAA,MACzC,OAAuB,oBAAI,IAAI,CAAC,IAAI,CAAC;AAAA,MACrC,OAAuB,oBAAI,IAAI,CAAC,IAAI,CAAC;AAAA,MACrC,OAAuB,oBAAI,IAAI,CAAC,IAAI,CAAC;AAAA;AAAA,MAErC,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AACA,IAAM,mBAAmB;AAAA;AAAA,MAEvB,MAAM;AAAA,MACN,MAAsB,oBAAI,IAAI,CAAC,MAAM,CAAC;AAAA,MACtC,MAAsB,oBAAI,IAAI,CAAC,MAAM,CAAC;AAAA;AAAA,MAEtC,IAAoB,oBAAI,IAAI,CAAC,IAAI,CAAC;AAAA,MAClC,UAA0B,oBAAI,IAAI,CAAC,OAAO,CAAC;AAAA,MAC3C,SAAyB,oBAAI,IAAI,CAAC,OAAO,CAAC;AAAA,MAC1C,OAAuB,oBAAI,IAAI,CAAC,OAAO,CAAC;AAAA,MACxC,OAAuB,oBAAI,IAAI,CAAC,OAAO,CAAC;AAAA,MACxC,KAAqB,oBAAI,IAAI,CAAC,UAAU,CAAC;AAAA,MACzC,IAAoB,oBAAI,IAAI,CAAC,IAAI,CAAC;AAAA,MAClC,OAAuB,oBAAI,IAAI,CAAC,OAAO,CAAC;AAAA,MACxC,IAAoB,oBAAI,IAAI,CAAC,SAAS,SAAS,OAAO,CAAC;AAAA;AAAA,MAEvD,IAAoB,oBAAI,IAAI,CAAC,MAAM,KAAK,CAAC;AAAA,MACzC,IAAoB,oBAAI,IAAI,CAAC,MAAM,KAAK,CAAC;AAAA;AAAA,MAEzC,YAA4B,oBAAI,IAAI,CAAC,QAAQ,CAAC;AAAA;AAAA,MAE9C,SAAyB,oBAAI,IAAI,CAAC,SAAS,CAAC;AAAA,MAC5C,MAAsB,oBAAI,IAAI,CAAC,KAAK,CAAC;AAAA,IACvC;AACA,IAAM,uBAAuB;AAAA,MAC3B,GAAmB,oBAAI,IAAI;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,MACD,KAAqB,oBAAI,IAAI;AAAA,QAC3B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AACA,IAAM,sBAAsB;AAAA,MAC1B,GAAmB,oBAAI,IAAI,CAAC,GAAG,CAAC;AAAA,MAChC,QAAwB,oBAAI,IAAI,CAAC,QAAQ,CAAC;AAAA,MAC1C,IAAoB,oBAAI,IAAI,CAAC,MAAM,IAAI,CAAC;AAAA,MACxC,IAAoB,oBAAI,IAAI,CAAC,MAAM,IAAI,CAAC;AAAA,MACxC,MAAsB,oBAAI,IAAI,CAAC,MAAM,CAAC;AAAA,MACtC,IAAoB,oBAAI,IAAI,CAAC,IAAI,CAAC;AAAA,MAClC,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN;AAEA,IAAM,sBAAsB,CAAC,MAAM,YAAY;AAC7C,UAAI,KAAK,SAAS,KAAK,KAAK,YAAY,KAAK,QAAQ,UAAU,QAAQ,OAAO,SAAS,KAAK,QAAQ,OAAO,YAAY,KAAK,CAAC,mBAAmB,QAAQ,OAAO,KAAK,KAAK,GAAG,GAAG;AAC7K,cAAM,QAAQ,IAAI;AAAA,UAChB,IAAI,KAAK,GAAG,yBAAyB,QAAQ,OAAO,GAAG;AAAA,QACzD;AACA,cAAM,MAAM,KAAK;AACjB,gBAAQ,OAAO,KAAK;AAAA,MACtB;AAAA,IACF;AAEA,IAAM,oBAAoB;AAAA,MACxB;AAAA,MACA,GAAG,OAA4C,CAAC,qBAAqB,mBAAmB,IAAI,CAAC;AAAA,IAC/F;AACA,IAAM,yBAAyB;AAAA,MAC7B,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAOD;AAAA;AAAA,MAEP,IAAIC;AAAA;AAAA,MAEJ,MAAM;AAAA,IACR;AAAA;AAAA;;;ACppBA;AAAA;AAAA;AAOA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAE5D,QAAI,cAAc;AAClB,QAAI,aAAa;AACjB,QAAI,SAAS;AAEb,aAAS,yBAAyB,GAAG;AACnC,UAAI,IAAI,uBAAO,OAAO,IAAI;AAC1B,UAAI,GAAG;AACL,iBAAS,KAAK,GAAG;AACf,YAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QACZ;AAAA,MACF;AACA,QAAE,UAAU;AACZ,aAAO,OAAO,OAAO,CAAC;AAAA,IACxB;AAEA,QAAI,wBAAqC,yBAAyB,UAAU;AAE5E,QAAM,eAA+B,uBAAO,OAAO,IAAI;AACvD,aAAS,kBAAkB,UAAU,SAAS;AAC5C,UAAI,CAAC,OAAO,SAAS,QAAQ,GAAG;AAC9B,YAAI,SAAS,UAAU;AACrB,qBAAW,SAAS;AAAA,QACtB,OAAO;AACL,qBAAW,KAAK,6BAA6B,QAAQ;AACrD,iBAAO,OAAO;AAAA,QAChB;AAAA,MACF;AACA,YAAM,MAAM,OAAO,YAAY,UAAU,OAAO;AAChD,YAAM,SAAS,aAAa,GAAG;AAC/B,UAAI,QAAQ;AACV,eAAO;AAAA,MACT;AACA,UAAI,SAAS,CAAC,MAAM,KAAK;AACvB,cAAM,KAAK,SAAS,cAAc,QAAQ;AAC1C,YAAI,CAAC,IAAI;AACP,qBAAW,KAAK,2CAA2C,QAAQ,EAAE;AAAA,QACvE;AACA,mBAAW,KAAK,GAAG,YAAY;AAAA,MACjC;AACA,YAAM,OAAO,OAAO;AAAA,QAClB;AAAA,UACE,aAAa;AAAA,UACb;AAAA,UACA,QAAQ,CAAC,MAAM,QAAQ,GAAG,IAAI;AAAA,QAChC;AAAA,QACA;AAAA,MACF;AACA,UAAI,CAAC,KAAK,mBAAmB,OAAO,mBAAmB,aAAa;AAClE,aAAK,kBAAkB,CAAC,QAAQ,CAAC,CAAC,eAAe,IAAI,GAAG;AAAA,MAC1D;AACA,YAAM,EAAE,KAAK,IAAI,YAAY,QAAQ,UAAU,IAAI;AACnD,eAAS,QAAQ,KAAK,YAAY,OAAO;AACvC,cAAM,UAAU,YAAY,IAAI,UAAU,+BAA+B,IAAI,OAAO;AACpF,cAAM,YAAY,IAAI,OAAO,OAAO;AAAA,UAClC;AAAA,UACA,IAAI,IAAI,MAAM;AAAA,UACd,IAAI,IAAI,IAAI;AAAA,QACd;AACA,mBAAW,KAAK,YAAY,GAAG,OAAO;AAAA,EACxC,SAAS,KAAK,OAAO;AAAA,MACrB;AACA,YAAM,SAAS,IAAI,SAAS,OAAO,IAAI,EAAE,qBAAqB;AAC9D,aAAO,MAAM;AACb,aAAO,aAAa,GAAG,IAAI;AAAA,IAC7B;AACA,eAAW,wBAAwB,iBAAiB;AAEpD,YAAQ,UAAU;AAClB,WAAO,KAAK,UAAU,EAAE,QAAQ,SAAU,GAAG;AAC3C,UAAI,MAAM,aAAa,CAAC,OAAO,UAAU,eAAe,KAAK,SAAS,CAAC,EAAG,SAAQ,CAAC,IAAI,WAAW,CAAC;AAAA,IACrG,CAAC;AAAA;AAAA;;;AC/ED;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;", "names": ["isComponent", "stack", "transformModel", "transformOn"]}