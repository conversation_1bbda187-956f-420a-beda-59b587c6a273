{"version": 3, "sources": ["../../.pnpm/codemirror@5.65.18/node_modules/codemirror/mode/r/r.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n\"use strict\";\n\nCodeMirror.registerHelper(\"wordChars\", \"r\", /[\\w.]/);\n\nCodeMirror.defineMode(\"r\", function(config) {\n  function wordObj(words) {\n    var res = {};\n    for (var i = 0; i < words.length; ++i) res[words[i]] = true;\n    return res;\n  }\n  var commonAtoms = [\"NULL\", \"NA\", \"Inf\", \"NaN\", \"NA_integer_\", \"NA_real_\", \"NA_complex_\", \"NA_character_\", \"TRUE\", \"FALSE\"];\n  var commonBuiltins = [\"list\", \"quote\", \"bquote\", \"eval\", \"return\", \"call\", \"parse\", \"deparse\"];\n  var commonKeywords = [\"if\", \"else\", \"repeat\", \"while\", \"function\", \"for\", \"in\", \"next\", \"break\"];\n  var commonBlockKeywords = [\"if\", \"else\", \"repeat\", \"while\", \"function\", \"for\"];\n\n  CodeMirror.registerHelper(\"hintWords\", \"r\", commonAtoms.concat(commonBuiltins, commonKeywords));\n\n  var atoms = wordObj(commonAtoms);\n  var builtins = wordObj(commonBuiltins);\n  var keywords = wordObj(commonKeywords);\n  var blockkeywords = wordObj(commonBlockKeywords);\n  var opChars = /[+\\-*\\/^<>=!&|~$:]/;\n  var curPunc;\n\n  function tokenBase(stream, state) {\n    curPunc = null;\n    var ch = stream.next();\n    if (ch == \"#\") {\n      stream.skipToEnd();\n      return \"comment\";\n    } else if (ch == \"0\" && stream.eat(\"x\")) {\n      stream.eatWhile(/[\\da-f]/i);\n      return \"number\";\n    } else if (ch == \".\" && stream.eat(/\\d/)) {\n      stream.match(/\\d*(?:e[+\\-]?\\d+)?/);\n      return \"number\";\n    } else if (/\\d/.test(ch)) {\n      stream.match(/\\d*(?:\\.\\d+)?(?:e[+\\-]\\d+)?L?/);\n      return \"number\";\n    } else if (ch == \"'\" || ch == '\"') {\n      state.tokenize = tokenString(ch);\n      return \"string\";\n    } else if (ch == \"`\") {\n      stream.match(/[^`]+`/);\n      return \"variable-3\";\n    } else if (ch == \".\" && stream.match(/.(?:[.]|\\d+)/)) {\n      return \"keyword\";\n    } else if (/[a-zA-Z\\.]/.test(ch)) {\n      stream.eatWhile(/[\\w\\.]/);\n      var word = stream.current();\n      if (atoms.propertyIsEnumerable(word)) return \"atom\";\n      if (keywords.propertyIsEnumerable(word)) {\n        // Block keywords start new blocks, except 'else if', which only starts\n        // one new block for the 'if', no block for the 'else'.\n        if (blockkeywords.propertyIsEnumerable(word) &&\n            !stream.match(/\\s*if(\\s+|$)/, false))\n          curPunc = \"block\";\n        return \"keyword\";\n      }\n      if (builtins.propertyIsEnumerable(word)) return \"builtin\";\n      return \"variable\";\n    } else if (ch == \"%\") {\n      if (stream.skipTo(\"%\")) stream.next();\n      return \"operator variable-2\";\n    } else if (\n        (ch == \"<\" && stream.eat(\"-\")) ||\n        (ch == \"<\" && stream.match(\"<-\")) ||\n        (ch == \"-\" && stream.match(/>>?/))\n      ) {\n      return \"operator arrow\";\n    } else if (ch == \"=\" && state.ctx.argList) {\n      return \"arg-is\";\n    } else if (opChars.test(ch)) {\n      if (ch == \"$\") return \"operator dollar\";\n      stream.eatWhile(opChars);\n      return \"operator\";\n    } else if (/[\\(\\){}\\[\\];]/.test(ch)) {\n      curPunc = ch;\n      if (ch == \";\") return \"semi\";\n      return null;\n    } else {\n      return null;\n    }\n  }\n\n  function tokenString(quote) {\n    return function(stream, state) {\n      if (stream.eat(\"\\\\\")) {\n        var ch = stream.next();\n        if (ch == \"x\") stream.match(/^[a-f0-9]{2}/i);\n        else if ((ch == \"u\" || ch == \"U\") && stream.eat(\"{\") && stream.skipTo(\"}\")) stream.next();\n        else if (ch == \"u\") stream.match(/^[a-f0-9]{4}/i);\n        else if (ch == \"U\") stream.match(/^[a-f0-9]{8}/i);\n        else if (/[0-7]/.test(ch)) stream.match(/^[0-7]{1,2}/);\n        return \"string-2\";\n      } else {\n        var next;\n        while ((next = stream.next()) != null) {\n          if (next == quote) { state.tokenize = tokenBase; break; }\n          if (next == \"\\\\\") { stream.backUp(1); break; }\n        }\n        return \"string\";\n      }\n    };\n  }\n\n  var ALIGN_YES = 1, ALIGN_NO = 2, BRACELESS = 4\n\n  function push(state, type, stream) {\n    state.ctx = {type: type,\n                 indent: state.indent,\n                 flags: 0,\n                 column: stream.column(),\n                 prev: state.ctx};\n  }\n  function setFlag(state, flag) {\n    var ctx = state.ctx\n    state.ctx = {type: ctx.type,\n                 indent: ctx.indent,\n                 flags: ctx.flags | flag,\n                 column: ctx.column,\n                 prev: ctx.prev}\n  }\n  function pop(state) {\n    state.indent = state.ctx.indent;\n    state.ctx = state.ctx.prev;\n  }\n\n  return {\n    startState: function() {\n      return {tokenize: tokenBase,\n              ctx: {type: \"top\",\n                    indent: -config.indentUnit,\n                    flags: ALIGN_NO},\n              indent: 0,\n              afterIdent: false};\n    },\n\n    token: function(stream, state) {\n      if (stream.sol()) {\n        if ((state.ctx.flags & 3) == 0) state.ctx.flags |= ALIGN_NO\n        if (state.ctx.flags & BRACELESS) pop(state)\n        state.indent = stream.indentation();\n      }\n      if (stream.eatSpace()) return null;\n      var style = state.tokenize(stream, state);\n      if (style != \"comment\" && (state.ctx.flags & ALIGN_NO) == 0) setFlag(state, ALIGN_YES)\n\n      if ((curPunc == \";\" || curPunc == \"{\" || curPunc == \"}\") && state.ctx.type == \"block\") pop(state);\n      if (curPunc == \"{\") push(state, \"}\", stream);\n      else if (curPunc == \"(\") {\n        push(state, \")\", stream);\n        if (state.afterIdent) state.ctx.argList = true;\n      }\n      else if (curPunc == \"[\") push(state, \"]\", stream);\n      else if (curPunc == \"block\") push(state, \"block\", stream);\n      else if (curPunc == state.ctx.type) pop(state);\n      else if (state.ctx.type == \"block\" && style != \"comment\") setFlag(state, BRACELESS)\n      state.afterIdent = style == \"variable\" || style == \"keyword\";\n      return style;\n    },\n\n    indent: function(state, textAfter) {\n      if (state.tokenize != tokenBase) return 0;\n      var firstChar = textAfter && textAfter.charAt(0), ctx = state.ctx,\n          closing = firstChar == ctx.type;\n      if (ctx.flags & BRACELESS) ctx = ctx.prev\n      if (ctx.type == \"block\") return ctx.indent + (firstChar == \"{\" ? 0 : config.indentUnit);\n      else if (ctx.flags & ALIGN_YES) return ctx.column + (closing ? 0 : 1);\n      else return ctx.indent + (closing ? 0 : config.indentUnit);\n    },\n\n    lineComment: \"#\"\n  };\n});\n\nCodeMirror.defineMIME(\"text/x-rsrc\", \"r\");\n\n});\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACxB;AAEA,MAAAA,YAAW,eAAe,aAAa,KAAK,OAAO;AAEnD,MAAAA,YAAW,WAAW,KAAK,SAAS,QAAQ;AAC1C,iBAAS,QAAQ,OAAO;AACtB,cAAI,MAAM,CAAC;AACX,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,EAAG,KAAI,MAAM,CAAC,CAAC,IAAI;AACvD,iBAAO;AAAA,QACT;AACA,YAAI,cAAc,CAAC,QAAQ,MAAM,OAAO,OAAO,eAAe,YAAY,eAAe,iBAAiB,QAAQ,OAAO;AACzH,YAAI,iBAAiB,CAAC,QAAQ,SAAS,UAAU,QAAQ,UAAU,QAAQ,SAAS,SAAS;AAC7F,YAAI,iBAAiB,CAAC,MAAM,QAAQ,UAAU,SAAS,YAAY,OAAO,MAAM,QAAQ,OAAO;AAC/F,YAAI,sBAAsB,CAAC,MAAM,QAAQ,UAAU,SAAS,YAAY,KAAK;AAE7E,QAAAA,YAAW,eAAe,aAAa,KAAK,YAAY,OAAO,gBAAgB,cAAc,CAAC;AAE9F,YAAI,QAAQ,QAAQ,WAAW;AAC/B,YAAI,WAAW,QAAQ,cAAc;AACrC,YAAI,WAAW,QAAQ,cAAc;AACrC,YAAI,gBAAgB,QAAQ,mBAAmB;AAC/C,YAAI,UAAU;AACd,YAAI;AAEJ,iBAAS,UAAU,QAAQ,OAAO;AAChC,oBAAU;AACV,cAAI,KAAK,OAAO,KAAK;AACrB,cAAI,MAAM,KAAK;AACb,mBAAO,UAAU;AACjB,mBAAO;AAAA,UACT,WAAW,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AACvC,mBAAO,SAAS,UAAU;AAC1B,mBAAO;AAAA,UACT,WAAW,MAAM,OAAO,OAAO,IAAI,IAAI,GAAG;AACxC,mBAAO,MAAM,oBAAoB;AACjC,mBAAO;AAAA,UACT,WAAW,KAAK,KAAK,EAAE,GAAG;AACxB,mBAAO,MAAM,+BAA+B;AAC5C,mBAAO;AAAA,UACT,WAAW,MAAM,OAAO,MAAM,KAAK;AACjC,kBAAM,WAAW,YAAY,EAAE;AAC/B,mBAAO;AAAA,UACT,WAAW,MAAM,KAAK;AACpB,mBAAO,MAAM,QAAQ;AACrB,mBAAO;AAAA,UACT,WAAW,MAAM,OAAO,OAAO,MAAM,cAAc,GAAG;AACpD,mBAAO;AAAA,UACT,WAAW,aAAa,KAAK,EAAE,GAAG;AAChC,mBAAO,SAAS,QAAQ;AACxB,gBAAI,OAAO,OAAO,QAAQ;AAC1B,gBAAI,MAAM,qBAAqB,IAAI,EAAG,QAAO;AAC7C,gBAAI,SAAS,qBAAqB,IAAI,GAAG;AAGvC,kBAAI,cAAc,qBAAqB,IAAI,KACvC,CAAC,OAAO,MAAM,gBAAgB,KAAK;AACrC,0BAAU;AACZ,qBAAO;AAAA,YACT;AACA,gBAAI,SAAS,qBAAqB,IAAI,EAAG,QAAO;AAChD,mBAAO;AAAA,UACT,WAAW,MAAM,KAAK;AACpB,gBAAI,OAAO,OAAO,GAAG,EAAG,QAAO,KAAK;AACpC,mBAAO;AAAA,UACT,WACK,MAAM,OAAO,OAAO,IAAI,GAAG,KAC3B,MAAM,OAAO,OAAO,MAAM,IAAI,KAC9B,MAAM,OAAO,OAAO,MAAM,KAAK,GAChC;AACF,mBAAO;AAAA,UACT,WAAW,MAAM,OAAO,MAAM,IAAI,SAAS;AACzC,mBAAO;AAAA,UACT,WAAW,QAAQ,KAAK,EAAE,GAAG;AAC3B,gBAAI,MAAM,IAAK,QAAO;AACtB,mBAAO,SAAS,OAAO;AACvB,mBAAO;AAAA,UACT,WAAW,gBAAgB,KAAK,EAAE,GAAG;AACnC,sBAAU;AACV,gBAAI,MAAM,IAAK,QAAO;AACtB,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,iBAAS,YAAY,OAAO;AAC1B,iBAAO,SAAS,QAAQ,OAAO;AAC7B,gBAAI,OAAO,IAAI,IAAI,GAAG;AACpB,kBAAI,KAAK,OAAO,KAAK;AACrB,kBAAI,MAAM,IAAK,QAAO,MAAM,eAAe;AAAA,wBACjC,MAAM,OAAO,MAAM,QAAQ,OAAO,IAAI,GAAG,KAAK,OAAO,OAAO,GAAG,EAAG,QAAO,KAAK;AAAA,uBAC/E,MAAM,IAAK,QAAO,MAAM,eAAe;AAAA,uBACvC,MAAM,IAAK,QAAO,MAAM,eAAe;AAAA,uBACvC,QAAQ,KAAK,EAAE,EAAG,QAAO,MAAM,aAAa;AACrD,qBAAO;AAAA,YACT,OAAO;AACL,kBAAI;AACJ,sBAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,oBAAI,QAAQ,OAAO;AAAE,wBAAM,WAAW;AAAW;AAAA,gBAAO;AACxD,oBAAI,QAAQ,MAAM;AAAE,yBAAO,OAAO,CAAC;AAAG;AAAA,gBAAO;AAAA,cAC/C;AACA,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAEA,YAAI,YAAY,GAAG,WAAW,GAAG,YAAY;AAE7C,iBAAS,KAAK,OAAO,MAAM,QAAQ;AACjC,gBAAM,MAAM;AAAA,YAAC;AAAA,YACA,QAAQ,MAAM;AAAA,YACd,OAAO;AAAA,YACP,QAAQ,OAAO,OAAO;AAAA,YACtB,MAAM,MAAM;AAAA,UAAG;AAAA,QAC9B;AACA,iBAAS,QAAQ,OAAO,MAAM;AAC5B,cAAI,MAAM,MAAM;AAChB,gBAAM,MAAM;AAAA,YAAC,MAAM,IAAI;AAAA,YACV,QAAQ,IAAI;AAAA,YACZ,OAAO,IAAI,QAAQ;AAAA,YACnB,QAAQ,IAAI;AAAA,YACZ,MAAM,IAAI;AAAA,UAAI;AAAA,QAC7B;AACA,iBAAS,IAAI,OAAO;AAClB,gBAAM,SAAS,MAAM,IAAI;AACzB,gBAAM,MAAM,MAAM,IAAI;AAAA,QACxB;AAEA,eAAO;AAAA,UACL,YAAY,WAAW;AACrB,mBAAO;AAAA,cAAC,UAAU;AAAA,cACV,KAAK;AAAA,gBAAC,MAAM;AAAA,gBACN,QAAQ,CAAC,OAAO;AAAA,gBAChB,OAAO;AAAA,cAAQ;AAAA,cACrB,QAAQ;AAAA,cACR,YAAY;AAAA,YAAK;AAAA,UAC3B;AAAA,UAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,gBAAI,OAAO,IAAI,GAAG;AAChB,mBAAK,MAAM,IAAI,QAAQ,MAAM,EAAG,OAAM,IAAI,SAAS;AACnD,kBAAI,MAAM,IAAI,QAAQ,UAAW,KAAI,KAAK;AAC1C,oBAAM,SAAS,OAAO,YAAY;AAAA,YACpC;AACA,gBAAI,OAAO,SAAS,EAAG,QAAO;AAC9B,gBAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AACxC,gBAAI,SAAS,cAAc,MAAM,IAAI,QAAQ,aAAa,EAAG,SAAQ,OAAO,SAAS;AAErF,iBAAK,WAAW,OAAO,WAAW,OAAO,WAAW,QAAQ,MAAM,IAAI,QAAQ,QAAS,KAAI,KAAK;AAChG,gBAAI,WAAW,IAAK,MAAK,OAAO,KAAK,MAAM;AAAA,qBAClC,WAAW,KAAK;AACvB,mBAAK,OAAO,KAAK,MAAM;AACvB,kBAAI,MAAM,WAAY,OAAM,IAAI,UAAU;AAAA,YAC5C,WACS,WAAW,IAAK,MAAK,OAAO,KAAK,MAAM;AAAA,qBACvC,WAAW,QAAS,MAAK,OAAO,SAAS,MAAM;AAAA,qBAC/C,WAAW,MAAM,IAAI,KAAM,KAAI,KAAK;AAAA,qBACpC,MAAM,IAAI,QAAQ,WAAW,SAAS,UAAW,SAAQ,OAAO,SAAS;AAClF,kBAAM,aAAa,SAAS,cAAc,SAAS;AACnD,mBAAO;AAAA,UACT;AAAA,UAEA,QAAQ,SAAS,OAAO,WAAW;AACjC,gBAAI,MAAM,YAAY,UAAW,QAAO;AACxC,gBAAI,YAAY,aAAa,UAAU,OAAO,CAAC,GAAG,MAAM,MAAM,KAC1D,UAAU,aAAa,IAAI;AAC/B,gBAAI,IAAI,QAAQ,UAAW,OAAM,IAAI;AACrC,gBAAI,IAAI,QAAQ,QAAS,QAAO,IAAI,UAAU,aAAa,MAAM,IAAI,OAAO;AAAA,qBACnE,IAAI,QAAQ,UAAW,QAAO,IAAI,UAAU,UAAU,IAAI;AAAA,gBAC9D,QAAO,IAAI,UAAU,UAAU,IAAI,OAAO;AAAA,UACjD;AAAA,UAEA,aAAa;AAAA,QACf;AAAA,MACF,CAAC;AAED,MAAAA,YAAW,WAAW,eAAe,GAAG;AAAA,IAExC,CAAC;AAAA;AAAA;", "names": ["CodeMirror"]}