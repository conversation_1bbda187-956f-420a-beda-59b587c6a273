import "./chunk-PLDDJCW6.js";

// node_modules/.pnpm/emoji-mart-vue-fast@15.0.3_vue@3.5.13_typescript@4.9.5_/node_modules/emoji-mart-vue-fast/src/utils/store.js
var NAMESPACE = "emoji-mart";
var _JSON = JSON;
var isLocalStorageSupported = typeof window !== "undefined" && "localStorage" in window;
var getter;
var setter;
function setHandlers(handlers) {
  handlers || (handlers = {});
  getter = handlers.getter;
  setter = handlers.setter;
}
function setNamespace(namespace) {
  NAMESPACE = namespace;
}
function update(state) {
  for (let key in state) {
    let value = state[key];
    set(key, value);
  }
}
function set(key, value) {
  if (setter) {
    setter(key, value);
  } else {
    if (!isLocalStorageSupported) return;
    try {
      window.localStorage[`${NAMESPACE}.${key}`] = _JSON.stringify(value);
    } catch (e) {
    }
  }
}
function get(key) {
  if (getter) {
    return getter(key);
  } else {
    if (!isLocalStorageSupported) return;
    try {
      var value = window.localStorage[`${NAMESPACE}.${key}`];
    } catch (e) {
      return;
    }
    if (value) {
      return JSON.parse(value);
    }
  }
}
var store_default = { update, set, get, setNamespace, setHandlers };

// node_modules/.pnpm/emoji-mart-vue-fast@15.0.3_vue@3.5.13_typescript@4.9.5_/node_modules/emoji-mart-vue-fast/src/utils/data.js
var mapping = {
  name: "a",
  unified: "b",
  non_qualified: "c",
  has_img_apple: "d",
  has_img_google: "e",
  has_img_twitter: "f",
  has_img_facebook: "h",
  keywords: "j",
  sheet: "k",
  emoticons: "l",
  text: "m",
  short_names: "n",
  added_in: "o"
};
var buildSearch = (emoji) => {
  const search = [];
  var addToSearch = (strings, split) => {
    if (!strings) {
      return;
    }
    ;
    (Array.isArray(strings) ? strings : [strings]).forEach((string) => {
      ;
      (split ? string.split(/[-|_|\s]+/) : [string]).forEach((s) => {
        s = s.toLowerCase();
        if (search.indexOf(s) == -1) {
          search.push(s);
        }
      });
    });
  };
  addToSearch(emoji.short_names, true);
  addToSearch(emoji.name, true);
  addToSearch(emoji.keywords, false);
  addToSearch(emoji.emoticons, false);
  return search.join(",");
};
function deepFreeze(object) {
  var propNames = Object.getOwnPropertyNames(object);
  for (let name of propNames) {
    let value = object[name];
    object[name] = value && typeof value === "object" ? deepFreeze(value) : value;
  }
  return Object.freeze(object);
}
var uncompress = (data) => {
  if (!data.compressed) {
    return data;
  }
  data.compressed = false;
  for (let id in data.emojis) {
    let emoji = data.emojis[id];
    for (let key in mapping) {
      emoji[key] = emoji[mapping[key]];
      delete emoji[mapping[key]];
    }
    if (!emoji.short_names) emoji.short_names = [];
    emoji.short_names.unshift(id);
    emoji.sheet_x = emoji.sheet[0];
    emoji.sheet_y = emoji.sheet[1];
    delete emoji.sheet;
    if (!emoji.text) emoji.text = "";
    if (!emoji.added_in) emoji.added_in = 6;
    emoji.added_in = emoji.added_in.toFixed(1);
    emoji.search = buildSearch(emoji);
  }
  data = deepFreeze(data);
  return data;
};

// node_modules/.pnpm/emoji-mart-vue-fast@15.0.3_vue@3.5.13_typescript@4.9.5_/node_modules/emoji-mart-vue-fast/src/utils/frequently.js
var DEFAULTS = [
  "+1",
  "grinning",
  "kissing_heart",
  "heart_eyes",
  "laughing",
  "stuck_out_tongue_winking_eye",
  "sweat_smile",
  "joy",
  "scream",
  "disappointed",
  "unamused",
  "weary",
  "sob",
  "sunglasses",
  "heart",
  "hankey"
];
var frequently;
var initialized;
var defaults = {};
function init() {
  initialized = true;
  frequently = store_default.get("frequently");
}
function add(emoji) {
  if (!initialized) init();
  var { id } = emoji;
  frequently || (frequently = defaults);
  frequently[id] || (frequently[id] = 0);
  frequently[id] += 1;
  store_default.set("last", id);
  store_default.set("frequently", frequently);
}
function get2(maxNumber) {
  if (!initialized) init();
  if (!frequently) {
    defaults = {};
    const result = [];
    let defaultLength = Math.min(maxNumber, DEFAULTS.length);
    for (let i = 0; i < defaultLength; i++) {
      defaults[DEFAULTS[i]] = parseInt((defaultLength - i) / 4, 10) + 1;
      result.push(DEFAULTS[i]);
    }
    return result;
  }
  const quantity = maxNumber;
  const frequentlyKeys = [];
  for (let key in frequently) {
    if (frequently.hasOwnProperty(key)) {
      frequentlyKeys.push(key);
    }
  }
  const sorted = frequentlyKeys.sort((a, b) => frequently[a] - frequently[b]).reverse();
  const sliced = sorted.slice(0, quantity);
  const last = store_default.get("last");
  if (last && sliced.indexOf(last) == -1) {
    sliced.pop();
    sliced.push(last);
  }
  return sliced;
}
var frequently_default = { add, get: get2 };

// node_modules/.pnpm/emoji-mart-vue-fast@15.0.3_vue@3.5.13_typescript@4.9.5_/node_modules/emoji-mart-vue-fast/src/components/index.js
import { default as default2 } from "E:/workplace/CW/JeecgBoot/jeecgboot-vue3/node_modules/.pnpm/emoji-mart-vue-fast@15.0.3_vue@3.5.13_typescript@4.9.5_/node_modules/emoji-mart-vue-fast/src/components/anchors.vue";
import { default as default3 } from "E:/workplace/CW/JeecgBoot/jeecgboot-vue3/node_modules/.pnpm/emoji-mart-vue-fast@15.0.3_vue@3.5.13_typescript@4.9.5_/node_modules/emoji-mart-vue-fast/src/components/category.vue";
import { default as default4 } from "E:/workplace/CW/JeecgBoot/jeecgboot-vue3/node_modules/.pnpm/emoji-mart-vue-fast@15.0.3_vue@3.5.13_typescript@4.9.5_/node_modules/emoji-mart-vue-fast/src/components/preview.vue";
import { default as default5 } from "E:/workplace/CW/JeecgBoot/jeecgboot-vue3/node_modules/.pnpm/emoji-mart-vue-fast@15.0.3_vue@3.5.13_typescript@4.9.5_/node_modules/emoji-mart-vue-fast/src/components/search.vue";
import { default as default6 } from "E:/workplace/CW/JeecgBoot/jeecgboot-vue3/node_modules/.pnpm/emoji-mart-vue-fast@15.0.3_vue@3.5.13_typescript@4.9.5_/node_modules/emoji-mart-vue-fast/src/components/skins.vue";
import { default as default7 } from "E:/workplace/CW/JeecgBoot/jeecgboot-vue3/node_modules/.pnpm/emoji-mart-vue-fast@15.0.3_vue@3.5.13_typescript@4.9.5_/node_modules/emoji-mart-vue-fast/src/components/Emoji.vue";
import { default as default8 } from "E:/workplace/CW/JeecgBoot/jeecgboot-vue3/node_modules/.pnpm/emoji-mart-vue-fast@15.0.3_vue@3.5.13_typescript@4.9.5_/node_modules/emoji-mart-vue-fast/src/components/Picker.vue";

// node_modules/.pnpm/emoji-mart-vue-fast@15.0.3_vue@3.5.13_typescript@4.9.5_/node_modules/emoji-mart-vue-fast/src/polyfills/stringFromCodePoint.js
var _String = String;
var stringFromCodePoint_default = _String.fromCodePoint || function stringFromCodePoint() {
  var MAX_SIZE = 16384;
  var codeUnits = [];
  var highSurrogate;
  var lowSurrogate;
  var index = -1;
  var length = arguments.length;
  if (!length) {
    return "";
  }
  var result = "";
  while (++index < length) {
    var codePoint = Number(arguments[index]);
    if (!isFinite(codePoint) || // `NaN`, `+Infinity`, or `-Infinity`
    codePoint < 0 || // not a valid Unicode code point
    codePoint > 1114111 || // not a valid Unicode code point
    Math.floor(codePoint) != codePoint) {
      throw RangeError("Invalid code point: " + codePoint);
    }
    if (codePoint <= 65535) {
      codeUnits.push(codePoint);
    } else {
      codePoint -= 65536;
      highSurrogate = (codePoint >> 10) + 55296;
      lowSurrogate = codePoint % 1024 + 56320;
      codeUnits.push(highSurrogate, lowSurrogate);
    }
    if (index + 1 === length || codeUnits.length > MAX_SIZE) {
      result += String.fromCharCode.apply(null, codeUnits);
      codeUnits.length = 0;
    }
  }
  return result;
};

// node_modules/.pnpm/emoji-mart-vue-fast@15.0.3_vue@3.5.13_typescript@4.9.5_/node_modules/emoji-mart-vue-fast/src/utils/index.js
function unifiedToNative(unified) {
  var unicodes = unified.split("-"), codePoints = unicodes.map((u) => `0x${u}`);
  return stringFromCodePoint_default.apply(null, codePoints);
}
function uniq(arr) {
  return arr.reduce((acc, item) => {
    if (acc.indexOf(item) === -1) {
      acc.push(item);
    }
    return acc;
  }, []);
}
function intersect(a, b) {
  const uniqA = uniq(a);
  const uniqB = uniq(b);
  return uniqA.filter((item) => uniqB.indexOf(item) >= 0);
}

// node_modules/.pnpm/emoji-mart-vue-fast@15.0.3_vue@3.5.13_typescript@4.9.5_/node_modules/emoji-mart-vue-fast/src/utils/emoji-data.js
var SHEET_COLUMNS = 61;
var COLONS_REGEX = /^(?:\:([^\:]+)\:)(?:\:skin-tone-(\d)\:)?$/;
var SKINS = ["1F3FA", "1F3FB", "1F3FC", "1F3FD", "1F3FE", "1F3FF"];
var EmojiIndex = class {
  /**
   * Constructor.
   *
   * @param {object} data - Raw json data, see the structure above.
   * @param {object} options - additional options, as an object:
   * @param {Function} emojisToShowFilter - optional, function to filter out
   *   some emojis, function(emoji) { return true|false }
   *   where `emoji` is an raw emoji object, see data.emojis above.
   * @param {Array} include - optional, a list of category ids to include.
   * @param {Array} exclude - optional, a list of category ids to exclude.
   * @param {Array} custom - optional, a list custom emojis, each emoji is
   *   an object, see data.emojis above for examples.
   */
  constructor(data, {
    emojisToShowFilter,
    include,
    exclude,
    custom,
    recent,
    recentLength = 20
  } = {}) {
    this._data = uncompress(data);
    this._emojisFilter = emojisToShowFilter || null;
    this._include = include || null;
    this._exclude = exclude || null;
    this._custom = custom || [];
    this._recent = recent || frequently_default.get(recentLength);
    this._emojis = {};
    this._nativeEmojis = {};
    this._emoticons = {};
    this._categories = [];
    this._recentCategory = { id: "recent", name: "Recent", emojis: [] };
    this._customCategory = { id: "custom", name: "Custom", emojis: [] };
    this._searchIndex = {};
    this.buildIndex();
    Object.freeze(this);
  }
  buildIndex() {
    let allCategories = this._data.categories;
    if (this._include) {
      allCategories = allCategories.filter((item) => {
        return this._include.includes(item.id);
      });
      allCategories = allCategories.sort((a, b) => {
        const indexA = this._include.indexOf(a.id);
        const indexB = this._include.indexOf(b.id);
        if (indexA < indexB) {
          return -1;
        }
        if (indexA > indexB) {
          return 1;
        }
        return 0;
      });
    }
    allCategories.forEach((categoryData) => {
      if (!this.isCategoryNeeded(categoryData.id)) {
        return;
      }
      let category = {
        id: categoryData.id,
        name: categoryData.name,
        emojis: []
      };
      categoryData.emojis.forEach((emojiId) => {
        let emoji = this.addEmoji(emojiId);
        if (emoji) {
          category.emojis.push(emoji);
        }
      });
      if (category.emojis.length) {
        this._categories.push(category);
      }
    });
    if (this.isCategoryNeeded("custom")) {
      if (this._custom.length > 0) {
        for (let customEmoji of this._custom) {
          this.addCustomEmoji(customEmoji);
        }
      }
      if (this._customCategory.emojis.length) {
        this._categories.push(this._customCategory);
      }
    }
    if (this.isCategoryNeeded("recent")) {
      if (this._recent.length) {
        this._recent.map((id) => {
          for (let customEmoji of this._customCategory.emojis) {
            if (customEmoji.id === id) {
              this._recentCategory.emojis.push(customEmoji);
              return;
            }
          }
          if (this.hasEmoji(id)) {
            this._recentCategory.emojis.push(this.emoji(id));
          }
          return;
        });
      }
      if (this._recentCategory.emojis.length) {
        this._categories.unshift(this._recentCategory);
      }
    }
  }
  /**
   * Find the emoji from the string
   */
  findEmoji(emoji, skin) {
    let matches = emoji.match(COLONS_REGEX);
    if (matches) {
      emoji = matches[1];
      if (matches[2]) {
        skin = parseInt(matches[2], 10);
      }
    }
    if (this._data.aliases.hasOwnProperty(emoji)) {
      emoji = this._data.aliases[emoji];
    }
    if (this._emojis.hasOwnProperty(emoji)) {
      let emojiObject = this._emojis[emoji];
      if (skin) {
        return emojiObject.getSkin(skin);
      }
      return emojiObject;
    }
    if (this._nativeEmojis.hasOwnProperty(emoji)) {
      return this._nativeEmojis[emoji];
    }
    return null;
  }
  categories() {
    return this._categories;
  }
  emoji(emojiId) {
    if (this._data.aliases.hasOwnProperty(emojiId)) {
      emojiId = this._data.aliases[emojiId];
    }
    let emoji = this._emojis[emojiId];
    if (!emoji) {
      throw new Error("Can not find emoji by id: " + emojiId);
    }
    return emoji;
  }
  firstEmoji() {
    let emoji = this._emojis[Object.keys(this._emojis)[0]];
    if (!emoji) {
      throw new Error("Can not get first emoji");
    }
    return emoji;
  }
  hasEmoji(emojiId) {
    if (this._data.aliases.hasOwnProperty(emojiId)) {
      emojiId = this._data.aliases[emojiId];
    }
    if (this._emojis[emojiId]) {
      return true;
    }
    return false;
  }
  nativeEmoji(unicodeEmoji) {
    if (this._nativeEmojis.hasOwnProperty(unicodeEmoji)) {
      return this._nativeEmojis[unicodeEmoji];
    }
    return null;
  }
  search(value, maxResults) {
    maxResults || (maxResults = 75);
    if (!value.length) {
      return null;
    }
    if (value == "-" || value == "-1") {
      return [this.emoji("-1")];
    }
    let values = value.toLowerCase().split(/[\s|,|\-|_]+/);
    let allResults = [];
    if (values.length > 2) {
      values = [values[0], values[1]];
    }
    allResults = values.map((value2) => {
      let emojis = this._emojis;
      let currentIndex = this._searchIndex;
      let length = 0;
      for (let charIndex = 0; charIndex < value2.length; charIndex++) {
        const char = value2[charIndex];
        length++;
        currentIndex[char] || (currentIndex[char] = {});
        currentIndex = currentIndex[char];
        if (!currentIndex.results) {
          let scores = {};
          currentIndex.results = [];
          currentIndex.emojis = {};
          for (let emojiId in emojis) {
            let emoji = emojis[emojiId];
            let search = emoji._data.search;
            let sub = value2.substr(0, length);
            let subIndex = search.indexOf(sub);
            if (subIndex != -1) {
              let score = subIndex + 1;
              if (sub == emojiId) score = 0;
              currentIndex.results.push(emoji);
              currentIndex.emojis[emojiId] = emoji;
              scores[emojiId] = score;
            }
          }
          currentIndex.results.sort((a, b) => {
            var aScore = scores[a.id], bScore = scores[b.id];
            return aScore - bScore;
          });
        }
        emojis = currentIndex.emojis;
      }
      return currentIndex.results;
    }).filter((a) => a);
    var results = null;
    if (allResults.length > 1) {
      results = intersect.apply(null, allResults);
    } else if (allResults.length) {
      results = allResults[0];
    } else {
      results = [];
    }
    if (results && results.length > maxResults) {
      results = results.slice(0, maxResults);
    }
    return results;
  }
  addCustomEmoji(customEmoji) {
    let emojiData = Object.assign({}, customEmoji, {
      id: customEmoji.short_names[0],
      custom: true
    });
    if (!emojiData.search) {
      emojiData.search = buildSearch(emojiData);
    }
    let emoji = new EmojiData(emojiData);
    this._emojis[emoji.id] = emoji;
    this._customCategory.emojis.push(emoji);
    return emoji;
  }
  addEmoji(emojiId) {
    let data = this._data.emojis[emojiId];
    if (!this.isEmojiNeeded(data)) {
      return false;
    }
    let emoji = new EmojiData(data);
    this._emojis[emojiId] = emoji;
    if (emoji.native) {
      this._nativeEmojis[emoji.native] = emoji;
    }
    if (emoji._skins) {
      for (let idx in emoji._skins) {
        let skin = emoji._skins[idx];
        if (skin.native) {
          this._nativeEmojis[skin.native] = skin;
        }
      }
    }
    if (emoji.emoticons) {
      emoji.emoticons.forEach((emoticon) => {
        if (this._emoticons[emoticon]) {
          return;
        }
        this._emoticons[emoticon] = emojiId;
      });
    }
    return emoji;
  }
  /**
   * Check if we need to include given category.
   *
   * @param {string} category_id - The category id.
   * @return {boolean} - Whether to include the emoji.
   */
  isCategoryNeeded(category_id) {
    let isIncluded = this._include && this._include.length ? this._include.indexOf(category_id) > -1 : true;
    let isExcluded = this._exclude && this._exclude.length ? this._exclude.indexOf(category_id) > -1 : false;
    if (!isIncluded || isExcluded) {
      return false;
    }
    return true;
  }
  /**
   * Check if we need to include given emoji.
   *
   * @param {object} emoji - The raw emoji object.
   * @return {boolean} - Whether to include the emoji.
   */
  isEmojiNeeded(emoji) {
    if (this._emojisFilter) {
      return this._emojisFilter(emoji);
    }
    return true;
  }
};
var EmojiData = class _EmojiData {
  constructor(data) {
    this._data = Object.assign({}, data);
    this._skins = null;
    if (this._data.skin_variations) {
      this._skins = [];
      for (var skinIdx in SKINS) {
        let skinKey = SKINS[skinIdx];
        let variationData = this._data.skin_variations[skinKey];
        let skinData = Object.assign({}, data);
        for (let k in variationData) {
          skinData[k] = variationData[k];
        }
        delete skinData.skin_variations;
        skinData["skin_tone"] = parseInt(skinIdx) + 1;
        this._skins.push(new _EmojiData(skinData));
      }
    }
    this._sanitized = sanitize(this._data);
    for (let key in this._sanitized) {
      this[key] = this._sanitized[key];
    }
    this.short_names = this._data.short_names;
    this.short_name = this._data.short_names[0];
    Object.freeze(this);
  }
  getSkin(skinIdx) {
    if (skinIdx && skinIdx != "native" && this._skins) {
      return this._skins[skinIdx - 1];
    }
    return this;
  }
  getPosition() {
    let adjustedColumns = SHEET_COLUMNS - 1, x = +(100 / adjustedColumns * this._data.sheet_x).toFixed(2), y = +(100 / adjustedColumns * this._data.sheet_y).toFixed(2);
    return `${x}% ${y}%`;
  }
  ariaLabel() {
    return [this.native].concat(this.short_names).filter(Boolean).join(", ");
  }
};
var EmojiView = class {
  /**
   * emoji - Emoji to display
   * set - string, emoji set name
   * native - boolean, whether to render native emoji
   * fallback - fallback function to render missing emoji, optional
   * emojiTooltip - wether we need to show the emoji tooltip, optional
   * emojiSize - emoji size in pixels, optional
   */
  constructor(emoji, skin, set2, native, fallback, emojiTooltip, emojiSize) {
    this._emoji = emoji;
    this._native = native;
    this._skin = skin;
    this._set = set2;
    this._fallback = fallback;
    this.canRender = this._canRender();
    this.cssClass = this._cssClass();
    this.cssStyle = this._cssStyle(emojiSize);
    this.content = this._content();
    this.title = emojiTooltip === true ? emoji.short_name : null;
    this.ariaLabel = emoji.ariaLabel();
    Object.freeze(this);
  }
  getEmoji() {
    return this._emoji.getSkin(this._skin);
  }
  _canRender() {
    return this._isCustom() || this._isNative() || this._hasEmoji() || this._fallback;
  }
  _cssClass() {
    return ["emoji-set-" + this._set, "emoji-type-" + this._emojiType()];
  }
  _cssStyle(emojiSize) {
    let cssStyle = {};
    if (this._isCustom()) {
      cssStyle = {
        backgroundImage: "url(" + this.getEmoji()._data.imageUrl + ")",
        backgroundSize: "100%",
        width: emojiSize + "px",
        height: emojiSize + "px"
      };
    } else if (this._hasEmoji() && !this._isNative()) {
      cssStyle = {
        backgroundPosition: this.getEmoji().getPosition()
      };
    }
    if (emojiSize) {
      if (this._isNative()) {
        cssStyle = Object.assign(cssStyle, {
          // font-size is used for native emoji which we need
          // to scale with 0.95 factor to have them look approximately
          // the same size as image-based emoji.
          fontSize: Math.round(emojiSize * 0.95 * 10) / 10 + "px"
        });
      } else {
        cssStyle = Object.assign(cssStyle, {
          width: emojiSize + "px",
          height: emojiSize + "px"
        });
      }
    }
    return cssStyle;
  }
  _content() {
    if (this._isCustom()) {
      return "";
    }
    if (this._isNative()) {
      return this.getEmoji().native;
    }
    if (this._hasEmoji()) {
      return "";
    }
    return this._fallback ? this._fallback(this.getEmoji()) : null;
  }
  _isNative() {
    return this._native;
  }
  _isCustom() {
    return this.getEmoji().custom;
  }
  _hasEmoji() {
    if (!this.getEmoji()._data) {
      return false;
    }
    const hasImage = this.getEmoji()._data["has_img_" + this._set];
    if (hasImage === void 0) {
      return true;
    }
    return hasImage;
  }
  _emojiType() {
    if (this._isCustom()) {
      return "custom";
    }
    if (this._isNative()) {
      return "native";
    }
    if (this._hasEmoji()) {
      return "image";
    }
    return "fallback";
  }
};
function sanitize(emoji) {
  var {
    name,
    short_names,
    skin_tone,
    skin_variations,
    emoticons,
    unified,
    custom,
    imageUrl
  } = emoji, id = emoji.id || short_names[0], colons = `:${id}:`;
  if (custom) {
    return {
      id,
      name,
      colons,
      emoticons,
      custom,
      imageUrl
    };
  }
  if (skin_tone) {
    colons += `:skin-tone-${skin_tone}:`;
  }
  return {
    id,
    name,
    colons,
    emoticons,
    unified: unified.toLowerCase(),
    skin: skin_tone || (skin_variations ? 1 : null),
    native: unifiedToNative(unified)
  };
}
export {
  default2 as Anchors,
  default3 as Category,
  default7 as Emoji,
  EmojiData,
  EmojiIndex,
  EmojiView,
  default8 as Picker,
  default4 as Preview,
  default5 as Search,
  default6 as Skins,
  frequently_default as frequently,
  sanitize,
  store_default as store,
  uncompress
};
//# sourceMappingURL=emoji-mart-vue-fast_src.js.map
