# costTrendByUnitWithTypes 函数性能优化方案

## 性能瓶颈分析

### 1. N+1 查询问题
**问题描述：** 当前函数在循环中调用 `fetchMonthlyDetail` 方法，每次调用都会触发多个数据库查询：
- 对于12个月的查询，会产生 24+ 次数据库查询
- 每个月份都单独查询日表和月表数据
- 处理量数据也是逐月单独获取

**影响：** 数据库连接开销大，查询时间随月份数量线性增长

### 2. 重复查询相同数据
**问题描述：** 
- 月表数据在每次循环中都重新查询
- 相同的处理量数据被重复获取
- 缺乏数据复用机制

**影响：** 浪费数据库资源，增加不必要的网络传输

### 3. 数据转换效率低
**问题描述：**
- 在循环中逐个创建 `CwCostTypePoint` 对象
- 重复调用 `getTypeKeyFromName` 方法进行类型转换
- 缺乏批量处理机制

**影响：** CPU 使用率高，内存分配频繁

### 4. 缺乏缓存机制
**问题描述：**
- 没有任何缓存机制
- 每次请求都重新计算所有数据
- 历史数据重复计算

**影响：** 响应时间长，服务器负载高

## 优化方案

### 1. 批量查询优化
**实现方式：**
```java
// 新增批量查询方法
private BatchMonthlyDetail fetchMonthlyDetailBatch(String unitKey, Date startDate, Date endDate) {
    // 一次性获取整年的数据
    Map<String, List<CwKrbRow>> monthlyRowsMap = getMonthlyRowsBatch(unitKey, startDate, endDate);
    // 批量获取处理量数据
    Map<String, BigDecimal> volumeDataMap = getVolumeDataBatch(unitKey, startDate, endDate);
    // 在内存中组装数据
    return assembleMonthlyData(monthlyRowsMap, volumeDataMap);
}
```

**优化效果：**
- 数据库查询次数从 O(n) 降低到 O(1)
- 对于12个月查询，从24次减少到2次数据库访问

### 2. 各单位服务批量查询方法
**接口定义：**
```java
// 为每个单位服务添加批量查询方法
Map<String, List<CwKrbRow>> sumByMonthRange(Date startDate, Date endDate);
```

**实现特点：**
- 一次性查询日期范围内的所有日表和月表数据
- 在内存中按月份分组处理
- 减少数据库连接和查询开销

### 3. 数据转换优化
**预构建映射表：**
```java
private Map<String, String> buildTypeKeyMap() {
    Map<String, String> typeKeyMap = new HashMap<>();
    typeKeyMap.put("材料", CwKrbRow.CL);
    typeKeyMap.put("备件", CwKrbRow.BJ);
    // ... 其他类型映射
    return typeKeyMap;
}
```

**批量转换：**
```java
private List<CwCostTypePoint> convertToCostTypePoints(List<CwKrbRow> rows, Map<String, String> typeKeyMap) {
    // 批量转换，避免重复调用 getTypeKeyFromName
}
```

### 4. 缓存机制
**缓存策略：**
```java
@Cacheable(cacheNames = "monthlyDetail", key = "#unitKey + '_' + #period", 
           condition = "#period != T(cn.hutool.core.date.DateUtil).format(new java.util.Date(), 'yyyy-MM')")
public MonthlyDetail getMonthlyDetailWithCache(String unitKey, String period) {
    // 对历史月份数据添加缓存，当前月份不缓存
}
```

**缓存配置：**
- 历史月份数据：缓存24小时
- 当前月份数据：不缓存或缓存5分钟
- 使用 Redis 作为缓存存储

## 优化后的代码结构

### 1. 主要方法重构
```java
@Override
public List<CwCostTrendByTypeResult> costTrendByUnitWithTypes(Date date, String unit) {
    // 参数验证
    validateParameters(date, unit);
    
    // 使用优化后的批量查询方法
    return costTrendByUnitWithTypesOptimized(unit.toLowerCase(), 
                                           DateUtil.beginOfYear(date), 
                                           date, 
                                           DateUtil.month(date) + 1);
}

private List<CwCostTrendByTypeResult> costTrendByUnitWithTypesOptimized(
        String unitKey, Date firstDayOfYear, Date endDate, int monthCount) {
    // 1. 批量获取所有月份的数据
    BatchMonthlyDetail batchData = fetchMonthlyDetailBatch(unitKey, firstDayOfYear, endDate);
    
    // 2. 预构建类型映射表
    Map<String, String> typeKeyMap = buildTypeKeyMap();
    
    // 3. 批量转换数据
    return convertToResults(batchData, typeKeyMap, firstDayOfYear, monthCount);
}
```

### 2. 批量数据容器
```java
private static class BatchMonthlyDetail {
    private final Map<String, MonthlyDetail> monthlyDataMap;
    
    BatchMonthlyDetail(Map<String, MonthlyDetail> monthlyDataMap) {
        this.monthlyDataMap = monthlyDataMap;
    }
    
    MonthlyDetail getMonthlyDetail(String period) {
        return monthlyDataMap.get(period);
    }
}
```

## 预期性能提升

### 1. 数据库查询优化
- **查询次数：** 从 O(n) 降低到 O(1)，其中 n 是月份数量
- **具体提升：** 12个月查询从24次减少到2次，减少92%的数据库访问
- **响应时间：** 数据库查询时间减少80-90%

### 2. 内存使用优化
- **对象创建：** 减少重复对象创建，降低GC压力
- **数据复用：** 提高数据复用率，减少内存占用
- **批量处理：** 提高CPU缓存命中率

### 3. 缓存效果
- **重复查询：** 历史数据查询响应时间提升95%以上
- **服务器负载：** 减少数据库压力，提高并发处理能力
- **用户体验：** 页面加载时间显著减少

### 4. 整体性能提升
- **响应时间：** 预计提升70-85%
- **并发能力：** 提升50-70%
- **资源使用：** 数据库连接使用减少90%以上

## 实施建议

### 1. 分阶段实施
1. **第一阶段：** 实现批量查询优化（核心优化）
2. **第二阶段：** 添加缓存机制
3. **第三阶段：** 数据转换优化和性能调优

### 2. 测试验证
- 对比优化前后的性能指标
- 进行压力测试验证并发性能
- 监控数据库连接池使用情况

### 3. 监控和调优
- 添加性能监控指标
- 定期分析缓存命中率
- 根据实际使用情况调整缓存策略

## 风险评估

### 1. 内存使用
- **风险：** 批量查询可能增加内存使用
- **缓解：** 合理控制查询范围，添加内存监控

### 2. 数据一致性
- **风险：** 缓存可能导致数据不一致
- **缓解：** 合理设置缓存过期时间，当前月份数据不缓存

### 3. 代码复杂度
- **风险：** 优化后代码复杂度增加
- **缓解：** 充分的单元测试和文档说明

## 总结

通过实施上述优化方案，`costTrendByUnitWithTypes` 函数的性能将得到显著提升，特别是在数据库查询效率和响应时间方面。建议优先实施批量查询优化，这是最核心的性能瓶颈，能够带来最大的性能提升。
