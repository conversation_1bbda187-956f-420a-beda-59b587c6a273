# 采矿场综合成本表计算逻辑文档

## 一、表格行结构

### 1. 处理量预测行
- **数据来源**：`CwCllycData`表中的`cllyc`字段
- **显示逻辑**：从当月第一天获取处理量预测数据

### 2. 采剥总量行
- **数据来源**：`CwDwBase`表中的`cbzl`字段
- **显示逻辑**：显示当天的采剥总量数据

### 3. 其他成本项行
- **数据来源**：`CwNameDict`表中定义的各成本项

## 二、表格列计算逻辑

### 1. 月预测列
- **数据来源**：用户填写（16 号以后可填写，下半月与月末各填写一次）
- **填写规则**：
  1. 每月 16-月末可填写一次"下半月月预测"。
  2. 月末（当月最后一天）可再次填写"月末月预测"，覆盖下半月预测。
- **显示状态**：根据日期显示"下半月"或"月末"标签

 -### 2. 当日数列
 - **数据来源**：完全由系统计算生成，不存储在数据库中
 - **三种计算口径（优先级）**：
   1. **月预测计算（最高优先）**：当月任意项目存在月预测（`secondHalfForecast`）时，整月按“月预测”口径计算；若某项目未填写月预测，则该项目当月的预测视为 0。
   2. **月预算计算（其次）**：若当月不存在任何月预测但存在任意项目的月预算（`yys`），则整月按“月预算”口径计算；若某项目未填写月预算，则该项目当月的预算视为 0。
   3. **平均单价/平均总耗计算（回退）**：若当月既无月预测也无月预算（或在月预测/预算口径下因处理量预测缺失无法计算），则回退使用“平均单价 × 平均总耗”计算当日数。

 - **代码实现要点**：
   - 在各厂的 `calculateDrs(...)` 中（见下列文件），实现了上述优先级判断：先判断当月是否存在任意项目的月预测，再判断是否存在任意项目的月预算，最后回退到平均单价口径。
   - 在月预测或月预算口径下，如果该项目的月预测/预算为空，代码会把该项目的 `yyc` 赋为 `BigDecimal.ZERO`，仍然按该口径计算（即用 0 参与单位成本计算）。
   - 月预测/预算口径计算的前提是当月处理量预测 `cllyc` 存在且 > 0，且当日对应的处理量（如采剥总量或厂处理量）存在；否则会回退到平均单价计算。

 - **对应代码位置**：
   - `jeecg-boot/jeecg-module-cw/src/main/java/org/jeecg/modules/cw/jw/service/impl/CwJwZhcbServiceImpl.java` - `calculateDrs`
   - `jeecg-boot/jeecg-module-cw/src/main/java/org/jeecg/modules/cw/ds/service/impl/CwDsZhcbServiceImpl.java` - `calculateDrs`
   - `jeecg-boot/jeecg-module-cw/src/main/java/org/jeecg/modules/cw/ckc/service/impl/CwCkcZhcbServiceImpl.java` - `calculateDrs`
   - `jeecg-boot/jeecg-module-cw/src/main/java/org/jeecg/modules/cw/sx/service/impl/CwSxZhcbServiceImpl.java` - `calculateDrs`

 - **结论**：当前代码已实现你描述的三种算法及优先级（月预测 > 月预算 > 平均单价/平均总耗），并在存在月预测/预算的口径下将缺失的项目级预测/预算视为 0 参与计算；当 cllyc 或当日处理量缺失时回退为平均单价口径。

### 3. 月累计列
- **计算公式**：月累计 = 本月内当日之前所有天的当日数之和（不包含当日）
- **数据来源**：所有当日数全部通过计算得到，不存储在数据库中
- **特殊情况**：
  - 即使当天采剥量为空导致当日数为空，月累计仍然会显示之前有效数据的累计值
  - 如果月内某一天的采剥量为空导致当日数为空，这一天会被跳过，不会影响其他天数据的累计
  - 例如：1、2、4、5号每天的当日数都为1，3号为空，则1、2、3、4、5号的月累计分别为0、1、2、2、3
- **计算逻辑**：
  ```java
  月累计 = 0
  
  // 获取本月第一天到查询日期前一天的所有日期
  List<Date> allDatesInMonth = 获取本月所有日期列表()
  
  // 为每个日期计算当日数并累计
  for (每一天的日期 : allDatesInMonth) {
    if (日期 < 当前查询日期) {
      // 获取该日期对应的处理量预测和采剥总量
      
      // 计算当日数
      计算得到的当日数 = calculateDrs(...)
      
      // 如果当日数不为空，则累计
      if (计算得到的当日数 != null) {
        月累计 += 计算得到的当日数
      }
      // 如果当日数为空，则跳过该天，不影响累计结果
    }
  }
  ```

### 4. 平均单价列
- **计算公式**：平均单价 = 月累计 ÷ 采剥总量月累计
- **计算逻辑**：
  ```java
  if (采剥总量月累计 != null && 采剥总量月累计 > 0 && 月累计 != null) {
    平均单价 = 月累计 / 采剥总量月累计
  }
  ```

### 5. 平均单耗列
- **计算公式**：平均单耗 = 月累计 ÷ 处理量月累计
- **计算逻辑**：
  ```java
  if (处理量月累计 != null && 处理量月累计 > 0 && 月累计 != null) {
    平均单耗 = 月累计 / 处理量月累计
  }
  ```

### 6. 平均总耗列
- **计算公式**：平均总耗 = 月累计 ÷ 当月天数
- **计算逻辑**：
  ```java
  if (月累计 != null && 当月天数 > 0) {
    平均总耗 = 月累计 / 当月天数
  }
  ```

### 7. 月预算列（新增计算逻辑）
- **数据来源**：用户填写（1-15 号期间填写，一月仅一次）
- **计算作用**：上半月 (1-15 号) 相关计算全部使用月预算，而非月预测
- **显示状态**：当日期在 1-15 号时可编辑月预算，其余时间只读

- **变更触发**：提交时若月预算 `yys` 值发生变化（新增/修改/删除），系统会将其视为需要触发整月重算的变更之一（与处理量预测 `cllyc`、月预测 `yyc` 一并判断），从而对当月所有日的 `drs` 进行重算并同步更新模拟利润统计。

### 8. 节超比列
- **计算公式**：节超比 = (月预算 - 月累计) ÷ 月预算 × 100%
- **计算逻辑**：
  ```java
  if (月预算 != null && 月预算 != 0 && 月累计 != null) {
    节超比 = (月预算 - 月累计) / 月预算 * 100%
    // 正数表示节约，负数表示超支
  }
  ```

## 三、月预测填写状态管理

1. **判断逻辑**：
   ```java
   int currentDay = DateUtil.dayOfMonth(queryDate);
   boolean isFirstHalf = currentDay <= 15;
   ```

2. **显示标签**：
   - 当日期在1-15号时，显示"上半月"
   - 当日期在16-31号时，显示"下半月"

3. **填写状态记录**：
   - 使用API请求记录月预测填写状态
   - 月预算：每月仅能填写一次（1-15 号期间）。
   - 月预测：每月可填写两次（16-月末一次，月末最后一天再次填写覆盖）。 