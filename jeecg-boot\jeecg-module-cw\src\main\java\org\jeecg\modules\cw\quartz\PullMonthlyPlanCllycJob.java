package org.jeecg.modules.cw.quartz;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.jeecg.modules.cw.base.entity.CwCllycData;
import org.jeecg.modules.cw.base.service.ICwCllycDataService;
import org.jeecg.modules.cw.ckc.service.ICwCkcZhcbService;
import org.jeecg.modules.cw.ds.service.ICwDsZhcbService;
import org.jeecg.modules.cw.jw.service.ICwJwZhcbService;
import org.jeecg.modules.cw.sx.service.ICwSxZhcbService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 拉取“月计划-预测处理量”数据并入库到 cw_cllyc_data
 * 数据来源字段：
 * - cbzl    -> 采矿场(ckc) 预测处理量
 * - szykcll -> 泗选厂(sx)   预测处理量
 * - dsykcll -> 大山厂(ds)   预测处理量
 * 单位：接口已按需求返回（默认按万吨处理），按月初日期归档。
 */
@Log4j2
@Service
public class PullMonthlyPlanCllycJob implements Job {

    @Resource
    private ICwCllycDataService cwCllycDataService;
    @Resource
    private ICwCkcZhcbService ckcZhcbService;
    @Resource
    private ICwSxZhcbService sxZhcbService;
    @Resource
    private ICwDsZhcbService dsZhcbService;
    @Resource
    private ICwJwZhcbService jwZhcbService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        try {
            // 1) 以当前月份为默认查询参数，格式 yyyy-MM；支持从 Job parameter 传入覆盖
            String monthParam = DateUtil.format(new Date(), "yyyy-MM");
            Object paramStr = context.getJobDetail().getJobDataMap().get("parameter");
            if (paramStr instanceof String) {
                try {
                    JSONObject p = JSONObject.parseObject((String) paramStr);
                    String jhyf = p.getString("jhyf");
                    if (ObjectUtil.isNotEmpty(jhyf)) {
                        monthParam = jhyf;
                    } else {
                        String month = p.getString("month");
                        if (ObjectUtil.isNotEmpty(month)) {
                            monthParam = month;
                        }
                    }
                } catch (Exception ignore) {
                }
            }
            String url = "http://172.18.136.24:8102/GetXXZX/getTechnicalPlannedValue?jhyf=" + monthParam;

            String resp = HttpUtil.get(url);
            if (ObjectUtil.isEmpty(resp)) {
                log.error("[cllyc] 月计划接口无响应: {}", url);
                return;
            }

            JSONObject root = JSONObject.parseObject(resp);
            boolean ok = root.getBooleanValue("Code");
            if (!ok) {
                log.error("[cllyc] 月计划接口返回失败: {}", resp);
                return;
            }

            JSONObject data = root.getJSONObject("Data");
            if (ObjectUtil.isEmpty(data)) {
                log.warn("[cllyc] 月计划接口Data为空: {}", resp);
                return;
            }

            // 2) 解析月份，按月初记账
            String jhyf = data.getString("jhyf");
            Date recordTime = DateUtil.beginOfMonth(ObjectUtil.isNotEmpty(jhyf)
                    ? DateUtil.parse(jhyf, "yyyy-MM")
                    : new Date());

            // 3) 解析三类预测处理量
            BigDecimal ckcYc = parseDecimal(data.getString("cbzl"));     // 采矿场
            BigDecimal sxYc = parseDecimal(data.getString("szykcll"));   // 泗选厂
            BigDecimal dsYc = parseDecimal(data.getString("dsykcll"));   // 大山厂

            // 4) 入库（保存或更新）
            upsertCllyc("ckc", ckcYc, recordTime);
            upsertCllyc("sx", sxYc, recordTime);
            upsertCllyc("ds", dsYc, recordTime);

            log.info("[cllyc] 月计划预测处理量入库完成：月份={}, ckc={}, sx={}, ds={}",
                    DateUtil.format(recordTime, "yyyy-MM"), valOf(ckcYc), valOf(sxYc), valOf(dsYc));

            // 5) 重算当日数
            DateTime beginOfMonth = DateUtil.beginOfMonth(recordTime);
            DateTime endOfMonth = DateUtil.endOfMonth(recordTime);
            ckcZhcbService.recalculateDrs(beginOfMonth, endOfMonth);
            sxZhcbService.recalculateDrs(beginOfMonth, endOfMonth);
            dsZhcbService.recalculateDrs(beginOfMonth, endOfMonth);
            jwZhcbService.recalculateDrs(beginOfMonth, endOfMonth);

        } catch (Exception e) {
            log.error("[cllyc] 拉取月计划预测处理量失败", e);
        }
    }

    private void upsertCllyc(String name, BigDecimal value, Date recordTime) {
        if (ObjectUtil.isEmpty(value)) {
            return;
        }
        CwCllycData entity = cwCllycDataService.lambdaQuery()
                .eq(CwCllycData::getRecordTime, recordTime)
                .eq(CwCllycData::getName, name)
                .one();
        if (entity == null) {
            entity = new CwCllycData();
            entity.setRecordTime(recordTime);
            entity.setName(name);
        }
        entity.setCllyc(value);
        if (entity.getId() == null) {
            cwCllycDataService.save(entity);
        } else {
            cwCllycDataService.updateById(entity);
        }
    }

    private static BigDecimal parseDecimal(String s) {
        try {
            if (ObjectUtil.isEmpty(s)) {
                return null;
            }
            return new BigDecimal(s.trim());
        } catch (Exception e) {
            return null;
        }
    }

    private static String valOf(BigDecimal v) {
        return v == null ? "-" : v.stripTrailingZeros().toPlainString();
    }
}


