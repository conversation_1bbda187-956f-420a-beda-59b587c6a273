{"version": 3, "sources": ["../../.pnpm/event-source-polyfill@1.0.31/node_modules/event-source-polyfill/src/eventsource.js"], "sourcesContent": ["/** @license\r\n * eventsource.js\r\n * Available under MIT License (MIT)\r\n * https://github.com/Yaffle/EventSource/\r\n */\r\n\r\n/*jslint indent: 2, vars: true, plusplus: true */\r\n/*global setTimeout, clearTimeout */\r\n\r\n(function (global) {\r\n  \"use strict\";\r\n\r\n  var setTimeout = global.setTimeout;\r\n  var clearTimeout = global.clearTimeout;\r\n  var XMLHttpRequest = global.XMLHttpRequest;\r\n  var XDomainRequest = global.XDomainRequest;\r\n  var ActiveXObject = global.ActiveXObject;\r\n  var NativeEventSource = global.EventSource;\r\n\r\n  var document = global.document;\r\n  var Promise = global.Promise;\r\n  var fetch = global.fetch;\r\n  var Response = global.Response;\r\n  var TextDecoder = global.TextDecoder;\r\n  var TextEncoder = global.TextEncoder;\r\n  var AbortController = global.AbortController;\r\n\r\n  if (typeof window !== \"undefined\" && typeof document !== \"undefined\" && !(\"readyState\" in document) && document.body == null) { // Firefox 2\r\n    document.readyState = \"loading\";\r\n    window.addEventListener(\"load\", function (event) {\r\n      document.readyState = \"complete\";\r\n    }, false);\r\n  }\r\n\r\n  if (XMLHttpRequest == null && ActiveXObject != null) { // https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/Using_XMLHttpRequest_in_IE6\r\n    XMLHttpRequest = function () {\r\n      return new ActiveXObject(\"Microsoft.XMLHTTP\");\r\n    };\r\n  }\r\n\r\n  if (Object.create == undefined) {\r\n    Object.create = function (C) {\r\n      function F(){}\r\n      F.prototype = C;\r\n      return new F();\r\n    };\r\n  }\r\n\r\n  if (!Date.now) {\r\n    Date.now = function now() {\r\n      return new Date().getTime();\r\n    };\r\n  }\r\n\r\n  // see #118 (Promise#finally with polyfilled Promise)\r\n  // see #123 (data URLs crash Edge)\r\n  // see #125 (CSP violations)\r\n  // see pull/#138\r\n  // => No way to polyfill Promise#finally\r\n\r\n  if (AbortController == undefined) {\r\n    var originalFetch2 = fetch;\r\n    fetch = function (url, options) {\r\n      var signal = options.signal;\r\n      return originalFetch2(url, {headers: options.headers, credentials: options.credentials, cache: options.cache}).then(function (response) {\r\n        var reader = response.body.getReader();\r\n        signal._reader = reader;\r\n        if (signal._aborted) {\r\n          signal._reader.cancel();\r\n        }\r\n        return {\r\n          status: response.status,\r\n          statusText: response.statusText,\r\n          headers: response.headers,\r\n          body: {\r\n            getReader: function () {\r\n              return reader;\r\n            }\r\n          }\r\n        };\r\n      });\r\n    };\r\n    AbortController = function () {\r\n      this.signal = {\r\n        _reader: null,\r\n        _aborted: false\r\n      };\r\n      this.abort = function () {\r\n        if (this.signal._reader != null) {\r\n          this.signal._reader.cancel();\r\n        }\r\n        this.signal._aborted = true;\r\n      };\r\n    };\r\n  }\r\n\r\n  function TextDecoderPolyfill() {\r\n    this.bitsNeeded = 0;\r\n    this.codePoint = 0;\r\n  }\r\n\r\n  TextDecoderPolyfill.prototype.decode = function (octets) {\r\n    function valid(codePoint, shift, octetsCount) {\r\n      if (octetsCount === 1) {\r\n        return codePoint >= 0x0080 >> shift && codePoint << shift <= 0x07FF;\r\n      }\r\n      if (octetsCount === 2) {\r\n        return codePoint >= 0x0800 >> shift && codePoint << shift <= 0xD7FF || codePoint >= 0xE000 >> shift && codePoint << shift <= 0xFFFF;\r\n      }\r\n      if (octetsCount === 3) {\r\n        return codePoint >= 0x010000 >> shift && codePoint << shift <= 0x10FFFF;\r\n      }\r\n      throw new Error();\r\n    }\r\n    function octetsCount(bitsNeeded, codePoint) {\r\n      if (bitsNeeded === 6 * 1) {\r\n        return codePoint >> 6 > 15 ? 3 : codePoint > 31 ? 2 : 1;\r\n      }\r\n      if (bitsNeeded === 6 * 2) {\r\n        return codePoint > 15 ? 3 : 2;\r\n      }\r\n      if (bitsNeeded === 6 * 3) {\r\n        return 3;\r\n      }\r\n      throw new Error();\r\n    }\r\n    var REPLACER = 0xFFFD;\r\n    var string = \"\";\r\n    var bitsNeeded = this.bitsNeeded;\r\n    var codePoint = this.codePoint;\r\n    for (var i = 0; i < octets.length; i += 1) {\r\n      var octet = octets[i];\r\n      if (bitsNeeded !== 0) {\r\n        if (octet < 128 || octet > 191 || !valid(codePoint << 6 | octet & 63, bitsNeeded - 6, octetsCount(bitsNeeded, codePoint))) {\r\n          bitsNeeded = 0;\r\n          codePoint = REPLACER;\r\n          string += String.fromCharCode(codePoint);\r\n        }\r\n      }\r\n      if (bitsNeeded === 0) {\r\n        if (octet >= 0 && octet <= 127) {\r\n          bitsNeeded = 0;\r\n          codePoint = octet;\r\n        } else if (octet >= 192 && octet <= 223) {\r\n          bitsNeeded = 6 * 1;\r\n          codePoint = octet & 31;\r\n        } else if (octet >= 224 && octet <= 239) {\r\n          bitsNeeded = 6 * 2;\r\n          codePoint = octet & 15;\r\n        } else if (octet >= 240 && octet <= 247) {\r\n          bitsNeeded = 6 * 3;\r\n          codePoint = octet & 7;\r\n        } else {\r\n          bitsNeeded = 0;\r\n          codePoint = REPLACER;\r\n        }\r\n        if (bitsNeeded !== 0 && !valid(codePoint, bitsNeeded, octetsCount(bitsNeeded, codePoint))) {\r\n          bitsNeeded = 0;\r\n          codePoint = REPLACER;\r\n        }\r\n      } else {\r\n        bitsNeeded -= 6;\r\n        codePoint = codePoint << 6 | octet & 63;\r\n      }\r\n      if (bitsNeeded === 0) {\r\n        if (codePoint <= 0xFFFF) {\r\n          string += String.fromCharCode(codePoint);\r\n        } else {\r\n          string += String.fromCharCode(0xD800 + (codePoint - 0xFFFF - 1 >> 10));\r\n          string += String.fromCharCode(0xDC00 + (codePoint - 0xFFFF - 1 & 0x3FF));\r\n        }\r\n      }\r\n    }\r\n    this.bitsNeeded = bitsNeeded;\r\n    this.codePoint = codePoint;\r\n    return string;\r\n  };\r\n\r\n  // Firefox < 38 throws an error with stream option\r\n  var supportsStreamOption = function () {\r\n    try {\r\n      return new TextDecoder().decode(new TextEncoder().encode(\"test\"), {stream: true}) === \"test\";\r\n    } catch (error) {\r\n      console.debug(\"TextDecoder does not support streaming option. Using polyfill instead: \" + error);\r\n    }\r\n    return false;\r\n  };\r\n\r\n  // IE, Edge\r\n  if (TextDecoder == undefined || TextEncoder == undefined || !supportsStreamOption()) {\r\n    TextDecoder = TextDecoderPolyfill;\r\n  }\r\n\r\n  var k = function () {\r\n  };\r\n\r\n  function XHRWrapper(xhr) {\r\n    this.withCredentials = false;\r\n    this.readyState = 0;\r\n    this.status = 0;\r\n    this.statusText = \"\";\r\n    this.responseText = \"\";\r\n    this.onprogress = k;\r\n    this.onload = k;\r\n    this.onerror = k;\r\n    this.onreadystatechange = k;\r\n    this._contentType = \"\";\r\n    this._xhr = xhr;\r\n    this._sendTimeout = 0;\r\n    this._abort = k;\r\n  }\r\n\r\n  XHRWrapper.prototype.open = function (method, url) {\r\n    this._abort(true);\r\n\r\n    var that = this;\r\n    var xhr = this._xhr;\r\n    var state = 1;\r\n    var timeout = 0;\r\n\r\n    this._abort = function (silent) {\r\n      if (that._sendTimeout !== 0) {\r\n        clearTimeout(that._sendTimeout);\r\n        that._sendTimeout = 0;\r\n      }\r\n      if (state === 1 || state === 2 || state === 3) {\r\n        state = 4;\r\n        xhr.onload = k;\r\n        xhr.onerror = k;\r\n        xhr.onabort = k;\r\n        xhr.onprogress = k;\r\n        xhr.onreadystatechange = k;\r\n        // IE 8 - 9: XDomainRequest#abort() does not fire any event\r\n        // Opera < 10: XMLHttpRequest#abort() does not fire any event\r\n        xhr.abort();\r\n        if (timeout !== 0) {\r\n          clearTimeout(timeout);\r\n          timeout = 0;\r\n        }\r\n        if (!silent) {\r\n          that.readyState = 4;\r\n          that.onabort(null);\r\n          that.onreadystatechange();\r\n        }\r\n      }\r\n      state = 0;\r\n    };\r\n\r\n    var onStart = function () {\r\n      if (state === 1) {\r\n        //state = 2;\r\n        var status = 0;\r\n        var statusText = \"\";\r\n        var contentType = undefined;\r\n        if (!(\"contentType\" in xhr)) {\r\n          try {\r\n            status = xhr.status;\r\n            statusText = xhr.statusText;\r\n            contentType = xhr.getResponseHeader(\"Content-Type\");\r\n          } catch (error) {\r\n            // IE < 10 throws exception for `xhr.status` when xhr.readyState === 2 || xhr.readyState === 3\r\n            // Opera < 11 throws exception for `xhr.status` when xhr.readyState === 2\r\n            // https://bugs.webkit.org/show_bug.cgi?id=29121\r\n            status = 0;\r\n            statusText = \"\";\r\n            contentType = undefined;\r\n            // Firefox < 14, Chrome ?, Safari ?\r\n            // https://bugs.webkit.org/show_bug.cgi?id=29658\r\n            // https://bugs.webkit.org/show_bug.cgi?id=77854\r\n          }\r\n        } else {\r\n          status = 200;\r\n          statusText = \"OK\";\r\n          contentType = xhr.contentType;\r\n        }\r\n        if (status !== 0) {\r\n          state = 2;\r\n          that.readyState = 2;\r\n          that.status = status;\r\n          that.statusText = statusText;\r\n          that._contentType = contentType;\r\n          that.onreadystatechange();\r\n        }\r\n      }\r\n    };\r\n    var onProgress = function () {\r\n      onStart();\r\n      if (state === 2 || state === 3) {\r\n        state = 3;\r\n        var responseText = \"\";\r\n        try {\r\n          responseText = xhr.responseText;\r\n        } catch (error) {\r\n          // IE 8 - 9 with XMLHttpRequest\r\n        }\r\n        that.readyState = 3;\r\n        that.responseText = responseText;\r\n        that.onprogress();\r\n      }\r\n    };\r\n    var onFinish = function (type, event) {\r\n      if (event == null || event.preventDefault == null) {\r\n        event = {\r\n          preventDefault: k\r\n        };\r\n      }\r\n      // Firefox 52 fires \"readystatechange\" (xhr.readyState === 4) without final \"readystatechange\" (xhr.readyState === 3)\r\n      // IE 8 fires \"onload\" without \"onprogress\"\r\n      onProgress();\r\n      if (state === 1 || state === 2 || state === 3) {\r\n        state = 4;\r\n        if (timeout !== 0) {\r\n          clearTimeout(timeout);\r\n          timeout = 0;\r\n        }\r\n        that.readyState = 4;\r\n        if (type === \"load\") {\r\n          that.onload(event);\r\n        } else if (type === \"error\") {\r\n          that.onerror(event);\r\n        } else if (type === \"abort\") {\r\n          that.onabort(event);\r\n        } else {\r\n          throw new TypeError();\r\n        }\r\n        that.onreadystatechange();\r\n      }\r\n    };\r\n    var onReadyStateChange = function (event) {\r\n      if (xhr != undefined) { // Opera 12\r\n        if (xhr.readyState === 4) {\r\n          if (!(\"onload\" in xhr) || !(\"onerror\" in xhr) || !(\"onabort\" in xhr)) {\r\n            onFinish(xhr.responseText === \"\" ? \"error\" : \"load\", event);\r\n          }\r\n        } else if (xhr.readyState === 3) {\r\n          if (!(\"onprogress\" in xhr)) { // testing XMLHttpRequest#responseText too many times is too slow in IE 11\r\n            // and in Firefox 3.6\r\n            onProgress();\r\n          }\r\n        } else if (xhr.readyState === 2) {\r\n          onStart();\r\n        }\r\n      }\r\n    };\r\n    var onTimeout = function () {\r\n      timeout = setTimeout(function () {\r\n        onTimeout();\r\n      }, 500);\r\n      if (xhr.readyState === 3) {\r\n        onProgress();\r\n      }\r\n    };\r\n\r\n    // XDomainRequest#abort removes onprogress, onerror, onload\r\n    if (\"onload\" in xhr) {\r\n      xhr.onload = function (event) {\r\n        onFinish(\"load\", event);\r\n      };\r\n    }\r\n    if (\"onerror\" in xhr) {\r\n      xhr.onerror = function (event) {\r\n        onFinish(\"error\", event);\r\n      };\r\n    }\r\n    // improper fix to match Firefox behaviour, but it is better than just ignore abort\r\n    // see https://bugzilla.mozilla.org/show_bug.cgi?id=768596\r\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=880200\r\n    // https://code.google.com/p/chromium/issues/detail?id=153570\r\n    // IE 8 fires \"onload\" without \"onprogress\r\n    if (\"onabort\" in xhr) {\r\n      xhr.onabort = function (event) {\r\n        onFinish(\"abort\", event);\r\n      };\r\n    }\r\n\r\n    if (\"onprogress\" in xhr) {\r\n      xhr.onprogress = onProgress;\r\n    }\r\n\r\n    // IE 8 - 9 (XMLHTTPRequest)\r\n    // Opera < 12\r\n    // Firefox < 3.5\r\n    // Firefox 3.5 - 3.6 - ? < 9.0\r\n    // onprogress is not fired sometimes or delayed\r\n    // see also #64 (significant lag in IE 11)\r\n    if (\"onreadystatechange\" in xhr) {\r\n      xhr.onreadystatechange = function (event) {\r\n        onReadyStateChange(event);\r\n      };\r\n    }\r\n\r\n    if (\"contentType\" in xhr || !(\"ontimeout\" in XMLHttpRequest.prototype)) {\r\n      url += (url.indexOf(\"?\") === -1 ? \"?\" : \"&\") + \"padding=true\";\r\n    }\r\n    xhr.open(method, url, true);\r\n\r\n    if (\"readyState\" in xhr) {\r\n      // workaround for Opera 12 issue with \"progress\" events\r\n      // #91 (XMLHttpRequest onprogress not fired for streaming response in Edge 14-15-?)\r\n      timeout = setTimeout(function () {\r\n        onTimeout();\r\n      }, 0);\r\n    }\r\n  };\r\n  XHRWrapper.prototype.abort = function () {\r\n    this._abort(false);\r\n  };\r\n  XHRWrapper.prototype.getResponseHeader = function (name) {\r\n    return this._contentType;\r\n  };\r\n  XHRWrapper.prototype.setRequestHeader = function (name, value) {\r\n    var xhr = this._xhr;\r\n    if (\"setRequestHeader\" in xhr) {\r\n      xhr.setRequestHeader(name, value);\r\n    }\r\n  };\r\n  XHRWrapper.prototype.getAllResponseHeaders = function () {\r\n    // XMLHttpRequest#getAllResponseHeaders returns null for CORS requests in Firefox 3.6.28\r\n    return this._xhr.getAllResponseHeaders != undefined ? this._xhr.getAllResponseHeaders() || \"\" : \"\";\r\n  };\r\n  XHRWrapper.prototype.send = function () {\r\n    // loading indicator in Safari < ? (6), Chrome < 14, Firefox\r\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=736723\r\n    if ((!(\"ontimeout\" in XMLHttpRequest.prototype) || (!(\"sendAsBinary\" in XMLHttpRequest.prototype) && !(\"mozAnon\" in XMLHttpRequest.prototype))) &&\r\n        document != undefined &&\r\n        document.readyState != undefined &&\r\n        document.readyState !== \"complete\") {\r\n      var that = this;\r\n      that._sendTimeout = setTimeout(function () {\r\n        that._sendTimeout = 0;\r\n        that.send();\r\n      }, 4);\r\n      return;\r\n    }\r\n\r\n    var xhr = this._xhr;\r\n    // withCredentials should be set after \"open\" for Safari and Chrome (< 19 ?)\r\n    if (\"withCredentials\" in xhr) {\r\n      xhr.withCredentials = this.withCredentials;\r\n    }\r\n    try {\r\n      // xhr.send(); throws \"Not enough arguments\" in Firefox 3.0\r\n      xhr.send(undefined);\r\n    } catch (error1) {\r\n      // Safari 5.1.7, Opera 12\r\n      throw error1;\r\n    }\r\n  };\r\n\r\n  function toLowerCase(name) {\r\n    return name.replace(/[A-Z]/g, function (c) {\r\n      return String.fromCharCode(c.charCodeAt(0) + 0x20);\r\n    });\r\n  }\r\n\r\n  function HeadersPolyfill(all) {\r\n    // Get headers: implemented according to mozilla's example code: https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/getAllResponseHeaders#Example\r\n    var map = Object.create(null);\r\n    var array = all.split(\"\\r\\n\");\r\n    for (var i = 0; i < array.length; i += 1) {\r\n      var line = array[i];\r\n      var parts = line.split(\": \");\r\n      var name = parts.shift();\r\n      var value = parts.join(\": \");\r\n      map[toLowerCase(name)] = value;\r\n    }\r\n    this._map = map;\r\n  }\r\n  HeadersPolyfill.prototype.get = function (name) {\r\n    return this._map[toLowerCase(name)];\r\n  };\r\n\r\n  if (XMLHttpRequest != null && XMLHttpRequest.HEADERS_RECEIVED == null) { // IE < 9, Firefox 3.6\r\n    XMLHttpRequest.HEADERS_RECEIVED = 2;\r\n  }\r\n\r\n  function XHRTransport() {\r\n  }\r\n\r\n  XHRTransport.prototype.open = function (xhr, onStartCallback, onProgressCallback, onFinishCallback, url, withCredentials, headers) {\r\n    xhr.open(\"GET\", url);\r\n    var offset = 0;\r\n    xhr.onprogress = function () {\r\n      var responseText = xhr.responseText;\r\n      var chunk = responseText.slice(offset);\r\n      offset += chunk.length;\r\n      onProgressCallback(chunk);\r\n    };\r\n    xhr.onerror = function (event) {\r\n      event.preventDefault();\r\n      onFinishCallback(new Error(\"NetworkError\"));\r\n    };\r\n    xhr.onload = function () {\r\n      onFinishCallback(null);\r\n    };\r\n    xhr.onabort = function () {\r\n      onFinishCallback(null);\r\n    };\r\n    xhr.onreadystatechange = function () {\r\n      if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {\r\n        var status = xhr.status;\r\n        var statusText = xhr.statusText;\r\n        var contentType = xhr.getResponseHeader(\"Content-Type\");\r\n        var headers = xhr.getAllResponseHeaders();\r\n        onStartCallback(status, statusText, contentType, new HeadersPolyfill(headers));\r\n      }\r\n    };\r\n    xhr.withCredentials = withCredentials;\r\n    for (var name in headers) {\r\n      if (Object.prototype.hasOwnProperty.call(headers, name)) {\r\n        xhr.setRequestHeader(name, headers[name]);\r\n      }\r\n    }\r\n    xhr.send();\r\n    return xhr;\r\n  };\r\n\r\n  function HeadersWrapper(headers) {\r\n    this._headers = headers;\r\n  }\r\n  HeadersWrapper.prototype.get = function (name) {\r\n    return this._headers.get(name);\r\n  };\r\n\r\n  function FetchTransport() {\r\n  }\r\n\r\n  FetchTransport.prototype.open = function (xhr, onStartCallback, onProgressCallback, onFinishCallback, url, withCredentials, headers) {\r\n    var reader = null;\r\n    var controller = new AbortController();\r\n    var signal = controller.signal;\r\n    var textDecoder = new TextDecoder();\r\n    fetch(url, {\r\n      headers: headers,\r\n      credentials: withCredentials ? \"include\" : \"same-origin\",\r\n      signal: signal,\r\n      cache: \"no-store\"\r\n    }).then(function (response) {\r\n      reader = response.body.getReader();\r\n      onStartCallback(response.status, response.statusText, response.headers.get(\"Content-Type\"), new HeadersWrapper(response.headers));\r\n      // see https://github.com/promises-aplus/promises-spec/issues/179\r\n      return new Promise(function (resolve, reject) {\r\n        var readNextChunk = function () {\r\n          reader.read().then(function (result) {\r\n            if (result.done) {\r\n              //Note: bytes in textDecoder are ignored\r\n              resolve(undefined);\r\n            } else {\r\n              var chunk = textDecoder.decode(result.value, {stream: true});\r\n              onProgressCallback(chunk);\r\n              readNextChunk();\r\n            }\r\n          })[\"catch\"](function (error) {\r\n            reject(error);\r\n          });\r\n        };\r\n        readNextChunk();\r\n      });\r\n    })[\"catch\"](function (error) {\r\n      if (error.name === \"AbortError\") {\r\n        return undefined;\r\n      } else {\r\n        return error;\r\n      }\r\n    }).then(function (error) {\r\n      onFinishCallback(error);\r\n    });\r\n    return {\r\n      abort: function () {\r\n        if (reader != null) {\r\n          reader.cancel(); // https://bugzilla.mozilla.org/show_bug.cgi?id=1583815\r\n        }\r\n        controller.abort();\r\n      }\r\n    };\r\n  };\r\n\r\n  function EventTarget() {\r\n    this._listeners = Object.create(null);\r\n  }\r\n\r\n  function throwError(e) {\r\n    setTimeout(function () {\r\n      throw e;\r\n    }, 0);\r\n  }\r\n\r\n  EventTarget.prototype.dispatchEvent = function (event) {\r\n    event.target = this;\r\n    var typeListeners = this._listeners[event.type];\r\n    if (typeListeners != undefined) {\r\n      var length = typeListeners.length;\r\n      for (var i = 0; i < length; i += 1) {\r\n        var listener = typeListeners[i];\r\n        try {\r\n          if (typeof listener.handleEvent === \"function\") {\r\n            listener.handleEvent(event);\r\n          } else {\r\n            listener.call(this, event);\r\n          }\r\n        } catch (e) {\r\n          throwError(e);\r\n        }\r\n      }\r\n    }\r\n  };\r\n  EventTarget.prototype.addEventListener = function (type, listener) {\r\n    type = String(type);\r\n    var listeners = this._listeners;\r\n    var typeListeners = listeners[type];\r\n    if (typeListeners == undefined) {\r\n      typeListeners = [];\r\n      listeners[type] = typeListeners;\r\n    }\r\n    var found = false;\r\n    for (var i = 0; i < typeListeners.length; i += 1) {\r\n      if (typeListeners[i] === listener) {\r\n        found = true;\r\n      }\r\n    }\r\n    if (!found) {\r\n      typeListeners.push(listener);\r\n    }\r\n  };\r\n  EventTarget.prototype.removeEventListener = function (type, listener) {\r\n    type = String(type);\r\n    var listeners = this._listeners;\r\n    var typeListeners = listeners[type];\r\n    if (typeListeners != undefined) {\r\n      var filtered = [];\r\n      for (var i = 0; i < typeListeners.length; i += 1) {\r\n        if (typeListeners[i] !== listener) {\r\n          filtered.push(typeListeners[i]);\r\n        }\r\n      }\r\n      if (filtered.length === 0) {\r\n        delete listeners[type];\r\n      } else {\r\n        listeners[type] = filtered;\r\n      }\r\n    }\r\n  };\r\n\r\n  function Event(type) {\r\n    this.type = type;\r\n    this.target = undefined;\r\n  }\r\n\r\n  function MessageEvent(type, options) {\r\n    Event.call(this, type);\r\n    this.data = options.data;\r\n    this.lastEventId = options.lastEventId;\r\n  }\r\n\r\n  MessageEvent.prototype = Object.create(Event.prototype);\r\n\r\n  function ConnectionEvent(type, options) {\r\n    Event.call(this, type);\r\n    this.status = options.status;\r\n    this.statusText = options.statusText;\r\n    this.headers = options.headers;\r\n  }\r\n\r\n  ConnectionEvent.prototype = Object.create(Event.prototype);\r\n\r\n  function ErrorEvent(type, options) {\r\n    Event.call(this, type);\r\n    this.error = options.error;\r\n  }\r\n\r\n  ErrorEvent.prototype = Object.create(Event.prototype);\r\n\r\n  var WAITING = -1;\r\n  var CONNECTING = 0;\r\n  var OPEN = 1;\r\n  var CLOSED = 2;\r\n\r\n  var AFTER_CR = -1;\r\n  var FIELD_START = 0;\r\n  var FIELD = 1;\r\n  var VALUE_START = 2;\r\n  var VALUE = 3;\r\n\r\n  var contentTypeRegExp = /^text\\/event\\-stream(;.*)?$/i;\r\n\r\n  var MINIMUM_DURATION = 1000;\r\n  var MAXIMUM_DURATION = 18000000;\r\n\r\n  var parseDuration = function (value, def) {\r\n    var n = value == null ? def : parseInt(value, 10);\r\n    if (n !== n) {\r\n      n = def;\r\n    }\r\n    return clampDuration(n);\r\n  };\r\n  var clampDuration = function (n) {\r\n    return Math.min(Math.max(n, MINIMUM_DURATION), MAXIMUM_DURATION);\r\n  };\r\n\r\n  var fire = function (that, f, event) {\r\n    try {\r\n      if (typeof f === \"function\") {\r\n        f.call(that, event);\r\n      }\r\n    } catch (e) {\r\n      throwError(e);\r\n    }\r\n  };\r\n\r\n  function EventSourcePolyfill(url, options) {\r\n    EventTarget.call(this);\r\n    options = options || {};\r\n\r\n    this.onopen = undefined;\r\n    this.onmessage = undefined;\r\n    this.onerror = undefined;\r\n\r\n    this.url = undefined;\r\n    this.readyState = undefined;\r\n    this.withCredentials = undefined;\r\n    this.headers = undefined;\r\n\r\n    this._close = undefined;\r\n\r\n    start(this, url, options);\r\n  }\r\n\r\n  function getBestXHRTransport() {\r\n    return (XMLHttpRequest != undefined && (\"withCredentials\" in XMLHttpRequest.prototype)) || XDomainRequest == undefined\r\n        ? new XMLHttpRequest()\r\n        : new XDomainRequest();\r\n  }\r\n\r\n  var isFetchSupported = fetch != undefined && Response != undefined && \"body\" in Response.prototype;\r\n\r\n  function start(es, url, options) {\r\n    url = String(url);\r\n    var withCredentials = Boolean(options.withCredentials);\r\n    var lastEventIdQueryParameterName = options.lastEventIdQueryParameterName || \"lastEventId\";\r\n\r\n    var initialRetry = clampDuration(1000);\r\n    var heartbeatTimeout = parseDuration(options.heartbeatTimeout, 45000);\r\n\r\n    var lastEventId = \"\";\r\n    var retry = initialRetry;\r\n    var wasActivity = false;\r\n    var textLength = 0;\r\n    var headers = options.headers || {};\r\n    var TransportOption = options.Transport;\r\n    var xhr = isFetchSupported && TransportOption == undefined ? undefined : new XHRWrapper(TransportOption != undefined ? new TransportOption() : getBestXHRTransport());\r\n    var transport = TransportOption != null && typeof TransportOption !== \"string\" ? new TransportOption() : (xhr == undefined ? new FetchTransport() : new XHRTransport());\r\n    var abortController = undefined;\r\n    var timeout = 0;\r\n    var currentState = WAITING;\r\n    var dataBuffer = \"\";\r\n    var lastEventIdBuffer = \"\";\r\n    var eventTypeBuffer = \"\";\r\n\r\n    var textBuffer = \"\";\r\n    var state = FIELD_START;\r\n    var fieldStart = 0;\r\n    var valueStart = 0;\r\n\r\n    var onStart = function (status, statusText, contentType, headers) {\r\n      if (currentState === CONNECTING) {\r\n        if (status === 200 && contentType != undefined && contentTypeRegExp.test(contentType)) {\r\n          currentState = OPEN;\r\n          wasActivity = Date.now();\r\n          retry = initialRetry;\r\n          es.readyState = OPEN;\r\n          var event = new ConnectionEvent(\"open\", {\r\n            status: status,\r\n            statusText: statusText,\r\n            headers: headers\r\n          });\r\n          es.dispatchEvent(event);\r\n          fire(es, es.onopen, event);\r\n        } else {\r\n          var message = \"\";\r\n          if (status !== 200) {\r\n            if (statusText) {\r\n              statusText = statusText.replace(/\\s+/g, \" \");\r\n            }\r\n            message = \"EventSource's response has a status \" + status + \" \" + statusText + \" that is not 200. Aborting the connection.\";\r\n          } else {\r\n            message = \"EventSource's response has a Content-Type specifying an unsupported type: \" + (contentType == undefined ? \"-\" : contentType.replace(/\\s+/g, \" \")) + \". Aborting the connection.\";\r\n          }\r\n          close();\r\n          var event = new ConnectionEvent(\"error\", {\r\n            status: status,\r\n            statusText: statusText,\r\n            headers: headers\r\n          });\r\n          es.dispatchEvent(event);\r\n          fire(es, es.onerror, event);\r\n          console.error(message);\r\n        }\r\n      }\r\n    };\r\n\r\n    var onProgress = function (textChunk) {\r\n      if (currentState === OPEN) {\r\n        var n = -1;\r\n        for (var i = 0; i < textChunk.length; i += 1) {\r\n          var c = textChunk.charCodeAt(i);\r\n          if (c === \"\\n\".charCodeAt(0) || c === \"\\r\".charCodeAt(0)) {\r\n            n = i;\r\n          }\r\n        }\r\n        var chunk = (n !== -1 ? textBuffer : \"\") + textChunk.slice(0, n + 1);\r\n        textBuffer = (n === -1 ? textBuffer : \"\") + textChunk.slice(n + 1);\r\n        if (textChunk !== \"\") {\r\n          wasActivity = Date.now();\r\n          textLength += textChunk.length;\r\n        }\r\n        for (var position = 0; position < chunk.length; position += 1) {\r\n          var c = chunk.charCodeAt(position);\r\n          if (state === AFTER_CR && c === \"\\n\".charCodeAt(0)) {\r\n            state = FIELD_START;\r\n          } else {\r\n            if (state === AFTER_CR) {\r\n              state = FIELD_START;\r\n            }\r\n            if (c === \"\\r\".charCodeAt(0) || c === \"\\n\".charCodeAt(0)) {\r\n              if (state !== FIELD_START) {\r\n                if (state === FIELD) {\r\n                  valueStart = position + 1;\r\n                }\r\n                var field = chunk.slice(fieldStart, valueStart - 1);\r\n                var value = chunk.slice(valueStart + (valueStart < position && chunk.charCodeAt(valueStart) === \" \".charCodeAt(0) ? 1 : 0), position);\r\n                if (field === \"data\") {\r\n                  dataBuffer += \"\\n\";\r\n                  dataBuffer += value;\r\n                } else if (field === \"id\") {\r\n                  lastEventIdBuffer = value;\r\n                } else if (field === \"event\") {\r\n                  eventTypeBuffer = value;\r\n                } else if (field === \"retry\") {\r\n                  initialRetry = parseDuration(value, initialRetry);\r\n                  retry = initialRetry;\r\n                } else if (field === \"heartbeatTimeout\") {\r\n                  heartbeatTimeout = parseDuration(value, heartbeatTimeout);\r\n                  if (timeout !== 0) {\r\n                    clearTimeout(timeout);\r\n                    timeout = setTimeout(function () {\r\n                      onTimeout();\r\n                    }, heartbeatTimeout);\r\n                  }\r\n                }\r\n              }\r\n              if (state === FIELD_START) {\r\n                if (dataBuffer !== \"\") {\r\n                  lastEventId = lastEventIdBuffer;\r\n                  if (eventTypeBuffer === \"\") {\r\n                    eventTypeBuffer = \"message\";\r\n                  }\r\n                  var event = new MessageEvent(eventTypeBuffer, {\r\n                    data: dataBuffer.slice(1),\r\n                    lastEventId: lastEventIdBuffer\r\n                  });\r\n                  es.dispatchEvent(event);\r\n                  if (eventTypeBuffer === \"open\") {\r\n                    fire(es, es.onopen, event);\r\n                  } else if (eventTypeBuffer === \"message\") {\r\n                    fire(es, es.onmessage, event);\r\n                  } else if (eventTypeBuffer === \"error\") {\r\n                    fire(es, es.onerror, event);\r\n                  }\r\n                  if (currentState === CLOSED) {\r\n                    return;\r\n                  }\r\n                }\r\n                dataBuffer = \"\";\r\n                eventTypeBuffer = \"\";\r\n              }\r\n              state = c === \"\\r\".charCodeAt(0) ? AFTER_CR : FIELD_START;\r\n            } else {\r\n              if (state === FIELD_START) {\r\n                fieldStart = position;\r\n                state = FIELD;\r\n              }\r\n              if (state === FIELD) {\r\n                if (c === \":\".charCodeAt(0)) {\r\n                  valueStart = position + 1;\r\n                  state = VALUE_START;\r\n                }\r\n              } else if (state === VALUE_START) {\r\n                state = VALUE;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    var onFinish = function (error) {\r\n      if (currentState === OPEN || currentState === CONNECTING) {\r\n        currentState = WAITING;\r\n        if (timeout !== 0) {\r\n          clearTimeout(timeout);\r\n          timeout = 0;\r\n        }\r\n        timeout = setTimeout(function () {\r\n          onTimeout();\r\n        }, retry);\r\n        retry = clampDuration(Math.min(initialRetry * 16, retry * 2));\r\n\r\n        es.readyState = CONNECTING;\r\n        var event = new ErrorEvent(\"error\", {error: error});\r\n        es.dispatchEvent(event);\r\n        fire(es, es.onerror, event);\r\n        if (error != undefined) {\r\n          console.error(error);\r\n        }\r\n      }\r\n    };\r\n\r\n    var close = function () {\r\n      currentState = CLOSED;\r\n      if (abortController != undefined) {\r\n        abortController.abort();\r\n        abortController = undefined;\r\n      }\r\n      if (timeout !== 0) {\r\n        clearTimeout(timeout);\r\n        timeout = 0;\r\n      }\r\n      es.readyState = CLOSED;\r\n    };\r\n\r\n    var onTimeout = function () {\r\n      timeout = 0;\r\n\r\n      if (currentState !== WAITING) {\r\n        if (!wasActivity && abortController != undefined) {\r\n          onFinish(new Error(\"No activity within \" + heartbeatTimeout + \" milliseconds.\" + \" \" + (currentState === CONNECTING ? \"No response received.\" : textLength + \" chars received.\") + \" \" + \"Reconnecting.\"));\r\n          if (abortController != undefined) {\r\n            abortController.abort();\r\n            abortController = undefined;\r\n          }\r\n        } else {\r\n          var nextHeartbeat = Math.max((wasActivity || Date.now()) + heartbeatTimeout - Date.now(), 1);\r\n          wasActivity = false;\r\n          timeout = setTimeout(function () {\r\n            onTimeout();\r\n          }, nextHeartbeat);\r\n        }\r\n        return;\r\n      }\r\n\r\n      wasActivity = false;\r\n      textLength = 0;\r\n      timeout = setTimeout(function () {\r\n        onTimeout();\r\n      }, heartbeatTimeout);\r\n\r\n      currentState = CONNECTING;\r\n      dataBuffer = \"\";\r\n      eventTypeBuffer = \"\";\r\n      lastEventIdBuffer = lastEventId;\r\n      textBuffer = \"\";\r\n      fieldStart = 0;\r\n      valueStart = 0;\r\n      state = FIELD_START;\r\n\r\n      // https://bugzilla.mozilla.org/show_bug.cgi?id=428916\r\n      // Request header field Last-Event-ID is not allowed by Access-Control-Allow-Headers.\r\n      var requestURL = url;\r\n      if (url.slice(0, 5) !== \"data:\" && url.slice(0, 5) !== \"blob:\") {\r\n        if (lastEventId !== \"\") {\r\n          // Remove the lastEventId parameter if it's already part of the request URL.\r\n          var i = url.indexOf(\"?\");\r\n          requestURL = i === -1 ? url : url.slice(0, i + 1) + url.slice(i + 1).replace(/(?:^|&)([^=&]*)(?:=[^&]*)?/g, function (p, paramName) {\r\n            return paramName === lastEventIdQueryParameterName ? '' : p;\r\n          });\r\n          // Append the current lastEventId to the request URL.\r\n          requestURL += (url.indexOf(\"?\") === -1 ? \"?\" : \"&\") + lastEventIdQueryParameterName +\"=\" + encodeURIComponent(lastEventId);\r\n        }\r\n      }\r\n      var withCredentials = es.withCredentials;\r\n      var requestHeaders = {};\r\n      requestHeaders[\"Accept\"] = \"text/event-stream\";\r\n      var headers = es.headers;\r\n      if (headers != undefined) {\r\n        for (var name in headers) {\r\n          if (Object.prototype.hasOwnProperty.call(headers, name)) {\r\n            requestHeaders[name] = headers[name];\r\n          }\r\n        }\r\n      }\r\n      try {\r\n        abortController = transport.open(xhr, onStart, onProgress, onFinish, requestURL, withCredentials, requestHeaders);\r\n      } catch (error) {\r\n        close();\r\n        throw error;\r\n      }\r\n    };\r\n\r\n    es.url = url;\r\n    es.readyState = CONNECTING;\r\n    es.withCredentials = withCredentials;\r\n    es.headers = headers;\r\n    es._close = close;\r\n\r\n    onTimeout();\r\n  }\r\n\r\n  EventSourcePolyfill.prototype = Object.create(EventTarget.prototype);\r\n  EventSourcePolyfill.prototype.CONNECTING = CONNECTING;\r\n  EventSourcePolyfill.prototype.OPEN = OPEN;\r\n  EventSourcePolyfill.prototype.CLOSED = CLOSED;\r\n  EventSourcePolyfill.prototype.close = function () {\r\n    this._close();\r\n  };\r\n\r\n  EventSourcePolyfill.CONNECTING = CONNECTING;\r\n  EventSourcePolyfill.OPEN = OPEN;\r\n  EventSourcePolyfill.CLOSED = CLOSED;\r\n  EventSourcePolyfill.prototype.withCredentials = undefined;\r\n\r\n  var R = NativeEventSource\r\n  if (XMLHttpRequest != undefined && (NativeEventSource == undefined || !(\"withCredentials\" in NativeEventSource.prototype))) {\r\n    // Why replace a native EventSource ?\r\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=444328\r\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=831392\r\n    // https://code.google.com/p/chromium/issues/detail?id=260144\r\n    // https://code.google.com/p/chromium/issues/detail?id=225654\r\n    // ...\r\n    R = EventSourcePolyfill;\r\n  }\r\n\r\n  (function (factory) {\r\n    if (typeof module === \"object\" && typeof module.exports === \"object\") {\r\n      var v = factory(exports);\r\n      if (v !== undefined) module.exports = v;\r\n    }\r\n    else if (typeof define === \"function\" && define.amd) {\r\n      define([\"exports\"], factory);\r\n    }\r\n    else {\r\n      factory(global);\r\n    }\r\n  })(function (exports) {\r\n    exports.EventSourcePolyfill = EventSourcePolyfill;\r\n    exports.NativeEventSource = NativeEventSource;\r\n    exports.EventSource = R;\r\n  });\r\n}(typeof globalThis === 'undefined' ? (typeof window !== 'undefined' ? window : typeof self !== 'undefined' ? self : this) : globalThis));\r\n"], "mappings": ";;;;;AAAA;AAAA;AASA,KAAC,SAAU,QAAQ;AACjB;AAEA,UAAI,aAAa,OAAO;AACxB,UAAI,eAAe,OAAO;AAC1B,UAAI,iBAAiB,OAAO;AAC5B,UAAI,iBAAiB,OAAO;AAC5B,UAAI,gBAAgB,OAAO;AAC3B,UAAI,oBAAoB,OAAO;AAE/B,UAAI,WAAW,OAAO;AACtB,UAAIA,WAAU,OAAO;AACrB,UAAI,QAAQ,OAAO;AACnB,UAAI,WAAW,OAAO;AACtB,UAAI,cAAc,OAAO;AACzB,UAAI,cAAc,OAAO;AACzB,UAAI,kBAAkB,OAAO;AAE7B,UAAI,OAAO,WAAW,eAAe,OAAO,aAAa,eAAe,EAAE,gBAAgB,aAAa,SAAS,QAAQ,MAAM;AAC5H,iBAAS,aAAa;AACtB,eAAO,iBAAiB,QAAQ,SAAU,OAAO;AAC/C,mBAAS,aAAa;AAAA,QACxB,GAAG,KAAK;AAAA,MACV;AAEA,UAAI,kBAAkB,QAAQ,iBAAiB,MAAM;AACnD,yBAAiB,WAAY;AAC3B,iBAAO,IAAI,cAAc,mBAAmB;AAAA,QAC9C;AAAA,MACF;AAEA,UAAI,OAAO,UAAU,QAAW;AAC9B,eAAO,SAAS,SAAU,GAAG;AAC3B,mBAAS,IAAG;AAAA,UAAC;AACb,YAAE,YAAY;AACd,iBAAO,IAAI,EAAE;AAAA,QACf;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,KAAK;AACb,aAAK,MAAM,SAAS,MAAM;AACxB,kBAAO,oBAAI,KAAK,GAAE,QAAQ;AAAA,QAC5B;AAAA,MACF;AAQA,UAAI,mBAAmB,QAAW;AAChC,YAAI,iBAAiB;AACrB,gBAAQ,SAAU,KAAK,SAAS;AAC9B,cAAI,SAAS,QAAQ;AACrB,iBAAO,eAAe,KAAK,EAAC,SAAS,QAAQ,SAAS,aAAa,QAAQ,aAAa,OAAO,QAAQ,MAAK,CAAC,EAAE,KAAK,SAAU,UAAU;AACtI,gBAAI,SAAS,SAAS,KAAK,UAAU;AACrC,mBAAO,UAAU;AACjB,gBAAI,OAAO,UAAU;AACnB,qBAAO,QAAQ,OAAO;AAAA,YACxB;AACA,mBAAO;AAAA,cACL,QAAQ,SAAS;AAAA,cACjB,YAAY,SAAS;AAAA,cACrB,SAAS,SAAS;AAAA,cAClB,MAAM;AAAA,gBACJ,WAAW,WAAY;AACrB,yBAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AACA,0BAAkB,WAAY;AAC5B,eAAK,SAAS;AAAA,YACZ,SAAS;AAAA,YACT,UAAU;AAAA,UACZ;AACA,eAAK,QAAQ,WAAY;AACvB,gBAAI,KAAK,OAAO,WAAW,MAAM;AAC/B,mBAAK,OAAO,QAAQ,OAAO;AAAA,YAC7B;AACA,iBAAK,OAAO,WAAW;AAAA,UACzB;AAAA,QACF;AAAA,MACF;AAEA,eAAS,sBAAsB;AAC7B,aAAK,aAAa;AAClB,aAAK,YAAY;AAAA,MACnB;AAEA,0BAAoB,UAAU,SAAS,SAAU,QAAQ;AACvD,iBAAS,MAAMC,YAAW,OAAOC,cAAa;AAC5C,cAAIA,iBAAgB,GAAG;AACrB,mBAAOD,cAAa,OAAU,SAASA,cAAa,SAAS;AAAA,UAC/D;AACA,cAAIC,iBAAgB,GAAG;AACrB,mBAAOD,cAAa,QAAU,SAASA,cAAa,SAAS,SAAUA,cAAa,SAAU,SAASA,cAAa,SAAS;AAAA,UAC/H;AACA,cAAIC,iBAAgB,GAAG;AACrB,mBAAOD,cAAa,SAAY,SAASA,cAAa,SAAS;AAAA,UACjE;AACA,gBAAM,IAAI,MAAM;AAAA,QAClB;AACA,iBAAS,YAAYE,aAAYF,YAAW;AAC1C,cAAIE,gBAAe,IAAI,GAAG;AACxB,mBAAOF,cAAa,IAAI,KAAK,IAAIA,aAAY,KAAK,IAAI;AAAA,UACxD;AACA,cAAIE,gBAAe,IAAI,GAAG;AACxB,mBAAOF,aAAY,KAAK,IAAI;AAAA,UAC9B;AACA,cAAIE,gBAAe,IAAI,GAAG;AACxB,mBAAO;AAAA,UACT;AACA,gBAAM,IAAI,MAAM;AAAA,QAClB;AACA,YAAI,WAAW;AACf,YAAI,SAAS;AACb,YAAI,aAAa,KAAK;AACtB,YAAI,YAAY,KAAK;AACrB,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,cAAI,QAAQ,OAAO,CAAC;AACpB,cAAI,eAAe,GAAG;AACpB,gBAAI,QAAQ,OAAO,QAAQ,OAAO,CAAC,MAAM,aAAa,IAAI,QAAQ,IAAI,aAAa,GAAG,YAAY,YAAY,SAAS,CAAC,GAAG;AACzH,2BAAa;AACb,0BAAY;AACZ,wBAAU,OAAO,aAAa,SAAS;AAAA,YACzC;AAAA,UACF;AACA,cAAI,eAAe,GAAG;AACpB,gBAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,2BAAa;AACb,0BAAY;AAAA,YACd,WAAW,SAAS,OAAO,SAAS,KAAK;AACvC,2BAAa,IAAI;AACjB,0BAAY,QAAQ;AAAA,YACtB,WAAW,SAAS,OAAO,SAAS,KAAK;AACvC,2BAAa,IAAI;AACjB,0BAAY,QAAQ;AAAA,YACtB,WAAW,SAAS,OAAO,SAAS,KAAK;AACvC,2BAAa,IAAI;AACjB,0BAAY,QAAQ;AAAA,YACtB,OAAO;AACL,2BAAa;AACb,0BAAY;AAAA,YACd;AACA,gBAAI,eAAe,KAAK,CAAC,MAAM,WAAW,YAAY,YAAY,YAAY,SAAS,CAAC,GAAG;AACzF,2BAAa;AACb,0BAAY;AAAA,YACd;AAAA,UACF,OAAO;AACL,0BAAc;AACd,wBAAY,aAAa,IAAI,QAAQ;AAAA,UACvC;AACA,cAAI,eAAe,GAAG;AACpB,gBAAI,aAAa,OAAQ;AACvB,wBAAU,OAAO,aAAa,SAAS;AAAA,YACzC,OAAO;AACL,wBAAU,OAAO,aAAa,SAAU,YAAY,QAAS,KAAK,GAAG;AACrE,wBAAU,OAAO,aAAa,SAAU,YAAY,QAAS,IAAI,KAAM;AAAA,YACzE;AAAA,UACF;AAAA,QACF;AACA,aAAK,aAAa;AAClB,aAAK,YAAY;AACjB,eAAO;AAAA,MACT;AAGA,UAAI,uBAAuB,WAAY;AACrC,YAAI;AACF,iBAAO,IAAI,YAAY,EAAE,OAAO,IAAI,YAAY,EAAE,OAAO,MAAM,GAAG,EAAC,QAAQ,KAAI,CAAC,MAAM;AAAA,QACxF,SAAS,OAAO;AACd,kBAAQ,MAAM,4EAA4E,KAAK;AAAA,QACjG;AACA,eAAO;AAAA,MACT;AAGA,UAAI,eAAe,UAAa,eAAe,UAAa,CAAC,qBAAqB,GAAG;AACnF,sBAAc;AAAA,MAChB;AAEA,UAAI,IAAI,WAAY;AAAA,MACpB;AAEA,eAAS,WAAW,KAAK;AACvB,aAAK,kBAAkB;AACvB,aAAK,aAAa;AAClB,aAAK,SAAS;AACd,aAAK,aAAa;AAClB,aAAK,eAAe;AACpB,aAAK,aAAa;AAClB,aAAK,SAAS;AACd,aAAK,UAAU;AACf,aAAK,qBAAqB;AAC1B,aAAK,eAAe;AACpB,aAAK,OAAO;AACZ,aAAK,eAAe;AACpB,aAAK,SAAS;AAAA,MAChB;AAEA,iBAAW,UAAU,OAAO,SAAU,QAAQ,KAAK;AACjD,aAAK,OAAO,IAAI;AAEhB,YAAI,OAAO;AACX,YAAI,MAAM,KAAK;AACf,YAAI,QAAQ;AACZ,YAAI,UAAU;AAEd,aAAK,SAAS,SAAU,QAAQ;AAC9B,cAAI,KAAK,iBAAiB,GAAG;AAC3B,yBAAa,KAAK,YAAY;AAC9B,iBAAK,eAAe;AAAA,UACtB;AACA,cAAI,UAAU,KAAK,UAAU,KAAK,UAAU,GAAG;AAC7C,oBAAQ;AACR,gBAAI,SAAS;AACb,gBAAI,UAAU;AACd,gBAAI,UAAU;AACd,gBAAI,aAAa;AACjB,gBAAI,qBAAqB;AAGzB,gBAAI,MAAM;AACV,gBAAI,YAAY,GAAG;AACjB,2BAAa,OAAO;AACpB,wBAAU;AAAA,YACZ;AACA,gBAAI,CAAC,QAAQ;AACX,mBAAK,aAAa;AAClB,mBAAK,QAAQ,IAAI;AACjB,mBAAK,mBAAmB;AAAA,YAC1B;AAAA,UACF;AACA,kBAAQ;AAAA,QACV;AAEA,YAAI,UAAU,WAAY;AACxB,cAAI,UAAU,GAAG;AAEf,gBAAI,SAAS;AACb,gBAAI,aAAa;AACjB,gBAAI,cAAc;AAClB,gBAAI,EAAE,iBAAiB,MAAM;AAC3B,kBAAI;AACF,yBAAS,IAAI;AACb,6BAAa,IAAI;AACjB,8BAAc,IAAI,kBAAkB,cAAc;AAAA,cACpD,SAAS,OAAO;AAId,yBAAS;AACT,6BAAa;AACb,8BAAc;AAAA,cAIhB;AAAA,YACF,OAAO;AACL,uBAAS;AACT,2BAAa;AACb,4BAAc,IAAI;AAAA,YACpB;AACA,gBAAI,WAAW,GAAG;AAChB,sBAAQ;AACR,mBAAK,aAAa;AAClB,mBAAK,SAAS;AACd,mBAAK,aAAa;AAClB,mBAAK,eAAe;AACpB,mBAAK,mBAAmB;AAAA,YAC1B;AAAA,UACF;AAAA,QACF;AACA,YAAI,aAAa,WAAY;AAC3B,kBAAQ;AACR,cAAI,UAAU,KAAK,UAAU,GAAG;AAC9B,oBAAQ;AACR,gBAAI,eAAe;AACnB,gBAAI;AACF,6BAAe,IAAI;AAAA,YACrB,SAAS,OAAO;AAAA,YAEhB;AACA,iBAAK,aAAa;AAClB,iBAAK,eAAe;AACpB,iBAAK,WAAW;AAAA,UAClB;AAAA,QACF;AACA,YAAI,WAAW,SAAU,MAAM,OAAO;AACpC,cAAI,SAAS,QAAQ,MAAM,kBAAkB,MAAM;AACjD,oBAAQ;AAAA,cACN,gBAAgB;AAAA,YAClB;AAAA,UACF;AAGA,qBAAW;AACX,cAAI,UAAU,KAAK,UAAU,KAAK,UAAU,GAAG;AAC7C,oBAAQ;AACR,gBAAI,YAAY,GAAG;AACjB,2BAAa,OAAO;AACpB,wBAAU;AAAA,YACZ;AACA,iBAAK,aAAa;AAClB,gBAAI,SAAS,QAAQ;AACnB,mBAAK,OAAO,KAAK;AAAA,YACnB,WAAW,SAAS,SAAS;AAC3B,mBAAK,QAAQ,KAAK;AAAA,YACpB,WAAW,SAAS,SAAS;AAC3B,mBAAK,QAAQ,KAAK;AAAA,YACpB,OAAO;AACL,oBAAM,IAAI,UAAU;AAAA,YACtB;AACA,iBAAK,mBAAmB;AAAA,UAC1B;AAAA,QACF;AACA,YAAI,qBAAqB,SAAU,OAAO;AACxC,cAAI,OAAO,QAAW;AACpB,gBAAI,IAAI,eAAe,GAAG;AACxB,kBAAI,EAAE,YAAY,QAAQ,EAAE,aAAa,QAAQ,EAAE,aAAa,MAAM;AACpE,yBAAS,IAAI,iBAAiB,KAAK,UAAU,QAAQ,KAAK;AAAA,cAC5D;AAAA,YACF,WAAW,IAAI,eAAe,GAAG;AAC/B,kBAAI,EAAE,gBAAgB,MAAM;AAE1B,2BAAW;AAAA,cACb;AAAA,YACF,WAAW,IAAI,eAAe,GAAG;AAC/B,sBAAQ;AAAA,YACV;AAAA,UACF;AAAA,QACF;AACA,YAAI,YAAY,WAAY;AAC1B,oBAAU,WAAW,WAAY;AAC/B,sBAAU;AAAA,UACZ,GAAG,GAAG;AACN,cAAI,IAAI,eAAe,GAAG;AACxB,uBAAW;AAAA,UACb;AAAA,QACF;AAGA,YAAI,YAAY,KAAK;AACnB,cAAI,SAAS,SAAU,OAAO;AAC5B,qBAAS,QAAQ,KAAK;AAAA,UACxB;AAAA,QACF;AACA,YAAI,aAAa,KAAK;AACpB,cAAI,UAAU,SAAU,OAAO;AAC7B,qBAAS,SAAS,KAAK;AAAA,UACzB;AAAA,QACF;AAMA,YAAI,aAAa,KAAK;AACpB,cAAI,UAAU,SAAU,OAAO;AAC7B,qBAAS,SAAS,KAAK;AAAA,UACzB;AAAA,QACF;AAEA,YAAI,gBAAgB,KAAK;AACvB,cAAI,aAAa;AAAA,QACnB;AAQA,YAAI,wBAAwB,KAAK;AAC/B,cAAI,qBAAqB,SAAU,OAAO;AACxC,+BAAmB,KAAK;AAAA,UAC1B;AAAA,QACF;AAEA,YAAI,iBAAiB,OAAO,EAAE,eAAe,eAAe,YAAY;AACtE,kBAAQ,IAAI,QAAQ,GAAG,MAAM,KAAK,MAAM,OAAO;AAAA,QACjD;AACA,YAAI,KAAK,QAAQ,KAAK,IAAI;AAE1B,YAAI,gBAAgB,KAAK;AAGvB,oBAAU,WAAW,WAAY;AAC/B,sBAAU;AAAA,UACZ,GAAG,CAAC;AAAA,QACN;AAAA,MACF;AACA,iBAAW,UAAU,QAAQ,WAAY;AACvC,aAAK,OAAO,KAAK;AAAA,MACnB;AACA,iBAAW,UAAU,oBAAoB,SAAU,MAAM;AACvD,eAAO,KAAK;AAAA,MACd;AACA,iBAAW,UAAU,mBAAmB,SAAU,MAAM,OAAO;AAC7D,YAAI,MAAM,KAAK;AACf,YAAI,sBAAsB,KAAK;AAC7B,cAAI,iBAAiB,MAAM,KAAK;AAAA,QAClC;AAAA,MACF;AACA,iBAAW,UAAU,wBAAwB,WAAY;AAEvD,eAAO,KAAK,KAAK,yBAAyB,SAAY,KAAK,KAAK,sBAAsB,KAAK,KAAK;AAAA,MAClG;AACA,iBAAW,UAAU,OAAO,WAAY;AAGtC,aAAK,EAAE,eAAe,eAAe,cAAe,EAAE,kBAAkB,eAAe,cAAc,EAAE,aAAa,eAAe,eAC/H,YAAY,UACZ,SAAS,cAAc,UACvB,SAAS,eAAe,YAAY;AACtC,cAAI,OAAO;AACX,eAAK,eAAe,WAAW,WAAY;AACzC,iBAAK,eAAe;AACpB,iBAAK,KAAK;AAAA,UACZ,GAAG,CAAC;AACJ;AAAA,QACF;AAEA,YAAI,MAAM,KAAK;AAEf,YAAI,qBAAqB,KAAK;AAC5B,cAAI,kBAAkB,KAAK;AAAA,QAC7B;AACA,YAAI;AAEF,cAAI,KAAK,MAAS;AAAA,QACpB,SAAS,QAAQ;AAEf,gBAAM;AAAA,QACR;AAAA,MACF;AAEA,eAAS,YAAY,MAAM;AACzB,eAAO,KAAK,QAAQ,UAAU,SAAU,GAAG;AACzC,iBAAO,OAAO,aAAa,EAAE,WAAW,CAAC,IAAI,EAAI;AAAA,QACnD,CAAC;AAAA,MACH;AAEA,eAAS,gBAAgB,KAAK;AAE5B,YAAI,MAAM,uBAAO,OAAO,IAAI;AAC5B,YAAI,QAAQ,IAAI,MAAM,MAAM;AAC5B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,cAAI,OAAO,MAAM,CAAC;AAClB,cAAI,QAAQ,KAAK,MAAM,IAAI;AAC3B,cAAI,OAAO,MAAM,MAAM;AACvB,cAAI,QAAQ,MAAM,KAAK,IAAI;AAC3B,cAAI,YAAY,IAAI,CAAC,IAAI;AAAA,QAC3B;AACA,aAAK,OAAO;AAAA,MACd;AACA,sBAAgB,UAAU,MAAM,SAAU,MAAM;AAC9C,eAAO,KAAK,KAAK,YAAY,IAAI,CAAC;AAAA,MACpC;AAEA,UAAI,kBAAkB,QAAQ,eAAe,oBAAoB,MAAM;AACrE,uBAAe,mBAAmB;AAAA,MACpC;AAEA,eAAS,eAAe;AAAA,MACxB;AAEA,mBAAa,UAAU,OAAO,SAAU,KAAK,iBAAiB,oBAAoB,kBAAkB,KAAK,iBAAiB,SAAS;AACjI,YAAI,KAAK,OAAO,GAAG;AACnB,YAAI,SAAS;AACb,YAAI,aAAa,WAAY;AAC3B,cAAI,eAAe,IAAI;AACvB,cAAI,QAAQ,aAAa,MAAM,MAAM;AACrC,oBAAU,MAAM;AAChB,6BAAmB,KAAK;AAAA,QAC1B;AACA,YAAI,UAAU,SAAU,OAAO;AAC7B,gBAAM,eAAe;AACrB,2BAAiB,IAAI,MAAM,cAAc,CAAC;AAAA,QAC5C;AACA,YAAI,SAAS,WAAY;AACvB,2BAAiB,IAAI;AAAA,QACvB;AACA,YAAI,UAAU,WAAY;AACxB,2BAAiB,IAAI;AAAA,QACvB;AACA,YAAI,qBAAqB,WAAY;AACnC,cAAI,IAAI,eAAe,eAAe,kBAAkB;AACtD,gBAAI,SAAS,IAAI;AACjB,gBAAI,aAAa,IAAI;AACrB,gBAAI,cAAc,IAAI,kBAAkB,cAAc;AACtD,gBAAIC,WAAU,IAAI,sBAAsB;AACxC,4BAAgB,QAAQ,YAAY,aAAa,IAAI,gBAAgBA,QAAO,CAAC;AAAA,UAC/E;AAAA,QACF;AACA,YAAI,kBAAkB;AACtB,iBAAS,QAAQ,SAAS;AACxB,cAAI,OAAO,UAAU,eAAe,KAAK,SAAS,IAAI,GAAG;AACvD,gBAAI,iBAAiB,MAAM,QAAQ,IAAI,CAAC;AAAA,UAC1C;AAAA,QACF;AACA,YAAI,KAAK;AACT,eAAO;AAAA,MACT;AAEA,eAAS,eAAe,SAAS;AAC/B,aAAK,WAAW;AAAA,MAClB;AACA,qBAAe,UAAU,MAAM,SAAU,MAAM;AAC7C,eAAO,KAAK,SAAS,IAAI,IAAI;AAAA,MAC/B;AAEA,eAAS,iBAAiB;AAAA,MAC1B;AAEA,qBAAe,UAAU,OAAO,SAAU,KAAK,iBAAiB,oBAAoB,kBAAkB,KAAK,iBAAiB,SAAS;AACnI,YAAI,SAAS;AACb,YAAI,aAAa,IAAI,gBAAgB;AACrC,YAAI,SAAS,WAAW;AACxB,YAAI,cAAc,IAAI,YAAY;AAClC,cAAM,KAAK;AAAA,UACT;AAAA,UACA,aAAa,kBAAkB,YAAY;AAAA,UAC3C;AAAA,UACA,OAAO;AAAA,QACT,CAAC,EAAE,KAAK,SAAU,UAAU;AAC1B,mBAAS,SAAS,KAAK,UAAU;AACjC,0BAAgB,SAAS,QAAQ,SAAS,YAAY,SAAS,QAAQ,IAAI,cAAc,GAAG,IAAI,eAAe,SAAS,OAAO,CAAC;AAEhI,iBAAO,IAAIJ,SAAQ,SAAU,SAAS,QAAQ;AAC5C,gBAAI,gBAAgB,WAAY;AAC9B,qBAAO,KAAK,EAAE,KAAK,SAAU,QAAQ;AACnC,oBAAI,OAAO,MAAM;AAEf,0BAAQ,MAAS;AAAA,gBACnB,OAAO;AACL,sBAAI,QAAQ,YAAY,OAAO,OAAO,OAAO,EAAC,QAAQ,KAAI,CAAC;AAC3D,qCAAmB,KAAK;AACxB,gCAAc;AAAA,gBAChB;AAAA,cACF,CAAC,EAAE,OAAO,EAAE,SAAU,OAAO;AAC3B,uBAAO,KAAK;AAAA,cACd,CAAC;AAAA,YACH;AACA,0BAAc;AAAA,UAChB,CAAC;AAAA,QACH,CAAC,EAAE,OAAO,EAAE,SAAU,OAAO;AAC3B,cAAI,MAAM,SAAS,cAAc;AAC/B,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF,CAAC,EAAE,KAAK,SAAU,OAAO;AACvB,2BAAiB,KAAK;AAAA,QACxB,CAAC;AACD,eAAO;AAAA,UACL,OAAO,WAAY;AACjB,gBAAI,UAAU,MAAM;AAClB,qBAAO,OAAO;AAAA,YAChB;AACA,uBAAW,MAAM;AAAA,UACnB;AAAA,QACF;AAAA,MACF;AAEA,eAAS,cAAc;AACrB,aAAK,aAAa,uBAAO,OAAO,IAAI;AAAA,MACtC;AAEA,eAAS,WAAW,GAAG;AACrB,mBAAW,WAAY;AACrB,gBAAM;AAAA,QACR,GAAG,CAAC;AAAA,MACN;AAEA,kBAAY,UAAU,gBAAgB,SAAU,OAAO;AACrD,cAAM,SAAS;AACf,YAAI,gBAAgB,KAAK,WAAW,MAAM,IAAI;AAC9C,YAAI,iBAAiB,QAAW;AAC9B,cAAI,SAAS,cAAc;AAC3B,mBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAClC,gBAAI,WAAW,cAAc,CAAC;AAC9B,gBAAI;AACF,kBAAI,OAAO,SAAS,gBAAgB,YAAY;AAC9C,yBAAS,YAAY,KAAK;AAAA,cAC5B,OAAO;AACL,yBAAS,KAAK,MAAM,KAAK;AAAA,cAC3B;AAAA,YACF,SAAS,GAAG;AACV,yBAAW,CAAC;AAAA,YACd;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,kBAAY,UAAU,mBAAmB,SAAU,MAAM,UAAU;AACjE,eAAO,OAAO,IAAI;AAClB,YAAI,YAAY,KAAK;AACrB,YAAI,gBAAgB,UAAU,IAAI;AAClC,YAAI,iBAAiB,QAAW;AAC9B,0BAAgB,CAAC;AACjB,oBAAU,IAAI,IAAI;AAAA,QACpB;AACA,YAAI,QAAQ;AACZ,iBAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK,GAAG;AAChD,cAAI,cAAc,CAAC,MAAM,UAAU;AACjC,oBAAQ;AAAA,UACV;AAAA,QACF;AACA,YAAI,CAAC,OAAO;AACV,wBAAc,KAAK,QAAQ;AAAA,QAC7B;AAAA,MACF;AACA,kBAAY,UAAU,sBAAsB,SAAU,MAAM,UAAU;AACpE,eAAO,OAAO,IAAI;AAClB,YAAI,YAAY,KAAK;AACrB,YAAI,gBAAgB,UAAU,IAAI;AAClC,YAAI,iBAAiB,QAAW;AAC9B,cAAI,WAAW,CAAC;AAChB,mBAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK,GAAG;AAChD,gBAAI,cAAc,CAAC,MAAM,UAAU;AACjC,uBAAS,KAAK,cAAc,CAAC,CAAC;AAAA,YAChC;AAAA,UACF;AACA,cAAI,SAAS,WAAW,GAAG;AACzB,mBAAO,UAAU,IAAI;AAAA,UACvB,OAAO;AACL,sBAAU,IAAI,IAAI;AAAA,UACpB;AAAA,QACF;AAAA,MACF;AAEA,eAAS,MAAM,MAAM;AACnB,aAAK,OAAO;AACZ,aAAK,SAAS;AAAA,MAChB;AAEA,eAAS,aAAa,MAAM,SAAS;AACnC,cAAM,KAAK,MAAM,IAAI;AACrB,aAAK,OAAO,QAAQ;AACpB,aAAK,cAAc,QAAQ;AAAA,MAC7B;AAEA,mBAAa,YAAY,OAAO,OAAO,MAAM,SAAS;AAEtD,eAAS,gBAAgB,MAAM,SAAS;AACtC,cAAM,KAAK,MAAM,IAAI;AACrB,aAAK,SAAS,QAAQ;AACtB,aAAK,aAAa,QAAQ;AAC1B,aAAK,UAAU,QAAQ;AAAA,MACzB;AAEA,sBAAgB,YAAY,OAAO,OAAO,MAAM,SAAS;AAEzD,eAAS,WAAW,MAAM,SAAS;AACjC,cAAM,KAAK,MAAM,IAAI;AACrB,aAAK,QAAQ,QAAQ;AAAA,MACvB;AAEA,iBAAW,YAAY,OAAO,OAAO,MAAM,SAAS;AAEpD,UAAI,UAAU;AACd,UAAI,aAAa;AACjB,UAAI,OAAO;AACX,UAAI,SAAS;AAEb,UAAI,WAAW;AACf,UAAI,cAAc;AAClB,UAAI,QAAQ;AACZ,UAAI,cAAc;AAClB,UAAI,QAAQ;AAEZ,UAAI,oBAAoB;AAExB,UAAI,mBAAmB;AACvB,UAAI,mBAAmB;AAEvB,UAAI,gBAAgB,SAAU,OAAO,KAAK;AACxC,YAAI,IAAI,SAAS,OAAO,MAAM,SAAS,OAAO,EAAE;AAChD,YAAI,MAAM,GAAG;AACX,cAAI;AAAA,QACN;AACA,eAAO,cAAc,CAAC;AAAA,MACxB;AACA,UAAI,gBAAgB,SAAU,GAAG;AAC/B,eAAO,KAAK,IAAI,KAAK,IAAI,GAAG,gBAAgB,GAAG,gBAAgB;AAAA,MACjE;AAEA,UAAI,OAAO,SAAU,MAAM,GAAG,OAAO;AACnC,YAAI;AACF,cAAI,OAAO,MAAM,YAAY;AAC3B,cAAE,KAAK,MAAM,KAAK;AAAA,UACpB;AAAA,QACF,SAAS,GAAG;AACV,qBAAW,CAAC;AAAA,QACd;AAAA,MACF;AAEA,eAAS,oBAAoB,KAAK,SAAS;AACzC,oBAAY,KAAK,IAAI;AACrB,kBAAU,WAAW,CAAC;AAEtB,aAAK,SAAS;AACd,aAAK,YAAY;AACjB,aAAK,UAAU;AAEf,aAAK,MAAM;AACX,aAAK,aAAa;AAClB,aAAK,kBAAkB;AACvB,aAAK,UAAU;AAEf,aAAK,SAAS;AAEd,cAAM,MAAM,KAAK,OAAO;AAAA,MAC1B;AAEA,eAAS,sBAAsB;AAC7B,eAAQ,kBAAkB,UAAc,qBAAqB,eAAe,aAAe,kBAAkB,SACvG,IAAI,eAAe,IACnB,IAAI,eAAe;AAAA,MAC3B;AAEA,UAAI,mBAAmB,SAAS,UAAa,YAAY,UAAa,UAAU,SAAS;AAEzF,eAAS,MAAM,IAAI,KAAK,SAAS;AAC/B,cAAM,OAAO,GAAG;AAChB,YAAI,kBAAkB,QAAQ,QAAQ,eAAe;AACrD,YAAI,gCAAgC,QAAQ,iCAAiC;AAE7E,YAAI,eAAe,cAAc,GAAI;AACrC,YAAI,mBAAmB,cAAc,QAAQ,kBAAkB,IAAK;AAEpE,YAAI,cAAc;AAClB,YAAI,QAAQ;AACZ,YAAI,cAAc;AAClB,YAAI,aAAa;AACjB,YAAI,UAAU,QAAQ,WAAW,CAAC;AAClC,YAAI,kBAAkB,QAAQ;AAC9B,YAAI,MAAM,oBAAoB,mBAAmB,SAAY,SAAY,IAAI,WAAW,mBAAmB,SAAY,IAAI,gBAAgB,IAAI,oBAAoB,CAAC;AACpK,YAAI,YAAY,mBAAmB,QAAQ,OAAO,oBAAoB,WAAW,IAAI,gBAAgB,IAAK,OAAO,SAAY,IAAI,eAAe,IAAI,IAAI,aAAa;AACrK,YAAI,kBAAkB;AACtB,YAAI,UAAU;AACd,YAAI,eAAe;AACnB,YAAI,aAAa;AACjB,YAAI,oBAAoB;AACxB,YAAI,kBAAkB;AAEtB,YAAI,aAAa;AACjB,YAAI,QAAQ;AACZ,YAAI,aAAa;AACjB,YAAI,aAAa;AAEjB,YAAI,UAAU,SAAU,QAAQ,YAAY,aAAaI,UAAS;AAChE,cAAI,iBAAiB,YAAY;AAC/B,gBAAI,WAAW,OAAO,eAAe,UAAa,kBAAkB,KAAK,WAAW,GAAG;AACrF,6BAAe;AACf,4BAAc,KAAK,IAAI;AACvB,sBAAQ;AACR,iBAAG,aAAa;AAChB,kBAAI,QAAQ,IAAI,gBAAgB,QAAQ;AAAA,gBACtC;AAAA,gBACA;AAAA,gBACA,SAASA;AAAA,cACX,CAAC;AACD,iBAAG,cAAc,KAAK;AACtB,mBAAK,IAAI,GAAG,QAAQ,KAAK;AAAA,YAC3B,OAAO;AACL,kBAAI,UAAU;AACd,kBAAI,WAAW,KAAK;AAClB,oBAAI,YAAY;AACd,+BAAa,WAAW,QAAQ,QAAQ,GAAG;AAAA,gBAC7C;AACA,0BAAU,yCAAyC,SAAS,MAAM,aAAa;AAAA,cACjF,OAAO;AACL,0BAAU,gFAAgF,eAAe,SAAY,MAAM,YAAY,QAAQ,QAAQ,GAAG,KAAK;AAAA,cACjK;AACA,oBAAM;AACN,kBAAI,QAAQ,IAAI,gBAAgB,SAAS;AAAA,gBACvC;AAAA,gBACA;AAAA,gBACA,SAASA;AAAA,cACX,CAAC;AACD,iBAAG,cAAc,KAAK;AACtB,mBAAK,IAAI,GAAG,SAAS,KAAK;AAC1B,sBAAQ,MAAM,OAAO;AAAA,YACvB;AAAA,UACF;AAAA,QACF;AAEA,YAAI,aAAa,SAAU,WAAW;AACpC,cAAI,iBAAiB,MAAM;AACzB,gBAAI,IAAI;AACR,qBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK,GAAG;AAC5C,kBAAI,IAAI,UAAU,WAAW,CAAC;AAC9B,kBAAI,MAAM,KAAK,WAAW,CAAC,KAAK,MAAM,KAAK,WAAW,CAAC,GAAG;AACxD,oBAAI;AAAA,cACN;AAAA,YACF;AACA,gBAAI,SAAS,MAAM,KAAK,aAAa,MAAM,UAAU,MAAM,GAAG,IAAI,CAAC;AACnE,0BAAc,MAAM,KAAK,aAAa,MAAM,UAAU,MAAM,IAAI,CAAC;AACjE,gBAAI,cAAc,IAAI;AACpB,4BAAc,KAAK,IAAI;AACvB,4BAAc,UAAU;AAAA,YAC1B;AACA,qBAAS,WAAW,GAAG,WAAW,MAAM,QAAQ,YAAY,GAAG;AAC7D,kBAAI,IAAI,MAAM,WAAW,QAAQ;AACjC,kBAAI,UAAU,YAAY,MAAM,KAAK,WAAW,CAAC,GAAG;AAClD,wBAAQ;AAAA,cACV,OAAO;AACL,oBAAI,UAAU,UAAU;AACtB,0BAAQ;AAAA,gBACV;AACA,oBAAI,MAAM,KAAK,WAAW,CAAC,KAAK,MAAM,KAAK,WAAW,CAAC,GAAG;AACxD,sBAAI,UAAU,aAAa;AACzB,wBAAI,UAAU,OAAO;AACnB,mCAAa,WAAW;AAAA,oBAC1B;AACA,wBAAI,QAAQ,MAAM,MAAM,YAAY,aAAa,CAAC;AAClD,wBAAI,QAAQ,MAAM,MAAM,cAAc,aAAa,YAAY,MAAM,WAAW,UAAU,MAAM,IAAI,WAAW,CAAC,IAAI,IAAI,IAAI,QAAQ;AACpI,wBAAI,UAAU,QAAQ;AACpB,oCAAc;AACd,oCAAc;AAAA,oBAChB,WAAW,UAAU,MAAM;AACzB,0CAAoB;AAAA,oBACtB,WAAW,UAAU,SAAS;AAC5B,wCAAkB;AAAA,oBACpB,WAAW,UAAU,SAAS;AAC5B,qCAAe,cAAc,OAAO,YAAY;AAChD,8BAAQ;AAAA,oBACV,WAAW,UAAU,oBAAoB;AACvC,yCAAmB,cAAc,OAAO,gBAAgB;AACxD,0BAAI,YAAY,GAAG;AACjB,qCAAa,OAAO;AACpB,kCAAU,WAAW,WAAY;AAC/B,oCAAU;AAAA,wBACZ,GAAG,gBAAgB;AAAA,sBACrB;AAAA,oBACF;AAAA,kBACF;AACA,sBAAI,UAAU,aAAa;AACzB,wBAAI,eAAe,IAAI;AACrB,oCAAc;AACd,0BAAI,oBAAoB,IAAI;AAC1B,0CAAkB;AAAA,sBACpB;AACA,0BAAI,QAAQ,IAAI,aAAa,iBAAiB;AAAA,wBAC5C,MAAM,WAAW,MAAM,CAAC;AAAA,wBACxB,aAAa;AAAA,sBACf,CAAC;AACD,yBAAG,cAAc,KAAK;AACtB,0BAAI,oBAAoB,QAAQ;AAC9B,6BAAK,IAAI,GAAG,QAAQ,KAAK;AAAA,sBAC3B,WAAW,oBAAoB,WAAW;AACxC,6BAAK,IAAI,GAAG,WAAW,KAAK;AAAA,sBAC9B,WAAW,oBAAoB,SAAS;AACtC,6BAAK,IAAI,GAAG,SAAS,KAAK;AAAA,sBAC5B;AACA,0BAAI,iBAAiB,QAAQ;AAC3B;AAAA,sBACF;AAAA,oBACF;AACA,iCAAa;AACb,sCAAkB;AAAA,kBACpB;AACA,0BAAQ,MAAM,KAAK,WAAW,CAAC,IAAI,WAAW;AAAA,gBAChD,OAAO;AACL,sBAAI,UAAU,aAAa;AACzB,iCAAa;AACb,4BAAQ;AAAA,kBACV;AACA,sBAAI,UAAU,OAAO;AACnB,wBAAI,MAAM,IAAI,WAAW,CAAC,GAAG;AAC3B,mCAAa,WAAW;AACxB,8BAAQ;AAAA,oBACV;AAAA,kBACF,WAAW,UAAU,aAAa;AAChC,4BAAQ;AAAA,kBACV;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,YAAI,WAAW,SAAU,OAAO;AAC9B,cAAI,iBAAiB,QAAQ,iBAAiB,YAAY;AACxD,2BAAe;AACf,gBAAI,YAAY,GAAG;AACjB,2BAAa,OAAO;AACpB,wBAAU;AAAA,YACZ;AACA,sBAAU,WAAW,WAAY;AAC/B,wBAAU;AAAA,YACZ,GAAG,KAAK;AACR,oBAAQ,cAAc,KAAK,IAAI,eAAe,IAAI,QAAQ,CAAC,CAAC;AAE5D,eAAG,aAAa;AAChB,gBAAI,QAAQ,IAAI,WAAW,SAAS,EAAC,MAAY,CAAC;AAClD,eAAG,cAAc,KAAK;AACtB,iBAAK,IAAI,GAAG,SAAS,KAAK;AAC1B,gBAAI,SAAS,QAAW;AACtB,sBAAQ,MAAM,KAAK;AAAA,YACrB;AAAA,UACF;AAAA,QACF;AAEA,YAAI,QAAQ,WAAY;AACtB,yBAAe;AACf,cAAI,mBAAmB,QAAW;AAChC,4BAAgB,MAAM;AACtB,8BAAkB;AAAA,UACpB;AACA,cAAI,YAAY,GAAG;AACjB,yBAAa,OAAO;AACpB,sBAAU;AAAA,UACZ;AACA,aAAG,aAAa;AAAA,QAClB;AAEA,YAAI,YAAY,WAAY;AAC1B,oBAAU;AAEV,cAAI,iBAAiB,SAAS;AAC5B,gBAAI,CAAC,eAAe,mBAAmB,QAAW;AAChD,uBAAS,IAAI,MAAM,wBAAwB,mBAAmB,qBAA0B,iBAAiB,aAAa,0BAA0B,aAAa,sBAAsB,gBAAqB,CAAC;AACzM,kBAAI,mBAAmB,QAAW;AAChC,gCAAgB,MAAM;AACtB,kCAAkB;AAAA,cACpB;AAAA,YACF,OAAO;AACL,kBAAI,gBAAgB,KAAK,KAAK,eAAe,KAAK,IAAI,KAAK,mBAAmB,KAAK,IAAI,GAAG,CAAC;AAC3F,4BAAc;AACd,wBAAU,WAAW,WAAY;AAC/B,0BAAU;AAAA,cACZ,GAAG,aAAa;AAAA,YAClB;AACA;AAAA,UACF;AAEA,wBAAc;AACd,uBAAa;AACb,oBAAU,WAAW,WAAY;AAC/B,sBAAU;AAAA,UACZ,GAAG,gBAAgB;AAEnB,yBAAe;AACf,uBAAa;AACb,4BAAkB;AAClB,8BAAoB;AACpB,uBAAa;AACb,uBAAa;AACb,uBAAa;AACb,kBAAQ;AAIR,cAAI,aAAa;AACjB,cAAI,IAAI,MAAM,GAAG,CAAC,MAAM,WAAW,IAAI,MAAM,GAAG,CAAC,MAAM,SAAS;AAC9D,gBAAI,gBAAgB,IAAI;AAEtB,kBAAI,IAAI,IAAI,QAAQ,GAAG;AACvB,2BAAa,MAAM,KAAK,MAAM,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,IAAI,MAAM,IAAI,CAAC,EAAE,QAAQ,+BAA+B,SAAU,GAAG,WAAW;AAClI,uBAAO,cAAc,gCAAgC,KAAK;AAAA,cAC5D,CAAC;AAED,6BAAe,IAAI,QAAQ,GAAG,MAAM,KAAK,MAAM,OAAO,gCAA+B,MAAM,mBAAmB,WAAW;AAAA,YAC3H;AAAA,UACF;AACA,cAAIC,mBAAkB,GAAG;AACzB,cAAI,iBAAiB,CAAC;AACtB,yBAAe,QAAQ,IAAI;AAC3B,cAAID,WAAU,GAAG;AACjB,cAAIA,YAAW,QAAW;AACxB,qBAAS,QAAQA,UAAS;AACxB,kBAAI,OAAO,UAAU,eAAe,KAAKA,UAAS,IAAI,GAAG;AACvD,+BAAe,IAAI,IAAIA,SAAQ,IAAI;AAAA,cACrC;AAAA,YACF;AAAA,UACF;AACA,cAAI;AACF,8BAAkB,UAAU,KAAK,KAAK,SAAS,YAAY,UAAU,YAAYC,kBAAiB,cAAc;AAAA,UAClH,SAAS,OAAO;AACd,kBAAM;AACN,kBAAM;AAAA,UACR;AAAA,QACF;AAEA,WAAG,MAAM;AACT,WAAG,aAAa;AAChB,WAAG,kBAAkB;AACrB,WAAG,UAAU;AACb,WAAG,SAAS;AAEZ,kBAAU;AAAA,MACZ;AAEA,0BAAoB,YAAY,OAAO,OAAO,YAAY,SAAS;AACnE,0BAAoB,UAAU,aAAa;AAC3C,0BAAoB,UAAU,OAAO;AACrC,0BAAoB,UAAU,SAAS;AACvC,0BAAoB,UAAU,QAAQ,WAAY;AAChD,aAAK,OAAO;AAAA,MACd;AAEA,0BAAoB,aAAa;AACjC,0BAAoB,OAAO;AAC3B,0BAAoB,SAAS;AAC7B,0BAAoB,UAAU,kBAAkB;AAEhD,UAAI,IAAI;AACR,UAAI,kBAAkB,WAAc,qBAAqB,UAAa,EAAE,qBAAqB,kBAAkB,aAAa;AAO1H,YAAI;AAAA,MACN;AAEA,OAAC,SAAU,SAAS;AAClB,YAAI,OAAO,WAAW,YAAY,OAAO,OAAO,YAAY,UAAU;AACpE,cAAI,IAAI,QAAQ,OAAO;AACvB,cAAI,MAAM,OAAW,QAAO,UAAU;AAAA,QACxC,WACS,OAAO,WAAW,cAAc,OAAO,KAAK;AACnD,iBAAO,CAAC,SAAS,GAAG,OAAO;AAAA,QAC7B,OACK;AACH,kBAAQ,MAAM;AAAA,QAChB;AAAA,MACF,GAAG,SAAUC,UAAS;AACpB,QAAAA,SAAQ,sBAAsB;AAC9B,QAAAA,SAAQ,oBAAoB;AAC5B,QAAAA,SAAQ,cAAc;AAAA,MACxB,CAAC;AAAA,IACH,GAAE,OAAO,eAAe,cAAe,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,UAAQ,UAAU;AAAA;AAAA;", "names": ["Promise", "codePoint", "octetsCount", "bitsNeeded", "headers", "withCredentials", "exports"]}