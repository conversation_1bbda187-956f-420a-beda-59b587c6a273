package org.jeecg.modules.cw.statistics.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.cw.statistics.entity.CwStatistics;
import org.jeecg.modules.cw.statistics.result.*;

import java.util.Date;
import java.util.List;

public interface ICwStatisticsService extends IService<CwStatistics> {
    /**
     * 查询指定日期所在月份从1号到该日期的金属价格
     * @param today 当天日期
     * @return 结果列表（按日期升序）
     */
    List<CwMetalPriceResult> getMetalPriceMonth(Date today, Integer recentDays);

    /**
     * 按指定维度统计利润，并返回环比、同比、计划比
     * @param date 查询日期（基准日，月/年维度也使用该日期确定期间）
     * @param dimension 统计维度：day | month | year
     * @return 统计结果
     */
    CwProfitStatResult profitStatistics(Date date, String dimension, Integer recentDays);

    /**
     * 利润趋势数据
     * @param date 基准日期，空=今天
     * @param dimension day | month
     * @return 时间序列利润列表
     */
    List<CwProfitTrendPointResult> profitTrend(Date date, String dimension, Integer recentDays);

    /**
     * 金属价量分析利润柱状图数据
     * @param date 基准日期，空=当天
     * @param dimension 维度：month | year
     * @return 各金属计划/实际利润列表
     */
    List<CwMetalProfitBarResult> metalProfitBar(Date date, String dimension);

    /**
     * 成本统计：总成本 / 吨矿成本 / 金属成本
     * @param date 基准日期
     * @param dimension 维度：month | year
     */
    CwCostStatResult costStatistics(Date date, String dimension);

    /**
     * 月度成本趋势：从1月到当前月
     * @param date 基准日期
     * @return 趋势数据
     */
    List<CwCostTrendPointResult> costTrend(Date date);

    /**
     * 按单位获取月度成本趋势：从1月到当前月，包含实际和计划成本
     * @param date 基准日期
     * @param unit 单位key（ckc/ds/sx/jw/all），all表示全公司汇总
     * @return 趋势数据（包含实际和计划成本）
     */
    List<CwCostTrendPointResult> costTrendByUnit(Date date, String unit);

    /**
     * 按单位和类型获取月度成本趋势：从1月到当前月，按类型分组的堆叠数据
     * @param date 基准日期
     * @param unit 单位key（ckc/ds/sx/jw），不支持all
     * @return 按类型分组的趋势数据
     */
    List<CwCostTrendByTypeResult> costTrendByUnitWithTypes(Date date, String unit);

    /**
     * 单位成本柱状图数据
     * @param date 基准日期，空=当日
     * @param dimension month | year
     * @return 各单位计划/实际成本及吨矿成本
     */
    List<CwUnitCostBarResult> unitCostBar(Date date, String dimension);

    /**
     * 单位成本详细数据
     * @param unit 单位key（ckc/ds/sx/jw）
     * @param date 基准日期
     * @param dimension month | year
     */
    CwUnitCostDetailResult unitCostDetail(String unit, Date date, String dimension);
}
