import {
  require_codemirror
} from "./chunk-J32C3BWF.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/.pnpm/codemirror@5.65.18/node_modules/codemirror/addon/hint/anyword-hint.js
var require_anyword_hint = __commonJS({
  "node_modules/.pnpm/codemirror@5.65.18/node_modules/codemirror/addon/hint/anyword-hint.js"(exports, module) {
    (function(mod) {
      if (typeof exports == "object" && typeof module == "object")
        mod(require_codemirror());
      else if (typeof define == "function" && define.amd)
        define(["../../lib/codemirror"], mod);
      else
        mod(CodeMirror);
    })(function(CodeMirror2) {
      "use strict";
      var WORD = /[\w$]+/, RANGE = 500;
      CodeMirror2.registerHelper("hint", "anyword", function(editor, options) {
        var word = options && options.word || WORD;
        var range = options && options.range || RANGE;
        var cur = editor.getCursor(), curLine = editor.getLine(cur.line);
        var end = cur.ch, start = end;
        while (start && word.test(curLine.charAt(start - 1))) --start;
        var curWord = start != end && curLine.slice(start, end);
        var list = options && options.list || [], seen = {};
        var re = new RegExp(word.source, "g");
        for (var dir = -1; dir <= 1; dir += 2) {
          var line = cur.line, endLine = Math.min(Math.max(line + dir * range, editor.firstLine()), editor.lastLine()) + dir;
          for (; line != endLine; line += dir) {
            var text = editor.getLine(line), m;
            while (m = re.exec(text)) {
              if (line == cur.line && m[0] === curWord) continue;
              if ((!curWord || m[0].lastIndexOf(curWord, 0) == 0) && !Object.prototype.hasOwnProperty.call(seen, m[0])) {
                seen[m[0]] = true;
                list.push(m[0]);
              }
            }
          }
        }
        return { list, from: CodeMirror2.Pos(cur.line, start), to: CodeMirror2.Pos(cur.line, end) };
      });
    });
  }
});
export default require_anyword_hint();
//# sourceMappingURL=codemirror_addon_hint_anyword-hint__js.js.map
