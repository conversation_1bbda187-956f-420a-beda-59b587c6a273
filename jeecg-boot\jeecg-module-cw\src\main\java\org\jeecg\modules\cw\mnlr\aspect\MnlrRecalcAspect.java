package org.jeecg.modules.cw.mnlr.aspect;

import lombok.extern.log4j.Log4j2;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.jeecg.modules.cw.mnlr.param.CwMnlrDaySumbitParam;
import org.jeecg.modules.cw.mnlr.param.CwMnlrMonthSumbitParam;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrStatisticsDayService;
import cn.hutool.core.date.DateUtil;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicReference;
import javax.annotation.PreDestroy;
import javax.annotation.PostConstruct;
import java.lang.reflect.Method;

/**
 * 提交成功后自动重算当月模拟利润的切面。
 * <p>
 * 拦截 mnlr 包内 Service 的 *submit(..) 方法，
 * 根据参数中的 submitDate 或 Date 推断需要重算的月份，
 * 调用 {@link ICwMnlrStatisticsDayService#recalcMonth(int, int)} 进行全月重算。
 */
@Aspect
@Component
@Log4j2
public class MnlrRecalcAspect {

    @Resource
    private ICwMnlrStatisticsDayService statisticsDayService;

    /**
     * 单线程池，顺序执行重算任务，避免高并发写数据库导致锁冲突。
     * 使用更健壮的配置，避免任务被拒绝。
     */
    private final AtomicReference<ExecutorService> recalcExecutor = new AtomicReference<>();

    @PreDestroy
    public void shutdown() {
        ExecutorService currentExecutor = recalcExecutor.get();
        if (currentExecutor != null) {
            try {
                log.info("[MnlrRecalcAspect] 开始关闭重算线程池");
                currentExecutor.shutdown();
                // 等待线程池关闭，最多等待30秒
                if (!currentExecutor.awaitTermination(30, java.util.concurrent.TimeUnit.SECONDS)) {
                    log.warn("[MnlrRecalcAspect] 线程池关闭超时，强制关闭");
                    currentExecutor.shutdownNow();
                }
                log.info("[MnlrRecalcAspect] 重算线程池已关闭");
            } catch (InterruptedException e) {
                log.error("[MnlrRecalcAspect] 关闭线程池时被中断", e);
                Thread.currentThread().interrupt();
                currentExecutor.shutdownNow();
            }
        }
    }

    @PostConstruct
    public void init() {
        ensureExecutorAvailable();
        log.info("[MnlrRecalcAspect] 线程池已初始化");
    }

    /**
     * 检查线程池状态，如果已关闭则重新创建
     */
    private synchronized void ensureExecutorAvailable() {
        ExecutorService currentExecutor = recalcExecutor.get();
        if (currentExecutor == null || currentExecutor.isShutdown() || currentExecutor.isTerminated()) {
            log.warn("[MnlrRecalcAspect] 检测到线程池已关闭，重新创建线程池");
            ExecutorService newExecutor = Executors.newSingleThreadExecutor(new ThreadFactory() {
                @Override
                public Thread newThread(Runnable r) {
                    Thread t = new Thread(r, "mnlr-recalc-executor");
                    t.setDaemon(true);
                    t.setUncaughtExceptionHandler((thread, throwable) -> {
                        log.error("[MnlrRecalcAspect] 重算线程异常: {}", thread.getName(), throwable);
                    });
                    return t;
                }
            });
            recalcExecutor.set(newExecutor);
            log.info("[MnlrRecalcAspect] 线程池已重新创建");
        }
    }

    /**
     * Pointcut 说明：
     *  - 任意返回值，位于 org.jeecg.modules.cw.mnlr.service 包及其子包
     *  - 方法名以 submit 结尾
     */
    private static final String POINTCUT = "execution(* org.jeecg.modules.cw.*.service..*.submit(..))";

    @AfterReturning(pointcut = POINTCUT)
    public void afterSubmit(JoinPoint joinPoint) {
        Date dateToRecalc = extractDate(joinPoint.getArgs());
        if (dateToRecalc != null) {
            try {
                // 确保线程池可用
                ensureExecutorAvailable();
                
                ExecutorService executor = recalcExecutor.get();
                if (executor == null) {
                    log.error("[MnlrRecalcAspect] 无法获取可用的线程池，跳过异步重算，日期: {}", DateUtil.formatDate(dateToRecalc));
                    return;
                }
                
                // 提取年月信息用于全月重算
                int year = DateUtil.year(dateToRecalc);
                int month = DateUtil.month(dateToRecalc) + 1; // DateUtil.month() 返回 0-11，需要 +1
                
                log.info("[MnlrRecalcAspect] submit 完成后异步触发全月重算：{}年{}月", year, month);
                executor.submit(() -> {
                    try {
                        statisticsDayService.recalcMonth(year, month);
                        log.info("[MnlrRecalcAspect] 异步重算{}年{}月模拟利润完成", year, month);
                    } catch (Exception ex) {
                        log.error("[MnlrRecalcAspect] 异步重算{}年{}月模拟利润异常", year, month, ex);
                    }
                });
            } catch (Exception e) {
                log.error("[MnlrRecalcAspect] 重算模拟利润异常", e);
            }
        } else {
            log.warn("[MnlrRecalcAspect] 未获取到 submitDate，跳过重算");
        }
    }

    /**
     * 从方法参数中提取日期信息。
     * 支持以下情况：
     *  1. 参数本身为 java.util.Date
     *  2. 参数为 CwMnlrDaySumbitParam / CwMnlrMonthSumbitParam，读取 getSubmitDate()
     */
    private Date extractDate(Object[] args) {
        if (args == null) {
            return null;
        }
        for (Object arg : args) {
            if (arg == null) {
                continue;
            }
            if (arg instanceof Date) {
                return (Date) arg;
            }
            if (arg instanceof CwMnlrDaySumbitParam) {
                return ((CwMnlrDaySumbitParam) arg).getSubmitDate();
            }
            if (arg instanceof CwMnlrMonthSumbitParam) {
                return ((CwMnlrMonthSumbitParam) arg).getSubmitDate();
            }
        }
        return null;
    }
} 