{"version": 3, "sources": ["../../.pnpm/big.js@6.2.2/node_modules/big.js/big.mjs"], "sourcesContent": ["/*\r\n *  big.js v6.2.2\r\n *  A small, fast, easy-to-use library for arbitrary-precision decimal arithmetic.\r\n *  Copyright (c) 2024 <PERSON>\r\n *  https://github.com/MikeMcl/big.js/LICENCE.md\r\n */\r\n\r\n\r\n/************************************** EDITABLE DEFAULTS *****************************************/\r\n\r\n\r\n  // The default values below must be integers within the stated ranges.\r\n\r\n  /*\r\n   * The maximum number of decimal places (DP) of the results of operations involving division:\r\n   * div and sqrt, and pow with negative exponents.\r\n   */\r\nvar DP = 20,          // 0 to MAX_DP\r\n\r\n  /*\r\n   * The rounding mode (RM) used when rounding to the above decimal places.\r\n   *\r\n   *  0  Towards zero (i.e. truncate, no rounding).       (ROUND_DOWN)\r\n   *  1  To nearest neighbour. If equidistant, round up.  (ROUND_HALF_UP)\r\n   *  2  To nearest neighbour. If equidistant, to even.   (ROUND_HALF_EVEN)\r\n   *  3  Away from zero.                                  (ROUND_UP)\r\n   */\r\n  RM = 1,             // 0, 1, 2 or 3\r\n\r\n  // The maximum value of DP and Big.DP.\r\n  MAX_DP = 1E6,       // 0 to 1000000\r\n\r\n  // The maximum magnitude of the exponent argument to the pow method.\r\n  MAX_POWER = 1E6,    // 1 to 1000000\r\n\r\n  /*\r\n   * The negative exponent (NE) at and beneath which toString returns exponential notation.\r\n   * (JavaScript numbers: -7)\r\n   * -1000000 is the minimum recommended exponent value of a Big.\r\n   */\r\n  NE = -7,            // 0 to -1000000\r\n\r\n  /*\r\n   * The positive exponent (PE) at and above which toString returns exponential notation.\r\n   * (JavaScript numbers: 21)\r\n   * 1000000 is the maximum recommended exponent value of a Big, but this limit is not enforced.\r\n   */\r\n  PE = 21,            // 0 to 1000000\r\n\r\n  /*\r\n   * When true, an error will be thrown if a primitive number is passed to the Big constructor,\r\n   * or if valueOf is called, or if toNumber is called on a Big which cannot be converted to a\r\n   * primitive number without a loss of precision.\r\n   */\r\n  STRICT = false,     // true or false\r\n\r\n\r\n/**************************************************************************************************/\r\n\r\n\r\n  // Error messages.\r\n  NAME = '[big.js] ',\r\n  INVALID = NAME + 'Invalid ',\r\n  INVALID_DP = INVALID + 'decimal places',\r\n  INVALID_RM = INVALID + 'rounding mode',\r\n  DIV_BY_ZERO = NAME + 'Division by zero',\r\n\r\n  // The shared prototype object.\r\n  P = {},\r\n  UNDEFINED = void 0,\r\n  NUMERIC = /^-?(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i;\r\n\r\n\r\n/*\r\n * Create and return a Big constructor.\r\n */\r\nfunction _Big_() {\r\n\r\n  /*\r\n   * The Big constructor and exported function.\r\n   * Create and return a new instance of a Big number object.\r\n   *\r\n   * n {number|string|Big} A numeric value.\r\n   */\r\n  function Big(n) {\r\n    var x = this;\r\n\r\n    // Enable constructor usage without new.\r\n    if (!(x instanceof Big)) return n === UNDEFINED ? _Big_() : new Big(n);\r\n\r\n    // Duplicate.\r\n    if (n instanceof Big) {\r\n      x.s = n.s;\r\n      x.e = n.e;\r\n      x.c = n.c.slice();\r\n    } else {\r\n      if (typeof n !== 'string') {\r\n        if (Big.strict === true && typeof n !== 'bigint') {\r\n          throw TypeError(INVALID + 'value');\r\n        }\r\n\r\n        // Minus zero?\r\n        n = n === 0 && 1 / n < 0 ? '-0' : String(n);\r\n      }\r\n\r\n      parse(x, n);\r\n    }\r\n\r\n    // Retain a reference to this Big constructor.\r\n    // Shadow Big.prototype.constructor which points to Object.\r\n    x.constructor = Big;\r\n  }\r\n\r\n  Big.prototype = P;\r\n  Big.DP = DP;\r\n  Big.RM = RM;\r\n  Big.NE = NE;\r\n  Big.PE = PE;\r\n  Big.strict = STRICT;\r\n  Big.roundDown = 0;\r\n  Big.roundHalfUp = 1;\r\n  Big.roundHalfEven = 2;\r\n  Big.roundUp = 3;\r\n\r\n  return Big;\r\n}\r\n\r\n\r\n/*\r\n * Parse the number or string value passed to a Big constructor.\r\n *\r\n * x {Big} A Big number instance.\r\n * n {number|string} A numeric value.\r\n */\r\nfunction parse(x, n) {\r\n  var e, i, nl;\r\n\r\n  if (!NUMERIC.test(n)) {\r\n    throw Error(INVALID + 'number');\r\n  }\r\n\r\n  // Determine sign.\r\n  x.s = n.charAt(0) == '-' ? (n = n.slice(1), -1) : 1;\r\n\r\n  // Decimal point?\r\n  if ((e = n.indexOf('.')) > -1) n = n.replace('.', '');\r\n\r\n  // Exponential form?\r\n  if ((i = n.search(/e/i)) > 0) {\r\n\r\n    // Determine exponent.\r\n    if (e < 0) e = i;\r\n    e += +n.slice(i + 1);\r\n    n = n.substring(0, i);\r\n  } else if (e < 0) {\r\n\r\n    // Integer.\r\n    e = n.length;\r\n  }\r\n\r\n  nl = n.length;\r\n\r\n  // Determine leading zeros.\r\n  for (i = 0; i < nl && n.charAt(i) == '0';) ++i;\r\n\r\n  if (i == nl) {\r\n\r\n    // Zero.\r\n    x.c = [x.e = 0];\r\n  } else {\r\n\r\n    // Determine trailing zeros.\r\n    for (; nl > 0 && n.charAt(--nl) == '0';);\r\n    x.e = e - i - 1;\r\n    x.c = [];\r\n\r\n    // Convert string to array of digits without leading/trailing zeros.\r\n    for (e = 0; i <= nl;) x.c[e++] = +n.charAt(i++);\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * Round Big x to a maximum of sd significant digits using rounding mode rm.\r\n *\r\n * x {Big} The Big to round.\r\n * sd {number} Significant digits: integer, 0 to MAX_DP inclusive.\r\n * rm {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n * [more] {boolean} Whether the result of division was truncated.\r\n */\r\nfunction round(x, sd, rm, more) {\r\n  var xc = x.c;\r\n\r\n  if (rm === UNDEFINED) rm = x.constructor.RM;\r\n  if (rm !== 0 && rm !== 1 && rm !== 2 && rm !== 3) {\r\n    throw Error(INVALID_RM);\r\n  }\r\n\r\n  if (sd < 1) {\r\n    more =\r\n      rm === 3 && (more || !!xc[0]) || sd === 0 && (\r\n      rm === 1 && xc[0] >= 5 ||\r\n      rm === 2 && (xc[0] > 5 || xc[0] === 5 && (more || xc[1] !== UNDEFINED))\r\n    );\r\n\r\n    xc.length = 1;\r\n\r\n    if (more) {\r\n\r\n      // 1, 0.1, 0.01, 0.001, 0.0001 etc.\r\n      x.e = x.e - sd + 1;\r\n      xc[0] = 1;\r\n    } else {\r\n\r\n      // Zero.\r\n      xc[0] = x.e = 0;\r\n    }\r\n  } else if (sd < xc.length) {\r\n\r\n    // xc[sd] is the digit after the digit that may be rounded up.\r\n    more =\r\n      rm === 1 && xc[sd] >= 5 ||\r\n      rm === 2 && (xc[sd] > 5 || xc[sd] === 5 &&\r\n        (more || xc[sd + 1] !== UNDEFINED || xc[sd - 1] & 1)) ||\r\n      rm === 3 && (more || !!xc[0]);\r\n\r\n    // Remove any digits after the required precision.\r\n    xc.length = sd;\r\n\r\n    // Round up?\r\n    if (more) {\r\n\r\n      // Rounding up may mean the previous digit has to be rounded up.\r\n      for (; ++xc[--sd] > 9;) {\r\n        xc[sd] = 0;\r\n        if (sd === 0) {\r\n          ++x.e;\r\n          xc.unshift(1);\r\n          break;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Remove trailing zeros.\r\n    for (sd = xc.length; !xc[--sd];) xc.pop();\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * Return a string representing the value of Big x in normal or exponential notation.\r\n * Handles P.toExponential, P.toFixed, P.toJSON, P.toPrecision, P.toString and P.valueOf.\r\n */\r\nfunction stringify(x, doExponential, isNonzero) {\r\n  var e = x.e,\r\n    s = x.c.join(''),\r\n    n = s.length;\r\n\r\n  // Exponential notation?\r\n  if (doExponential) {\r\n    s = s.charAt(0) + (n > 1 ? '.' + s.slice(1) : '') + (e < 0 ? 'e' : 'e+') + e;\r\n\r\n  // Normal notation.\r\n  } else if (e < 0) {\r\n    for (; ++e;) s = '0' + s;\r\n    s = '0.' + s;\r\n  } else if (e > 0) {\r\n    if (++e > n) {\r\n      for (e -= n; e--;) s += '0';\r\n    } else if (e < n) {\r\n      s = s.slice(0, e) + '.' + s.slice(e);\r\n    }\r\n  } else if (n > 1) {\r\n    s = s.charAt(0) + '.' + s.slice(1);\r\n  }\r\n\r\n  return x.s < 0 && isNonzero ? '-' + s : s;\r\n}\r\n\r\n\r\n// Prototype/instance methods\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the absolute value of this Big.\r\n */\r\nP.abs = function () {\r\n  var x = new this.constructor(this);\r\n  x.s = 1;\r\n  return x;\r\n};\r\n\r\n\r\n/*\r\n * Return 1 if the value of this Big is greater than the value of Big y,\r\n *       -1 if the value of this Big is less than the value of Big y, or\r\n *        0 if they have the same value.\r\n */\r\nP.cmp = function (y) {\r\n  var isneg,\r\n    x = this,\r\n    xc = x.c,\r\n    yc = (y = new x.constructor(y)).c,\r\n    i = x.s,\r\n    j = y.s,\r\n    k = x.e,\r\n    l = y.e;\r\n\r\n  // Either zero?\r\n  if (!xc[0] || !yc[0]) return !xc[0] ? !yc[0] ? 0 : -j : i;\r\n\r\n  // Signs differ?\r\n  if (i != j) return i;\r\n\r\n  isneg = i < 0;\r\n\r\n  // Compare exponents.\r\n  if (k != l) return k > l ^ isneg ? 1 : -1;\r\n\r\n  j = (k = xc.length) < (l = yc.length) ? k : l;\r\n\r\n  // Compare digit by digit.\r\n  for (i = -1; ++i < j;) {\r\n    if (xc[i] != yc[i]) return xc[i] > yc[i] ^ isneg ? 1 : -1;\r\n  }\r\n\r\n  // Compare lengths.\r\n  return k == l ? 0 : k > l ^ isneg ? 1 : -1;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big divided by the value of Big y, rounded,\r\n * if necessary, to a maximum of Big.DP decimal places using rounding mode Big.RM.\r\n */\r\nP.div = function (y) {\r\n  var x = this,\r\n    Big = x.constructor,\r\n    a = x.c,                  // dividend\r\n    b = (y = new Big(y)).c,   // divisor\r\n    k = x.s == y.s ? 1 : -1,\r\n    dp = Big.DP;\r\n\r\n  if (dp !== ~~dp || dp < 0 || dp > MAX_DP) {\r\n    throw Error(INVALID_DP);\r\n  }\r\n\r\n  // Divisor is zero?\r\n  if (!b[0]) {\r\n    throw Error(DIV_BY_ZERO);\r\n  }\r\n\r\n  // Dividend is 0? Return +-0.\r\n  if (!a[0]) {\r\n    y.s = k;\r\n    y.c = [y.e = 0];\r\n    return y;\r\n  }\r\n\r\n  var bl, bt, n, cmp, ri,\r\n    bz = b.slice(),\r\n    ai = bl = b.length,\r\n    al = a.length,\r\n    r = a.slice(0, bl),   // remainder\r\n    rl = r.length,\r\n    q = y,                // quotient\r\n    qc = q.c = [],\r\n    qi = 0,\r\n    p = dp + (q.e = x.e - y.e) + 1;    // precision of the result\r\n\r\n  q.s = k;\r\n  k = p < 0 ? 0 : p;\r\n\r\n  // Create version of divisor with leading zero.\r\n  bz.unshift(0);\r\n\r\n  // Add zeros to make remainder as long as divisor.\r\n  for (; rl++ < bl;) r.push(0);\r\n\r\n  do {\r\n\r\n    // n is how many times the divisor goes into current remainder.\r\n    for (n = 0; n < 10; n++) {\r\n\r\n      // Compare divisor and remainder.\r\n      if (bl != (rl = r.length)) {\r\n        cmp = bl > rl ? 1 : -1;\r\n      } else {\r\n        for (ri = -1, cmp = 0; ++ri < bl;) {\r\n          if (b[ri] != r[ri]) {\r\n            cmp = b[ri] > r[ri] ? 1 : -1;\r\n            break;\r\n          }\r\n        }\r\n      }\r\n\r\n      // If divisor < remainder, subtract divisor from remainder.\r\n      if (cmp < 0) {\r\n\r\n        // Remainder can't be more than 1 digit longer than divisor.\r\n        // Equalise lengths using divisor with extra leading zero?\r\n        for (bt = rl == bl ? b : bz; rl;) {\r\n          if (r[--rl] < bt[rl]) {\r\n            ri = rl;\r\n            for (; ri && !r[--ri];) r[ri] = 9;\r\n            --r[ri];\r\n            r[rl] += 10;\r\n          }\r\n          r[rl] -= bt[rl];\r\n        }\r\n\r\n        for (; !r[0];) r.shift();\r\n      } else {\r\n        break;\r\n      }\r\n    }\r\n\r\n    // Add the digit n to the result array.\r\n    qc[qi++] = cmp ? n : ++n;\r\n\r\n    // Update the remainder.\r\n    if (r[0] && cmp) r[rl] = a[ai] || 0;\r\n    else r = [a[ai]];\r\n\r\n  } while ((ai++ < al || r[0] !== UNDEFINED) && k--);\r\n\r\n  // Leading zero? Do not remove if result is simply zero (qi == 1).\r\n  if (!qc[0] && qi != 1) {\r\n\r\n    // There can't be more than one zero.\r\n    qc.shift();\r\n    q.e--;\r\n    p--;\r\n  }\r\n\r\n  // Round?\r\n  if (qi > p) round(q, p, Big.RM, r[0] !== UNDEFINED);\r\n\r\n  return q;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is equal to the value of Big y, otherwise return false.\r\n */\r\nP.eq = function (y) {\r\n  return this.cmp(y) === 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is greater than the value of Big y, otherwise return\r\n * false.\r\n */\r\nP.gt = function (y) {\r\n  return this.cmp(y) > 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is greater than or equal to the value of Big y, otherwise\r\n * return false.\r\n */\r\nP.gte = function (y) {\r\n  return this.cmp(y) > -1;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is less than the value of Big y, otherwise return false.\r\n */\r\nP.lt = function (y) {\r\n  return this.cmp(y) < 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Big is less than or equal to the value of Big y, otherwise\r\n * return false.\r\n */\r\nP.lte = function (y) {\r\n  return this.cmp(y) < 1;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big minus the value of Big y.\r\n */\r\nP.minus = P.sub = function (y) {\r\n  var i, j, t, xlty,\r\n    x = this,\r\n    Big = x.constructor,\r\n    a = x.s,\r\n    b = (y = new Big(y)).s;\r\n\r\n  // Signs differ?\r\n  if (a != b) {\r\n    y.s = -b;\r\n    return x.plus(y);\r\n  }\r\n\r\n  var xc = x.c.slice(),\r\n    xe = x.e,\r\n    yc = y.c,\r\n    ye = y.e;\r\n\r\n  // Either zero?\r\n  if (!xc[0] || !yc[0]) {\r\n    if (yc[0]) {\r\n      y.s = -b;\r\n    } else if (xc[0]) {\r\n      y = new Big(x);\r\n    } else {\r\n      y.s = 1;\r\n    }\r\n    return y;\r\n  }\r\n\r\n  // Determine which is the bigger number. Prepend zeros to equalise exponents.\r\n  if (a = xe - ye) {\r\n\r\n    if (xlty = a < 0) {\r\n      a = -a;\r\n      t = xc;\r\n    } else {\r\n      ye = xe;\r\n      t = yc;\r\n    }\r\n\r\n    t.reverse();\r\n    for (b = a; b--;) t.push(0);\r\n    t.reverse();\r\n  } else {\r\n\r\n    // Exponents equal. Check digit by digit.\r\n    j = ((xlty = xc.length < yc.length) ? xc : yc).length;\r\n\r\n    for (a = b = 0; b < j; b++) {\r\n      if (xc[b] != yc[b]) {\r\n        xlty = xc[b] < yc[b];\r\n        break;\r\n      }\r\n    }\r\n  }\r\n\r\n  // x < y? Point xc to the array of the bigger number.\r\n  if (xlty) {\r\n    t = xc;\r\n    xc = yc;\r\n    yc = t;\r\n    y.s = -y.s;\r\n  }\r\n\r\n  /*\r\n   * Append zeros to xc if shorter. No need to add zeros to yc if shorter as subtraction only\r\n   * needs to start at yc.length.\r\n   */\r\n  if ((b = (j = yc.length) - (i = xc.length)) > 0) for (; b--;) xc[i++] = 0;\r\n\r\n  // Subtract yc from xc.\r\n  for (b = i; j > a;) {\r\n    if (xc[--j] < yc[j]) {\r\n      for (i = j; i && !xc[--i];) xc[i] = 9;\r\n      --xc[i];\r\n      xc[j] += 10;\r\n    }\r\n\r\n    xc[j] -= yc[j];\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  for (; xc[--b] === 0;) xc.pop();\r\n\r\n  // Remove leading zeros and adjust exponent accordingly.\r\n  for (; xc[0] === 0;) {\r\n    xc.shift();\r\n    --ye;\r\n  }\r\n\r\n  if (!xc[0]) {\r\n\r\n    // n - n = +0\r\n    y.s = 1;\r\n\r\n    // Result must be zero.\r\n    xc = [ye = 0];\r\n  }\r\n\r\n  y.c = xc;\r\n  y.e = ye;\r\n\r\n  return y;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big modulo the value of Big y.\r\n */\r\nP.mod = function (y) {\r\n  var ygtx,\r\n    x = this,\r\n    Big = x.constructor,\r\n    a = x.s,\r\n    b = (y = new Big(y)).s;\r\n\r\n  if (!y.c[0]) {\r\n    throw Error(DIV_BY_ZERO);\r\n  }\r\n\r\n  x.s = y.s = 1;\r\n  ygtx = y.cmp(x) == 1;\r\n  x.s = a;\r\n  y.s = b;\r\n\r\n  if (ygtx) return new Big(x);\r\n\r\n  a = Big.DP;\r\n  b = Big.RM;\r\n  Big.DP = Big.RM = 0;\r\n  x = x.div(y);\r\n  Big.DP = a;\r\n  Big.RM = b;\r\n\r\n  return this.minus(x.times(y));\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big negated.\r\n */\r\nP.neg = function () {\r\n  var x = new this.constructor(this);\r\n  x.s = -x.s;\r\n  return x;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big plus the value of Big y.\r\n */\r\nP.plus = P.add = function (y) {\r\n  var e, k, t,\r\n    x = this,\r\n    Big = x.constructor;\r\n\r\n  y = new Big(y);\r\n\r\n  // Signs differ?\r\n  if (x.s != y.s) {\r\n    y.s = -y.s;\r\n    return x.minus(y);\r\n  }\r\n\r\n  var xe = x.e,\r\n    xc = x.c,\r\n    ye = y.e,\r\n    yc = y.c;\r\n\r\n  // Either zero?\r\n  if (!xc[0] || !yc[0]) {\r\n    if (!yc[0]) {\r\n      if (xc[0]) {\r\n        y = new Big(x);\r\n      } else {\r\n        y.s = x.s;\r\n      }\r\n    }\r\n    return y;\r\n  }\r\n\r\n  xc = xc.slice();\r\n\r\n  // Prepend zeros to equalise exponents.\r\n  // Note: reverse faster than unshifts.\r\n  if (e = xe - ye) {\r\n    if (e > 0) {\r\n      ye = xe;\r\n      t = yc;\r\n    } else {\r\n      e = -e;\r\n      t = xc;\r\n    }\r\n\r\n    t.reverse();\r\n    for (; e--;) t.push(0);\r\n    t.reverse();\r\n  }\r\n\r\n  // Point xc to the longer array.\r\n  if (xc.length - yc.length < 0) {\r\n    t = yc;\r\n    yc = xc;\r\n    xc = t;\r\n  }\r\n\r\n  e = yc.length;\r\n\r\n  // Only start adding at yc.length - 1 as the further digits of xc can be left as they are.\r\n  for (k = 0; e; xc[e] %= 10) k = (xc[--e] = xc[e] + yc[e] + k) / 10 | 0;\r\n\r\n  // No need to check for zero, as +x + +y != 0 && -x + -y != 0\r\n\r\n  if (k) {\r\n    xc.unshift(k);\r\n    ++ye;\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  for (e = xc.length; xc[--e] === 0;) xc.pop();\r\n\r\n  y.c = xc;\r\n  y.e = ye;\r\n\r\n  return y;\r\n};\r\n\r\n\r\n/*\r\n * Return a Big whose value is the value of this Big raised to the power n.\r\n * If n is negative, round to a maximum of Big.DP decimal places using rounding\r\n * mode Big.RM.\r\n *\r\n * n {number} Integer, -MAX_POWER to MAX_POWER inclusive.\r\n */\r\nP.pow = function (n) {\r\n  var x = this,\r\n    one = new x.constructor('1'),\r\n    y = one,\r\n    isneg = n < 0;\r\n\r\n  if (n !== ~~n || n < -MAX_POWER || n > MAX_POWER) {\r\n    throw Error(INVALID + 'exponent');\r\n  }\r\n\r\n  if (isneg) n = -n;\r\n\r\n  for (;;) {\r\n    if (n & 1) y = y.times(x);\r\n    n >>= 1;\r\n    if (!n) break;\r\n    x = x.times(x);\r\n  }\r\n\r\n  return isneg ? one.div(y) : y;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big rounded to a maximum precision of sd\r\n * significant digits using rounding mode rm, or Big.RM if rm is not specified.\r\n *\r\n * sd {number} Significant digits: integer, 1 to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n */\r\nP.prec = function (sd, rm) {\r\n  if (sd !== ~~sd || sd < 1 || sd > MAX_DP) {\r\n    throw Error(INVALID + 'precision');\r\n  }\r\n  return round(new this.constructor(this), sd, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big rounded to a maximum of dp decimal places\r\n * using rounding mode rm, or Big.RM if rm is not specified.\r\n * If dp is negative, round to an integer which is a multiple of 10**-dp.\r\n * If dp is not specified, round to 0 decimal places.\r\n *\r\n * dp? {number} Integer, -MAX_DP to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n */\r\nP.round = function (dp, rm) {\r\n  if (dp === UNDEFINED) dp = 0;\r\n  else if (dp !== ~~dp || dp < -MAX_DP || dp > MAX_DP) {\r\n    throw Error(INVALID_DP);\r\n  }\r\n  return round(new this.constructor(this), dp + this.e + 1, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the square root of the value of this Big, rounded, if\r\n * necessary, to a maximum of Big.DP decimal places using rounding mode Big.RM.\r\n */\r\nP.sqrt = function () {\r\n  var r, c, t,\r\n    x = this,\r\n    Big = x.constructor,\r\n    s = x.s,\r\n    e = x.e,\r\n    half = new Big('0.5');\r\n\r\n  // Zero?\r\n  if (!x.c[0]) return new Big(x);\r\n\r\n  // Negative?\r\n  if (s < 0) {\r\n    throw Error(NAME + 'No square root');\r\n  }\r\n\r\n  // Estimate.\r\n  s = Math.sqrt(+stringify(x, true, true));\r\n\r\n  // Math.sqrt underflow/overflow?\r\n  // Re-estimate: pass x coefficient to Math.sqrt as integer, then adjust the result exponent.\r\n  if (s === 0 || s === 1 / 0) {\r\n    c = x.c.join('');\r\n    if (!(c.length + e & 1)) c += '0';\r\n    s = Math.sqrt(c);\r\n    e = ((e + 1) / 2 | 0) - (e < 0 || e & 1);\r\n    r = new Big((s == 1 / 0 ? '5e' : (s = s.toExponential()).slice(0, s.indexOf('e') + 1)) + e);\r\n  } else {\r\n    r = new Big(s + '');\r\n  }\r\n\r\n  e = r.e + (Big.DP += 4);\r\n\r\n  // Newton-Raphson iteration.\r\n  do {\r\n    t = r;\r\n    r = half.times(t.plus(x.div(t)));\r\n  } while (t.c.slice(0, e).join('') !== r.c.slice(0, e).join(''));\r\n\r\n  return round(r, (Big.DP -= 4) + r.e + 1, Big.RM);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Big whose value is the value of this Big times the value of Big y.\r\n */\r\nP.times = P.mul = function (y) {\r\n  var c,\r\n    x = this,\r\n    Big = x.constructor,\r\n    xc = x.c,\r\n    yc = (y = new Big(y)).c,\r\n    a = xc.length,\r\n    b = yc.length,\r\n    i = x.e,\r\n    j = y.e;\r\n\r\n  // Determine sign of result.\r\n  y.s = x.s == y.s ? 1 : -1;\r\n\r\n  // Return signed 0 if either 0.\r\n  if (!xc[0] || !yc[0]) {\r\n    y.c = [y.e = 0];\r\n    return y;\r\n  }\r\n\r\n  // Initialise exponent of result as x.e + y.e.\r\n  y.e = i + j;\r\n\r\n  // If array xc has fewer digits than yc, swap xc and yc, and lengths.\r\n  if (a < b) {\r\n    c = xc;\r\n    xc = yc;\r\n    yc = c;\r\n    j = a;\r\n    a = b;\r\n    b = j;\r\n  }\r\n\r\n  // Initialise coefficient array of result with zeros.\r\n  for (c = new Array(j = a + b); j--;) c[j] = 0;\r\n\r\n  // Multiply.\r\n\r\n  // i is initially xc.length.\r\n  for (i = b; i--;) {\r\n    b = 0;\r\n\r\n    // a is yc.length.\r\n    for (j = a + i; j > i;) {\r\n\r\n      // Current sum of products at this digit position, plus carry.\r\n      b = c[j] + yc[i] * xc[j - i - 1] + b;\r\n      c[j--] = b % 10;\r\n\r\n      // carry\r\n      b = b / 10 | 0;\r\n    }\r\n\r\n    c[j] = b;\r\n  }\r\n\r\n  // Increment result exponent if there is a final carry, otherwise remove leading zero.\r\n  if (b) ++y.e;\r\n  else c.shift();\r\n\r\n  // Remove trailing zeros.\r\n  for (i = c.length; !c[--i];) c.pop();\r\n  y.c = c;\r\n\r\n  return y;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big in exponential notation rounded to dp fixed\r\n * decimal places using rounding mode rm, or Big.RM if rm is not specified.\r\n *\r\n * dp? {number} Decimal places: integer, 0 to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n */\r\nP.toExponential = function (dp, rm) {\r\n  var x = this,\r\n    n = x.c[0];\r\n\r\n  if (dp !== UNDEFINED) {\r\n    if (dp !== ~~dp || dp < 0 || dp > MAX_DP) {\r\n      throw Error(INVALID_DP);\r\n    }\r\n    x = round(new x.constructor(x), ++dp, rm);\r\n    for (; x.c.length < dp;) x.c.push(0);\r\n  }\r\n\r\n  return stringify(x, true, !!n);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big in normal notation rounded to dp fixed\r\n * decimal places using rounding mode rm, or Big.RM if rm is not specified.\r\n *\r\n * dp? {number} Decimal places: integer, 0 to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n *\r\n * (-0).toFixed(0) is '0', but (-0.1).toFixed(0) is '-0'.\r\n * (-0).toFixed(1) is '0.0', but (-0.01).toFixed(1) is '-0.0'.\r\n */\r\nP.toFixed = function (dp, rm) {\r\n  var x = this,\r\n    n = x.c[0];\r\n\r\n  if (dp !== UNDEFINED) {\r\n    if (dp !== ~~dp || dp < 0 || dp > MAX_DP) {\r\n      throw Error(INVALID_DP);\r\n    }\r\n    x = round(new x.constructor(x), dp + x.e + 1, rm);\r\n\r\n    // x.e may have changed if the value is rounded up.\r\n    for (dp = dp + x.e + 1; x.c.length < dp;) x.c.push(0);\r\n  }\r\n\r\n  return stringify(x, false, !!n);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big.\r\n * Return exponential notation if this Big has a positive exponent equal to or greater than\r\n * Big.PE, or a negative exponent equal to or less than Big.NE.\r\n * Omit the sign for negative zero.\r\n */\r\nP[Symbol.for('nodejs.util.inspect.custom')] = P.toJSON = P.toString = function () {\r\n  var x = this,\r\n    Big = x.constructor;\r\n  return stringify(x, x.e <= Big.NE || x.e >= Big.PE, !!x.c[0]);\r\n};\r\n\r\n\r\n/*\r\n * Return the value of this Big as a primitve number.\r\n */\r\nP.toNumber = function () {\r\n  var n = +stringify(this, true, true);\r\n  if (this.constructor.strict === true && !this.eq(n.toString())) {\r\n    throw Error(NAME + 'Imprecise conversion');\r\n  }\r\n  return n;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big rounded to sd significant digits using\r\n * rounding mode rm, or Big.RM if rm is not specified.\r\n * Use exponential notation if sd is less than the number of digits necessary to represent\r\n * the integer part of the value in normal notation.\r\n *\r\n * sd {number} Significant digits: integer, 1 to MAX_DP inclusive.\r\n * rm? {number} Rounding mode: 0 (down), 1 (half-up), 2 (half-even) or 3 (up).\r\n */\r\nP.toPrecision = function (sd, rm) {\r\n  var x = this,\r\n    Big = x.constructor,\r\n    n = x.c[0];\r\n\r\n  if (sd !== UNDEFINED) {\r\n    if (sd !== ~~sd || sd < 1 || sd > MAX_DP) {\r\n      throw Error(INVALID + 'precision');\r\n    }\r\n    x = round(new Big(x), sd, rm);\r\n    for (; x.c.length < sd;) x.c.push(0);\r\n  }\r\n\r\n  return stringify(x, sd <= x.e || x.e <= Big.NE || x.e >= Big.PE, !!n);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Big.\r\n * Return exponential notation if this Big has a positive exponent equal to or greater than\r\n * Big.PE, or a negative exponent equal to or less than Big.NE.\r\n * Include the sign for negative zero.\r\n */\r\nP.valueOf = function () {\r\n  var x = this,\r\n    Big = x.constructor;\r\n  if (Big.strict === true) {\r\n    throw Error(NAME + 'valueOf disallowed');\r\n  }\r\n  return stringify(x, x.e <= Big.NE || x.e >= Big.PE, true);\r\n};\r\n\r\n\r\n// Export\r\n\r\n\r\nexport var Big = _Big_();\r\n\r\n/// <reference types=\"https://raw.githubusercontent.com/DefinitelyTyped/DefinitelyTyped/master/types/big.js/index.d.ts\" />\r\nexport default Big;\r\n"], "mappings": ";;;AAiBA,IAAI,KAAK;AAAT,IAUE,KAAK;AAVP,IAaE,SAAS;AAbX,IAgBE,YAAY;AAhBd,IAuBE,KAAK;AAvBP,IA8BE,KAAK;AA9BP,IAqCE,SAAS;AArCX,IA4CE,OAAO;AA5CT,IA6CE,UAAU,OAAO;AA7CnB,IA8CE,aAAa,UAAU;AA9CzB,IA+CE,aAAa,UAAU;AA/CzB,IAgDE,cAAc,OAAO;AAhDvB,IAmDE,IAAI,CAAC;AAnDP,IAoDE,YAAY;AApDd,IAqDE,UAAU;AAMZ,SAAS,QAAQ;AAQf,WAASA,KAAI,GAAG;AACd,QAAI,IAAI;AAGR,QAAI,EAAE,aAAaA,MAAM,QAAO,MAAM,YAAY,MAAM,IAAI,IAAIA,KAAI,CAAC;AAGrE,QAAI,aAAaA,MAAK;AACpB,QAAE,IAAI,EAAE;AACR,QAAE,IAAI,EAAE;AACR,QAAE,IAAI,EAAE,EAAE,MAAM;AAAA,IAClB,OAAO;AACL,UAAI,OAAO,MAAM,UAAU;AACzB,YAAIA,KAAI,WAAW,QAAQ,OAAO,MAAM,UAAU;AAChD,gBAAM,UAAU,UAAU,OAAO;AAAA,QACnC;AAGA,YAAI,MAAM,KAAK,IAAI,IAAI,IAAI,OAAO,OAAO,CAAC;AAAA,MAC5C;AAEA,YAAM,GAAG,CAAC;AAAA,IACZ;AAIA,MAAE,cAAcA;AAAA,EAClB;AAEA,EAAAA,KAAI,YAAY;AAChB,EAAAA,KAAI,KAAK;AACT,EAAAA,KAAI,KAAK;AACT,EAAAA,KAAI,KAAK;AACT,EAAAA,KAAI,KAAK;AACT,EAAAA,KAAI,SAAS;AACb,EAAAA,KAAI,YAAY;AAChB,EAAAA,KAAI,cAAc;AAClB,EAAAA,KAAI,gBAAgB;AACpB,EAAAA,KAAI,UAAU;AAEd,SAAOA;AACT;AASA,SAAS,MAAM,GAAG,GAAG;AACnB,MAAI,GAAG,GAAG;AAEV,MAAI,CAAC,QAAQ,KAAK,CAAC,GAAG;AACpB,UAAM,MAAM,UAAU,QAAQ;AAAA,EAChC;AAGA,IAAE,IAAI,EAAE,OAAO,CAAC,KAAK,OAAO,IAAI,EAAE,MAAM,CAAC,GAAG,MAAM;AAGlD,OAAK,IAAI,EAAE,QAAQ,GAAG,KAAK,GAAI,KAAI,EAAE,QAAQ,KAAK,EAAE;AAGpD,OAAK,IAAI,EAAE,OAAO,IAAI,KAAK,GAAG;AAG5B,QAAI,IAAI,EAAG,KAAI;AACf,SAAK,CAAC,EAAE,MAAM,IAAI,CAAC;AACnB,QAAI,EAAE,UAAU,GAAG,CAAC;AAAA,EACtB,WAAW,IAAI,GAAG;AAGhB,QAAI,EAAE;AAAA,EACR;AAEA,OAAK,EAAE;AAGP,OAAK,IAAI,GAAG,IAAI,MAAM,EAAE,OAAO,CAAC,KAAK,MAAM,GAAE;AAE7C,MAAI,KAAK,IAAI;AAGX,MAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AAAA,EAChB,OAAO;AAGL,WAAO,KAAK,KAAK,EAAE,OAAO,EAAE,EAAE,KAAK,MAAK;AACxC,MAAE,IAAI,IAAI,IAAI;AACd,MAAE,IAAI,CAAC;AAGP,SAAK,IAAI,GAAG,KAAK,KAAK,GAAE,EAAE,GAAG,IAAI,CAAC,EAAE,OAAO,GAAG;AAAA,EAChD;AAEA,SAAO;AACT;AAWA,SAAS,MAAM,GAAG,IAAI,IAAI,MAAM;AAC9B,MAAI,KAAK,EAAE;AAEX,MAAI,OAAO,UAAW,MAAK,EAAE,YAAY;AACzC,MAAI,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO,GAAG;AAChD,UAAM,MAAM,UAAU;AAAA,EACxB;AAEA,MAAI,KAAK,GAAG;AACV,WACE,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM,OAAO,MACxC,OAAO,KAAK,GAAG,CAAC,KAAK,KACrB,OAAO,MAAM,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM,MAAM,QAAQ,GAAG,CAAC,MAAM;AAG9D,OAAG,SAAS;AAEZ,QAAI,MAAM;AAGR,QAAE,IAAI,EAAE,IAAI,KAAK;AACjB,SAAG,CAAC,IAAI;AAAA,IACV,OAAO;AAGL,SAAG,CAAC,IAAI,EAAE,IAAI;AAAA,IAChB;AAAA,EACF,WAAW,KAAK,GAAG,QAAQ;AAGzB,WACE,OAAO,KAAK,GAAG,EAAE,KAAK,KACtB,OAAO,MAAM,GAAG,EAAE,IAAI,KAAK,GAAG,EAAE,MAAM,MACnC,QAAQ,GAAG,KAAK,CAAC,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,OACpD,OAAO,MAAM,QAAQ,CAAC,CAAC,GAAG,CAAC;AAG7B,OAAG,SAAS;AAGZ,QAAI,MAAM;AAGR,aAAO,EAAE,GAAG,EAAE,EAAE,IAAI,KAAI;AACtB,WAAG,EAAE,IAAI;AACT,YAAI,OAAO,GAAG;AACZ,YAAE,EAAE;AACJ,aAAG,QAAQ,CAAC;AACZ;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,SAAK,KAAK,GAAG,QAAQ,CAAC,GAAG,EAAE,EAAE,IAAI,IAAG,IAAI;AAAA,EAC1C;AAEA,SAAO;AACT;AAOA,SAAS,UAAU,GAAG,eAAe,WAAW;AAC9C,MAAI,IAAI,EAAE,GACR,IAAI,EAAE,EAAE,KAAK,EAAE,GACf,IAAI,EAAE;AAGR,MAAI,eAAe;AACjB,QAAI,EAAE,OAAO,CAAC,KAAK,IAAI,IAAI,MAAM,EAAE,MAAM,CAAC,IAAI,OAAO,IAAI,IAAI,MAAM,QAAQ;AAAA,EAG7E,WAAW,IAAI,GAAG;AAChB,WAAO,EAAE,IAAI,KAAI,MAAM;AACvB,QAAI,OAAO;AAAA,EACb,WAAW,IAAI,GAAG;AAChB,QAAI,EAAE,IAAI,GAAG;AACX,WAAK,KAAK,GAAG,MAAM,MAAK;AAAA,IAC1B,WAAW,IAAI,GAAG;AAChB,UAAI,EAAE,MAAM,GAAG,CAAC,IAAI,MAAM,EAAE,MAAM,CAAC;AAAA,IACrC;AAAA,EACF,WAAW,IAAI,GAAG;AAChB,QAAI,EAAE,OAAO,CAAC,IAAI,MAAM,EAAE,MAAM,CAAC;AAAA,EACnC;AAEA,SAAO,EAAE,IAAI,KAAK,YAAY,MAAM,IAAI;AAC1C;AASA,EAAE,MAAM,WAAY;AAClB,MAAI,IAAI,IAAI,KAAK,YAAY,IAAI;AACjC,IAAE,IAAI;AACN,SAAO;AACT;AAQA,EAAE,MAAM,SAAU,GAAG;AACnB,MAAI,OACF,IAAI,MACJ,KAAK,EAAE,GACP,MAAM,IAAI,IAAI,EAAE,YAAY,CAAC,GAAG,GAChC,IAAI,EAAE,GACN,IAAI,EAAE,GACN,IAAI,EAAE,GACN,IAAI,EAAE;AAGR,MAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAG,QAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI;AAGxD,MAAI,KAAK,EAAG,QAAO;AAEnB,UAAQ,IAAI;AAGZ,MAAI,KAAK,EAAG,QAAO,IAAI,IAAI,QAAQ,IAAI;AAEvC,OAAK,IAAI,GAAG,WAAW,IAAI,GAAG,UAAU,IAAI;AAG5C,OAAK,IAAI,IAAI,EAAE,IAAI,KAAI;AACrB,QAAI,GAAG,CAAC,KAAK,GAAG,CAAC,EAAG,QAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,IAAI;AAAA,EACzD;AAGA,SAAO,KAAK,IAAI,IAAI,IAAI,IAAI,QAAQ,IAAI;AAC1C;AAOA,EAAE,MAAM,SAAU,GAAG;AACnB,MAAI,IAAI,MACNA,OAAM,EAAE,aACR,IAAI,EAAE,GACN,KAAK,IAAI,IAAIA,KAAI,CAAC,GAAG,GACrB,IAAI,EAAE,KAAK,EAAE,IAAI,IAAI,IACrB,KAAKA,KAAI;AAEX,MAAI,OAAO,CAAC,CAAC,MAAM,KAAK,KAAK,KAAK,QAAQ;AACxC,UAAM,MAAM,UAAU;AAAA,EACxB;AAGA,MAAI,CAAC,EAAE,CAAC,GAAG;AACT,UAAM,MAAM,WAAW;AAAA,EACzB;AAGA,MAAI,CAAC,EAAE,CAAC,GAAG;AACT,MAAE,IAAI;AACN,MAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AACd,WAAO;AAAA,EACT;AAEA,MAAI,IAAI,IAAI,GAAG,KAAK,IAClB,KAAK,EAAE,MAAM,GACb,KAAK,KAAK,EAAE,QACZ,KAAK,EAAE,QACP,IAAI,EAAE,MAAM,GAAG,EAAE,GACjB,KAAK,EAAE,QACP,IAAI,GACJ,KAAK,EAAE,IAAI,CAAC,GACZ,KAAK,GACL,IAAI,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK;AAE/B,IAAE,IAAI;AACN,MAAI,IAAI,IAAI,IAAI;AAGhB,KAAG,QAAQ,CAAC;AAGZ,SAAO,OAAO,KAAK,GAAE,KAAK,CAAC;AAE3B,KAAG;AAGD,SAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AAGvB,UAAI,OAAO,KAAK,EAAE,SAAS;AACzB,cAAM,KAAK,KAAK,IAAI;AAAA,MACtB,OAAO;AACL,aAAK,KAAK,IAAI,MAAM,GAAG,EAAE,KAAK,MAAK;AACjC,cAAI,EAAE,EAAE,KAAK,EAAE,EAAE,GAAG;AAClB,kBAAM,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,IAAI;AAC1B;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAGA,UAAI,MAAM,GAAG;AAIX,aAAK,KAAK,MAAM,KAAK,IAAI,IAAI,MAAK;AAChC,cAAI,EAAE,EAAE,EAAE,IAAI,GAAG,EAAE,GAAG;AACpB,iBAAK;AACL,mBAAO,MAAM,CAAC,EAAE,EAAE,EAAE,IAAI,GAAE,EAAE,IAAI;AAChC,cAAE,EAAE,EAAE;AACN,cAAE,EAAE,KAAK;AAAA,UACX;AACA,YAAE,EAAE,KAAK,GAAG,EAAE;AAAA,QAChB;AAEA,eAAO,CAAC,EAAE,CAAC,IAAI,GAAE,MAAM;AAAA,MACzB,OAAO;AACL;AAAA,MACF;AAAA,IACF;AAGA,OAAG,IAAI,IAAI,MAAM,IAAI,EAAE;AAGvB,QAAI,EAAE,CAAC,KAAK,IAAK,GAAE,EAAE,IAAI,EAAE,EAAE,KAAK;AAAA,QAC7B,KAAI,CAAC,EAAE,EAAE,CAAC;AAAA,EAEjB,UAAU,OAAO,MAAM,EAAE,CAAC,MAAM,cAAc;AAG9C,MAAI,CAAC,GAAG,CAAC,KAAK,MAAM,GAAG;AAGrB,OAAG,MAAM;AACT,MAAE;AACF;AAAA,EACF;AAGA,MAAI,KAAK,EAAG,OAAM,GAAG,GAAGA,KAAI,IAAI,EAAE,CAAC,MAAM,SAAS;AAElD,SAAO;AACT;AAMA,EAAE,KAAK,SAAU,GAAG;AAClB,SAAO,KAAK,IAAI,CAAC,MAAM;AACzB;AAOA,EAAE,KAAK,SAAU,GAAG;AAClB,SAAO,KAAK,IAAI,CAAC,IAAI;AACvB;AAOA,EAAE,MAAM,SAAU,GAAG;AACnB,SAAO,KAAK,IAAI,CAAC,IAAI;AACvB;AAMA,EAAE,KAAK,SAAU,GAAG;AAClB,SAAO,KAAK,IAAI,CAAC,IAAI;AACvB;AAOA,EAAE,MAAM,SAAU,GAAG;AACnB,SAAO,KAAK,IAAI,CAAC,IAAI;AACvB;AAMA,EAAE,QAAQ,EAAE,MAAM,SAAU,GAAG;AAC7B,MAAI,GAAG,GAAG,GAAG,MACX,IAAI,MACJA,OAAM,EAAE,aACR,IAAI,EAAE,GACN,KAAK,IAAI,IAAIA,KAAI,CAAC,GAAG;AAGvB,MAAI,KAAK,GAAG;AACV,MAAE,IAAI,CAAC;AACP,WAAO,EAAE,KAAK,CAAC;AAAA,EACjB;AAEA,MAAI,KAAK,EAAE,EAAE,MAAM,GACjB,KAAK,EAAE,GACP,KAAK,EAAE,GACP,KAAK,EAAE;AAGT,MAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG;AACpB,QAAI,GAAG,CAAC,GAAG;AACT,QAAE,IAAI,CAAC;AAAA,IACT,WAAW,GAAG,CAAC,GAAG;AAChB,UAAI,IAAIA,KAAI,CAAC;AAAA,IACf,OAAO;AACL,QAAE,IAAI;AAAA,IACR;AACA,WAAO;AAAA,EACT;AAGA,MAAI,IAAI,KAAK,IAAI;AAEf,QAAI,OAAO,IAAI,GAAG;AAChB,UAAI,CAAC;AACL,UAAI;AAAA,IACN,OAAO;AACL,WAAK;AACL,UAAI;AAAA,IACN;AAEA,MAAE,QAAQ;AACV,SAAK,IAAI,GAAG,MAAM,GAAE,KAAK,CAAC;AAC1B,MAAE,QAAQ;AAAA,EACZ,OAAO;AAGL,UAAM,OAAO,GAAG,SAAS,GAAG,UAAU,KAAK,IAAI;AAE/C,SAAK,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,UAAI,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG;AAClB,eAAO,GAAG,CAAC,IAAI,GAAG,CAAC;AACnB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAGA,MAAI,MAAM;AACR,QAAI;AACJ,SAAK;AACL,SAAK;AACL,MAAE,IAAI,CAAC,EAAE;AAAA,EACX;AAMA,OAAK,KAAK,IAAI,GAAG,WAAW,IAAI,GAAG,WAAW,EAAG,QAAO,MAAM,IAAG,GAAG,IAAI;AAGxE,OAAK,IAAI,GAAG,IAAI,KAAI;AAClB,QAAI,GAAG,EAAE,CAAC,IAAI,GAAG,CAAC,GAAG;AACnB,WAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,IAAG,CAAC,IAAI;AACpC,QAAE,GAAG,CAAC;AACN,SAAG,CAAC,KAAK;AAAA,IACX;AAEA,OAAG,CAAC,KAAK,GAAG,CAAC;AAAA,EACf;AAGA,SAAO,GAAG,EAAE,CAAC,MAAM,IAAI,IAAG,IAAI;AAG9B,SAAO,GAAG,CAAC,MAAM,KAAI;AACnB,OAAG,MAAM;AACT,MAAE;AAAA,EACJ;AAEA,MAAI,CAAC,GAAG,CAAC,GAAG;AAGV,MAAE,IAAI;AAGN,SAAK,CAAC,KAAK,CAAC;AAAA,EACd;AAEA,IAAE,IAAI;AACN,IAAE,IAAI;AAEN,SAAO;AACT;AAMA,EAAE,MAAM,SAAU,GAAG;AACnB,MAAI,MACF,IAAI,MACJA,OAAM,EAAE,aACR,IAAI,EAAE,GACN,KAAK,IAAI,IAAIA,KAAI,CAAC,GAAG;AAEvB,MAAI,CAAC,EAAE,EAAE,CAAC,GAAG;AACX,UAAM,MAAM,WAAW;AAAA,EACzB;AAEA,IAAE,IAAI,EAAE,IAAI;AACZ,SAAO,EAAE,IAAI,CAAC,KAAK;AACnB,IAAE,IAAI;AACN,IAAE,IAAI;AAEN,MAAI,KAAM,QAAO,IAAIA,KAAI,CAAC;AAE1B,MAAIA,KAAI;AACR,MAAIA,KAAI;AACR,EAAAA,KAAI,KAAKA,KAAI,KAAK;AAClB,MAAI,EAAE,IAAI,CAAC;AACX,EAAAA,KAAI,KAAK;AACT,EAAAA,KAAI,KAAK;AAET,SAAO,KAAK,MAAM,EAAE,MAAM,CAAC,CAAC;AAC9B;AAMA,EAAE,MAAM,WAAY;AAClB,MAAI,IAAI,IAAI,KAAK,YAAY,IAAI;AACjC,IAAE,IAAI,CAAC,EAAE;AACT,SAAO;AACT;AAMA,EAAE,OAAO,EAAE,MAAM,SAAU,GAAG;AAC5B,MAAI,GAAG,GAAG,GACR,IAAI,MACJA,OAAM,EAAE;AAEV,MAAI,IAAIA,KAAI,CAAC;AAGb,MAAI,EAAE,KAAK,EAAE,GAAG;AACd,MAAE,IAAI,CAAC,EAAE;AACT,WAAO,EAAE,MAAM,CAAC;AAAA,EAClB;AAEA,MAAI,KAAK,EAAE,GACT,KAAK,EAAE,GACP,KAAK,EAAE,GACP,KAAK,EAAE;AAGT,MAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG;AACpB,QAAI,CAAC,GAAG,CAAC,GAAG;AACV,UAAI,GAAG,CAAC,GAAG;AACT,YAAI,IAAIA,KAAI,CAAC;AAAA,MACf,OAAO;AACL,UAAE,IAAI,EAAE;AAAA,MACV;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,OAAK,GAAG,MAAM;AAId,MAAI,IAAI,KAAK,IAAI;AACf,QAAI,IAAI,GAAG;AACT,WAAK;AACL,UAAI;AAAA,IACN,OAAO;AACL,UAAI,CAAC;AACL,UAAI;AAAA,IACN;AAEA,MAAE,QAAQ;AACV,WAAO,MAAM,GAAE,KAAK,CAAC;AACrB,MAAE,QAAQ;AAAA,EACZ;AAGA,MAAI,GAAG,SAAS,GAAG,SAAS,GAAG;AAC7B,QAAI;AACJ,SAAK;AACL,SAAK;AAAA,EACP;AAEA,MAAI,GAAG;AAGP,OAAK,IAAI,GAAG,GAAG,GAAG,CAAC,KAAK,GAAI,MAAK,GAAG,EAAE,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,KAAK;AAIrE,MAAI,GAAG;AACL,OAAG,QAAQ,CAAC;AACZ,MAAE;AAAA,EACJ;AAGA,OAAK,IAAI,GAAG,QAAQ,GAAG,EAAE,CAAC,MAAM,IAAI,IAAG,IAAI;AAE3C,IAAE,IAAI;AACN,IAAE,IAAI;AAEN,SAAO;AACT;AAUA,EAAE,MAAM,SAAU,GAAG;AACnB,MAAI,IAAI,MACN,MAAM,IAAI,EAAE,YAAY,GAAG,GAC3B,IAAI,KACJ,QAAQ,IAAI;AAEd,MAAI,MAAM,CAAC,CAAC,KAAK,IAAI,CAAC,aAAa,IAAI,WAAW;AAChD,UAAM,MAAM,UAAU,UAAU;AAAA,EAClC;AAEA,MAAI,MAAO,KAAI,CAAC;AAEhB,aAAS;AACP,QAAI,IAAI,EAAG,KAAI,EAAE,MAAM,CAAC;AACxB,UAAM;AACN,QAAI,CAAC,EAAG;AACR,QAAI,EAAE,MAAM,CAAC;AAAA,EACf;AAEA,SAAO,QAAQ,IAAI,IAAI,CAAC,IAAI;AAC9B;AAUA,EAAE,OAAO,SAAU,IAAI,IAAI;AACzB,MAAI,OAAO,CAAC,CAAC,MAAM,KAAK,KAAK,KAAK,QAAQ;AACxC,UAAM,MAAM,UAAU,WAAW;AAAA,EACnC;AACA,SAAO,MAAM,IAAI,KAAK,YAAY,IAAI,GAAG,IAAI,EAAE;AACjD;AAYA,EAAE,QAAQ,SAAU,IAAI,IAAI;AAC1B,MAAI,OAAO,UAAW,MAAK;AAAA,WAClB,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,UAAU,KAAK,QAAQ;AACnD,UAAM,MAAM,UAAU;AAAA,EACxB;AACA,SAAO,MAAM,IAAI,KAAK,YAAY,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,EAAE;AAC9D;AAOA,EAAE,OAAO,WAAY;AACnB,MAAI,GAAG,GAAG,GACR,IAAI,MACJA,OAAM,EAAE,aACR,IAAI,EAAE,GACN,IAAI,EAAE,GACN,OAAO,IAAIA,KAAI,KAAK;AAGtB,MAAI,CAAC,EAAE,EAAE,CAAC,EAAG,QAAO,IAAIA,KAAI,CAAC;AAG7B,MAAI,IAAI,GAAG;AACT,UAAM,MAAM,OAAO,gBAAgB;AAAA,EACrC;AAGA,MAAI,KAAK,KAAK,CAAC,UAAU,GAAG,MAAM,IAAI,CAAC;AAIvC,MAAI,MAAM,KAAK,MAAM,IAAI,GAAG;AAC1B,QAAI,EAAE,EAAE,KAAK,EAAE;AACf,QAAI,EAAE,EAAE,SAAS,IAAI,GAAI,MAAK;AAC9B,QAAI,KAAK,KAAK,CAAC;AACf,UAAM,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI;AACtC,QAAI,IAAIA,MAAK,KAAK,IAAI,IAAI,QAAQ,IAAI,EAAE,cAAc,GAAG,MAAM,GAAG,EAAE,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;AAAA,EAC5F,OAAO;AACL,QAAI,IAAIA,KAAI,IAAI,EAAE;AAAA,EACpB;AAEA,MAAI,EAAE,KAAKA,KAAI,MAAM;AAGrB,KAAG;AACD,QAAI;AACJ,QAAI,KAAK,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;AAAA,EACjC,SAAS,EAAE,EAAE,MAAM,GAAG,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,MAAM,GAAG,CAAC,EAAE,KAAK,EAAE;AAE7D,SAAO,MAAM,IAAIA,KAAI,MAAM,KAAK,EAAE,IAAI,GAAGA,KAAI,EAAE;AACjD;AAMA,EAAE,QAAQ,EAAE,MAAM,SAAU,GAAG;AAC7B,MAAI,GACF,IAAI,MACJA,OAAM,EAAE,aACR,KAAK,EAAE,GACP,MAAM,IAAI,IAAIA,KAAI,CAAC,GAAG,GACtB,IAAI,GAAG,QACP,IAAI,GAAG,QACP,IAAI,EAAE,GACN,IAAI,EAAE;AAGR,IAAE,IAAI,EAAE,KAAK,EAAE,IAAI,IAAI;AAGvB,MAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG;AACpB,MAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AACd,WAAO;AAAA,EACT;AAGA,IAAE,IAAI,IAAI;AAGV,MAAI,IAAI,GAAG;AACT,QAAI;AACJ,SAAK;AACL,SAAK;AACL,QAAI;AACJ,QAAI;AACJ,QAAI;AAAA,EACN;AAGA,OAAK,IAAI,IAAI,MAAM,IAAI,IAAI,CAAC,GAAG,MAAM,GAAE,CAAC,IAAI;AAK5C,OAAK,IAAI,GAAG,OAAM;AAChB,QAAI;AAGJ,SAAK,IAAI,IAAI,GAAG,IAAI,KAAI;AAGtB,UAAI,EAAE,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI;AACnC,QAAE,GAAG,IAAI,IAAI;AAGb,UAAI,IAAI,KAAK;AAAA,IACf;AAEA,MAAE,CAAC,IAAI;AAAA,EACT;AAGA,MAAI,EAAG,GAAE,EAAE;AAAA,MACN,GAAE,MAAM;AAGb,OAAK,IAAI,EAAE,QAAQ,CAAC,EAAE,EAAE,CAAC,IAAI,GAAE,IAAI;AACnC,IAAE,IAAI;AAEN,SAAO;AACT;AAUA,EAAE,gBAAgB,SAAU,IAAI,IAAI;AAClC,MAAI,IAAI,MACN,IAAI,EAAE,EAAE,CAAC;AAEX,MAAI,OAAO,WAAW;AACpB,QAAI,OAAO,CAAC,CAAC,MAAM,KAAK,KAAK,KAAK,QAAQ;AACxC,YAAM,MAAM,UAAU;AAAA,IACxB;AACA,QAAI,MAAM,IAAI,EAAE,YAAY,CAAC,GAAG,EAAE,IAAI,EAAE;AACxC,WAAO,EAAE,EAAE,SAAS,KAAK,GAAE,EAAE,KAAK,CAAC;AAAA,EACrC;AAEA,SAAO,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC;AAC/B;AAaA,EAAE,UAAU,SAAU,IAAI,IAAI;AAC5B,MAAI,IAAI,MACN,IAAI,EAAE,EAAE,CAAC;AAEX,MAAI,OAAO,WAAW;AACpB,QAAI,OAAO,CAAC,CAAC,MAAM,KAAK,KAAK,KAAK,QAAQ;AACxC,YAAM,MAAM,UAAU;AAAA,IACxB;AACA,QAAI,MAAM,IAAI,EAAE,YAAY,CAAC,GAAG,KAAK,EAAE,IAAI,GAAG,EAAE;AAGhD,SAAK,KAAK,KAAK,EAAE,IAAI,GAAG,EAAE,EAAE,SAAS,KAAK,GAAE,EAAE,KAAK,CAAC;AAAA,EACtD;AAEA,SAAO,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC;AAChC;AASA,EAAE,OAAO,IAAI,4BAA4B,CAAC,IAAI,EAAE,SAAS,EAAE,WAAW,WAAY;AAChF,MAAI,IAAI,MACNA,OAAM,EAAE;AACV,SAAO,UAAU,GAAG,EAAE,KAAKA,KAAI,MAAM,EAAE,KAAKA,KAAI,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC9D;AAMA,EAAE,WAAW,WAAY;AACvB,MAAI,IAAI,CAAC,UAAU,MAAM,MAAM,IAAI;AACnC,MAAI,KAAK,YAAY,WAAW,QAAQ,CAAC,KAAK,GAAG,EAAE,SAAS,CAAC,GAAG;AAC9D,UAAM,MAAM,OAAO,sBAAsB;AAAA,EAC3C;AACA,SAAO;AACT;AAYA,EAAE,cAAc,SAAU,IAAI,IAAI;AAChC,MAAI,IAAI,MACNA,OAAM,EAAE,aACR,IAAI,EAAE,EAAE,CAAC;AAEX,MAAI,OAAO,WAAW;AACpB,QAAI,OAAO,CAAC,CAAC,MAAM,KAAK,KAAK,KAAK,QAAQ;AACxC,YAAM,MAAM,UAAU,WAAW;AAAA,IACnC;AACA,QAAI,MAAM,IAAIA,KAAI,CAAC,GAAG,IAAI,EAAE;AAC5B,WAAO,EAAE,EAAE,SAAS,KAAK,GAAE,EAAE,KAAK,CAAC;AAAA,EACrC;AAEA,SAAO,UAAU,GAAG,MAAM,EAAE,KAAK,EAAE,KAAKA,KAAI,MAAM,EAAE,KAAKA,KAAI,IAAI,CAAC,CAAC,CAAC;AACtE;AASA,EAAE,UAAU,WAAY;AACtB,MAAI,IAAI,MACNA,OAAM,EAAE;AACV,MAAIA,KAAI,WAAW,MAAM;AACvB,UAAM,MAAM,OAAO,oBAAoB;AAAA,EACzC;AACA,SAAO,UAAU,GAAG,EAAE,KAAKA,KAAI,MAAM,EAAE,KAAKA,KAAI,IAAI,IAAI;AAC1D;AAMO,IAAI,MAAM,MAAM;AAGvB,IAAO,cAAQ;", "names": ["Big"]}