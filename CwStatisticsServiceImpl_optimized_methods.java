/**
 * CwStatisticsServiceImpl 优化后的方法实现
 * 主要优化：批量查询、缓存机制、数据转换优化
 */

// 1. 优化后的主方法
@Override
public List<CwCostTrendByTypeResult> costTrendByUnitWithTypes(Date date, String unit) {
    if (date == null) {
        date = new Date();
    }
    if (unit == null || "all".equalsIgnoreCase(unit)) {
        throw new IllegalArgumentException("单位参数不能为空或all，请指定具体单位：ckc/ds/sx/jw");
    }

    String unitKey = unit.toLowerCase();
    Date firstDayOfYear = DateUtil.beginOfYear(date);
    int monthCount = DateUtil.month(date) + 1; // 0-based

    // 使用优化后的批量查询方法
    return costTrendByUnitWithTypesOptimized(unitKey, firstDayOfYear, date, monthCount);
}

/**
 * 优化后的按单位和类型获取成本趋势方法
 * 主要优化：批量查询、减少数据库访问次数、优化数据转换
 */
private List<CwCostTrendByTypeResult> costTrendByUnitWithTypesOptimized(
        String unitKey, Date firstDayOfYear, Date endDate, int monthCount) {
    
    // 1. 批量获取所有月份的数据
    BatchMonthlyDetail batchData = fetchMonthlyDetailBatch(unitKey, firstDayOfYear, endDate);
    
    // 2. 预构建类型映射表，避免重复调用getTypeKeyFromName
    Map<String, String> typeKeyMap = buildTypeKeyMap();
    
    // 3. 批量转换数据
    List<CwCostTrendByTypeResult> list = new ArrayList<>(monthCount);
    
    for (int i = 0; i < monthCount; i++) {
        Date monthDate = DateUtil.offsetMonth(firstDayOfYear, i);
        String period = DateUtil.format(monthDate, "yyyy-MM");
        
        MonthlyDetail monthlyData = batchData.getMonthlyDetail(period);
        if (monthlyData == null) {
            // 如果没有数据，创建空的结果
            monthlyData = new MonthlyDetail(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, new ArrayList<>());
        }

        CwCostTrendByTypeResult result = new CwCostTrendByTypeResult();
        result.setPeriod(period);
        result.setUnit(unitKey);
        result.setTypePoints(convertToCostTypePoints(monthlyData.rows, typeKeyMap));
        list.add(result);
    }
    
    return list;
}

/**
 * 批量获取月度明细数据
 */
private BatchMonthlyDetail fetchMonthlyDetailBatch(String unitKey, Date startDate, Date endDate) {
    Map<String, MonthlyDetail> monthlyDataMap = new HashMap<>();
    
    // 根据单位类型调用对应的批量查询方法
    Map<String, List<CwKrbRow>> monthlyRowsMap;
    switch (unitKey) {
        case "ckc":
            monthlyRowsMap = ckcZhcbService.sumByMonthRange(startDate, endDate);
            break;
        case "ds":
            monthlyRowsMap = dsZhcbService.sumByMonthRange(startDate, endDate);
            break;
        case "sx":
            monthlyRowsMap = sxZhcbService.sumByMonthRange(startDate, endDate);
            break;
        case "jw":
            monthlyRowsMap = jwZhcbService.sumByMonthRange(startDate, endDate);
            break;
        default:
            monthlyRowsMap = new HashMap<>();
            break;
    }
    
    // 批量获取处理量数据
    Map<String, BigDecimal> volumeDataMap = getVolumeDataBatch(unitKey, startDate, endDate);
    
    // 组装月度数据
    for (Map.Entry<String, List<CwKrbRow>> entry : monthlyRowsMap.entrySet()) {
        String period = entry.getKey();
        List<CwKrbRow> rows = entry.getValue();
        
        BigDecimal actual = sumActual(rows);
        BigDecimal plan = sumBudget(rows);
        BigDecimal volume = volumeDataMap.getOrDefault(period, BigDecimal.ZERO);
        
        monthlyDataMap.put(period, new MonthlyDetail(actual, plan, volume, rows));
    }
    
    return new BatchMonthlyDetail(monthlyDataMap);
}

/**
 * 批量获取处理量数据
 */
private Map<String, BigDecimal> getVolumeDataBatch(String unitKey, Date startDate, Date endDate) {
    Map<String, BigDecimal> volumeMap = new HashMap<>();
    
    // 按月遍历获取处理量数据
    Date current = DateUtil.beginOfMonth(startDate);
    Date end = DateUtil.endOfMonth(endDate);
    
    while (!current.after(end)) {
        String period = DateUtil.format(current, "yyyy-MM");
        BigDecimal volume = BigDecimal.ZERO;
        
        switch (unitKey) {
            case "ckc":
                // 采矿场处理量 = 采剥总量
                Map<String, String> cbzlData = cwCllDataService.getMonthCllData(CwCllDataName.CBZL, current);
                volume = sumStringMap(cbzlData);
                break;
            case "ds":
                // 大山厂处理量
                Map<String, String> dsData = cwCllDataService.getMonthCllData(CwCllDataName.DSCLL, current);
                volume = sumStringMap(dsData);
                break;
            case "sx":
                // 泗选厂处理量
                Map<String, String> sxData = cwCllDataService.getMonthCllData(CwCllDataName.SZCLL, current);
                volume = sumStringMap(sxData);
                break;
            case "jw":
                // 精尾厂处理量为大山厂(DSCLL)与泗选厂(SZCLL)之和
                Map<String, String> dsJwData = cwCllDataService.getMonthCllData(CwCllDataName.DSCLL, current);
                Map<String, String> sxJwData = cwCllDataService.getMonthCllData(CwCllDataName.SZCLL, current);
                volume = sumStringMap(dsJwData).add(sumStringMap(sxJwData));
                break;
        }
        
        volumeMap.put(period, volume);
        current = DateUtil.offsetMonth(current, 1);
    }
    
    return volumeMap;
}

/**
 * 预构建类型映射表
 */
private Map<String, String> buildTypeKeyMap() {
    Map<String, String> typeKeyMap = new HashMap<>();
    typeKeyMap.put("材料", CwKrbRow.CL);
    typeKeyMap.put("备件", CwKrbRow.BJ);
    typeKeyMap.put("燃料", CwKrbRow.RL);
    typeKeyMap.put("水", CwKrbRow.S);
    typeKeyMap.put("电", CwKrbRow.D);
    typeKeyMap.put("工资", CwKrbRow.GZ);
    typeKeyMap.put("工资性费用", CwKrbRow.GZXFY);
    typeKeyMap.put("制造费用", CwKrbRow.ZZFY);
    typeKeyMap.put("折旧费", CwKrbRow.ZJF);
    return typeKeyMap;
}

/**
 * 批量转换 CwKrbRow 到 CwCostTypePoint
 */
private List<CwCostTypePoint> convertToCostTypePoints(List<CwKrbRow> rows, Map<String, String> typeKeyMap) {
    if (rows == null || rows.isEmpty()) {
        return new ArrayList<>();
    }
    
    List<CwCostTypePoint> typePoints = new ArrayList<>(rows.size());
    for (CwKrbRow row : rows) {
        CwCostTypePoint point = new CwCostTypePoint();
        point.setType(typeKeyMap.getOrDefault(row.getName(), row.getName().toLowerCase()));
        point.setName(row.getName());
        point.setActual(row.getRlj() != null ? row.getRlj() : BigDecimal.ZERO);
        point.setPlan(row.getYys() != null ? row.getYys() : BigDecimal.ZERO);
        typePoints.add(point);
    }
    return typePoints;
}

/**
 * 统计实际成本合计
 */
private BigDecimal sumActual(List<CwKrbRow> rows) {
    BigDecimal total = BigDecimal.ZERO;
    if (rows != null) {
        for (CwKrbRow row : rows) {
            total = total.add(nullSafe(row.getRlj()));
        }
    }
    return total;
}

/**
 * 批量月度数据容器
 */
private static class BatchMonthlyDetail {
    private final Map<String, MonthlyDetail> monthlyDataMap;
    
    BatchMonthlyDetail(Map<String, MonthlyDetail> monthlyDataMap) {
        this.monthlyDataMap = monthlyDataMap;
    }
    
    MonthlyDetail getMonthlyDetail(String period) {
        return monthlyDataMap.get(period);
    }
}

// 2. 缓存优化版本（可选）
@Cacheable(cacheNames = "monthlyDetail", 
           key = "#unitKey + '_' + #period", 
           condition = "#period != T(cn.hutool.core.date.DateUtil).format(new java.util.Date(), 'yyyy-MM')")
public MonthlyDetail getMonthlyDetailWithCache(String unitKey, String period) {
    Date monthDate = DateUtil.parse(period, "yyyy-MM");
    return fetchMonthlyDetail(unitKey, DateUtil.endOfMonth(monthDate));
}

// 3. 各单位服务的批量查询方法示例（以采矿场为例）
@Override
public Map<String, List<CwKrbRow>> sumByMonthRange(Date startDate, Date endDate) {
    Map<String, List<CwKrbRow>> resultMap = new HashMap<>();
    
    // 1. 批量获取日期范围内的所有数据
    Date beginOfStartMonth = DateUtil.beginOfMonth(startDate);
    Date endOfEndMonth = DateUtil.endOfMonth(endDate);
    
    // 2. 批量查询日表和月表数据
    List<CwCkcDay> allDays = ckcDayService.lambdaQuery()
            .between(CwCkcDay::getRecordTime, beginOfStartMonth, endOfEndMonth)
            .list();
    List<CwCkcMonth> allMonths = ckcMonthService.lambdaQuery()
            .ge(CwCkcMonth::getRecordTime, beginOfStartMonth)
            .le(CwCkcMonth::getRecordTime, endOfEndMonth)
            .list();
    
    // 3. 按月份分组数据
    Map<String, List<CwCkcDay>> daysByMonth = allDays.stream()
            .collect(Collectors.groupingBy(day -> DateUtil.format(day.getRecordTime(), "yyyy-MM")));
    Map<String, List<CwCkcMonth>> monthsByMonth = allMonths.stream()
            .collect(Collectors.groupingBy(month -> DateUtil.format(month.getRecordTime(), "yyyy-MM")));
    
    // 4. 按月计算成本数据
    Date current = DateUtil.beginOfMonth(startDate);
    while (!current.after(DateUtil.endOfMonth(endDate))) {
        String period = DateUtil.format(current, "yyyy-MM");
        
        // 初始化结果
        Map<String, CwKrbRow> result = CwKrbRow.takeDefaultKrbRows("采矿场");
        
        // 获取当月数据
        List<CwCkcDay> monthDays = daysByMonth.getOrDefault(period, new ArrayList<>());
        List<CwCkcMonth> monthMonths = monthsByMonth.getOrDefault(period, new ArrayList<>());
        
        // 计算当月成本
        calculateMonthCost(result, monthDays, monthMonths);
        
        resultMap.put(period, new ArrayList<>(result.values()));
        current = DateUtil.offsetMonth(current, 1);
    }
    
    return resultMap;
}
