{"version": 3, "sources": ["../../.pnpm/@rys-fe+vite-plugin-theme@0.8.6_vite@5.4.14_@types+node@20.17.19_less@4.2.2_terser@5.39.0_/node_modules/@rys-fe/vite-plugin-theme/es/client.js"], "sourcesContent": ["export const globalField = '__VITE_THEME__';\nexport const styleTagId = '__VITE_PLUGIN_THEME__';\nexport const darkStyleTagId = '__VITE_PLUGIN_DARK_THEME__';\nexport const linkID = '__VITE_PLUGIN_THEME-ANTD_DARK_THEME_LINK__';\nconst colorPluginOutputFileName = __COLOR_PLUGIN_OUTPUT_FILE_NAME__;\nconst isProd = __PROD__;\nconst colorPluginOptions = __COLOR_PLUGIN_OPTIONS__;\nconst injectTo = colorPluginOptions.injectTo;\nconst debounceThemeRender = debounce(200, renderTheme);\nexport let darkCssIsReady = false;\n(() => {\n    if (!window[globalField]) {\n        window[globalField] = {\n            styleIdMap: new Map(),\n            styleRenderQueueMap: new Map(),\n        };\n    }\n    setGlobalOptions('replaceStyleVariables', replaceStyleVariables);\n    if (!getGlobalOptions('defaultOptions')) {\n        // assign defines\n        setGlobalOptions('defaultOptions', colorPluginOptions);\n    }\n})();\nexport function addCssToQueue(id, styleString) {\n    const styleIdMap = getGlobalOptions('styleIdMap');\n    if (!styleIdMap.get(id)) {\n        window[globalField].styleRenderQueueMap.set(id, styleString);\n        debounceThemeRender();\n    }\n}\nfunction renderTheme() {\n    const variables = getGlobalOptions('colorVariables');\n    if (!variables) {\n        return;\n    }\n    const styleRenderQueueMap = getGlobalOptions('styleRenderQueueMap');\n    const styleDom = getStyleDom(styleTagId);\n    let html = styleDom.innerHTML;\n    for (const [id, css] of styleRenderQueueMap.entries()) {\n        html += css;\n        window[globalField].styleRenderQueueMap.delete(id);\n        window[globalField].styleIdMap.set(id, css);\n    }\n    replaceCssColors(html, variables).then((processCss) => {\n        appendCssToDom(styleDom, processCss, injectTo);\n    });\n}\nexport async function replaceStyleVariables({ colorVariables, customCssHandler, }) {\n    setGlobalOptions('colorVariables', colorVariables);\n    const styleIdMap = getGlobalOptions('styleIdMap');\n    const styleRenderQueueMap = getGlobalOptions('styleRenderQueueMap');\n    if (!isProd) {\n        for (const [id, css] of styleIdMap.entries()) {\n            styleRenderQueueMap.set(id, css);\n        }\n        renderTheme();\n    }\n    else {\n        try {\n            const cssText = await fetchCss(colorPluginOutputFileName);\n            const styleDom = getStyleDom(styleTagId);\n            const processCss = await replaceCssColors(cssText, colorVariables, customCssHandler);\n            appendCssToDom(styleDom, processCss, injectTo);\n        }\n        catch (error) {\n            throw new Error(error);\n        }\n    }\n}\nexport async function loadDarkThemeCss() {\n    const extractCss = __ANTD_DARK_PLUGIN_EXTRACT_CSS__;\n    const isLoadLink = __ANTD_DARK_PLUGIN_LOAD_LINK__;\n    if (darkCssIsReady || !extractCss) {\n        return;\n    }\n    if (isLoadLink) {\n        const linkTag = document.getElementById(linkID);\n        if (linkTag) {\n            linkTag.removeAttribute('disabled');\n            linkTag.setAttribute('rel', 'stylesheet');\n        }\n    }\n    else {\n        const colorPluginOutputFileName = __ANTD_DARK_PLUGIN_OUTPUT_FILE_NAME__;\n        const cssText = await fetchCss(colorPluginOutputFileName);\n        const styleDom = getStyleDom(darkStyleTagId);\n        appendCssToDom(styleDom, cssText, injectTo);\n    }\n    darkCssIsReady = true;\n}\n// Used to replace css color variables. Note that the order of the two arrays must be the same\nexport async function replaceCssColors(css, colors, customCssHandler) {\n    let retCss = css;\n    const defaultOptions = getGlobalOptions('defaultOptions');\n    const colorVariables = defaultOptions ? defaultOptions.colorVariables || [] : [];\n    colorVariables.forEach(function (color, index) {\n        const reg = new RegExp(color.replace(/,/g, ',\\\\s*').replace(/\\s/g, '').replace('(', `\\\\(`).replace(')', `\\\\)`) +\n            '([\\\\da-f]{2})?(\\\\b|\\\\)|,|\\\\s)?', 'ig');\n        retCss = retCss.replace(reg, colors[index] + '$1$2').replace('$1$2', '');\n        if (customCssHandler && typeof customCssHandler === 'function') {\n            retCss = customCssHandler(retCss) || retCss;\n        }\n    });\n    return retCss;\n}\nexport function setGlobalOptions(key, value) {\n    window[globalField][key] = value;\n}\nexport function getGlobalOptions(key) {\n    return window[globalField][key];\n}\nexport function getStyleDom(id) {\n    let style = document.getElementById(id);\n    if (!style) {\n        style = document.createElement('style');\n        style.setAttribute('id', id);\n    }\n    return style;\n}\nexport async function appendCssToDom(styleDom, cssText, appendTo = 'body') {\n    styleDom.innerHTML = cssText;\n    if (appendTo === 'head') {\n        document.head.appendChild(styleDom);\n    }\n    else if (appendTo === 'body') {\n        document.body.appendChild(styleDom);\n    }\n    else if (appendTo === 'body-prepend') {\n        const firstChildren = document.body.firstChild;\n        document.body.insertBefore(styleDom, firstChildren);\n    }\n}\nfunction fetchCss(fileName) {\n    return new Promise((resolve, reject) => {\n        const append = getGlobalOptions('appended');\n        if (append) {\n            setGlobalOptions('appended', false);\n            resolve('');\n            return;\n        }\n        const xhr = new XMLHttpRequest();\n        xhr.onload = function () {\n            if (xhr.readyState === 4) {\n                if (xhr.status === 200) {\n                    resolve(xhr.responseText);\n                }\n                else {\n                    reject(xhr.status);\n                }\n            }\n        };\n        xhr.onerror = function (e) {\n            reject(e);\n        };\n        xhr.ontimeout = function (e) {\n            reject(e);\n        };\n        xhr.open('GET', fileName, true);\n        xhr.send();\n    });\n}\nfunction debounce(delay, fn) {\n    let timer;\n    return function (...args) {\n        // @ts-ignore\n        // eslint-disable-next-line @typescript-eslint/no-this-alias\n        const ctx = this;\n        clearTimeout(timer);\n        timer = setTimeout(function () {\n            fn.apply(ctx, args);\n        }, delay);\n    };\n}\n"], "mappings": ";;;AAAO,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,iBAAiB;AACvB,IAAM,SAAS;AACtB,IAAM,4BAA4B;AAClC,IAAM,SAAS;AACf,IAAM,qBAAqB;AAC3B,IAAM,WAAW,mBAAmB;AACpC,IAAM,sBAAsB,SAAS,KAAK,WAAW;AAC9C,IAAI,iBAAiB;AAAA,CAC3B,MAAM;AACH,MAAI,CAAC,OAAO,WAAW,GAAG;AACtB,WAAO,WAAW,IAAI;AAAA,MAClB,YAAY,oBAAI,IAAI;AAAA,MACpB,qBAAqB,oBAAI,IAAI;AAAA,IACjC;AAAA,EACJ;AACA,mBAAiB,yBAAyB,qBAAqB;AAC/D,MAAI,CAAC,iBAAiB,gBAAgB,GAAG;AAErC,qBAAiB,kBAAkB,kBAAkB;AAAA,EACzD;AACJ,GAAG;AACI,SAAS,cAAc,IAAI,aAAa;AAC3C,QAAM,aAAa,iBAAiB,YAAY;AAChD,MAAI,CAAC,WAAW,IAAI,EAAE,GAAG;AACrB,WAAO,WAAW,EAAE,oBAAoB,IAAI,IAAI,WAAW;AAC3D,wBAAoB;AAAA,EACxB;AACJ;AACA,SAAS,cAAc;AACnB,QAAM,YAAY,iBAAiB,gBAAgB;AACnD,MAAI,CAAC,WAAW;AACZ;AAAA,EACJ;AACA,QAAM,sBAAsB,iBAAiB,qBAAqB;AAClE,QAAM,WAAW,YAAY,UAAU;AACvC,MAAI,OAAO,SAAS;AACpB,aAAW,CAAC,IAAI,GAAG,KAAK,oBAAoB,QAAQ,GAAG;AACnD,YAAQ;AACR,WAAO,WAAW,EAAE,oBAAoB,OAAO,EAAE;AACjD,WAAO,WAAW,EAAE,WAAW,IAAI,IAAI,GAAG;AAAA,EAC9C;AACA,mBAAiB,MAAM,SAAS,EAAE,KAAK,CAAC,eAAe;AACnD,mBAAe,UAAU,YAAY,QAAQ;AAAA,EACjD,CAAC;AACL;AACA,eAAsB,sBAAsB,EAAE,gBAAgB,iBAAkB,GAAG;AAC/E,mBAAiB,kBAAkB,cAAc;AACjD,QAAM,aAAa,iBAAiB,YAAY;AAChD,QAAM,sBAAsB,iBAAiB,qBAAqB;AAClE,MAAI,CAAC,QAAQ;AACT,eAAW,CAAC,IAAI,GAAG,KAAK,WAAW,QAAQ,GAAG;AAC1C,0BAAoB,IAAI,IAAI,GAAG;AAAA,IACnC;AACA,gBAAY;AAAA,EAChB,OACK;AACD,QAAI;AACA,YAAM,UAAU,MAAM,SAAS,yBAAyB;AACxD,YAAM,WAAW,YAAY,UAAU;AACvC,YAAM,aAAa,MAAM,iBAAiB,SAAS,gBAAgB,gBAAgB;AACnF,qBAAe,UAAU,YAAY,QAAQ;AAAA,IACjD,SACO,OAAO;AACV,YAAM,IAAI,MAAM,KAAK;AAAA,IACzB;AAAA,EACJ;AACJ;AACA,eAAsB,mBAAmB;AACrC,QAAM,aAAa;AACnB,QAAM,aAAa;AACnB,MAAI,kBAAkB,CAAC,YAAY;AAC/B;AAAA,EACJ;AACA,MAAI,YAAY;AACZ,UAAM,UAAU,SAAS,eAAe,MAAM;AAC9C,QAAI,SAAS;AACT,cAAQ,gBAAgB,UAAU;AAClC,cAAQ,aAAa,OAAO,YAAY;AAAA,IAC5C;AAAA,EACJ,OACK;AACD,UAAMA,6BAA4B;AAClC,UAAM,UAAU,MAAM,SAASA,0BAAyB;AACxD,UAAM,WAAW,YAAY,cAAc;AAC3C,mBAAe,UAAU,SAAS,QAAQ;AAAA,EAC9C;AACA,mBAAiB;AACrB;AAEA,eAAsB,iBAAiB,KAAK,QAAQ,kBAAkB;AAClE,MAAI,SAAS;AACb,QAAM,iBAAiB,iBAAiB,gBAAgB;AACxD,QAAM,iBAAiB,iBAAiB,eAAe,kBAAkB,CAAC,IAAI,CAAC;AAC/E,iBAAe,QAAQ,SAAU,OAAO,OAAO;AAC3C,UAAM,MAAM,IAAI,OAAO,MAAM,QAAQ,MAAM,OAAO,EAAE,QAAQ,OAAO,EAAE,EAAE,QAAQ,KAAK,KAAK,EAAE,QAAQ,KAAK,KAAK,IACzG,kCAAkC,IAAI;AAC1C,aAAS,OAAO,QAAQ,KAAK,OAAO,KAAK,IAAI,MAAM,EAAE,QAAQ,QAAQ,EAAE;AACvE,QAAI,oBAAoB,OAAO,qBAAqB,YAAY;AAC5D,eAAS,iBAAiB,MAAM,KAAK;AAAA,IACzC;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AACO,SAAS,iBAAiB,KAAK,OAAO;AACzC,SAAO,WAAW,EAAE,GAAG,IAAI;AAC/B;AACO,SAAS,iBAAiB,KAAK;AAClC,SAAO,OAAO,WAAW,EAAE,GAAG;AAClC;AACO,SAAS,YAAY,IAAI;AAC5B,MAAI,QAAQ,SAAS,eAAe,EAAE;AACtC,MAAI,CAAC,OAAO;AACR,YAAQ,SAAS,cAAc,OAAO;AACtC,UAAM,aAAa,MAAM,EAAE;AAAA,EAC/B;AACA,SAAO;AACX;AACA,eAAsB,eAAe,UAAU,SAAS,WAAW,QAAQ;AACvE,WAAS,YAAY;AACrB,MAAI,aAAa,QAAQ;AACrB,aAAS,KAAK,YAAY,QAAQ;AAAA,EACtC,WACS,aAAa,QAAQ;AAC1B,aAAS,KAAK,YAAY,QAAQ;AAAA,EACtC,WACS,aAAa,gBAAgB;AAClC,UAAM,gBAAgB,SAAS,KAAK;AACpC,aAAS,KAAK,aAAa,UAAU,aAAa;AAAA,EACtD;AACJ;AACA,SAAS,SAAS,UAAU;AACxB,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,UAAM,SAAS,iBAAiB,UAAU;AAC1C,QAAI,QAAQ;AACR,uBAAiB,YAAY,KAAK;AAClC,cAAQ,EAAE;AACV;AAAA,IACJ;AACA,UAAM,MAAM,IAAI,eAAe;AAC/B,QAAI,SAAS,WAAY;AACrB,UAAI,IAAI,eAAe,GAAG;AACtB,YAAI,IAAI,WAAW,KAAK;AACpB,kBAAQ,IAAI,YAAY;AAAA,QAC5B,OACK;AACD,iBAAO,IAAI,MAAM;AAAA,QACrB;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,UAAU,SAAU,GAAG;AACvB,aAAO,CAAC;AAAA,IACZ;AACA,QAAI,YAAY,SAAU,GAAG;AACzB,aAAO,CAAC;AAAA,IACZ;AACA,QAAI,KAAK,OAAO,UAAU,IAAI;AAC9B,QAAI,KAAK;AAAA,EACb,CAAC;AACL;AACA,SAAS,SAAS,OAAO,IAAI;AACzB,MAAI;AACJ,SAAO,YAAa,MAAM;AAGtB,UAAM,MAAM;AACZ,iBAAa,KAAK;AAClB,YAAQ,WAAW,WAAY;AAC3B,SAAG,MAAM,KAAK,IAAI;AAAA,IACtB,GAAG,KAAK;AAAA,EACZ;AACJ;", "names": ["colorPluginOutputFileName"]}