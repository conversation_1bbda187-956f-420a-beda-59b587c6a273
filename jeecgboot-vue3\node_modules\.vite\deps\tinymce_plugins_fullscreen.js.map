{"version": 3, "sources": ["../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/fullscreen/plugin.js", "../../.pnpm/tinymce@6.6.2/node_modules/tinymce/plugins/fullscreen/index.js"], "sourcesContent": ["/**\n * TinyMCE version 6.6.2 (2023-08-09)\n */\n\n(function () {\n    'use strict';\n\n    const Cell = initial => {\n      let value = initial;\n      const get = () => {\n        return value;\n      };\n      const set = v => {\n        value = v;\n      };\n      return {\n        get,\n        set\n      };\n    };\n\n    var global$2 = tinymce.util.Tools.resolve('tinymce.PluginManager');\n\n    const get$5 = fullscreenState => ({ isFullscreen: () => fullscreenState.get() !== null });\n\n    const hasProto = (v, constructor, predicate) => {\n      var _a;\n      if (predicate(v, constructor.prototype)) {\n        return true;\n      } else {\n        return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;\n      }\n    };\n    const typeOf = x => {\n      const t = typeof x;\n      if (x === null) {\n        return 'null';\n      } else if (t === 'object' && Array.isArray(x)) {\n        return 'array';\n      } else if (t === 'object' && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {\n        return 'string';\n      } else {\n        return t;\n      }\n    };\n    const isType$1 = type => value => typeOf(value) === type;\n    const isSimpleType = type => value => typeof value === type;\n    const eq$1 = t => a => t === a;\n    const isString = isType$1('string');\n    const isArray = isType$1('array');\n    const isNull = eq$1(null);\n    const isBoolean = isSimpleType('boolean');\n    const isUndefined = eq$1(undefined);\n    const isNullable = a => a === null || a === undefined;\n    const isNonNullable = a => !isNullable(a);\n    const isFunction = isSimpleType('function');\n    const isNumber = isSimpleType('number');\n\n    const noop = () => {\n    };\n    const compose = (fa, fb) => {\n      return (...args) => {\n        return fa(fb.apply(null, args));\n      };\n    };\n    const compose1 = (fbc, fab) => a => fbc(fab(a));\n    const constant = value => {\n      return () => {\n        return value;\n      };\n    };\n    function curry(fn, ...initialArgs) {\n      return (...restArgs) => {\n        const all = initialArgs.concat(restArgs);\n        return fn.apply(null, all);\n      };\n    }\n    const never = constant(false);\n    const always = constant(true);\n\n    class Optional {\n      constructor(tag, value) {\n        this.tag = tag;\n        this.value = value;\n      }\n      static some(value) {\n        return new Optional(true, value);\n      }\n      static none() {\n        return Optional.singletonNone;\n      }\n      fold(onNone, onSome) {\n        if (this.tag) {\n          return onSome(this.value);\n        } else {\n          return onNone();\n        }\n      }\n      isSome() {\n        return this.tag;\n      }\n      isNone() {\n        return !this.tag;\n      }\n      map(mapper) {\n        if (this.tag) {\n          return Optional.some(mapper(this.value));\n        } else {\n          return Optional.none();\n        }\n      }\n      bind(binder) {\n        if (this.tag) {\n          return binder(this.value);\n        } else {\n          return Optional.none();\n        }\n      }\n      exists(predicate) {\n        return this.tag && predicate(this.value);\n      }\n      forall(predicate) {\n        return !this.tag || predicate(this.value);\n      }\n      filter(predicate) {\n        if (!this.tag || predicate(this.value)) {\n          return this;\n        } else {\n          return Optional.none();\n        }\n      }\n      getOr(replacement) {\n        return this.tag ? this.value : replacement;\n      }\n      or(replacement) {\n        return this.tag ? this : replacement;\n      }\n      getOrThunk(thunk) {\n        return this.tag ? this.value : thunk();\n      }\n      orThunk(thunk) {\n        return this.tag ? this : thunk();\n      }\n      getOrDie(message) {\n        if (!this.tag) {\n          throw new Error(message !== null && message !== void 0 ? message : 'Called getOrDie on None');\n        } else {\n          return this.value;\n        }\n      }\n      static from(value) {\n        return isNonNullable(value) ? Optional.some(value) : Optional.none();\n      }\n      getOrNull() {\n        return this.tag ? this.value : null;\n      }\n      getOrUndefined() {\n        return this.value;\n      }\n      each(worker) {\n        if (this.tag) {\n          worker(this.value);\n        }\n      }\n      toArray() {\n        return this.tag ? [this.value] : [];\n      }\n      toString() {\n        return this.tag ? `some(${ this.value })` : 'none()';\n      }\n    }\n    Optional.singletonNone = new Optional(false);\n\n    const singleton = doRevoke => {\n      const subject = Cell(Optional.none());\n      const revoke = () => subject.get().each(doRevoke);\n      const clear = () => {\n        revoke();\n        subject.set(Optional.none());\n      };\n      const isSet = () => subject.get().isSome();\n      const get = () => subject.get();\n      const set = s => {\n        revoke();\n        subject.set(Optional.some(s));\n      };\n      return {\n        clear,\n        isSet,\n        get,\n        set\n      };\n    };\n    const unbindable = () => singleton(s => s.unbind());\n    const value = () => {\n      const subject = singleton(noop);\n      const on = f => subject.get().each(f);\n      return {\n        ...subject,\n        on\n      };\n    };\n\n    const first = (fn, rate) => {\n      let timer = null;\n      const cancel = () => {\n        if (!isNull(timer)) {\n          clearTimeout(timer);\n          timer = null;\n        }\n      };\n      const throttle = (...args) => {\n        if (isNull(timer)) {\n          timer = setTimeout(() => {\n            timer = null;\n            fn.apply(null, args);\n          }, rate);\n        }\n      };\n      return {\n        cancel,\n        throttle\n      };\n    };\n\n    const nativePush = Array.prototype.push;\n    const map = (xs, f) => {\n      const len = xs.length;\n      const r = new Array(len);\n      for (let i = 0; i < len; i++) {\n        const x = xs[i];\n        r[i] = f(x, i);\n      }\n      return r;\n    };\n    const each$1 = (xs, f) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        f(x, i);\n      }\n    };\n    const filter$1 = (xs, pred) => {\n      const r = [];\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        if (pred(x, i)) {\n          r.push(x);\n        }\n      }\n      return r;\n    };\n    const findUntil = (xs, pred, until) => {\n      for (let i = 0, len = xs.length; i < len; i++) {\n        const x = xs[i];\n        if (pred(x, i)) {\n          return Optional.some(x);\n        } else if (until(x, i)) {\n          break;\n        }\n      }\n      return Optional.none();\n    };\n    const find$1 = (xs, pred) => {\n      return findUntil(xs, pred, never);\n    };\n    const flatten = xs => {\n      const r = [];\n      for (let i = 0, len = xs.length; i < len; ++i) {\n        if (!isArray(xs[i])) {\n          throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);\n        }\n        nativePush.apply(r, xs[i]);\n      }\n      return r;\n    };\n    const bind$3 = (xs, f) => flatten(map(xs, f));\n    const get$4 = (xs, i) => i >= 0 && i < xs.length ? Optional.some(xs[i]) : Optional.none();\n    const head = xs => get$4(xs, 0);\n    const findMap = (arr, f) => {\n      for (let i = 0; i < arr.length; i++) {\n        const r = f(arr[i], i);\n        if (r.isSome()) {\n          return r;\n        }\n      }\n      return Optional.none();\n    };\n\n    const keys = Object.keys;\n    const each = (obj, f) => {\n      const props = keys(obj);\n      for (let k = 0, len = props.length; k < len; k++) {\n        const i = props[k];\n        const x = obj[i];\n        f(x, i);\n      }\n    };\n\n    const contains = (str, substr, start = 0, end) => {\n      const idx = str.indexOf(substr, start);\n      if (idx !== -1) {\n        return isUndefined(end) ? true : idx + substr.length <= end;\n      } else {\n        return false;\n      }\n    };\n\n    const isSupported$1 = dom => dom.style !== undefined && isFunction(dom.style.getPropertyValue);\n\n    const fromHtml = (html, scope) => {\n      const doc = scope || document;\n      const div = doc.createElement('div');\n      div.innerHTML = html;\n      if (!div.hasChildNodes() || div.childNodes.length > 1) {\n        const message = 'HTML does not have a single root node';\n        console.error(message, html);\n        throw new Error(message);\n      }\n      return fromDom(div.childNodes[0]);\n    };\n    const fromTag = (tag, scope) => {\n      const doc = scope || document;\n      const node = doc.createElement(tag);\n      return fromDom(node);\n    };\n    const fromText = (text, scope) => {\n      const doc = scope || document;\n      const node = doc.createTextNode(text);\n      return fromDom(node);\n    };\n    const fromDom = node => {\n      if (node === null || node === undefined) {\n        throw new Error('Node cannot be null or undefined');\n      }\n      return { dom: node };\n    };\n    const fromPoint = (docElm, x, y) => Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom);\n    const SugarElement = {\n      fromHtml,\n      fromTag,\n      fromText,\n      fromDom,\n      fromPoint\n    };\n\n    typeof window !== 'undefined' ? window : Function('return this;')();\n\n    const DOCUMENT = 9;\n    const DOCUMENT_FRAGMENT = 11;\n    const ELEMENT = 1;\n    const TEXT = 3;\n\n    const type = element => element.dom.nodeType;\n    const isType = t => element => type(element) === t;\n    const isElement = isType(ELEMENT);\n    const isText = isType(TEXT);\n    const isDocument = isType(DOCUMENT);\n    const isDocumentFragment = isType(DOCUMENT_FRAGMENT);\n\n    const is = (element, selector) => {\n      const dom = element.dom;\n      if (dom.nodeType !== ELEMENT) {\n        return false;\n      } else {\n        const elem = dom;\n        if (elem.matches !== undefined) {\n          return elem.matches(selector);\n        } else if (elem.msMatchesSelector !== undefined) {\n          return elem.msMatchesSelector(selector);\n        } else if (elem.webkitMatchesSelector !== undefined) {\n          return elem.webkitMatchesSelector(selector);\n        } else if (elem.mozMatchesSelector !== undefined) {\n          return elem.mozMatchesSelector(selector);\n        } else {\n          throw new Error('Browser lacks native selectors');\n        }\n      }\n    };\n    const bypassSelector = dom => dom.nodeType !== ELEMENT && dom.nodeType !== DOCUMENT && dom.nodeType !== DOCUMENT_FRAGMENT || dom.childElementCount === 0;\n    const all$1 = (selector, scope) => {\n      const base = scope === undefined ? document : scope.dom;\n      return bypassSelector(base) ? [] : map(base.querySelectorAll(selector), SugarElement.fromDom);\n    };\n\n    const eq = (e1, e2) => e1.dom === e2.dom;\n\n    const owner = element => SugarElement.fromDom(element.dom.ownerDocument);\n    const documentOrOwner = dos => isDocument(dos) ? dos : owner(dos);\n    const parent = element => Optional.from(element.dom.parentNode).map(SugarElement.fromDom);\n    const parents = (element, isRoot) => {\n      const stop = isFunction(isRoot) ? isRoot : never;\n      let dom = element.dom;\n      const ret = [];\n      while (dom.parentNode !== null && dom.parentNode !== undefined) {\n        const rawParent = dom.parentNode;\n        const p = SugarElement.fromDom(rawParent);\n        ret.push(p);\n        if (stop(p) === true) {\n          break;\n        } else {\n          dom = rawParent;\n        }\n      }\n      return ret;\n    };\n    const siblings$2 = element => {\n      const filterSelf = elements => filter$1(elements, x => !eq(element, x));\n      return parent(element).map(children).map(filterSelf).getOr([]);\n    };\n    const children = element => map(element.dom.childNodes, SugarElement.fromDom);\n\n    const isShadowRoot = dos => isDocumentFragment(dos) && isNonNullable(dos.dom.host);\n    const supported = isFunction(Element.prototype.attachShadow) && isFunction(Node.prototype.getRootNode);\n    const isSupported = constant(supported);\n    const getRootNode = supported ? e => SugarElement.fromDom(e.dom.getRootNode()) : documentOrOwner;\n    const getShadowRoot = e => {\n      const r = getRootNode(e);\n      return isShadowRoot(r) ? Optional.some(r) : Optional.none();\n    };\n    const getShadowHost = e => SugarElement.fromDom(e.dom.host);\n    const getOriginalEventTarget = event => {\n      if (isSupported() && isNonNullable(event.target)) {\n        const el = SugarElement.fromDom(event.target);\n        if (isElement(el) && isOpenShadowHost(el)) {\n          if (event.composed && event.composedPath) {\n            const composedPath = event.composedPath();\n            if (composedPath) {\n              return head(composedPath);\n            }\n          }\n        }\n      }\n      return Optional.from(event.target);\n    };\n    const isOpenShadowHost = element => isNonNullable(element.dom.shadowRoot);\n\n    const inBody = element => {\n      const dom = isText(element) ? element.dom.parentNode : element.dom;\n      if (dom === undefined || dom === null || dom.ownerDocument === null) {\n        return false;\n      }\n      const doc = dom.ownerDocument;\n      return getShadowRoot(SugarElement.fromDom(dom)).fold(() => doc.body.contains(dom), compose1(inBody, getShadowHost));\n    };\n    const getBody = doc => {\n      const b = doc.dom.body;\n      if (b === null || b === undefined) {\n        throw new Error('Body is not available yet');\n      }\n      return SugarElement.fromDom(b);\n    };\n\n    const rawSet = (dom, key, value) => {\n      if (isString(value) || isBoolean(value) || isNumber(value)) {\n        dom.setAttribute(key, value + '');\n      } else {\n        console.error('Invalid call to Attribute.set. Key ', key, ':: Value ', value, ':: Element ', dom);\n        throw new Error('Attribute value was not simple');\n      }\n    };\n    const set = (element, key, value) => {\n      rawSet(element.dom, key, value);\n    };\n    const get$3 = (element, key) => {\n      const v = element.dom.getAttribute(key);\n      return v === null ? undefined : v;\n    };\n    const remove = (element, key) => {\n      element.dom.removeAttribute(key);\n    };\n\n    const internalSet = (dom, property, value) => {\n      if (!isString(value)) {\n        console.error('Invalid call to CSS.set. Property ', property, ':: Value ', value, ':: Element ', dom);\n        throw new Error('CSS value must be a string: ' + value);\n      }\n      if (isSupported$1(dom)) {\n        dom.style.setProperty(property, value);\n      }\n    };\n    const setAll = (element, css) => {\n      const dom = element.dom;\n      each(css, (v, k) => {\n        internalSet(dom, k, v);\n      });\n    };\n    const get$2 = (element, property) => {\n      const dom = element.dom;\n      const styles = window.getComputedStyle(dom);\n      const r = styles.getPropertyValue(property);\n      return r === '' && !inBody(element) ? getUnsafeProperty(dom, property) : r;\n    };\n    const getUnsafeProperty = (dom, property) => isSupported$1(dom) ? dom.style.getPropertyValue(property) : '';\n\n    const mkEvent = (target, x, y, stop, prevent, kill, raw) => ({\n      target,\n      x,\n      y,\n      stop,\n      prevent,\n      kill,\n      raw\n    });\n    const fromRawEvent = rawEvent => {\n      const target = SugarElement.fromDom(getOriginalEventTarget(rawEvent).getOr(rawEvent.target));\n      const stop = () => rawEvent.stopPropagation();\n      const prevent = () => rawEvent.preventDefault();\n      const kill = compose(prevent, stop);\n      return mkEvent(target, rawEvent.clientX, rawEvent.clientY, stop, prevent, kill, rawEvent);\n    };\n    const handle = (filter, handler) => rawEvent => {\n      if (filter(rawEvent)) {\n        handler(fromRawEvent(rawEvent));\n      }\n    };\n    const binder = (element, event, filter, handler, useCapture) => {\n      const wrapped = handle(filter, handler);\n      element.dom.addEventListener(event, wrapped, useCapture);\n      return { unbind: curry(unbind, element, event, wrapped, useCapture) };\n    };\n    const bind$2 = (element, event, filter, handler) => binder(element, event, filter, handler, false);\n    const unbind = (element, event, handler, useCapture) => {\n      element.dom.removeEventListener(event, handler, useCapture);\n    };\n\n    const filter = always;\n    const bind$1 = (element, event, handler) => bind$2(element, event, filter, handler);\n\n    const cached = f => {\n      let called = false;\n      let r;\n      return (...args) => {\n        if (!called) {\n          called = true;\n          r = f.apply(null, args);\n        }\n        return r;\n      };\n    };\n\n    const DeviceType = (os, browser, userAgent, mediaMatch) => {\n      const isiPad = os.isiOS() && /ipad/i.test(userAgent) === true;\n      const isiPhone = os.isiOS() && !isiPad;\n      const isMobile = os.isiOS() || os.isAndroid();\n      const isTouch = isMobile || mediaMatch('(pointer:coarse)');\n      const isTablet = isiPad || !isiPhone && isMobile && mediaMatch('(min-device-width:768px)');\n      const isPhone = isiPhone || isMobile && !isTablet;\n      const iOSwebview = browser.isSafari() && os.isiOS() && /safari/i.test(userAgent) === false;\n      const isDesktop = !isPhone && !isTablet && !iOSwebview;\n      return {\n        isiPad: constant(isiPad),\n        isiPhone: constant(isiPhone),\n        isTablet: constant(isTablet),\n        isPhone: constant(isPhone),\n        isTouch: constant(isTouch),\n        isAndroid: os.isAndroid,\n        isiOS: os.isiOS,\n        isWebView: constant(iOSwebview),\n        isDesktop: constant(isDesktop)\n      };\n    };\n\n    const firstMatch = (regexes, s) => {\n      for (let i = 0; i < regexes.length; i++) {\n        const x = regexes[i];\n        if (x.test(s)) {\n          return x;\n        }\n      }\n      return undefined;\n    };\n    const find = (regexes, agent) => {\n      const r = firstMatch(regexes, agent);\n      if (!r) {\n        return {\n          major: 0,\n          minor: 0\n        };\n      }\n      const group = i => {\n        return Number(agent.replace(r, '$' + i));\n      };\n      return nu$2(group(1), group(2));\n    };\n    const detect$3 = (versionRegexes, agent) => {\n      const cleanedAgent = String(agent).toLowerCase();\n      if (versionRegexes.length === 0) {\n        return unknown$2();\n      }\n      return find(versionRegexes, cleanedAgent);\n    };\n    const unknown$2 = () => {\n      return nu$2(0, 0);\n    };\n    const nu$2 = (major, minor) => {\n      return {\n        major,\n        minor\n      };\n    };\n    const Version = {\n      nu: nu$2,\n      detect: detect$3,\n      unknown: unknown$2\n    };\n\n    const detectBrowser$1 = (browsers, userAgentData) => {\n      return findMap(userAgentData.brands, uaBrand => {\n        const lcBrand = uaBrand.brand.toLowerCase();\n        return find$1(browsers, browser => {\n          var _a;\n          return lcBrand === ((_a = browser.brand) === null || _a === void 0 ? void 0 : _a.toLowerCase());\n        }).map(info => ({\n          current: info.name,\n          version: Version.nu(parseInt(uaBrand.version, 10), 0)\n        }));\n      });\n    };\n\n    const detect$2 = (candidates, userAgent) => {\n      const agent = String(userAgent).toLowerCase();\n      return find$1(candidates, candidate => {\n        return candidate.search(agent);\n      });\n    };\n    const detectBrowser = (browsers, userAgent) => {\n      return detect$2(browsers, userAgent).map(browser => {\n        const version = Version.detect(browser.versionRegexes, userAgent);\n        return {\n          current: browser.name,\n          version\n        };\n      });\n    };\n    const detectOs = (oses, userAgent) => {\n      return detect$2(oses, userAgent).map(os => {\n        const version = Version.detect(os.versionRegexes, userAgent);\n        return {\n          current: os.name,\n          version\n        };\n      });\n    };\n\n    const normalVersionRegex = /.*?version\\/\\ ?([0-9]+)\\.([0-9]+).*/;\n    const checkContains = target => {\n      return uastring => {\n        return contains(uastring, target);\n      };\n    };\n    const browsers = [\n      {\n        name: 'Edge',\n        versionRegexes: [/.*?edge\\/ ?([0-9]+)\\.([0-9]+)$/],\n        search: uastring => {\n          return contains(uastring, 'edge/') && contains(uastring, 'chrome') && contains(uastring, 'safari') && contains(uastring, 'applewebkit');\n        }\n      },\n      {\n        name: 'Chromium',\n        brand: 'Chromium',\n        versionRegexes: [\n          /.*?chrome\\/([0-9]+)\\.([0-9]+).*/,\n          normalVersionRegex\n        ],\n        search: uastring => {\n          return contains(uastring, 'chrome') && !contains(uastring, 'chromeframe');\n        }\n      },\n      {\n        name: 'IE',\n        versionRegexes: [\n          /.*?msie\\ ?([0-9]+)\\.([0-9]+).*/,\n          /.*?rv:([0-9]+)\\.([0-9]+).*/\n        ],\n        search: uastring => {\n          return contains(uastring, 'msie') || contains(uastring, 'trident');\n        }\n      },\n      {\n        name: 'Opera',\n        versionRegexes: [\n          normalVersionRegex,\n          /.*?opera\\/([0-9]+)\\.([0-9]+).*/\n        ],\n        search: checkContains('opera')\n      },\n      {\n        name: 'Firefox',\n        versionRegexes: [/.*?firefox\\/\\ ?([0-9]+)\\.([0-9]+).*/],\n        search: checkContains('firefox')\n      },\n      {\n        name: 'Safari',\n        versionRegexes: [\n          normalVersionRegex,\n          /.*?cpu os ([0-9]+)_([0-9]+).*/\n        ],\n        search: uastring => {\n          return (contains(uastring, 'safari') || contains(uastring, 'mobile/')) && contains(uastring, 'applewebkit');\n        }\n      }\n    ];\n    const oses = [\n      {\n        name: 'Windows',\n        search: checkContains('win'),\n        versionRegexes: [/.*?windows\\ nt\\ ?([0-9]+)\\.([0-9]+).*/]\n      },\n      {\n        name: 'iOS',\n        search: uastring => {\n          return contains(uastring, 'iphone') || contains(uastring, 'ipad');\n        },\n        versionRegexes: [\n          /.*?version\\/\\ ?([0-9]+)\\.([0-9]+).*/,\n          /.*cpu os ([0-9]+)_([0-9]+).*/,\n          /.*cpu iphone os ([0-9]+)_([0-9]+).*/\n        ]\n      },\n      {\n        name: 'Android',\n        search: checkContains('android'),\n        versionRegexes: [/.*?android\\ ?([0-9]+)\\.([0-9]+).*/]\n      },\n      {\n        name: 'macOS',\n        search: checkContains('mac os x'),\n        versionRegexes: [/.*?mac\\ os\\ x\\ ?([0-9]+)_([0-9]+).*/]\n      },\n      {\n        name: 'Linux',\n        search: checkContains('linux'),\n        versionRegexes: []\n      },\n      {\n        name: 'Solaris',\n        search: checkContains('sunos'),\n        versionRegexes: []\n      },\n      {\n        name: 'FreeBSD',\n        search: checkContains('freebsd'),\n        versionRegexes: []\n      },\n      {\n        name: 'ChromeOS',\n        search: checkContains('cros'),\n        versionRegexes: [/.*?chrome\\/([0-9]+)\\.([0-9]+).*/]\n      }\n    ];\n    const PlatformInfo = {\n      browsers: constant(browsers),\n      oses: constant(oses)\n    };\n\n    const edge = 'Edge';\n    const chromium = 'Chromium';\n    const ie = 'IE';\n    const opera = 'Opera';\n    const firefox = 'Firefox';\n    const safari = 'Safari';\n    const unknown$1 = () => {\n      return nu$1({\n        current: undefined,\n        version: Version.unknown()\n      });\n    };\n    const nu$1 = info => {\n      const current = info.current;\n      const version = info.version;\n      const isBrowser = name => () => current === name;\n      return {\n        current,\n        version,\n        isEdge: isBrowser(edge),\n        isChromium: isBrowser(chromium),\n        isIE: isBrowser(ie),\n        isOpera: isBrowser(opera),\n        isFirefox: isBrowser(firefox),\n        isSafari: isBrowser(safari)\n      };\n    };\n    const Browser = {\n      unknown: unknown$1,\n      nu: nu$1,\n      edge: constant(edge),\n      chromium: constant(chromium),\n      ie: constant(ie),\n      opera: constant(opera),\n      firefox: constant(firefox),\n      safari: constant(safari)\n    };\n\n    const windows = 'Windows';\n    const ios = 'iOS';\n    const android = 'Android';\n    const linux = 'Linux';\n    const macos = 'macOS';\n    const solaris = 'Solaris';\n    const freebsd = 'FreeBSD';\n    const chromeos = 'ChromeOS';\n    const unknown = () => {\n      return nu({\n        current: undefined,\n        version: Version.unknown()\n      });\n    };\n    const nu = info => {\n      const current = info.current;\n      const version = info.version;\n      const isOS = name => () => current === name;\n      return {\n        current,\n        version,\n        isWindows: isOS(windows),\n        isiOS: isOS(ios),\n        isAndroid: isOS(android),\n        isMacOS: isOS(macos),\n        isLinux: isOS(linux),\n        isSolaris: isOS(solaris),\n        isFreeBSD: isOS(freebsd),\n        isChromeOS: isOS(chromeos)\n      };\n    };\n    const OperatingSystem = {\n      unknown,\n      nu,\n      windows: constant(windows),\n      ios: constant(ios),\n      android: constant(android),\n      linux: constant(linux),\n      macos: constant(macos),\n      solaris: constant(solaris),\n      freebsd: constant(freebsd),\n      chromeos: constant(chromeos)\n    };\n\n    const detect$1 = (userAgent, userAgentDataOpt, mediaMatch) => {\n      const browsers = PlatformInfo.browsers();\n      const oses = PlatformInfo.oses();\n      const browser = userAgentDataOpt.bind(userAgentData => detectBrowser$1(browsers, userAgentData)).orThunk(() => detectBrowser(browsers, userAgent)).fold(Browser.unknown, Browser.nu);\n      const os = detectOs(oses, userAgent).fold(OperatingSystem.unknown, OperatingSystem.nu);\n      const deviceType = DeviceType(os, browser, userAgent, mediaMatch);\n      return {\n        browser,\n        os,\n        deviceType\n      };\n    };\n    const PlatformDetection = { detect: detect$1 };\n\n    const mediaMatch = query => window.matchMedia(query).matches;\n    let platform = cached(() => PlatformDetection.detect(navigator.userAgent, Optional.from(navigator.userAgentData), mediaMatch));\n    const detect = () => platform();\n\n    const r = (left, top) => {\n      const translate = (x, y) => r(left + x, top + y);\n      return {\n        left,\n        top,\n        translate\n      };\n    };\n    const SugarPosition = r;\n\n    const get$1 = _DOC => {\n      const doc = _DOC !== undefined ? _DOC.dom : document;\n      const x = doc.body.scrollLeft || doc.documentElement.scrollLeft;\n      const y = doc.body.scrollTop || doc.documentElement.scrollTop;\n      return SugarPosition(x, y);\n    };\n\n    const get = _win => {\n      const win = _win === undefined ? window : _win;\n      if (detect().browser.isFirefox()) {\n        return Optional.none();\n      } else {\n        return Optional.from(win.visualViewport);\n      }\n    };\n    const bounds = (x, y, width, height) => ({\n      x,\n      y,\n      width,\n      height,\n      right: x + width,\n      bottom: y + height\n    });\n    const getBounds = _win => {\n      const win = _win === undefined ? window : _win;\n      const doc = win.document;\n      const scroll = get$1(SugarElement.fromDom(doc));\n      return get(win).fold(() => {\n        const html = win.document.documentElement;\n        const width = html.clientWidth;\n        const height = html.clientHeight;\n        return bounds(scroll.left, scroll.top, width, height);\n      }, visualViewport => bounds(Math.max(visualViewport.pageLeft, scroll.left), Math.max(visualViewport.pageTop, scroll.top), visualViewport.width, visualViewport.height));\n    };\n    const bind = (name, callback, _win) => get(_win).map(visualViewport => {\n      const handler = e => callback(fromRawEvent(e));\n      visualViewport.addEventListener(name, handler);\n      return { unbind: () => visualViewport.removeEventListener(name, handler) };\n    }).getOrThunk(() => ({ unbind: noop }));\n\n    var global$1 = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils');\n\n    var global = tinymce.util.Tools.resolve('tinymce.Env');\n\n    const fireFullscreenStateChanged = (editor, state) => {\n      editor.dispatch('FullscreenStateChanged', { state });\n      editor.dispatch('ResizeEditor');\n    };\n\n    const option = name => editor => editor.options.get(name);\n    const register$2 = editor => {\n      const registerOption = editor.options.register;\n      registerOption('fullscreen_native', {\n        processor: 'boolean',\n        default: false\n      });\n    };\n    const getFullscreenNative = option('fullscreen_native');\n\n    const getFullscreenRoot = editor => {\n      const elem = SugarElement.fromDom(editor.getElement());\n      return getShadowRoot(elem).map(getShadowHost).getOrThunk(() => getBody(owner(elem)));\n    };\n    const getFullscreenElement = root => {\n      if (root.fullscreenElement !== undefined) {\n        return root.fullscreenElement;\n      } else if (root.msFullscreenElement !== undefined) {\n        return root.msFullscreenElement;\n      } else if (root.webkitFullscreenElement !== undefined) {\n        return root.webkitFullscreenElement;\n      } else {\n        return null;\n      }\n    };\n    const getFullscreenchangeEventName = () => {\n      if (document.fullscreenElement !== undefined) {\n        return 'fullscreenchange';\n      } else if (document.msFullscreenElement !== undefined) {\n        return 'MSFullscreenChange';\n      } else if (document.webkitFullscreenElement !== undefined) {\n        return 'webkitfullscreenchange';\n      } else {\n        return 'fullscreenchange';\n      }\n    };\n    const requestFullscreen = sugarElem => {\n      const elem = sugarElem.dom;\n      if (elem.requestFullscreen) {\n        elem.requestFullscreen();\n      } else if (elem.msRequestFullscreen) {\n        elem.msRequestFullscreen();\n      } else if (elem.webkitRequestFullScreen) {\n        elem.webkitRequestFullScreen();\n      }\n    };\n    const exitFullscreen = sugarDoc => {\n      const doc = sugarDoc.dom;\n      if (doc.exitFullscreen) {\n        doc.exitFullscreen();\n      } else if (doc.msExitFullscreen) {\n        doc.msExitFullscreen();\n      } else if (doc.webkitCancelFullScreen) {\n        doc.webkitCancelFullScreen();\n      }\n    };\n    const isFullscreenElement = elem => elem.dom === getFullscreenElement(owner(elem).dom);\n\n    const ancestors$1 = (scope, predicate, isRoot) => filter$1(parents(scope, isRoot), predicate);\n    const siblings$1 = (scope, predicate) => filter$1(siblings$2(scope), predicate);\n\n    const all = selector => all$1(selector);\n    const ancestors = (scope, selector, isRoot) => ancestors$1(scope, e => is(e, selector), isRoot);\n    const siblings = (scope, selector) => siblings$1(scope, e => is(e, selector));\n\n    const attr = 'data-ephox-mobile-fullscreen-style';\n    const siblingStyles = 'display:none!important;';\n    const ancestorPosition = 'position:absolute!important;';\n    const ancestorStyles = 'top:0!important;left:0!important;margin:0!important;padding:0!important;width:100%!important;height:100%!important;overflow:visible!important;';\n    const bgFallback = 'background-color:rgb(255,255,255)!important;';\n    const isAndroid = global.os.isAndroid();\n    const matchColor = editorBody => {\n      const color = get$2(editorBody, 'background-color');\n      return color !== undefined && color !== '' ? 'background-color:' + color + '!important' : bgFallback;\n    };\n    const clobberStyles = (dom, container, editorBody) => {\n      const gatherSiblings = element => {\n        return siblings(element, '*:not(.tox-silver-sink)');\n      };\n      const clobber = clobberStyle => element => {\n        const styles = get$3(element, 'style');\n        const backup = styles === undefined ? 'no-styles' : styles.trim();\n        if (backup === clobberStyle) {\n          return;\n        } else {\n          set(element, attr, backup);\n          setAll(element, dom.parseStyle(clobberStyle));\n        }\n      };\n      const ancestors$1 = ancestors(container, '*');\n      const siblings$1 = bind$3(ancestors$1, gatherSiblings);\n      const bgColor = matchColor(editorBody);\n      each$1(siblings$1, clobber(siblingStyles));\n      each$1(ancestors$1, clobber(ancestorPosition + ancestorStyles + bgColor));\n      const containerStyles = isAndroid === true ? '' : ancestorPosition;\n      clobber(containerStyles + ancestorStyles + bgColor)(container);\n    };\n    const restoreStyles = dom => {\n      const clobberedEls = all('[' + attr + ']');\n      each$1(clobberedEls, element => {\n        const restore = get$3(element, attr);\n        if (restore && restore !== 'no-styles') {\n          setAll(element, dom.parseStyle(restore));\n        } else {\n          remove(element, 'style');\n        }\n        remove(element, attr);\n      });\n    };\n\n    const DOM = global$1.DOM;\n    const getScrollPos = () => getBounds(window);\n    const setScrollPos = pos => window.scrollTo(pos.x, pos.y);\n    const viewportUpdate = get().fold(() => ({\n      bind: noop,\n      unbind: noop\n    }), visualViewport => {\n      const editorContainer = value();\n      const resizeBinder = unbindable();\n      const scrollBinder = unbindable();\n      const refreshScroll = () => {\n        document.body.scrollTop = 0;\n        document.documentElement.scrollTop = 0;\n      };\n      const refreshVisualViewport = () => {\n        window.requestAnimationFrame(() => {\n          editorContainer.on(container => setAll(container, {\n            top: visualViewport.offsetTop + 'px',\n            left: visualViewport.offsetLeft + 'px',\n            height: visualViewport.height + 'px',\n            width: visualViewport.width + 'px'\n          }));\n        });\n      };\n      const update = first(() => {\n        refreshScroll();\n        refreshVisualViewport();\n      }, 50);\n      const bind$1 = element => {\n        editorContainer.set(element);\n        update.throttle();\n        resizeBinder.set(bind('resize', update.throttle));\n        scrollBinder.set(bind('scroll', update.throttle));\n      };\n      const unbind = () => {\n        editorContainer.on(() => {\n          resizeBinder.clear();\n          scrollBinder.clear();\n        });\n        editorContainer.clear();\n      };\n      return {\n        bind: bind$1,\n        unbind\n      };\n    });\n    const toggleFullscreen = (editor, fullscreenState) => {\n      const body = document.body;\n      const documentElement = document.documentElement;\n      const editorContainer = editor.getContainer();\n      const editorContainerS = SugarElement.fromDom(editorContainer);\n      const fullscreenRoot = getFullscreenRoot(editor);\n      const fullscreenInfo = fullscreenState.get();\n      const editorBody = SugarElement.fromDom(editor.getBody());\n      const isTouch = global.deviceType.isTouch();\n      const editorContainerStyle = editorContainer.style;\n      const iframe = editor.iframeElement;\n      const iframeStyle = iframe === null || iframe === void 0 ? void 0 : iframe.style;\n      const handleClasses = handler => {\n        handler(body, 'tox-fullscreen');\n        handler(documentElement, 'tox-fullscreen');\n        handler(editorContainer, 'tox-fullscreen');\n        getShadowRoot(editorContainerS).map(root => getShadowHost(root).dom).each(host => {\n          handler(host, 'tox-fullscreen');\n          handler(host, 'tox-shadowhost');\n        });\n      };\n      const cleanup = () => {\n        if (isTouch) {\n          restoreStyles(editor.dom);\n        }\n        handleClasses(DOM.removeClass);\n        viewportUpdate.unbind();\n        Optional.from(fullscreenState.get()).each(info => info.fullscreenChangeHandler.unbind());\n      };\n      if (!fullscreenInfo) {\n        const fullscreenChangeHandler = bind$1(owner(fullscreenRoot), getFullscreenchangeEventName(), _evt => {\n          if (getFullscreenNative(editor)) {\n            if (!isFullscreenElement(fullscreenRoot) && fullscreenState.get() !== null) {\n              toggleFullscreen(editor, fullscreenState);\n            }\n          }\n        });\n        const newFullScreenInfo = {\n          scrollPos: getScrollPos(),\n          containerWidth: editorContainerStyle.width,\n          containerHeight: editorContainerStyle.height,\n          containerTop: editorContainerStyle.top,\n          containerLeft: editorContainerStyle.left,\n          iframeWidth: iframeStyle.width,\n          iframeHeight: iframeStyle.height,\n          fullscreenChangeHandler\n        };\n        if (isTouch) {\n          clobberStyles(editor.dom, editorContainerS, editorBody);\n        }\n        iframeStyle.width = iframeStyle.height = '100%';\n        editorContainerStyle.width = editorContainerStyle.height = '';\n        handleClasses(DOM.addClass);\n        viewportUpdate.bind(editorContainerS);\n        editor.on('remove', cleanup);\n        fullscreenState.set(newFullScreenInfo);\n        if (getFullscreenNative(editor)) {\n          requestFullscreen(fullscreenRoot);\n        }\n        fireFullscreenStateChanged(editor, true);\n      } else {\n        fullscreenInfo.fullscreenChangeHandler.unbind();\n        if (getFullscreenNative(editor) && isFullscreenElement(fullscreenRoot)) {\n          exitFullscreen(owner(fullscreenRoot));\n        }\n        iframeStyle.width = fullscreenInfo.iframeWidth;\n        iframeStyle.height = fullscreenInfo.iframeHeight;\n        editorContainerStyle.width = fullscreenInfo.containerWidth;\n        editorContainerStyle.height = fullscreenInfo.containerHeight;\n        editorContainerStyle.top = fullscreenInfo.containerTop;\n        editorContainerStyle.left = fullscreenInfo.containerLeft;\n        cleanup();\n        setScrollPos(fullscreenInfo.scrollPos);\n        fullscreenState.set(null);\n        fireFullscreenStateChanged(editor, false);\n        editor.off('remove', cleanup);\n      }\n    };\n\n    const register$1 = (editor, fullscreenState) => {\n      editor.addCommand('mceFullScreen', () => {\n        toggleFullscreen(editor, fullscreenState);\n      });\n    };\n\n    const makeSetupHandler = (editor, fullscreenState) => api => {\n      api.setActive(fullscreenState.get() !== null);\n      const editorEventCallback = e => api.setActive(e.state);\n      editor.on('FullscreenStateChanged', editorEventCallback);\n      return () => editor.off('FullscreenStateChanged', editorEventCallback);\n    };\n    const register = (editor, fullscreenState) => {\n      const onAction = () => editor.execCommand('mceFullScreen');\n      editor.ui.registry.addToggleMenuItem('fullscreen', {\n        text: 'Fullscreen',\n        icon: 'fullscreen',\n        shortcut: 'Meta+Shift+F',\n        onAction,\n        onSetup: makeSetupHandler(editor, fullscreenState)\n      });\n      editor.ui.registry.addToggleButton('fullscreen', {\n        tooltip: 'Fullscreen',\n        icon: 'fullscreen',\n        onAction,\n        onSetup: makeSetupHandler(editor, fullscreenState)\n      });\n    };\n\n    var Plugin = () => {\n      global$2.add('fullscreen', editor => {\n        const fullscreenState = Cell(null);\n        if (editor.inline) {\n          return get$5(fullscreenState);\n        }\n        register$2(editor);\n        register$1(editor, fullscreenState);\n        register(editor, fullscreenState);\n        editor.addShortcut('Meta+Shift+F', '', 'mceFullScreen');\n        return get$5(fullscreenState);\n      });\n    };\n\n    Plugin();\n\n})();\n", "// Exports the \"fullscreen\" plugin for usage with module loaders\n// Usage:\n//   CommonJS:\n//     require('tinymce/plugins/fullscreen')\n//   ES2015:\n//     import 'tinymce/plugins/fullscreen'\nrequire('./plugin.js');"], "mappings": ";;;;;AAAA;AAAA;AAIA,KAAC,WAAY;AACT;AAEA,YAAM,OAAO,aAAW;AACtB,YAAIA,SAAQ;AACZ,cAAMC,OAAM,MAAM;AAChB,iBAAOD;AAAA,QACT;AACA,cAAME,OAAM,OAAK;AACf,UAAAF,SAAQ;AAAA,QACV;AACA,eAAO;AAAA,UACL,KAAAC;AAAA,UACA,KAAAC;AAAA,QACF;AAAA,MACF;AAEA,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,uBAAuB;AAEjE,YAAM,QAAQ,sBAAoB,EAAE,cAAc,MAAM,gBAAgB,IAAI,MAAM,KAAK;AAEvF,YAAM,WAAW,CAAC,GAAG,aAAa,cAAc;AAC9C,YAAI;AACJ,YAAI,UAAU,GAAG,YAAY,SAAS,GAAG;AACvC,iBAAO;AAAA,QACT,OAAO;AACL,mBAAS,KAAK,EAAE,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,YAAY;AAAA,QAC7F;AAAA,MACF;AACA,YAAM,SAAS,OAAK;AAClB,cAAM,IAAI,OAAO;AACjB,YAAI,MAAM,MAAM;AACd,iBAAO;AAAA,QACT,WAAW,MAAM,YAAY,MAAM,QAAQ,CAAC,GAAG;AAC7C,iBAAO;AAAA,QACT,WAAW,MAAM,YAAY,SAAS,GAAG,QAAQ,CAAC,GAAG,UAAU,MAAM,cAAc,CAAC,CAAC,GAAG;AACtF,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,WAAW,CAAAC,UAAQ,CAAAH,WAAS,OAAOA,MAAK,MAAMG;AACpD,YAAM,eAAe,CAAAA,UAAQ,CAAAH,WAAS,OAAOA,WAAUG;AACvD,YAAM,OAAO,OAAK,OAAK,MAAM;AAC7B,YAAM,WAAW,SAAS,QAAQ;AAClC,YAAM,UAAU,SAAS,OAAO;AAChC,YAAM,SAAS,KAAK,IAAI;AACxB,YAAM,YAAY,aAAa,SAAS;AACxC,YAAM,cAAc,KAAK,MAAS;AAClC,YAAM,aAAa,OAAK,MAAM,QAAQ,MAAM;AAC5C,YAAM,gBAAgB,OAAK,CAAC,WAAW,CAAC;AACxC,YAAM,aAAa,aAAa,UAAU;AAC1C,YAAM,WAAW,aAAa,QAAQ;AAEtC,YAAM,OAAO,MAAM;AAAA,MACnB;AACA,YAAM,UAAU,CAAC,IAAI,OAAO;AAC1B,eAAO,IAAI,SAAS;AAClB,iBAAO,GAAG,GAAG,MAAM,MAAM,IAAI,CAAC;AAAA,QAChC;AAAA,MACF;AACA,YAAM,WAAW,CAAC,KAAK,QAAQ,OAAK,IAAI,IAAI,CAAC,CAAC;AAC9C,YAAM,WAAW,CAAAH,WAAS;AACxB,eAAO,MAAM;AACX,iBAAOA;AAAA,QACT;AAAA,MACF;AACA,eAAS,MAAM,OAAO,aAAa;AACjC,eAAO,IAAI,aAAa;AACtB,gBAAMI,OAAM,YAAY,OAAO,QAAQ;AACvC,iBAAO,GAAG,MAAM,MAAMA,IAAG;AAAA,QAC3B;AAAA,MACF;AACA,YAAM,QAAQ,SAAS,KAAK;AAC5B,YAAM,SAAS,SAAS,IAAI;AAAA,MAE5B,MAAM,SAAS;AAAA,QACb,YAAY,KAAKJ,QAAO;AACtB,eAAK,MAAM;AACX,eAAK,QAAQA;AAAA,QACf;AAAA,QACA,OAAO,KAAKA,QAAO;AACjB,iBAAO,IAAI,SAAS,MAAMA,MAAK;AAAA,QACjC;AAAA,QACA,OAAO,OAAO;AACZ,iBAAO,SAAS;AAAA,QAClB;AAAA,QACA,KAAK,QAAQ,QAAQ;AACnB,cAAI,KAAK,KAAK;AACZ,mBAAO,OAAO,KAAK,KAAK;AAAA,UAC1B,OAAO;AACL,mBAAO,OAAO;AAAA,UAChB;AAAA,QACF;AAAA,QACA,SAAS;AACP,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,SAAS;AACP,iBAAO,CAAC,KAAK;AAAA,QACf;AAAA,QACA,IAAI,QAAQ;AACV,cAAI,KAAK,KAAK;AACZ,mBAAO,SAAS,KAAK,OAAO,KAAK,KAAK,CAAC;AAAA,UACzC,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,KAAKK,SAAQ;AACX,cAAI,KAAK,KAAK;AACZ,mBAAOA,QAAO,KAAK,KAAK;AAAA,UAC1B,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,OAAO,WAAW;AAChB,iBAAO,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,QACzC;AAAA,QACA,OAAO,WAAW;AAChB,iBAAO,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,QAC1C;AAAA,QACA,OAAO,WAAW;AAChB,cAAI,CAAC,KAAK,OAAO,UAAU,KAAK,KAAK,GAAG;AACtC,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,SAAS,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA,MAAM,aAAa;AACjB,iBAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,QACjC;AAAA,QACA,GAAG,aAAa;AACd,iBAAO,KAAK,MAAM,OAAO;AAAA,QAC3B;AAAA,QACA,WAAW,OAAO;AAChB,iBAAO,KAAK,MAAM,KAAK,QAAQ,MAAM;AAAA,QACvC;AAAA,QACA,QAAQ,OAAO;AACb,iBAAO,KAAK,MAAM,OAAO,MAAM;AAAA,QACjC;AAAA,QACA,SAAS,SAAS;AAChB,cAAI,CAAC,KAAK,KAAK;AACb,kBAAM,IAAI,MAAM,YAAY,QAAQ,YAAY,SAAS,UAAU,yBAAyB;AAAA,UAC9F,OAAO;AACL,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAAA,QACA,OAAO,KAAKL,QAAO;AACjB,iBAAO,cAAcA,MAAK,IAAI,SAAS,KAAKA,MAAK,IAAI,SAAS,KAAK;AAAA,QACrE;AAAA,QACA,YAAY;AACV,iBAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,QACjC;AAAA,QACA,iBAAiB;AACf,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,KAAK,KAAK;AACZ,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF;AAAA,QACA,UAAU;AACR,iBAAO,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;AAAA,QACpC;AAAA,QACA,WAAW;AACT,iBAAO,KAAK,MAAM,QAAS,KAAK,KAAM,MAAM;AAAA,QAC9C;AAAA,MACF;AACA,eAAS,gBAAgB,IAAI,SAAS,KAAK;AAE3C,YAAM,YAAY,cAAY;AAC5B,cAAM,UAAU,KAAK,SAAS,KAAK,CAAC;AACpC,cAAM,SAAS,MAAM,QAAQ,IAAI,EAAE,KAAK,QAAQ;AAChD,cAAM,QAAQ,MAAM;AAClB,iBAAO;AACP,kBAAQ,IAAI,SAAS,KAAK,CAAC;AAAA,QAC7B;AACA,cAAM,QAAQ,MAAM,QAAQ,IAAI,EAAE,OAAO;AACzC,cAAMC,OAAM,MAAM,QAAQ,IAAI;AAC9B,cAAMC,OAAM,OAAK;AACf,iBAAO;AACP,kBAAQ,IAAI,SAAS,KAAK,CAAC,CAAC;AAAA,QAC9B;AACA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,KAAAD;AAAA,UACA,KAAAC;AAAA,QACF;AAAA,MACF;AACA,YAAM,aAAa,MAAM,UAAU,OAAK,EAAE,OAAO,CAAC;AAClD,YAAM,QAAQ,MAAM;AAClB,cAAM,UAAU,UAAU,IAAI;AAC9B,cAAM,KAAK,OAAK,QAAQ,IAAI,EAAE,KAAK,CAAC;AACpC,eAAO;AAAA,UACL,GAAG;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAEA,YAAM,QAAQ,CAAC,IAAI,SAAS;AAC1B,YAAI,QAAQ;AACZ,cAAM,SAAS,MAAM;AACnB,cAAI,CAAC,OAAO,KAAK,GAAG;AAClB,yBAAa,KAAK;AAClB,oBAAQ;AAAA,UACV;AAAA,QACF;AACA,cAAM,WAAW,IAAI,SAAS;AAC5B,cAAI,OAAO,KAAK,GAAG;AACjB,oBAAQ,WAAW,MAAM;AACvB,sBAAQ;AACR,iBAAG,MAAM,MAAM,IAAI;AAAA,YACrB,GAAG,IAAI;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,YAAM,aAAa,MAAM,UAAU;AACnC,YAAM,MAAM,CAAC,IAAI,MAAM;AACrB,cAAM,MAAM,GAAG;AACf,cAAMI,KAAI,IAAI,MAAM,GAAG;AACvB,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,gBAAM,IAAI,GAAG,CAAC;AACd,UAAAA,GAAE,CAAC,IAAI,EAAE,GAAG,CAAC;AAAA,QACf;AACA,eAAOA;AAAA,MACT;AACA,YAAM,SAAS,CAAC,IAAI,MAAM;AACxB,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG,CAAC;AACd,YAAE,GAAG,CAAC;AAAA,QACR;AAAA,MACF;AACA,YAAM,WAAW,CAAC,IAAI,SAAS;AAC7B,cAAMA,KAAI,CAAC;AACX,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG,CAAC;AACd,cAAI,KAAK,GAAG,CAAC,GAAG;AACd,YAAAA,GAAE,KAAK,CAAC;AAAA,UACV;AAAA,QACF;AACA,eAAOA;AAAA,MACT;AACA,YAAM,YAAY,CAAC,IAAI,MAAM,UAAU;AACrC,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,KAAK;AAC7C,gBAAM,IAAI,GAAG,CAAC;AACd,cAAI,KAAK,GAAG,CAAC,GAAG;AACd,mBAAO,SAAS,KAAK,CAAC;AAAA,UACxB,WAAW,MAAM,GAAG,CAAC,GAAG;AACtB;AAAA,UACF;AAAA,QACF;AACA,eAAO,SAAS,KAAK;AAAA,MACvB;AACA,YAAM,SAAS,CAAC,IAAI,SAAS;AAC3B,eAAO,UAAU,IAAI,MAAM,KAAK;AAAA,MAClC;AACA,YAAM,UAAU,QAAM;AACpB,cAAMA,KAAI,CAAC;AACX,iBAAS,IAAI,GAAG,MAAM,GAAG,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC7C,cAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG;AACnB,kBAAM,IAAI,MAAM,sBAAsB,IAAI,+BAA+B,EAAE;AAAA,UAC7E;AACA,qBAAW,MAAMA,IAAG,GAAG,CAAC,CAAC;AAAA,QAC3B;AACA,eAAOA;AAAA,MACT;AACA,YAAM,SAAS,CAAC,IAAI,MAAM,QAAQ,IAAI,IAAI,CAAC,CAAC;AAC5C,YAAM,QAAQ,CAAC,IAAI,MAAM,KAAK,KAAK,IAAI,GAAG,SAAS,SAAS,KAAK,GAAG,CAAC,CAAC,IAAI,SAAS,KAAK;AACxF,YAAM,OAAO,QAAM,MAAM,IAAI,CAAC;AAC9B,YAAM,UAAU,CAAC,KAAK,MAAM;AAC1B,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,gBAAMA,KAAI,EAAE,IAAI,CAAC,GAAG,CAAC;AACrB,cAAIA,GAAE,OAAO,GAAG;AACd,mBAAOA;AAAA,UACT;AAAA,QACF;AACA,eAAO,SAAS,KAAK;AAAA,MACvB;AAEA,YAAM,OAAO,OAAO;AACpB,YAAM,OAAO,CAAC,KAAK,MAAM;AACvB,cAAM,QAAQ,KAAK,GAAG;AACtB,iBAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,gBAAM,IAAI,MAAM,CAAC;AACjB,gBAAM,IAAI,IAAI,CAAC;AACf,YAAE,GAAG,CAAC;AAAA,QACR;AAAA,MACF;AAEA,YAAM,WAAW,CAAC,KAAK,QAAQ,QAAQ,GAAG,QAAQ;AAChD,cAAM,MAAM,IAAI,QAAQ,QAAQ,KAAK;AACrC,YAAI,QAAQ,IAAI;AACd,iBAAO,YAAY,GAAG,IAAI,OAAO,MAAM,OAAO,UAAU;AAAA,QAC1D,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,YAAM,gBAAgB,SAAO,IAAI,UAAU,UAAa,WAAW,IAAI,MAAM,gBAAgB;AAE7F,YAAM,WAAW,CAAC,MAAM,UAAU;AAChC,cAAM,MAAM,SAAS;AACrB,cAAM,MAAM,IAAI,cAAc,KAAK;AACnC,YAAI,YAAY;AAChB,YAAI,CAAC,IAAI,cAAc,KAAK,IAAI,WAAW,SAAS,GAAG;AACrD,gBAAM,UAAU;AAChB,kBAAQ,MAAM,SAAS,IAAI;AAC3B,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB;AACA,eAAO,QAAQ,IAAI,WAAW,CAAC,CAAC;AAAA,MAClC;AACA,YAAM,UAAU,CAAC,KAAK,UAAU;AAC9B,cAAM,MAAM,SAAS;AACrB,cAAM,OAAO,IAAI,cAAc,GAAG;AAClC,eAAO,QAAQ,IAAI;AAAA,MACrB;AACA,YAAM,WAAW,CAAC,MAAM,UAAU;AAChC,cAAM,MAAM,SAAS;AACrB,cAAM,OAAO,IAAI,eAAe,IAAI;AACpC,eAAO,QAAQ,IAAI;AAAA,MACrB;AACA,YAAM,UAAU,UAAQ;AACtB,YAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,gBAAM,IAAI,MAAM,kCAAkC;AAAA,QACpD;AACA,eAAO,EAAE,KAAK,KAAK;AAAA,MACrB;AACA,YAAM,YAAY,CAAC,QAAQ,GAAG,MAAM,SAAS,KAAK,OAAO,IAAI,iBAAiB,GAAG,CAAC,CAAC,EAAE,IAAI,OAAO;AAChG,YAAM,eAAe;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,aAAO,WAAW,cAAc,SAAS,SAAS,cAAc,EAAE;AAElE,YAAM,WAAW;AACjB,YAAM,oBAAoB;AAC1B,YAAM,UAAU;AAChB,YAAM,OAAO;AAEb,YAAM,OAAO,aAAW,QAAQ,IAAI;AACpC,YAAM,SAAS,OAAK,aAAW,KAAK,OAAO,MAAM;AACjD,YAAM,YAAY,OAAO,OAAO;AAChC,YAAM,SAAS,OAAO,IAAI;AAC1B,YAAM,aAAa,OAAO,QAAQ;AAClC,YAAM,qBAAqB,OAAO,iBAAiB;AAEnD,YAAM,KAAK,CAAC,SAAS,aAAa;AAChC,cAAM,MAAM,QAAQ;AACpB,YAAI,IAAI,aAAa,SAAS;AAC5B,iBAAO;AAAA,QACT,OAAO;AACL,gBAAM,OAAO;AACb,cAAI,KAAK,YAAY,QAAW;AAC9B,mBAAO,KAAK,QAAQ,QAAQ;AAAA,UAC9B,WAAW,KAAK,sBAAsB,QAAW;AAC/C,mBAAO,KAAK,kBAAkB,QAAQ;AAAA,UACxC,WAAW,KAAK,0BAA0B,QAAW;AACnD,mBAAO,KAAK,sBAAsB,QAAQ;AAAA,UAC5C,WAAW,KAAK,uBAAuB,QAAW;AAChD,mBAAO,KAAK,mBAAmB,QAAQ;AAAA,UACzC,OAAO;AACL,kBAAM,IAAI,MAAM,gCAAgC;AAAA,UAClD;AAAA,QACF;AAAA,MACF;AACA,YAAM,iBAAiB,SAAO,IAAI,aAAa,WAAW,IAAI,aAAa,YAAY,IAAI,aAAa,qBAAqB,IAAI,sBAAsB;AACvJ,YAAM,QAAQ,CAAC,UAAU,UAAU;AACjC,cAAM,OAAO,UAAU,SAAY,WAAW,MAAM;AACpD,eAAO,eAAe,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,iBAAiB,QAAQ,GAAG,aAAa,OAAO;AAAA,MAC9F;AAEA,YAAM,KAAK,CAAC,IAAI,OAAO,GAAG,QAAQ,GAAG;AAErC,YAAM,QAAQ,aAAW,aAAa,QAAQ,QAAQ,IAAI,aAAa;AACvE,YAAM,kBAAkB,SAAO,WAAW,GAAG,IAAI,MAAM,MAAM,GAAG;AAChE,YAAM,SAAS,aAAW,SAAS,KAAK,QAAQ,IAAI,UAAU,EAAE,IAAI,aAAa,OAAO;AACxF,YAAM,UAAU,CAAC,SAAS,WAAW;AACnC,cAAM,OAAO,WAAW,MAAM,IAAI,SAAS;AAC3C,YAAI,MAAM,QAAQ;AAClB,cAAM,MAAM,CAAC;AACb,eAAO,IAAI,eAAe,QAAQ,IAAI,eAAe,QAAW;AAC9D,gBAAM,YAAY,IAAI;AACtB,gBAAM,IAAI,aAAa,QAAQ,SAAS;AACxC,cAAI,KAAK,CAAC;AACV,cAAI,KAAK,CAAC,MAAM,MAAM;AACpB;AAAA,UACF,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,aAAa,aAAW;AAC5B,cAAM,aAAa,cAAY,SAAS,UAAU,OAAK,CAAC,GAAG,SAAS,CAAC,CAAC;AACtE,eAAO,OAAO,OAAO,EAAE,IAAI,QAAQ,EAAE,IAAI,UAAU,EAAE,MAAM,CAAC,CAAC;AAAA,MAC/D;AACA,YAAM,WAAW,aAAW,IAAI,QAAQ,IAAI,YAAY,aAAa,OAAO;AAE5E,YAAM,eAAe,SAAO,mBAAmB,GAAG,KAAK,cAAc,IAAI,IAAI,IAAI;AACjF,YAAM,YAAY,WAAW,QAAQ,UAAU,YAAY,KAAK,WAAW,KAAK,UAAU,WAAW;AACrG,YAAM,cAAc,SAAS,SAAS;AACtC,YAAM,cAAc,YAAY,OAAK,aAAa,QAAQ,EAAE,IAAI,YAAY,CAAC,IAAI;AACjF,YAAM,gBAAgB,OAAK;AACzB,cAAMA,KAAI,YAAY,CAAC;AACvB,eAAO,aAAaA,EAAC,IAAI,SAAS,KAAKA,EAAC,IAAI,SAAS,KAAK;AAAA,MAC5D;AACA,YAAM,gBAAgB,OAAK,aAAa,QAAQ,EAAE,IAAI,IAAI;AAC1D,YAAM,yBAAyB,WAAS;AACtC,YAAI,YAAY,KAAK,cAAc,MAAM,MAAM,GAAG;AAChD,gBAAM,KAAK,aAAa,QAAQ,MAAM,MAAM;AAC5C,cAAI,UAAU,EAAE,KAAK,iBAAiB,EAAE,GAAG;AACzC,gBAAI,MAAM,YAAY,MAAM,cAAc;AACxC,oBAAM,eAAe,MAAM,aAAa;AACxC,kBAAI,cAAc;AAChB,uBAAO,KAAK,YAAY;AAAA,cAC1B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,eAAO,SAAS,KAAK,MAAM,MAAM;AAAA,MACnC;AACA,YAAM,mBAAmB,aAAW,cAAc,QAAQ,IAAI,UAAU;AAExE,YAAM,SAAS,aAAW;AACxB,cAAM,MAAM,OAAO,OAAO,IAAI,QAAQ,IAAI,aAAa,QAAQ;AAC/D,YAAI,QAAQ,UAAa,QAAQ,QAAQ,IAAI,kBAAkB,MAAM;AACnE,iBAAO;AAAA,QACT;AACA,cAAM,MAAM,IAAI;AAChB,eAAO,cAAc,aAAa,QAAQ,GAAG,CAAC,EAAE,KAAK,MAAM,IAAI,KAAK,SAAS,GAAG,GAAG,SAAS,QAAQ,aAAa,CAAC;AAAA,MACpH;AACA,YAAM,UAAU,SAAO;AACrB,cAAM,IAAI,IAAI,IAAI;AAClB,YAAI,MAAM,QAAQ,MAAM,QAAW;AACjC,gBAAM,IAAI,MAAM,2BAA2B;AAAA,QAC7C;AACA,eAAO,aAAa,QAAQ,CAAC;AAAA,MAC/B;AAEA,YAAM,SAAS,CAAC,KAAK,KAAKN,WAAU;AAClC,YAAI,SAASA,MAAK,KAAK,UAAUA,MAAK,KAAK,SAASA,MAAK,GAAG;AAC1D,cAAI,aAAa,KAAKA,SAAQ,EAAE;AAAA,QAClC,OAAO;AACL,kBAAQ,MAAM,uCAAuC,KAAK,aAAaA,QAAO,eAAe,GAAG;AAChG,gBAAM,IAAI,MAAM,gCAAgC;AAAA,QAClD;AAAA,MACF;AACA,YAAM,MAAM,CAAC,SAAS,KAAKA,WAAU;AACnC,eAAO,QAAQ,KAAK,KAAKA,MAAK;AAAA,MAChC;AACA,YAAM,QAAQ,CAAC,SAAS,QAAQ;AAC9B,cAAM,IAAI,QAAQ,IAAI,aAAa,GAAG;AACtC,eAAO,MAAM,OAAO,SAAY;AAAA,MAClC;AACA,YAAM,SAAS,CAAC,SAAS,QAAQ;AAC/B,gBAAQ,IAAI,gBAAgB,GAAG;AAAA,MACjC;AAEA,YAAM,cAAc,CAAC,KAAK,UAAUA,WAAU;AAC5C,YAAI,CAAC,SAASA,MAAK,GAAG;AACpB,kBAAQ,MAAM,sCAAsC,UAAU,aAAaA,QAAO,eAAe,GAAG;AACpG,gBAAM,IAAI,MAAM,iCAAiCA,MAAK;AAAA,QACxD;AACA,YAAI,cAAc,GAAG,GAAG;AACtB,cAAI,MAAM,YAAY,UAAUA,MAAK;AAAA,QACvC;AAAA,MACF;AACA,YAAM,SAAS,CAAC,SAAS,QAAQ;AAC/B,cAAM,MAAM,QAAQ;AACpB,aAAK,KAAK,CAAC,GAAG,MAAM;AAClB,sBAAY,KAAK,GAAG,CAAC;AAAA,QACvB,CAAC;AAAA,MACH;AACA,YAAM,QAAQ,CAAC,SAAS,aAAa;AACnC,cAAM,MAAM,QAAQ;AACpB,cAAM,SAAS,OAAO,iBAAiB,GAAG;AAC1C,cAAMM,KAAI,OAAO,iBAAiB,QAAQ;AAC1C,eAAOA,OAAM,MAAM,CAAC,OAAO,OAAO,IAAI,kBAAkB,KAAK,QAAQ,IAAIA;AAAA,MAC3E;AACA,YAAM,oBAAoB,CAAC,KAAK,aAAa,cAAc,GAAG,IAAI,IAAI,MAAM,iBAAiB,QAAQ,IAAI;AAEzG,YAAM,UAAU,CAAC,QAAQ,GAAG,GAAG,MAAM,SAAS,MAAM,SAAS;AAAA,QAC3D;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,eAAe,cAAY;AAC/B,cAAM,SAAS,aAAa,QAAQ,uBAAuB,QAAQ,EAAE,MAAM,SAAS,MAAM,CAAC;AAC3F,cAAM,OAAO,MAAM,SAAS,gBAAgB;AAC5C,cAAM,UAAU,MAAM,SAAS,eAAe;AAC9C,cAAM,OAAO,QAAQ,SAAS,IAAI;AAClC,eAAO,QAAQ,QAAQ,SAAS,SAAS,SAAS,SAAS,MAAM,SAAS,MAAM,QAAQ;AAAA,MAC1F;AACA,YAAM,SAAS,CAACC,SAAQ,YAAY,cAAY;AAC9C,YAAIA,QAAO,QAAQ,GAAG;AACpB,kBAAQ,aAAa,QAAQ,CAAC;AAAA,QAChC;AAAA,MACF;AACA,YAAM,SAAS,CAAC,SAAS,OAAOA,SAAQ,SAAS,eAAe;AAC9D,cAAM,UAAU,OAAOA,SAAQ,OAAO;AACtC,gBAAQ,IAAI,iBAAiB,OAAO,SAAS,UAAU;AACvD,eAAO,EAAE,QAAQ,MAAM,QAAQ,SAAS,OAAO,SAAS,UAAU,EAAE;AAAA,MACtE;AACA,YAAM,SAAS,CAAC,SAAS,OAAOA,SAAQ,YAAY,OAAO,SAAS,OAAOA,SAAQ,SAAS,KAAK;AACjG,YAAM,SAAS,CAAC,SAAS,OAAO,SAAS,eAAe;AACtD,gBAAQ,IAAI,oBAAoB,OAAO,SAAS,UAAU;AAAA,MAC5D;AAEA,YAAM,SAAS;AACf,YAAM,SAAS,CAAC,SAAS,OAAO,YAAY,OAAO,SAAS,OAAO,QAAQ,OAAO;AAElF,YAAM,SAAS,OAAK;AAClB,YAAI,SAAS;AACb,YAAID;AACJ,eAAO,IAAI,SAAS;AAClB,cAAI,CAAC,QAAQ;AACX,qBAAS;AACT,YAAAA,KAAI,EAAE,MAAM,MAAM,IAAI;AAAA,UACxB;AACA,iBAAOA;AAAA,QACT;AAAA,MACF;AAEA,YAAM,aAAa,CAAC,IAAI,SAAS,WAAWE,gBAAe;AACzD,cAAM,SAAS,GAAG,MAAM,KAAK,QAAQ,KAAK,SAAS,MAAM;AACzD,cAAM,WAAW,GAAG,MAAM,KAAK,CAAC;AAChC,cAAM,WAAW,GAAG,MAAM,KAAK,GAAG,UAAU;AAC5C,cAAM,UAAU,YAAYA,YAAW,kBAAkB;AACzD,cAAM,WAAW,UAAU,CAAC,YAAY,YAAYA,YAAW,0BAA0B;AACzF,cAAM,UAAU,YAAY,YAAY,CAAC;AACzC,cAAM,aAAa,QAAQ,SAAS,KAAK,GAAG,MAAM,KAAK,UAAU,KAAK,SAAS,MAAM;AACrF,cAAM,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC;AAC5C,eAAO;AAAA,UACL,QAAQ,SAAS,MAAM;AAAA,UACvB,UAAU,SAAS,QAAQ;AAAA,UAC3B,UAAU,SAAS,QAAQ;AAAA,UAC3B,SAAS,SAAS,OAAO;AAAA,UACzB,SAAS,SAAS,OAAO;AAAA,UACzB,WAAW,GAAG;AAAA,UACd,OAAO,GAAG;AAAA,UACV,WAAW,SAAS,UAAU;AAAA,UAC9B,WAAW,SAAS,SAAS;AAAA,QAC/B;AAAA,MACF;AAEA,YAAM,aAAa,CAAC,SAAS,MAAM;AACjC,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,gBAAM,IAAI,QAAQ,CAAC;AACnB,cAAI,EAAE,KAAK,CAAC,GAAG;AACb,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,OAAO,CAAC,SAAS,UAAU;AAC/B,cAAMF,KAAI,WAAW,SAAS,KAAK;AACnC,YAAI,CAACA,IAAG;AACN,iBAAO;AAAA,YACL,OAAO;AAAA,YACP,OAAO;AAAA,UACT;AAAA,QACF;AACA,cAAM,QAAQ,OAAK;AACjB,iBAAO,OAAO,MAAM,QAAQA,IAAG,MAAM,CAAC,CAAC;AAAA,QACzC;AACA,eAAO,KAAK,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,MAChC;AACA,YAAM,WAAW,CAAC,gBAAgB,UAAU;AAC1C,cAAM,eAAe,OAAO,KAAK,EAAE,YAAY;AAC/C,YAAI,eAAe,WAAW,GAAG;AAC/B,iBAAO,UAAU;AAAA,QACnB;AACA,eAAO,KAAK,gBAAgB,YAAY;AAAA,MAC1C;AACA,YAAM,YAAY,MAAM;AACtB,eAAO,KAAK,GAAG,CAAC;AAAA,MAClB;AACA,YAAM,OAAO,CAAC,OAAO,UAAU;AAC7B,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,UAAU;AAAA,QACd,IAAI;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAEA,YAAM,kBAAkB,CAACG,WAAU,kBAAkB;AACnD,eAAO,QAAQ,cAAc,QAAQ,aAAW;AAC9C,gBAAM,UAAU,QAAQ,MAAM,YAAY;AAC1C,iBAAO,OAAOA,WAAU,aAAW;AACjC,gBAAI;AACJ,mBAAO,cAAc,KAAK,QAAQ,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY;AAAA,UAC/F,CAAC,EAAE,IAAI,WAAS;AAAA,YACd,SAAS,KAAK;AAAA,YACd,SAAS,QAAQ,GAAG,SAAS,QAAQ,SAAS,EAAE,GAAG,CAAC;AAAA,UACtD,EAAE;AAAA,QACJ,CAAC;AAAA,MACH;AAEA,YAAM,WAAW,CAAC,YAAY,cAAc;AAC1C,cAAM,QAAQ,OAAO,SAAS,EAAE,YAAY;AAC5C,eAAO,OAAO,YAAY,eAAa;AACrC,iBAAO,UAAU,OAAO,KAAK;AAAA,QAC/B,CAAC;AAAA,MACH;AACA,YAAM,gBAAgB,CAACA,WAAU,cAAc;AAC7C,eAAO,SAASA,WAAU,SAAS,EAAE,IAAI,aAAW;AAClD,gBAAM,UAAU,QAAQ,OAAO,QAAQ,gBAAgB,SAAS;AAChE,iBAAO;AAAA,YACL,SAAS,QAAQ;AAAA,YACjB;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,WAAW,CAACC,OAAM,cAAc;AACpC,eAAO,SAASA,OAAM,SAAS,EAAE,IAAI,QAAM;AACzC,gBAAM,UAAU,QAAQ,OAAO,GAAG,gBAAgB,SAAS;AAC3D,iBAAO;AAAA,YACL,SAAS,GAAG;AAAA,YACZ;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAEA,YAAM,qBAAqB;AAC3B,YAAM,gBAAgB,YAAU;AAC9B,eAAO,cAAY;AACjB,iBAAO,SAAS,UAAU,MAAM;AAAA,QAClC;AAAA,MACF;AACA,YAAM,WAAW;AAAA,QACf;AAAA,UACE,MAAM;AAAA,UACN,gBAAgB,CAAC,gCAAgC;AAAA,UACjD,QAAQ,cAAY;AAClB,mBAAO,SAAS,UAAU,OAAO,KAAK,SAAS,UAAU,QAAQ,KAAK,SAAS,UAAU,QAAQ,KAAK,SAAS,UAAU,aAAa;AAAA,UACxI;AAAA,QACF;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,UACP,gBAAgB;AAAA,YACd;AAAA,YACA;AAAA,UACF;AAAA,UACA,QAAQ,cAAY;AAClB,mBAAO,SAAS,UAAU,QAAQ,KAAK,CAAC,SAAS,UAAU,aAAa;AAAA,UAC1E;AAAA,QACF;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,gBAAgB;AAAA,YACd;AAAA,YACA;AAAA,UACF;AAAA,UACA,QAAQ,cAAY;AAClB,mBAAO,SAAS,UAAU,MAAM,KAAK,SAAS,UAAU,SAAS;AAAA,UACnE;AAAA,QACF;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,gBAAgB;AAAA,YACd;AAAA,YACA;AAAA,UACF;AAAA,UACA,QAAQ,cAAc,OAAO;AAAA,QAC/B;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,gBAAgB,CAAC,qCAAqC;AAAA,UACtD,QAAQ,cAAc,SAAS;AAAA,QACjC;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,gBAAgB;AAAA,YACd;AAAA,YACA;AAAA,UACF;AAAA,UACA,QAAQ,cAAY;AAClB,oBAAQ,SAAS,UAAU,QAAQ,KAAK,SAAS,UAAU,SAAS,MAAM,SAAS,UAAU,aAAa;AAAA,UAC5G;AAAA,QACF;AAAA,MACF;AACA,YAAM,OAAO;AAAA,QACX;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc,KAAK;AAAA,UAC3B,gBAAgB,CAAC,uCAAuC;AAAA,QAC1D;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAY;AAClB,mBAAO,SAAS,UAAU,QAAQ,KAAK,SAAS,UAAU,MAAM;AAAA,UAClE;AAAA,UACA,gBAAgB;AAAA,YACd;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc,SAAS;AAAA,UAC/B,gBAAgB,CAAC,mCAAmC;AAAA,QACtD;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc,UAAU;AAAA,UAChC,gBAAgB,CAAC,qCAAqC;AAAA,QACxD;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc,OAAO;AAAA,UAC7B,gBAAgB,CAAC;AAAA,QACnB;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc,OAAO;AAAA,UAC7B,gBAAgB,CAAC;AAAA,QACnB;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc,SAAS;AAAA,UAC/B,gBAAgB,CAAC;AAAA,QACnB;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,QAAQ,cAAc,MAAM;AAAA,UAC5B,gBAAgB,CAAC,iCAAiC;AAAA,QACpD;AAAA,MACF;AACA,YAAM,eAAe;AAAA,QACnB,UAAU,SAAS,QAAQ;AAAA,QAC3B,MAAM,SAAS,IAAI;AAAA,MACrB;AAEA,YAAM,OAAO;AACb,YAAM,WAAW;AACjB,YAAM,KAAK;AACX,YAAM,QAAQ;AACd,YAAM,UAAU;AAChB,YAAM,SAAS;AACf,YAAM,YAAY,MAAM;AACtB,eAAO,KAAK;AAAA,UACV,SAAS;AAAA,UACT,SAAS,QAAQ,QAAQ;AAAA,QAC3B,CAAC;AAAA,MACH;AACA,YAAM,OAAO,UAAQ;AACnB,cAAM,UAAU,KAAK;AACrB,cAAM,UAAU,KAAK;AACrB,cAAM,YAAY,UAAQ,MAAM,YAAY;AAC5C,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,QAAQ,UAAU,IAAI;AAAA,UACtB,YAAY,UAAU,QAAQ;AAAA,UAC9B,MAAM,UAAU,EAAE;AAAA,UAClB,SAAS,UAAU,KAAK;AAAA,UACxB,WAAW,UAAU,OAAO;AAAA,UAC5B,UAAU,UAAU,MAAM;AAAA,QAC5B;AAAA,MACF;AACA,YAAM,UAAU;AAAA,QACd,SAAS;AAAA,QACT,IAAI;AAAA,QACJ,MAAM,SAAS,IAAI;AAAA,QACnB,UAAU,SAAS,QAAQ;AAAA,QAC3B,IAAI,SAAS,EAAE;AAAA,QACf,OAAO,SAAS,KAAK;AAAA,QACrB,SAAS,SAAS,OAAO;AAAA,QACzB,QAAQ,SAAS,MAAM;AAAA,MACzB;AAEA,YAAM,UAAU;AAChB,YAAM,MAAM;AACZ,YAAM,UAAU;AAChB,YAAM,QAAQ;AACd,YAAM,QAAQ;AACd,YAAM,UAAU;AAChB,YAAM,UAAU;AAChB,YAAM,WAAW;AACjB,YAAM,UAAU,MAAM;AACpB,eAAO,GAAG;AAAA,UACR,SAAS;AAAA,UACT,SAAS,QAAQ,QAAQ;AAAA,QAC3B,CAAC;AAAA,MACH;AACA,YAAM,KAAK,UAAQ;AACjB,cAAM,UAAU,KAAK;AACrB,cAAM,UAAU,KAAK;AACrB,cAAM,OAAO,UAAQ,MAAM,YAAY;AACvC,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,WAAW,KAAK,OAAO;AAAA,UACvB,OAAO,KAAK,GAAG;AAAA,UACf,WAAW,KAAK,OAAO;AAAA,UACvB,SAAS,KAAK,KAAK;AAAA,UACnB,SAAS,KAAK,KAAK;AAAA,UACnB,WAAW,KAAK,OAAO;AAAA,UACvB,WAAW,KAAK,OAAO;AAAA,UACvB,YAAY,KAAK,QAAQ;AAAA,QAC3B;AAAA,MACF;AACA,YAAM,kBAAkB;AAAA,QACtB;AAAA,QACA;AAAA,QACA,SAAS,SAAS,OAAO;AAAA,QACzB,KAAK,SAAS,GAAG;AAAA,QACjB,SAAS,SAAS,OAAO;AAAA,QACzB,OAAO,SAAS,KAAK;AAAA,QACrB,OAAO,SAAS,KAAK;AAAA,QACrB,SAAS,SAAS,OAAO;AAAA,QACzB,SAAS,SAAS,OAAO;AAAA,QACzB,UAAU,SAAS,QAAQ;AAAA,MAC7B;AAEA,YAAM,WAAW,CAAC,WAAW,kBAAkBF,gBAAe;AAC5D,cAAMC,YAAW,aAAa,SAAS;AACvC,cAAMC,QAAO,aAAa,KAAK;AAC/B,cAAM,UAAU,iBAAiB,KAAK,mBAAiB,gBAAgBD,WAAU,aAAa,CAAC,EAAE,QAAQ,MAAM,cAAcA,WAAU,SAAS,CAAC,EAAE,KAAK,QAAQ,SAAS,QAAQ,EAAE;AACnL,cAAM,KAAK,SAASC,OAAM,SAAS,EAAE,KAAK,gBAAgB,SAAS,gBAAgB,EAAE;AACrF,cAAM,aAAa,WAAW,IAAI,SAAS,WAAWF,WAAU;AAChE,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,oBAAoB,EAAE,QAAQ,SAAS;AAE7C,YAAM,aAAa,WAAS,OAAO,WAAW,KAAK,EAAE;AACrD,UAAI,WAAW,OAAO,MAAM,kBAAkB,OAAO,UAAU,WAAW,SAAS,KAAK,UAAU,aAAa,GAAG,UAAU,CAAC;AAC7H,YAAM,SAAS,MAAM,SAAS;AAE9B,YAAM,IAAI,CAAC,MAAM,QAAQ;AACvB,cAAM,YAAY,CAAC,GAAG,MAAM,EAAE,OAAO,GAAG,MAAM,CAAC;AAC/C,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,gBAAgB;AAEtB,YAAM,QAAQ,UAAQ;AACpB,cAAM,MAAM,SAAS,SAAY,KAAK,MAAM;AAC5C,cAAM,IAAI,IAAI,KAAK,cAAc,IAAI,gBAAgB;AACrD,cAAM,IAAI,IAAI,KAAK,aAAa,IAAI,gBAAgB;AACpD,eAAO,cAAc,GAAG,CAAC;AAAA,MAC3B;AAEA,YAAM,MAAM,UAAQ;AAClB,cAAM,MAAM,SAAS,SAAY,SAAS;AAC1C,YAAI,OAAO,EAAE,QAAQ,UAAU,GAAG;AAChC,iBAAO,SAAS,KAAK;AAAA,QACvB,OAAO;AACL,iBAAO,SAAS,KAAK,IAAI,cAAc;AAAA,QACzC;AAAA,MACF;AACA,YAAM,SAAS,CAAC,GAAG,GAAG,OAAO,YAAY;AAAA,QACvC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,OAAO,IAAI;AAAA,QACX,QAAQ,IAAI;AAAA,MACd;AACA,YAAM,YAAY,UAAQ;AACxB,cAAM,MAAM,SAAS,SAAY,SAAS;AAC1C,cAAM,MAAM,IAAI;AAChB,cAAM,SAAS,MAAM,aAAa,QAAQ,GAAG,CAAC;AAC9C,eAAO,IAAI,GAAG,EAAE,KAAK,MAAM;AACzB,gBAAM,OAAO,IAAI,SAAS;AAC1B,gBAAM,QAAQ,KAAK;AACnB,gBAAM,SAAS,KAAK;AACpB,iBAAO,OAAO,OAAO,MAAM,OAAO,KAAK,OAAO,MAAM;AAAA,QACtD,GAAG,oBAAkB,OAAO,KAAK,IAAI,eAAe,UAAU,OAAO,IAAI,GAAG,KAAK,IAAI,eAAe,SAAS,OAAO,GAAG,GAAG,eAAe,OAAO,eAAe,MAAM,CAAC;AAAA,MACxK;AACA,YAAM,OAAO,CAAC,MAAM,UAAU,SAAS,IAAI,IAAI,EAAE,IAAI,oBAAkB;AACrE,cAAM,UAAU,OAAK,SAAS,aAAa,CAAC,CAAC;AAC7C,uBAAe,iBAAiB,MAAM,OAAO;AAC7C,eAAO,EAAE,QAAQ,MAAM,eAAe,oBAAoB,MAAM,OAAO,EAAE;AAAA,MAC3E,CAAC,EAAE,WAAW,OAAO,EAAE,QAAQ,KAAK,EAAE;AAEtC,UAAI,WAAW,QAAQ,KAAK,MAAM,QAAQ,sBAAsB;AAEhE,UAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,aAAa;AAErD,YAAM,6BAA6B,CAAC,QAAQ,UAAU;AACpD,eAAO,SAAS,0BAA0B,EAAE,MAAM,CAAC;AACnD,eAAO,SAAS,cAAc;AAAA,MAChC;AAEA,YAAM,SAAS,UAAQ,YAAU,OAAO,QAAQ,IAAI,IAAI;AACxD,YAAM,aAAa,YAAU;AAC3B,cAAM,iBAAiB,OAAO,QAAQ;AACtC,uBAAe,qBAAqB;AAAA,UAClC,WAAW;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AACA,YAAM,sBAAsB,OAAO,mBAAmB;AAEtD,YAAM,oBAAoB,YAAU;AAClC,cAAM,OAAO,aAAa,QAAQ,OAAO,WAAW,CAAC;AACrD,eAAO,cAAc,IAAI,EAAE,IAAI,aAAa,EAAE,WAAW,MAAM,QAAQ,MAAM,IAAI,CAAC,CAAC;AAAA,MACrF;AACA,YAAM,uBAAuB,UAAQ;AACnC,YAAI,KAAK,sBAAsB,QAAW;AACxC,iBAAO,KAAK;AAAA,QACd,WAAW,KAAK,wBAAwB,QAAW;AACjD,iBAAO,KAAK;AAAA,QACd,WAAW,KAAK,4BAA4B,QAAW;AACrD,iBAAO,KAAK;AAAA,QACd,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,+BAA+B,MAAM;AACzC,YAAI,SAAS,sBAAsB,QAAW;AAC5C,iBAAO;AAAA,QACT,WAAW,SAAS,wBAAwB,QAAW;AACrD,iBAAO;AAAA,QACT,WAAW,SAAS,4BAA4B,QAAW;AACzD,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,oBAAoB,eAAa;AACrC,cAAM,OAAO,UAAU;AACvB,YAAI,KAAK,mBAAmB;AAC1B,eAAK,kBAAkB;AAAA,QACzB,WAAW,KAAK,qBAAqB;AACnC,eAAK,oBAAoB;AAAA,QAC3B,WAAW,KAAK,yBAAyB;AACvC,eAAK,wBAAwB;AAAA,QAC/B;AAAA,MACF;AACA,YAAM,iBAAiB,cAAY;AACjC,cAAM,MAAM,SAAS;AACrB,YAAI,IAAI,gBAAgB;AACtB,cAAI,eAAe;AAAA,QACrB,WAAW,IAAI,kBAAkB;AAC/B,cAAI,iBAAiB;AAAA,QACvB,WAAW,IAAI,wBAAwB;AACrC,cAAI,uBAAuB;AAAA,QAC7B;AAAA,MACF;AACA,YAAM,sBAAsB,UAAQ,KAAK,QAAQ,qBAAqB,MAAM,IAAI,EAAE,GAAG;AAErF,YAAM,cAAc,CAAC,OAAO,WAAW,WAAW,SAAS,QAAQ,OAAO,MAAM,GAAG,SAAS;AAC5F,YAAM,aAAa,CAAC,OAAO,cAAc,SAAS,WAAW,KAAK,GAAG,SAAS;AAE9E,YAAM,MAAM,cAAY,MAAM,QAAQ;AACtC,YAAM,YAAY,CAAC,OAAO,UAAU,WAAW,YAAY,OAAO,OAAK,GAAG,GAAG,QAAQ,GAAG,MAAM;AAC9F,YAAM,WAAW,CAAC,OAAO,aAAa,WAAW,OAAO,OAAK,GAAG,GAAG,QAAQ,CAAC;AAE5E,YAAM,OAAO;AACb,YAAM,gBAAgB;AACtB,YAAM,mBAAmB;AACzB,YAAM,iBAAiB;AACvB,YAAM,aAAa;AACnB,YAAM,YAAY,OAAO,GAAG,UAAU;AACtC,YAAM,aAAa,gBAAc;AAC/B,cAAM,QAAQ,MAAM,YAAY,kBAAkB;AAClD,eAAO,UAAU,UAAa,UAAU,KAAK,sBAAsB,QAAQ,eAAe;AAAA,MAC5F;AACA,YAAM,gBAAgB,CAAC,KAAK,WAAW,eAAe;AACpD,cAAM,iBAAiB,aAAW;AAChC,iBAAO,SAAS,SAAS,yBAAyB;AAAA,QACpD;AACA,cAAM,UAAU,kBAAgB,aAAW;AACzC,gBAAM,SAAS,MAAM,SAAS,OAAO;AACrC,gBAAM,SAAS,WAAW,SAAY,cAAc,OAAO,KAAK;AAChE,cAAI,WAAW,cAAc;AAC3B;AAAA,UACF,OAAO;AACL,gBAAI,SAAS,MAAM,MAAM;AACzB,mBAAO,SAAS,IAAI,WAAW,YAAY,CAAC;AAAA,UAC9C;AAAA,QACF;AACA,cAAMG,eAAc,UAAU,WAAW,GAAG;AAC5C,cAAMC,cAAa,OAAOD,cAAa,cAAc;AACrD,cAAM,UAAU,WAAW,UAAU;AACrC,eAAOC,aAAY,QAAQ,aAAa,CAAC;AACzC,eAAOD,cAAa,QAAQ,mBAAmB,iBAAiB,OAAO,CAAC;AACxE,cAAM,kBAAkB,cAAc,OAAO,KAAK;AAClD,gBAAQ,kBAAkB,iBAAiB,OAAO,EAAE,SAAS;AAAA,MAC/D;AACA,YAAM,gBAAgB,SAAO;AAC3B,cAAM,eAAe,IAAI,MAAM,OAAO,GAAG;AACzC,eAAO,cAAc,aAAW;AAC9B,gBAAM,UAAU,MAAM,SAAS,IAAI;AACnC,cAAI,WAAW,YAAY,aAAa;AACtC,mBAAO,SAAS,IAAI,WAAW,OAAO,CAAC;AAAA,UACzC,OAAO;AACL,mBAAO,SAAS,OAAO;AAAA,UACzB;AACA,iBAAO,SAAS,IAAI;AAAA,QACtB,CAAC;AAAA,MACH;AAEA,YAAM,MAAM,SAAS;AACrB,YAAM,eAAe,MAAM,UAAU,MAAM;AAC3C,YAAM,eAAe,SAAO,OAAO,SAAS,IAAI,GAAG,IAAI,CAAC;AACxD,YAAM,iBAAiB,IAAI,EAAE,KAAK,OAAO;AAAA,QACvC,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,IAAI,oBAAkB;AACpB,cAAM,kBAAkB,MAAM;AAC9B,cAAM,eAAe,WAAW;AAChC,cAAM,eAAe,WAAW;AAChC,cAAM,gBAAgB,MAAM;AAC1B,mBAAS,KAAK,YAAY;AAC1B,mBAAS,gBAAgB,YAAY;AAAA,QACvC;AACA,cAAM,wBAAwB,MAAM;AAClC,iBAAO,sBAAsB,MAAM;AACjC,4BAAgB,GAAG,eAAa,OAAO,WAAW;AAAA,cAChD,KAAK,eAAe,YAAY;AAAA,cAChC,MAAM,eAAe,aAAa;AAAA,cAClC,QAAQ,eAAe,SAAS;AAAA,cAChC,OAAO,eAAe,QAAQ;AAAA,YAChC,CAAC,CAAC;AAAA,UACJ,CAAC;AAAA,QACH;AACA,cAAM,SAAS,MAAM,MAAM;AACzB,wBAAc;AACd,gCAAsB;AAAA,QACxB,GAAG,EAAE;AACL,cAAME,UAAS,aAAW;AACxB,0BAAgB,IAAI,OAAO;AAC3B,iBAAO,SAAS;AAChB,uBAAa,IAAI,KAAK,UAAU,OAAO,QAAQ,CAAC;AAChD,uBAAa,IAAI,KAAK,UAAU,OAAO,QAAQ,CAAC;AAAA,QAClD;AACA,cAAMC,UAAS,MAAM;AACnB,0BAAgB,GAAG,MAAM;AACvB,yBAAa,MAAM;AACnB,yBAAa,MAAM;AAAA,UACrB,CAAC;AACD,0BAAgB,MAAM;AAAA,QACxB;AACA,eAAO;AAAA,UACL,MAAMD;AAAA,UACN,QAAAC;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,mBAAmB,CAAC,QAAQ,oBAAoB;AACpD,cAAM,OAAO,SAAS;AACtB,cAAM,kBAAkB,SAAS;AACjC,cAAM,kBAAkB,OAAO,aAAa;AAC5C,cAAM,mBAAmB,aAAa,QAAQ,eAAe;AAC7D,cAAM,iBAAiB,kBAAkB,MAAM;AAC/C,cAAM,iBAAiB,gBAAgB,IAAI;AAC3C,cAAM,aAAa,aAAa,QAAQ,OAAO,QAAQ,CAAC;AACxD,cAAM,UAAU,OAAO,WAAW,QAAQ;AAC1C,cAAM,uBAAuB,gBAAgB;AAC7C,cAAM,SAAS,OAAO;AACtB,cAAM,cAAc,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO;AAC3E,cAAM,gBAAgB,aAAW;AAC/B,kBAAQ,MAAM,gBAAgB;AAC9B,kBAAQ,iBAAiB,gBAAgB;AACzC,kBAAQ,iBAAiB,gBAAgB;AACzC,wBAAc,gBAAgB,EAAE,IAAI,UAAQ,cAAc,IAAI,EAAE,GAAG,EAAE,KAAK,UAAQ;AAChF,oBAAQ,MAAM,gBAAgB;AAC9B,oBAAQ,MAAM,gBAAgB;AAAA,UAChC,CAAC;AAAA,QACH;AACA,cAAM,UAAU,MAAM;AACpB,cAAI,SAAS;AACX,0BAAc,OAAO,GAAG;AAAA,UAC1B;AACA,wBAAc,IAAI,WAAW;AAC7B,yBAAe,OAAO;AACtB,mBAAS,KAAK,gBAAgB,IAAI,CAAC,EAAE,KAAK,UAAQ,KAAK,wBAAwB,OAAO,CAAC;AAAA,QACzF;AACA,YAAI,CAAC,gBAAgB;AACnB,gBAAM,0BAA0B,OAAO,MAAM,cAAc,GAAG,6BAA6B,GAAG,UAAQ;AACpG,gBAAI,oBAAoB,MAAM,GAAG;AAC/B,kBAAI,CAAC,oBAAoB,cAAc,KAAK,gBAAgB,IAAI,MAAM,MAAM;AAC1E,iCAAiB,QAAQ,eAAe;AAAA,cAC1C;AAAA,YACF;AAAA,UACF,CAAC;AACD,gBAAM,oBAAoB;AAAA,YACxB,WAAW,aAAa;AAAA,YACxB,gBAAgB,qBAAqB;AAAA,YACrC,iBAAiB,qBAAqB;AAAA,YACtC,cAAc,qBAAqB;AAAA,YACnC,eAAe,qBAAqB;AAAA,YACpC,aAAa,YAAY;AAAA,YACzB,cAAc,YAAY;AAAA,YAC1B;AAAA,UACF;AACA,cAAI,SAAS;AACX,0BAAc,OAAO,KAAK,kBAAkB,UAAU;AAAA,UACxD;AACA,sBAAY,QAAQ,YAAY,SAAS;AACzC,+BAAqB,QAAQ,qBAAqB,SAAS;AAC3D,wBAAc,IAAI,QAAQ;AAC1B,yBAAe,KAAK,gBAAgB;AACpC,iBAAO,GAAG,UAAU,OAAO;AAC3B,0BAAgB,IAAI,iBAAiB;AACrC,cAAI,oBAAoB,MAAM,GAAG;AAC/B,8BAAkB,cAAc;AAAA,UAClC;AACA,qCAA2B,QAAQ,IAAI;AAAA,QACzC,OAAO;AACL,yBAAe,wBAAwB,OAAO;AAC9C,cAAI,oBAAoB,MAAM,KAAK,oBAAoB,cAAc,GAAG;AACtE,2BAAe,MAAM,cAAc,CAAC;AAAA,UACtC;AACA,sBAAY,QAAQ,eAAe;AACnC,sBAAY,SAAS,eAAe;AACpC,+BAAqB,QAAQ,eAAe;AAC5C,+BAAqB,SAAS,eAAe;AAC7C,+BAAqB,MAAM,eAAe;AAC1C,+BAAqB,OAAO,eAAe;AAC3C,kBAAQ;AACR,uBAAa,eAAe,SAAS;AACrC,0BAAgB,IAAI,IAAI;AACxB,qCAA2B,QAAQ,KAAK;AACxC,iBAAO,IAAI,UAAU,OAAO;AAAA,QAC9B;AAAA,MACF;AAEA,YAAM,aAAa,CAAC,QAAQ,oBAAoB;AAC9C,eAAO,WAAW,iBAAiB,MAAM;AACvC,2BAAiB,QAAQ,eAAe;AAAA,QAC1C,CAAC;AAAA,MACH;AAEA,YAAM,mBAAmB,CAAC,QAAQ,oBAAoB,SAAO;AAC3D,YAAI,UAAU,gBAAgB,IAAI,MAAM,IAAI;AAC5C,cAAM,sBAAsB,OAAK,IAAI,UAAU,EAAE,KAAK;AACtD,eAAO,GAAG,0BAA0B,mBAAmB;AACvD,eAAO,MAAM,OAAO,IAAI,0BAA0B,mBAAmB;AAAA,MACvE;AACA,YAAM,WAAW,CAAC,QAAQ,oBAAoB;AAC5C,cAAM,WAAW,MAAM,OAAO,YAAY,eAAe;AACzD,eAAO,GAAG,SAAS,kBAAkB,cAAc;AAAA,UACjD,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV;AAAA,UACA,SAAS,iBAAiB,QAAQ,eAAe;AAAA,QACnD,CAAC;AACD,eAAO,GAAG,SAAS,gBAAgB,cAAc;AAAA,UAC/C,SAAS;AAAA,UACT,MAAM;AAAA,UACN;AAAA,UACA,SAAS,iBAAiB,QAAQ,eAAe;AAAA,QACnD,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,MAAM;AACjB,iBAAS,IAAI,cAAc,YAAU;AACnC,gBAAM,kBAAkB,KAAK,IAAI;AACjC,cAAI,OAAO,QAAQ;AACjB,mBAAO,MAAM,eAAe;AAAA,UAC9B;AACA,qBAAW,MAAM;AACjB,qBAAW,QAAQ,eAAe;AAClC,mBAAS,QAAQ,eAAe;AAChC,iBAAO,YAAY,gBAAgB,IAAI,eAAe;AACtD,iBAAO,MAAM,eAAe;AAAA,QAC9B,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IAEX,GAAG;AAAA;AAAA;;;ACrqCH;", "names": ["value", "get", "set", "type", "all", "binder", "r", "filter", "mediaMatch", "browsers", "oses", "ancestors$1", "siblings$1", "bind$1", "unbind"]}