{"version": 3, "sources": ["../../.pnpm/markdown-it-link-attributes@4.0.1/node_modules/markdown-it-link-attributes/index.js"], "sourcesContent": ["\"use strict\";\n\n// Adapted from https://github.com/markdown-it/markdown-it/blob/fbc6b0fed563ba7c00557ab638fd19752f8e759d/docs/architecture.md\n\nfunction findFirstMatchingConfig(link, configs) {\n  var i, config;\n  var href = link.attrs[link.attrIndex(\"href\")][1];\n\n  for (i = 0; i < configs.length; ++i) {\n    config = configs[i];\n\n    // If there is a matcher function defined then call it\n    // Matcher Function should return a boolean indicating\n    // whether or not it matched. If it matched, use that\n    // configuration, otherwise, try the next one.\n    if (typeof config.matcher === \"function\") {\n      if (config.matcher(href, config)) {\n        return config;\n      } else {\n        continue;\n      }\n    }\n\n    return config;\n  }\n}\n\nfunction applyAttributes(idx, tokens, attributes) {\n  Object.keys(attributes).forEach(function (attr) {\n    var attrIndex;\n    var value = attributes[attr];\n\n    if (attr === \"className\") {\n      // when dealing with applying classes\n      // programatically, some programmers\n      // may prefer to use the className syntax\n      attr = \"class\";\n    }\n\n    attrIndex = tokens[idx].attrIndex(attr);\n\n    if (attrIndex < 0) {\n      // attr doesn't exist, add new attribute\n      tokens[idx].attrPush([attr, value]);\n    } else {\n      // attr already exists, overwrite it\n      tokens[idx].attrs[attrIndex][1] = value; // replace value of existing attr\n    }\n  });\n}\n\nfunction markdownitLinkAttributes(md, configs) {\n  if (!configs) {\n    configs = [];\n  } else {\n    configs = Array.isArray(configs) ? configs : [configs];\n  }\n\n  Object.freeze(configs);\n\n  var defaultRender = md.renderer.rules.link_open || this.defaultRender;\n\n  md.renderer.rules.link_open = function (tokens, idx, options, env, self) {\n    var config = findFirstMatchingConfig(tokens[idx], configs);\n    var attributes = config && config.attrs;\n\n    if (attributes) {\n      applyAttributes(idx, tokens, attributes);\n    }\n\n    // pass token to default renderer.\n    return defaultRender(tokens, idx, options, env, self);\n  };\n}\n\nmarkdownitLinkAttributes.defaultRender = function (\n  tokens,\n  idx,\n  options,\n  env,\n  self\n) {\n  return self.renderToken(tokens, idx, options);\n};\n\nmodule.exports = markdownitLinkAttributes;\n"], "mappings": ";;;;;AAAA;AAAA;AAIA,aAAS,wBAAwB,MAAM,SAAS;AAC9C,UAAI,GAAG;AACP,UAAI,OAAO,KAAK,MAAM,KAAK,UAAU,MAAM,CAAC,EAAE,CAAC;AAE/C,WAAK,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE,GAAG;AACnC,iBAAS,QAAQ,CAAC;AAMlB,YAAI,OAAO,OAAO,YAAY,YAAY;AACxC,cAAI,OAAO,QAAQ,MAAM,MAAM,GAAG;AAChC,mBAAO;AAAA,UACT,OAAO;AACL;AAAA,UACF;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,gBAAgB,KAAK,QAAQ,YAAY;AAChD,aAAO,KAAK,UAAU,EAAE,QAAQ,SAAU,MAAM;AAC9C,YAAI;AACJ,YAAI,QAAQ,WAAW,IAAI;AAE3B,YAAI,SAAS,aAAa;AAIxB,iBAAO;AAAA,QACT;AAEA,oBAAY,OAAO,GAAG,EAAE,UAAU,IAAI;AAEtC,YAAI,YAAY,GAAG;AAEjB,iBAAO,GAAG,EAAE,SAAS,CAAC,MAAM,KAAK,CAAC;AAAA,QACpC,OAAO;AAEL,iBAAO,GAAG,EAAE,MAAM,SAAS,EAAE,CAAC,IAAI;AAAA,QACpC;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,yBAAyB,IAAI,SAAS;AAC7C,UAAI,CAAC,SAAS;AACZ,kBAAU,CAAC;AAAA,MACb,OAAO;AACL,kBAAU,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAAA,MACvD;AAEA,aAAO,OAAO,OAAO;AAErB,UAAI,gBAAgB,GAAG,SAAS,MAAM,aAAa,KAAK;AAExD,SAAG,SAAS,MAAM,YAAY,SAAU,QAAQ,KAAK,SAAS,KAAK,MAAM;AACvE,YAAI,SAAS,wBAAwB,OAAO,GAAG,GAAG,OAAO;AACzD,YAAI,aAAa,UAAU,OAAO;AAElC,YAAI,YAAY;AACd,0BAAgB,KAAK,QAAQ,UAAU;AAAA,QACzC;AAGA,eAAO,cAAc,QAAQ,KAAK,SAAS,KAAK,IAAI;AAAA,MACtD;AAAA,IACF;AAEA,6BAAyB,gBAAgB,SACvC,QACA,KACA,SACA,KACA,MACA;AACA,aAAO,KAAK,YAAY,QAAQ,KAAK,OAAO;AAAA,IAC9C;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}