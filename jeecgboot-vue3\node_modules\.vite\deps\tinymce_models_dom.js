import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/.pnpm/tinymce@6.6.2/node_modules/tinymce/models/dom/model.js
var require_model = __commonJS({
  "node_modules/.pnpm/tinymce@6.6.2/node_modules/tinymce/models/dom/model.js"() {
    (function() {
      "use strict";
      var global$1 = tinymce.util.Tools.resolve("tinymce.ModelManager");
      const hasProto = (v, constructor, predicate) => {
        var _a;
        if (predicate(v, constructor.prototype)) {
          return true;
        } else {
          return ((_a = v.constructor) === null || _a === void 0 ? void 0 : _a.name) === constructor.name;
        }
      };
      const typeOf = (x) => {
        const t = typeof x;
        if (x === null) {
          return "null";
        } else if (t === "object" && Array.isArray(x)) {
          return "array";
        } else if (t === "object" && hasProto(x, String, (o, proto) => proto.isPrototypeOf(o))) {
          return "string";
        } else {
          return t;
        }
      };
      const isType$1 = (type2) => (value2) => typeOf(value2) === type2;
      const isSimpleType = (type2) => (value2) => typeof value2 === type2;
      const eq$2 = (t) => (a) => t === a;
      const isString = isType$1("string");
      const isObject = isType$1("object");
      const isArray = isType$1("array");
      const isNull = eq$2(null);
      const isBoolean = isSimpleType("boolean");
      const isUndefined = eq$2(void 0);
      const isNullable = (a) => a === null || a === void 0;
      const isNonNullable = (a) => !isNullable(a);
      const isFunction = isSimpleType("function");
      const isNumber = isSimpleType("number");
      const noop = () => {
      };
      const compose = (fa, fb) => {
        return (...args) => {
          return fa(fb.apply(null, args));
        };
      };
      const compose1 = (fbc, fab) => (a) => fbc(fab(a));
      const constant = (value2) => {
        return () => {
          return value2;
        };
      };
      const identity = (x) => {
        return x;
      };
      const tripleEquals = (a, b) => {
        return a === b;
      };
      function curry(fn, ...initialArgs) {
        return (...restArgs) => {
          const all2 = initialArgs.concat(restArgs);
          return fn.apply(null, all2);
        };
      }
      const not = (f) => (t) => !f(t);
      const die = (msg) => {
        return () => {
          throw new Error(msg);
        };
      };
      const apply = (f) => {
        return f();
      };
      const never = constant(false);
      const always = constant(true);
      class Optional {
        constructor(tag, value2) {
          this.tag = tag;
          this.value = value2;
        }
        static some(value2) {
          return new Optional(true, value2);
        }
        static none() {
          return Optional.singletonNone;
        }
        fold(onNone, onSome) {
          if (this.tag) {
            return onSome(this.value);
          } else {
            return onNone();
          }
        }
        isSome() {
          return this.tag;
        }
        isNone() {
          return !this.tag;
        }
        map(mapper) {
          if (this.tag) {
            return Optional.some(mapper(this.value));
          } else {
            return Optional.none();
          }
        }
        bind(binder2) {
          if (this.tag) {
            return binder2(this.value);
          } else {
            return Optional.none();
          }
        }
        exists(predicate) {
          return this.tag && predicate(this.value);
        }
        forall(predicate) {
          return !this.tag || predicate(this.value);
        }
        filter(predicate) {
          if (!this.tag || predicate(this.value)) {
            return this;
          } else {
            return Optional.none();
          }
        }
        getOr(replacement) {
          return this.tag ? this.value : replacement;
        }
        or(replacement) {
          return this.tag ? this : replacement;
        }
        getOrThunk(thunk) {
          return this.tag ? this.value : thunk();
        }
        orThunk(thunk) {
          return this.tag ? this : thunk();
        }
        getOrDie(message) {
          if (!this.tag) {
            throw new Error(message !== null && message !== void 0 ? message : "Called getOrDie on None");
          } else {
            return this.value;
          }
        }
        static from(value2) {
          return isNonNullable(value2) ? Optional.some(value2) : Optional.none();
        }
        getOrNull() {
          return this.tag ? this.value : null;
        }
        getOrUndefined() {
          return this.value;
        }
        each(worker) {
          if (this.tag) {
            worker(this.value);
          }
        }
        toArray() {
          return this.tag ? [this.value] : [];
        }
        toString() {
          return this.tag ? `some(${this.value})` : "none()";
        }
      }
      Optional.singletonNone = new Optional(false);
      const nativeSlice = Array.prototype.slice;
      const nativeIndexOf = Array.prototype.indexOf;
      const nativePush = Array.prototype.push;
      const rawIndexOf = (ts, t) => nativeIndexOf.call(ts, t);
      const contains$2 = (xs, x) => rawIndexOf(xs, x) > -1;
      const exists = (xs, pred) => {
        for (let i = 0, len = xs.length; i < len; i++) {
          const x = xs[i];
          if (pred(x, i)) {
            return true;
          }
        }
        return false;
      };
      const range$1 = (num, f) => {
        const r2 = [];
        for (let i = 0; i < num; i++) {
          r2.push(f(i));
        }
        return r2;
      };
      const map$1 = (xs, f) => {
        const len = xs.length;
        const r2 = new Array(len);
        for (let i = 0; i < len; i++) {
          const x = xs[i];
          r2[i] = f(x, i);
        }
        return r2;
      };
      const each$2 = (xs, f) => {
        for (let i = 0, len = xs.length; i < len; i++) {
          const x = xs[i];
          f(x, i);
        }
      };
      const eachr = (xs, f) => {
        for (let i = xs.length - 1; i >= 0; i--) {
          const x = xs[i];
          f(x, i);
        }
      };
      const partition = (xs, pred) => {
        const pass = [];
        const fail = [];
        for (let i = 0, len = xs.length; i < len; i++) {
          const x = xs[i];
          const arr = pred(x, i) ? pass : fail;
          arr.push(x);
        }
        return {
          pass,
          fail
        };
      };
      const filter$2 = (xs, pred) => {
        const r2 = [];
        for (let i = 0, len = xs.length; i < len; i++) {
          const x = xs[i];
          if (pred(x, i)) {
            r2.push(x);
          }
        }
        return r2;
      };
      const foldr = (xs, f, acc) => {
        eachr(xs, (x, i) => {
          acc = f(acc, x, i);
        });
        return acc;
      };
      const foldl = (xs, f, acc) => {
        each$2(xs, (x, i) => {
          acc = f(acc, x, i);
        });
        return acc;
      };
      const findUntil = (xs, pred, until) => {
        for (let i = 0, len = xs.length; i < len; i++) {
          const x = xs[i];
          if (pred(x, i)) {
            return Optional.some(x);
          } else if (until(x, i)) {
            break;
          }
        }
        return Optional.none();
      };
      const find$1 = (xs, pred) => {
        return findUntil(xs, pred, never);
      };
      const findIndex = (xs, pred) => {
        for (let i = 0, len = xs.length; i < len; i++) {
          const x = xs[i];
          if (pred(x, i)) {
            return Optional.some(i);
          }
        }
        return Optional.none();
      };
      const flatten = (xs) => {
        const r2 = [];
        for (let i = 0, len = xs.length; i < len; ++i) {
          if (!isArray(xs[i])) {
            throw new Error("Arr.flatten item " + i + " was not an array, input: " + xs);
          }
          nativePush.apply(r2, xs[i]);
        }
        return r2;
      };
      const bind$2 = (xs, f) => flatten(map$1(xs, f));
      const forall = (xs, pred) => {
        for (let i = 0, len = xs.length; i < len; ++i) {
          const x = xs[i];
          if (pred(x, i) !== true) {
            return false;
          }
        }
        return true;
      };
      const reverse = (xs) => {
        const r2 = nativeSlice.call(xs, 0);
        r2.reverse();
        return r2;
      };
      const mapToObject = (xs, f) => {
        const r2 = {};
        for (let i = 0, len = xs.length; i < len; i++) {
          const x = xs[i];
          r2[String(x)] = f(x, i);
        }
        return r2;
      };
      const sort$1 = (xs, comparator) => {
        const copy2 = nativeSlice.call(xs, 0);
        copy2.sort(comparator);
        return copy2;
      };
      const get$d = (xs, i) => i >= 0 && i < xs.length ? Optional.some(xs[i]) : Optional.none();
      const head = (xs) => get$d(xs, 0);
      const last$2 = (xs) => get$d(xs, xs.length - 1);
      const findMap = (arr, f) => {
        for (let i = 0; i < arr.length; i++) {
          const r2 = f(arr[i], i);
          if (r2.isSome()) {
            return r2;
          }
        }
        return Optional.none();
      };
      const keys = Object.keys;
      const hasOwnProperty = Object.hasOwnProperty;
      const each$1 = (obj, f) => {
        const props = keys(obj);
        for (let k = 0, len = props.length; k < len; k++) {
          const i = props[k];
          const x = obj[i];
          f(x, i);
        }
      };
      const map = (obj, f) => {
        return tupleMap(obj, (x, i) => ({
          k: i,
          v: f(x, i)
        }));
      };
      const tupleMap = (obj, f) => {
        const r2 = {};
        each$1(obj, (x, i) => {
          const tuple = f(x, i);
          r2[tuple.k] = tuple.v;
        });
        return r2;
      };
      const objAcc = (r2) => (x, i) => {
        r2[i] = x;
      };
      const internalFilter = (obj, pred, onTrue, onFalse) => {
        each$1(obj, (x, i) => {
          (pred(x, i) ? onTrue : onFalse)(x, i);
        });
      };
      const filter$1 = (obj, pred) => {
        const t = {};
        internalFilter(obj, pred, objAcc(t), noop);
        return t;
      };
      const mapToArray = (obj, f) => {
        const r2 = [];
        each$1(obj, (value2, name2) => {
          r2.push(f(value2, name2));
        });
        return r2;
      };
      const values = (obj) => {
        return mapToArray(obj, identity);
      };
      const get$c = (obj, key2) => {
        return has$1(obj, key2) ? Optional.from(obj[key2]) : Optional.none();
      };
      const has$1 = (obj, key2) => hasOwnProperty.call(obj, key2);
      const hasNonNullableKey = (obj, key2) => has$1(obj, key2) && obj[key2] !== void 0 && obj[key2] !== null;
      const isEmpty = (r2) => {
        for (const x in r2) {
          if (hasOwnProperty.call(r2, x)) {
            return false;
          }
        }
        return true;
      };
      const Global = typeof window !== "undefined" ? window : Function("return this;")();
      const path = (parts, scope) => {
        let o = scope !== void 0 && scope !== null ? scope : Global;
        for (let i = 0; i < parts.length && o !== void 0 && o !== null; ++i) {
          o = o[parts[i]];
        }
        return o;
      };
      const resolve$2 = (p, scope) => {
        const parts = p.split(".");
        return path(parts, scope);
      };
      const unsafe = (name2, scope) => {
        return resolve$2(name2, scope);
      };
      const getOrDie = (name2, scope) => {
        const actual = unsafe(name2, scope);
        if (actual === void 0 || actual === null) {
          throw new Error(name2 + " not available on this browser");
        }
        return actual;
      };
      const getPrototypeOf = Object.getPrototypeOf;
      const sandHTMLElement = (scope) => {
        return getOrDie("HTMLElement", scope);
      };
      const isPrototypeOf = (x) => {
        const scope = resolve$2("ownerDocument.defaultView", x);
        return isObject(x) && (sandHTMLElement(scope).prototype.isPrototypeOf(x) || /^HTML\w*Element$/.test(getPrototypeOf(x).constructor.name));
      };
      const COMMENT = 8;
      const DOCUMENT = 9;
      const DOCUMENT_FRAGMENT = 11;
      const ELEMENT = 1;
      const TEXT = 3;
      const name = (element) => {
        const r2 = element.dom.nodeName;
        return r2.toLowerCase();
      };
      const type = (element) => element.dom.nodeType;
      const isType = (t) => (element) => type(element) === t;
      const isComment = (element) => type(element) === COMMENT || name(element) === "#comment";
      const isHTMLElement = (element) => isElement(element) && isPrototypeOf(element.dom);
      const isElement = isType(ELEMENT);
      const isText = isType(TEXT);
      const isDocument = isType(DOCUMENT);
      const isDocumentFragment = isType(DOCUMENT_FRAGMENT);
      const isTag = (tag) => (e) => isElement(e) && name(e) === tag;
      const rawSet = (dom, key2, value2) => {
        if (isString(value2) || isBoolean(value2) || isNumber(value2)) {
          dom.setAttribute(key2, value2 + "");
        } else {
          console.error("Invalid call to Attribute.set. Key ", key2, ":: Value ", value2, ":: Element ", dom);
          throw new Error("Attribute value was not simple");
        }
      };
      const set$2 = (element, key2, value2) => {
        rawSet(element.dom, key2, value2);
      };
      const setAll$1 = (element, attrs) => {
        const dom = element.dom;
        each$1(attrs, (v, k) => {
          rawSet(dom, k, v);
        });
      };
      const setOptions = (element, attrs) => {
        each$1(attrs, (v, k) => {
          v.fold(() => {
            remove$7(element, k);
          }, (value2) => {
            rawSet(element.dom, k, value2);
          });
        });
      };
      const get$b = (element, key2) => {
        const v = element.dom.getAttribute(key2);
        return v === null ? void 0 : v;
      };
      const getOpt = (element, key2) => Optional.from(get$b(element, key2));
      const remove$7 = (element, key2) => {
        element.dom.removeAttribute(key2);
      };
      const clone$2 = (element) => foldl(element.dom.attributes, (acc, attr) => {
        acc[attr.name] = attr.value;
        return acc;
      }, {});
      const fromHtml$1 = (html, scope) => {
        const doc = scope || document;
        const div = doc.createElement("div");
        div.innerHTML = html;
        if (!div.hasChildNodes() || div.childNodes.length > 1) {
          const message = "HTML does not have a single root node";
          console.error(message, html);
          throw new Error(message);
        }
        return fromDom$1(div.childNodes[0]);
      };
      const fromTag = (tag, scope) => {
        const doc = scope || document;
        const node = doc.createElement(tag);
        return fromDom$1(node);
      };
      const fromText = (text, scope) => {
        const doc = scope || document;
        const node = doc.createTextNode(text);
        return fromDom$1(node);
      };
      const fromDom$1 = (node) => {
        if (node === null || node === void 0) {
          throw new Error("Node cannot be null or undefined");
        }
        return { dom: node };
      };
      const fromPoint$1 = (docElm, x, y) => Optional.from(docElm.dom.elementFromPoint(x, y)).map(fromDom$1);
      const SugarElement = {
        fromHtml: fromHtml$1,
        fromTag,
        fromText,
        fromDom: fromDom$1,
        fromPoint: fromPoint$1
      };
      const is$2 = (element, selector) => {
        const dom = element.dom;
        if (dom.nodeType !== ELEMENT) {
          return false;
        } else {
          const elem = dom;
          if (elem.matches !== void 0) {
            return elem.matches(selector);
          } else if (elem.msMatchesSelector !== void 0) {
            return elem.msMatchesSelector(selector);
          } else if (elem.webkitMatchesSelector !== void 0) {
            return elem.webkitMatchesSelector(selector);
          } else if (elem.mozMatchesSelector !== void 0) {
            return elem.mozMatchesSelector(selector);
          } else {
            throw new Error("Browser lacks native selectors");
          }
        }
      };
      const bypassSelector = (dom) => dom.nodeType !== ELEMENT && dom.nodeType !== DOCUMENT && dom.nodeType !== DOCUMENT_FRAGMENT || dom.childElementCount === 0;
      const all$1 = (selector, scope) => {
        const base2 = scope === void 0 ? document : scope.dom;
        return bypassSelector(base2) ? [] : map$1(base2.querySelectorAll(selector), SugarElement.fromDom);
      };
      const one = (selector, scope) => {
        const base2 = scope === void 0 ? document : scope.dom;
        return bypassSelector(base2) ? Optional.none() : Optional.from(base2.querySelector(selector)).map(SugarElement.fromDom);
      };
      const eq$1 = (e1, e2) => e1.dom === e2.dom;
      const contains$1 = (e1, e2) => {
        const d1 = e1.dom;
        const d2 = e2.dom;
        return d1 === d2 ? false : d1.contains(d2);
      };
      const is$1 = is$2;
      const owner = (element) => SugarElement.fromDom(element.dom.ownerDocument);
      const documentOrOwner = (dos) => isDocument(dos) ? dos : owner(dos);
      const documentElement = (element) => SugarElement.fromDom(documentOrOwner(element).dom.documentElement);
      const defaultView = (element) => SugarElement.fromDom(documentOrOwner(element).dom.defaultView);
      const parent = (element) => Optional.from(element.dom.parentNode).map(SugarElement.fromDom);
      const parentElement = (element) => Optional.from(element.dom.parentElement).map(SugarElement.fromDom);
      const parents = (element, isRoot) => {
        const stop = isFunction(isRoot) ? isRoot : never;
        let dom = element.dom;
        const ret = [];
        while (dom.parentNode !== null && dom.parentNode !== void 0) {
          const rawParent = dom.parentNode;
          const p = SugarElement.fromDom(rawParent);
          ret.push(p);
          if (stop(p) === true) {
            break;
          } else {
            dom = rawParent;
          }
        }
        return ret;
      };
      const prevSibling = (element) => Optional.from(element.dom.previousSibling).map(SugarElement.fromDom);
      const nextSibling = (element) => Optional.from(element.dom.nextSibling).map(SugarElement.fromDom);
      const children$2 = (element) => map$1(element.dom.childNodes, SugarElement.fromDom);
      const child$2 = (element, index) => {
        const cs = element.dom.childNodes;
        return Optional.from(cs[index]).map(SugarElement.fromDom);
      };
      const firstChild = (element) => child$2(element, 0);
      const before$3 = (marker, element) => {
        const parent$1 = parent(marker);
        parent$1.each((v) => {
          v.dom.insertBefore(element.dom, marker.dom);
        });
      };
      const after$5 = (marker, element) => {
        const sibling = nextSibling(marker);
        sibling.fold(() => {
          const parent$1 = parent(marker);
          parent$1.each((v) => {
            append$1(v, element);
          });
        }, (v) => {
          before$3(v, element);
        });
      };
      const prepend = (parent2, element) => {
        const firstChild$1 = firstChild(parent2);
        firstChild$1.fold(() => {
          append$1(parent2, element);
        }, (v) => {
          parent2.dom.insertBefore(element.dom, v.dom);
        });
      };
      const append$1 = (parent2, element) => {
        parent2.dom.appendChild(element.dom);
      };
      const appendAt = (parent2, element, index) => {
        child$2(parent2, index).fold(() => {
          append$1(parent2, element);
        }, (v) => {
          before$3(v, element);
        });
      };
      const wrap = (element, wrapper) => {
        before$3(element, wrapper);
        append$1(wrapper, element);
      };
      const after$4 = (marker, elements) => {
        each$2(elements, (x, i) => {
          const e = i === 0 ? marker : elements[i - 1];
          after$5(e, x);
        });
      };
      const append = (parent2, elements) => {
        each$2(elements, (x) => {
          append$1(parent2, x);
        });
      };
      const empty = (element) => {
        element.dom.textContent = "";
        each$2(children$2(element), (rogue) => {
          remove$6(rogue);
        });
      };
      const remove$6 = (element) => {
        const dom = element.dom;
        if (dom.parentNode !== null) {
          dom.parentNode.removeChild(dom);
        }
      };
      const unwrap = (wrapper) => {
        const children2 = children$2(wrapper);
        if (children2.length > 0) {
          after$4(wrapper, children2);
        }
        remove$6(wrapper);
      };
      const clone$1 = (original, isDeep) => SugarElement.fromDom(original.dom.cloneNode(isDeep));
      const shallow = (original) => clone$1(original, false);
      const deep = (original) => clone$1(original, true);
      const shallowAs = (original, tag) => {
        const nu2 = SugarElement.fromTag(tag);
        const attributes = clone$2(original);
        setAll$1(nu2, attributes);
        return nu2;
      };
      const copy$2 = (original, tag) => {
        const nu2 = shallowAs(original, tag);
        const cloneChildren = children$2(deep(original));
        append(nu2, cloneChildren);
        return nu2;
      };
      const mutate$1 = (original, tag) => {
        const nu2 = shallowAs(original, tag);
        after$5(original, nu2);
        const children2 = children$2(original);
        append(nu2, children2);
        remove$6(original);
        return nu2;
      };
      const validSectionList = [
        "tfoot",
        "thead",
        "tbody",
        "colgroup"
      ];
      const isValidSection = (parentName) => contains$2(validSectionList, parentName);
      const grid = (rows2, columns2) => ({
        rows: rows2,
        columns: columns2
      });
      const address = (row2, column) => ({
        row: row2,
        column
      });
      const detail = (element, rowspan, colspan) => ({
        element,
        rowspan,
        colspan
      });
      const detailnew = (element, rowspan, colspan, isNew) => ({
        element,
        rowspan,
        colspan,
        isNew
      });
      const extended = (element, rowspan, colspan, row2, column, isLocked) => ({
        element,
        rowspan,
        colspan,
        row: row2,
        column,
        isLocked
      });
      const rowdetail = (element, cells2, section2) => ({
        element,
        cells: cells2,
        section: section2
      });
      const rowdetailnew = (element, cells2, section2, isNew) => ({
        element,
        cells: cells2,
        section: section2,
        isNew
      });
      const elementnew = (element, isNew, isLocked) => ({
        element,
        isNew,
        isLocked
      });
      const rowcells = (element, cells2, section2, isNew) => ({
        element,
        cells: cells2,
        section: section2,
        isNew
      });
      const bounds = (startRow, startCol, finishRow, finishCol) => ({
        startRow,
        startCol,
        finishRow,
        finishCol
      });
      const columnext = (element, colspan, column) => ({
        element,
        colspan,
        column
      });
      const colgroup = (element, columns2) => ({
        element,
        columns: columns2
      });
      const isShadowRoot = (dos) => isDocumentFragment(dos) && isNonNullable(dos.dom.host);
      const supported = isFunction(Element.prototype.attachShadow) && isFunction(Node.prototype.getRootNode);
      const isSupported$1 = constant(supported);
      const getRootNode = supported ? (e) => SugarElement.fromDom(e.dom.getRootNode()) : documentOrOwner;
      const getShadowRoot = (e) => {
        const r2 = getRootNode(e);
        return isShadowRoot(r2) ? Optional.some(r2) : Optional.none();
      };
      const getShadowHost = (e) => SugarElement.fromDom(e.dom.host);
      const getOriginalEventTarget = (event) => {
        if (isSupported$1() && isNonNullable(event.target)) {
          const el = SugarElement.fromDom(event.target);
          if (isElement(el) && isOpenShadowHost(el)) {
            if (event.composed && event.composedPath) {
              const composedPath = event.composedPath();
              if (composedPath) {
                return head(composedPath);
              }
            }
          }
        }
        return Optional.from(event.target);
      };
      const isOpenShadowHost = (element) => isNonNullable(element.dom.shadowRoot);
      const inBody = (element) => {
        const dom = isText(element) ? element.dom.parentNode : element.dom;
        if (dom === void 0 || dom === null || dom.ownerDocument === null) {
          return false;
        }
        const doc = dom.ownerDocument;
        return getShadowRoot(SugarElement.fromDom(dom)).fold(() => doc.body.contains(dom), compose1(inBody, getShadowHost));
      };
      const body$1 = () => getBody$1(SugarElement.fromDom(document));
      const getBody$1 = (doc) => {
        const b = doc.dom.body;
        if (b === null || b === void 0) {
          throw new Error("Body is not available yet");
        }
        return SugarElement.fromDom(b);
      };
      const ancestors$4 = (scope, predicate, isRoot) => filter$2(parents(scope, isRoot), predicate);
      const children$1 = (scope, predicate) => filter$2(children$2(scope), predicate);
      const descendants$1 = (scope, predicate) => {
        let result = [];
        each$2(children$2(scope), (x) => {
          if (predicate(x)) {
            result = result.concat([x]);
          }
          result = result.concat(descendants$1(x, predicate));
        });
        return result;
      };
      const ancestors$3 = (scope, selector, isRoot) => ancestors$4(scope, (e) => is$2(e, selector), isRoot);
      const children = (scope, selector) => children$1(scope, (e) => is$2(e, selector));
      const descendants = (scope, selector) => all$1(selector, scope);
      var ClosestOrAncestor = (is2, ancestor2, scope, a, isRoot) => {
        if (is2(scope, a)) {
          return Optional.some(scope);
        } else if (isFunction(isRoot) && isRoot(scope)) {
          return Optional.none();
        } else {
          return ancestor2(scope, a, isRoot);
        }
      };
      const ancestor$2 = (scope, predicate, isRoot) => {
        let element = scope.dom;
        const stop = isFunction(isRoot) ? isRoot : never;
        while (element.parentNode) {
          element = element.parentNode;
          const el = SugarElement.fromDom(element);
          if (predicate(el)) {
            return Optional.some(el);
          } else if (stop(el)) {
            break;
          }
        }
        return Optional.none();
      };
      const closest$2 = (scope, predicate, isRoot) => {
        const is2 = (s, test) => test(s);
        return ClosestOrAncestor(is2, ancestor$2, scope, predicate, isRoot);
      };
      const child$1 = (scope, predicate) => {
        const pred = (node) => predicate(SugarElement.fromDom(node));
        const result = find$1(scope.dom.childNodes, pred);
        return result.map(SugarElement.fromDom);
      };
      const descendant$1 = (scope, predicate) => {
        const descend = (node) => {
          for (let i = 0; i < node.childNodes.length; i++) {
            const child2 = SugarElement.fromDom(node.childNodes[i]);
            if (predicate(child2)) {
              return Optional.some(child2);
            }
            const res = descend(node.childNodes[i]);
            if (res.isSome()) {
              return res;
            }
          }
          return Optional.none();
        };
        return descend(scope.dom);
      };
      const ancestor$1 = (scope, selector, isRoot) => ancestor$2(scope, (e) => is$2(e, selector), isRoot);
      const child = (scope, selector) => child$1(scope, (e) => is$2(e, selector));
      const descendant = (scope, selector) => one(selector, scope);
      const closest$1 = (scope, selector, isRoot) => {
        const is2 = (element, selector2) => is$2(element, selector2);
        return ClosestOrAncestor(is2, ancestor$1, scope, selector, isRoot);
      };
      const is = (lhs, rhs, comparator = tripleEquals) => lhs.exists((left2) => comparator(left2, rhs));
      const cat = (arr) => {
        const r2 = [];
        const push = (x) => {
          r2.push(x);
        };
        for (let i = 0; i < arr.length; i++) {
          arr[i].each(push);
        }
        return r2;
      };
      const bindFrom = (a, f) => a !== void 0 && a !== null ? f(a) : Optional.none();
      const someIf = (b, a) => b ? Optional.some(a) : Optional.none();
      const checkRange = (str, substr, start) => substr === "" || str.length >= substr.length && str.substr(start, start + substr.length) === substr;
      const contains = (str, substr, start = 0, end) => {
        const idx = str.indexOf(substr, start);
        if (idx !== -1) {
          return isUndefined(end) ? true : idx + substr.length <= end;
        } else {
          return false;
        }
      };
      const startsWith = (str, prefix) => {
        return checkRange(str, prefix, 0);
      };
      const endsWith = (str, suffix) => {
        return checkRange(str, suffix, str.length - suffix.length);
      };
      const blank = (r2) => (s) => s.replace(r2, "");
      const trim = blank(/^\s+|\s+$/g);
      const isNotEmpty = (s) => s.length > 0;
      const toFloat = (value2) => {
        const num = parseFloat(value2);
        return isNaN(num) ? Optional.none() : Optional.some(num);
      };
      const isSupported = (dom) => dom.style !== void 0 && isFunction(dom.style.getPropertyValue);
      const internalSet = (dom, property, value2) => {
        if (!isString(value2)) {
          console.error("Invalid call to CSS.set. Property ", property, ":: Value ", value2, ":: Element ", dom);
          throw new Error("CSS value must be a string: " + value2);
        }
        if (isSupported(dom)) {
          dom.style.setProperty(property, value2);
        }
      };
      const internalRemove = (dom, property) => {
        if (isSupported(dom)) {
          dom.style.removeProperty(property);
        }
      };
      const set$1 = (element, property, value2) => {
        const dom = element.dom;
        internalSet(dom, property, value2);
      };
      const setAll = (element, css2) => {
        const dom = element.dom;
        each$1(css2, (v, k) => {
          internalSet(dom, k, v);
        });
      };
      const get$a = (element, property) => {
        const dom = element.dom;
        const styles2 = window.getComputedStyle(dom);
        const r2 = styles2.getPropertyValue(property);
        return r2 === "" && !inBody(element) ? getUnsafeProperty(dom, property) : r2;
      };
      const getUnsafeProperty = (dom, property) => isSupported(dom) ? dom.style.getPropertyValue(property) : "";
      const getRaw$2 = (element, property) => {
        const dom = element.dom;
        const raw = getUnsafeProperty(dom, property);
        return Optional.from(raw).filter((r2) => r2.length > 0);
      };
      const remove$5 = (element, property) => {
        const dom = element.dom;
        internalRemove(dom, property);
        if (is(getOpt(element, "style").map(trim), "")) {
          remove$7(element, "style");
        }
      };
      const copy$1 = (source, target) => {
        const sourceDom = source.dom;
        const targetDom = target.dom;
        if (isSupported(sourceDom) && isSupported(targetDom)) {
          targetDom.style.cssText = sourceDom.style.cssText;
        }
      };
      const getAttrValue = (cell2, name2, fallback2 = 0) => getOpt(cell2, name2).map((value2) => parseInt(value2, 10)).getOr(fallback2);
      const getSpan = (cell2, type2) => getAttrValue(cell2, type2, 1);
      const hasColspan = (cellOrCol) => {
        if (isTag("col")(cellOrCol)) {
          return getAttrValue(cellOrCol, "span", 1) > 1;
        } else {
          return getSpan(cellOrCol, "colspan") > 1;
        }
      };
      const hasRowspan = (cell2) => getSpan(cell2, "rowspan") > 1;
      const getCssValue = (element, property) => parseInt(get$a(element, property), 10);
      const minWidth = constant(10);
      const minHeight = constant(10);
      const firstLayer = (scope, selector) => {
        return filterFirstLayer(scope, selector, always);
      };
      const filterFirstLayer = (scope, selector, predicate) => {
        return bind$2(children$2(scope), (x) => {
          if (is$2(x, selector)) {
            return predicate(x) ? [x] : [];
          } else {
            return filterFirstLayer(x, selector, predicate);
          }
        });
      };
      const lookup = (tags, element, isRoot = never) => {
        if (isRoot(element)) {
          return Optional.none();
        }
        if (contains$2(tags, name(element))) {
          return Optional.some(element);
        }
        const isRootOrUpperTable = (elm) => is$2(elm, "table") || isRoot(elm);
        return ancestor$1(element, tags.join(","), isRootOrUpperTable);
      };
      const cell = (element, isRoot) => lookup([
        "td",
        "th"
      ], element, isRoot);
      const cells$1 = (ancestor2) => firstLayer(ancestor2, "th,td");
      const columns$1 = (ancestor2) => {
        if (is$2(ancestor2, "colgroup")) {
          return children(ancestor2, "col");
        } else {
          return bind$2(columnGroups(ancestor2), (columnGroup) => children(columnGroup, "col"));
        }
      };
      const table = (element, isRoot) => closest$1(element, "table", isRoot);
      const rows$1 = (ancestor2) => firstLayer(ancestor2, "tr");
      const columnGroups = (ancestor2) => table(ancestor2).fold(constant([]), (table2) => children(table2, "colgroup"));
      const fromRowsOrColGroups = (elems, getSection) => map$1(elems, (row2) => {
        if (name(row2) === "colgroup") {
          const cells2 = map$1(columns$1(row2), (column) => {
            const colspan = getAttrValue(column, "span", 1);
            return detail(column, 1, colspan);
          });
          return rowdetail(row2, cells2, "colgroup");
        } else {
          const cells2 = map$1(cells$1(row2), (cell2) => {
            const rowspan = getAttrValue(cell2, "rowspan", 1);
            const colspan = getAttrValue(cell2, "colspan", 1);
            return detail(cell2, rowspan, colspan);
          });
          return rowdetail(row2, cells2, getSection(row2));
        }
      });
      const getParentSection = (group) => parent(group).map((parent2) => {
        const parentName = name(parent2);
        return isValidSection(parentName) ? parentName : "tbody";
      }).getOr("tbody");
      const fromTable$1 = (table2) => {
        const rows2 = rows$1(table2);
        const columnGroups$1 = columnGroups(table2);
        const elems = [
          ...columnGroups$1,
          ...rows2
        ];
        return fromRowsOrColGroups(elems, getParentSection);
      };
      const fromPastedRows = (elems, section2) => fromRowsOrColGroups(elems, () => section2);
      const cached = (f) => {
        let called = false;
        let r2;
        return (...args) => {
          if (!called) {
            called = true;
            r2 = f.apply(null, args);
          }
          return r2;
        };
      };
      const DeviceType = (os, browser, userAgent, mediaMatch2) => {
        const isiPad = os.isiOS() && /ipad/i.test(userAgent) === true;
        const isiPhone = os.isiOS() && !isiPad;
        const isMobile = os.isiOS() || os.isAndroid();
        const isTouch = isMobile || mediaMatch2("(pointer:coarse)");
        const isTablet = isiPad || !isiPhone && isMobile && mediaMatch2("(min-device-width:768px)");
        const isPhone = isiPhone || isMobile && !isTablet;
        const iOSwebview = browser.isSafari() && os.isiOS() && /safari/i.test(userAgent) === false;
        const isDesktop = !isPhone && !isTablet && !iOSwebview;
        return {
          isiPad: constant(isiPad),
          isiPhone: constant(isiPhone),
          isTablet: constant(isTablet),
          isPhone: constant(isPhone),
          isTouch: constant(isTouch),
          isAndroid: os.isAndroid,
          isiOS: os.isiOS,
          isWebView: constant(iOSwebview),
          isDesktop: constant(isDesktop)
        };
      };
      const firstMatch = (regexes, s) => {
        for (let i = 0; i < regexes.length; i++) {
          const x = regexes[i];
          if (x.test(s)) {
            return x;
          }
        }
        return void 0;
      };
      const find = (regexes, agent) => {
        const r2 = firstMatch(regexes, agent);
        if (!r2) {
          return {
            major: 0,
            minor: 0
          };
        }
        const group = (i) => {
          return Number(agent.replace(r2, "$" + i));
        };
        return nu$2(group(1), group(2));
      };
      const detect$5 = (versionRegexes, agent) => {
        const cleanedAgent = String(agent).toLowerCase();
        if (versionRegexes.length === 0) {
          return unknown$2();
        }
        return find(versionRegexes, cleanedAgent);
      };
      const unknown$2 = () => {
        return nu$2(0, 0);
      };
      const nu$2 = (major, minor) => {
        return {
          major,
          minor
        };
      };
      const Version = {
        nu: nu$2,
        detect: detect$5,
        unknown: unknown$2
      };
      const detectBrowser$1 = (browsers2, userAgentData) => {
        return findMap(userAgentData.brands, (uaBrand) => {
          const lcBrand = uaBrand.brand.toLowerCase();
          return find$1(browsers2, (browser) => {
            var _a;
            return lcBrand === ((_a = browser.brand) === null || _a === void 0 ? void 0 : _a.toLowerCase());
          }).map((info) => ({
            current: info.name,
            version: Version.nu(parseInt(uaBrand.version, 10), 0)
          }));
        });
      };
      const detect$4 = (candidates, userAgent) => {
        const agent = String(userAgent).toLowerCase();
        return find$1(candidates, (candidate) => {
          return candidate.search(agent);
        });
      };
      const detectBrowser = (browsers2, userAgent) => {
        return detect$4(browsers2, userAgent).map((browser) => {
          const version = Version.detect(browser.versionRegexes, userAgent);
          return {
            current: browser.name,
            version
          };
        });
      };
      const detectOs = (oses2, userAgent) => {
        return detect$4(oses2, userAgent).map((os) => {
          const version = Version.detect(os.versionRegexes, userAgent);
          return {
            current: os.name,
            version
          };
        });
      };
      const normalVersionRegex = /.*?version\/\ ?([0-9]+)\.([0-9]+).*/;
      const checkContains = (target) => {
        return (uastring) => {
          return contains(uastring, target);
        };
      };
      const browsers = [
        {
          name: "Edge",
          versionRegexes: [/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],
          search: (uastring) => {
            return contains(uastring, "edge/") && contains(uastring, "chrome") && contains(uastring, "safari") && contains(uastring, "applewebkit");
          }
        },
        {
          name: "Chromium",
          brand: "Chromium",
          versionRegexes: [
            /.*?chrome\/([0-9]+)\.([0-9]+).*/,
            normalVersionRegex
          ],
          search: (uastring) => {
            return contains(uastring, "chrome") && !contains(uastring, "chromeframe");
          }
        },
        {
          name: "IE",
          versionRegexes: [
            /.*?msie\ ?([0-9]+)\.([0-9]+).*/,
            /.*?rv:([0-9]+)\.([0-9]+).*/
          ],
          search: (uastring) => {
            return contains(uastring, "msie") || contains(uastring, "trident");
          }
        },
        {
          name: "Opera",
          versionRegexes: [
            normalVersionRegex,
            /.*?opera\/([0-9]+)\.([0-9]+).*/
          ],
          search: checkContains("opera")
        },
        {
          name: "Firefox",
          versionRegexes: [/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],
          search: checkContains("firefox")
        },
        {
          name: "Safari",
          versionRegexes: [
            normalVersionRegex,
            /.*?cpu os ([0-9]+)_([0-9]+).*/
          ],
          search: (uastring) => {
            return (contains(uastring, "safari") || contains(uastring, "mobile/")) && contains(uastring, "applewebkit");
          }
        }
      ];
      const oses = [
        {
          name: "Windows",
          search: checkContains("win"),
          versionRegexes: [/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]
        },
        {
          name: "iOS",
          search: (uastring) => {
            return contains(uastring, "iphone") || contains(uastring, "ipad");
          },
          versionRegexes: [
            /.*?version\/\ ?([0-9]+)\.([0-9]+).*/,
            /.*cpu os ([0-9]+)_([0-9]+).*/,
            /.*cpu iphone os ([0-9]+)_([0-9]+).*/
          ]
        },
        {
          name: "Android",
          search: checkContains("android"),
          versionRegexes: [/.*?android\ ?([0-9]+)\.([0-9]+).*/]
        },
        {
          name: "macOS",
          search: checkContains("mac os x"),
          versionRegexes: [/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]
        },
        {
          name: "Linux",
          search: checkContains("linux"),
          versionRegexes: []
        },
        {
          name: "Solaris",
          search: checkContains("sunos"),
          versionRegexes: []
        },
        {
          name: "FreeBSD",
          search: checkContains("freebsd"),
          versionRegexes: []
        },
        {
          name: "ChromeOS",
          search: checkContains("cros"),
          versionRegexes: [/.*?chrome\/([0-9]+)\.([0-9]+).*/]
        }
      ];
      const PlatformInfo = {
        browsers: constant(browsers),
        oses: constant(oses)
      };
      const edge = "Edge";
      const chromium = "Chromium";
      const ie = "IE";
      const opera = "Opera";
      const firefox = "Firefox";
      const safari = "Safari";
      const unknown$1 = () => {
        return nu$1({
          current: void 0,
          version: Version.unknown()
        });
      };
      const nu$1 = (info) => {
        const current = info.current;
        const version = info.version;
        const isBrowser = (name2) => () => current === name2;
        return {
          current,
          version,
          isEdge: isBrowser(edge),
          isChromium: isBrowser(chromium),
          isIE: isBrowser(ie),
          isOpera: isBrowser(opera),
          isFirefox: isBrowser(firefox),
          isSafari: isBrowser(safari)
        };
      };
      const Browser = {
        unknown: unknown$1,
        nu: nu$1,
        edge: constant(edge),
        chromium: constant(chromium),
        ie: constant(ie),
        opera: constant(opera),
        firefox: constant(firefox),
        safari: constant(safari)
      };
      const windows = "Windows";
      const ios = "iOS";
      const android = "Android";
      const linux = "Linux";
      const macos = "macOS";
      const solaris = "Solaris";
      const freebsd = "FreeBSD";
      const chromeos = "ChromeOS";
      const unknown = () => {
        return nu({
          current: void 0,
          version: Version.unknown()
        });
      };
      const nu = (info) => {
        const current = info.current;
        const version = info.version;
        const isOS = (name2) => () => current === name2;
        return {
          current,
          version,
          isWindows: isOS(windows),
          isiOS: isOS(ios),
          isAndroid: isOS(android),
          isMacOS: isOS(macos),
          isLinux: isOS(linux),
          isSolaris: isOS(solaris),
          isFreeBSD: isOS(freebsd),
          isChromeOS: isOS(chromeos)
        };
      };
      const OperatingSystem = {
        unknown,
        nu,
        windows: constant(windows),
        ios: constant(ios),
        android: constant(android),
        linux: constant(linux),
        macos: constant(macos),
        solaris: constant(solaris),
        freebsd: constant(freebsd),
        chromeos: constant(chromeos)
      };
      const detect$3 = (userAgent, userAgentDataOpt, mediaMatch2) => {
        const browsers2 = PlatformInfo.browsers();
        const oses2 = PlatformInfo.oses();
        const browser = userAgentDataOpt.bind((userAgentData) => detectBrowser$1(browsers2, userAgentData)).orThunk(() => detectBrowser(browsers2, userAgent)).fold(Browser.unknown, Browser.nu);
        const os = detectOs(oses2, userAgent).fold(OperatingSystem.unknown, OperatingSystem.nu);
        const deviceType = DeviceType(os, browser, userAgent, mediaMatch2);
        return {
          browser,
          os,
          deviceType
        };
      };
      const PlatformDetection = { detect: detect$3 };
      const mediaMatch = (query) => window.matchMedia(query).matches;
      let platform = cached(() => PlatformDetection.detect(navigator.userAgent, Optional.from(navigator.userAgentData), mediaMatch));
      const detect$2 = () => platform();
      const Dimension = (name2, getOffset) => {
        const set2 = (element, h) => {
          if (!isNumber(h) && !h.match(/^[0-9]+$/)) {
            throw new Error(name2 + ".set accepts only positive integer values. Value was " + h);
          }
          const dom = element.dom;
          if (isSupported(dom)) {
            dom.style[name2] = h + "px";
          }
        };
        const get2 = (element) => {
          const r2 = getOffset(element);
          if (r2 <= 0 || r2 === null) {
            const css2 = get$a(element, name2);
            return parseFloat(css2) || 0;
          }
          return r2;
        };
        const getOuter2 = get2;
        const aggregate = (element, properties) => foldl(properties, (acc, property) => {
          const val = get$a(element, property);
          const value2 = val === void 0 ? 0 : parseInt(val, 10);
          return isNaN(value2) ? acc : acc + value2;
        }, 0);
        const max = (element, value2, properties) => {
          const cumulativeInclusions = aggregate(element, properties);
          const absoluteMax = value2 > cumulativeInclusions ? value2 - cumulativeInclusions : 0;
          return absoluteMax;
        };
        return {
          set: set2,
          get: get2,
          getOuter: getOuter2,
          aggregate,
          max
        };
      };
      const toNumber = (px, fallback2) => toFloat(px).getOr(fallback2);
      const getProp = (element, name2, fallback2) => toNumber(get$a(element, name2), fallback2);
      const calcContentBoxSize = (element, size, upper, lower) => {
        const paddingUpper = getProp(element, `padding-${upper}`, 0);
        const paddingLower = getProp(element, `padding-${lower}`, 0);
        const borderUpper = getProp(element, `border-${upper}-width`, 0);
        const borderLower = getProp(element, `border-${lower}-width`, 0);
        return size - paddingUpper - paddingLower - borderUpper - borderLower;
      };
      const getCalculatedWidth = (element, boxSizing) => {
        const dom = element.dom;
        const width2 = dom.getBoundingClientRect().width || dom.offsetWidth;
        return boxSizing === "border-box" ? width2 : calcContentBoxSize(element, width2, "left", "right");
      };
      const getHeight$1 = (element) => getProp(element, "height", element.dom.offsetHeight);
      const getWidth = (element) => getProp(element, "width", element.dom.offsetWidth);
      const getInnerWidth = (element) => getCalculatedWidth(element, "content-box");
      const api$2 = Dimension("width", (element) => element.dom.offsetWidth);
      const get$9 = (element) => api$2.get(element);
      const getOuter$2 = (element) => api$2.getOuter(element);
      const getInner = getInnerWidth;
      const getRuntime$1 = getWidth;
      const addCells = (gridRow, index, cells2) => {
        const existingCells = gridRow.cells;
        const before2 = existingCells.slice(0, index);
        const after2 = existingCells.slice(index);
        const newCells = before2.concat(cells2).concat(after2);
        return setCells(gridRow, newCells);
      };
      const addCell = (gridRow, index, cell2) => addCells(gridRow, index, [cell2]);
      const mutateCell = (gridRow, index, cell2) => {
        const cells2 = gridRow.cells;
        cells2[index] = cell2;
      };
      const setCells = (gridRow, cells2) => rowcells(gridRow.element, cells2, gridRow.section, gridRow.isNew);
      const mapCells = (gridRow, f) => {
        const cells2 = gridRow.cells;
        const r2 = map$1(cells2, f);
        return rowcells(gridRow.element, r2, gridRow.section, gridRow.isNew);
      };
      const getCell = (gridRow, index) => gridRow.cells[index];
      const getCellElement = (gridRow, index) => getCell(gridRow, index).element;
      const cellLength = (gridRow) => gridRow.cells.length;
      const extractGridDetails = (grid2) => {
        const result = partition(grid2, (row2) => row2.section === "colgroup");
        return {
          rows: result.fail,
          cols: result.pass
        };
      };
      const clone = (gridRow, cloneRow2, cloneCell) => {
        const newCells = map$1(gridRow.cells, cloneCell);
        return rowcells(cloneRow2(gridRow.element), newCells, gridRow.section, true);
      };
      const LOCKED_COL_ATTR = "data-snooker-locked-cols";
      const getLockedColumnsFromTable = (table2) => getOpt(table2, LOCKED_COL_ATTR).bind((lockedColStr) => Optional.from(lockedColStr.match(/\d+/g))).map((lockedCols) => mapToObject(lockedCols, always));
      const getLockedColumnsFromGrid = (grid2) => {
        const locked = foldl(extractGridDetails(grid2).rows, (acc, row2) => {
          each$2(row2.cells, (cell2, idx) => {
            if (cell2.isLocked) {
              acc[idx] = true;
            }
          });
          return acc;
        }, {});
        const lockedArr = mapToArray(locked, (_val, key2) => parseInt(key2, 10));
        return sort$1(lockedArr);
      };
      const key = (row2, column) => {
        return row2 + "," + column;
      };
      const getAt = (warehouse, row2, column) => Optional.from(warehouse.access[key(row2, column)]);
      const findItem = (warehouse, item, comparator) => {
        const filtered = filterItems(warehouse, (detail2) => {
          return comparator(item, detail2.element);
        });
        return filtered.length > 0 ? Optional.some(filtered[0]) : Optional.none();
      };
      const filterItems = (warehouse, predicate) => {
        const all2 = bind$2(warehouse.all, (r2) => {
          return r2.cells;
        });
        return filter$2(all2, predicate);
      };
      const generateColumns = (rowData) => {
        const columnsGroup = {};
        let index = 0;
        each$2(rowData.cells, (column) => {
          const colspan = column.colspan;
          range$1(colspan, (columnIndex) => {
            const colIndex = index + columnIndex;
            columnsGroup[colIndex] = columnext(column.element, colspan, colIndex);
          });
          index += colspan;
        });
        return columnsGroup;
      };
      const generate$1 = (list) => {
        const access = {};
        const cells2 = [];
        const tableOpt = head(list).map((rowData) => rowData.element).bind(table);
        const lockedColumns = tableOpt.bind(getLockedColumnsFromTable).getOr({});
        let maxRows = 0;
        let maxColumns = 0;
        let rowCount = 0;
        const {
          pass: colgroupRows,
          fail: rows2
        } = partition(list, (rowData) => rowData.section === "colgroup");
        each$2(rows2, (rowData) => {
          const currentRow = [];
          each$2(rowData.cells, (rowCell) => {
            let start = 0;
            while (access[key(rowCount, start)] !== void 0) {
              start++;
            }
            const isLocked = hasNonNullableKey(lockedColumns, start.toString());
            const current = extended(rowCell.element, rowCell.rowspan, rowCell.colspan, rowCount, start, isLocked);
            for (let occupiedColumnPosition = 0; occupiedColumnPosition < rowCell.colspan; occupiedColumnPosition++) {
              for (let occupiedRowPosition = 0; occupiedRowPosition < rowCell.rowspan; occupiedRowPosition++) {
                const rowPosition = rowCount + occupiedRowPosition;
                const columnPosition = start + occupiedColumnPosition;
                const newpos = key(rowPosition, columnPosition);
                access[newpos] = current;
                maxColumns = Math.max(maxColumns, columnPosition + 1);
              }
            }
            currentRow.push(current);
          });
          maxRows++;
          cells2.push(rowdetail(rowData.element, currentRow, rowData.section));
          rowCount++;
        });
        const { columns: columns2, colgroups } = last$2(colgroupRows).map((rowData) => {
          const columns3 = generateColumns(rowData);
          const colgroup$1 = colgroup(rowData.element, values(columns3));
          return {
            colgroups: [colgroup$1],
            columns: columns3
          };
        }).getOrThunk(() => ({
          colgroups: [],
          columns: {}
        }));
        const grid$1 = grid(maxRows, maxColumns);
        return {
          grid: grid$1,
          access,
          all: cells2,
          columns: columns2,
          colgroups
        };
      };
      const fromTable = (table2) => {
        const list = fromTable$1(table2);
        return generate$1(list);
      };
      const justCells = (warehouse) => bind$2(warehouse.all, (w) => w.cells);
      const justColumns = (warehouse) => values(warehouse.columns);
      const hasColumns = (warehouse) => keys(warehouse.columns).length > 0;
      const getColumnAt = (warehouse, columnIndex) => Optional.from(warehouse.columns[columnIndex]);
      const Warehouse = {
        fromTable,
        generate: generate$1,
        getAt,
        findItem,
        filterItems,
        justCells,
        justColumns,
        hasColumns,
        getColumnAt
      };
      const columns = (warehouse, isValidCell = always) => {
        const grid2 = warehouse.grid;
        const cols = range$1(grid2.columns, identity);
        const rowsArr = range$1(grid2.rows, identity);
        return map$1(cols, (col2) => {
          const getBlock = () => bind$2(rowsArr, (r2) => Warehouse.getAt(warehouse, r2, col2).filter((detail2) => detail2.column === col2).toArray());
          const isValid = (detail2) => detail2.colspan === 1 && isValidCell(detail2.element);
          const getFallback = () => Warehouse.getAt(warehouse, 0, col2);
          return decide(getBlock, isValid, getFallback);
        });
      };
      const decide = (getBlock, isValid, getFallback) => {
        const inBlock = getBlock();
        const validInBlock = find$1(inBlock, isValid);
        const detailOption = validInBlock.orThunk(() => Optional.from(inBlock[0]).orThunk(getFallback));
        return detailOption.map((detail2) => detail2.element);
      };
      const rows = (warehouse) => {
        const grid2 = warehouse.grid;
        const rowsArr = range$1(grid2.rows, identity);
        const cols = range$1(grid2.columns, identity);
        return map$1(rowsArr, (row2) => {
          const getBlock = () => bind$2(cols, (c) => Warehouse.getAt(warehouse, row2, c).filter((detail2) => detail2.row === row2).fold(constant([]), (detail2) => [detail2]));
          const isSingle = (detail2) => detail2.rowspan === 1;
          const getFallback = () => Warehouse.getAt(warehouse, row2, 0);
          return decide(getBlock, isSingle, getFallback);
        });
      };
      const deduce = (xs, index) => {
        if (index < 0 || index >= xs.length - 1) {
          return Optional.none();
        }
        const current = xs[index].fold(() => {
          const rest = reverse(xs.slice(0, index));
          return findMap(rest, (a, i) => a.map((aa) => ({
            value: aa,
            delta: i + 1
          })));
        }, (c) => Optional.some({
          value: c,
          delta: 0
        }));
        const next = xs[index + 1].fold(() => {
          const rest = xs.slice(index + 1);
          return findMap(rest, (a, i) => a.map((aa) => ({
            value: aa,
            delta: i + 1
          })));
        }, (n) => Optional.some({
          value: n,
          delta: 1
        }));
        return current.bind((c) => next.map((n) => {
          const extras = n.delta + c.delta;
          return Math.abs(n.value - c.value) / extras;
        }));
      };
      const onDirection = (isLtr, isRtl) => (element) => getDirection(element) === "rtl" ? isRtl : isLtr;
      const getDirection = (element) => get$a(element, "direction") === "rtl" ? "rtl" : "ltr";
      const api$1 = Dimension("height", (element) => {
        const dom = element.dom;
        return inBody(element) ? dom.getBoundingClientRect().height : dom.offsetHeight;
      });
      const get$8 = (element) => api$1.get(element);
      const getOuter$1 = (element) => api$1.getOuter(element);
      const getRuntime = getHeight$1;
      const r = (left2, top) => {
        const translate2 = (x, y) => r(left2 + x, top + y);
        return {
          left: left2,
          top,
          translate: translate2
        };
      };
      const SugarPosition = r;
      const boxPosition = (dom) => {
        const box = dom.getBoundingClientRect();
        return SugarPosition(box.left, box.top);
      };
      const firstDefinedOrZero = (a, b) => {
        if (a !== void 0) {
          return a;
        } else {
          return b !== void 0 ? b : 0;
        }
      };
      const absolute = (element) => {
        const doc = element.dom.ownerDocument;
        const body2 = doc.body;
        const win = doc.defaultView;
        const html = doc.documentElement;
        if (body2 === element.dom) {
          return SugarPosition(body2.offsetLeft, body2.offsetTop);
        }
        const scrollTop = firstDefinedOrZero(win === null || win === void 0 ? void 0 : win.pageYOffset, html.scrollTop);
        const scrollLeft = firstDefinedOrZero(win === null || win === void 0 ? void 0 : win.pageXOffset, html.scrollLeft);
        const clientTop = firstDefinedOrZero(html.clientTop, body2.clientTop);
        const clientLeft = firstDefinedOrZero(html.clientLeft, body2.clientLeft);
        return viewport(element).translate(scrollLeft - clientLeft, scrollTop - clientTop);
      };
      const viewport = (element) => {
        const dom = element.dom;
        const doc = dom.ownerDocument;
        const body2 = doc.body;
        if (body2 === dom) {
          return SugarPosition(body2.offsetLeft, body2.offsetTop);
        }
        if (!inBody(element)) {
          return SugarPosition(0, 0);
        }
        return boxPosition(dom);
      };
      const rowInfo = (row2, y) => ({
        row: row2,
        y
      });
      const colInfo = (col2, x) => ({
        col: col2,
        x
      });
      const rtlEdge = (cell2) => {
        const pos = absolute(cell2);
        return pos.left + getOuter$2(cell2);
      };
      const ltrEdge = (cell2) => {
        return absolute(cell2).left;
      };
      const getLeftEdge = (index, cell2) => {
        return colInfo(index, ltrEdge(cell2));
      };
      const getRightEdge = (index, cell2) => {
        return colInfo(index, rtlEdge(cell2));
      };
      const getTop$1 = (cell2) => {
        return absolute(cell2).top;
      };
      const getTopEdge = (index, cell2) => {
        return rowInfo(index, getTop$1(cell2));
      };
      const getBottomEdge = (index, cell2) => {
        return rowInfo(index, getTop$1(cell2) + getOuter$1(cell2));
      };
      const findPositions = (getInnerEdge, getOuterEdge, array) => {
        if (array.length === 0) {
          return [];
        }
        const lines = map$1(array.slice(1), (cellOption, index) => {
          return cellOption.map((cell2) => {
            return getInnerEdge(index, cell2);
          });
        });
        const lastLine = array[array.length - 1].map((cell2) => {
          return getOuterEdge(array.length - 1, cell2);
        });
        return lines.concat([lastLine]);
      };
      const negate = (step) => {
        return -step;
      };
      const height = {
        delta: identity,
        positions: (optElements) => findPositions(getTopEdge, getBottomEdge, optElements),
        edge: getTop$1
      };
      const ltr$1 = {
        delta: identity,
        edge: ltrEdge,
        positions: (optElements) => findPositions(getLeftEdge, getRightEdge, optElements)
      };
      const rtl$1 = {
        delta: negate,
        edge: rtlEdge,
        positions: (optElements) => findPositions(getRightEdge, getLeftEdge, optElements)
      };
      const detect$1 = onDirection(ltr$1, rtl$1);
      const width = {
        delta: (amount, table2) => detect$1(table2).delta(amount, table2),
        positions: (cols, table2) => detect$1(table2).positions(cols, table2),
        edge: (cell2) => detect$1(cell2).edge(cell2)
      };
      const units = {
        unsupportedLength: [
          "em",
          "ex",
          "cap",
          "ch",
          "ic",
          "rem",
          "lh",
          "rlh",
          "vw",
          "vh",
          "vi",
          "vb",
          "vmin",
          "vmax",
          "cm",
          "mm",
          "Q",
          "in",
          "pc",
          "pt",
          "px"
        ],
        fixed: [
          "px",
          "pt"
        ],
        relative: ["%"],
        empty: [""]
      };
      const pattern = (() => {
        const decimalDigits = "[0-9]+";
        const signedInteger = "[+-]?" + decimalDigits;
        const exponentPart = "[eE]" + signedInteger;
        const dot = "\\.";
        const opt = (input) => `(?:${input})?`;
        const unsignedDecimalLiteral = [
          "Infinity",
          decimalDigits + dot + opt(decimalDigits) + opt(exponentPart),
          dot + decimalDigits + opt(exponentPart),
          decimalDigits + opt(exponentPart)
        ].join("|");
        const float = `[+-]?(?:${unsignedDecimalLiteral})`;
        return new RegExp(`^(${float})(.*)$`);
      })();
      const isUnit = (unit, accepted) => exists(accepted, (acc) => exists(units[acc], (check) => unit === check));
      const parse = (input, accepted) => {
        const match = Optional.from(pattern.exec(input));
        return match.bind((array) => {
          const value2 = Number(array[1]);
          const unitRaw = array[2];
          if (isUnit(unitRaw, accepted)) {
            return Optional.some({
              value: value2,
              unit: unitRaw
            });
          } else {
            return Optional.none();
          }
        });
      };
      const rPercentageBasedSizeRegex = /(\d+(\.\d+)?)%/;
      const rPixelBasedSizeRegex = /(\d+(\.\d+)?)px|em/;
      const isCol$2 = isTag("col");
      const getPercentSize = (elm, outerGetter, innerGetter) => {
        const relativeParent = parentElement(elm).getOrThunk(() => getBody$1(owner(elm)));
        return outerGetter(elm) / innerGetter(relativeParent) * 100;
      };
      const setPixelWidth = (cell2, amount) => {
        set$1(cell2, "width", amount + "px");
      };
      const setPercentageWidth = (cell2, amount) => {
        set$1(cell2, "width", amount + "%");
      };
      const setHeight = (cell2, amount) => {
        set$1(cell2, "height", amount + "px");
      };
      const getHeightValue = (cell2) => getRuntime(cell2) + "px";
      const convert = (cell2, number, getter, setter) => {
        const newSize = table(cell2).map((table2) => {
          const total2 = getter(table2);
          return Math.floor(number / 100 * total2);
        }).getOr(number);
        setter(cell2, newSize);
        return newSize;
      };
      const normalizePixelSize = (value2, cell2, getter, setter) => {
        const number = parseFloat(value2);
        return endsWith(value2, "%") && name(cell2) !== "table" ? convert(cell2, number, getter, setter) : number;
      };
      const getTotalHeight = (cell2) => {
        const value2 = getHeightValue(cell2);
        if (!value2) {
          return get$8(cell2);
        }
        return normalizePixelSize(value2, cell2, get$8, setHeight);
      };
      const get$7 = (cell2, type2, f) => {
        const v = f(cell2);
        const span = getSpan(cell2, type2);
        return v / span;
      };
      const getRaw$1 = (element, prop) => {
        return getRaw$2(element, prop).orThunk(() => {
          return getOpt(element, prop).map((val) => val + "px");
        });
      };
      const getRawWidth$1 = (element) => getRaw$1(element, "width");
      const getRawHeight = (element) => getRaw$1(element, "height");
      const getPercentageWidth = (cell2) => getPercentSize(cell2, get$9, getInner);
      const getPixelWidth$1 = (cell2) => isCol$2(cell2) ? get$9(cell2) : getRuntime$1(cell2);
      const getHeight = (cell2) => {
        return get$7(cell2, "rowspan", getTotalHeight);
      };
      const getGenericWidth = (cell2) => {
        const width2 = getRawWidth$1(cell2);
        return width2.bind((w) => parse(w, [
          "fixed",
          "relative",
          "empty"
        ]));
      };
      const setGenericWidth = (cell2, amount, unit) => {
        set$1(cell2, "width", amount + unit);
      };
      const getPixelTableWidth = (table2) => get$9(table2) + "px";
      const getPercentTableWidth = (table2) => getPercentSize(table2, get$9, getInner) + "%";
      const isPercentSizing$1 = (table2) => getRawWidth$1(table2).exists((size) => rPercentageBasedSizeRegex.test(size));
      const isPixelSizing$1 = (table2) => getRawWidth$1(table2).exists((size) => rPixelBasedSizeRegex.test(size));
      const isNoneSizing$1 = (table2) => getRawWidth$1(table2).isNone();
      const percentageBasedSizeRegex = constant(rPercentageBasedSizeRegex);
      const isCol$1 = isTag("col");
      const getRawW = (cell2) => {
        return getRawWidth$1(cell2).getOrThunk(() => getPixelWidth$1(cell2) + "px");
      };
      const getRawH = (cell2) => {
        return getRawHeight(cell2).getOrThunk(() => getHeight(cell2) + "px");
      };
      const justCols = (warehouse) => map$1(Warehouse.justColumns(warehouse), (column) => Optional.from(column.element));
      const isValidColumn = (cell2) => {
        const browser = detect$2().browser;
        const supportsColWidths = browser.isChromium() || browser.isFirefox();
        return isCol$1(cell2) ? supportsColWidths : true;
      };
      const getDimension = (cellOpt, index, backups, filter2, getter, fallback2) => cellOpt.filter(filter2).fold(() => fallback2(deduce(backups, index)), (cell2) => getter(cell2));
      const getWidthFrom = (warehouse, table2, getWidth2, fallback2) => {
        const columnCells = columns(warehouse);
        const columns$12 = Warehouse.hasColumns(warehouse) ? justCols(warehouse) : columnCells;
        const backups = [Optional.some(width.edge(table2))].concat(map$1(width.positions(columnCells, table2), (pos) => pos.map((p) => p.x)));
        const colFilter = not(hasColspan);
        return map$1(columns$12, (cellOption, c) => {
          return getDimension(cellOption, c, backups, colFilter, (column) => {
            if (isValidColumn(column)) {
              return getWidth2(column);
            } else {
              const cell2 = bindFrom(columnCells[c], identity);
              return getDimension(cell2, c, backups, colFilter, (cell3) => fallback2(Optional.some(get$9(cell3))), fallback2);
            }
          }, fallback2);
        });
      };
      const getDeduced = (deduced) => {
        return deduced.map((d) => {
          return d + "px";
        }).getOr("");
      };
      const getRawWidths = (warehouse, table2) => {
        return getWidthFrom(warehouse, table2, getRawW, getDeduced);
      };
      const getPercentageWidths = (warehouse, table2, tableSize) => {
        return getWidthFrom(warehouse, table2, getPercentageWidth, (deduced) => {
          return deduced.fold(() => {
            return tableSize.minCellWidth();
          }, (cellWidth) => {
            return cellWidth / tableSize.pixelWidth() * 100;
          });
        });
      };
      const getPixelWidths = (warehouse, table2, tableSize) => {
        return getWidthFrom(warehouse, table2, getPixelWidth$1, (deduced) => {
          return deduced.getOrThunk(tableSize.minCellWidth);
        });
      };
      const getHeightFrom = (warehouse, table2, direction, getHeight2, fallback2) => {
        const rows$12 = rows(warehouse);
        const backups = [Optional.some(direction.edge(table2))].concat(map$1(direction.positions(rows$12, table2), (pos) => pos.map((p) => p.y)));
        return map$1(rows$12, (cellOption, c) => {
          return getDimension(cellOption, c, backups, not(hasRowspan), getHeight2, fallback2);
        });
      };
      const getPixelHeights = (warehouse, table2, direction) => {
        return getHeightFrom(warehouse, table2, direction, getHeight, (deduced) => {
          return deduced.getOrThunk(minHeight);
        });
      };
      const getRawHeights = (warehouse, table2, direction) => {
        return getHeightFrom(warehouse, table2, direction, getRawH, getDeduced);
      };
      const widthLookup = (table2, getter) => () => {
        if (inBody(table2)) {
          return getter(table2);
        } else {
          return parseFloat(getRaw$2(table2, "width").getOr("0"));
        }
      };
      const noneSize = (table2) => {
        const getWidth2 = widthLookup(table2, get$9);
        const zero2 = constant(0);
        const getWidths = (warehouse, tableSize) => getPixelWidths(warehouse, table2, tableSize);
        return {
          width: getWidth2,
          pixelWidth: getWidth2,
          getWidths,
          getCellDelta: zero2,
          singleColumnWidth: constant([0]),
          minCellWidth: zero2,
          setElementWidth: noop,
          adjustTableWidth: noop,
          isRelative: true,
          label: "none"
        };
      };
      const percentageSize = (table2) => {
        const getFloatWidth = widthLookup(table2, (elem) => parseFloat(getPercentTableWidth(elem)));
        const getWidth2 = widthLookup(table2, get$9);
        const getCellDelta = (delta) => delta / getWidth2() * 100;
        const singleColumnWidth = (w, _delta) => [100 - w];
        const minCellWidth = () => minWidth() / getWidth2() * 100;
        const adjustTableWidth = (delta) => {
          const currentWidth = getFloatWidth();
          const change = delta / 100 * currentWidth;
          const newWidth = currentWidth + change;
          setPercentageWidth(table2, newWidth);
        };
        const getWidths = (warehouse, tableSize) => getPercentageWidths(warehouse, table2, tableSize);
        return {
          width: getFloatWidth,
          pixelWidth: getWidth2,
          getWidths,
          getCellDelta,
          singleColumnWidth,
          minCellWidth,
          setElementWidth: setPercentageWidth,
          adjustTableWidth,
          isRelative: true,
          label: "percent"
        };
      };
      const pixelSize = (table2) => {
        const getWidth2 = widthLookup(table2, get$9);
        const getCellDelta = identity;
        const singleColumnWidth = (w, delta) => {
          const newNext = Math.max(minWidth(), w + delta);
          return [newNext - w];
        };
        const adjustTableWidth = (delta) => {
          const newWidth = getWidth2() + delta;
          setPixelWidth(table2, newWidth);
        };
        const getWidths = (warehouse, tableSize) => getPixelWidths(warehouse, table2, tableSize);
        return {
          width: getWidth2,
          pixelWidth: getWidth2,
          getWidths,
          getCellDelta,
          singleColumnWidth,
          minCellWidth: minWidth,
          setElementWidth: setPixelWidth,
          adjustTableWidth,
          isRelative: false,
          label: "pixel"
        };
      };
      const chooseSize = (element, width2) => {
        const percentMatch = percentageBasedSizeRegex().exec(width2);
        if (percentMatch !== null) {
          return percentageSize(element);
        } else {
          return pixelSize(element);
        }
      };
      const getTableSize = (table2) => {
        const width2 = getRawWidth$1(table2);
        return width2.fold(() => noneSize(table2), (w) => chooseSize(table2, w));
      };
      const TableSize = {
        getTableSize,
        pixelSize,
        percentageSize,
        noneSize
      };
      const statsStruct = (minRow, minCol, maxRow, maxCol, allCells, selectedCells) => ({
        minRow,
        minCol,
        maxRow,
        maxCol,
        allCells,
        selectedCells
      });
      const findSelectedStats = (house, isSelected) => {
        const totalColumns = house.grid.columns;
        const totalRows = house.grid.rows;
        let minRow = totalRows;
        let minCol = totalColumns;
        let maxRow = 0;
        let maxCol = 0;
        const allCells = [];
        const selectedCells = [];
        each$1(house.access, (detail2) => {
          allCells.push(detail2);
          if (isSelected(detail2)) {
            selectedCells.push(detail2);
            const startRow = detail2.row;
            const endRow = startRow + detail2.rowspan - 1;
            const startCol = detail2.column;
            const endCol = startCol + detail2.colspan - 1;
            if (startRow < minRow) {
              minRow = startRow;
            } else if (endRow > maxRow) {
              maxRow = endRow;
            }
            if (startCol < minCol) {
              minCol = startCol;
            } else if (endCol > maxCol) {
              maxCol = endCol;
            }
          }
        });
        return statsStruct(minRow, minCol, maxRow, maxCol, allCells, selectedCells);
      };
      const makeCell = (list, seenSelected, rowIndex) => {
        const row2 = list[rowIndex].element;
        const td = SugarElement.fromTag("td");
        append$1(td, SugarElement.fromTag("br"));
        const f = seenSelected ? append$1 : prepend;
        f(row2, td);
      };
      const fillInGaps = (list, house, stats, isSelected) => {
        const rows2 = filter$2(list, (row2) => row2.section !== "colgroup");
        const totalColumns = house.grid.columns;
        const totalRows = house.grid.rows;
        for (let i = 0; i < totalRows; i++) {
          let seenSelected = false;
          for (let j = 0; j < totalColumns; j++) {
            if (!(i < stats.minRow || i > stats.maxRow || j < stats.minCol || j > stats.maxCol)) {
              const needCell = Warehouse.getAt(house, i, j).filter(isSelected).isNone();
              if (needCell) {
                makeCell(rows2, seenSelected, i);
              } else {
                seenSelected = true;
              }
            }
          }
        }
      };
      const clean = (replica, stats, house, widthDelta) => {
        each$1(house.columns, (col2) => {
          if (col2.column < stats.minCol || col2.column > stats.maxCol) {
            remove$6(col2.element);
          }
        });
        const emptyRows = filter$2(firstLayer(replica, "tr"), (row2) => row2.dom.childElementCount === 0);
        each$2(emptyRows, remove$6);
        if (stats.minCol === stats.maxCol || stats.minRow === stats.maxRow) {
          each$2(firstLayer(replica, "th,td"), (cell2) => {
            remove$7(cell2, "rowspan");
            remove$7(cell2, "colspan");
          });
        }
        remove$7(replica, LOCKED_COL_ATTR);
        remove$7(replica, "data-snooker-col-series");
        const tableSize = TableSize.getTableSize(replica);
        tableSize.adjustTableWidth(widthDelta);
      };
      const getTableWidthDelta = (table2, warehouse, tableSize, stats) => {
        if (stats.minCol === 0 && warehouse.grid.columns === stats.maxCol + 1) {
          return 0;
        }
        const colWidths = getPixelWidths(warehouse, table2, tableSize);
        const allColsWidth = foldl(colWidths, (acc, width2) => acc + width2, 0);
        const selectedColsWidth = foldl(colWidths.slice(stats.minCol, stats.maxCol + 1), (acc, width2) => acc + width2, 0);
        const newWidth = selectedColsWidth / allColsWidth * tableSize.pixelWidth();
        const delta = newWidth - tableSize.pixelWidth();
        return tableSize.getCellDelta(delta);
      };
      const extract$1 = (table2, selectedSelector) => {
        const isSelected = (detail2) => is$2(detail2.element, selectedSelector);
        const replica = deep(table2);
        const list = fromTable$1(replica);
        const tableSize = TableSize.getTableSize(table2);
        const replicaHouse = Warehouse.generate(list);
        const replicaStats = findSelectedStats(replicaHouse, isSelected);
        const selector = "th:not(" + selectedSelector + "),td:not(" + selectedSelector + ")";
        const unselectedCells = filterFirstLayer(replica, "th,td", (cell2) => is$2(cell2, selector));
        each$2(unselectedCells, remove$6);
        fillInGaps(list, replicaHouse, replicaStats, isSelected);
        const house = Warehouse.fromTable(table2);
        const widthDelta = getTableWidthDelta(table2, house, tableSize, replicaStats);
        clean(replica, replicaStats, replicaHouse, widthDelta);
        return replica;
      };
      const nbsp = " ";
      const NodeValue = (is2, name2) => {
        const get2 = (element) => {
          if (!is2(element)) {
            throw new Error("Can only get " + name2 + " value of a " + name2 + " node");
          }
          return getOption2(element).getOr("");
        };
        const getOption2 = (element) => is2(element) ? Optional.from(element.dom.nodeValue) : Optional.none();
        const set2 = (element, value2) => {
          if (!is2(element)) {
            throw new Error("Can only set raw " + name2 + " value of a " + name2 + " node");
          }
          element.dom.nodeValue = value2;
        };
        return {
          get: get2,
          getOption: getOption2,
          set: set2
        };
      };
      const api = NodeValue(isText, "text");
      const get$6 = (element) => api.get(element);
      const getOption = (element) => api.getOption(element);
      const set = (element, value2) => api.set(element, value2);
      const getEnd = (element) => name(element) === "img" ? 1 : getOption(element).fold(() => children$2(element).length, (v) => v.length);
      const isTextNodeWithCursorPosition = (el) => getOption(el).filter((text) => text.trim().length !== 0 || text.indexOf(nbsp) > -1).isSome();
      const isContentEditableFalse = (elem) => isHTMLElement(elem) && get$b(elem, "contenteditable") === "false";
      const elementsWithCursorPosition = [
        "img",
        "br"
      ];
      const isCursorPosition = (elem) => {
        const hasCursorPosition = isTextNodeWithCursorPosition(elem);
        return hasCursorPosition || contains$2(elementsWithCursorPosition, name(elem)) || isContentEditableFalse(elem);
      };
      const first = (element) => descendant$1(element, isCursorPosition);
      const last$1 = (element) => descendantRtl(element, isCursorPosition);
      const descendantRtl = (scope, predicate) => {
        const descend = (element) => {
          const children2 = children$2(element);
          for (let i = children2.length - 1; i >= 0; i--) {
            const child2 = children2[i];
            if (predicate(child2)) {
              return Optional.some(child2);
            }
            const res = descend(child2);
            if (res.isSome()) {
              return res;
            }
          }
          return Optional.none();
        };
        return descend(scope);
      };
      const transferableAttributes = {
        scope: [
          "row",
          "col"
        ]
      };
      const createCell = (doc) => () => {
        const td = SugarElement.fromTag("td", doc.dom);
        append$1(td, SugarElement.fromTag("br", doc.dom));
        return td;
      };
      const createCol = (doc) => () => {
        return SugarElement.fromTag("col", doc.dom);
      };
      const createColgroup = (doc) => () => {
        return SugarElement.fromTag("colgroup", doc.dom);
      };
      const createRow$1 = (doc) => () => {
        return SugarElement.fromTag("tr", doc.dom);
      };
      const replace$1 = (cell2, tag, attrs) => {
        const replica = copy$2(cell2, tag);
        each$1(attrs, (v, k) => {
          if (v === null) {
            remove$7(replica, k);
          } else {
            set$2(replica, k, v);
          }
        });
        return replica;
      };
      const pasteReplace = (cell2) => {
        return cell2;
      };
      const cloneFormats = (oldCell, newCell, formats) => {
        const first$1 = first(oldCell);
        return first$1.map((firstText) => {
          const formatSelector = formats.join(",");
          const parents2 = ancestors$3(firstText, formatSelector, (element) => {
            return eq$1(element, oldCell);
          });
          return foldr(parents2, (last2, parent2) => {
            const clonedFormat = shallow(parent2);
            append$1(last2, clonedFormat);
            return clonedFormat;
          }, newCell);
        }).getOr(newCell);
      };
      const cloneAppropriateAttributes = (original, clone2) => {
        each$1(transferableAttributes, (validAttributes, attributeName) => getOpt(original, attributeName).filter((attribute) => contains$2(validAttributes, attribute)).each((attribute) => set$2(clone2, attributeName, attribute)));
      };
      const cellOperations = (mutate2, doc, formatsToClone) => {
        const cloneCss = (prev, clone2) => {
          copy$1(prev.element, clone2);
          remove$5(clone2, "height");
          if (prev.colspan !== 1) {
            remove$5(clone2, "width");
          }
        };
        const newCell = (prev) => {
          const td = SugarElement.fromTag(name(prev.element), doc.dom);
          const formats = formatsToClone.getOr([
            "strong",
            "em",
            "b",
            "i",
            "span",
            "font",
            "h1",
            "h2",
            "h3",
            "h4",
            "h5",
            "h6",
            "p",
            "div"
          ]);
          const lastNode = formats.length > 0 ? cloneFormats(prev.element, td, formats) : td;
          append$1(lastNode, SugarElement.fromTag("br"));
          cloneCss(prev, td);
          cloneAppropriateAttributes(prev.element, td);
          mutate2(prev.element, td);
          return td;
        };
        const newCol = (prev) => {
          const col2 = SugarElement.fromTag(name(prev.element), doc.dom);
          cloneCss(prev, col2);
          mutate2(prev.element, col2);
          return col2;
        };
        return {
          col: newCol,
          colgroup: createColgroup(doc),
          row: createRow$1(doc),
          cell: newCell,
          replace: replace$1,
          colGap: createCol(doc),
          gap: createCell(doc)
        };
      };
      const paste$1 = (doc) => {
        return {
          col: createCol(doc),
          colgroup: createColgroup(doc),
          row: createRow$1(doc),
          cell: createCell(doc),
          replace: pasteReplace,
          colGap: createCol(doc),
          gap: createCell(doc)
        };
      };
      const fromHtml = (html, scope) => {
        const doc = scope || document;
        const div = doc.createElement("div");
        div.innerHTML = html;
        return children$2(SugarElement.fromDom(div));
      };
      const fromDom = (nodes) => map$1(nodes, SugarElement.fromDom);
      const option = (name2) => (editor) => editor.options.get(name2);
      const defaultWidth = "100%";
      const getPixelForcedWidth = (editor) => {
        var _a;
        const dom = editor.dom;
        const parentBlock = (_a = dom.getParent(editor.selection.getStart(), dom.isBlock)) !== null && _a !== void 0 ? _a : editor.getBody();
        return getInner(SugarElement.fromDom(parentBlock)) + "px";
      };
      const determineDefaultTableStyles = (editor, defaultStyles) => {
        if (isTableResponsiveForced(editor) || !shouldStyleWithCss(editor)) {
          return defaultStyles;
        } else if (isTablePixelsForced(editor)) {
          return {
            ...defaultStyles,
            width: getPixelForcedWidth(editor)
          };
        } else {
          return {
            ...defaultStyles,
            width: defaultWidth
          };
        }
      };
      const determineDefaultTableAttributes = (editor, defaultAttributes) => {
        if (isTableResponsiveForced(editor) || shouldStyleWithCss(editor)) {
          return defaultAttributes;
        } else if (isTablePixelsForced(editor)) {
          return {
            ...defaultAttributes,
            width: getPixelForcedWidth(editor)
          };
        } else {
          return {
            ...defaultAttributes,
            width: defaultWidth
          };
        }
      };
      const register = (editor) => {
        const registerOption = editor.options.register;
        registerOption("table_clone_elements", { processor: "string[]" });
        registerOption("table_use_colgroups", {
          processor: "boolean",
          default: true
        });
        registerOption("table_header_type", {
          processor: (value2) => {
            const valid = contains$2([
              "section",
              "cells",
              "sectionCells",
              "auto"
            ], value2);
            return valid ? {
              value: value2,
              valid
            } : {
              valid: false,
              message: "Must be one of: section, cells, sectionCells or auto."
            };
          },
          default: "section"
        });
        registerOption("table_sizing_mode", {
          processor: "string",
          default: "auto"
        });
        registerOption("table_default_attributes", {
          processor: "object",
          default: { border: "1" }
        });
        registerOption("table_default_styles", {
          processor: "object",
          default: { "border-collapse": "collapse" }
        });
        registerOption("table_column_resizing", {
          processor: (value2) => {
            const valid = contains$2([
              "preservetable",
              "resizetable"
            ], value2);
            return valid ? {
              value: value2,
              valid
            } : {
              valid: false,
              message: "Must be preservetable, or resizetable."
            };
          },
          default: "preservetable"
        });
        registerOption("table_resize_bars", {
          processor: "boolean",
          default: true
        });
        registerOption("table_style_by_css", {
          processor: "boolean",
          default: true
        });
        registerOption("table_merge_content_on_paste", {
          processor: "boolean",
          default: true
        });
      };
      const getTableCloneElements = (editor) => {
        return Optional.from(editor.options.get("table_clone_elements"));
      };
      const hasTableObjectResizing = (editor) => {
        const objectResizing = editor.options.get("object_resizing");
        return contains$2(objectResizing.split(","), "table");
      };
      const getTableHeaderType = option("table_header_type");
      const getTableColumnResizingBehaviour = option("table_column_resizing");
      const isPreserveTableColumnResizing = (editor) => getTableColumnResizingBehaviour(editor) === "preservetable";
      const isResizeTableColumnResizing = (editor) => getTableColumnResizingBehaviour(editor) === "resizetable";
      const getTableSizingMode = option("table_sizing_mode");
      const isTablePercentagesForced = (editor) => getTableSizingMode(editor) === "relative";
      const isTablePixelsForced = (editor) => getTableSizingMode(editor) === "fixed";
      const isTableResponsiveForced = (editor) => getTableSizingMode(editor) === "responsive";
      const hasTableResizeBars = option("table_resize_bars");
      const shouldStyleWithCss = option("table_style_by_css");
      const shouldMergeContentOnPaste = option("table_merge_content_on_paste");
      const getTableDefaultAttributes = (editor) => {
        const options = editor.options;
        const defaultAttributes = options.get("table_default_attributes");
        return options.isSet("table_default_attributes") ? defaultAttributes : determineDefaultTableAttributes(editor, defaultAttributes);
      };
      const getTableDefaultStyles = (editor) => {
        const options = editor.options;
        const defaultStyles = options.get("table_default_styles");
        return options.isSet("table_default_styles") ? defaultStyles : determineDefaultTableStyles(editor, defaultStyles);
      };
      const tableUseColumnGroup = option("table_use_colgroups");
      const closest = (target) => closest$1(target, "[contenteditable]");
      const isEditable$1 = (element, assumeEditable = false) => {
        if (inBody(element)) {
          return element.dom.isContentEditable;
        } else {
          return closest(element).fold(constant(assumeEditable), (editable) => getRaw(editable) === "true");
        }
      };
      const getRaw = (element) => element.dom.contentEditable;
      const getBody = (editor) => SugarElement.fromDom(editor.getBody());
      const getIsRoot = (editor) => (element) => eq$1(element, getBody(editor));
      const removeDataStyle = (table2) => {
        remove$7(table2, "data-mce-style");
        const removeStyleAttribute = (element) => remove$7(element, "data-mce-style");
        each$2(cells$1(table2), removeStyleAttribute);
        each$2(columns$1(table2), removeStyleAttribute);
        each$2(rows$1(table2), removeStyleAttribute);
      };
      const getSelectionStart = (editor) => SugarElement.fromDom(editor.selection.getStart());
      const getPixelWidth = (elm) => elm.getBoundingClientRect().width;
      const getPixelHeight = (elm) => elm.getBoundingClientRect().height;
      const getRawWidth = (editor, elm) => {
        const raw = editor.dom.getStyle(elm, "width") || editor.dom.getAttrib(elm, "width");
        return Optional.from(raw).filter(isNotEmpty);
      };
      const isPercentage$1 = (value2) => /^(\d+(\.\d+)?)%$/.test(value2);
      const isPixel = (value2) => /^(\d+(\.\d+)?)px$/.test(value2);
      const isInEditableContext$1 = (cell2) => closest$2(cell2, isTag("table")).exists(isEditable$1);
      const inSelection = (bounds2, detail2) => {
        const leftEdge = detail2.column;
        const rightEdge = detail2.column + detail2.colspan - 1;
        const topEdge = detail2.row;
        const bottomEdge = detail2.row + detail2.rowspan - 1;
        return leftEdge <= bounds2.finishCol && rightEdge >= bounds2.startCol && (topEdge <= bounds2.finishRow && bottomEdge >= bounds2.startRow);
      };
      const isWithin = (bounds2, detail2) => {
        return detail2.column >= bounds2.startCol && detail2.column + detail2.colspan - 1 <= bounds2.finishCol && detail2.row >= bounds2.startRow && detail2.row + detail2.rowspan - 1 <= bounds2.finishRow;
      };
      const isRectangular = (warehouse, bounds2) => {
        let isRect = true;
        const detailIsWithin = curry(isWithin, bounds2);
        for (let i = bounds2.startRow; i <= bounds2.finishRow; i++) {
          for (let j = bounds2.startCol; j <= bounds2.finishCol; j++) {
            isRect = isRect && Warehouse.getAt(warehouse, i, j).exists(detailIsWithin);
          }
        }
        return isRect ? Optional.some(bounds2) : Optional.none();
      };
      const getBounds = (detailA, detailB) => {
        return bounds(Math.min(detailA.row, detailB.row), Math.min(detailA.column, detailB.column), Math.max(detailA.row + detailA.rowspan - 1, detailB.row + detailB.rowspan - 1), Math.max(detailA.column + detailA.colspan - 1, detailB.column + detailB.colspan - 1));
      };
      const getAnyBox = (warehouse, startCell, finishCell) => {
        const startCoords = Warehouse.findItem(warehouse, startCell, eq$1);
        const finishCoords = Warehouse.findItem(warehouse, finishCell, eq$1);
        return startCoords.bind((sc) => {
          return finishCoords.map((fc) => {
            return getBounds(sc, fc);
          });
        });
      };
      const getBox$1 = (warehouse, startCell, finishCell) => {
        return getAnyBox(warehouse, startCell, finishCell).bind((bounds2) => {
          return isRectangular(warehouse, bounds2);
        });
      };
      const moveBy$1 = (warehouse, cell2, row2, column) => {
        return Warehouse.findItem(warehouse, cell2, eq$1).bind((detail2) => {
          const startRow = row2 > 0 ? detail2.row + detail2.rowspan - 1 : detail2.row;
          const startCol = column > 0 ? detail2.column + detail2.colspan - 1 : detail2.column;
          const dest = Warehouse.getAt(warehouse, startRow + row2, startCol + column);
          return dest.map((d) => {
            return d.element;
          });
        });
      };
      const intercepts$1 = (warehouse, start, finish) => {
        return getAnyBox(warehouse, start, finish).map((bounds2) => {
          const inside = Warehouse.filterItems(warehouse, curry(inSelection, bounds2));
          return map$1(inside, (detail2) => {
            return detail2.element;
          });
        });
      };
      const parentCell = (warehouse, innerCell) => {
        const isContainedBy = (c1, c2) => {
          return contains$1(c2, c1);
        };
        return Warehouse.findItem(warehouse, innerCell, isContainedBy).map((detail2) => {
          return detail2.element;
        });
      };
      const moveBy = (cell2, deltaRow, deltaColumn) => {
        return table(cell2).bind((table2) => {
          const warehouse = getWarehouse(table2);
          return moveBy$1(warehouse, cell2, deltaRow, deltaColumn);
        });
      };
      const intercepts = (table2, first2, last2) => {
        const warehouse = getWarehouse(table2);
        return intercepts$1(warehouse, first2, last2);
      };
      const nestedIntercepts = (table2, first2, firstTable, last2, lastTable) => {
        const warehouse = getWarehouse(table2);
        const optStartCell = eq$1(table2, firstTable) ? Optional.some(first2) : parentCell(warehouse, first2);
        const optLastCell = eq$1(table2, lastTable) ? Optional.some(last2) : parentCell(warehouse, last2);
        return optStartCell.bind((startCell) => optLastCell.bind((lastCell) => intercepts$1(warehouse, startCell, lastCell)));
      };
      const getBox = (table2, first2, last2) => {
        const warehouse = getWarehouse(table2);
        return getBox$1(warehouse, first2, last2);
      };
      const getWarehouse = Warehouse.fromTable;
      var TagBoundaries = [
        "body",
        "p",
        "div",
        "article",
        "aside",
        "figcaption",
        "figure",
        "footer",
        "header",
        "nav",
        "section",
        "ol",
        "ul",
        "li",
        "table",
        "thead",
        "tbody",
        "tfoot",
        "caption",
        "tr",
        "td",
        "th",
        "h1",
        "h2",
        "h3",
        "h4",
        "h5",
        "h6",
        "blockquote",
        "pre",
        "address"
      ];
      var DomUniverse = () => {
        const clone2 = (element) => {
          return SugarElement.fromDom(element.dom.cloneNode(false));
        };
        const document2 = (element) => documentOrOwner(element).dom;
        const isBoundary = (element) => {
          if (!isElement(element)) {
            return false;
          }
          if (name(element) === "body") {
            return true;
          }
          return contains$2(TagBoundaries, name(element));
        };
        const isEmptyTag2 = (element) => {
          if (!isElement(element)) {
            return false;
          }
          return contains$2([
            "br",
            "img",
            "hr",
            "input"
          ], name(element));
        };
        const isNonEditable = (element) => isElement(element) && get$b(element, "contenteditable") === "false";
        const comparePosition = (element, other) => {
          return element.dom.compareDocumentPosition(other.dom);
        };
        const copyAttributesTo = (source, destination) => {
          const as = clone$2(source);
          setAll$1(destination, as);
        };
        const isSpecial = (element) => {
          const tag = name(element);
          return contains$2([
            "script",
            "noscript",
            "iframe",
            "noframes",
            "noembed",
            "title",
            "style",
            "textarea",
            "xmp"
          ], tag);
        };
        const getLanguage = (element) => isElement(element) ? getOpt(element, "lang") : Optional.none();
        return {
          up: constant({
            selector: ancestor$1,
            closest: closest$1,
            predicate: ancestor$2,
            all: parents
          }),
          down: constant({
            selector: descendants,
            predicate: descendants$1
          }),
          styles: constant({
            get: get$a,
            getRaw: getRaw$2,
            set: set$1,
            remove: remove$5
          }),
          attrs: constant({
            get: get$b,
            set: set$2,
            remove: remove$7,
            copyTo: copyAttributesTo
          }),
          insert: constant({
            before: before$3,
            after: after$5,
            afterAll: after$4,
            append: append$1,
            appendAll: append,
            prepend,
            wrap
          }),
          remove: constant({
            unwrap,
            remove: remove$6
          }),
          create: constant({
            nu: SugarElement.fromTag,
            clone: clone2,
            text: SugarElement.fromText
          }),
          query: constant({
            comparePosition,
            prevSibling,
            nextSibling
          }),
          property: constant({
            children: children$2,
            name,
            parent,
            document: document2,
            isText,
            isComment,
            isElement,
            isSpecial,
            getLanguage,
            getText: get$6,
            setText: set,
            isBoundary,
            isEmptyTag: isEmptyTag2,
            isNonEditable
          }),
          eq: eq$1,
          is: is$1
        };
      };
      const all = (universe2, look, elements, f) => {
        const head2 = elements[0];
        const tail = elements.slice(1);
        return f(universe2, look, head2, tail);
      };
      const oneAll = (universe2, look, elements) => {
        return elements.length > 0 ? all(universe2, look, elements, unsafeOne) : Optional.none();
      };
      const unsafeOne = (universe2, look, head2, tail) => {
        const start = look(universe2, head2);
        return foldr(tail, (b, a) => {
          const current = look(universe2, a);
          return commonElement(universe2, b, current);
        }, start);
      };
      const commonElement = (universe2, start, end) => {
        return start.bind((s) => {
          return end.filter(curry(universe2.eq, s));
        });
      };
      const eq = (universe2, item) => {
        return curry(universe2.eq, item);
      };
      const ancestors$2 = (universe2, start, end, isRoot = never) => {
        const ps1 = [start].concat(universe2.up().all(start));
        const ps2 = [end].concat(universe2.up().all(end));
        const prune2 = (path2) => {
          const index = findIndex(path2, isRoot);
          return index.fold(() => {
            return path2;
          }, (ind) => {
            return path2.slice(0, ind + 1);
          });
        };
        const pruned1 = prune2(ps1);
        const pruned2 = prune2(ps2);
        const shared = find$1(pruned1, (x) => {
          return exists(pruned2, eq(universe2, x));
        });
        return {
          firstpath: pruned1,
          secondpath: pruned2,
          shared
        };
      };
      const sharedOne$1 = oneAll;
      const ancestors$1 = ancestors$2;
      const universe$3 = DomUniverse();
      const sharedOne = (look, elements) => {
        return sharedOne$1(universe$3, (_universe, element) => {
          return look(element);
        }, elements);
      };
      const ancestors = (start, finish, isRoot) => {
        return ancestors$1(universe$3, start, finish, isRoot);
      };
      const lookupTable = (container) => {
        return ancestor$1(container, "table");
      };
      const identify = (start, finish, isRoot) => {
        const getIsRoot2 = (rootTable) => {
          return (element) => {
            return isRoot !== void 0 && isRoot(element) || eq$1(element, rootTable);
          };
        };
        if (eq$1(start, finish)) {
          return Optional.some({
            boxes: Optional.some([start]),
            start,
            finish
          });
        } else {
          return lookupTable(start).bind((startTable) => {
            return lookupTable(finish).bind((finishTable) => {
              if (eq$1(startTable, finishTable)) {
                return Optional.some({
                  boxes: intercepts(startTable, start, finish),
                  start,
                  finish
                });
              } else if (contains$1(startTable, finishTable)) {
                const ancestorCells = ancestors$3(finish, "td,th", getIsRoot2(startTable));
                const finishCell = ancestorCells.length > 0 ? ancestorCells[ancestorCells.length - 1] : finish;
                return Optional.some({
                  boxes: nestedIntercepts(startTable, start, startTable, finish, finishTable),
                  start,
                  finish: finishCell
                });
              } else if (contains$1(finishTable, startTable)) {
                const ancestorCells = ancestors$3(start, "td,th", getIsRoot2(finishTable));
                const startCell = ancestorCells.length > 0 ? ancestorCells[ancestorCells.length - 1] : start;
                return Optional.some({
                  boxes: nestedIntercepts(finishTable, start, startTable, finish, finishTable),
                  start,
                  finish: startCell
                });
              } else {
                return ancestors(start, finish).shared.bind((lca) => {
                  return closest$1(lca, "table", isRoot).bind((lcaTable) => {
                    const finishAncestorCells = ancestors$3(finish, "td,th", getIsRoot2(lcaTable));
                    const finishCell = finishAncestorCells.length > 0 ? finishAncestorCells[finishAncestorCells.length - 1] : finish;
                    const startAncestorCells = ancestors$3(start, "td,th", getIsRoot2(lcaTable));
                    const startCell = startAncestorCells.length > 0 ? startAncestorCells[startAncestorCells.length - 1] : start;
                    return Optional.some({
                      boxes: nestedIntercepts(lcaTable, start, startTable, finish, finishTable),
                      start: startCell,
                      finish: finishCell
                    });
                  });
                });
              }
            });
          });
        }
      };
      const retrieve$1 = (container, selector) => {
        const sels = descendants(container, selector);
        return sels.length > 0 ? Optional.some(sels) : Optional.none();
      };
      const getLast = (boxes, lastSelectedSelector) => {
        return find$1(boxes, (box) => {
          return is$2(box, lastSelectedSelector);
        });
      };
      const getEdges = (container, firstSelectedSelector, lastSelectedSelector) => {
        return descendant(container, firstSelectedSelector).bind((first2) => {
          return descendant(container, lastSelectedSelector).bind((last2) => {
            return sharedOne(lookupTable, [
              first2,
              last2
            ]).map((table2) => {
              return {
                first: first2,
                last: last2,
                table: table2
              };
            });
          });
        });
      };
      const expandTo = (finish, firstSelectedSelector) => {
        return ancestor$1(finish, "table").bind((table2) => {
          return descendant(table2, firstSelectedSelector).bind((start) => {
            return identify(start, finish).bind((identified) => {
              return identified.boxes.map((boxes) => {
                return {
                  boxes,
                  start: identified.start,
                  finish: identified.finish
                };
              });
            });
          });
        });
      };
      const shiftSelection = (boxes, deltaRow, deltaColumn, firstSelectedSelector, lastSelectedSelector) => {
        return getLast(boxes, lastSelectedSelector).bind((last2) => {
          return moveBy(last2, deltaRow, deltaColumn).bind((finish) => {
            return expandTo(finish, firstSelectedSelector);
          });
        });
      };
      const retrieve = (container, selector) => {
        return retrieve$1(container, selector);
      };
      const retrieveBox = (container, firstSelectedSelector, lastSelectedSelector) => {
        return getEdges(container, firstSelectedSelector, lastSelectedSelector).bind((edges) => {
          const isRoot = (ancestor2) => {
            return eq$1(container, ancestor2);
          };
          const sectionSelector = "thead,tfoot,tbody,table";
          const firstAncestor = ancestor$1(edges.first, sectionSelector, isRoot);
          const lastAncestor = ancestor$1(edges.last, sectionSelector, isRoot);
          return firstAncestor.bind((fA) => {
            return lastAncestor.bind((lA) => {
              return eq$1(fA, lA) ? getBox(edges.table, edges.first, edges.last) : Optional.none();
            });
          });
        });
      };
      const selection = identity;
      const unmergable = (selectedCells) => {
        const hasSpan = (elem, type2) => getOpt(elem, type2).exists((span) => parseInt(span, 10) > 1);
        const hasRowOrColSpan = (elem) => hasSpan(elem, "rowspan") || hasSpan(elem, "colspan");
        return selectedCells.length > 0 && forall(selectedCells, hasRowOrColSpan) ? Optional.some(selectedCells) : Optional.none();
      };
      const mergable = (table2, selectedCells, ephemera2) => {
        if (selectedCells.length <= 1) {
          return Optional.none();
        } else {
          return retrieveBox(table2, ephemera2.firstSelectedSelector, ephemera2.lastSelectedSelector).map((bounds2) => ({
            bounds: bounds2,
            cells: selectedCells
          }));
        }
      };
      const strSelected = "data-mce-selected";
      const strSelectedSelector = "td[" + strSelected + "],th[" + strSelected + "]";
      const strAttributeSelector = "[" + strSelected + "]";
      const strFirstSelected = "data-mce-first-selected";
      const strFirstSelectedSelector = "td[" + strFirstSelected + "],th[" + strFirstSelected + "]";
      const strLastSelected = "data-mce-last-selected";
      const strLastSelectedSelector = "td[" + strLastSelected + "],th[" + strLastSelected + "]";
      const attributeSelector = strAttributeSelector;
      const ephemera = {
        selected: strSelected,
        selectedSelector: strSelectedSelector,
        firstSelected: strFirstSelected,
        firstSelectedSelector: strFirstSelectedSelector,
        lastSelected: strLastSelected,
        lastSelectedSelector: strLastSelectedSelector
      };
      const forMenu = (selectedCells, table2, cell2) => ({
        element: cell2,
        mergable: mergable(table2, selectedCells, ephemera),
        unmergable: unmergable(selectedCells),
        selection: selection(selectedCells)
      });
      const paste = (element, clipboard, generators) => ({
        element,
        clipboard,
        generators
      });
      const pasteRows = (selectedCells, _cell, clipboard, generators) => ({
        selection: selection(selectedCells),
        clipboard,
        generators
      });
      const getSelectionCellFallback = (element) => table(element).bind((table2) => retrieve(table2, ephemera.firstSelectedSelector)).fold(constant(element), (cells2) => cells2[0]);
      const getSelectionFromSelector = (selector) => (initCell, isRoot) => {
        const cellName = name(initCell);
        const cell2 = cellName === "col" || cellName === "colgroup" ? getSelectionCellFallback(initCell) : initCell;
        return closest$1(cell2, selector, isRoot);
      };
      const getSelectionCellOrCaption = getSelectionFromSelector("th,td,caption");
      const getSelectionCell = getSelectionFromSelector("th,td");
      const getCellsFromSelection = (editor) => fromDom(editor.model.table.getSelectedCells());
      const getCellsFromFakeSelection = (editor) => filter$2(getCellsFromSelection(editor), (cell2) => is$2(cell2, ephemera.selectedSelector));
      const extractSelected = (cells2) => {
        return table(cells2[0]).map((table2) => {
          const replica = extract$1(table2, attributeSelector);
          removeDataStyle(replica);
          return [replica];
        });
      };
      const serializeElements = (editor, elements) => map$1(elements, (elm) => editor.selection.serializer.serialize(elm.dom, {})).join("");
      const getTextContent = (elements) => map$1(elements, (element) => element.dom.innerText).join("");
      const registerEvents = (editor, actions) => {
        editor.on("BeforeGetContent", (e) => {
          const multiCellContext = (cells2) => {
            e.preventDefault();
            extractSelected(cells2).each((elements) => {
              e.content = e.format === "text" ? getTextContent(elements) : serializeElements(editor, elements);
            });
          };
          if (e.selection === true) {
            const cells2 = getCellsFromFakeSelection(editor);
            if (cells2.length >= 1) {
              multiCellContext(cells2);
            }
          }
        });
        editor.on("BeforeSetContent", (e) => {
          if (e.selection === true && e.paste === true) {
            const selectedCells = getCellsFromSelection(editor);
            head(selectedCells).each((cell2) => {
              table(cell2).each((table2) => {
                const elements = filter$2(fromHtml(e.content), (content) => {
                  return name(content) !== "meta";
                });
                const isTable2 = isTag("table");
                if (shouldMergeContentOnPaste(editor) && elements.length === 1 && isTable2(elements[0])) {
                  e.preventDefault();
                  const doc = SugarElement.fromDom(editor.getDoc());
                  const generators = paste$1(doc);
                  const targets = paste(cell2, elements[0], generators);
                  actions.pasteCells(table2, targets).each(() => {
                    editor.focus();
                  });
                }
              });
            });
          }
        });
      };
      const point = (element, offset) => ({
        element,
        offset
      });
      const scan$1 = (universe2, element, direction) => {
        if (universe2.property().isText(element) && universe2.property().getText(element).trim().length === 0 || universe2.property().isComment(element)) {
          return direction(element).bind((elem) => {
            return scan$1(universe2, elem, direction).orThunk(() => {
              return Optional.some(elem);
            });
          });
        } else {
          return Optional.none();
        }
      };
      const toEnd = (universe2, element) => {
        if (universe2.property().isText(element)) {
          return universe2.property().getText(element).length;
        }
        const children2 = universe2.property().children(element);
        return children2.length;
      };
      const freefallRtl$2 = (universe2, element) => {
        const candidate = scan$1(universe2, element, universe2.query().prevSibling).getOr(element);
        if (universe2.property().isText(candidate)) {
          return point(candidate, toEnd(universe2, candidate));
        }
        const children2 = universe2.property().children(candidate);
        return children2.length > 0 ? freefallRtl$2(universe2, children2[children2.length - 1]) : point(candidate, toEnd(universe2, candidate));
      };
      const freefallRtl$1 = freefallRtl$2;
      const universe$2 = DomUniverse();
      const freefallRtl = (element) => {
        return freefallRtl$1(universe$2, element);
      };
      const halve = (main, other) => {
        if (!hasColspan(main)) {
          const width2 = getGenericWidth(main);
          width2.each((w) => {
            const newWidth = w.value / 2;
            setGenericWidth(main, newWidth, w.unit);
            setGenericWidth(other, newWidth, w.unit);
          });
        }
      };
      const zero = (array) => map$1(array, constant(0));
      const surround = (sizes, startIndex, endIndex, results, f) => f(sizes.slice(0, startIndex)).concat(results).concat(f(sizes.slice(endIndex)));
      const clampDeltaHelper = (predicate) => (sizes, index, delta, minCellSize) => {
        if (!predicate(delta)) {
          return delta;
        } else {
          const newSize = Math.max(minCellSize, sizes[index] - Math.abs(delta));
          const diff = Math.abs(newSize - sizes[index]);
          return delta >= 0 ? diff : -diff;
        }
      };
      const clampNegativeDelta = clampDeltaHelper((delta) => delta < 0);
      const clampDelta = clampDeltaHelper(always);
      const resizeTable = () => {
        const calcFixedDeltas = (sizes, index, next, delta, minCellSize) => {
          const clampedDelta = clampNegativeDelta(sizes, index, delta, minCellSize);
          return surround(sizes, index, next + 1, [
            clampedDelta,
            0
          ], zero);
        };
        const calcRelativeDeltas = (sizes, index, delta, minCellSize) => {
          const ratio = (100 + delta) / 100;
          const newThis = Math.max(minCellSize, (sizes[index] + delta) / ratio);
          return map$1(sizes, (size, idx) => {
            const newSize = idx === index ? newThis : size / ratio;
            return newSize - size;
          });
        };
        const calcLeftEdgeDeltas = (sizes, index, next, delta, minCellSize, isRelative) => {
          if (isRelative) {
            return calcRelativeDeltas(sizes, index, delta, minCellSize);
          } else {
            return calcFixedDeltas(sizes, index, next, delta, minCellSize);
          }
        };
        const calcMiddleDeltas = (sizes, _prev, index, next, delta, minCellSize, isRelative) => calcLeftEdgeDeltas(sizes, index, next, delta, minCellSize, isRelative);
        const resizeTable2 = (resizer, delta) => resizer(delta);
        const calcRightEdgeDeltas = (sizes, _prev, index, delta, minCellSize, isRelative) => {
          if (isRelative) {
            return calcRelativeDeltas(sizes, index, delta, minCellSize);
          } else {
            const clampedDelta = clampNegativeDelta(sizes, index, delta, minCellSize);
            return zero(sizes.slice(0, index)).concat([clampedDelta]);
          }
        };
        const calcRedestributedWidths = (sizes, totalWidth, pixelDelta, isRelative) => {
          if (isRelative) {
            const tableWidth = totalWidth + pixelDelta;
            const ratio = tableWidth / totalWidth;
            const newSizes = map$1(sizes, (size) => size / ratio);
            return {
              delta: ratio * 100 - 100,
              newSizes
            };
          } else {
            return {
              delta: pixelDelta,
              newSizes: sizes
            };
          }
        };
        return {
          resizeTable: resizeTable2,
          clampTableDelta: clampNegativeDelta,
          calcLeftEdgeDeltas,
          calcMiddleDeltas,
          calcRightEdgeDeltas,
          calcRedestributedWidths
        };
      };
      const preserveTable = () => {
        const calcLeftEdgeDeltas = (sizes, index, next, delta, minCellSize) => {
          const idx = delta >= 0 ? next : index;
          const clampedDelta = clampDelta(sizes, idx, delta, minCellSize);
          return surround(sizes, index, next + 1, [
            clampedDelta,
            -clampedDelta
          ], zero);
        };
        const calcMiddleDeltas = (sizes, _prev, index, next, delta, minCellSize) => calcLeftEdgeDeltas(sizes, index, next, delta, minCellSize);
        const resizeTable2 = (resizer, delta, isLastColumn) => {
          if (isLastColumn) {
            resizer(delta);
          }
        };
        const calcRightEdgeDeltas = (sizes, _prev, _index, delta, _minCellSize, isRelative) => {
          if (isRelative) {
            return zero(sizes);
          } else {
            const diff = delta / sizes.length;
            return map$1(sizes, constant(diff));
          }
        };
        const clampTableDelta = (sizes, index, delta, minCellSize, isLastColumn) => {
          if (isLastColumn) {
            if (delta >= 0) {
              return delta;
            } else {
              const maxDelta = foldl(sizes, (a, b) => a + b - minCellSize, 0);
              return Math.max(-maxDelta, delta);
            }
          } else {
            return clampNegativeDelta(sizes, index, delta, minCellSize);
          }
        };
        const calcRedestributedWidths = (sizes, _totalWidth, _pixelDelta, _isRelative) => ({
          delta: 0,
          newSizes: sizes
        });
        return {
          resizeTable: resizeTable2,
          clampTableDelta,
          calcLeftEdgeDeltas,
          calcMiddleDeltas,
          calcRightEdgeDeltas,
          calcRedestributedWidths
        };
      };
      const getGridSize = (table2) => {
        const warehouse = Warehouse.fromTable(table2);
        return warehouse.grid;
      };
      const isHeaderCell = isTag("th");
      const isHeaderCells = (cells2) => forall(cells2, (cell2) => isHeaderCell(cell2.element));
      const getRowHeaderType = (isHeaderRow, isHeaderCells2) => {
        if (isHeaderRow && isHeaderCells2) {
          return "sectionCells";
        } else if (isHeaderRow) {
          return "section";
        } else {
          return "cells";
        }
      };
      const getRowType = (row2) => {
        const isHeaderRow = row2.section === "thead";
        const isHeaderCells2 = is(findCommonCellType(row2.cells), "th");
        if (row2.section === "tfoot") {
          return { type: "footer" };
        } else if (isHeaderRow || isHeaderCells2) {
          return {
            type: "header",
            subType: getRowHeaderType(isHeaderRow, isHeaderCells2)
          };
        } else {
          return { type: "body" };
        }
      };
      const findCommonCellType = (cells2) => {
        const headerCells = filter$2(cells2, (cell2) => isHeaderCell(cell2.element));
        if (headerCells.length === 0) {
          return Optional.some("td");
        } else if (headerCells.length === cells2.length) {
          return Optional.some("th");
        } else {
          return Optional.none();
        }
      };
      const findCommonRowType = (rows2) => {
        const rowTypes = map$1(rows2, (row2) => getRowType(row2).type);
        const hasHeader = contains$2(rowTypes, "header");
        const hasFooter = contains$2(rowTypes, "footer");
        if (!hasHeader && !hasFooter) {
          return Optional.some("body");
        } else {
          const hasBody = contains$2(rowTypes, "body");
          if (hasHeader && !hasBody && !hasFooter) {
            return Optional.some("header");
          } else if (!hasHeader && !hasBody && hasFooter) {
            return Optional.some("footer");
          } else {
            return Optional.none();
          }
        }
      };
      const findTableRowHeaderType = (warehouse) => findMap(warehouse.all, (row2) => {
        const rowType = getRowType(row2);
        return rowType.type === "header" ? Optional.from(rowType.subType) : Optional.none();
      });
      const transformCell = (cell2, comparator, substitution) => elementnew(substitution(cell2.element, comparator), true, cell2.isLocked);
      const transformRow = (row2, section2) => row2.section !== section2 ? rowcells(row2.element, row2.cells, section2, row2.isNew) : row2;
      const section = () => ({
        transformRow,
        transformCell: (cell2, comparator, substitution) => {
          const newCell = substitution(cell2.element, comparator);
          const fixedCell = name(newCell) !== "td" ? mutate$1(newCell, "td") : newCell;
          return elementnew(fixedCell, cell2.isNew, cell2.isLocked);
        }
      });
      const sectionCells = () => ({
        transformRow,
        transformCell
      });
      const cells = () => ({
        transformRow: (row2, section2) => {
          const newSection = section2 === "thead" ? "tbody" : section2;
          return transformRow(row2, newSection);
        },
        transformCell
      });
      const fallback = () => ({
        transformRow: identity,
        transformCell
      });
      const getTableSectionType = (table2, fallback2) => {
        const warehouse = Warehouse.fromTable(table2);
        const type2 = findTableRowHeaderType(warehouse).getOr(fallback2);
        switch (type2) {
          case "section":
            return section();
          case "sectionCells":
            return sectionCells();
          case "cells":
            return cells();
        }
      };
      const TableSection = {
        getTableSectionType,
        section,
        sectionCells,
        cells,
        fallback
      };
      const setIfNot = (element, property, value2, ignore) => {
        if (value2 === ignore) {
          remove$7(element, property);
        } else {
          set$2(element, property, value2);
        }
      };
      const insert$1 = (table2, selector, element) => {
        last$2(children(table2, selector)).fold(() => prepend(table2, element), (child2) => after$5(child2, element));
      };
      const generateSection = (table2, sectionName) => {
        const section2 = child(table2, sectionName).getOrThunk(() => {
          const newSection = SugarElement.fromTag(sectionName, owner(table2).dom);
          if (sectionName === "thead") {
            insert$1(table2, "caption,colgroup", newSection);
          } else if (sectionName === "colgroup") {
            insert$1(table2, "caption", newSection);
          } else {
            append$1(table2, newSection);
          }
          return newSection;
        });
        empty(section2);
        return section2;
      };
      const render$1 = (table2, grid2) => {
        const newRows = [];
        const newCells = [];
        const syncRows = (gridSection) => map$1(gridSection, (row2) => {
          if (row2.isNew) {
            newRows.push(row2.element);
          }
          const tr = row2.element;
          empty(tr);
          each$2(row2.cells, (cell2) => {
            if (cell2.isNew) {
              newCells.push(cell2.element);
            }
            setIfNot(cell2.element, "colspan", cell2.colspan, 1);
            setIfNot(cell2.element, "rowspan", cell2.rowspan, 1);
            append$1(tr, cell2.element);
          });
          return tr;
        });
        const syncColGroup = (gridSection) => bind$2(gridSection, (colGroup) => map$1(colGroup.cells, (col2) => {
          setIfNot(col2.element, "span", col2.colspan, 1);
          return col2.element;
        }));
        const renderSection = (gridSection, sectionName) => {
          const section2 = generateSection(table2, sectionName);
          const sync2 = sectionName === "colgroup" ? syncColGroup : syncRows;
          const sectionElems = sync2(gridSection);
          append(section2, sectionElems);
        };
        const removeSection = (sectionName) => {
          child(table2, sectionName).each(remove$6);
        };
        const renderOrRemoveSection = (gridSection, sectionName) => {
          if (gridSection.length > 0) {
            renderSection(gridSection, sectionName);
          } else {
            removeSection(sectionName);
          }
        };
        const headSection = [];
        const bodySection = [];
        const footSection = [];
        const columnGroupsSection = [];
        each$2(grid2, (row2) => {
          switch (row2.section) {
            case "thead":
              headSection.push(row2);
              break;
            case "tbody":
              bodySection.push(row2);
              break;
            case "tfoot":
              footSection.push(row2);
              break;
            case "colgroup":
              columnGroupsSection.push(row2);
              break;
          }
        });
        renderOrRemoveSection(columnGroupsSection, "colgroup");
        renderOrRemoveSection(headSection, "thead");
        renderOrRemoveSection(bodySection, "tbody");
        renderOrRemoveSection(footSection, "tfoot");
        return {
          newRows,
          newCells
        };
      };
      const copy = (grid2) => map$1(grid2, (row2) => {
        const tr = shallow(row2.element);
        each$2(row2.cells, (cell2) => {
          const clonedCell = deep(cell2.element);
          setIfNot(clonedCell, "colspan", cell2.colspan, 1);
          setIfNot(clonedCell, "rowspan", cell2.rowspan, 1);
          append$1(tr, clonedCell);
        });
        return tr;
      });
      const getColumn = (grid2, index) => {
        return map$1(grid2, (row2) => {
          return getCell(row2, index);
        });
      };
      const getRow = (grid2, index) => {
        return grid2[index];
      };
      const findDiff = (xs, comp) => {
        if (xs.length === 0) {
          return 0;
        }
        const first2 = xs[0];
        const index = findIndex(xs, (x) => {
          return !comp(first2.element, x.element);
        });
        return index.getOr(xs.length);
      };
      const subgrid = (grid2, row2, column, comparator) => {
        const gridRow = getRow(grid2, row2);
        const isColRow = gridRow.section === "colgroup";
        const colspan = findDiff(gridRow.cells.slice(column), comparator);
        const rowspan = isColRow ? 1 : findDiff(getColumn(grid2.slice(row2), column), comparator);
        return {
          colspan,
          rowspan
        };
      };
      const toDetails = (grid2, comparator) => {
        const seen = map$1(grid2, (row2) => map$1(row2.cells, never));
        const updateSeen = (rowIndex, columnIndex, rowspan, colspan) => {
          for (let row2 = rowIndex; row2 < rowIndex + rowspan; row2++) {
            for (let column = columnIndex; column < columnIndex + colspan; column++) {
              seen[row2][column] = true;
            }
          }
        };
        return map$1(grid2, (row2, rowIndex) => {
          const details = bind$2(row2.cells, (cell2, columnIndex) => {
            if (seen[rowIndex][columnIndex] === false) {
              const result = subgrid(grid2, rowIndex, columnIndex, comparator);
              updateSeen(rowIndex, columnIndex, result.rowspan, result.colspan);
              return [detailnew(cell2.element, result.rowspan, result.colspan, cell2.isNew)];
            } else {
              return [];
            }
          });
          return rowdetailnew(row2.element, details, row2.section, row2.isNew);
        });
      };
      const toGrid = (warehouse, generators, isNew) => {
        const grid2 = [];
        each$2(warehouse.colgroups, (colgroup2) => {
          const colgroupCols = [];
          for (let columnIndex = 0; columnIndex < warehouse.grid.columns; columnIndex++) {
            const element = Warehouse.getColumnAt(warehouse, columnIndex).map((column) => elementnew(column.element, isNew, false)).getOrThunk(() => elementnew(generators.colGap(), true, false));
            colgroupCols.push(element);
          }
          grid2.push(rowcells(colgroup2.element, colgroupCols, "colgroup", isNew));
        });
        for (let rowIndex = 0; rowIndex < warehouse.grid.rows; rowIndex++) {
          const rowCells = [];
          for (let columnIndex = 0; columnIndex < warehouse.grid.columns; columnIndex++) {
            const element = Warehouse.getAt(warehouse, rowIndex, columnIndex).map((item) => elementnew(item.element, isNew, item.isLocked)).getOrThunk(() => elementnew(generators.gap(), true, false));
            rowCells.push(element);
          }
          const rowDetail = warehouse.all[rowIndex];
          const row2 = rowcells(rowDetail.element, rowCells, rowDetail.section, isNew);
          grid2.push(row2);
        }
        return grid2;
      };
      const fromWarehouse = (warehouse, generators) => toGrid(warehouse, generators, false);
      const toDetailList = (grid2) => toDetails(grid2, eq$1);
      const findInWarehouse = (warehouse, element) => findMap(warehouse.all, (r2) => find$1(r2.cells, (e) => eq$1(element, e.element)));
      const extractCells = (warehouse, target, predicate) => {
        const details = map$1(target.selection, (cell$1) => {
          return cell(cell$1).bind((lc) => findInWarehouse(warehouse, lc)).filter(predicate);
        });
        const cells2 = cat(details);
        return someIf(cells2.length > 0, cells2);
      };
      const run = (operation, extract2, adjustment, postAction, genWrappers) => (table2, target, generators, behaviours) => {
        const warehouse = Warehouse.fromTable(table2);
        const tableSection = Optional.from(behaviours === null || behaviours === void 0 ? void 0 : behaviours.section).getOrThunk(TableSection.fallback);
        const output = extract2(warehouse, target).map((info) => {
          const model = fromWarehouse(warehouse, generators);
          const result = operation(model, info, eq$1, genWrappers(generators), tableSection);
          const lockedColumns = getLockedColumnsFromGrid(result.grid);
          const grid2 = toDetailList(result.grid);
          return {
            info,
            grid: grid2,
            cursor: result.cursor,
            lockedColumns
          };
        });
        return output.bind((out) => {
          const newElements = render$1(table2, out.grid);
          const tableSizing = Optional.from(behaviours === null || behaviours === void 0 ? void 0 : behaviours.sizing).getOrThunk(() => TableSize.getTableSize(table2));
          const resizing = Optional.from(behaviours === null || behaviours === void 0 ? void 0 : behaviours.resize).getOrThunk(preserveTable);
          adjustment(table2, out.grid, out.info, {
            sizing: tableSizing,
            resize: resizing,
            section: tableSection
          });
          postAction(table2);
          remove$7(table2, LOCKED_COL_ATTR);
          if (out.lockedColumns.length > 0) {
            set$2(table2, LOCKED_COL_ATTR, out.lockedColumns.join(","));
          }
          return Optional.some({
            cursor: out.cursor,
            newRows: newElements.newRows,
            newCells: newElements.newCells
          });
        });
      };
      const onPaste = (warehouse, target) => cell(target.element).bind((cell2) => findInWarehouse(warehouse, cell2).map((details) => {
        const value2 = {
          ...details,
          generators: target.generators,
          clipboard: target.clipboard
        };
        return value2;
      }));
      const onPasteByEditor = (warehouse, target) => extractCells(warehouse, target, always).map((cells2) => ({
        cells: cells2,
        generators: target.generators,
        clipboard: target.clipboard
      }));
      const onMergable = (_warehouse, target) => target.mergable;
      const onUnmergable = (_warehouse, target) => target.unmergable;
      const onCells = (warehouse, target) => extractCells(warehouse, target, always);
      const onUnlockedCells = (warehouse, target) => extractCells(warehouse, target, (detail2) => !detail2.isLocked);
      const isUnlockedTableCell = (warehouse, cell2) => findInWarehouse(warehouse, cell2).exists((detail2) => !detail2.isLocked);
      const allUnlocked = (warehouse, cells2) => forall(cells2, (cell2) => isUnlockedTableCell(warehouse, cell2));
      const onUnlockedMergable = (warehouse, target) => onMergable(warehouse, target).filter((mergeable) => allUnlocked(warehouse, mergeable.cells));
      const onUnlockedUnmergable = (warehouse, target) => onUnmergable(warehouse, target).filter((cells2) => allUnlocked(warehouse, cells2));
      const merge$2 = (grid2, bounds2, comparator, substitution) => {
        const rows2 = extractGridDetails(grid2).rows;
        if (rows2.length === 0) {
          return grid2;
        }
        for (let i = bounds2.startRow; i <= bounds2.finishRow; i++) {
          for (let j = bounds2.startCol; j <= bounds2.finishCol; j++) {
            const row2 = rows2[i];
            const isLocked = getCell(row2, j).isLocked;
            mutateCell(row2, j, elementnew(substitution(), false, isLocked));
          }
        }
        return grid2;
      };
      const unmerge = (grid2, target, comparator, substitution) => {
        const rows2 = extractGridDetails(grid2).rows;
        let first2 = true;
        for (let i = 0; i < rows2.length; i++) {
          for (let j = 0; j < cellLength(rows2[0]); j++) {
            const row2 = rows2[i];
            const currentCell = getCell(row2, j);
            const currentCellElm = currentCell.element;
            const isToReplace = comparator(currentCellElm, target);
            if (isToReplace && !first2) {
              mutateCell(row2, j, elementnew(substitution(), true, currentCell.isLocked));
            } else if (isToReplace) {
              first2 = false;
            }
          }
        }
        return grid2;
      };
      const uniqueCells = (row2, comparator) => {
        return foldl(row2, (rest, cell2) => {
          return exists(rest, (currentCell) => {
            return comparator(currentCell.element, cell2.element);
          }) ? rest : rest.concat([cell2]);
        }, []);
      };
      const splitCols = (grid2, index, comparator, substitution) => {
        if (index > 0 && index < grid2[0].cells.length) {
          each$2(grid2, (row2) => {
            const prevCell = row2.cells[index - 1];
            let offset = 0;
            const substitute = substitution();
            while (row2.cells.length > index + offset && comparator(prevCell.element, row2.cells[index + offset].element)) {
              mutateCell(row2, index + offset, elementnew(substitute, true, row2.cells[index + offset].isLocked));
              offset++;
            }
          });
        }
        return grid2;
      };
      const splitRows = (grid2, index, comparator, substitution) => {
        const rows2 = extractGridDetails(grid2).rows;
        if (index > 0 && index < rows2.length) {
          const rowPrevCells = rows2[index - 1].cells;
          const cells2 = uniqueCells(rowPrevCells, comparator);
          each$2(cells2, (cell2) => {
            let replacement = Optional.none();
            for (let i = index; i < rows2.length; i++) {
              for (let j = 0; j < cellLength(rows2[0]); j++) {
                const row2 = rows2[i];
                const current = getCell(row2, j);
                const isToReplace = comparator(current.element, cell2.element);
                if (isToReplace) {
                  if (replacement.isNone()) {
                    replacement = Optional.some(substitution());
                  }
                  replacement.each((sub) => {
                    mutateCell(row2, j, elementnew(sub, true, current.isLocked));
                  });
                }
              }
            }
          });
        }
        return grid2;
      };
      const value$1 = (value2) => {
        const applyHelper = (fn) => fn(value2);
        const constHelper = constant(value2);
        const outputHelper = () => output;
        const output = {
          tag: true,
          inner: value2,
          fold: (_onError, onValue) => onValue(value2),
          isValue: always,
          isError: never,
          map: (mapper) => Result.value(mapper(value2)),
          mapError: outputHelper,
          bind: applyHelper,
          exists: applyHelper,
          forall: applyHelper,
          getOr: constHelper,
          or: outputHelper,
          getOrThunk: constHelper,
          orThunk: outputHelper,
          getOrDie: constHelper,
          each: (fn) => {
            fn(value2);
          },
          toOptional: () => Optional.some(value2)
        };
        return output;
      };
      const error = (error2) => {
        const outputHelper = () => output;
        const output = {
          tag: false,
          inner: error2,
          fold: (onError, _onValue) => onError(error2),
          isValue: never,
          isError: always,
          map: outputHelper,
          mapError: (mapper) => Result.error(mapper(error2)),
          bind: outputHelper,
          exists: never,
          forall: always,
          getOr: identity,
          or: identity,
          getOrThunk: apply,
          orThunk: apply,
          getOrDie: die(String(error2)),
          each: noop,
          toOptional: Optional.none
        };
        return output;
      };
      const fromOption = (optional, err) => optional.fold(() => error(err), value$1);
      const Result = {
        value: value$1,
        error,
        fromOption
      };
      const measure = (startAddress, gridA, gridB) => {
        if (startAddress.row >= gridA.length || startAddress.column > cellLength(gridA[0])) {
          return Result.error("invalid start address out of table bounds, row: " + startAddress.row + ", column: " + startAddress.column);
        }
        const rowRemainder = gridA.slice(startAddress.row);
        const colRemainder = rowRemainder[0].cells.slice(startAddress.column);
        const colRequired = cellLength(gridB[0]);
        const rowRequired = gridB.length;
        return Result.value({
          rowDelta: rowRemainder.length - rowRequired,
          colDelta: colRemainder.length - colRequired
        });
      };
      const measureWidth = (gridA, gridB) => {
        const colLengthA = cellLength(gridA[0]);
        const colLengthB = cellLength(gridB[0]);
        return {
          rowDelta: 0,
          colDelta: colLengthA - colLengthB
        };
      };
      const measureHeight = (gridA, gridB) => {
        const rowLengthA = gridA.length;
        const rowLengthB = gridB.length;
        return {
          rowDelta: rowLengthA - rowLengthB,
          colDelta: 0
        };
      };
      const generateElements = (amount, row2, generators, isLocked) => {
        const generator = row2.section === "colgroup" ? generators.col : generators.cell;
        return range$1(amount, (idx) => elementnew(generator(), true, isLocked(idx)));
      };
      const rowFill = (grid2, amount, generators, lockedColumns) => {
        const exampleRow = grid2[grid2.length - 1];
        return grid2.concat(range$1(amount, () => {
          const generator = exampleRow.section === "colgroup" ? generators.colgroup : generators.row;
          const row2 = clone(exampleRow, generator, identity);
          const elements = generateElements(row2.cells.length, row2, generators, (idx) => has$1(lockedColumns, idx.toString()));
          return setCells(row2, elements);
        }));
      };
      const colFill = (grid2, amount, generators, startIndex) => map$1(grid2, (row2) => {
        const newChildren = generateElements(amount, row2, generators, never);
        return addCells(row2, startIndex, newChildren);
      });
      const lockedColFill = (grid2, generators, lockedColumns) => map$1(grid2, (row2) => {
        return foldl(lockedColumns, (acc, colNum) => {
          const newChild = generateElements(1, row2, generators, always)[0];
          return addCell(acc, colNum, newChild);
        }, row2);
      });
      const tailor = (gridA, delta, generators) => {
        const fillCols = delta.colDelta < 0 ? colFill : identity;
        const fillRows = delta.rowDelta < 0 ? rowFill : identity;
        const lockedColumns = getLockedColumnsFromGrid(gridA);
        const gridWidth = cellLength(gridA[0]);
        const isLastColLocked = exists(lockedColumns, (locked) => locked === gridWidth - 1);
        const modifiedCols = fillCols(gridA, Math.abs(delta.colDelta), generators, isLastColLocked ? gridWidth - 1 : gridWidth);
        const newLockedColumns = getLockedColumnsFromGrid(modifiedCols);
        return fillRows(modifiedCols, Math.abs(delta.rowDelta), generators, mapToObject(newLockedColumns, always));
      };
      const isSpanning = (grid2, row2, col2, comparator) => {
        const candidate = getCell(grid2[row2], col2);
        const matching = curry(comparator, candidate.element);
        const currentRow = grid2[row2];
        return grid2.length > 1 && cellLength(currentRow) > 1 && (col2 > 0 && matching(getCellElement(currentRow, col2 - 1)) || col2 < currentRow.cells.length - 1 && matching(getCellElement(currentRow, col2 + 1)) || row2 > 0 && matching(getCellElement(grid2[row2 - 1], col2)) || row2 < grid2.length - 1 && matching(getCellElement(grid2[row2 + 1], col2)));
      };
      const mergeTables = (startAddress, gridA, gridBRows, generator, comparator, lockedColumns) => {
        const startRow = startAddress.row;
        const startCol = startAddress.column;
        const mergeHeight = gridBRows.length;
        const mergeWidth = cellLength(gridBRows[0]);
        const endRow = startRow + mergeHeight;
        const endCol = startCol + mergeWidth + lockedColumns.length;
        const lockedColumnObj = mapToObject(lockedColumns, always);
        for (let r2 = startRow; r2 < endRow; r2++) {
          let skippedCol = 0;
          for (let c = startCol; c < endCol; c++) {
            if (lockedColumnObj[c]) {
              skippedCol++;
              continue;
            }
            if (isSpanning(gridA, r2, c, comparator)) {
              unmerge(gridA, getCellElement(gridA[r2], c), comparator, generator.cell);
            }
            const gridBColIndex = c - startCol - skippedCol;
            const newCell = getCell(gridBRows[r2 - startRow], gridBColIndex);
            const newCellElm = newCell.element;
            const replacement = generator.replace(newCellElm);
            mutateCell(gridA[r2], c, elementnew(replacement, true, newCell.isLocked));
          }
        }
        return gridA;
      };
      const getValidStartAddress = (currentStartAddress, grid2, lockedColumns) => {
        const gridColLength = cellLength(grid2[0]);
        const adjustedRowAddress = extractGridDetails(grid2).cols.length + currentStartAddress.row;
        const possibleColAddresses = range$1(gridColLength - currentStartAddress.column, (num) => num + currentStartAddress.column);
        const validColAddress = find$1(possibleColAddresses, (num) => forall(lockedColumns, (col2) => col2 !== num)).getOr(gridColLength - 1);
        return {
          row: adjustedRowAddress,
          column: validColAddress
        };
      };
      const getLockedColumnsWithinBounds = (startAddress, rows2, lockedColumns) => filter$2(lockedColumns, (colNum) => colNum >= startAddress.column && colNum <= cellLength(rows2[0]) + startAddress.column);
      const merge$1 = (startAddress, gridA, gridB, generator, comparator) => {
        const lockedColumns = getLockedColumnsFromGrid(gridA);
        const validStartAddress = getValidStartAddress(startAddress, gridA, lockedColumns);
        const gridBRows = extractGridDetails(gridB).rows;
        const lockedColumnsWithinBounds = getLockedColumnsWithinBounds(validStartAddress, gridBRows, lockedColumns);
        const result = measure(validStartAddress, gridA, gridBRows);
        return result.map((diff) => {
          const delta = {
            ...diff,
            colDelta: diff.colDelta - lockedColumnsWithinBounds.length
          };
          const fittedGrid = tailor(gridA, delta, generator);
          const newLockedColumns = getLockedColumnsFromGrid(fittedGrid);
          const newLockedColumnsWithinBounds = getLockedColumnsWithinBounds(validStartAddress, gridBRows, newLockedColumns);
          return mergeTables(validStartAddress, fittedGrid, gridBRows, generator, comparator, newLockedColumnsWithinBounds);
        });
      };
      const insertCols = (index, gridA, gridB, generator, comparator) => {
        splitCols(gridA, index, comparator, generator.cell);
        const delta = measureHeight(gridB, gridA);
        const fittedNewGrid = tailor(gridB, delta, generator);
        const secondDelta = measureHeight(gridA, fittedNewGrid);
        const fittedOldGrid = tailor(gridA, secondDelta, generator);
        return map$1(fittedOldGrid, (gridRow, i) => {
          return addCells(gridRow, index, fittedNewGrid[i].cells);
        });
      };
      const insertRows = (index, gridA, gridB, generator, comparator) => {
        splitRows(gridA, index, comparator, generator.cell);
        const locked = getLockedColumnsFromGrid(gridA);
        const diff = measureWidth(gridA, gridB);
        const delta = {
          ...diff,
          colDelta: diff.colDelta - locked.length
        };
        const fittedOldGrid = tailor(gridA, delta, generator);
        const {
          cols: oldCols,
          rows: oldRows
        } = extractGridDetails(fittedOldGrid);
        const newLocked = getLockedColumnsFromGrid(fittedOldGrid);
        const secondDiff = measureWidth(gridB, gridA);
        const secondDelta = {
          ...secondDiff,
          colDelta: secondDiff.colDelta + newLocked.length
        };
        const fittedGridB = lockedColFill(gridB, generator, newLocked);
        const fittedNewGrid = tailor(fittedGridB, secondDelta, generator);
        return [
          ...oldCols,
          ...oldRows.slice(0, index),
          ...fittedNewGrid,
          ...oldRows.slice(index, oldRows.length)
        ];
      };
      const cloneRow = (row2, cloneCell, comparator, substitution) => clone(row2, (elem) => substitution(elem, comparator), cloneCell);
      const insertRowAt = (grid2, index, example, comparator, substitution) => {
        const { rows: rows2, cols } = extractGridDetails(grid2);
        const before2 = rows2.slice(0, index);
        const after2 = rows2.slice(index);
        const newRow = cloneRow(rows2[example], (ex, c) => {
          const withinSpan = index > 0 && index < rows2.length && comparator(getCellElement(rows2[index - 1], c), getCellElement(rows2[index], c));
          const ret = withinSpan ? getCell(rows2[index], c) : elementnew(substitution(ex.element, comparator), true, ex.isLocked);
          return ret;
        }, comparator, substitution);
        return [
          ...cols,
          ...before2,
          newRow,
          ...after2
        ];
      };
      const getElementFor = (row2, column, section2, withinSpan, example, comparator, substitution) => {
        if (section2 === "colgroup" || !withinSpan) {
          const cell2 = getCell(row2, example);
          return elementnew(substitution(cell2.element, comparator), true, false);
        } else {
          return getCell(row2, column);
        }
      };
      const insertColumnAt = (grid2, index, example, comparator, substitution) => map$1(grid2, (row2) => {
        const withinSpan = index > 0 && index < cellLength(row2) && comparator(getCellElement(row2, index - 1), getCellElement(row2, index));
        const sub = getElementFor(row2, index, row2.section, withinSpan, example, comparator, substitution);
        return addCell(row2, index, sub);
      });
      const deleteColumnsAt = (grid2, columns2) => bind$2(grid2, (row2) => {
        const existingCells = row2.cells;
        const cells2 = foldr(columns2, (acc, column) => column >= 0 && column < acc.length ? acc.slice(0, column).concat(acc.slice(column + 1)) : acc, existingCells);
        return cells2.length > 0 ? [rowcells(row2.element, cells2, row2.section, row2.isNew)] : [];
      });
      const deleteRowsAt = (grid2, start, finish) => {
        const { rows: rows2, cols } = extractGridDetails(grid2);
        return [
          ...cols,
          ...rows2.slice(0, start),
          ...rows2.slice(finish + 1)
        ];
      };
      const notInStartRow = (grid2, rowIndex, colIndex, comparator) => getCellElement(grid2[rowIndex], colIndex) !== void 0 && (rowIndex > 0 && comparator(getCellElement(grid2[rowIndex - 1], colIndex), getCellElement(grid2[rowIndex], colIndex)));
      const notInStartColumn = (row2, index, comparator) => index > 0 && comparator(getCellElement(row2, index - 1), getCellElement(row2, index));
      const isDuplicatedCell = (grid2, rowIndex, colIndex, comparator) => notInStartRow(grid2, rowIndex, colIndex, comparator) || notInStartColumn(grid2[rowIndex], colIndex, comparator);
      const rowReplacerPredicate = (targetRow, columnHeaders) => {
        const entireTableIsHeader = forall(columnHeaders, identity) && isHeaderCells(targetRow.cells);
        return entireTableIsHeader ? always : (cell2, _rowIndex, colIndex) => {
          const type2 = name(cell2.element);
          return !(type2 === "th" && columnHeaders[colIndex]);
        };
      };
      const columnReplacePredicate = (targetColumn, rowHeaders) => {
        const entireTableIsHeader = forall(rowHeaders, identity) && isHeaderCells(targetColumn);
        return entireTableIsHeader ? always : (cell2, rowIndex, _colIndex) => {
          const type2 = name(cell2.element);
          return !(type2 === "th" && rowHeaders[rowIndex]);
        };
      };
      const determineScope = (applyScope, cell2, newScope, isInHeader) => {
        const hasSpan = (scope) => scope === "row" ? hasRowspan(cell2) : hasColspan(cell2);
        const getScope = (scope) => hasSpan(scope) ? `${scope}group` : scope;
        if (applyScope) {
          return isHeaderCell(cell2) ? getScope(newScope) : null;
        } else if (isInHeader && isHeaderCell(cell2)) {
          const oppositeScope = newScope === "row" ? "col" : "row";
          return getScope(oppositeScope);
        } else {
          return null;
        }
      };
      const rowScopeGenerator = (applyScope, columnHeaders) => (cell2, rowIndex, columnIndex) => Optional.some(determineScope(applyScope, cell2.element, "col", columnHeaders[columnIndex]));
      const columnScopeGenerator = (applyScope, rowHeaders) => (cell2, rowIndex) => Optional.some(determineScope(applyScope, cell2.element, "row", rowHeaders[rowIndex]));
      const replace = (cell2, comparator, substitute) => elementnew(substitute(cell2.element, comparator), true, cell2.isLocked);
      const replaceIn = (grid2, targets, comparator, substitute, replacer, genScope, shouldReplace) => {
        const isTarget = (cell2) => {
          return exists(targets, (target) => {
            return comparator(cell2.element, target.element);
          });
        };
        return map$1(grid2, (row2, rowIndex) => {
          return mapCells(row2, (cell2, colIndex) => {
            if (isTarget(cell2)) {
              const newCell = shouldReplace(cell2, rowIndex, colIndex) ? replacer(cell2, comparator, substitute) : cell2;
              genScope(newCell, rowIndex, colIndex).each((scope) => {
                setOptions(newCell.element, { scope: Optional.from(scope) });
              });
              return newCell;
            } else {
              return cell2;
            }
          });
        });
      };
      const getColumnCells = (rows2, columnIndex, comparator) => bind$2(rows2, (row2, i) => {
        return isDuplicatedCell(rows2, i, columnIndex, comparator) ? [] : [getCell(row2, columnIndex)];
      });
      const getRowCells = (rows2, rowIndex, comparator) => {
        const targetRow = rows2[rowIndex];
        return bind$2(targetRow.cells, (item, i) => {
          return isDuplicatedCell(rows2, rowIndex, i, comparator) ? [] : [item];
        });
      };
      const replaceColumns = (grid2, indexes, applyScope, comparator, substitution) => {
        const rows2 = extractGridDetails(grid2).rows;
        const targets = bind$2(indexes, (index) => getColumnCells(rows2, index, comparator));
        const rowHeaders = map$1(rows2, (row2) => isHeaderCells(row2.cells));
        const shouldReplaceCell = columnReplacePredicate(targets, rowHeaders);
        const scopeGenerator = columnScopeGenerator(applyScope, rowHeaders);
        return replaceIn(grid2, targets, comparator, substitution, replace, scopeGenerator, shouldReplaceCell);
      };
      const replaceRows = (grid2, indexes, section2, applyScope, comparator, substitution, tableSection) => {
        const { cols, rows: rows2 } = extractGridDetails(grid2);
        const targetRow = rows2[indexes[0]];
        const targets = bind$2(indexes, (index) => getRowCells(rows2, index, comparator));
        const columnHeaders = map$1(targetRow.cells, (_cell, index) => isHeaderCells(getColumnCells(rows2, index, comparator)));
        const newRows = [...rows2];
        each$2(indexes, (index) => {
          newRows[index] = tableSection.transformRow(rows2[index], section2);
        });
        const newGrid = [
          ...cols,
          ...newRows
        ];
        const shouldReplaceCell = rowReplacerPredicate(targetRow, columnHeaders);
        const scopeGenerator = rowScopeGenerator(applyScope, columnHeaders);
        return replaceIn(newGrid, targets, comparator, substitution, tableSection.transformCell, scopeGenerator, shouldReplaceCell);
      };
      const replaceCells = (grid2, details, comparator, substitution) => {
        const rows2 = extractGridDetails(grid2).rows;
        const targetCells = map$1(details, (detail2) => getCell(rows2[detail2.row], detail2.column));
        return replaceIn(grid2, targetCells, comparator, substitution, replace, Optional.none, always);
      };
      const generate = (cases) => {
        if (!isArray(cases)) {
          throw new Error("cases must be an array");
        }
        if (cases.length === 0) {
          throw new Error("there must be at least one case");
        }
        const constructors = [];
        const adt2 = {};
        each$2(cases, (acase, count) => {
          const keys$1 = keys(acase);
          if (keys$1.length !== 1) {
            throw new Error("one and only one name per case");
          }
          const key2 = keys$1[0];
          const value2 = acase[key2];
          if (adt2[key2] !== void 0) {
            throw new Error("duplicate key detected:" + key2);
          } else if (key2 === "cata") {
            throw new Error("cannot have a case named cata (sorry)");
          } else if (!isArray(value2)) {
            throw new Error("case arguments must be an array");
          }
          constructors.push(key2);
          adt2[key2] = (...args) => {
            const argLength = args.length;
            if (argLength !== value2.length) {
              throw new Error("Wrong number of arguments to case " + key2 + ". Expected " + value2.length + " (" + value2 + "), got " + argLength);
            }
            const match = (branches) => {
              const branchKeys = keys(branches);
              if (constructors.length !== branchKeys.length) {
                throw new Error("Wrong number of arguments to match. Expected: " + constructors.join(",") + "\nActual: " + branchKeys.join(","));
              }
              const allReqd = forall(constructors, (reqKey) => {
                return contains$2(branchKeys, reqKey);
              });
              if (!allReqd) {
                throw new Error("Not all branches were specified when using match. Specified: " + branchKeys.join(", ") + "\nRequired: " + constructors.join(", "));
              }
              return branches[key2].apply(null, args);
            };
            return {
              fold: (...foldArgs) => {
                if (foldArgs.length !== cases.length) {
                  throw new Error("Wrong number of arguments to fold. Expected " + cases.length + ", got " + foldArgs.length);
                }
                const target = foldArgs[count];
                return target.apply(null, args);
              },
              match,
              log: (label) => {
                console.log(label, {
                  constructors,
                  constructor: key2,
                  params: args
                });
              }
            };
          };
        });
        return adt2;
      };
      const Adt = { generate };
      const adt$6 = Adt.generate([
        { none: [] },
        { only: ["index"] },
        {
          left: [
            "index",
            "next"
          ]
        },
        {
          middle: [
            "prev",
            "index",
            "next"
          ]
        },
        {
          right: [
            "prev",
            "index"
          ]
        }
      ]);
      const ColumnContext = { ...adt$6 };
      const neighbours = (input, index) => {
        if (input.length === 0) {
          return ColumnContext.none();
        }
        if (input.length === 1) {
          return ColumnContext.only(0);
        }
        if (index === 0) {
          return ColumnContext.left(0, 1);
        }
        if (index === input.length - 1) {
          return ColumnContext.right(index - 1, index);
        }
        if (index > 0 && index < input.length - 1) {
          return ColumnContext.middle(index - 1, index, index + 1);
        }
        return ColumnContext.none();
      };
      const determine = (input, column, step, tableSize, resize2) => {
        const result = input.slice(0);
        const context = neighbours(input, column);
        const onNone = constant(map$1(result, constant(0)));
        const onOnly = (index) => tableSize.singleColumnWidth(result[index], step);
        const onLeft = (index, next) => resize2.calcLeftEdgeDeltas(result, index, next, step, tableSize.minCellWidth(), tableSize.isRelative);
        const onMiddle = (prev, index, next) => resize2.calcMiddleDeltas(result, prev, index, next, step, tableSize.minCellWidth(), tableSize.isRelative);
        const onRight = (prev, index) => resize2.calcRightEdgeDeltas(result, prev, index, step, tableSize.minCellWidth(), tableSize.isRelative);
        return context.fold(onNone, onOnly, onLeft, onMiddle, onRight);
      };
      const total = (start, end, measures) => {
        let r2 = 0;
        for (let i = start; i < end; i++) {
          r2 += measures[i] !== void 0 ? measures[i] : 0;
        }
        return r2;
      };
      const recalculateWidthForCells = (warehouse, widths) => {
        const all2 = Warehouse.justCells(warehouse);
        return map$1(all2, (cell2) => {
          const width2 = total(cell2.column, cell2.column + cell2.colspan, widths);
          return {
            element: cell2.element,
            width: width2,
            colspan: cell2.colspan
          };
        });
      };
      const recalculateWidthForColumns = (warehouse, widths) => {
        const groups = Warehouse.justColumns(warehouse);
        return map$1(groups, (column, index) => ({
          element: column.element,
          width: widths[index],
          colspan: column.colspan
        }));
      };
      const recalculateHeightForCells = (warehouse, heights) => {
        const all2 = Warehouse.justCells(warehouse);
        return map$1(all2, (cell2) => {
          const height2 = total(cell2.row, cell2.row + cell2.rowspan, heights);
          return {
            element: cell2.element,
            height: height2,
            rowspan: cell2.rowspan
          };
        });
      };
      const matchRowHeight = (warehouse, heights) => {
        return map$1(warehouse.all, (row2, i) => {
          return {
            element: row2.element,
            height: heights[i]
          };
        });
      };
      const sumUp = (newSize) => foldr(newSize, (b, a) => b + a, 0);
      const recalculate = (warehouse, widths) => {
        if (Warehouse.hasColumns(warehouse)) {
          return recalculateWidthForColumns(warehouse, widths);
        } else {
          return recalculateWidthForCells(warehouse, widths);
        }
      };
      const recalculateAndApply = (warehouse, widths, tableSize) => {
        const newSizes = recalculate(warehouse, widths);
        each$2(newSizes, (cell2) => {
          tableSize.setElementWidth(cell2.element, cell2.width);
        });
      };
      const adjustWidth = (table2, delta, index, resizing, tableSize) => {
        const warehouse = Warehouse.fromTable(table2);
        const step = tableSize.getCellDelta(delta);
        const widths = tableSize.getWidths(warehouse, tableSize);
        const isLastColumn = index === warehouse.grid.columns - 1;
        const clampedStep = resizing.clampTableDelta(widths, index, step, tableSize.minCellWidth(), isLastColumn);
        const deltas = determine(widths, index, clampedStep, tableSize, resizing);
        const newWidths = map$1(deltas, (dx, i) => dx + widths[i]);
        recalculateAndApply(warehouse, newWidths, tableSize);
        resizing.resizeTable(tableSize.adjustTableWidth, clampedStep, isLastColumn);
      };
      const adjustHeight = (table2, delta, index, direction) => {
        const warehouse = Warehouse.fromTable(table2);
        const heights = getPixelHeights(warehouse, table2, direction);
        const newHeights = map$1(heights, (dy, i) => index === i ? Math.max(delta + dy, minHeight()) : dy);
        const newCellSizes = recalculateHeightForCells(warehouse, newHeights);
        const newRowSizes = matchRowHeight(warehouse, newHeights);
        each$2(newRowSizes, (row2) => {
          setHeight(row2.element, row2.height);
        });
        each$2(newCellSizes, (cell2) => {
          setHeight(cell2.element, cell2.height);
        });
        const total2 = sumUp(newHeights);
        setHeight(table2, total2);
      };
      const adjustAndRedistributeWidths$1 = (_table, list, details, tableSize, resizeBehaviour) => {
        const warehouse = Warehouse.generate(list);
        const sizes = tableSize.getWidths(warehouse, tableSize);
        const tablePixelWidth = tableSize.pixelWidth();
        const { newSizes, delta } = resizeBehaviour.calcRedestributedWidths(sizes, tablePixelWidth, details.pixelDelta, tableSize.isRelative);
        recalculateAndApply(warehouse, newSizes, tableSize);
        tableSize.adjustTableWidth(delta);
      };
      const adjustWidthTo = (_table, list, _info, tableSize) => {
        const warehouse = Warehouse.generate(list);
        const widths = tableSize.getWidths(warehouse, tableSize);
        recalculateAndApply(warehouse, widths, tableSize);
      };
      const uniqueColumns = (details) => {
        const uniqueCheck = (rest, detail2) => {
          const columnExists = exists(rest, (currentDetail) => currentDetail.column === detail2.column);
          return columnExists ? rest : rest.concat([detail2]);
        };
        return foldl(details, uniqueCheck, []).sort((detailA, detailB) => detailA.column - detailB.column);
      };
      const isCol = isTag("col");
      const isColgroup = isTag("colgroup");
      const isRow$1 = (element) => name(element) === "tr" || isColgroup(element);
      const elementToData = (element) => {
        const colspan = getAttrValue(element, "colspan", 1);
        const rowspan = getAttrValue(element, "rowspan", 1);
        return {
          element,
          colspan,
          rowspan
        };
      };
      const modification = (generators, toData = elementToData) => {
        const nuCell = (data) => isCol(data.element) ? generators.col(data) : generators.cell(data);
        const nuRow = (data) => isColgroup(data.element) ? generators.colgroup(data) : generators.row(data);
        const add2 = (element) => {
          if (isRow$1(element)) {
            return nuRow({ element });
          } else {
            const cell2 = element;
            const replacement = nuCell(toData(cell2));
            recent = Optional.some({
              item: cell2,
              replacement
            });
            return replacement;
          }
        };
        let recent = Optional.none();
        const getOrInit = (element, comparator) => {
          return recent.fold(() => {
            return add2(element);
          }, (p) => {
            return comparator(element, p.item) ? p.replacement : add2(element);
          });
        };
        return { getOrInit };
      };
      const transform$1 = (tag) => {
        return (generators) => {
          const list = [];
          const find2 = (element, comparator) => {
            return find$1(list, (x) => {
              return comparator(x.item, element);
            });
          };
          const makeNew = (element) => {
            const attrs = tag === "td" ? { scope: null } : {};
            const cell2 = generators.replace(element, tag, attrs);
            list.push({
              item: element,
              sub: cell2
            });
            return cell2;
          };
          const replaceOrInit = (element, comparator) => {
            if (isRow$1(element) || isCol(element)) {
              return element;
            } else {
              const cell2 = element;
              return find2(cell2, comparator).fold(() => {
                return makeNew(cell2);
              }, (p) => {
                return comparator(element, p.item) ? p.sub : makeNew(cell2);
              });
            }
          };
          return { replaceOrInit };
        };
      };
      const getScopeAttribute = (cell2) => getOpt(cell2, "scope").map((attribute) => attribute.substr(0, 3));
      const merging = (generators) => {
        const unmerge2 = (cell2) => {
          const scope = getScopeAttribute(cell2);
          scope.each((attribute) => set$2(cell2, "scope", attribute));
          return () => {
            const raw = generators.cell({
              element: cell2,
              colspan: 1,
              rowspan: 1
            });
            remove$5(raw, "width");
            remove$5(cell2, "width");
            scope.each((attribute) => set$2(raw, "scope", attribute));
            return raw;
          };
        };
        const merge2 = (cells2) => {
          const getScopeProperty = () => {
            const stringAttributes = cat(map$1(cells2, getScopeAttribute));
            if (stringAttributes.length === 0) {
              return Optional.none();
            } else {
              const baseScope = stringAttributes[0];
              const scopes = [
                "row",
                "col"
              ];
              const isMixed = exists(stringAttributes, (attribute) => {
                return attribute !== baseScope && contains$2(scopes, attribute);
              });
              return isMixed ? Optional.none() : Optional.from(baseScope);
            }
          };
          remove$5(cells2[0], "width");
          getScopeProperty().fold(() => remove$7(cells2[0], "scope"), (attribute) => set$2(cells2[0], "scope", attribute + "group"));
          return constant(cells2[0]);
        };
        return {
          unmerge: unmerge2,
          merge: merge2
        };
      };
      const Generators = {
        modification,
        transform: transform$1,
        merging
      };
      const blockList = [
        "body",
        "p",
        "div",
        "article",
        "aside",
        "figcaption",
        "figure",
        "footer",
        "header",
        "nav",
        "section",
        "ol",
        "ul",
        "table",
        "thead",
        "tfoot",
        "tbody",
        "caption",
        "tr",
        "td",
        "th",
        "h1",
        "h2",
        "h3",
        "h4",
        "h5",
        "h6",
        "blockquote",
        "pre",
        "address"
      ];
      const isList$1 = (universe2, item) => {
        const tagName = universe2.property().name(item);
        return contains$2([
          "ol",
          "ul"
        ], tagName);
      };
      const isBlock$1 = (universe2, item) => {
        const tagName = universe2.property().name(item);
        return contains$2(blockList, tagName);
      };
      const isEmptyTag$1 = (universe2, item) => {
        return contains$2([
          "br",
          "img",
          "hr",
          "input"
        ], universe2.property().name(item));
      };
      const universe$1 = DomUniverse();
      const isBlock = (element) => {
        return isBlock$1(universe$1, element);
      };
      const isList = (element) => {
        return isList$1(universe$1, element);
      };
      const isEmptyTag = (element) => {
        return isEmptyTag$1(universe$1, element);
      };
      const merge = (cells2) => {
        const isBr2 = isTag("br");
        const advancedBr = (children2) => {
          return forall(children2, (c) => {
            return isBr2(c) || isText(c) && get$6(c).trim().length === 0;
          });
        };
        const isListItem = (el) => {
          return name(el) === "li" || ancestor$2(el, isList).isSome();
        };
        const siblingIsBlock = (el) => {
          return nextSibling(el).map((rightSibling) => {
            if (isBlock(rightSibling)) {
              return true;
            }
            if (isEmptyTag(rightSibling)) {
              return name(rightSibling) === "img" ? false : true;
            }
            return false;
          }).getOr(false);
        };
        const markCell = (cell2) => {
          return last$1(cell2).bind((rightEdge) => {
            const rightSiblingIsBlock = siblingIsBlock(rightEdge);
            return parent(rightEdge).map((parent2) => {
              return rightSiblingIsBlock === true || isListItem(parent2) || isBr2(rightEdge) || isBlock(parent2) && !eq$1(cell2, parent2) ? [] : [SugarElement.fromTag("br")];
            });
          }).getOr([]);
        };
        const markContent = () => {
          const content = bind$2(cells2, (cell2) => {
            const children2 = children$2(cell2);
            return advancedBr(children2) ? [] : children2.concat(markCell(cell2));
          });
          return content.length === 0 ? [SugarElement.fromTag("br")] : content;
        };
        const contents = markContent();
        empty(cells2[0]);
        append(cells2[0], contents);
      };
      const isEditable = (elem) => isEditable$1(elem, true);
      const prune = (table2) => {
        const cells2 = cells$1(table2);
        if (cells2.length === 0) {
          remove$6(table2);
        }
      };
      const outcome = (grid2, cursor) => ({
        grid: grid2,
        cursor
      });
      const findEditableCursorPosition = (rows2) => findMap(rows2, (row2) => findMap(row2.cells, (cell2) => {
        const elem = cell2.element;
        return someIf(isEditable(elem), elem);
      }));
      const elementFromGrid = (grid2, row2, column) => {
        var _a, _b;
        const rows2 = extractGridDetails(grid2).rows;
        return Optional.from((_b = (_a = rows2[row2]) === null || _a === void 0 ? void 0 : _a.cells[column]) === null || _b === void 0 ? void 0 : _b.element).filter(isEditable).orThunk(() => findEditableCursorPosition(rows2));
      };
      const bundle = (grid2, row2, column) => {
        const cursorElement = elementFromGrid(grid2, row2, column);
        return outcome(grid2, cursorElement);
      };
      const uniqueRows = (details) => {
        const rowCompilation = (rest, detail2) => {
          const rowExists = exists(rest, (currentDetail) => currentDetail.row === detail2.row);
          return rowExists ? rest : rest.concat([detail2]);
        };
        return foldl(details, rowCompilation, []).sort((detailA, detailB) => detailA.row - detailB.row);
      };
      const opInsertRowsBefore = (grid2, details, comparator, genWrappers) => {
        const targetIndex = details[0].row;
        const rows2 = uniqueRows(details);
        const newGrid = foldr(rows2, (acc, row2) => {
          const newG = insertRowAt(acc.grid, targetIndex, row2.row + acc.delta, comparator, genWrappers.getOrInit);
          return {
            grid: newG,
            delta: acc.delta + 1
          };
        }, {
          grid: grid2,
          delta: 0
        }).grid;
        return bundle(newGrid, targetIndex, details[0].column);
      };
      const opInsertRowsAfter = (grid2, details, comparator, genWrappers) => {
        const rows2 = uniqueRows(details);
        const target = rows2[rows2.length - 1];
        const targetIndex = target.row + target.rowspan;
        const newGrid = foldr(rows2, (newG, row2) => {
          return insertRowAt(newG, targetIndex, row2.row, comparator, genWrappers.getOrInit);
        }, grid2);
        return bundle(newGrid, targetIndex, details[0].column);
      };
      const opInsertColumnsBefore = (grid2, extractDetail, comparator, genWrappers) => {
        const details = extractDetail.details;
        const columns2 = uniqueColumns(details);
        const targetIndex = columns2[0].column;
        const newGrid = foldr(columns2, (acc, col2) => {
          const newG = insertColumnAt(acc.grid, targetIndex, col2.column + acc.delta, comparator, genWrappers.getOrInit);
          return {
            grid: newG,
            delta: acc.delta + 1
          };
        }, {
          grid: grid2,
          delta: 0
        }).grid;
        return bundle(newGrid, details[0].row, targetIndex);
      };
      const opInsertColumnsAfter = (grid2, extractDetail, comparator, genWrappers) => {
        const details = extractDetail.details;
        const target = details[details.length - 1];
        const targetIndex = target.column + target.colspan;
        const columns2 = uniqueColumns(details);
        const newGrid = foldr(columns2, (newG, col2) => {
          return insertColumnAt(newG, targetIndex, col2.column, comparator, genWrappers.getOrInit);
        }, grid2);
        return bundle(newGrid, details[0].row, targetIndex);
      };
      const opMakeColumnsHeader = (initialGrid, details, comparator, genWrappers) => {
        const columns2 = uniqueColumns(details);
        const columnIndexes = map$1(columns2, (detail2) => detail2.column);
        const newGrid = replaceColumns(initialGrid, columnIndexes, true, comparator, genWrappers.replaceOrInit);
        return bundle(newGrid, details[0].row, details[0].column);
      };
      const opMakeCellsHeader = (initialGrid, details, comparator, genWrappers) => {
        const newGrid = replaceCells(initialGrid, details, comparator, genWrappers.replaceOrInit);
        return bundle(newGrid, details[0].row, details[0].column);
      };
      const opUnmakeColumnsHeader = (initialGrid, details, comparator, genWrappers) => {
        const columns2 = uniqueColumns(details);
        const columnIndexes = map$1(columns2, (detail2) => detail2.column);
        const newGrid = replaceColumns(initialGrid, columnIndexes, false, comparator, genWrappers.replaceOrInit);
        return bundle(newGrid, details[0].row, details[0].column);
      };
      const opUnmakeCellsHeader = (initialGrid, details, comparator, genWrappers) => {
        const newGrid = replaceCells(initialGrid, details, comparator, genWrappers.replaceOrInit);
        return bundle(newGrid, details[0].row, details[0].column);
      };
      const makeRowsSection = (section2, applyScope) => (initialGrid, details, comparator, genWrappers, tableSection) => {
        const rows2 = uniqueRows(details);
        const rowIndexes = map$1(rows2, (detail2) => detail2.row);
        const newGrid = replaceRows(initialGrid, rowIndexes, section2, applyScope, comparator, genWrappers.replaceOrInit, tableSection);
        return bundle(newGrid, details[0].row, details[0].column);
      };
      const opMakeRowsHeader = makeRowsSection("thead", true);
      const opMakeRowsBody = makeRowsSection("tbody", false);
      const opMakeRowsFooter = makeRowsSection("tfoot", false);
      const opEraseColumns = (grid2, extractDetail, _comparator, _genWrappers) => {
        const columns2 = uniqueColumns(extractDetail.details);
        const newGrid = deleteColumnsAt(grid2, map$1(columns2, (column) => column.column));
        const maxColIndex = newGrid.length > 0 ? newGrid[0].cells.length - 1 : 0;
        return bundle(newGrid, columns2[0].row, Math.min(columns2[0].column, maxColIndex));
      };
      const opEraseRows = (grid2, details, _comparator, _genWrappers) => {
        const rows2 = uniqueRows(details);
        const newGrid = deleteRowsAt(grid2, rows2[0].row, rows2[rows2.length - 1].row);
        const maxRowIndex = newGrid.length > 0 ? newGrid.length - 1 : 0;
        return bundle(newGrid, Math.min(details[0].row, maxRowIndex), details[0].column);
      };
      const opMergeCells = (grid2, mergable2, comparator, genWrappers) => {
        const cells2 = mergable2.cells;
        merge(cells2);
        const newGrid = merge$2(grid2, mergable2.bounds, comparator, genWrappers.merge(cells2));
        return outcome(newGrid, Optional.from(cells2[0]));
      };
      const opUnmergeCells = (grid2, unmergable2, comparator, genWrappers) => {
        const unmerge$1 = (b, cell2) => unmerge(b, cell2, comparator, genWrappers.unmerge(cell2));
        const newGrid = foldr(unmergable2, unmerge$1, grid2);
        return outcome(newGrid, Optional.from(unmergable2[0]));
      };
      const opPasteCells = (grid2, pasteDetails, comparator, _genWrappers) => {
        const gridify = (table2, generators) => {
          const wh = Warehouse.fromTable(table2);
          return toGrid(wh, generators, true);
        };
        const gridB = gridify(pasteDetails.clipboard, pasteDetails.generators);
        const startAddress = address(pasteDetails.row, pasteDetails.column);
        const mergedGrid = merge$1(startAddress, grid2, gridB, pasteDetails.generators, comparator);
        return mergedGrid.fold(() => outcome(grid2, Optional.some(pasteDetails.element)), (newGrid) => {
          return bundle(newGrid, pasteDetails.row, pasteDetails.column);
        });
      };
      const gridifyRows = (rows2, generators, context) => {
        const pasteDetails = fromPastedRows(rows2, context.section);
        const wh = Warehouse.generate(pasteDetails);
        return toGrid(wh, generators, true);
      };
      const opPasteColsBefore = (grid2, pasteDetails, comparator, _genWrappers) => {
        const rows2 = extractGridDetails(grid2).rows;
        const index = pasteDetails.cells[0].column;
        const context = rows2[pasteDetails.cells[0].row];
        const gridB = gridifyRows(pasteDetails.clipboard, pasteDetails.generators, context);
        const mergedGrid = insertCols(index, grid2, gridB, pasteDetails.generators, comparator);
        return bundle(mergedGrid, pasteDetails.cells[0].row, pasteDetails.cells[0].column);
      };
      const opPasteColsAfter = (grid2, pasteDetails, comparator, _genWrappers) => {
        const rows2 = extractGridDetails(grid2).rows;
        const index = pasteDetails.cells[pasteDetails.cells.length - 1].column + pasteDetails.cells[pasteDetails.cells.length - 1].colspan;
        const context = rows2[pasteDetails.cells[0].row];
        const gridB = gridifyRows(pasteDetails.clipboard, pasteDetails.generators, context);
        const mergedGrid = insertCols(index, grid2, gridB, pasteDetails.generators, comparator);
        return bundle(mergedGrid, pasteDetails.cells[0].row, pasteDetails.cells[0].column);
      };
      const opPasteRowsBefore = (grid2, pasteDetails, comparator, _genWrappers) => {
        const rows2 = extractGridDetails(grid2).rows;
        const index = pasteDetails.cells[0].row;
        const context = rows2[index];
        const gridB = gridifyRows(pasteDetails.clipboard, pasteDetails.generators, context);
        const mergedGrid = insertRows(index, grid2, gridB, pasteDetails.generators, comparator);
        return bundle(mergedGrid, pasteDetails.cells[0].row, pasteDetails.cells[0].column);
      };
      const opPasteRowsAfter = (grid2, pasteDetails, comparator, _genWrappers) => {
        const rows2 = extractGridDetails(grid2).rows;
        const index = pasteDetails.cells[pasteDetails.cells.length - 1].row + pasteDetails.cells[pasteDetails.cells.length - 1].rowspan;
        const context = rows2[pasteDetails.cells[0].row];
        const gridB = gridifyRows(pasteDetails.clipboard, pasteDetails.generators, context);
        const mergedGrid = insertRows(index, grid2, gridB, pasteDetails.generators, comparator);
        return bundle(mergedGrid, pasteDetails.cells[0].row, pasteDetails.cells[0].column);
      };
      const opGetColumnsType = (table2, target) => {
        const house = Warehouse.fromTable(table2);
        const details = onCells(house, target);
        return details.bind((selectedCells) => {
          const lastSelectedCell = selectedCells[selectedCells.length - 1];
          const minColRange = selectedCells[0].column;
          const maxColRange = lastSelectedCell.column + lastSelectedCell.colspan;
          const selectedColumnCells = flatten(map$1(house.all, (row2) => filter$2(row2.cells, (cell2) => cell2.column >= minColRange && cell2.column < maxColRange)));
          return findCommonCellType(selectedColumnCells);
        }).getOr("");
      };
      const opGetCellsType = (table2, target) => {
        const house = Warehouse.fromTable(table2);
        const details = onCells(house, target);
        return details.bind(findCommonCellType).getOr("");
      };
      const opGetRowsType = (table2, target) => {
        const house = Warehouse.fromTable(table2);
        const details = onCells(house, target);
        return details.bind((selectedCells) => {
          const lastSelectedCell = selectedCells[selectedCells.length - 1];
          const minRowRange = selectedCells[0].row;
          const maxRowRange = lastSelectedCell.row + lastSelectedCell.rowspan;
          const selectedRows = house.all.slice(minRowRange, maxRowRange);
          return findCommonRowType(selectedRows);
        }).getOr("");
      };
      const resize = (table2, list, details, behaviours) => adjustWidthTo(table2, list, details, behaviours.sizing);
      const adjustAndRedistributeWidths = (table2, list, details, behaviours) => adjustAndRedistributeWidths$1(table2, list, details, behaviours.sizing, behaviours.resize);
      const firstColumnIsLocked = (_warehouse, details) => exists(details, (detail2) => detail2.column === 0 && detail2.isLocked);
      const lastColumnIsLocked = (warehouse, details) => exists(details, (detail2) => detail2.column + detail2.colspan >= warehouse.grid.columns && detail2.isLocked);
      const getColumnsWidth = (warehouse, details) => {
        const columns$12 = columns(warehouse);
        const uniqueCols = uniqueColumns(details);
        return foldl(uniqueCols, (acc, detail2) => {
          const column = columns$12[detail2.column];
          const colWidth = column.map(getOuter$2).getOr(0);
          return acc + colWidth;
        }, 0);
      };
      const insertColumnsExtractor = (before2) => (warehouse, target) => onCells(warehouse, target).filter((details) => {
        const checkLocked = before2 ? firstColumnIsLocked : lastColumnIsLocked;
        return !checkLocked(warehouse, details);
      }).map((details) => ({
        details,
        pixelDelta: getColumnsWidth(warehouse, details)
      }));
      const eraseColumnsExtractor = (warehouse, target) => onUnlockedCells(warehouse, target).map((details) => ({
        details,
        pixelDelta: -getColumnsWidth(warehouse, details)
      }));
      const pasteColumnsExtractor = (before2) => (warehouse, target) => onPasteByEditor(warehouse, target).filter((details) => {
        const checkLocked = before2 ? firstColumnIsLocked : lastColumnIsLocked;
        return !checkLocked(warehouse, details.cells);
      });
      const headerCellGenerator = Generators.transform("th");
      const bodyCellGenerator = Generators.transform("td");
      const insertRowsBefore = run(opInsertRowsBefore, onCells, noop, noop, Generators.modification);
      const insertRowsAfter = run(opInsertRowsAfter, onCells, noop, noop, Generators.modification);
      const insertColumnsBefore = run(opInsertColumnsBefore, insertColumnsExtractor(true), adjustAndRedistributeWidths, noop, Generators.modification);
      const insertColumnsAfter = run(opInsertColumnsAfter, insertColumnsExtractor(false), adjustAndRedistributeWidths, noop, Generators.modification);
      const eraseColumns = run(opEraseColumns, eraseColumnsExtractor, adjustAndRedistributeWidths, prune, Generators.modification);
      const eraseRows = run(opEraseRows, onCells, noop, prune, Generators.modification);
      const makeColumnsHeader = run(opMakeColumnsHeader, onUnlockedCells, noop, noop, headerCellGenerator);
      const unmakeColumnsHeader = run(opUnmakeColumnsHeader, onUnlockedCells, noop, noop, bodyCellGenerator);
      const makeRowsHeader = run(opMakeRowsHeader, onUnlockedCells, noop, noop, headerCellGenerator);
      const makeRowsBody = run(opMakeRowsBody, onUnlockedCells, noop, noop, bodyCellGenerator);
      const makeRowsFooter = run(opMakeRowsFooter, onUnlockedCells, noop, noop, bodyCellGenerator);
      const makeCellsHeader = run(opMakeCellsHeader, onUnlockedCells, noop, noop, headerCellGenerator);
      const unmakeCellsHeader = run(opUnmakeCellsHeader, onUnlockedCells, noop, noop, bodyCellGenerator);
      const mergeCells = run(opMergeCells, onUnlockedMergable, resize, noop, Generators.merging);
      const unmergeCells = run(opUnmergeCells, onUnlockedUnmergable, resize, noop, Generators.merging);
      const pasteCells = run(opPasteCells, onPaste, resize, noop, Generators.modification);
      const pasteColsBefore = run(opPasteColsBefore, pasteColumnsExtractor(true), noop, noop, Generators.modification);
      const pasteColsAfter = run(opPasteColsAfter, pasteColumnsExtractor(false), noop, noop, Generators.modification);
      const pasteRowsBefore = run(opPasteRowsBefore, onPasteByEditor, noop, noop, Generators.modification);
      const pasteRowsAfter = run(opPasteRowsAfter, onPasteByEditor, noop, noop, Generators.modification);
      const getColumnsType = opGetColumnsType;
      const getCellsType = opGetCellsType;
      const getRowsType = opGetRowsType;
      const fireNewRow = (editor, row2) => editor.dispatch("NewRow", { node: row2 });
      const fireNewCell = (editor, cell2) => editor.dispatch("NewCell", { node: cell2 });
      const fireTableModified = (editor, table2, data) => {
        editor.dispatch("TableModified", {
          ...data,
          table: table2
        });
      };
      const fireTableSelectionChange = (editor, cells2, start, finish, otherCells) => {
        editor.dispatch("TableSelectionChange", {
          cells: cells2,
          start,
          finish,
          otherCells
        });
      };
      const fireTableSelectionClear = (editor) => {
        editor.dispatch("TableSelectionClear");
      };
      const fireObjectResizeStart = (editor, target, width2, height2, origin) => {
        editor.dispatch("ObjectResizeStart", {
          target,
          width: width2,
          height: height2,
          origin
        });
      };
      const fireObjectResized = (editor, target, width2, height2, origin) => {
        editor.dispatch("ObjectResized", {
          target,
          width: width2,
          height: height2,
          origin
        });
      };
      const styleModified = {
        structure: false,
        style: true
      };
      const structureModified = {
        structure: true,
        style: false
      };
      const styleAndStructureModified = {
        structure: true,
        style: true
      };
      const get$5 = (editor, table2) => {
        if (isTablePercentagesForced(editor)) {
          return TableSize.percentageSize(table2);
        } else if (isTablePixelsForced(editor)) {
          return TableSize.pixelSize(table2);
        } else {
          return TableSize.getTableSize(table2);
        }
      };
      const TableActions = (editor, resizeHandler, cellSelectionHandler) => {
        const isTableBody = (editor2) => name(getBody(editor2)) === "table";
        const lastRowGuard = (table2) => !isTableBody(editor) || getGridSize(table2).rows > 1;
        const lastColumnGuard = (table2) => !isTableBody(editor) || getGridSize(table2).columns > 1;
        const cloneFormats2 = getTableCloneElements(editor);
        const colMutationOp = isResizeTableColumnResizing(editor) ? noop : halve;
        const getTableSectionType2 = (table2) => {
          switch (getTableHeaderType(editor)) {
            case "section":
              return TableSection.section();
            case "sectionCells":
              return TableSection.sectionCells();
            case "cells":
              return TableSection.cells();
            default:
              return TableSection.getTableSectionType(table2, "section");
          }
        };
        const setSelectionFromAction = (table2, result) => result.cursor.fold(() => {
          const cells2 = cells$1(table2);
          return head(cells2).filter(inBody).map((firstCell) => {
            cellSelectionHandler.clearSelectedCells(table2.dom);
            const rng = editor.dom.createRng();
            rng.selectNode(firstCell.dom);
            editor.selection.setRng(rng);
            set$2(firstCell, "data-mce-selected", "1");
            return rng;
          });
        }, (cell2) => {
          const des = freefallRtl(cell2);
          const rng = editor.dom.createRng();
          rng.setStart(des.element.dom, des.offset);
          rng.setEnd(des.element.dom, des.offset);
          editor.selection.setRng(rng);
          cellSelectionHandler.clearSelectedCells(table2.dom);
          return Optional.some(rng);
        });
        const execute = (operation, guard, mutate2, effect) => (table2, target, noEvents = false) => {
          removeDataStyle(table2);
          const doc = SugarElement.fromDom(editor.getDoc());
          const generators = cellOperations(mutate2, doc, cloneFormats2);
          const behaviours = {
            sizing: get$5(editor, table2),
            resize: isResizeTableColumnResizing(editor) ? resizeTable() : preserveTable(),
            section: getTableSectionType2(table2)
          };
          return guard(table2) ? operation(table2, target, generators, behaviours).bind((result) => {
            resizeHandler.refresh(table2.dom);
            each$2(result.newRows, (row2) => {
              fireNewRow(editor, row2.dom);
            });
            each$2(result.newCells, (cell2) => {
              fireNewCell(editor, cell2.dom);
            });
            const range2 = setSelectionFromAction(table2, result);
            if (inBody(table2)) {
              removeDataStyle(table2);
              if (!noEvents) {
                fireTableModified(editor, table2.dom, effect);
              }
            }
            return range2.map((rng) => ({
              rng,
              effect
            }));
          }) : Optional.none();
        };
        const deleteRow = execute(eraseRows, lastRowGuard, noop, structureModified);
        const deleteColumn = execute(eraseColumns, lastColumnGuard, noop, structureModified);
        const insertRowsBefore$1 = execute(insertRowsBefore, always, noop, structureModified);
        const insertRowsAfter$1 = execute(insertRowsAfter, always, noop, structureModified);
        const insertColumnsBefore$1 = execute(insertColumnsBefore, always, colMutationOp, structureModified);
        const insertColumnsAfter$1 = execute(insertColumnsAfter, always, colMutationOp, structureModified);
        const mergeCells$1 = execute(mergeCells, always, noop, structureModified);
        const unmergeCells$1 = execute(unmergeCells, always, noop, structureModified);
        const pasteColsBefore$1 = execute(pasteColsBefore, always, noop, structureModified);
        const pasteColsAfter$1 = execute(pasteColsAfter, always, noop, structureModified);
        const pasteRowsBefore$1 = execute(pasteRowsBefore, always, noop, structureModified);
        const pasteRowsAfter$1 = execute(pasteRowsAfter, always, noop, structureModified);
        const pasteCells$1 = execute(pasteCells, always, noop, styleAndStructureModified);
        const makeCellsHeader$1 = execute(makeCellsHeader, always, noop, structureModified);
        const unmakeCellsHeader$1 = execute(unmakeCellsHeader, always, noop, structureModified);
        const makeColumnsHeader$1 = execute(makeColumnsHeader, always, noop, structureModified);
        const unmakeColumnsHeader$1 = execute(unmakeColumnsHeader, always, noop, structureModified);
        const makeRowsHeader$1 = execute(makeRowsHeader, always, noop, structureModified);
        const makeRowsBody$1 = execute(makeRowsBody, always, noop, structureModified);
        const makeRowsFooter$1 = execute(makeRowsFooter, always, noop, structureModified);
        const getTableCellType = getCellsType;
        const getTableColType = getColumnsType;
        const getTableRowType = getRowsType;
        return {
          deleteRow,
          deleteColumn,
          insertRowsBefore: insertRowsBefore$1,
          insertRowsAfter: insertRowsAfter$1,
          insertColumnsBefore: insertColumnsBefore$1,
          insertColumnsAfter: insertColumnsAfter$1,
          mergeCells: mergeCells$1,
          unmergeCells: unmergeCells$1,
          pasteColsBefore: pasteColsBefore$1,
          pasteColsAfter: pasteColsAfter$1,
          pasteRowsBefore: pasteRowsBefore$1,
          pasteRowsAfter: pasteRowsAfter$1,
          pasteCells: pasteCells$1,
          makeCellsHeader: makeCellsHeader$1,
          unmakeCellsHeader: unmakeCellsHeader$1,
          makeColumnsHeader: makeColumnsHeader$1,
          unmakeColumnsHeader: unmakeColumnsHeader$1,
          makeRowsHeader: makeRowsHeader$1,
          makeRowsBody: makeRowsBody$1,
          makeRowsFooter: makeRowsFooter$1,
          getTableRowType,
          getTableCellType,
          getTableColType
        };
      };
      const constrainSpan = (element, property, value2) => {
        const currentColspan = getAttrValue(element, property, 1);
        if (value2 === 1 || currentColspan <= 1) {
          remove$7(element, property);
        } else {
          set$2(element, property, Math.min(value2, currentColspan));
        }
      };
      const isColInRange = (minColRange, maxColRange) => (cell2) => {
        const endCol = cell2.column + cell2.colspan - 1;
        const startCol = cell2.column;
        return endCol >= minColRange && startCol < maxColRange;
      };
      const generateColGroup = (house, minColRange, maxColRange) => {
        if (Warehouse.hasColumns(house)) {
          const colsToCopy = filter$2(Warehouse.justColumns(house), isColInRange(minColRange, maxColRange));
          const copiedCols = map$1(colsToCopy, (c) => {
            const clonedCol = deep(c.element);
            constrainSpan(clonedCol, "span", maxColRange - minColRange);
            return clonedCol;
          });
          const fakeColgroup = SugarElement.fromTag("colgroup");
          append(fakeColgroup, copiedCols);
          return [fakeColgroup];
        } else {
          return [];
        }
      };
      const generateRows = (house, minColRange, maxColRange) => map$1(house.all, (row2) => {
        const cellsToCopy = filter$2(row2.cells, isColInRange(minColRange, maxColRange));
        const copiedCells = map$1(cellsToCopy, (cell2) => {
          const clonedCell = deep(cell2.element);
          constrainSpan(clonedCell, "colspan", maxColRange - minColRange);
          return clonedCell;
        });
        const fakeTR = SugarElement.fromTag("tr");
        append(fakeTR, copiedCells);
        return fakeTR;
      });
      const copyCols = (table2, target) => {
        const house = Warehouse.fromTable(table2);
        const details = onUnlockedCells(house, target);
        return details.map((selectedCells) => {
          const lastSelectedCell = selectedCells[selectedCells.length - 1];
          const minColRange = selectedCells[0].column;
          const maxColRange = lastSelectedCell.column + lastSelectedCell.colspan;
          const fakeColGroups = generateColGroup(house, minColRange, maxColRange);
          const fakeRows = generateRows(house, minColRange, maxColRange);
          return [
            ...fakeColGroups,
            ...fakeRows
          ];
        });
      };
      const copyRows = (table2, target, generators) => {
        const warehouse = Warehouse.fromTable(table2);
        const details = onCells(warehouse, target);
        return details.bind((selectedCells) => {
          const grid2 = toGrid(warehouse, generators, false);
          const rows2 = extractGridDetails(grid2).rows;
          const slicedGrid = rows2.slice(selectedCells[0].row, selectedCells[selectedCells.length - 1].row + selectedCells[selectedCells.length - 1].rowspan);
          const filteredGrid = bind$2(slicedGrid, (row2) => {
            const newCells = filter$2(row2.cells, (cell2) => !cell2.isLocked);
            return newCells.length > 0 ? [{
              ...row2,
              cells: newCells
            }] : [];
          });
          const slicedDetails = toDetailList(filteredGrid);
          return someIf(slicedDetails.length > 0, slicedDetails);
        }).map((slicedDetails) => copy(slicedDetails));
      };
      const adt$5 = Adt.generate([
        { invalid: ["raw"] },
        { pixels: ["value"] },
        { percent: ["value"] }
      ]);
      const validateFor = (suffix, type2, value2) => {
        const rawAmount = value2.substring(0, value2.length - suffix.length);
        const amount = parseFloat(rawAmount);
        return rawAmount === amount.toString() ? type2(amount) : adt$5.invalid(value2);
      };
      const from = (value2) => {
        if (endsWith(value2, "%")) {
          return validateFor("%", adt$5.percent, value2);
        }
        if (endsWith(value2, "px")) {
          return validateFor("px", adt$5.pixels, value2);
        }
        return adt$5.invalid(value2);
      };
      const Size = {
        ...adt$5,
        from
      };
      const redistributeToPercent = (widths, totalWidth) => {
        return map$1(widths, (w) => {
          const colType = Size.from(w);
          return colType.fold(() => {
            return w;
          }, (px) => {
            const ratio = px / totalWidth * 100;
            return ratio + "%";
          }, (pc) => {
            return pc + "%";
          });
        });
      };
      const redistributeToPx = (widths, totalWidth, newTotalWidth) => {
        const scale = newTotalWidth / totalWidth;
        return map$1(widths, (w) => {
          const colType = Size.from(w);
          return colType.fold(() => {
            return w;
          }, (px) => {
            return px * scale + "px";
          }, (pc) => {
            return pc / 100 * newTotalWidth + "px";
          });
        });
      };
      const redistributeEmpty = (newWidthType, columns2) => {
        const f = newWidthType.fold(() => constant(""), (pixels) => {
          const num = pixels / columns2;
          return constant(num + "px");
        }, () => {
          const num = 100 / columns2;
          return constant(num + "%");
        });
        return range$1(columns2, f);
      };
      const redistributeValues = (newWidthType, widths, totalWidth) => {
        return newWidthType.fold(() => {
          return widths;
        }, (px) => {
          return redistributeToPx(widths, totalWidth, px);
        }, (_pc) => {
          return redistributeToPercent(widths, totalWidth);
        });
      };
      const redistribute$1 = (widths, totalWidth, newWidth) => {
        const newType = Size.from(newWidth);
        const floats = forall(widths, (s) => {
          return s === "0px";
        }) ? redistributeEmpty(newType, widths.length) : redistributeValues(newType, widths, totalWidth);
        return normalize(floats);
      };
      const sum = (values2, fallback2) => {
        if (values2.length === 0) {
          return fallback2;
        }
        return foldr(values2, (rest, v) => {
          return Size.from(v).fold(constant(0), identity, identity) + rest;
        }, 0);
      };
      const roundDown = (num, unit) => {
        const floored = Math.floor(num);
        return {
          value: floored + unit,
          remainder: num - floored
        };
      };
      const add$3 = (value2, amount) => {
        return Size.from(value2).fold(constant(value2), (px) => {
          return px + amount + "px";
        }, (pc) => {
          return pc + amount + "%";
        });
      };
      const normalize = (values2) => {
        if (values2.length === 0) {
          return values2;
        }
        const scan2 = foldr(values2, (rest, value2) => {
          const info = Size.from(value2).fold(() => ({
            value: value2,
            remainder: 0
          }), (num) => roundDown(num, "px"), (num) => ({
            value: num + "%",
            remainder: 0
          }));
          return {
            output: [info.value].concat(rest.output),
            remainder: rest.remainder + info.remainder
          };
        }, {
          output: [],
          remainder: 0
        });
        const r2 = scan2.output;
        return r2.slice(0, r2.length - 1).concat([add$3(r2[r2.length - 1], Math.round(scan2.remainder))]);
      };
      const validate = Size.from;
      const redistributeToW = (newWidths, cells2, unit) => {
        each$2(cells2, (cell2) => {
          const widths = newWidths.slice(cell2.column, cell2.colspan + cell2.column);
          const w = sum(widths, minWidth());
          set$1(cell2.element, "width", w + unit);
        });
      };
      const redistributeToColumns = (newWidths, columns2, unit) => {
        each$2(columns2, (column, index) => {
          const width2 = sum([newWidths[index]], minWidth());
          set$1(column.element, "width", width2 + unit);
        });
      };
      const redistributeToH = (newHeights, rows2, cells2, unit) => {
        each$2(cells2, (cell2) => {
          const heights = newHeights.slice(cell2.row, cell2.rowspan + cell2.row);
          const h = sum(heights, minHeight());
          set$1(cell2.element, "height", h + unit);
        });
        each$2(rows2, (row2, i) => {
          set$1(row2.element, "height", newHeights[i]);
        });
      };
      const getUnit = (newSize) => {
        return validate(newSize).fold(constant("px"), constant("px"), constant("%"));
      };
      const redistribute = (table2, optWidth, optHeight) => {
        const warehouse = Warehouse.fromTable(table2);
        const rows2 = warehouse.all;
        const cells2 = Warehouse.justCells(warehouse);
        const columns2 = Warehouse.justColumns(warehouse);
        optWidth.each((newWidth) => {
          const widthUnit = getUnit(newWidth);
          const totalWidth = get$9(table2);
          const oldWidths = getRawWidths(warehouse, table2);
          const nuWidths = redistribute$1(oldWidths, totalWidth, newWidth);
          if (Warehouse.hasColumns(warehouse)) {
            redistributeToColumns(nuWidths, columns2, widthUnit);
          } else {
            redistributeToW(nuWidths, cells2, widthUnit);
          }
          set$1(table2, "width", newWidth);
        });
        optHeight.each((newHeight) => {
          const hUnit = getUnit(newHeight);
          const totalHeight = get$8(table2);
          const oldHeights = getRawHeights(warehouse, table2, height);
          const nuHeights = redistribute$1(oldHeights, totalHeight, newHeight);
          redistributeToH(nuHeights, rows2, cells2, hUnit);
          set$1(table2, "height", newHeight);
        });
      };
      const isPercentSizing = isPercentSizing$1;
      const isPixelSizing = isPixelSizing$1;
      const isNoneSizing = isNoneSizing$1;
      const cleanupLegacyAttributes = (element) => {
        remove$7(element, "width");
      };
      const convertToPercentSize = (table2) => {
        const newWidth = getPercentTableWidth(table2);
        redistribute(table2, Optional.some(newWidth), Optional.none());
        cleanupLegacyAttributes(table2);
      };
      const convertToPixelSize = (table2) => {
        const newWidth = getPixelTableWidth(table2);
        redistribute(table2, Optional.some(newWidth), Optional.none());
        cleanupLegacyAttributes(table2);
      };
      const convertToNoneSize = (table2) => {
        remove$5(table2, "width");
        const columns2 = columns$1(table2);
        const rowElements = columns2.length > 0 ? columns2 : cells$1(table2);
        each$2(rowElements, (cell2) => {
          remove$5(cell2, "width");
          cleanupLegacyAttributes(cell2);
        });
        cleanupLegacyAttributes(table2);
      };
      const DefaultRenderOptions = {
        styles: {
          "border-collapse": "collapse",
          "width": "100%"
        },
        attributes: { border: "1" },
        colGroups: false
      };
      const tableHeaderCell = () => SugarElement.fromTag("th");
      const tableCell = () => SugarElement.fromTag("td");
      const tableColumn = () => SugarElement.fromTag("col");
      const createRow = (columns2, rowHeaders, columnHeaders, rowIndex) => {
        const tr = SugarElement.fromTag("tr");
        for (let j = 0; j < columns2; j++) {
          const td = rowIndex < rowHeaders || j < columnHeaders ? tableHeaderCell() : tableCell();
          if (j < columnHeaders) {
            set$2(td, "scope", "row");
          }
          if (rowIndex < rowHeaders) {
            set$2(td, "scope", "col");
          }
          append$1(td, SugarElement.fromTag("br"));
          append$1(tr, td);
        }
        return tr;
      };
      const createGroupRow = (columns2) => {
        const columnGroup = SugarElement.fromTag("colgroup");
        range$1(columns2, () => append$1(columnGroup, tableColumn()));
        return columnGroup;
      };
      const createRows = (rows2, columns2, rowHeaders, columnHeaders) => range$1(rows2, (r2) => createRow(columns2, rowHeaders, columnHeaders, r2));
      const render = (rows2, columns2, rowHeaders, columnHeaders, headerType, renderOpts = DefaultRenderOptions) => {
        const table2 = SugarElement.fromTag("table");
        const rowHeadersGoInThead = headerType !== "cells";
        setAll(table2, renderOpts.styles);
        setAll$1(table2, renderOpts.attributes);
        if (renderOpts.colGroups) {
          append$1(table2, createGroupRow(columns2));
        }
        const actualRowHeaders = Math.min(rows2, rowHeaders);
        if (rowHeadersGoInThead && rowHeaders > 0) {
          const thead = SugarElement.fromTag("thead");
          append$1(table2, thead);
          const theadRowHeaders = headerType === "sectionCells" ? actualRowHeaders : 0;
          const theadRows = createRows(rowHeaders, columns2, theadRowHeaders, columnHeaders);
          append(thead, theadRows);
        }
        const tbody = SugarElement.fromTag("tbody");
        append$1(table2, tbody);
        const numRows = rowHeadersGoInThead ? rows2 - actualRowHeaders : rows2;
        const numRowHeaders = rowHeadersGoInThead ? 0 : rowHeaders;
        const tbodyRows = createRows(numRows, columns2, numRowHeaders, columnHeaders);
        append(tbody, tbodyRows);
        return table2;
      };
      const get$4 = (element) => element.dom.innerHTML;
      const getOuter = (element) => {
        const container = SugarElement.fromTag("div");
        const clone2 = SugarElement.fromDom(element.dom.cloneNode(true));
        append$1(container, clone2);
        return get$4(container);
      };
      const placeCaretInCell = (editor, cell2) => {
        editor.selection.select(cell2.dom, true);
        editor.selection.collapse(true);
      };
      const selectFirstCellInTable = (editor, tableElm) => {
        descendant(tableElm, "td,th").each(curry(placeCaretInCell, editor));
      };
      const fireEvents = (editor, table2) => {
        each$2(descendants(table2, "tr"), (row2) => {
          fireNewRow(editor, row2.dom);
          each$2(descendants(row2, "th,td"), (cell2) => {
            fireNewCell(editor, cell2.dom);
          });
        });
      };
      const isPercentage = (width2) => isString(width2) && width2.indexOf("%") !== -1;
      const insert = (editor, columns2, rows2, colHeaders, rowHeaders) => {
        const defaultStyles = getTableDefaultStyles(editor);
        const options = {
          styles: defaultStyles,
          attributes: getTableDefaultAttributes(editor),
          colGroups: tableUseColumnGroup(editor)
        };
        editor.undoManager.ignore(() => {
          const table2 = render(rows2, columns2, rowHeaders, colHeaders, getTableHeaderType(editor), options);
          set$2(table2, "data-mce-id", "__mce");
          const html = getOuter(table2);
          editor.insertContent(html);
          editor.addVisual();
        });
        return descendant(getBody(editor), 'table[data-mce-id="__mce"]').map((table2) => {
          if (isTablePixelsForced(editor)) {
            convertToPixelSize(table2);
          } else if (isTableResponsiveForced(editor)) {
            convertToNoneSize(table2);
          } else if (isTablePercentagesForced(editor) || isPercentage(defaultStyles.width)) {
            convertToPercentSize(table2);
          }
          removeDataStyle(table2);
          remove$7(table2, "data-mce-id");
          fireEvents(editor, table2);
          selectFirstCellInTable(editor, table2);
          return table2.dom;
        }).getOrNull();
      };
      const insertTable = (editor, rows2, columns2, options = {}) => {
        const checkInput = (val) => isNumber(val) && val > 0;
        if (checkInput(rows2) && checkInput(columns2)) {
          const headerRows = options.headerRows || 0;
          const headerColumns = options.headerColumns || 0;
          return insert(editor, columns2, rows2, headerColumns, headerRows);
        } else {
          console.error("Invalid values for mceInsertTable - rows and columns values are required to insert a table.");
          return null;
        }
      };
      var global = tinymce.util.Tools.resolve("tinymce.FakeClipboard");
      const tableTypeBase = "x-tinymce/dom-table-";
      const tableTypeRow = tableTypeBase + "rows";
      const tableTypeColumn = tableTypeBase + "columns";
      const setData = (items) => {
        const fakeClipboardItem = global.FakeClipboardItem(items);
        global.write([fakeClipboardItem]);
      };
      const getData = (type2) => {
        var _a;
        const items = (_a = global.read()) !== null && _a !== void 0 ? _a : [];
        return findMap(items, (item) => Optional.from(item.getType(type2)));
      };
      const clearData = (type2) => {
        if (getData(type2).isSome()) {
          global.clear();
        }
      };
      const setRows = (rowsOpt) => {
        rowsOpt.fold(clearRows, (rows2) => setData({ [tableTypeRow]: rows2 }));
      };
      const getRows = () => getData(tableTypeRow);
      const clearRows = () => clearData(tableTypeRow);
      const setColumns = (columnsOpt) => {
        columnsOpt.fold(clearColumns, (columns2) => setData({ [tableTypeColumn]: columns2 }));
      };
      const getColumns = () => getData(tableTypeColumn);
      const clearColumns = () => clearData(tableTypeColumn);
      const getSelectionStartCellOrCaption = (editor) => getSelectionCellOrCaption(getSelectionStart(editor), getIsRoot(editor)).filter(isInEditableContext$1);
      const getSelectionStartCell = (editor) => getSelectionCell(getSelectionStart(editor), getIsRoot(editor)).filter(isInEditableContext$1);
      const registerCommands = (editor, actions) => {
        const isRoot = getIsRoot(editor);
        const eraseTable = () => getSelectionStartCellOrCaption(editor).each((cellOrCaption) => {
          table(cellOrCaption, isRoot).filter(not(isRoot)).each((table2) => {
            const cursor = SugarElement.fromText("");
            after$5(table2, cursor);
            remove$6(table2);
            if (editor.dom.isEmpty(editor.getBody())) {
              editor.setContent("");
              editor.selection.setCursorLocation();
            } else {
              const rng = editor.dom.createRng();
              rng.setStart(cursor.dom, 0);
              rng.setEnd(cursor.dom, 0);
              editor.selection.setRng(rng);
              editor.nodeChanged();
            }
          });
        });
        const setSizingMode = (sizing) => getSelectionStartCellOrCaption(editor).each((cellOrCaption) => {
          const isForcedSizing = isTableResponsiveForced(editor) || isTablePixelsForced(editor) || isTablePercentagesForced(editor);
          if (!isForcedSizing) {
            table(cellOrCaption, isRoot).each((table2) => {
              if (sizing === "relative" && !isPercentSizing(table2)) {
                convertToPercentSize(table2);
              } else if (sizing === "fixed" && !isPixelSizing(table2)) {
                convertToPixelSize(table2);
              } else if (sizing === "responsive" && !isNoneSizing(table2)) {
                convertToNoneSize(table2);
              }
              removeDataStyle(table2);
              fireTableModified(editor, table2.dom, structureModified);
            });
          }
        });
        const getTableFromCell = (cell2) => table(cell2, isRoot);
        const performActionOnSelection = (action) => getSelectionStartCell(editor).bind((cell2) => getTableFromCell(cell2).map((table2) => action(table2, cell2)));
        const toggleTableClass = (_ui, clazz) => {
          performActionOnSelection((table2) => {
            editor.formatter.toggle("tableclass", { value: clazz }, table2.dom);
            fireTableModified(editor, table2.dom, styleModified);
          });
        };
        const toggleTableCellClass = (_ui, clazz) => {
          performActionOnSelection((table2) => {
            const selectedCells = getCellsFromSelection(editor);
            const allHaveClass = forall(selectedCells, (cell2) => editor.formatter.match("tablecellclass", { value: clazz }, cell2.dom));
            const formatterAction = allHaveClass ? editor.formatter.remove : editor.formatter.apply;
            each$2(selectedCells, (cell2) => formatterAction("tablecellclass", { value: clazz }, cell2.dom));
            fireTableModified(editor, table2.dom, styleModified);
          });
        };
        const toggleCaption = () => {
          getSelectionStartCellOrCaption(editor).each((cellOrCaption) => {
            table(cellOrCaption, isRoot).each((table2) => {
              child(table2, "caption").fold(() => {
                const caption = SugarElement.fromTag("caption");
                append$1(caption, SugarElement.fromText("Caption"));
                appendAt(table2, caption, 0);
                editor.selection.setCursorLocation(caption.dom, 0);
              }, (caption) => {
                if (isTag("caption")(cellOrCaption)) {
                  one("td", table2).each((td) => editor.selection.setCursorLocation(td.dom, 0));
                }
                remove$6(caption);
              });
              fireTableModified(editor, table2.dom, structureModified);
            });
          });
        };
        const postExecute = (_data) => {
          editor.focus();
        };
        const actOnSelection = (execute, noEvents = false) => performActionOnSelection((table2, startCell) => {
          const targets = forMenu(getCellsFromSelection(editor), table2, startCell);
          execute(table2, targets, noEvents).each(postExecute);
        });
        const copyRowSelection = () => performActionOnSelection((table2, startCell) => {
          const targets = forMenu(getCellsFromSelection(editor), table2, startCell);
          const generators = cellOperations(noop, SugarElement.fromDom(editor.getDoc()), Optional.none());
          return copyRows(table2, targets, generators);
        });
        const copyColSelection = () => performActionOnSelection((table2, startCell) => {
          const targets = forMenu(getCellsFromSelection(editor), table2, startCell);
          return copyCols(table2, targets);
        });
        const pasteOnSelection = (execute, getRows2) => getRows2().each((rows2) => {
          const clonedRows = map$1(rows2, (row2) => deep(row2));
          performActionOnSelection((table2, startCell) => {
            const generators = paste$1(SugarElement.fromDom(editor.getDoc()));
            const targets = pasteRows(getCellsFromSelection(editor), startCell, clonedRows, generators);
            execute(table2, targets).each(postExecute);
          });
        });
        const actOnType = (getAction) => (_ui, args) => get$c(args, "type").each((type2) => {
          actOnSelection(getAction(type2), args.no_events);
        });
        each$1({
          mceTableSplitCells: () => actOnSelection(actions.unmergeCells),
          mceTableMergeCells: () => actOnSelection(actions.mergeCells),
          mceTableInsertRowBefore: () => actOnSelection(actions.insertRowsBefore),
          mceTableInsertRowAfter: () => actOnSelection(actions.insertRowsAfter),
          mceTableInsertColBefore: () => actOnSelection(actions.insertColumnsBefore),
          mceTableInsertColAfter: () => actOnSelection(actions.insertColumnsAfter),
          mceTableDeleteCol: () => actOnSelection(actions.deleteColumn),
          mceTableDeleteRow: () => actOnSelection(actions.deleteRow),
          mceTableCutCol: () => copyColSelection().each((selection2) => {
            setColumns(selection2);
            actOnSelection(actions.deleteColumn);
          }),
          mceTableCutRow: () => copyRowSelection().each((selection2) => {
            setRows(selection2);
            actOnSelection(actions.deleteRow);
          }),
          mceTableCopyCol: () => copyColSelection().each((selection2) => setColumns(selection2)),
          mceTableCopyRow: () => copyRowSelection().each((selection2) => setRows(selection2)),
          mceTablePasteColBefore: () => pasteOnSelection(actions.pasteColsBefore, getColumns),
          mceTablePasteColAfter: () => pasteOnSelection(actions.pasteColsAfter, getColumns),
          mceTablePasteRowBefore: () => pasteOnSelection(actions.pasteRowsBefore, getRows),
          mceTablePasteRowAfter: () => pasteOnSelection(actions.pasteRowsAfter, getRows),
          mceTableDelete: eraseTable,
          mceTableCellToggleClass: toggleTableCellClass,
          mceTableToggleClass: toggleTableClass,
          mceTableToggleCaption: toggleCaption,
          mceTableSizingMode: (_ui, sizing) => setSizingMode(sizing),
          mceTableCellType: actOnType((type2) => type2 === "th" ? actions.makeCellsHeader : actions.unmakeCellsHeader),
          mceTableColType: actOnType((type2) => type2 === "th" ? actions.makeColumnsHeader : actions.unmakeColumnsHeader),
          mceTableRowType: actOnType((type2) => {
            switch (type2) {
              case "header":
                return actions.makeRowsHeader;
              case "footer":
                return actions.makeRowsFooter;
              default:
                return actions.makeRowsBody;
            }
          })
        }, (func, name2) => editor.addCommand(name2, func));
        editor.addCommand("mceInsertTable", (_ui, args) => {
          insertTable(editor, args.rows, args.columns, args.options);
        });
        editor.addCommand("mceTableApplyCellStyle", (_ui, args) => {
          const getFormatName = (style) => "tablecell" + style.toLowerCase().replace("-", "");
          if (!isObject(args)) {
            return;
          }
          const cells2 = filter$2(getCellsFromSelection(editor), isInEditableContext$1);
          if (cells2.length === 0) {
            return;
          }
          const validArgs = filter$1(args, (value2, style) => editor.formatter.has(getFormatName(style)) && isString(value2));
          if (isEmpty(validArgs)) {
            return;
          }
          each$1(validArgs, (value2, style) => {
            const formatName = getFormatName(style);
            each$2(cells2, (cell2) => {
              if (value2 === "") {
                editor.formatter.remove(formatName, { value: null }, cell2.dom, true);
              } else {
                editor.formatter.apply(formatName, { value: value2 }, cell2.dom);
              }
            });
          });
          getTableFromCell(cells2[0]).each((table2) => fireTableModified(editor, table2.dom, styleModified));
        });
      };
      const registerQueryCommands = (editor, actions) => {
        const isRoot = getIsRoot(editor);
        const lookupOnSelection = (action) => getSelectionCell(getSelectionStart(editor)).bind((cell2) => table(cell2, isRoot).map((table2) => {
          const targets = forMenu(getCellsFromSelection(editor), table2, cell2);
          return action(table2, targets);
        })).getOr("");
        each$1({
          mceTableRowType: () => lookupOnSelection(actions.getTableRowType),
          mceTableCellType: () => lookupOnSelection(actions.getTableCellType),
          mceTableColType: () => lookupOnSelection(actions.getTableColType)
        }, (func, name2) => editor.addQueryValueHandler(name2, func));
      };
      const adt$4 = Adt.generate([
        { before: ["element"] },
        {
          on: [
            "element",
            "offset"
          ]
        },
        { after: ["element"] }
      ]);
      const cata$1 = (subject, onBefore, onOn, onAfter) => subject.fold(onBefore, onOn, onAfter);
      const getStart$1 = (situ) => situ.fold(identity, identity, identity);
      const before$2 = adt$4.before;
      const on = adt$4.on;
      const after$3 = adt$4.after;
      const Situ = {
        before: before$2,
        on,
        after: after$3,
        cata: cata$1,
        getStart: getStart$1
      };
      const create$4 = (selection2, kill) => ({
        selection: selection2,
        kill
      });
      const Response = { create: create$4 };
      const selectNode = (win, element) => {
        const rng = win.document.createRange();
        rng.selectNode(element.dom);
        return rng;
      };
      const selectNodeContents = (win, element) => {
        const rng = win.document.createRange();
        selectNodeContentsUsing(rng, element);
        return rng;
      };
      const selectNodeContentsUsing = (rng, element) => rng.selectNodeContents(element.dom);
      const setStart = (rng, situ) => {
        situ.fold((e) => {
          rng.setStartBefore(e.dom);
        }, (e, o) => {
          rng.setStart(e.dom, o);
        }, (e) => {
          rng.setStartAfter(e.dom);
        });
      };
      const setFinish = (rng, situ) => {
        situ.fold((e) => {
          rng.setEndBefore(e.dom);
        }, (e, o) => {
          rng.setEnd(e.dom, o);
        }, (e) => {
          rng.setEndAfter(e.dom);
        });
      };
      const relativeToNative = (win, startSitu, finishSitu) => {
        const range2 = win.document.createRange();
        setStart(range2, startSitu);
        setFinish(range2, finishSitu);
        return range2;
      };
      const exactToNative = (win, start, soffset, finish, foffset) => {
        const rng = win.document.createRange();
        rng.setStart(start.dom, soffset);
        rng.setEnd(finish.dom, foffset);
        return rng;
      };
      const toRect = (rect) => ({
        left: rect.left,
        top: rect.top,
        right: rect.right,
        bottom: rect.bottom,
        width: rect.width,
        height: rect.height
      });
      const getFirstRect$1 = (rng) => {
        const rects = rng.getClientRects();
        const rect = rects.length > 0 ? rects[0] : rng.getBoundingClientRect();
        return rect.width > 0 || rect.height > 0 ? Optional.some(rect).map(toRect) : Optional.none();
      };
      const adt$3 = Adt.generate([
        {
          ltr: [
            "start",
            "soffset",
            "finish",
            "foffset"
          ]
        },
        {
          rtl: [
            "start",
            "soffset",
            "finish",
            "foffset"
          ]
        }
      ]);
      const fromRange = (win, type2, range2) => type2(SugarElement.fromDom(range2.startContainer), range2.startOffset, SugarElement.fromDom(range2.endContainer), range2.endOffset);
      const getRanges = (win, selection2) => selection2.match({
        domRange: (rng) => {
          return {
            ltr: constant(rng),
            rtl: Optional.none
          };
        },
        relative: (startSitu, finishSitu) => {
          return {
            ltr: cached(() => relativeToNative(win, startSitu, finishSitu)),
            rtl: cached(() => Optional.some(relativeToNative(win, finishSitu, startSitu)))
          };
        },
        exact: (start, soffset, finish, foffset) => {
          return {
            ltr: cached(() => exactToNative(win, start, soffset, finish, foffset)),
            rtl: cached(() => Optional.some(exactToNative(win, finish, foffset, start, soffset)))
          };
        }
      });
      const doDiagnose = (win, ranges) => {
        const rng = ranges.ltr();
        if (rng.collapsed) {
          const reversed = ranges.rtl().filter((rev) => rev.collapsed === false);
          return reversed.map((rev) => adt$3.rtl(SugarElement.fromDom(rev.endContainer), rev.endOffset, SugarElement.fromDom(rev.startContainer), rev.startOffset)).getOrThunk(() => fromRange(win, adt$3.ltr, rng));
        } else {
          return fromRange(win, adt$3.ltr, rng);
        }
      };
      const diagnose = (win, selection2) => {
        const ranges = getRanges(win, selection2);
        return doDiagnose(win, ranges);
      };
      const asLtrRange = (win, selection2) => {
        const diagnosis = diagnose(win, selection2);
        return diagnosis.match({
          ltr: (start, soffset, finish, foffset) => {
            const rng = win.document.createRange();
            rng.setStart(start.dom, soffset);
            rng.setEnd(finish.dom, foffset);
            return rng;
          },
          rtl: (start, soffset, finish, foffset) => {
            const rng = win.document.createRange();
            rng.setStart(finish.dom, foffset);
            rng.setEnd(start.dom, soffset);
            return rng;
          }
        });
      };
      adt$3.ltr;
      adt$3.rtl;
      const create$3 = (start, soffset, finish, foffset) => ({
        start,
        soffset,
        finish,
        foffset
      });
      const SimRange = { create: create$3 };
      const create$2 = (start, soffset, finish, foffset) => {
        return {
          start: Situ.on(start, soffset),
          finish: Situ.on(finish, foffset)
        };
      };
      const Situs = { create: create$2 };
      const convertToRange = (win, selection2) => {
        const rng = asLtrRange(win, selection2);
        return SimRange.create(SugarElement.fromDom(rng.startContainer), rng.startOffset, SugarElement.fromDom(rng.endContainer), rng.endOffset);
      };
      const makeSitus = Situs.create;
      const sync = (container, isRoot, start, soffset, finish, foffset, selectRange) => {
        if (!(eq$1(start, finish) && soffset === foffset)) {
          return closest$1(start, "td,th", isRoot).bind((s) => {
            return closest$1(finish, "td,th", isRoot).bind((f) => {
              return detect(container, isRoot, s, f, selectRange);
            });
          });
        } else {
          return Optional.none();
        }
      };
      const detect = (container, isRoot, start, finish, selectRange) => {
        if (!eq$1(start, finish)) {
          return identify(start, finish, isRoot).bind((cellSel) => {
            const boxes = cellSel.boxes.getOr([]);
            if (boxes.length > 1) {
              selectRange(container, boxes, cellSel.start, cellSel.finish);
              return Optional.some(Response.create(Optional.some(makeSitus(start, 0, start, getEnd(start))), true));
            } else {
              return Optional.none();
            }
          });
        } else {
          return Optional.none();
        }
      };
      const update = (rows2, columns2, container, selected, annotations) => {
        const updateSelection = (newSels) => {
          annotations.clearBeforeUpdate(container);
          annotations.selectRange(container, newSels.boxes, newSels.start, newSels.finish);
          return newSels.boxes;
        };
        return shiftSelection(selected, rows2, columns2, annotations.firstSelectedSelector, annotations.lastSelectedSelector).map(updateSelection);
      };
      const traverse = (item, mode) => ({
        item,
        mode
      });
      const backtrack = (universe2, item, _direction, transition = sidestep) => {
        return universe2.property().parent(item).map((p) => {
          return traverse(p, transition);
        });
      };
      const sidestep = (universe2, item, direction, transition = advance) => {
        return direction.sibling(universe2, item).map((p) => {
          return traverse(p, transition);
        });
      };
      const advance = (universe2, item, direction, transition = advance) => {
        const children2 = universe2.property().children(item);
        const result = direction.first(children2);
        return result.map((r2) => {
          return traverse(r2, transition);
        });
      };
      const successors = [
        {
          current: backtrack,
          next: sidestep,
          fallback: Optional.none()
        },
        {
          current: sidestep,
          next: advance,
          fallback: Optional.some(backtrack)
        },
        {
          current: advance,
          next: advance,
          fallback: Optional.some(sidestep)
        }
      ];
      const go = (universe2, item, mode, direction, rules = successors) => {
        const ruleOpt = find$1(rules, (succ) => {
          return succ.current === mode;
        });
        return ruleOpt.bind((rule) => {
          return rule.current(universe2, item, direction, rule.next).orThunk(() => {
            return rule.fallback.bind((fb) => {
              return go(universe2, item, fb, direction);
            });
          });
        });
      };
      const left$1 = () => {
        const sibling = (universe2, item) => {
          return universe2.query().prevSibling(item);
        };
        const first2 = (children2) => {
          return children2.length > 0 ? Optional.some(children2[children2.length - 1]) : Optional.none();
        };
        return {
          sibling,
          first: first2
        };
      };
      const right$1 = () => {
        const sibling = (universe2, item) => {
          return universe2.query().nextSibling(item);
        };
        const first2 = (children2) => {
          return children2.length > 0 ? Optional.some(children2[0]) : Optional.none();
        };
        return {
          sibling,
          first: first2
        };
      };
      const Walkers = {
        left: left$1,
        right: right$1
      };
      const hone = (universe2, item, predicate, mode, direction, isRoot) => {
        const next = go(universe2, item, mode, direction);
        return next.bind((n) => {
          if (isRoot(n.item)) {
            return Optional.none();
          } else {
            return predicate(n.item) ? Optional.some(n.item) : hone(universe2, n.item, predicate, n.mode, direction, isRoot);
          }
        });
      };
      const left = (universe2, item, predicate, isRoot) => {
        return hone(universe2, item, predicate, sidestep, Walkers.left(), isRoot);
      };
      const right = (universe2, item, predicate, isRoot) => {
        return hone(universe2, item, predicate, sidestep, Walkers.right(), isRoot);
      };
      const isLeaf = (universe2) => (element) => universe2.property().children(element).length === 0;
      const before$1 = (universe2, item, isRoot) => {
        return seekLeft$1(universe2, item, isLeaf(universe2), isRoot);
      };
      const after$2 = (universe2, item, isRoot) => {
        return seekRight$1(universe2, item, isLeaf(universe2), isRoot);
      };
      const seekLeft$1 = left;
      const seekRight$1 = right;
      const universe = DomUniverse();
      const before = (element, isRoot) => {
        return before$1(universe, element, isRoot);
      };
      const after$1 = (element, isRoot) => {
        return after$2(universe, element, isRoot);
      };
      const seekLeft = (element, predicate, isRoot) => {
        return seekLeft$1(universe, element, predicate, isRoot);
      };
      const seekRight = (element, predicate, isRoot) => {
        return seekRight$1(universe, element, predicate, isRoot);
      };
      const ancestor = (scope, predicate, isRoot) => ancestor$2(scope, predicate, isRoot).isSome();
      const adt$2 = Adt.generate([
        { none: ["message"] },
        { success: [] },
        { failedUp: ["cell"] },
        { failedDown: ["cell"] }
      ]);
      const isOverlapping = (bridge, before2, after2) => {
        const beforeBounds = bridge.getRect(before2);
        const afterBounds = bridge.getRect(after2);
        return afterBounds.right > beforeBounds.left && afterBounds.left < beforeBounds.right;
      };
      const isRow = (elem) => {
        return closest$1(elem, "tr");
      };
      const verify = (bridge, before2, beforeOffset, after2, afterOffset, failure, isRoot) => {
        return closest$1(after2, "td,th", isRoot).bind((afterCell) => {
          return closest$1(before2, "td,th", isRoot).map((beforeCell) => {
            if (!eq$1(afterCell, beforeCell)) {
              return sharedOne(isRow, [
                afterCell,
                beforeCell
              ]).fold(() => {
                return isOverlapping(bridge, beforeCell, afterCell) ? adt$2.success() : failure(beforeCell);
              }, (_sharedRow) => {
                return failure(beforeCell);
              });
            } else {
              return eq$1(after2, afterCell) && getEnd(afterCell) === afterOffset ? failure(beforeCell) : adt$2.none("in same cell");
            }
          });
        }).getOr(adt$2.none("default"));
      };
      const cata = (subject, onNone, onSuccess, onFailedUp, onFailedDown) => {
        return subject.fold(onNone, onSuccess, onFailedUp, onFailedDown);
      };
      const BeforeAfter = {
        ...adt$2,
        verify,
        cata
      };
      const inParent = (parent2, children2, element, index) => ({
        parent: parent2,
        children: children2,
        element,
        index
      });
      const indexInParent = (element) => parent(element).bind((parent2) => {
        const children2 = children$2(parent2);
        return indexOf(children2, element).map((index) => inParent(parent2, children2, element, index));
      });
      const indexOf = (elements, element) => findIndex(elements, curry(eq$1, element));
      const isBr = isTag("br");
      const gatherer = (cand, gather, isRoot) => {
        return gather(cand, isRoot).bind((target) => {
          return isText(target) && get$6(target).trim().length === 0 ? gatherer(target, gather, isRoot) : Optional.some(target);
        });
      };
      const handleBr = (isRoot, element, direction) => {
        return direction.traverse(element).orThunk(() => {
          return gatherer(element, direction.gather, isRoot);
        }).map(direction.relative);
      };
      const findBr = (element, offset) => {
        return child$2(element, offset).filter(isBr).orThunk(() => {
          return child$2(element, offset - 1).filter(isBr);
        });
      };
      const handleParent = (isRoot, element, offset, direction) => {
        return findBr(element, offset).bind((br) => {
          return direction.traverse(br).fold(() => {
            return gatherer(br, direction.gather, isRoot).map(direction.relative);
          }, (adjacent) => {
            return indexInParent(adjacent).map((info) => {
              return Situ.on(info.parent, info.index);
            });
          });
        });
      };
      const tryBr = (isRoot, element, offset, direction) => {
        const target = isBr(element) ? handleBr(isRoot, element, direction) : handleParent(isRoot, element, offset, direction);
        return target.map((tgt) => {
          return {
            start: tgt,
            finish: tgt
          };
        });
      };
      const process = (analysis) => {
        return BeforeAfter.cata(analysis, (_message) => {
          return Optional.none();
        }, () => {
          return Optional.none();
        }, (cell2) => {
          return Optional.some(point(cell2, 0));
        }, (cell2) => {
          return Optional.some(point(cell2, getEnd(cell2)));
        });
      };
      const moveDown = (caret, amount) => {
        return {
          left: caret.left,
          top: caret.top + amount,
          right: caret.right,
          bottom: caret.bottom + amount
        };
      };
      const moveUp = (caret, amount) => {
        return {
          left: caret.left,
          top: caret.top - amount,
          right: caret.right,
          bottom: caret.bottom - amount
        };
      };
      const translate = (caret, xDelta, yDelta) => {
        return {
          left: caret.left + xDelta,
          top: caret.top + yDelta,
          right: caret.right + xDelta,
          bottom: caret.bottom + yDelta
        };
      };
      const getTop = (caret) => {
        return caret.top;
      };
      const getBottom = (caret) => {
        return caret.bottom;
      };
      const getPartialBox = (bridge, element, offset) => {
        if (offset >= 0 && offset < getEnd(element)) {
          return bridge.getRangedRect(element, offset, element, offset + 1);
        } else if (offset > 0) {
          return bridge.getRangedRect(element, offset - 1, element, offset);
        }
        return Optional.none();
      };
      const toCaret = (rect) => ({
        left: rect.left,
        top: rect.top,
        right: rect.right,
        bottom: rect.bottom
      });
      const getElemBox = (bridge, element) => {
        return Optional.some(bridge.getRect(element));
      };
      const getBoxAt = (bridge, element, offset) => {
        if (isElement(element)) {
          return getElemBox(bridge, element).map(toCaret);
        } else if (isText(element)) {
          return getPartialBox(bridge, element, offset).map(toCaret);
        } else {
          return Optional.none();
        }
      };
      const getEntireBox = (bridge, element) => {
        if (isElement(element)) {
          return getElemBox(bridge, element).map(toCaret);
        } else if (isText(element)) {
          return bridge.getRangedRect(element, 0, element, getEnd(element)).map(toCaret);
        } else {
          return Optional.none();
        }
      };
      const JUMP_SIZE = 5;
      const NUM_RETRIES = 100;
      const adt$1 = Adt.generate([
        { none: [] },
        { retry: ["caret"] }
      ]);
      const isOutside = (caret, box) => {
        return caret.left < box.left || Math.abs(box.right - caret.left) < 1 || caret.left > box.right;
      };
      const inOutsideBlock = (bridge, element, caret) => {
        return closest$2(element, isBlock).fold(never, (cell2) => {
          return getEntireBox(bridge, cell2).exists((box) => {
            return isOutside(caret, box);
          });
        });
      };
      const adjustDown = (bridge, element, guessBox, original, caret) => {
        const lowerCaret = moveDown(caret, JUMP_SIZE);
        if (Math.abs(guessBox.bottom - original.bottom) < 1) {
          return adt$1.retry(lowerCaret);
        } else if (guessBox.top > caret.bottom) {
          return adt$1.retry(lowerCaret);
        } else if (guessBox.top === caret.bottom) {
          return adt$1.retry(moveDown(caret, 1));
        } else {
          return inOutsideBlock(bridge, element, caret) ? adt$1.retry(translate(lowerCaret, JUMP_SIZE, 0)) : adt$1.none();
        }
      };
      const adjustUp = (bridge, element, guessBox, original, caret) => {
        const higherCaret = moveUp(caret, JUMP_SIZE);
        if (Math.abs(guessBox.top - original.top) < 1) {
          return adt$1.retry(higherCaret);
        } else if (guessBox.bottom < caret.top) {
          return adt$1.retry(higherCaret);
        } else if (guessBox.bottom === caret.top) {
          return adt$1.retry(moveUp(caret, 1));
        } else {
          return inOutsideBlock(bridge, element, caret) ? adt$1.retry(translate(higherCaret, JUMP_SIZE, 0)) : adt$1.none();
        }
      };
      const upMovement = {
        point: getTop,
        adjuster: adjustUp,
        move: moveUp,
        gather: before
      };
      const downMovement = {
        point: getBottom,
        adjuster: adjustDown,
        move: moveDown,
        gather: after$1
      };
      const isAtTable = (bridge, x, y) => {
        return bridge.elementFromPoint(x, y).filter((elm) => {
          return name(elm) === "table";
        }).isSome();
      };
      const adjustForTable = (bridge, movement, original, caret, numRetries) => {
        return adjustTil(bridge, movement, original, movement.move(caret, JUMP_SIZE), numRetries);
      };
      const adjustTil = (bridge, movement, original, caret, numRetries) => {
        if (numRetries === 0) {
          return Optional.some(caret);
        }
        if (isAtTable(bridge, caret.left, movement.point(caret))) {
          return adjustForTable(bridge, movement, original, caret, numRetries - 1);
        }
        return bridge.situsFromPoint(caret.left, movement.point(caret)).bind((guess) => {
          return guess.start.fold(Optional.none, (element) => {
            return getEntireBox(bridge, element).bind((guessBox) => {
              return movement.adjuster(bridge, element, guessBox, original, caret).fold(Optional.none, (newCaret) => {
                return adjustTil(bridge, movement, original, newCaret, numRetries - 1);
              });
            }).orThunk(() => {
              return Optional.some(caret);
            });
          }, Optional.none);
        });
      };
      const checkScroll = (movement, adjusted, bridge) => {
        if (movement.point(adjusted) > bridge.getInnerHeight()) {
          return Optional.some(movement.point(adjusted) - bridge.getInnerHeight());
        } else if (movement.point(adjusted) < 0) {
          return Optional.some(-movement.point(adjusted));
        } else {
          return Optional.none();
        }
      };
      const retry = (movement, bridge, caret) => {
        const moved = movement.move(caret, JUMP_SIZE);
        const adjusted = adjustTil(bridge, movement, caret, moved, NUM_RETRIES).getOr(moved);
        return checkScroll(movement, adjusted, bridge).fold(() => {
          return bridge.situsFromPoint(adjusted.left, movement.point(adjusted));
        }, (delta) => {
          bridge.scrollBy(0, delta);
          return bridge.situsFromPoint(adjusted.left, movement.point(adjusted) - delta);
        });
      };
      const Retries = {
        tryUp: curry(retry, upMovement),
        tryDown: curry(retry, downMovement),
        getJumpSize: constant(JUMP_SIZE)
      };
      const MAX_RETRIES = 20;
      const findSpot = (bridge, isRoot, direction) => {
        return bridge.getSelection().bind((sel) => {
          return tryBr(isRoot, sel.finish, sel.foffset, direction).fold(() => {
            return Optional.some(point(sel.finish, sel.foffset));
          }, (brNeighbour) => {
            const range2 = bridge.fromSitus(brNeighbour);
            const analysis = BeforeAfter.verify(bridge, sel.finish, sel.foffset, range2.finish, range2.foffset, direction.failure, isRoot);
            return process(analysis);
          });
        });
      };
      const scan = (bridge, isRoot, element, offset, direction, numRetries) => {
        if (numRetries === 0) {
          return Optional.none();
        }
        return tryCursor(bridge, isRoot, element, offset, direction).bind((situs) => {
          const range2 = bridge.fromSitus(situs);
          const analysis = BeforeAfter.verify(bridge, element, offset, range2.finish, range2.foffset, direction.failure, isRoot);
          return BeforeAfter.cata(analysis, () => {
            return Optional.none();
          }, () => {
            return Optional.some(situs);
          }, (cell2) => {
            if (eq$1(element, cell2) && offset === 0) {
              return tryAgain(bridge, element, offset, moveUp, direction);
            } else {
              return scan(bridge, isRoot, cell2, 0, direction, numRetries - 1);
            }
          }, (cell2) => {
            if (eq$1(element, cell2) && offset === getEnd(cell2)) {
              return tryAgain(bridge, element, offset, moveDown, direction);
            } else {
              return scan(bridge, isRoot, cell2, getEnd(cell2), direction, numRetries - 1);
            }
          });
        });
      };
      const tryAgain = (bridge, element, offset, move, direction) => {
        return getBoxAt(bridge, element, offset).bind((box) => {
          return tryAt(bridge, direction, move(box, Retries.getJumpSize()));
        });
      };
      const tryAt = (bridge, direction, box) => {
        const browser = detect$2().browser;
        if (browser.isChromium() || browser.isSafari() || browser.isFirefox()) {
          return direction.retry(bridge, box);
        } else {
          return Optional.none();
        }
      };
      const tryCursor = (bridge, isRoot, element, offset, direction) => {
        return getBoxAt(bridge, element, offset).bind((box) => {
          return tryAt(bridge, direction, box);
        });
      };
      const handle$1 = (bridge, isRoot, direction) => {
        return findSpot(bridge, isRoot, direction).bind((spot) => {
          return scan(bridge, isRoot, spot.element, spot.offset, direction, MAX_RETRIES).map(bridge.fromSitus);
        });
      };
      const inSameTable = (elem, table2) => {
        return ancestor(elem, (e) => {
          return parent(e).exists((p) => {
            return eq$1(p, table2);
          });
        });
      };
      const simulate = (bridge, isRoot, direction, initial, anchor) => {
        return closest$1(initial, "td,th", isRoot).bind((start) => {
          return closest$1(start, "table", isRoot).bind((table2) => {
            if (!inSameTable(anchor, table2)) {
              return Optional.none();
            }
            return handle$1(bridge, isRoot, direction).bind((range2) => {
              return closest$1(range2.finish, "td,th", isRoot).map((finish) => {
                return {
                  start,
                  finish,
                  range: range2
                };
              });
            });
          });
        });
      };
      const navigate = (bridge, isRoot, direction, initial, anchor, precheck) => {
        return precheck(initial, isRoot).orThunk(() => {
          return simulate(bridge, isRoot, direction, initial, anchor).map((info) => {
            const range2 = info.range;
            return Response.create(Optional.some(makeSitus(range2.start, range2.soffset, range2.finish, range2.foffset)), true);
          });
        });
      };
      const firstUpCheck = (initial, isRoot) => {
        return closest$1(initial, "tr", isRoot).bind((startRow) => {
          return closest$1(startRow, "table", isRoot).bind((table2) => {
            const rows2 = descendants(table2, "tr");
            if (eq$1(startRow, rows2[0])) {
              return seekLeft(table2, (element) => {
                return last$1(element).isSome();
              }, isRoot).map((last2) => {
                const lastOffset = getEnd(last2);
                return Response.create(Optional.some(makeSitus(last2, lastOffset, last2, lastOffset)), true);
              });
            } else {
              return Optional.none();
            }
          });
        });
      };
      const lastDownCheck = (initial, isRoot) => {
        return closest$1(initial, "tr", isRoot).bind((startRow) => {
          return closest$1(startRow, "table", isRoot).bind((table2) => {
            const rows2 = descendants(table2, "tr");
            if (eq$1(startRow, rows2[rows2.length - 1])) {
              return seekRight(table2, (element) => {
                return first(element).isSome();
              }, isRoot).map((first2) => {
                return Response.create(Optional.some(makeSitus(first2, 0, first2, 0)), true);
              });
            } else {
              return Optional.none();
            }
          });
        });
      };
      const select = (bridge, container, isRoot, direction, initial, anchor, selectRange) => {
        return simulate(bridge, isRoot, direction, initial, anchor).bind((info) => {
          return detect(container, isRoot, info.start, info.finish, selectRange);
        });
      };
      const Cell = (initial) => {
        let value2 = initial;
        const get2 = () => {
          return value2;
        };
        const set2 = (v) => {
          value2 = v;
        };
        return {
          get: get2,
          set: set2
        };
      };
      const singleton = (doRevoke) => {
        const subject = Cell(Optional.none());
        const revoke = () => subject.get().each(doRevoke);
        const clear2 = () => {
          revoke();
          subject.set(Optional.none());
        };
        const isSet = () => subject.get().isSome();
        const get2 = () => subject.get();
        const set2 = (s) => {
          revoke();
          subject.set(Optional.some(s));
        };
        return {
          clear: clear2,
          isSet,
          get: get2,
          set: set2
        };
      };
      const value = () => {
        const subject = singleton(noop);
        const on2 = (f) => subject.get().each(f);
        return {
          ...subject,
          on: on2
        };
      };
      const findCell = (target, isRoot) => closest$1(target, "td,th", isRoot);
      const isInEditableContext = (cell2) => parentElement(cell2).exists(isEditable$1);
      const MouseSelection = (bridge, container, isRoot, annotations) => {
        const cursor = value();
        const clearstate = cursor.clear;
        const applySelection = (event) => {
          cursor.on((start) => {
            annotations.clearBeforeUpdate(container);
            findCell(event.target, isRoot).each((finish) => {
              identify(start, finish, isRoot).each((cellSel) => {
                const boxes = cellSel.boxes.getOr([]);
                if (boxes.length === 1) {
                  const singleCell = boxes[0];
                  const isNonEditableCell = getRaw(singleCell) === "false";
                  const isCellClosestContentEditable = is(closest(event.target), singleCell, eq$1);
                  if (isNonEditableCell && isCellClosestContentEditable) {
                    annotations.selectRange(container, boxes, singleCell, singleCell);
                    bridge.selectContents(singleCell);
                  }
                } else if (boxes.length > 1) {
                  annotations.selectRange(container, boxes, cellSel.start, cellSel.finish);
                  bridge.selectContents(finish);
                }
              });
            });
          });
        };
        const mousedown = (event) => {
          annotations.clear(container);
          findCell(event.target, isRoot).filter(isInEditableContext).each(cursor.set);
        };
        const mouseover = (event) => {
          applySelection(event);
        };
        const mouseup = (event) => {
          applySelection(event);
          clearstate();
        };
        return {
          clearstate,
          mousedown,
          mouseover,
          mouseup
        };
      };
      const down = {
        traverse: nextSibling,
        gather: after$1,
        relative: Situ.before,
        retry: Retries.tryDown,
        failure: BeforeAfter.failedDown
      };
      const up = {
        traverse: prevSibling,
        gather: before,
        relative: Situ.before,
        retry: Retries.tryUp,
        failure: BeforeAfter.failedUp
      };
      const isKey = (key2) => {
        return (keycode) => {
          return keycode === key2;
        };
      };
      const isUp = isKey(38);
      const isDown = isKey(40);
      const isNavigation = (keycode) => {
        return keycode >= 37 && keycode <= 40;
      };
      const ltr = {
        isBackward: isKey(37),
        isForward: isKey(39)
      };
      const rtl = {
        isBackward: isKey(39),
        isForward: isKey(37)
      };
      const get$3 = (_DOC) => {
        const doc = _DOC !== void 0 ? _DOC.dom : document;
        const x = doc.body.scrollLeft || doc.documentElement.scrollLeft;
        const y = doc.body.scrollTop || doc.documentElement.scrollTop;
        return SugarPosition(x, y);
      };
      const by = (x, y, _DOC) => {
        const doc = _DOC !== void 0 ? _DOC.dom : document;
        const win = doc.defaultView;
        if (win) {
          win.scrollBy(x, y);
        }
      };
      const adt = Adt.generate([
        { domRange: ["rng"] },
        {
          relative: [
            "startSitu",
            "finishSitu"
          ]
        },
        {
          exact: [
            "start",
            "soffset",
            "finish",
            "foffset"
          ]
        }
      ]);
      const exactFromRange = (simRange) => adt.exact(simRange.start, simRange.soffset, simRange.finish, simRange.foffset);
      const getStart = (selection2) => selection2.match({
        domRange: (rng) => SugarElement.fromDom(rng.startContainer),
        relative: (startSitu, _finishSitu) => Situ.getStart(startSitu),
        exact: (start, _soffset, _finish, _foffset) => start
      });
      const domRange = adt.domRange;
      const relative = adt.relative;
      const exact = adt.exact;
      const getWin = (selection2) => {
        const start = getStart(selection2);
        return defaultView(start);
      };
      const range = SimRange.create;
      const SimSelection = {
        domRange,
        relative,
        exact,
        exactFromRange,
        getWin,
        range
      };
      const caretPositionFromPoint = (doc, x, y) => {
        var _a, _b;
        return Optional.from((_b = (_a = doc.dom).caretPositionFromPoint) === null || _b === void 0 ? void 0 : _b.call(_a, x, y)).bind((pos) => {
          if (pos.offsetNode === null) {
            return Optional.none();
          }
          const r2 = doc.dom.createRange();
          r2.setStart(pos.offsetNode, pos.offset);
          r2.collapse();
          return Optional.some(r2);
        });
      };
      const caretRangeFromPoint = (doc, x, y) => {
        var _a, _b;
        return Optional.from((_b = (_a = doc.dom).caretRangeFromPoint) === null || _b === void 0 ? void 0 : _b.call(_a, x, y));
      };
      const availableSearch = (() => {
        if (document.caretPositionFromPoint) {
          return caretPositionFromPoint;
        } else if (document.caretRangeFromPoint) {
          return caretRangeFromPoint;
        } else {
          return Optional.none;
        }
      })();
      const fromPoint = (win, x, y) => {
        const doc = SugarElement.fromDom(win.document);
        return availableSearch(doc, x, y).map((rng) => SimRange.create(SugarElement.fromDom(rng.startContainer), rng.startOffset, SugarElement.fromDom(rng.endContainer), rng.endOffset));
      };
      const beforeSpecial = (element, offset) => {
        const name$1 = name(element);
        if ("input" === name$1) {
          return Situ.after(element);
        } else if (!contains$2([
          "br",
          "img"
        ], name$1)) {
          return Situ.on(element, offset);
        } else {
          return offset === 0 ? Situ.before(element) : Situ.after(element);
        }
      };
      const preprocessRelative = (startSitu, finishSitu) => {
        const start = startSitu.fold(Situ.before, beforeSpecial, Situ.after);
        const finish = finishSitu.fold(Situ.before, beforeSpecial, Situ.after);
        return SimSelection.relative(start, finish);
      };
      const preprocessExact = (start, soffset, finish, foffset) => {
        const startSitu = beforeSpecial(start, soffset);
        const finishSitu = beforeSpecial(finish, foffset);
        return SimSelection.relative(startSitu, finishSitu);
      };
      const makeRange = (start, soffset, finish, foffset) => {
        const doc = owner(start);
        const rng = doc.dom.createRange();
        rng.setStart(start.dom, soffset);
        rng.setEnd(finish.dom, foffset);
        return rng;
      };
      const after = (start, soffset, finish, foffset) => {
        const r2 = makeRange(start, soffset, finish, foffset);
        const same = eq$1(start, finish) && soffset === foffset;
        return r2.collapsed && !same;
      };
      const getNativeSelection = (win) => Optional.from(win.getSelection());
      const doSetNativeRange = (win, rng) => {
        getNativeSelection(win).each((selection2) => {
          selection2.removeAllRanges();
          selection2.addRange(rng);
        });
      };
      const doSetRange = (win, start, soffset, finish, foffset) => {
        const rng = exactToNative(win, start, soffset, finish, foffset);
        doSetNativeRange(win, rng);
      };
      const setLegacyRtlRange = (win, selection2, start, soffset, finish, foffset) => {
        selection2.collapse(start.dom, soffset);
        selection2.extend(finish.dom, foffset);
      };
      const setRangeFromRelative = (win, relative2) => diagnose(win, relative2).match({
        ltr: (start, soffset, finish, foffset) => {
          doSetRange(win, start, soffset, finish, foffset);
        },
        rtl: (start, soffset, finish, foffset) => {
          getNativeSelection(win).each((selection2) => {
            if (selection2.setBaseAndExtent) {
              selection2.setBaseAndExtent(start.dom, soffset, finish.dom, foffset);
            } else if (selection2.extend) {
              try {
                setLegacyRtlRange(win, selection2, start, soffset, finish, foffset);
              } catch (e) {
                doSetRange(win, finish, foffset, start, soffset);
              }
            } else {
              doSetRange(win, finish, foffset, start, soffset);
            }
          });
        }
      });
      const setExact = (win, start, soffset, finish, foffset) => {
        const relative2 = preprocessExact(start, soffset, finish, foffset);
        setRangeFromRelative(win, relative2);
      };
      const setRelative = (win, startSitu, finishSitu) => {
        const relative2 = preprocessRelative(startSitu, finishSitu);
        setRangeFromRelative(win, relative2);
      };
      const readRange = (selection2) => {
        if (selection2.rangeCount > 0) {
          const firstRng = selection2.getRangeAt(0);
          const lastRng = selection2.getRangeAt(selection2.rangeCount - 1);
          return Optional.some(SimRange.create(SugarElement.fromDom(firstRng.startContainer), firstRng.startOffset, SugarElement.fromDom(lastRng.endContainer), lastRng.endOffset));
        } else {
          return Optional.none();
        }
      };
      const doGetExact = (selection2) => {
        if (selection2.anchorNode === null || selection2.focusNode === null) {
          return readRange(selection2);
        } else {
          const anchor = SugarElement.fromDom(selection2.anchorNode);
          const focus = SugarElement.fromDom(selection2.focusNode);
          return after(anchor, selection2.anchorOffset, focus, selection2.focusOffset) ? Optional.some(SimRange.create(anchor, selection2.anchorOffset, focus, selection2.focusOffset)) : readRange(selection2);
        }
      };
      const setToElement = (win, element, selectNodeContents$1 = true) => {
        const rngGetter = selectNodeContents$1 ? selectNodeContents : selectNode;
        const rng = rngGetter(win, element);
        doSetNativeRange(win, rng);
      };
      const getExact = (win) => getNativeSelection(win).filter((sel) => sel.rangeCount > 0).bind(doGetExact);
      const get$2 = (win) => getExact(win).map((range2) => SimSelection.exact(range2.start, range2.soffset, range2.finish, range2.foffset));
      const getFirstRect = (win, selection2) => {
        const rng = asLtrRange(win, selection2);
        return getFirstRect$1(rng);
      };
      const getAtPoint = (win, x, y) => fromPoint(win, x, y);
      const clear = (win) => {
        getNativeSelection(win).each((selection2) => selection2.removeAllRanges());
      };
      const WindowBridge = (win) => {
        const elementFromPoint = (x, y) => {
          return SugarElement.fromPoint(SugarElement.fromDom(win.document), x, y);
        };
        const getRect = (element) => {
          return element.dom.getBoundingClientRect();
        };
        const getRangedRect = (start, soffset, finish, foffset) => {
          const sel = SimSelection.exact(start, soffset, finish, foffset);
          return getFirstRect(win, sel);
        };
        const getSelection = () => {
          return get$2(win).map((exactAdt) => {
            return convertToRange(win, exactAdt);
          });
        };
        const fromSitus = (situs) => {
          const relative2 = SimSelection.relative(situs.start, situs.finish);
          return convertToRange(win, relative2);
        };
        const situsFromPoint = (x, y) => {
          return getAtPoint(win, x, y).map((exact2) => {
            return Situs.create(exact2.start, exact2.soffset, exact2.finish, exact2.foffset);
          });
        };
        const clearSelection = () => {
          clear(win);
        };
        const collapseSelection = (toStart = false) => {
          get$2(win).each((sel) => sel.fold((rng) => rng.collapse(toStart), (startSitu, finishSitu) => {
            const situ = toStart ? startSitu : finishSitu;
            setRelative(win, situ, situ);
          }, (start, soffset, finish, foffset) => {
            const node = toStart ? start : finish;
            const offset = toStart ? soffset : foffset;
            setExact(win, node, offset, node, offset);
          }));
        };
        const selectNode2 = (element) => {
          setToElement(win, element, false);
        };
        const selectContents = (element) => {
          setToElement(win, element);
        };
        const setSelection = (sel) => {
          setExact(win, sel.start, sel.soffset, sel.finish, sel.foffset);
        };
        const setRelativeSelection = (start, finish) => {
          setRelative(win, start, finish);
        };
        const getInnerHeight = () => {
          return win.innerHeight;
        };
        const getScrollY = () => {
          const pos = get$3(SugarElement.fromDom(win.document));
          return pos.top;
        };
        const scrollBy = (x, y) => {
          by(x, y, SugarElement.fromDom(win.document));
        };
        return {
          elementFromPoint,
          getRect,
          getRangedRect,
          getSelection,
          fromSitus,
          situsFromPoint,
          clearSelection,
          collapseSelection,
          setSelection,
          setRelativeSelection,
          selectNode: selectNode2,
          selectContents,
          getInnerHeight,
          getScrollY,
          scrollBy
        };
      };
      const rc = (rows2, cols) => ({
        rows: rows2,
        cols
      });
      const mouse = (win, container, isRoot, annotations) => {
        const bridge = WindowBridge(win);
        const handlers = MouseSelection(bridge, container, isRoot, annotations);
        return {
          clearstate: handlers.clearstate,
          mousedown: handlers.mousedown,
          mouseover: handlers.mouseover,
          mouseup: handlers.mouseup
        };
      };
      const isEditableNode = (node) => closest$2(node, isHTMLElement).exists(isEditable$1);
      const isEditableSelection = (start, finish) => isEditableNode(start) || isEditableNode(finish);
      const keyboard = (win, container, isRoot, annotations) => {
        const bridge = WindowBridge(win);
        const clearToNavigate = () => {
          annotations.clear(container);
          return Optional.none();
        };
        const keydown = (event, start, soffset, finish, foffset, direction) => {
          const realEvent = event.raw;
          const keycode = realEvent.which;
          const shiftKey = realEvent.shiftKey === true;
          const handler = retrieve$1(container, annotations.selectedSelector).fold(() => {
            if (isNavigation(keycode) && !shiftKey) {
              annotations.clearBeforeUpdate(container);
            }
            if (isNavigation(keycode) && shiftKey && !isEditableSelection(start, finish)) {
              return Optional.none;
            } else if (isDown(keycode) && shiftKey) {
              return curry(select, bridge, container, isRoot, down, finish, start, annotations.selectRange);
            } else if (isUp(keycode) && shiftKey) {
              return curry(select, bridge, container, isRoot, up, finish, start, annotations.selectRange);
            } else if (isDown(keycode)) {
              return curry(navigate, bridge, isRoot, down, finish, start, lastDownCheck);
            } else if (isUp(keycode)) {
              return curry(navigate, bridge, isRoot, up, finish, start, firstUpCheck);
            } else {
              return Optional.none;
            }
          }, (selected) => {
            const update$1 = (attempts) => {
              return () => {
                const navigation = findMap(attempts, (delta) => {
                  return update(delta.rows, delta.cols, container, selected, annotations);
                });
                return navigation.fold(() => {
                  return getEdges(container, annotations.firstSelectedSelector, annotations.lastSelectedSelector).map((edges) => {
                    const relative2 = isDown(keycode) || direction.isForward(keycode) ? Situ.after : Situ.before;
                    bridge.setRelativeSelection(Situ.on(edges.first, 0), relative2(edges.table));
                    annotations.clear(container);
                    return Response.create(Optional.none(), true);
                  });
                }, (_) => {
                  return Optional.some(Response.create(Optional.none(), true));
                });
              };
            };
            if (isNavigation(keycode) && shiftKey && !isEditableSelection(start, finish)) {
              return Optional.none;
            } else if (isDown(keycode) && shiftKey) {
              return update$1([rc(1, 0)]);
            } else if (isUp(keycode) && shiftKey) {
              return update$1([rc(-1, 0)]);
            } else if (direction.isBackward(keycode) && shiftKey) {
              return update$1([
                rc(0, -1),
                rc(-1, 0)
              ]);
            } else if (direction.isForward(keycode) && shiftKey) {
              return update$1([
                rc(0, 1),
                rc(1, 0)
              ]);
            } else if (isNavigation(keycode) && !shiftKey) {
              return clearToNavigate;
            } else {
              return Optional.none;
            }
          });
          return handler();
        };
        const keyup = (event, start, soffset, finish, foffset) => {
          return retrieve$1(container, annotations.selectedSelector).fold(() => {
            const realEvent = event.raw;
            const keycode = realEvent.which;
            const shiftKey = realEvent.shiftKey === true;
            if (!shiftKey) {
              return Optional.none();
            }
            if (isNavigation(keycode) && isEditableSelection(start, finish)) {
              return sync(container, isRoot, start, soffset, finish, foffset, annotations.selectRange);
            } else {
              return Optional.none();
            }
          }, Optional.none);
        };
        return {
          keydown,
          keyup
        };
      };
      const external = (win, container, isRoot, annotations) => {
        const bridge = WindowBridge(win);
        return (start, finish) => {
          annotations.clearBeforeUpdate(container);
          identify(start, finish, isRoot).each((cellSel) => {
            const boxes = cellSel.boxes.getOr([]);
            annotations.selectRange(container, boxes, cellSel.start, cellSel.finish);
            bridge.selectContents(finish);
            bridge.collapseSelection();
          });
        };
      };
      const read = (element, attr) => {
        const value2 = get$b(element, attr);
        return value2 === void 0 || value2 === "" ? [] : value2.split(" ");
      };
      const add$2 = (element, attr, id) => {
        const old = read(element, attr);
        const nu2 = old.concat([id]);
        set$2(element, attr, nu2.join(" "));
        return true;
      };
      const remove$4 = (element, attr, id) => {
        const nu2 = filter$2(read(element, attr), (v) => v !== id);
        if (nu2.length > 0) {
          set$2(element, attr, nu2.join(" "));
        } else {
          remove$7(element, attr);
        }
        return false;
      };
      const supports = (element) => element.dom.classList !== void 0;
      const get$1 = (element) => read(element, "class");
      const add$1 = (element, clazz) => add$2(element, "class", clazz);
      const remove$3 = (element, clazz) => remove$4(element, "class", clazz);
      const add = (element, clazz) => {
        if (supports(element)) {
          element.dom.classList.add(clazz);
        } else {
          add$1(element, clazz);
        }
      };
      const cleanClass = (element) => {
        const classList = supports(element) ? element.dom.classList : get$1(element);
        if (classList.length === 0) {
          remove$7(element, "class");
        }
      };
      const remove$2 = (element, clazz) => {
        if (supports(element)) {
          const classList = element.dom.classList;
          classList.remove(clazz);
        } else {
          remove$3(element, clazz);
        }
        cleanClass(element);
      };
      const has = (element, clazz) => supports(element) && element.dom.classList.contains(clazz);
      const remove$1 = (element, classes) => {
        each$2(classes, (x) => {
          remove$2(element, x);
        });
      };
      const addClass = (clazz) => (element) => {
        add(element, clazz);
      };
      const removeClasses = (classes) => (element) => {
        remove$1(element, classes);
      };
      const byClass = (ephemera2) => {
        const addSelectionClass = addClass(ephemera2.selected);
        const removeSelectionClasses = removeClasses([
          ephemera2.selected,
          ephemera2.lastSelected,
          ephemera2.firstSelected
        ]);
        const clear2 = (container) => {
          const sels = descendants(container, ephemera2.selectedSelector);
          each$2(sels, removeSelectionClasses);
        };
        const selectRange = (container, cells2, start, finish) => {
          clear2(container);
          each$2(cells2, addSelectionClass);
          add(start, ephemera2.firstSelected);
          add(finish, ephemera2.lastSelected);
        };
        return {
          clearBeforeUpdate: clear2,
          clear: clear2,
          selectRange,
          selectedSelector: ephemera2.selectedSelector,
          firstSelectedSelector: ephemera2.firstSelectedSelector,
          lastSelectedSelector: ephemera2.lastSelectedSelector
        };
      };
      const byAttr = (ephemera2, onSelection, onClear) => {
        const removeSelectionAttributes = (element) => {
          remove$7(element, ephemera2.selected);
          remove$7(element, ephemera2.firstSelected);
          remove$7(element, ephemera2.lastSelected);
        };
        const addSelectionAttribute = (element) => {
          set$2(element, ephemera2.selected, "1");
        };
        const clear2 = (container) => {
          clearBeforeUpdate(container);
          onClear();
        };
        const clearBeforeUpdate = (container) => {
          const sels = descendants(container, `${ephemera2.selectedSelector},${ephemera2.firstSelectedSelector},${ephemera2.lastSelectedSelector}`);
          each$2(sels, removeSelectionAttributes);
        };
        const selectRange = (container, cells2, start, finish) => {
          clear2(container);
          each$2(cells2, addSelectionAttribute);
          set$2(start, ephemera2.firstSelected, "1");
          set$2(finish, ephemera2.lastSelected, "1");
          onSelection(cells2, start, finish);
        };
        return {
          clearBeforeUpdate,
          clear: clear2,
          selectRange,
          selectedSelector: ephemera2.selectedSelector,
          firstSelectedSelector: ephemera2.firstSelectedSelector,
          lastSelectedSelector: ephemera2.lastSelectedSelector
        };
      };
      const SelectionAnnotation = {
        byClass,
        byAttr
      };
      const fold = (subject, onNone, onMultiple, onSingle) => {
        switch (subject.tag) {
          case "none":
            return onNone();
          case "single":
            return onSingle(subject.element);
          case "multiple":
            return onMultiple(subject.elements);
        }
      };
      const none = () => ({ tag: "none" });
      const multiple = (elements) => ({
        tag: "multiple",
        elements
      });
      const single = (element) => ({
        tag: "single",
        element
      });
      const Selections = (lazyRoot, getStart2, selectedSelector) => {
        const get2 = () => retrieve(lazyRoot(), selectedSelector).fold(() => getStart2().fold(none, single), multiple);
        return { get: get2 };
      };
      const getUpOrLeftCells = (grid2, selectedCells) => {
        const upGrid = grid2.slice(0, selectedCells[selectedCells.length - 1].row + 1);
        const upDetails = toDetailList(upGrid);
        return bind$2(upDetails, (detail2) => {
          const slicedCells = detail2.cells.slice(0, selectedCells[selectedCells.length - 1].column + 1);
          return map$1(slicedCells, (cell2) => cell2.element);
        });
      };
      const getDownOrRightCells = (grid2, selectedCells) => {
        const downGrid = grid2.slice(selectedCells[0].row + selectedCells[0].rowspan - 1, grid2.length);
        const downDetails = toDetailList(downGrid);
        return bind$2(downDetails, (detail2) => {
          const slicedCells = detail2.cells.slice(selectedCells[0].column + selectedCells[0].colspan - 1, detail2.cells.length);
          return map$1(slicedCells, (cell2) => cell2.element);
        });
      };
      const getOtherCells = (table2, target, generators) => {
        const warehouse = Warehouse.fromTable(table2);
        const details = onCells(warehouse, target);
        return details.map((selectedCells) => {
          const grid2 = toGrid(warehouse, generators, false);
          const { rows: rows2 } = extractGridDetails(grid2);
          const upOrLeftCells = getUpOrLeftCells(rows2, selectedCells);
          const downOrRightCells = getDownOrRightCells(rows2, selectedCells);
          return {
            upOrLeftCells,
            downOrRightCells
          };
        });
      };
      const mkEvent = (target, x, y, stop, prevent, kill, raw) => ({
        target,
        x,
        y,
        stop,
        prevent,
        kill,
        raw
      });
      const fromRawEvent$1 = (rawEvent) => {
        const target = SugarElement.fromDom(getOriginalEventTarget(rawEvent).getOr(rawEvent.target));
        const stop = () => rawEvent.stopPropagation();
        const prevent = () => rawEvent.preventDefault();
        const kill = compose(prevent, stop);
        return mkEvent(target, rawEvent.clientX, rawEvent.clientY, stop, prevent, kill, rawEvent);
      };
      const handle = (filter2, handler) => (rawEvent) => {
        if (filter2(rawEvent)) {
          handler(fromRawEvent$1(rawEvent));
        }
      };
      const binder = (element, event, filter2, handler, useCapture) => {
        const wrapped = handle(filter2, handler);
        element.dom.addEventListener(event, wrapped, useCapture);
        return { unbind: curry(unbind, element, event, wrapped, useCapture) };
      };
      const bind$1 = (element, event, filter2, handler) => binder(element, event, filter2, handler, false);
      const unbind = (element, event, handler, useCapture) => {
        element.dom.removeEventListener(event, handler, useCapture);
      };
      const filter = always;
      const bind = (element, event, handler) => bind$1(element, event, filter, handler);
      const fromRawEvent = fromRawEvent$1;
      const hasInternalTarget = (e) => !has(SugarElement.fromDom(e.target), "ephox-snooker-resizer-bar");
      const TableCellSelectionHandler = (editor, resizeHandler) => {
        const cellSelection = Selections(() => SugarElement.fromDom(editor.getBody()), () => getSelectionCell(getSelectionStart(editor), getIsRoot(editor)), ephemera.selectedSelector);
        const onSelection = (cells2, start, finish) => {
          const tableOpt = table(start);
          tableOpt.each((table2) => {
            const cloneFormats2 = getTableCloneElements(editor);
            const generators = cellOperations(noop, SugarElement.fromDom(editor.getDoc()), cloneFormats2);
            const selectedCells = getCellsFromSelection(editor);
            const otherCells = getOtherCells(table2, { selection: selectedCells }, generators);
            fireTableSelectionChange(editor, cells2, start, finish, otherCells);
          });
        };
        const onClear = () => fireTableSelectionClear(editor);
        const annotations = SelectionAnnotation.byAttr(ephemera, onSelection, onClear);
        editor.on("init", (_e) => {
          const win = editor.getWin();
          const body2 = getBody(editor);
          const isRoot = getIsRoot(editor);
          const syncSelection = () => {
            const sel = editor.selection;
            const start = SugarElement.fromDom(sel.getStart());
            const end = SugarElement.fromDom(sel.getEnd());
            const shared = sharedOne(table, [
              start,
              end
            ]);
            shared.fold(() => annotations.clear(body2), noop);
          };
          const mouseHandlers = mouse(win, body2, isRoot, annotations);
          const keyHandlers = keyboard(win, body2, isRoot, annotations);
          const external$1 = external(win, body2, isRoot, annotations);
          const hasShiftKey = (event) => event.raw.shiftKey === true;
          editor.on("TableSelectorChange", (e) => external$1(e.start, e.finish));
          const handleResponse = (event, response) => {
            if (!hasShiftKey(event)) {
              return;
            }
            if (response.kill) {
              event.kill();
            }
            response.selection.each((ns) => {
              const relative2 = SimSelection.relative(ns.start, ns.finish);
              const rng = asLtrRange(win, relative2);
              editor.selection.setRng(rng);
            });
          };
          const keyup = (event) => {
            const wrappedEvent = fromRawEvent(event);
            if (wrappedEvent.raw.shiftKey && isNavigation(wrappedEvent.raw.which)) {
              const rng = editor.selection.getRng();
              const start = SugarElement.fromDom(rng.startContainer);
              const end = SugarElement.fromDom(rng.endContainer);
              keyHandlers.keyup(wrappedEvent, start, rng.startOffset, end, rng.endOffset).each((response) => {
                handleResponse(wrappedEvent, response);
              });
            }
          };
          const keydown = (event) => {
            const wrappedEvent = fromRawEvent(event);
            resizeHandler.hide();
            const rng = editor.selection.getRng();
            const start = SugarElement.fromDom(rng.startContainer);
            const end = SugarElement.fromDom(rng.endContainer);
            const direction = onDirection(ltr, rtl)(SugarElement.fromDom(editor.selection.getStart()));
            keyHandlers.keydown(wrappedEvent, start, rng.startOffset, end, rng.endOffset, direction).each((response) => {
              handleResponse(wrappedEvent, response);
            });
            resizeHandler.show();
          };
          const isLeftMouse = (raw) => raw.button === 0;
          const isLeftButtonPressed = (raw) => {
            if (raw.buttons === void 0) {
              return true;
            }
            return (raw.buttons & 1) !== 0;
          };
          const dragStart = (_e2) => {
            mouseHandlers.clearstate();
          };
          const mouseDown = (e) => {
            if (isLeftMouse(e) && hasInternalTarget(e)) {
              mouseHandlers.mousedown(fromRawEvent(e));
            }
          };
          const mouseOver = (e) => {
            if (isLeftButtonPressed(e) && hasInternalTarget(e)) {
              mouseHandlers.mouseover(fromRawEvent(e));
            }
          };
          const mouseUp = (e) => {
            if (isLeftMouse(e) && hasInternalTarget(e)) {
              mouseHandlers.mouseup(fromRawEvent(e));
            }
          };
          const getDoubleTap = () => {
            const lastTarget = Cell(SugarElement.fromDom(body2));
            const lastTimeStamp = Cell(0);
            const touchEnd = (t) => {
              const target = SugarElement.fromDom(t.target);
              if (isTag("td")(target) || isTag("th")(target)) {
                const lT = lastTarget.get();
                const lTS = lastTimeStamp.get();
                if (eq$1(lT, target) && t.timeStamp - lTS < 300) {
                  t.preventDefault();
                  external$1(target, target);
                }
              }
              lastTarget.set(target);
              lastTimeStamp.set(t.timeStamp);
            };
            return { touchEnd };
          };
          const doubleTap = getDoubleTap();
          editor.on("dragstart", dragStart);
          editor.on("mousedown", mouseDown);
          editor.on("mouseover", mouseOver);
          editor.on("mouseup", mouseUp);
          editor.on("touchend", doubleTap.touchEnd);
          editor.on("keyup", keyup);
          editor.on("keydown", keydown);
          editor.on("NodeChange", syncSelection);
        });
        editor.on("PreInit", () => {
          editor.serializer.addTempAttr(ephemera.firstSelected);
          editor.serializer.addTempAttr(ephemera.lastSelected);
        });
        const clearSelectedCells = (container) => annotations.clear(SugarElement.fromDom(container));
        const getSelectedCells = () => fold(cellSelection.get(), constant([]), (cells2) => {
          return map$1(cells2, (cell2) => cell2.dom);
        }, (cell2) => [cell2.dom]);
        return {
          getSelectedCells,
          clearSelectedCells
        };
      };
      const Event = (fields) => {
        let handlers = [];
        const bind2 = (handler) => {
          if (handler === void 0) {
            throw new Error("Event bind error: undefined handler");
          }
          handlers.push(handler);
        };
        const unbind2 = (handler) => {
          handlers = filter$2(handlers, (h) => {
            return h !== handler;
          });
        };
        const trigger = (...args) => {
          const event = {};
          each$2(fields, (name2, i) => {
            event[name2] = args[i];
          });
          each$2(handlers, (handler) => {
            handler(event);
          });
        };
        return {
          bind: bind2,
          unbind: unbind2,
          trigger
        };
      };
      const create$1 = (typeDefs) => {
        const registry = map(typeDefs, (event) => {
          return {
            bind: event.bind,
            unbind: event.unbind
          };
        });
        const trigger = map(typeDefs, (event) => {
          return event.trigger;
        });
        return {
          registry,
          trigger
        };
      };
      const last = (fn, rate) => {
        let timer = null;
        const cancel = () => {
          if (!isNull(timer)) {
            clearTimeout(timer);
            timer = null;
          }
        };
        const throttle = (...args) => {
          cancel();
          timer = setTimeout(() => {
            timer = null;
            fn.apply(null, args);
          }, rate);
        };
        return {
          cancel,
          throttle
        };
      };
      const sort = (arr) => {
        return arr.slice(0).sort();
      };
      const reqMessage = (required, keys2) => {
        throw new Error("All required keys (" + sort(required).join(", ") + ") were not specified. Specified keys were: " + sort(keys2).join(", ") + ".");
      };
      const unsuppMessage = (unsupported) => {
        throw new Error("Unsupported keys for object: " + sort(unsupported).join(", "));
      };
      const validateStrArr = (label, array) => {
        if (!isArray(array)) {
          throw new Error("The " + label + " fields must be an array. Was: " + array + ".");
        }
        each$2(array, (a) => {
          if (!isString(a)) {
            throw new Error("The value " + a + " in the " + label + " fields was not a string.");
          }
        });
      };
      const invalidTypeMessage = (incorrect, type2) => {
        throw new Error("All values need to be of type: " + type2 + ". Keys (" + sort(incorrect).join(", ") + ") were not.");
      };
      const checkDupes = (everything) => {
        const sorted = sort(everything);
        const dupe = find$1(sorted, (s, i) => {
          return i < sorted.length - 1 && s === sorted[i + 1];
        });
        dupe.each((d) => {
          throw new Error("The field: " + d + " occurs more than once in the combined fields: [" + sorted.join(", ") + "].");
        });
      };
      const base = (handleUnsupported, required) => {
        return baseWith(handleUnsupported, required, {
          validate: isFunction,
          label: "function"
        });
      };
      const baseWith = (handleUnsupported, required, pred) => {
        if (required.length === 0) {
          throw new Error("You must specify at least one required field.");
        }
        validateStrArr("required", required);
        checkDupes(required);
        return (obj) => {
          const keys$1 = keys(obj);
          const allReqd = forall(required, (req) => {
            return contains$2(keys$1, req);
          });
          if (!allReqd) {
            reqMessage(required, keys$1);
          }
          handleUnsupported(required, keys$1);
          const invalidKeys = filter$2(required, (key2) => {
            return !pred.validate(obj[key2], key2);
          });
          if (invalidKeys.length > 0) {
            invalidTypeMessage(invalidKeys, pred.label);
          }
          return obj;
        };
      };
      const handleExact = (required, keys2) => {
        const unsupported = filter$2(keys2, (key2) => {
          return !contains$2(required, key2);
        });
        if (unsupported.length > 0) {
          unsuppMessage(unsupported);
        }
      };
      const exactly = (required) => base(handleExact, required);
      const DragMode = exactly([
        "compare",
        "extract",
        "mutate",
        "sink"
      ]);
      const DragSink = exactly([
        "element",
        "start",
        "stop",
        "destroy"
      ]);
      const DragApi = exactly([
        "forceDrop",
        "drop",
        "move",
        "delayDrop"
      ]);
      const InDrag = () => {
        let previous = Optional.none();
        const reset = () => {
          previous = Optional.none();
        };
        const update2 = (mode, nu2) => {
          const result = previous.map((old) => {
            return mode.compare(old, nu2);
          });
          previous = Optional.some(nu2);
          return result;
        };
        const onEvent = (event, mode) => {
          const dataOption = mode.extract(event);
          dataOption.each((data) => {
            const offset = update2(mode, data);
            offset.each((d) => {
              events.trigger.move(d);
            });
          });
        };
        const events = create$1({ move: Event(["info"]) });
        return {
          onEvent,
          reset,
          events: events.registry
        };
      };
      const NoDrag = () => {
        const events = create$1({ move: Event(["info"]) });
        return {
          onEvent: noop,
          reset: noop,
          events: events.registry
        };
      };
      const Movement = () => {
        const noDragState = NoDrag();
        const inDragState = InDrag();
        let dragState = noDragState;
        const on2 = () => {
          dragState.reset();
          dragState = inDragState;
        };
        const off = () => {
          dragState.reset();
          dragState = noDragState;
        };
        const onEvent = (event, mode) => {
          dragState.onEvent(event, mode);
        };
        const isOn = () => {
          return dragState === inDragState;
        };
        return {
          on: on2,
          off,
          isOn,
          onEvent,
          events: inDragState.events
        };
      };
      const setup = (mutation, mode, settings) => {
        let active = false;
        const events = create$1({
          start: Event([]),
          stop: Event([])
        });
        const movement = Movement();
        const drop = () => {
          sink2.stop();
          if (movement.isOn()) {
            movement.off();
            events.trigger.stop();
          }
        };
        const throttledDrop = last(drop, 200);
        const go2 = (parent2) => {
          sink2.start(parent2);
          movement.on();
          events.trigger.start();
        };
        const mousemove = (event) => {
          throttledDrop.cancel();
          movement.onEvent(event, mode);
        };
        movement.events.move.bind((event) => {
          mode.mutate(mutation, event.info);
        });
        const on2 = () => {
          active = true;
        };
        const off = () => {
          active = false;
        };
        const isActive = () => active;
        const runIfActive = (f) => {
          return (...args) => {
            if (active) {
              f.apply(null, args);
            }
          };
        };
        const sink2 = mode.sink(DragApi({
          forceDrop: drop,
          drop: runIfActive(drop),
          move: runIfActive(mousemove),
          delayDrop: runIfActive(throttledDrop.throttle)
        }), settings);
        const destroy2 = () => {
          sink2.destroy();
        };
        return {
          element: sink2.element,
          go: go2,
          on: on2,
          off,
          isActive,
          destroy: destroy2,
          events: events.registry
        };
      };
      const css = (namespace) => {
        const dashNamespace = namespace.replace(/\./g, "-");
        const resolve2 = (str) => {
          return dashNamespace + "-" + str;
        };
        return { resolve: resolve2 };
      };
      const styles$1 = css("ephox-dragster");
      const resolve$1 = styles$1.resolve;
      const Blocker = (options) => {
        const settings = {
          layerClass: resolve$1("blocker"),
          ...options
        };
        const div = SugarElement.fromTag("div");
        set$2(div, "role", "presentation");
        setAll(div, {
          position: "fixed",
          left: "0px",
          top: "0px",
          width: "100%",
          height: "100%"
        });
        add(div, resolve$1("blocker"));
        add(div, settings.layerClass);
        const element = constant(div);
        const destroy2 = () => {
          remove$6(div);
        };
        return {
          element,
          destroy: destroy2
        };
      };
      const compare = (old, nu2) => {
        return SugarPosition(nu2.left - old.left, nu2.top - old.top);
      };
      const extract = (event) => {
        return Optional.some(SugarPosition(event.x, event.y));
      };
      const mutate = (mutation, info) => {
        mutation.mutate(info.left, info.top);
      };
      const sink = (dragApi, settings) => {
        const blocker = Blocker(settings);
        const mdown = bind(blocker.element(), "mousedown", dragApi.forceDrop);
        const mup = bind(blocker.element(), "mouseup", dragApi.drop);
        const mmove = bind(blocker.element(), "mousemove", dragApi.move);
        const mout = bind(blocker.element(), "mouseout", dragApi.delayDrop);
        const destroy2 = () => {
          blocker.destroy();
          mup.unbind();
          mmove.unbind();
          mout.unbind();
          mdown.unbind();
        };
        const start = (parent2) => {
          append$1(parent2, blocker.element());
        };
        const stop = () => {
          remove$6(blocker.element());
        };
        return DragSink({
          element: blocker.element,
          start,
          stop,
          destroy: destroy2
        });
      };
      var MouseDrag = DragMode({
        compare,
        extract,
        sink,
        mutate
      });
      const transform = (mutation, settings = {}) => {
        var _a;
        const mode = (_a = settings.mode) !== null && _a !== void 0 ? _a : MouseDrag;
        return setup(mutation, mode, settings);
      };
      const styles = css("ephox-snooker");
      const resolve = styles.resolve;
      const Mutation = () => {
        const events = create$1({
          drag: Event([
            "xDelta",
            "yDelta"
          ])
        });
        const mutate2 = (x, y) => {
          events.trigger.drag(x, y);
        };
        return {
          mutate: mutate2,
          events: events.registry
        };
      };
      const BarMutation = () => {
        const events = create$1({
          drag: Event([
            "xDelta",
            "yDelta",
            "target"
          ])
        });
        let target = Optional.none();
        const delegate = Mutation();
        delegate.events.drag.bind((event) => {
          target.each((t) => {
            events.trigger.drag(event.xDelta, event.yDelta, t);
          });
        });
        const assign = (t) => {
          target = Optional.some(t);
        };
        const get2 = () => {
          return target;
        };
        return {
          assign,
          get: get2,
          mutate: delegate.mutate,
          events: events.registry
        };
      };
      const col = (column, x, y, w, h) => {
        const bar = SugarElement.fromTag("div");
        setAll(bar, {
          position: "absolute",
          left: x - w / 2 + "px",
          top: y + "px",
          height: h + "px",
          width: w + "px"
        });
        setAll$1(bar, {
          "data-column": column,
          "role": "presentation"
        });
        return bar;
      };
      const row = (r2, x, y, w, h) => {
        const bar = SugarElement.fromTag("div");
        setAll(bar, {
          position: "absolute",
          left: x + "px",
          top: y - h / 2 + "px",
          height: h + "px",
          width: w + "px"
        });
        setAll$1(bar, {
          "data-row": r2,
          "role": "presentation"
        });
        return bar;
      };
      const resizeBar = resolve("resizer-bar");
      const resizeRowBar = resolve("resizer-rows");
      const resizeColBar = resolve("resizer-cols");
      const BAR_THICKNESS = 7;
      const resizableRows = (warehouse, isResizable2) => bind$2(warehouse.all, (row2, i) => isResizable2(row2.element) ? [i] : []);
      const resizableColumns = (warehouse, isResizable2) => {
        const resizableCols = [];
        range$1(warehouse.grid.columns, (index) => {
          const colElmOpt = Warehouse.getColumnAt(warehouse, index).map((col2) => col2.element);
          if (colElmOpt.forall(isResizable2)) {
            resizableCols.push(index);
          }
        });
        return filter$2(resizableCols, (colIndex) => {
          const columnCells = Warehouse.filterItems(warehouse, (cell2) => cell2.column === colIndex);
          return forall(columnCells, (cell2) => isResizable2(cell2.element));
        });
      };
      const destroy = (wire) => {
        const previous = descendants(wire.parent(), "." + resizeBar);
        each$2(previous, remove$6);
      };
      const drawBar = (wire, positions, create2) => {
        const origin = wire.origin();
        each$2(positions, (cpOption) => {
          cpOption.each((cp) => {
            const bar = create2(origin, cp);
            add(bar, resizeBar);
            append$1(wire.parent(), bar);
          });
        });
      };
      const refreshCol = (wire, colPositions, position, tableHeight) => {
        drawBar(wire, colPositions, (origin, cp) => {
          const colBar = col(cp.col, cp.x - origin.left, position.top - origin.top, BAR_THICKNESS, tableHeight);
          add(colBar, resizeColBar);
          return colBar;
        });
      };
      const refreshRow = (wire, rowPositions, position, tableWidth) => {
        drawBar(wire, rowPositions, (origin, cp) => {
          const rowBar = row(cp.row, position.left - origin.left, cp.y - origin.top, tableWidth, BAR_THICKNESS);
          add(rowBar, resizeRowBar);
          return rowBar;
        });
      };
      const refreshGrid = (warhouse, wire, table2, rows2, cols) => {
        const position = absolute(table2);
        const isResizable2 = wire.isResizable;
        const rowPositions = rows2.length > 0 ? height.positions(rows2, table2) : [];
        const resizableRowBars = rowPositions.length > 0 ? resizableRows(warhouse, isResizable2) : [];
        const resizableRowPositions = filter$2(rowPositions, (_pos, i) => exists(resizableRowBars, (barIndex) => i === barIndex));
        refreshRow(wire, resizableRowPositions, position, getOuter$2(table2));
        const colPositions = cols.length > 0 ? width.positions(cols, table2) : [];
        const resizableColBars = colPositions.length > 0 ? resizableColumns(warhouse, isResizable2) : [];
        const resizableColPositions = filter$2(colPositions, (_pos, i) => exists(resizableColBars, (barIndex) => i === barIndex));
        refreshCol(wire, resizableColPositions, position, getOuter$1(table2));
      };
      const refresh = (wire, table2) => {
        destroy(wire);
        if (wire.isResizable(table2)) {
          const warehouse = Warehouse.fromTable(table2);
          const rows$12 = rows(warehouse);
          const cols = columns(warehouse);
          refreshGrid(warehouse, wire, table2, rows$12, cols);
        }
      };
      const each = (wire, f) => {
        const bars = descendants(wire.parent(), "." + resizeBar);
        each$2(bars, f);
      };
      const hide = (wire) => {
        each(wire, (bar) => {
          set$1(bar, "display", "none");
        });
      };
      const show = (wire) => {
        each(wire, (bar) => {
          set$1(bar, "display", "block");
        });
      };
      const isRowBar = (element) => {
        return has(element, resizeRowBar);
      };
      const isColBar = (element) => {
        return has(element, resizeColBar);
      };
      const resizeBarDragging = resolve("resizer-bar-dragging");
      const BarManager = (wire) => {
        const mutation = BarMutation();
        const resizing = transform(mutation, {});
        let hoverTable = Optional.none();
        const getResizer = (element, type2) => {
          return Optional.from(get$b(element, type2));
        };
        mutation.events.drag.bind((event) => {
          getResizer(event.target, "data-row").each((_dataRow) => {
            const currentRow = getCssValue(event.target, "top");
            set$1(event.target, "top", currentRow + event.yDelta + "px");
          });
          getResizer(event.target, "data-column").each((_dataCol) => {
            const currentCol = getCssValue(event.target, "left");
            set$1(event.target, "left", currentCol + event.xDelta + "px");
          });
        });
        const getDelta = (target, dir) => {
          const newX = getCssValue(target, dir);
          const oldX = getAttrValue(target, "data-initial-" + dir, 0);
          return newX - oldX;
        };
        resizing.events.stop.bind(() => {
          mutation.get().each((target) => {
            hoverTable.each((table2) => {
              getResizer(target, "data-row").each((row2) => {
                const delta = getDelta(target, "top");
                remove$7(target, "data-initial-top");
                events.trigger.adjustHeight(table2, delta, parseInt(row2, 10));
              });
              getResizer(target, "data-column").each((column) => {
                const delta = getDelta(target, "left");
                remove$7(target, "data-initial-left");
                events.trigger.adjustWidth(table2, delta, parseInt(column, 10));
              });
              refresh(wire, table2);
            });
          });
        });
        const handler = (target, dir) => {
          events.trigger.startAdjust();
          mutation.assign(target);
          set$2(target, "data-initial-" + dir, getCssValue(target, dir));
          add(target, resizeBarDragging);
          set$1(target, "opacity", "0.2");
          resizing.go(wire.parent());
        };
        const mousedown = bind(wire.parent(), "mousedown", (event) => {
          if (isRowBar(event.target)) {
            handler(event.target, "top");
          }
          if (isColBar(event.target)) {
            handler(event.target, "left");
          }
        });
        const isRoot = (e) => {
          return eq$1(e, wire.view());
        };
        const findClosestEditableTable = (target) => closest$1(target, "table", isRoot).filter(isEditable$1);
        const mouseover = bind(wire.view(), "mouseover", (event) => {
          findClosestEditableTable(event.target).fold(() => {
            if (inBody(event.target)) {
              destroy(wire);
            }
          }, (table2) => {
            if (resizing.isActive()) {
              hoverTable = Optional.some(table2);
              refresh(wire, table2);
            }
          });
        });
        const destroy$1 = () => {
          mousedown.unbind();
          mouseover.unbind();
          resizing.destroy();
          destroy(wire);
        };
        const refresh$1 = (tbl) => {
          refresh(wire, tbl);
        };
        const events = create$1({
          adjustHeight: Event([
            "table",
            "delta",
            "row"
          ]),
          adjustWidth: Event([
            "table",
            "delta",
            "column"
          ]),
          startAdjust: Event([])
        });
        return {
          destroy: destroy$1,
          refresh: refresh$1,
          on: resizing.on,
          off: resizing.off,
          hideBars: curry(hide, wire),
          showBars: curry(show, wire),
          events: events.registry
        };
      };
      const create = (wire, resizing, lazySizing) => {
        const hdirection = height;
        const vdirection = width;
        const manager = BarManager(wire);
        const events = create$1({
          beforeResize: Event([
            "table",
            "type"
          ]),
          afterResize: Event([
            "table",
            "type"
          ]),
          startDrag: Event([])
        });
        manager.events.adjustHeight.bind((event) => {
          const table2 = event.table;
          events.trigger.beforeResize(table2, "row");
          const delta = hdirection.delta(event.delta, table2);
          adjustHeight(table2, delta, event.row, hdirection);
          events.trigger.afterResize(table2, "row");
        });
        manager.events.startAdjust.bind((_event) => {
          events.trigger.startDrag();
        });
        manager.events.adjustWidth.bind((event) => {
          const table2 = event.table;
          events.trigger.beforeResize(table2, "col");
          const delta = vdirection.delta(event.delta, table2);
          const tableSize = lazySizing(table2);
          adjustWidth(table2, delta, event.column, resizing, tableSize);
          events.trigger.afterResize(table2, "col");
        });
        return {
          on: manager.on,
          off: manager.off,
          refreshBars: manager.refresh,
          hideBars: manager.hideBars,
          showBars: manager.showBars,
          destroy: manager.destroy,
          events: events.registry
        };
      };
      const TableResize = { create };
      const only = (element, isResizable2) => {
        const parent2 = isDocument(element) ? documentElement(element) : element;
        return {
          parent: constant(parent2),
          view: constant(element),
          origin: constant(SugarPosition(0, 0)),
          isResizable: isResizable2
        };
      };
      const detached = (editable, chrome, isResizable2) => {
        const origin = () => absolute(chrome);
        return {
          parent: constant(chrome),
          view: constant(editable),
          origin,
          isResizable: isResizable2
        };
      };
      const body = (editable, chrome, isResizable2) => {
        return {
          parent: constant(chrome),
          view: constant(editable),
          origin: constant(SugarPosition(0, 0)),
          isResizable: isResizable2
        };
      };
      const ResizeWire = {
        only,
        detached,
        body
      };
      const createContainer = () => {
        const container = SugarElement.fromTag("div");
        setAll(container, {
          position: "static",
          height: "0",
          width: "0",
          padding: "0",
          margin: "0",
          border: "0"
        });
        append$1(body$1(), container);
        return container;
      };
      const get = (editor, isResizable2) => {
        return editor.inline ? ResizeWire.body(SugarElement.fromDom(editor.getBody()), createContainer(), isResizable2) : ResizeWire.only(SugarElement.fromDom(editor.getDoc()), isResizable2);
      };
      const remove = (editor, wire) => {
        if (editor.inline) {
          remove$6(wire.parent());
        }
      };
      const isTable = (node) => isNonNullable(node) && node.nodeName === "TABLE";
      const barResizerPrefix = "bar-";
      const isResizable = (elm) => get$b(elm, "data-mce-resize") !== "false";
      const syncPixels = (table2) => {
        const warehouse = Warehouse.fromTable(table2);
        if (!Warehouse.hasColumns(warehouse)) {
          each$2(cells$1(table2), (cell2) => {
            const computedWidth = get$a(cell2, "width");
            set$1(cell2, "width", computedWidth);
            remove$7(cell2, "width");
          });
        }
      };
      const TableResizeHandler = (editor) => {
        const selectionRng = value();
        const tableResize = value();
        const resizeWire = value();
        let startW;
        let startRawW;
        const lazySizing = (table2) => get$5(editor, table2);
        const lazyResizingBehaviour = () => isPreserveTableColumnResizing(editor) ? preserveTable() : resizeTable();
        const getNumColumns = (table2) => getGridSize(table2).columns;
        const afterCornerResize = (table2, origin, width2) => {
          const isRightEdgeResize = endsWith(origin, "e");
          if (startRawW === "") {
            convertToPercentSize(table2);
          }
          if (width2 !== startW && startRawW !== "") {
            set$1(table2, "width", startRawW);
            const resizing = lazyResizingBehaviour();
            const tableSize = lazySizing(table2);
            const col2 = isPreserveTableColumnResizing(editor) || isRightEdgeResize ? getNumColumns(table2) - 1 : 0;
            adjustWidth(table2, width2 - startW, col2, resizing, tableSize);
          } else if (isPercentage$1(startRawW)) {
            const percentW = parseFloat(startRawW.replace("%", ""));
            const targetPercentW = width2 * percentW / startW;
            set$1(table2, "width", targetPercentW + "%");
          }
          if (isPixel(startRawW)) {
            syncPixels(table2);
          }
        };
        const destroy2 = () => {
          tableResize.on((sz) => {
            sz.destroy();
          });
          resizeWire.on((w) => {
            remove(editor, w);
          });
        };
        editor.on("init", () => {
          const rawWire = get(editor, isResizable);
          resizeWire.set(rawWire);
          if (hasTableObjectResizing(editor) && hasTableResizeBars(editor)) {
            const resizing = lazyResizingBehaviour();
            const sz = TableResize.create(rawWire, resizing, lazySizing);
            sz.on();
            sz.events.startDrag.bind((_event) => {
              selectionRng.set(editor.selection.getRng());
            });
            sz.events.beforeResize.bind((event) => {
              const rawTable = event.table.dom;
              fireObjectResizeStart(editor, rawTable, getPixelWidth(rawTable), getPixelHeight(rawTable), barResizerPrefix + event.type);
            });
            sz.events.afterResize.bind((event) => {
              const table2 = event.table;
              const rawTable = table2.dom;
              removeDataStyle(table2);
              selectionRng.on((rng) => {
                editor.selection.setRng(rng);
                editor.focus();
              });
              fireObjectResized(editor, rawTable, getPixelWidth(rawTable), getPixelHeight(rawTable), barResizerPrefix + event.type);
              editor.undoManager.add();
            });
            tableResize.set(sz);
          }
        });
        editor.on("ObjectResizeStart", (e) => {
          const targetElm = e.target;
          if (isTable(targetElm)) {
            const table2 = SugarElement.fromDom(targetElm);
            each$2(editor.dom.select(".mce-clonedresizable"), (clone2) => {
              editor.dom.addClass(clone2, "mce-" + getTableColumnResizingBehaviour(editor) + "-columns");
            });
            if (!isPixelSizing(table2) && isTablePixelsForced(editor)) {
              convertToPixelSize(table2);
            } else if (!isPercentSizing(table2) && isTablePercentagesForced(editor)) {
              convertToPercentSize(table2);
            }
            if (isNoneSizing(table2) && startsWith(e.origin, barResizerPrefix)) {
              convertToPercentSize(table2);
            }
            startW = e.width;
            startRawW = isTableResponsiveForced(editor) ? "" : getRawWidth(editor, targetElm).getOr("");
          }
        });
        editor.on("ObjectResized", (e) => {
          const targetElm = e.target;
          if (isTable(targetElm)) {
            const table2 = SugarElement.fromDom(targetElm);
            const origin = e.origin;
            if (startsWith(origin, "corner-")) {
              afterCornerResize(table2, origin, e.width);
            }
            removeDataStyle(table2);
            fireTableModified(editor, table2.dom, styleModified);
          }
        });
        editor.on("SwitchMode", () => {
          tableResize.on((resize2) => {
            if (editor.mode.isReadOnly()) {
              resize2.hideBars();
            } else {
              resize2.showBars();
            }
          });
        });
        editor.on("dragstart dragend", (e) => {
          tableResize.on((resize2) => {
            if (e.type === "dragstart") {
              resize2.hideBars();
              resize2.off();
            } else {
              resize2.on();
              resize2.showBars();
            }
          });
        });
        editor.on("remove", () => {
          destroy2();
        });
        const refresh2 = (table2) => {
          tableResize.on((resize2) => resize2.refreshBars(SugarElement.fromDom(table2)));
        };
        const hide2 = () => {
          tableResize.on((resize2) => resize2.hideBars());
        };
        const show2 = () => {
          tableResize.on((resize2) => resize2.showBars());
        };
        return {
          refresh: refresh2,
          hide: hide2,
          show: show2
        };
      };
      const setupTable = (editor) => {
        register(editor);
        const resizeHandler = TableResizeHandler(editor);
        const cellSelectionHandler = TableCellSelectionHandler(editor, resizeHandler);
        const actions = TableActions(editor, resizeHandler, cellSelectionHandler);
        registerCommands(editor, actions);
        registerQueryCommands(editor, actions);
        registerEvents(editor, actions);
        return {
          getSelectedCells: cellSelectionHandler.getSelectedCells,
          clearSelectedCells: cellSelectionHandler.clearSelectedCells
        };
      };
      const DomModel = (editor) => {
        const table2 = setupTable(editor);
        return { table: table2 };
      };
      var Model = () => {
        global$1.add("dom", DomModel);
      };
      Model();
    })();
  }
});

// node_modules/.pnpm/tinymce@6.6.2/node_modules/tinymce/models/dom/index.js
require_model();
//# sourceMappingURL=tinymce_models_dom.js.map
