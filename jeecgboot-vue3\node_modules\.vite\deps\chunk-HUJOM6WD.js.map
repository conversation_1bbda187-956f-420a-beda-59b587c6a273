{"version": 3, "sources": ["../../.pnpm/dom-align@1.12.4/node_modules/src/propertyUtils.js", "../../.pnpm/dom-align@1.12.4/node_modules/src/utils.js", "../../.pnpm/dom-align@1.12.4/node_modules/src/getOffsetParent.js", "../../.pnpm/dom-align@1.12.4/node_modules/src/isAncestorFixed.js", "../../.pnpm/dom-align@1.12.4/node_modules/src/getVisibleRectForElement.js", "../../.pnpm/dom-align@1.12.4/node_modules/src/adjustForViewport.js", "../../.pnpm/dom-align@1.12.4/node_modules/src/getRegion.js", "../../.pnpm/dom-align@1.12.4/node_modules/src/getAlignOffset.js", "../../.pnpm/dom-align@1.12.4/node_modules/src/getElFuturePos.js", "../../.pnpm/dom-align@1.12.4/node_modules/src/align/align.js", "../../.pnpm/dom-align@1.12.4/node_modules/src/align/alignElement.js", "../../.pnpm/dom-align@1.12.4/node_modules/src/align/alignPoint.js"], "sourcesContent": ["let vendorPrefix;\n\nconst jsCssMap = {\n  Webkit: '-webkit-',\n  Moz: '-moz-',\n  // IE did it wrong again ...\n  ms: '-ms-',\n  O: '-o-',\n};\n\nfunction getVendorPrefix() {\n  if (vendorPrefix !== undefined) {\n    return vendorPrefix;\n  }\n  vendorPrefix = '';\n  const style = document.createElement('p').style;\n  const testProp = 'Transform';\n  for (const key in jsCssMap) {\n    if (key + testProp in style) {\n      vendorPrefix = key;\n    }\n  }\n  return vendorPrefix;\n}\n\nfunction getTransitionName() {\n  return getVendorPrefix()\n    ? `${getVendorPrefix()}TransitionProperty`\n    : 'transitionProperty';\n}\n\nexport function getTransformName() {\n  return getVendorPrefix() ? `${getVendorPrefix()}Transform` : 'transform';\n}\n\nexport function setTransitionProperty(node, value) {\n  const name = getTransitionName();\n  if (name) {\n    node.style[name] = value;\n    if (name !== 'transitionProperty') {\n      node.style.transitionProperty = value;\n    }\n  }\n}\n\nfunction setTransform(node, value) {\n  const name = getTransformName();\n  if (name) {\n    node.style[name] = value;\n    if (name !== 'transform') {\n      node.style.transform = value;\n    }\n  }\n}\n\nexport function getTransitionProperty(node) {\n  return node.style.transitionProperty || node.style[getTransitionName()];\n}\n\nexport function getTransformXY(node) {\n  const style = window.getComputedStyle(node, null);\n  const transform =\n    style.getPropertyValue('transform') ||\n    style.getPropertyValue(getTransformName());\n  if (transform && transform !== 'none') {\n    const matrix = transform.replace(/[^0-9\\-.,]/g, '').split(',');\n    return {\n      x: parseFloat(matrix[12] || matrix[4], 0),\n      y: parseFloat(matrix[13] || matrix[5], 0),\n    };\n  }\n  return {\n    x: 0,\n    y: 0,\n  };\n}\n\nconst matrix2d = /matrix\\((.*)\\)/;\nconst matrix3d = /matrix3d\\((.*)\\)/;\n\nexport function setTransformXY(node, xy) {\n  const style = window.getComputedStyle(node, null);\n  const transform =\n    style.getPropertyValue('transform') ||\n    style.getPropertyValue(getTransformName());\n  if (transform && transform !== 'none') {\n    let arr;\n    let match2d = transform.match(matrix2d);\n    if (match2d) {\n      match2d = match2d[1];\n      arr = match2d.split(',').map(item => {\n        return parseFloat(item, 10);\n      });\n      arr[4] = xy.x;\n      arr[5] = xy.y;\n      setTransform(node, `matrix(${arr.join(',')})`);\n    } else {\n      const match3d = transform.match(matrix3d)[1];\n      arr = match3d.split(',').map(item => {\n        return parseFloat(item, 10);\n      });\n      arr[12] = xy.x;\n      arr[13] = xy.y;\n      setTransform(node, `matrix3d(${arr.join(',')})`);\n    }\n  } else {\n    setTransform(\n      node,\n      `translateX(${xy.x}px) translateY(${xy.y}px) translateZ(0)`,\n    );\n  }\n}\n", "import {\n  setTransitionProperty,\n  getTransitionProperty,\n  getTransformXY,\n  setTransformXY,\n  getTransformName,\n} from './propertyUtils';\n\nconst RE_NUM = /[\\-+]?(?:\\d*\\.|)\\d+(?:[eE][\\-+]?\\d+|)/.source;\n\nlet getComputedStyleX;\n\n// https://stackoverflow.com/a/3485654/3040605\nfunction forceRelayout(elem) {\n  const originalStyle = elem.style.display;\n  elem.style.display = 'none';\n  elem.offsetHeight; // eslint-disable-line\n  elem.style.display = originalStyle;\n}\n\nfunction css(el, name, v) {\n  let value = v;\n  if (typeof name === 'object') {\n    for (const i in name) {\n      if (name.hasOwnProperty(i)) {\n        css(el, i, name[i]);\n      }\n    }\n    return undefined;\n  }\n  if (typeof value !== 'undefined') {\n    if (typeof value === 'number') {\n      value = `${value}px`;\n    }\n    el.style[name] = value;\n    return undefined;\n  }\n  return getComputedStyleX(el, name);\n}\n\nfunction getClientPosition(elem) {\n  let box;\n  let x;\n  let y;\n  const doc = elem.ownerDocument;\n  const body = doc.body;\n  const docElem = doc && doc.documentElement;\n  // 根据 GBS 最新数据，A-Grade Browsers 都已支持 getBoundingClientRect 方法，不用再考虑传统的实现方式\n  box = elem.getBoundingClientRect();\n\n  // 注：jQuery 还考虑减去 docElem.clientLeft/clientTop\n  // 但测试发现，这样反而会导致当 html 和 body 有边距/边框样式时，获取的值不正确\n  // 此外，ie6 会忽略 html 的 margin 值，幸运地是没有谁会去设置 html 的 margin\n\n  x = Math.floor(box.left);\n  y = Math.floor(box.top);\n\n  // In IE, most of the time, 2 extra pixels are added to the top and left\n  // due to the implicit 2-pixel inset border.  In IE6/7 quirks mode and\n  // IE6 standards mode, this border can be overridden by setting the\n  // document element's border to zero -- thus, we cannot rely on the\n  // offset always being 2 pixels.\n\n  // In quirks mode, the offset can be determined by querying the body's\n  // clientLeft/clientTop, but in standards mode, it is found by querying\n  // the document element's clientLeft/clientTop.  Since we already called\n  // getClientBoundingRect we have already forced a reflow, so it is not\n  // too expensive just to query them all.\n\n  // ie 下应该减去窗口的边框吧，毕竟默认 absolute 都是相对窗口定位的\n  // 窗口边框标准是设 documentElement ,quirks 时设置 body\n  // 最好禁止在 body 和 html 上边框 ，但 ie < 9 html 默认有 2px ，减去\n  // 但是非 ie 不可能设置窗口边框，body html 也不是窗口 ,ie 可以通过 html,body 设置\n  // 标准 ie 下 docElem.clientTop 就是 border-top\n  // ie7 html 即窗口边框改变不了。永远为 2\n  // 但标准 firefox/chrome/ie9 下 docElem.clientTop 是窗口边框，即使设了 border-top 也为 0\n\n  x -= docElem.clientLeft || body.clientLeft || 0;\n  y -= docElem.clientTop || body.clientTop || 0;\n\n  return {\n    left: x,\n    top: y,\n  };\n}\n\nfunction getScroll(w, top) {\n  let ret = w[`page${top ? 'Y' : 'X'}Offset`];\n  const method = `scroll${top ? 'Top' : 'Left'}`;\n  if (typeof ret !== 'number') {\n    const d = w.document;\n    // ie6,7,8 standard mode\n    ret = d.documentElement[method];\n    if (typeof ret !== 'number') {\n      // quirks mode\n      ret = d.body[method];\n    }\n  }\n  return ret;\n}\n\nfunction getScrollLeft(w) {\n  return getScroll(w);\n}\n\nfunction getScrollTop(w) {\n  return getScroll(w, true);\n}\n\nfunction getOffset(el) {\n  const pos = getClientPosition(el);\n  const doc = el.ownerDocument;\n  const w = doc.defaultView || doc.parentWindow;\n  pos.left += getScrollLeft(w);\n  pos.top += getScrollTop(w);\n  return pos;\n}\n\n/**\n * A crude way of determining if an object is a window\n * @member util\n */\nfunction isWindow(obj) {\n  // must use == for ie8\n  /* eslint eqeqeq:0 */\n  return obj !== null && obj !== undefined && obj == obj.window;\n}\n\nfunction getDocument(node) {\n  if (isWindow(node)) {\n    return node.document;\n  }\n  if (node.nodeType === 9) {\n    return node;\n  }\n  return node.ownerDocument;\n}\n\nfunction _getComputedStyle(elem, name, cs) {\n  let computedStyle = cs;\n  let val = '';\n  const d = getDocument(elem);\n  computedStyle = computedStyle || d.defaultView.getComputedStyle(elem, null);\n\n  // https://github.com/kissyteam/kissy/issues/61\n  if (computedStyle) {\n    val = computedStyle.getPropertyValue(name) || computedStyle[name];\n  }\n\n  return val;\n}\n\nconst _RE_NUM_NO_PX = new RegExp(`^(${RE_NUM})(?!px)[a-z%]+$`, 'i');\nconst RE_POS = /^(top|right|bottom|left)$/;\nconst CURRENT_STYLE = 'currentStyle';\nconst RUNTIME_STYLE = 'runtimeStyle';\nconst LEFT = 'left';\nconst PX = 'px';\n\nfunction _getComputedStyleIE(elem, name) {\n  // currentStyle maybe null\n  // http://msdn.microsoft.com/en-us/library/ms535231.aspx\n  let ret = elem[CURRENT_STYLE] && elem[CURRENT_STYLE][name];\n\n  // 当 width/height 设置为百分比时，通过 pixelLeft 方式转换的 width/height 值\n  // 一开始就处理了! CUSTOM_STYLE.height,CUSTOM_STYLE.width ,cssHook 解决@2011-08-19\n  // 在 ie 下不对，需要直接用 offset 方式\n  // borderWidth 等值也有问题，但考虑到 borderWidth 设为百分比的概率很小，这里就不考虑了\n\n  // From the awesome hack by Dean Edwards\n  // http://erik.eae.net/archives/2007/07/27/18.54.15/#comment-102291\n  // If we're not dealing with a regular pixel number\n  // but a number that has a weird ending, we need to convert it to pixels\n  // exclude left right for relativity\n  if (_RE_NUM_NO_PX.test(ret) && !RE_POS.test(name)) {\n    // Remember the original values\n    const style = elem.style;\n    const left = style[LEFT];\n    const rsLeft = elem[RUNTIME_STYLE][LEFT];\n\n    // prevent flashing of content\n    elem[RUNTIME_STYLE][LEFT] = elem[CURRENT_STYLE][LEFT];\n\n    // Put in the new values to get a computed value out\n    style[LEFT] = name === 'fontSize' ? '1em' : ret || 0;\n    ret = style.pixelLeft + PX;\n\n    // Revert the changed values\n    style[LEFT] = left;\n\n    elem[RUNTIME_STYLE][LEFT] = rsLeft;\n  }\n  return ret === '' ? 'auto' : ret;\n}\n\nif (typeof window !== 'undefined') {\n  getComputedStyleX = window.getComputedStyle\n    ? _getComputedStyle\n    : _getComputedStyleIE;\n}\n\nfunction getOffsetDirection(dir, option) {\n  if (dir === 'left') {\n    return option.useCssRight ? 'right' : dir;\n  }\n  return option.useCssBottom ? 'bottom' : dir;\n}\n\nfunction oppositeOffsetDirection(dir) {\n  if (dir === 'left') {\n    return 'right';\n  } else if (dir === 'right') {\n    return 'left';\n  } else if (dir === 'top') {\n    return 'bottom';\n  } else if (dir === 'bottom') {\n    return 'top';\n  }\n}\n\n// 设置 elem 相对 elem.ownerDocument 的坐标\nfunction setLeftTop(elem, offset, option) {\n  // set position first, in-case top/left are set even on static elem\n  if (css(elem, 'position') === 'static') {\n    elem.style.position = 'relative';\n  }\n  let presetH = -999;\n  let presetV = -999;\n  const horizontalProperty = getOffsetDirection('left', option);\n  const verticalProperty = getOffsetDirection('top', option);\n  const oppositeHorizontalProperty = oppositeOffsetDirection(\n    horizontalProperty,\n  );\n  const oppositeVerticalProperty = oppositeOffsetDirection(verticalProperty);\n\n  if (horizontalProperty !== 'left') {\n    presetH = 999;\n  }\n\n  if (verticalProperty !== 'top') {\n    presetV = 999;\n  }\n  let originalTransition = '';\n  const originalOffset = getOffset(elem);\n  if ('left' in offset || 'top' in offset) {\n    originalTransition = getTransitionProperty(elem) || '';\n    setTransitionProperty(elem, 'none');\n  }\n  if ('left' in offset) {\n    elem.style[oppositeHorizontalProperty] = '';\n    elem.style[horizontalProperty] = `${presetH}px`;\n  }\n  if ('top' in offset) {\n    elem.style[oppositeVerticalProperty] = '';\n    elem.style[verticalProperty] = `${presetV}px`;\n  }\n  // force relayout\n  forceRelayout(elem);\n  const old = getOffset(elem);\n  const originalStyle = {};\n  for (const key in offset) {\n    if (offset.hasOwnProperty(key)) {\n      const dir = getOffsetDirection(key, option);\n      const preset = key === 'left' ? presetH : presetV;\n      const off = originalOffset[key] - old[key];\n      if (dir === key) {\n        originalStyle[dir] = preset + off;\n      } else {\n        originalStyle[dir] = preset - off;\n      }\n    }\n  }\n  css(elem, originalStyle);\n  // force relayout\n  forceRelayout(elem);\n  if ('left' in offset || 'top' in offset) {\n    setTransitionProperty(elem, originalTransition);\n  }\n  const ret = {};\n  for (const key in offset) {\n    if (offset.hasOwnProperty(key)) {\n      const dir = getOffsetDirection(key, option);\n      const off = offset[key] - originalOffset[key];\n      if (key === dir) {\n        ret[dir] = originalStyle[dir] + off;\n      } else {\n        ret[dir] = originalStyle[dir] - off;\n      }\n    }\n  }\n  css(elem, ret);\n}\n\nfunction setTransform(elem, offset) {\n  const originalOffset = getOffset(elem);\n  const originalXY = getTransformXY(elem);\n  const resultXY = { x: originalXY.x, y: originalXY.y };\n  if ('left' in offset) {\n    resultXY.x = originalXY.x + offset.left - originalOffset.left;\n  }\n  if ('top' in offset) {\n    resultXY.y = originalXY.y + offset.top - originalOffset.top;\n  }\n  setTransformXY(elem, resultXY);\n}\n\nfunction setOffset(elem, offset, option) {\n  if (option.ignoreShake) {\n    const oriOffset = getOffset(elem);\n\n    const oLeft = oriOffset.left.toFixed(0);\n    const oTop = oriOffset.top.toFixed(0);\n    const tLeft = offset.left.toFixed(0);\n    const tTop = offset.top.toFixed(0);\n\n    if (oLeft === tLeft && oTop === tTop) {\n      return;\n    }\n  }\n\n  if (option.useCssRight || option.useCssBottom) {\n    setLeftTop(elem, offset, option);\n  } else if (\n    option.useCssTransform &&\n    getTransformName() in document.body.style\n  ) {\n    setTransform(elem, offset, option);\n  } else {\n    setLeftTop(elem, offset, option);\n  }\n}\n\nfunction each(arr, fn) {\n  for (let i = 0; i < arr.length; i++) {\n    fn(arr[i]);\n  }\n}\n\nfunction isBorderBoxFn(elem) {\n  return getComputedStyleX(elem, 'boxSizing') === 'border-box';\n}\n\nconst BOX_MODELS = ['margin', 'border', 'padding'];\nconst CONTENT_INDEX = -1;\nconst PADDING_INDEX = 2;\nconst BORDER_INDEX = 1;\nconst MARGIN_INDEX = 0;\n\nfunction swap(elem, options, callback) {\n  const old = {};\n  const style = elem.style;\n  let name;\n\n  // Remember the old values, and insert the new ones\n  for (name in options) {\n    if (options.hasOwnProperty(name)) {\n      old[name] = style[name];\n      style[name] = options[name];\n    }\n  }\n\n  callback.call(elem);\n\n  // Revert the old values\n  for (name in options) {\n    if (options.hasOwnProperty(name)) {\n      style[name] = old[name];\n    }\n  }\n}\n\nfunction getPBMWidth(elem, props, which) {\n  let value = 0;\n  let prop;\n  let j;\n  let i;\n  for (j = 0; j < props.length; j++) {\n    prop = props[j];\n    if (prop) {\n      for (i = 0; i < which.length; i++) {\n        let cssProp;\n        if (prop === 'border') {\n          cssProp = `${prop}${which[i]}Width`;\n        } else {\n          cssProp = prop + which[i];\n        }\n        value += parseFloat(getComputedStyleX(elem, cssProp)) || 0;\n      }\n    }\n  }\n  return value;\n}\n\nconst domUtils = {\n  getParent(element) {\n    let parent = element;\n    do {\n      if (parent.nodeType === 11 && parent.host) {\n        parent = parent.host;\n      } else {\n        parent = parent.parentNode;\n      }\n    } while (parent && parent.nodeType !== 1 && parent.nodeType !== 9);\n    return parent;\n  },\n};\n\neach(['Width', 'Height'], name => {\n  domUtils[`doc${name}`] = refWin => {\n    const d = refWin.document;\n    return Math.max(\n      // firefox chrome documentElement.scrollHeight< body.scrollHeight\n      // ie standard mode : documentElement.scrollHeight> body.scrollHeight\n      d.documentElement[`scroll${name}`],\n      // quirks : documentElement.scrollHeight 最大等于可视窗口多一点？\n      d.body[`scroll${name}`],\n      domUtils[`viewport${name}`](d),\n    );\n  };\n\n  domUtils[`viewport${name}`] = win => {\n    // pc browser includes scrollbar in window.innerWidth\n    const prop = `client${name}`;\n    const doc = win.document;\n    const body = doc.body;\n    const documentElement = doc.documentElement;\n    const documentElementProp = documentElement[prop];\n    // 标准模式取 documentElement\n    // backcompat 取 body\n    return (\n      (doc.compatMode === 'CSS1Compat' && documentElementProp) ||\n      (body && body[prop]) ||\n      documentElementProp\n    );\n  };\n});\n\n/*\n 得到元素的大小信息\n @param elem\n @param name\n @param {String} [extra]  'padding' : (css width) + padding\n 'border' : (css width) + padding + border\n 'margin' : (css width) + padding + border + margin\n */\nfunction getWH(elem, name, ex) {\n  let extra = ex;\n  if (isWindow(elem)) {\n    return name === 'width'\n      ? domUtils.viewportWidth(elem)\n      : domUtils.viewportHeight(elem);\n  } else if (elem.nodeType === 9) {\n    return name === 'width'\n      ? domUtils.docWidth(elem)\n      : domUtils.docHeight(elem);\n  }\n  const which = name === 'width' ? ['Left', 'Right'] : ['Top', 'Bottom'];\n  let borderBoxValue =\n    name === 'width'\n      ? Math.floor(elem.getBoundingClientRect().width)\n      : Math.floor(elem.getBoundingClientRect().height);\n  const isBorderBox = isBorderBoxFn(elem);\n  let cssBoxValue = 0;\n  if (\n    borderBoxValue === null ||\n    borderBoxValue === undefined ||\n    borderBoxValue <= 0\n  ) {\n    borderBoxValue = undefined;\n    // Fall back to computed then un computed css if necessary\n    cssBoxValue = getComputedStyleX(elem, name);\n    if (\n      cssBoxValue === null ||\n      cssBoxValue === undefined ||\n      Number(cssBoxValue) < 0\n    ) {\n      cssBoxValue = elem.style[name] || 0;\n    }\n    // Normalize '', auto, and prepare for extra\n    cssBoxValue = Math.floor(parseFloat(cssBoxValue)) || 0;\n  }\n  if (extra === undefined) {\n    extra = isBorderBox ? BORDER_INDEX : CONTENT_INDEX;\n  }\n  const borderBoxValueOrIsBorderBox =\n    borderBoxValue !== undefined || isBorderBox;\n  const val = borderBoxValue || cssBoxValue;\n  if (extra === CONTENT_INDEX) {\n    if (borderBoxValueOrIsBorderBox) {\n      return val - getPBMWidth(elem, ['border', 'padding'], which);\n    }\n    return cssBoxValue;\n  } else if (borderBoxValueOrIsBorderBox) {\n    if (extra === BORDER_INDEX) {\n      return val;\n    }\n    return (\n      val +\n      (extra === PADDING_INDEX\n        ? -getPBMWidth(elem, ['border'], which)\n        : getPBMWidth(elem, ['margin'], which))\n    );\n  }\n  return cssBoxValue + getPBMWidth(elem, BOX_MODELS.slice(extra), which);\n}\n\nconst cssShow = {\n  position: 'absolute',\n  visibility: 'hidden',\n  display: 'block',\n};\n\n// fix #119 : https://github.com/kissyteam/kissy/issues/119\nfunction getWHIgnoreDisplay(...args) {\n  let val;\n  const elem = args[0];\n  // in case elem is window\n  // elem.offsetWidth === undefined\n  if (elem.offsetWidth !== 0) {\n    val = getWH.apply(undefined, args);\n  } else {\n    swap(elem, cssShow, () => {\n      val = getWH.apply(undefined, args);\n    });\n  }\n  return val;\n}\n\neach(['width', 'height'], name => {\n  const first = name.charAt(0).toUpperCase() + name.slice(1);\n  domUtils[`outer${first}`] = (el, includeMargin) => {\n    return (\n      el &&\n      getWHIgnoreDisplay(el, name, includeMargin ? MARGIN_INDEX : BORDER_INDEX)\n    );\n  };\n  const which = name === 'width' ? ['Left', 'Right'] : ['Top', 'Bottom'];\n\n  domUtils[name] = (elem, v) => {\n    let val = v;\n    if (val !== undefined) {\n      if (elem) {\n        const isBorderBox = isBorderBoxFn(elem);\n        if (isBorderBox) {\n          val += getPBMWidth(elem, ['padding', 'border'], which);\n        }\n        return css(elem, name, val);\n      }\n      return undefined;\n    }\n    return elem && getWHIgnoreDisplay(elem, name, CONTENT_INDEX);\n  };\n});\n\nfunction mix(to, from) {\n  for (const i in from) {\n    if (from.hasOwnProperty(i)) {\n      to[i] = from[i];\n    }\n  }\n  return to;\n}\n\nconst utils = {\n  getWindow(node) {\n    if (node && node.document && node.setTimeout) {\n      return node;\n    }\n    const doc = node.ownerDocument || node;\n    return doc.defaultView || doc.parentWindow;\n  },\n  getDocument,\n  offset(el, value, option) {\n    if (typeof value !== 'undefined') {\n      setOffset(el, value, option || {});\n    } else {\n      return getOffset(el);\n    }\n  },\n  isWindow,\n  each,\n  css,\n  clone(obj) {\n    let i;\n    const ret = {};\n    for (i in obj) {\n      if (obj.hasOwnProperty(i)) {\n        ret[i] = obj[i];\n      }\n    }\n    const overflow = obj.overflow;\n    if (overflow) {\n      for (i in obj) {\n        if (obj.hasOwnProperty(i)) {\n          ret.overflow[i] = obj.overflow[i];\n        }\n      }\n    }\n    return ret;\n  },\n  mix,\n  getWindowScrollLeft(w) {\n    return getScrollLeft(w);\n  },\n  getWindowScrollTop(w) {\n    return getScrollTop(w);\n  },\n  merge(...args) {\n    const ret = {};\n    for (let i = 0; i < args.length; i++) {\n      utils.mix(ret, args[i]);\n    }\n    return ret;\n  },\n  viewportWidth: 0,\n  viewportHeight: 0,\n};\n\nmix(utils, domUtils);\n\nexport default utils;\n", "import utils from './utils';\n\n/**\n * 得到会导致元素显示不全的祖先元素\n */\nconst { getParent } = utils;\n\nfunction getOffsetParent(element) {\n  if (utils.isWindow(element) || element.nodeType === 9) {\n    return null;\n  }\n  // ie 这个也不是完全可行\n  /*\n   <div style=\"width: 50px;height: 100px;overflow: hidden\">\n   <div style=\"width: 50px;height: 100px;position: relative;\" id=\"d6\">\n   元素 6 高 100px 宽 50px<br/>\n   </div>\n   </div>\n   */\n  // element.offsetParent does the right thing in ie7 and below. Return parent with layout!\n  //  In other browsers it only includes elements with position absolute, relative or\n  // fixed, not elements with overflow set to auto or scroll.\n  //        if (UA.ie && ieMode < 8) {\n  //            return element.offsetParent;\n  //        }\n  // 统一的 offsetParent 方法\n  const doc = utils.getDocument(element);\n  const body = doc.body;\n  let parent;\n  let positionStyle = utils.css(element, 'position');\n  const skipStatic = positionStyle === 'fixed' || positionStyle === 'absolute';\n\n  if (!skipStatic) {\n    return element.nodeName.toLowerCase() === 'html'\n      ? null\n      : getParent(element);\n  }\n\n  for (\n    parent = getParent(element);\n    parent && parent !== body && parent.nodeType !== 9;\n    parent = getParent(parent)\n  ) {\n    positionStyle = utils.css(parent, 'position');\n    if (positionStyle !== 'static') {\n      return parent;\n    }\n  }\n  return null;\n}\n\nexport default getOffsetParent;\n", "import utils from './utils';\n\nconst { getParent } = utils;\n\nexport default function isAncestorFixed(element) {\n  if (utils.isWindow(element) || element.nodeType === 9) {\n    return false;\n  }\n\n  const doc = utils.getDocument(element);\n  const body = doc.body;\n  let parent = null;\n  for (\n    parent = getParent(element);\n    // 修复元素位于 document.documentElement 下导致崩溃问题\n    parent && parent !== body && parent !== doc;\n    parent = getParent(parent)\n  ) {\n    const positionStyle = utils.css(parent, 'position');\n    if (positionStyle === 'fixed') {\n      return true;\n    }\n  }\n  return false;\n}\n", "import utils from './utils';\nimport getOffsetParent from './getOffsetParent';\nimport isAncestorFixed from './isAncestorFixed';\n\n/**\n * 获得元素的显示部分的区域\n */\nfunction getVisibleRectForElement(element, alwaysByViewport) {\n  const visibleRect = {\n    left: 0,\n    right: Infinity,\n    top: 0,\n    bottom: Infinity,\n  };\n  let el = getOffsetParent(element);\n  const doc = utils.getDocument(element);\n  const win = doc.defaultView || doc.parentWindow;\n  const body = doc.body;\n  const documentElement = doc.documentElement;\n\n  // Determine the size of the visible rect by climbing the dom accounting for\n  // all scrollable containers.\n  while (el) {\n    // clientWidth is zero for inline block elements in ie.\n    if (\n      (navigator.userAgent.indexOf('MSIE') === -1 || el.clientWidth !== 0) &&\n      // body may have overflow set on it, yet we still get the entire\n      // viewport. In some browsers, el.offsetParent may be\n      // document.documentElement, so check for that too.\n      (el !== body &&\n        el !== documentElement &&\n        utils.css(el, 'overflow') !== 'visible')\n    ) {\n      const pos = utils.offset(el);\n      // add border\n      pos.left += el.clientLeft;\n      pos.top += el.clientTop;\n      visibleRect.top = Math.max(visibleRect.top, pos.top);\n      visibleRect.right = Math.min(\n        visibleRect.right,\n        // consider area without scrollBar\n        pos.left + el.clientWidth,\n      );\n      visibleRect.bottom = Math.min(\n        visibleRect.bottom,\n        pos.top + el.clientHeight,\n      );\n      visibleRect.left = Math.max(visibleRect.left, pos.left);\n    } else if (el === body || el === documentElement) {\n      break;\n    }\n    el = getOffsetParent(el);\n  }\n\n  // Set element position to fixed\n  // make sure absolute element itself don't affect it's visible area\n  // https://github.com/ant-design/ant-design/issues/7601\n  let originalPosition = null;\n  if (!utils.isWindow(element) && element.nodeType !== 9) {\n    originalPosition = element.style.position;\n    const position = utils.css(element, 'position');\n    if (position === 'absolute') {\n      element.style.position = 'fixed';\n    }\n  }\n\n  const scrollX = utils.getWindowScrollLeft(win);\n  const scrollY = utils.getWindowScrollTop(win);\n  const viewportWidth = utils.viewportWidth(win);\n  const viewportHeight = utils.viewportHeight(win);\n  let documentWidth = documentElement.scrollWidth;\n  let documentHeight = documentElement.scrollHeight;\n\n  // scrollXXX on html is sync with body which means overflow: hidden on body gets wrong scrollXXX.\n  // We should cut this ourself.\n  const bodyStyle = window.getComputedStyle(body);\n  if (bodyStyle.overflowX === 'hidden') {\n    documentWidth = win.innerWidth;\n  }\n  if (bodyStyle.overflowY === 'hidden') {\n    documentHeight = win.innerHeight;\n  }\n\n  // Reset element position after calculate the visible area\n  if (element.style) {\n    element.style.position = originalPosition;\n  }\n\n  if (alwaysByViewport || isAncestorFixed(element)) {\n    // Clip by viewport's size.\n    visibleRect.left = Math.max(visibleRect.left, scrollX);\n    visibleRect.top = Math.max(visibleRect.top, scrollY);\n    visibleRect.right = Math.min(visibleRect.right, scrollX + viewportWidth);\n    visibleRect.bottom = Math.min(visibleRect.bottom, scrollY + viewportHeight);\n  } else {\n    // Clip by document's size.\n    const maxVisibleWidth = Math.max(documentWidth, scrollX + viewportWidth);\n    visibleRect.right = Math.min(visibleRect.right, maxVisibleWidth);\n\n    const maxVisibleHeight = Math.max(documentHeight, scrollY + viewportHeight);\n    visibleRect.bottom = Math.min(visibleRect.bottom, maxVisibleHeight);\n  }\n\n  return visibleRect.top >= 0 &&\n    visibleRect.left >= 0 &&\n    visibleRect.bottom > visibleRect.top &&\n    visibleRect.right > visibleRect.left\n    ? visibleRect\n    : null;\n}\n\nexport default getVisibleRectForElement;\n", "import utils from './utils';\n\nfunction adjustForViewport(elFuturePos, elRegion, visibleRect, overflow) {\n  const pos = utils.clone(elFuturePos);\n  const size = {\n    width: elRegion.width,\n    height: elRegion.height,\n  };\n\n  if (overflow.adjustX && pos.left < visibleRect.left) {\n    pos.left = visibleRect.left;\n  }\n\n  // Left edge inside and right edge outside viewport, try to resize it.\n  if (\n    overflow.resizeWidth &&\n    pos.left >= visibleRect.left &&\n    pos.left + size.width > visibleRect.right\n  ) {\n    size.width -= pos.left + size.width - visibleRect.right;\n  }\n\n  // Right edge outside viewport, try to move it.\n  if (overflow.adjustX && pos.left + size.width > visibleRect.right) {\n    // 保证左边界和可视区域左边界对齐\n    pos.left = Math.max(visibleRect.right - size.width, visibleRect.left);\n  }\n\n  // Top edge outside viewport, try to move it.\n  if (overflow.adjustY && pos.top < visibleRect.top) {\n    pos.top = visibleRect.top;\n  }\n\n  // Top edge inside and bottom edge outside viewport, try to resize it.\n  if (\n    overflow.resizeHeight &&\n    pos.top >= visibleRect.top &&\n    pos.top + size.height > visibleRect.bottom\n  ) {\n    size.height -= pos.top + size.height - visibleRect.bottom;\n  }\n\n  // Bottom edge outside viewport, try to move it.\n  if (overflow.adjustY && pos.top + size.height > visibleRect.bottom) {\n    // 保证上边界和可视区域上边界对齐\n    pos.top = Math.max(visibleRect.bottom - size.height, visibleRect.top);\n  }\n\n  return utils.mix(pos, size);\n}\n\nexport default adjustForViewport;\n", "import utils from './utils';\n\nfunction getRegion(node) {\n  let offset;\n  let w;\n  let h;\n  if (!utils.isWindow(node) && node.nodeType !== 9) {\n    offset = utils.offset(node);\n    w = utils.outerWidth(node);\n    h = utils.outerHeight(node);\n  } else {\n    const win = utils.getWindow(node);\n    offset = {\n      left: utils.getWindowScrollLeft(win),\n      top: utils.getWindowScrollTop(win),\n    };\n    w = utils.viewportWidth(win);\n    h = utils.viewportHeight(win);\n  }\n  offset.width = w;\n  offset.height = h;\n  return offset;\n}\n\nexport default getRegion;\n", "/**\n * 获取 node 上的 align 对齐点 相对于页面的坐标\n */\n\nfunction getAlignOffset(region, align) {\n  const V = align.charAt(0);\n  const H = align.charAt(1);\n  const w = region.width;\n  const h = region.height;\n\n  let x = region.left;\n  let y = region.top;\n\n  if (V === 'c') {\n    y += h / 2;\n  } else if (V === 'b') {\n    y += h;\n  }\n\n  if (H === 'c') {\n    x += w / 2;\n  } else if (H === 'r') {\n    x += w;\n  }\n\n  return {\n    left: x,\n    top: y,\n  };\n}\n\nexport default getAlignOffset;\n", "import getAlignOffset from './getAlignOffset';\n\nfunction getElFuturePos(elRegion, refNodeRegion, points, offset, targetOffset) {\n  const p1 = getAlignOffset(refNodeRegion, points[1]);\n  const p2 = getAlignOffset(elRegion, points[0]);\n  const diff = [p2.left - p1.left, p2.top - p1.top];\n\n  return {\n    left: Math.round(elRegion.left - diff[0] + offset[0] - targetOffset[0]),\n    top: Math.round(elRegion.top - diff[1] + offset[1] - targetOffset[1]),\n  };\n}\n\nexport default getElFuturePos;\n", "/**\n * align dom node flexibly\n * <AUTHOR>\n */\n\nimport utils from '../utils';\nimport getVisibleRectForElement from '../getVisibleRectForElement';\nimport adjustForViewport from '../adjustForViewport';\nimport getRegion from '../getRegion';\nimport getElFuturePos from '../getElFuturePos';\n\n// http://yiminghe.iteye.com/blog/1124720\n\nfunction isFailX(elFuturePos, elRegion, visibleRect) {\n  return (\n    elFuturePos.left < visibleRect.left ||\n    elFuturePos.left + elRegion.width > visibleRect.right\n  );\n}\n\nfunction isFailY(elFuturePos, elRegion, visibleRect) {\n  return (\n    elFuturePos.top < visibleRect.top ||\n    elFuturePos.top + elRegion.height > visibleRect.bottom\n  );\n}\n\nfunction isCompleteFailX(elFuturePos, elRegion, visibleRect) {\n  return (\n    elFuturePos.left > visibleRect.right ||\n    elFuturePos.left + elRegion.width < visibleRect.left\n  );\n}\n\nfunction isCompleteFailY(elFuturePos, elRegion, visibleRect) {\n  return (\n    elFuturePos.top > visibleRect.bottom ||\n    elFuturePos.top + elRegion.height < visibleRect.top\n  );\n}\n\nfunction flip(points, reg, map) {\n  const ret = [];\n  utils.each(points, p => {\n    ret.push(\n      p.replace(reg, m => {\n        return map[m];\n      }),\n    );\n  });\n  return ret;\n}\n\nfunction flipOffset(offset, index) {\n  offset[index] = -offset[index];\n  return offset;\n}\n\nfunction convertOffset(str, offsetLen) {\n  let n;\n  if (/%$/.test(str)) {\n    n = (parseInt(str.substring(0, str.length - 1), 10) / 100) * offsetLen;\n  } else {\n    n = parseInt(str, 10);\n  }\n  return n || 0;\n}\n\nfunction normalizeOffset(offset, el) {\n  offset[0] = convertOffset(offset[0], el.width);\n  offset[1] = convertOffset(offset[1], el.height);\n}\n\n/**\n * @param el\n * @param tgtRegion 参照节点所占的区域: { left, top, width, height }\n * @param align\n */\nfunction doAlign(el, tgtRegion, align, isTgtRegionVisible) {\n  let points = align.points;\n  let offset = align.offset || [0, 0];\n  let targetOffset = align.targetOffset || [0, 0];\n  let overflow = align.overflow;\n  const source = align.source || el;\n  offset = [].concat(offset);\n  targetOffset = [].concat(targetOffset);\n  overflow = overflow || {};\n  const newOverflowCfg = {};\n  let fail = 0;\n  const alwaysByViewport = !!(overflow && overflow.alwaysByViewport);\n  // 当前节点可以被放置的显示区域\n  const visibleRect = getVisibleRectForElement(source, alwaysByViewport);\n  // 当前节点所占的区域, left/top/width/height\n  const elRegion = getRegion(source);\n  // 将 offset 转换成数值，支持百分比\n  normalizeOffset(offset, elRegion);\n  normalizeOffset(targetOffset, tgtRegion);\n  // 当前节点将要被放置的位置\n  let elFuturePos = getElFuturePos(\n    elRegion,\n    tgtRegion,\n    points,\n    offset,\n    targetOffset,\n  );\n  // 当前节点将要所处的区域\n  let newElRegion = utils.merge(elRegion, elFuturePos);\n\n  // 如果可视区域不能完全放置当前节点时允许调整\n  if (\n    visibleRect &&\n    (overflow.adjustX || overflow.adjustY) &&\n    isTgtRegionVisible\n  ) {\n    if (overflow.adjustX) {\n      // 如果横向不能放下\n      if (isFailX(elFuturePos, elRegion, visibleRect)) {\n        // 对齐位置反下\n        const newPoints = flip(points, /[lr]/gi, {\n          l: 'r',\n          r: 'l',\n        });\n        // 偏移量也反下\n        const newOffset = flipOffset(offset, 0);\n        const newTargetOffset = flipOffset(targetOffset, 0);\n        const newElFuturePos = getElFuturePos(\n          elRegion,\n          tgtRegion,\n          newPoints,\n          newOffset,\n          newTargetOffset,\n        );\n\n        if (!isCompleteFailX(newElFuturePos, elRegion, visibleRect)) {\n          fail = 1;\n          points = newPoints;\n          offset = newOffset;\n          targetOffset = newTargetOffset;\n        }\n      }\n    }\n\n    if (overflow.adjustY) {\n      // 如果纵向不能放下\n      if (isFailY(elFuturePos, elRegion, visibleRect)) {\n        // 对齐位置反下\n        const newPoints = flip(points, /[tb]/gi, {\n          t: 'b',\n          b: 't',\n        });\n        // 偏移量也反下\n        const newOffset = flipOffset(offset, 1);\n        const newTargetOffset = flipOffset(targetOffset, 1);\n        const newElFuturePos = getElFuturePos(\n          elRegion,\n          tgtRegion,\n          newPoints,\n          newOffset,\n          newTargetOffset,\n        );\n\n        if (!isCompleteFailY(newElFuturePos, elRegion, visibleRect)) {\n          fail = 1;\n          points = newPoints;\n          offset = newOffset;\n          targetOffset = newTargetOffset;\n        }\n      }\n    }\n\n    // 如果失败，重新计算当前节点将要被放置的位置\n    if (fail) {\n      elFuturePos = getElFuturePos(\n        elRegion,\n        tgtRegion,\n        points,\n        offset,\n        targetOffset,\n      );\n      utils.mix(newElRegion, elFuturePos);\n    }\n    const isStillFailX = isFailX(elFuturePos, elRegion, visibleRect);\n    const isStillFailY = isFailY(elFuturePos, elRegion, visibleRect);\n    // 检查反下后的位置是否可以放下了，如果仍然放不下：\n    // 1. 复原修改过的定位参数\n    if (isStillFailX || isStillFailY) {\n      let newPoints = points;\n\n      // 重置对应部分的翻转逻辑\n      if (isStillFailX) {\n        newPoints = flip(points, /[lr]/gi, {\n          l: 'r',\n          r: 'l',\n        });\n      }\n      if (isStillFailY) {\n        newPoints = flip(points, /[tb]/gi, {\n          t: 'b',\n          b: 't',\n        });\n      }\n\n      points = newPoints;\n\n      offset = align.offset || [0, 0];\n      targetOffset = align.targetOffset || [0, 0];\n    }\n    // 2. 只有指定了可以调整当前方向才调整\n    newOverflowCfg.adjustX = overflow.adjustX && isStillFailX;\n    newOverflowCfg.adjustY = overflow.adjustY && isStillFailY;\n\n    // 确实要调整，甚至可能会调整高度宽度\n    if (newOverflowCfg.adjustX || newOverflowCfg.adjustY) {\n      newElRegion = adjustForViewport(\n        elFuturePos,\n        elRegion,\n        visibleRect,\n        newOverflowCfg,\n      );\n    }\n  }\n\n  // need judge to in case set fixed with in css on height auto element\n  if (newElRegion.width !== elRegion.width) {\n    utils.css(\n      source,\n      'width',\n      utils.width(source) + newElRegion.width - elRegion.width,\n    );\n  }\n\n  if (newElRegion.height !== elRegion.height) {\n    utils.css(\n      source,\n      'height',\n      utils.height(source) + newElRegion.height - elRegion.height,\n    );\n  }\n\n  // https://github.com/kissyteam/kissy/issues/190\n  // 相对于屏幕位置没变，而 left/top 变了\n  // 例如 <div 'relative'><el absolute></div>\n  utils.offset(\n    source,\n    {\n      left: newElRegion.left,\n      top: newElRegion.top,\n    },\n    {\n      useCssRight: align.useCssRight,\n      useCssBottom: align.useCssBottom,\n      useCssTransform: align.useCssTransform,\n      ignoreShake: align.ignoreShake,\n    },\n  );\n\n  return {\n    points,\n    offset,\n    targetOffset,\n    overflow: newOverflowCfg,\n  };\n}\n\nexport default doAlign;\n/**\n *  2012-04-26 <EMAIL>\n *   - 优化智能对齐算法\n *   - 慎用 resizeXX\n *\n *  2011-07-13 <EMAIL> note:\n *   - 增加智能对齐，以及大小调整选项\n **/\n", "import doAlign from './align';\nimport getOffsetParent from '../getOffsetParent';\nimport getVisibleRectForElement from '../getVisibleRectForElement';\nimport getRegion from '../getRegion';\n\nfunction isOutOfVisibleRect(target, alwaysByViewport) {\n  const visibleRect = getVisibleRectForElement(target, alwaysByViewport);\n  const targetRegion = getRegion(target);\n\n  return (\n    !visibleRect ||\n    targetRegion.left + targetRegion.width <= visibleRect.left ||\n    targetRegion.top + targetRegion.height <= visibleRect.top ||\n    targetRegion.left >= visibleRect.right ||\n    targetRegion.top >= visibleRect.bottom\n  );\n}\n\nfunction alignElement(el, refNode, align) {\n  const target = align.target || refNode;\n  const refNodeRegion = getRegion(target);\n\n  const isTargetNotOutOfVisible = !isOutOfVisibleRect(\n    target,\n    align.overflow && align.overflow.alwaysByViewport,\n  );\n\n  return doAlign(el, refNodeRegion, align, isTargetNotOutOfVisible);\n}\n\nalignElement.__getOffsetParent = getOffsetParent;\n\nalignElement.__getVisibleRectForElement = getVisibleRectForElement;\n\nexport default alignElement;\n", "import utils from '../utils';\nimport doAlign from './align';\n\n/**\n * `tgtPoint`: { pageX, pageY } or { clientX, clientY }.\n * If client position provided, will internal convert to page position.\n */\n\nfunction alignPoint(el, tgtPoint, align) {\n  let pageX;\n  let pageY;\n\n  const doc = utils.getDocument(el);\n  const win = doc.defaultView || doc.parentWindow;\n\n  const scrollX = utils.getWindowScrollLeft(win);\n  const scrollY = utils.getWindowScrollTop(win);\n  const viewportWidth = utils.viewportWidth(win);\n  const viewportHeight = utils.viewportHeight(win);\n\n  if ('pageX' in tgtPoint) {\n    pageX = tgtPoint.pageX;\n  } else {\n    pageX = scrollX + tgtPoint.clientX;\n  }\n\n  if ('pageY' in tgtPoint) {\n    pageY = tgtPoint.pageY;\n  } else {\n    pageY = scrollY + tgtPoint.clientY;\n  }\n\n  const tgtRegion = {\n    left: pageX,\n    top: pageY,\n    width: 0,\n    height: 0,\n  };\n\n  const pointInView =\n    pageX >= 0 &&\n    pageX <= scrollX + viewportWidth &&\n    (pageY >= 0 && pageY <= scrollY + viewportHeight);\n\n  // Provide default target point\n  const points = [align.points[0], 'cc'];\n\n  return doAlign(el, tgtRegion, { ...align, points }, pointInView);\n}\n\nexport default alignPoint;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAIA;AAEJ,IAAMC,WAAW;EACfC,QAAQ;EACRC,KAAK;;EAELC,IAAI;EACJC,GAAG;AACL;AAEA,SAASC,kBAAkB;AACzB,MAAIN,iBAAiBO,QAAW;AAC9B,WAAOP;;AAETA,iBAAe;AACf,MAAMQ,QAAQC,SAASC,cAAc,GAAG,EAAEF;AAC1C,MAAMG,WAAW;AACjB,WAAWC,OAAOX,UAAU;AAC1B,QAAIW,MAAMD,YAAYH,OAAO;AAC3BR,qBAAeY;;;AAGnB,SAAOZ;AACT;AAEA,SAASa,oBAAoB;AAC3B,SAAOP,gBAAe,IAAE,GAAA,OACjBA,gBAAe,GAAE,oBAAA,IACpB;AACN;AAEO,SAASQ,mBAAmB;AACjC,SAAOR,gBAAe,IAAE,GAAA,OAAMA,gBAAe,GAAE,WAAA,IAAc;AAC/D;AAEO,SAASS,sBAAsBC,MAAMC,OAAO;AACjD,MAAMC,OAAOL,kBAAiB;AAC9B,MAAIK,MAAM;AACRF,SAAKR,MAAMU,IAAI,IAAID;AACnB,QAAIC,SAAS,sBAAsB;AACjCF,WAAKR,MAAMW,qBAAqBF;;;AAGtC;AAEA,SAASG,aAAaJ,MAAMC,OAAO;AACjC,MAAMC,OAAOJ,iBAAgB;AAC7B,MAAII,MAAM;AACRF,SAAKR,MAAMU,IAAI,IAAID;AACnB,QAAIC,SAAS,aAAa;AACxBF,WAAKR,MAAMa,YAAYJ;;;AAG7B;AAEO,SAASK,sBAAsBN,MAAM;AAC1C,SAAOA,KAAKR,MAAMW,sBAAsBH,KAAKR,MAAMK,kBAAiB,CAAE;AACxE;AAEO,SAASU,eAAeP,MAAM;AACnC,MAAMR,QAAQgB,OAAOC,iBAAiBT,MAAM,IAAI;AAChD,MAAMK,YACJb,MAAMkB,iBAAiB,WAAW,KAClClB,MAAMkB,iBAAiBZ,iBAAgB,CAAE;AAC3C,MAAIO,aAAaA,cAAc,QAAQ;AACrC,QAAMM,SAASN,UAAUO,QAAQ,eAAe,EAAE,EAAEC,MAAM,GAAG;AAC7D,WAAO;MACLC,GAAGC,WAAWJ,OAAO,EAAE,KAAKA,OAAO,CAAC,GAAG,CAAC;MACxCK,GAAGD,WAAWJ,OAAO,EAAE,KAAKA,OAAO,CAAC,GAAG,CAAC;;;AAG5C,SAAO;IACLG,GAAG;IACHE,GAAG;;AAEP;AAEA,IAAMC,WAAW;AACjB,IAAMC,WAAW;AAEV,SAASC,eAAenB,MAAMoB,IAAI;AACvC,MAAM5B,QAAQgB,OAAOC,iBAAiBT,MAAM,IAAI;AAChD,MAAMK,YACJb,MAAMkB,iBAAiB,WAAW,KAClClB,MAAMkB,iBAAiBZ,iBAAgB,CAAE;AAC3C,MAAIO,aAAaA,cAAc,QAAQ;AACrC,QAAIgB;AACJ,QAAIC,UAAUjB,UAAUkB,MAAMN,QAAQ;AACtC,QAAIK,SAAS;AACXA,gBAAUA,QAAQ,CAAC;AACnBD,YAAMC,QAAQT,MAAM,GAAG,EAAEW,IAAI,SAAAC,MAAQ;AACnC,eAAOV,WAAWU,MAAM,EAAE;OAC3B;AACDJ,UAAI,CAAC,IAAID,GAAGN;AACZO,UAAI,CAAC,IAAID,GAAGJ;AACZZ,mBAAaJ,MAAI,UAAA,OAAYqB,IAAIK,KAAK,GAAG,GAAC,GAAA,CAAA;WACrC;AACL,UAAMC,UAAUtB,UAAUkB,MAAML,QAAQ,EAAE,CAAC;AAC3CG,YAAMM,QAAQd,MAAM,GAAG,EAAEW,IAAI,SAAAC,MAAQ;AACnC,eAAOV,WAAWU,MAAM,EAAE;OAC3B;AACDJ,UAAI,EAAE,IAAID,GAAGN;AACbO,UAAI,EAAE,IAAID,GAAGJ;AACbZ,mBAAaJ,MAAI,YAAA,OAAcqB,IAAIK,KAAK,GAAG,GAAC,GAAA,CAAA;;SAEzC;AACLtB,iBACEJ,MAAI,cAAA,OACUoB,GAAGN,GAAC,iBAAA,EAAA,OAAkBM,GAAGJ,GAAC,mBAAA,CAAA;;AAG9C;ACvGA,IAAMY,SAAS,wCAAwCC;AAEvD,IAAIC;AAGJ,SAASC,cAAcC,MAAM;AAC3B,MAAMC,gBAAgBD,KAAKxC,MAAM0C;AACjCF,OAAKxC,MAAM0C,UAAU;AACrBF,OAAKG;AACLH,OAAKxC,MAAM0C,UAAUD;AACvB;AAEA,SAASG,IAAIC,IAAInC,MAAMoC,GAAG;AACxB,MAAIrC,QAAQqC;AACZ,MAAI,QAAOpC,IAAI,MAAK,UAAU;AAC5B,aAAWqC,KAAKrC,MAAM;AACpB,UAAIA,KAAKsC,eAAeD,CAAC,GAAG;AAC1BH,YAAIC,IAAIE,GAAGrC,KAAKqC,CAAC,CAAC;;;AAGtB,WAAOhD;;AAET,MAAI,OAAOU,UAAU,aAAa;AAChC,QAAI,OAAOA,UAAU,UAAU;AAC7BA,cAAK,GAAA,OAAMA,OAAK,IAAA;;AAElBoC,OAAG7C,MAAMU,IAAI,IAAID;AACjB,WAAOV;;AAET,SAAOuC,kBAAkBO,IAAInC,IAAI;AACnC;AAEA,SAASuC,kBAAkBT,MAAM;AAC/B,MAAIU;AACJ,MAAI5B;AACJ,MAAIE;AACJ,MAAM2B,MAAMX,KAAKY;AACjB,MAAMC,OAAOF,IAAIE;AACjB,MAAMC,UAAUH,OAAOA,IAAII;AAE3BL,QAAMV,KAAKgB,sBAAqB;AAMhClC,MAAImC,KAAKC,MAAMR,IAAIS,IAAI;AACvBnC,MAAIiC,KAAKC,MAAMR,IAAIU,GAAG;AAsBtBtC,OAAKgC,QAAQO,cAAcR,KAAKQ,cAAc;AAC9CrC,OAAK8B,QAAQQ,aAAaT,KAAKS,aAAa;AAE5C,SAAO;IACLH,MAAMrC;IACNsC,KAAKpC;;AAET;AAEA,SAASuC,UAAUC,GAAGJ,KAAK;AACzB,MAAIK,MAAMD,EAAC,OAAA,OAAQJ,MAAM,MAAM,KAAG,QAAA,CAAA;AAClC,MAAMM,SAAM,SAAA,OAAYN,MAAM,QAAQ,MAAM;AAC5C,MAAI,OAAOK,QAAQ,UAAU;AAC3B,QAAME,IAAIH,EAAE/D;AAEZgE,UAAME,EAAEZ,gBAAgBW,MAAM;AAC9B,QAAI,OAAOD,QAAQ,UAAU;AAE3BA,YAAME,EAAEd,KAAKa,MAAM;;;AAGvB,SAAOD;AACT;AAEA,SAASG,cAAcJ,GAAG;AACxB,SAAOD,UAAUC,CAAC;AACpB;AAEA,SAASK,aAAaL,GAAG;AACvB,SAAOD,UAAUC,GAAG,IAAI;AAC1B;AAEA,SAASM,UAAUzB,IAAI;AACrB,MAAM0B,MAAMtB,kBAAkBJ,EAAE;AAChC,MAAMM,MAAMN,GAAGO;AACf,MAAMY,IAAIb,IAAIqB,eAAerB,IAAIsB;AACjCF,MAAIZ,QAAQS,cAAcJ,CAAC;AAC3BO,MAAIX,OAAOS,aAAaL,CAAC;AACzB,SAAOO;AACT;AAMA,SAASG,SAASC,KAAK;AAGrB,SAAOA,QAAQ,QAAQA,QAAQ5E,UAAa4E,OAAOA,IAAI3D;AACzD;AAEA,SAAS4D,YAAYpE,MAAM;AACzB,MAAIkE,SAASlE,IAAI,GAAG;AAClB,WAAOA,KAAKP;;AAEd,MAAIO,KAAKqE,aAAa,GAAG;AACvB,WAAOrE;;AAET,SAAOA,KAAK4C;AACd;AAEA,SAAS0B,kBAAkBtC,MAAM9B,MAAMqE,IAAI;AACzC,MAAIC,gBAAgBD;AACpB,MAAIE,MAAM;AACV,MAAMd,IAAIS,YAAYpC,IAAI;AAC1BwC,kBAAgBA,iBAAiBb,EAAEK,YAAYvD,iBAAiBuB,MAAM,IAAI;AAG1E,MAAIwC,eAAe;AACjBC,UAAMD,cAAc9D,iBAAiBR,IAAI,KAAKsE,cAActE,IAAI;;AAGlE,SAAOuE;AACT;AAEA,IAAMC,gBAAgB,IAAIC,OAAM,KAAA,OAAM/C,QAAM,iBAAA,GAAmB,GAAG;AAClE,IAAMgD,SAAS;AACf,IAAMC,gBAAgB;AACtB,IAAMC,gBAAgB;AACtB,IAAMC,OAAO;AACb,IAAMC,KAAK;AAEX,SAASC,oBAAoBjD,MAAM9B,MAAM;AAGvC,MAAIuD,MAAMzB,KAAK6C,aAAa,KAAK7C,KAAK6C,aAAa,EAAE3E,IAAI;AAYzD,MAAIwE,cAAcQ,KAAKzB,GAAG,KAAK,CAACmB,OAAOM,KAAKhF,IAAI,GAAG;AAEjD,QAAMV,QAAQwC,KAAKxC;AACnB,QAAM2D,OAAO3D,MAAMuF,IAAI;AACvB,QAAMI,SAASnD,KAAK8C,aAAa,EAAEC,IAAI;AAGvC/C,SAAK8C,aAAa,EAAEC,IAAI,IAAI/C,KAAK6C,aAAa,EAAEE,IAAI;AAGpDvF,UAAMuF,IAAI,IAAI7E,SAAS,aAAa,QAAQuD,OAAO;AACnDA,UAAMjE,MAAM4F,YAAYJ;AAGxBxF,UAAMuF,IAAI,IAAI5B;AAEdnB,SAAK8C,aAAa,EAAEC,IAAI,IAAII;;AAE9B,SAAO1B,QAAQ,KAAK,SAASA;AAC/B;AAEA,IAAI,OAAOjD,WAAW,aAAa;AACjCsB,sBAAoBtB,OAAOC,mBACvB6D,oBACAW;AACN;AAEA,SAASI,mBAAmBC,KAAKC,QAAQ;AACvC,MAAID,QAAQ,QAAQ;AAClB,WAAOC,OAAOC,cAAc,UAAUF;;AAExC,SAAOC,OAAOE,eAAe,WAAWH;AAC1C;AAEA,SAASI,wBAAwBJ,KAAK;AACpC,MAAIA,QAAQ,QAAQ;AAClB,WAAO;aACEA,QAAQ,SAAS;AAC1B,WAAO;aACEA,QAAQ,OAAO;AACxB,WAAO;aACEA,QAAQ,UAAU;AAC3B,WAAO;;AAEX;AAGA,SAASK,WAAW3D,MAAM4D,SAAQL,QAAQ;AAExC,MAAInD,IAAIJ,MAAM,UAAU,MAAM,UAAU;AACtCA,SAAKxC,MAAMqG,WAAW;;AAExB,MAAIC,UAAU;AACd,MAAIC,UAAU;AACd,MAAMC,qBAAqBX,mBAAmB,QAAQE,MAAM;AAC5D,MAAMU,mBAAmBZ,mBAAmB,OAAOE,MAAM;AACzD,MAAMW,6BAA6BR,wBACjCM,kBAAkB;AAEpB,MAAMG,2BAA2BT,wBAAwBO,gBAAgB;AAEzE,MAAID,uBAAuB,QAAQ;AACjCF,cAAU;;AAGZ,MAAIG,qBAAqB,OAAO;AAC9BF,cAAU;;AAEZ,MAAIK,qBAAqB;AACzB,MAAMC,iBAAiBvC,UAAU9B,IAAI;AACrC,MAAI,UAAU4D,WAAU,SAASA,SAAQ;AACvCQ,yBAAqB9F,sBAAsB0B,IAAI,KAAK;AACpDjC,0BAAsBiC,MAAM,MAAM;;AAEpC,MAAI,UAAU4D,SAAQ;AACpB5D,SAAKxC,MAAM0G,0BAA0B,IAAI;AACzClE,SAAKxC,MAAMwG,kBAAkB,IAAC,GAAA,OAAMF,SAAO,IAAA;;AAE7C,MAAI,SAASF,SAAQ;AACnB5D,SAAKxC,MAAM2G,wBAAwB,IAAI;AACvCnE,SAAKxC,MAAMyG,gBAAgB,IAAC,GAAA,OAAMF,SAAO,IAAA;;AAG3ChE,gBAAcC,IAAI;AAClB,MAAMsE,MAAMxC,UAAU9B,IAAI;AAC1B,MAAMC,gBAAgB,CAAA;AACtB,WAAWrC,OAAOgG,SAAQ;AACxB,QAAIA,QAAOpD,eAAe5C,GAAG,GAAG;AAC9B,UAAM0F,MAAMD,mBAAmBzF,KAAK2F,MAAM;AAC1C,UAAMgB,SAAS3G,QAAQ,SAASkG,UAAUC;AAC1C,UAAMS,MAAMH,eAAezG,GAAG,IAAI0G,IAAI1G,GAAG;AACzC,UAAI0F,QAAQ1F,KAAK;AACfqC,sBAAcqD,GAAG,IAAIiB,SAASC;aACzB;AACLvE,sBAAcqD,GAAG,IAAIiB,SAASC;;;;AAIpCpE,MAAIJ,MAAMC,aAAa;AAEvBF,gBAAcC,IAAI;AAClB,MAAI,UAAU4D,WAAU,SAASA,SAAQ;AACvC7F,0BAAsBiC,MAAMoE,kBAAkB;;AAEhD,MAAM3C,MAAM,CAAA;AACZ,WAAW7D,QAAOgG,SAAQ;AACxB,QAAIA,QAAOpD,eAAe5C,IAAG,GAAG;AAC9B,UAAM0F,OAAMD,mBAAmBzF,MAAK2F,MAAM;AAC1C,UAAMiB,OAAMZ,QAAOhG,IAAG,IAAIyG,eAAezG,IAAG;AAC5C,UAAIA,SAAQ0F,MAAK;AACf7B,YAAI6B,IAAG,IAAIrD,cAAcqD,IAAG,IAAIkB;aAC3B;AACL/C,YAAI6B,IAAG,IAAIrD,cAAcqD,IAAG,IAAIkB;;;;AAItCpE,MAAIJ,MAAMyB,GAAG;AACf;AAEA,SAASrD,eAAa4B,MAAM4D,SAAQ;AAClC,MAAMS,iBAAiBvC,UAAU9B,IAAI;AACrC,MAAMyE,aAAalG,eAAeyB,IAAI;AACtC,MAAM0E,WAAW;IAAE5F,GAAG2F,WAAW3F;IAAGE,GAAGyF,WAAWzF;;AAClD,MAAI,UAAU4E,SAAQ;AACpBc,aAAS5F,IAAI2F,WAAW3F,IAAI8E,QAAOzC,OAAOkD,eAAelD;;AAE3D,MAAI,SAASyC,SAAQ;AACnBc,aAAS1F,IAAIyF,WAAWzF,IAAI4E,QAAOxC,MAAMiD,eAAejD;;AAE1DjC,iBAAea,MAAM0E,QAAQ;AAC/B;AAEA,SAASC,UAAU3E,MAAM4D,SAAQL,QAAQ;AACvC,MAAIA,OAAOqB,aAAa;AACtB,QAAMC,YAAY/C,UAAU9B,IAAI;AAEhC,QAAM8E,QAAQD,UAAU1D,KAAK4D,QAAQ,CAAC;AACtC,QAAMC,OAAOH,UAAUzD,IAAI2D,QAAQ,CAAC;AACpC,QAAME,QAAQrB,QAAOzC,KAAK4D,QAAQ,CAAC;AACnC,QAAMG,OAAOtB,QAAOxC,IAAI2D,QAAQ,CAAC;AAEjC,QAAID,UAAUG,SAASD,SAASE,MAAM;AACpC;;;AAIJ,MAAI3B,OAAOC,eAAeD,OAAOE,cAAc;AAC7CE,eAAW3D,MAAM4D,SAAQL,MAAM;aAE/BA,OAAO4B,mBACPrH,iBAAgB,KAAML,SAASoD,KAAKrD,OACpC;AACAY,mBAAa4B,MAAM4D,OAAc;SAC5B;AACLD,eAAW3D,MAAM4D,SAAQL,MAAM;;AAEnC;AAEA,SAAS6B,KAAK/F,KAAKgG,IAAI;AACrB,WAAS9E,IAAI,GAAGA,IAAIlB,IAAIiG,QAAQ/E,KAAK;AACnC8E,OAAGhG,IAAIkB,CAAC,CAAC;;AAEb;AAEA,SAASgF,cAAcvF,MAAM;AAC3B,SAAOF,kBAAkBE,MAAM,WAAW,MAAM;AAClD;AAEA,IAAMwF,aAAa,CAAC,UAAU,UAAU,SAAS;AACjD,IAAMC,gBAAgB;AACtB,IAAMC,gBAAgB;AACtB,IAAMC,eAAe;AACrB,IAAMC,eAAe;AAErB,SAASC,KAAK7F,MAAM8F,SAASC,UAAU;AACrC,MAAMzB,MAAM,CAAA;AACZ,MAAM9G,QAAQwC,KAAKxC;AACnB,MAAIU;AAGJ,OAAKA,QAAQ4H,SAAS;AACpB,QAAIA,QAAQtF,eAAetC,IAAI,GAAG;AAChCoG,UAAIpG,IAAI,IAAIV,MAAMU,IAAI;AACtBV,YAAMU,IAAI,IAAI4H,QAAQ5H,IAAI;;;AAI9B6H,WAASC,KAAKhG,IAAI;AAGlB,OAAK9B,QAAQ4H,SAAS;AACpB,QAAIA,QAAQtF,eAAetC,IAAI,GAAG;AAChCV,YAAMU,IAAI,IAAIoG,IAAIpG,IAAI;;;AAG5B;AAEA,SAAS+H,YAAYjG,MAAMkG,OAAOC,OAAO;AACvC,MAAIlI,QAAQ;AACZ,MAAImI;AACJ,MAAIC;AACJ,MAAI9F;AACJ,OAAK8F,IAAI,GAAGA,IAAIH,MAAMZ,QAAQe,KAAK;AACjCD,WAAOF,MAAMG,CAAC;AACd,QAAID,MAAM;AACR,WAAK7F,IAAI,GAAGA,IAAI4F,MAAMb,QAAQ/E,KAAK;AACjC,YAAI+F,UAAO;AACX,YAAIF,SAAS,UAAU;AACrBE,oBAAO,GAAA,OAAMF,IAAI,EAAA,OAAGD,MAAM5F,CAAC,GAAC,OAAA;eACvB;AACL+F,oBAAUF,OAAOD,MAAM5F,CAAC;;AAE1BtC,iBAASc,WAAWe,kBAAkBE,MAAMsG,OAAO,CAAC,KAAK;;;;AAI/D,SAAOrI;AACT;AAEA,IAAMsI,WAAW;EACfC,WAAS,SAAA,UAACC,SAAS;AACjB,QAAIC,SAASD;AACb,OAAG;AACD,UAAIC,OAAOrE,aAAa,MAAMqE,OAAOC,MAAM;AACzCD,iBAASA,OAAOC;aACX;AACLD,iBAASA,OAAOE;;aAEXF,UAAUA,OAAOrE,aAAa,KAAKqE,OAAOrE,aAAa;AAChE,WAAOqE;;AAEX;AAEAtB,KAAK,CAAC,SAAS,QAAQ,GAAG,SAAAlH,MAAQ;AAChCqI,WAAQ,MAAA,OAAOrI,IAAI,CAAA,IAAM,SAAA2I,QAAU;AACjC,QAAMlF,IAAIkF,OAAOpJ;AACjB,WAAOwD,KAAK6F;;;MAGVnF,EAAEZ,gBAAe,SAAA,OAAU7C,IAAI,CAAA;;MAE/ByD,EAAEd,KAAI,SAAA,OAAU3C,IAAI,CAAA;MACpBqI,SAAQ,WAAA,OAAYrI,IAAI,CAAA,EAAIyD,CAAC;IAAC;;AAIlC4E,WAAQ,WAAA,OAAYrI,IAAI,CAAA,IAAM,SAAA6I,KAAO;AAEnC,QAAMX,OAAI,SAAA,OAAYlI,IAAI;AAC1B,QAAMyC,MAAMoG,IAAItJ;AAChB,QAAMoD,OAAOF,IAAIE;AACjB,QAAME,kBAAkBJ,IAAII;AAC5B,QAAMiG,sBAAsBjG,gBAAgBqF,IAAI;AAGhD,WACGzF,IAAIsG,eAAe,gBAAgBD,uBACnCnG,QAAQA,KAAKuF,IAAI,KAClBY;;AAGN,CAAC;AAUD,SAASE,MAAMlH,MAAM9B,MAAMiJ,IAAI;AAC7B,MAAIC,QAAQD;AACZ,MAAIjF,SAASlC,IAAI,GAAG;AAClB,WAAO9B,SAAS,UACZqI,SAASc,cAAcrH,IAAI,IAC3BuG,SAASe,eAAetH,IAAI;aACvBA,KAAKqC,aAAa,GAAG;AAC9B,WAAOnE,SAAS,UACZqI,SAASgB,SAASvH,IAAI,IACtBuG,SAASiB,UAAUxH,IAAI;;AAE7B,MAAMmG,QAAQjI,SAAS,UAAU,CAAC,QAAQ,OAAO,IAAI,CAAC,OAAO,QAAQ;AACrE,MAAIuJ,iBACFvJ,SAAS,UACL+C,KAAKC,MAAMlB,KAAKgB,sBAAqB,EAAG0G,KAAK,IAC7CzG,KAAKC,MAAMlB,KAAKgB,sBAAqB,EAAG2G,MAAM;AACpD,MAAMC,cAAcrC,cAAcvF,IAAI;AACtC,MAAI6H,cAAc;AAClB,MACEJ,mBAAmB,QACnBA,mBAAmBlK,UACnBkK,kBAAkB,GAClB;AACAA,qBAAiBlK;AAEjBsK,kBAAc/H,kBAAkBE,MAAM9B,IAAI;AAC1C,QACE2J,gBAAgB,QAChBA,gBAAgBtK,UAChBuK,OAAOD,WAAW,IAAI,GACtB;AACAA,oBAAc7H,KAAKxC,MAAMU,IAAI,KAAK;;AAGpC2J,kBAAc5G,KAAKC,MAAMnC,WAAW8I,WAAW,CAAC,KAAK;;AAEvD,MAAIT,UAAU7J,QAAW;AACvB6J,YAAQQ,cAAcjC,eAAeF;;AAEvC,MAAMsC,8BACJN,mBAAmBlK,UAAaqK;AAClC,MAAMnF,MAAMgF,kBAAkBI;AAC9B,MAAIT,UAAU3B,eAAe;AAC3B,QAAIsC,6BAA6B;AAC/B,aAAOtF,MAAMwD,YAAYjG,MAAM,CAAC,UAAU,SAAS,GAAGmG,KAAK;;AAE7D,WAAO0B;aACEE,6BAA6B;AACtC,QAAIX,UAAUzB,cAAc;AAC1B,aAAOlD;;AAET,WACEA,OACC2E,UAAU1B,gBACP,CAACO,YAAYjG,MAAM,CAAC,QAAQ,GAAGmG,KAAK,IACpCF,YAAYjG,MAAM,CAAC,QAAQ,GAAGmG,KAAK;;AAG3C,SAAO0B,cAAc5B,YAAYjG,MAAMwF,WAAWwC,MAAMZ,KAAK,GAAGjB,KAAK;AACvE;AAEA,IAAM8B,UAAU;EACdpE,UAAU;EACVqE,YAAY;EACZhI,SAAS;AACX;AAGA,SAASiI,qBAA4B;AAAA,WAAA,OAAA,UAAA,QAANC,OAAI,IAAA,MAAA,IAAA,GAAA,QAAA,GAAA,QAAA,MAAA,SAAA;AAAJA,SAAI,KAAA,IAAA,UAAA,KAAA;;AACjC,MAAI3F;AACJ,MAAMzC,OAAOoI,KAAK,CAAC;AAGnB,MAAIpI,KAAKqI,gBAAgB,GAAG;AAC1B5F,UAAMyE,MAAMoB,MAAM/K,QAAW6K,IAAI;SAC5B;AACLvC,SAAK7F,MAAMiI,SAAS,WAAM;AACxBxF,YAAMyE,MAAMoB,MAAM/K,QAAW6K,IAAI;KAClC;;AAEH,SAAO3F;AACT;AAEA2C,KAAK,CAAC,SAAS,QAAQ,GAAG,SAAAlH,MAAQ;AAChC,MAAMqK,QAAQrK,KAAKsK,OAAO,CAAC,EAAEC,YAAW,IAAKvK,KAAK8J,MAAM,CAAC;AACzDzB,WAAQ,QAAA,OAASgC,KAAK,CAAA,IAAM,SAAClI,IAAIqI,eAAkB;AACjD,WACErI,MACA8H,mBAAmB9H,IAAInC,MAAMwK,gBAAgB9C,eAAeD,YAAY;;AAG5E,MAAMQ,QAAQjI,SAAS,UAAU,CAAC,QAAQ,OAAO,IAAI,CAAC,OAAO,QAAQ;AAErEqI,WAASrI,IAAI,IAAI,SAAC8B,MAAMM,GAAM;AAC5B,QAAImC,MAAMnC;AACV,QAAImC,QAAQlF,QAAW;AACrB,UAAIyC,MAAM;AACR,YAAM4H,cAAcrC,cAAcvF,IAAI;AACtC,YAAI4H,aAAa;AACfnF,iBAAOwD,YAAYjG,MAAM,CAAC,WAAW,QAAQ,GAAGmG,KAAK;;AAEvD,eAAO/F,IAAIJ,MAAM9B,MAAMuE,GAAG;;AAE5B,aAAOlF;;AAET,WAAOyC,QAAQmI,mBAAmBnI,MAAM9B,MAAMuH,aAAa;;AAE/D,CAAC;AAED,SAASkD,IAAIC,IAAIC,MAAM;AACrB,WAAWtI,KAAKsI,MAAM;AACpB,QAAIA,KAAKrI,eAAeD,CAAC,GAAG;AAC1BqI,SAAGrI,CAAC,IAAIsI,KAAKtI,CAAC;;;AAGlB,SAAOqI;AACT;AAEA,IAAME,QAAQ;EACZC,WAAS,SAAA,UAAC/K,MAAM;AACd,QAAIA,QAAQA,KAAKP,YAAYO,KAAKgL,YAAY;AAC5C,aAAOhL;;AAET,QAAM2C,MAAM3C,KAAK4C,iBAAiB5C;AAClC,WAAO2C,IAAIqB,eAAerB,IAAIsB;;EAEhCG;EACAwB,QAAM,SAAA,OAACvD,IAAIpC,OAAOsF,QAAQ;AACxB,QAAI,OAAOtF,UAAU,aAAa;AAChC0G,gBAAUtE,IAAIpC,OAAOsF,UAAU,CAAA,CAAE;WAC5B;AACL,aAAOzB,UAAUzB,EAAE;;;EAGvB6B;EACAkD;EACAhF;EACA6I,OAAK,SAAA,MAAC9G,KAAK;AACT,QAAI5B;AACJ,QAAMkB,MAAM,CAAA;AACZ,SAAKlB,KAAK4B,KAAK;AACb,UAAIA,IAAI3B,eAAeD,CAAC,GAAG;AACzBkB,YAAIlB,CAAC,IAAI4B,IAAI5B,CAAC;;;AAGlB,QAAM2I,WAAW/G,IAAI+G;AACrB,QAAIA,UAAU;AACZ,WAAK3I,KAAK4B,KAAK;AACb,YAAIA,IAAI3B,eAAeD,CAAC,GAAG;AACzBkB,cAAIyH,SAAS3I,CAAC,IAAI4B,IAAI+G,SAAS3I,CAAC;;;;AAItC,WAAOkB;;EAETkH;EACAQ,qBAAmB,SAAA,oBAAC3H,GAAG;AACrB,WAAOI,cAAcJ,CAAC;;EAExB4H,oBAAkB,SAAA,mBAAC5H,GAAG;AACpB,WAAOK,aAAaL,CAAC;;EAEvB6H,OAAK,SAAA,QAAU;AACb,QAAM5H,MAAM,CAAA;AACZ,aAASlB,IAAI,GAAGA,IAAI,UAAK+E,QAAQ/E,KAAK;AACpCuI,YAAMH,IAAIlH,KAAUlB,IAAC,KAAA,UAAA,UAADA,IAAC,SAAA,UAADA,CAAC,CAAA;;AAEvB,WAAOkB;;EAET4F,eAAe;EACfC,gBAAgB;AAClB;AAEAqB,IAAIG,OAAOvC,QAAQ;ACrmBnB,IAAQC,aAAcsC,MAAdtC;AAER,SAAS8C,gBAAgB7C,SAAS;AAChC,MAAIqC,MAAM5G,SAASuE,OAAO,KAAKA,QAAQpE,aAAa,GAAG;AACrD,WAAO;;AAiBT,MAAM1B,MAAMmI,MAAM1G,YAAYqE,OAAO;AACrC,MAAM5F,OAAOF,IAAIE;AACjB,MAAI6F;AACJ,MAAI6C,gBAAgBT,MAAM1I,IAAIqG,SAAS,UAAU;AACjD,MAAM+C,aAAaD,kBAAkB,WAAWA,kBAAkB;AAElE,MAAI,CAACC,YAAY;AACf,WAAO/C,QAAQgD,SAASC,YAAW,MAAO,SACtC,OACAlD,WAAUC,OAAO;;AAGvB,OACEC,SAASF,WAAUC,OAAO,GAC1BC,UAAUA,WAAW7F,QAAQ6F,OAAOrE,aAAa,GACjDqE,SAASF,WAAUE,MAAM,GACzB;AACA6C,oBAAgBT,MAAM1I,IAAIsG,QAAQ,UAAU;AAC5C,QAAI6C,kBAAkB,UAAU;AAC9B,aAAO7C;;;AAGX,SAAO;AACT;AC/CA,IAAQF,cAAcsC,MAAdtC;AAEO,SAASmD,gBAAgBlD,SAAS;AAC/C,MAAIqC,MAAM5G,SAASuE,OAAO,KAAKA,QAAQpE,aAAa,GAAG;AACrD,WAAO;;AAGT,MAAM1B,MAAMmI,MAAM1G,YAAYqE,OAAO;AACrC,MAAM5F,OAAOF,IAAIE;AACjB,MAAI6F,SAAS;AACb;IACEA,SAASF,YAAUC,OAAO;;IAE1BC,UAAUA,WAAW7F,QAAQ6F,WAAW/F;IACxC+F,SAASF,YAAUE,MAAM;IACzB;AACA,QAAM6C,gBAAgBT,MAAM1I,IAAIsG,QAAQ,UAAU;AAClD,QAAI6C,kBAAkB,SAAS;AAC7B,aAAO;;;AAGX,SAAO;AACT;ACjBA,SAASK,yBAAyBnD,SAASoD,kBAAkB;AAC3D,MAAMC,cAAc;IAClB3I,MAAM;IACN4I,OAAOC;IACP5I,KAAK;IACL6I,QAAQD;;AAEV,MAAI3J,KAAKiJ,gBAAgB7C,OAAO;AAChC,MAAM9F,MAAMmI,MAAM1G,YAAYqE,OAAO;AACrC,MAAMM,MAAMpG,IAAIqB,eAAerB,IAAIsB;AACnC,MAAMpB,OAAOF,IAAIE;AACjB,MAAME,kBAAkBJ,IAAII;AAI5B,SAAOV,IAAI;AAET,SACG6J,UAAUC,UAAUC,QAAQ,MAAM,MAAM,MAAM/J,GAAGgK,gBAAgB;;;IAIjEhK,OAAOQ,QACNR,OAAOU,mBACP+H,MAAM1I,IAAIC,IAAI,UAAU,MAAM,WAChC;AACA,UAAM0B,MAAM+G,MAAMlF,OAAOvD,EAAE;AAE3B0B,UAAIZ,QAAQd,GAAGgB;AACfU,UAAIX,OAAOf,GAAGiB;AACdwI,kBAAY1I,MAAMH,KAAK6F,IAAIgD,YAAY1I,KAAKW,IAAIX,GAAG;AACnD0I,kBAAYC,QAAQ9I,KAAKqJ;QACvBR,YAAYC;;QAEZhI,IAAIZ,OAAOd,GAAGgK;MAAW;AAE3BP,kBAAYG,SAAShJ,KAAKqJ,IACxBR,YAAYG,QACZlI,IAAIX,MAAMf,GAAGkK,YAAY;AAE3BT,kBAAY3I,OAAOF,KAAK6F,IAAIgD,YAAY3I,MAAMY,IAAIZ,IAAI;eAC7Cd,OAAOQ,QAAQR,OAAOU,iBAAiB;AAChD;;AAEFV,SAAKiJ,gBAAgBjJ,EAAE;;AAMzB,MAAImK,mBAAmB;AACvB,MAAI,CAAC1B,MAAM5G,SAASuE,OAAO,KAAKA,QAAQpE,aAAa,GAAG;AACtDmI,uBAAmB/D,QAAQjJ,MAAMqG;AACjC,QAAMA,WAAWiF,MAAM1I,IAAIqG,SAAS,UAAU;AAC9C,QAAI5C,aAAa,YAAY;AAC3B4C,cAAQjJ,MAAMqG,WAAW;;;AAI7B,MAAM4G,UAAU3B,MAAMK,oBAAoBpC,GAAG;AAC7C,MAAM2D,UAAU5B,MAAMM,mBAAmBrC,GAAG;AAC5C,MAAMM,gBAAgByB,MAAMzB,cAAcN,GAAG;AAC7C,MAAMO,iBAAiBwB,MAAMxB,eAAeP,GAAG;AAC/C,MAAI4D,gBAAgB5J,gBAAgB6J;AACpC,MAAIC,iBAAiB9J,gBAAgB+J;AAIrC,MAAMC,YAAYvM,OAAOC,iBAAiBoC,IAAI;AAC9C,MAAIkK,UAAUC,cAAc,UAAU;AACpCL,oBAAgB5D,IAAIkE;;AAEtB,MAAIF,UAAUG,cAAc,UAAU;AACpCL,qBAAiB9D,IAAIoE;;AAIvB,MAAI1E,QAAQjJ,OAAO;AACjBiJ,YAAQjJ,MAAMqG,WAAW2G;;AAG3B,MAAIX,oBAAoBF,gBAAgBlD,OAAO,GAAG;AAEhDqD,gBAAY3I,OAAOF,KAAK6F,IAAIgD,YAAY3I,MAAMsJ,OAAO;AACrDX,gBAAY1I,MAAMH,KAAK6F,IAAIgD,YAAY1I,KAAKsJ,OAAO;AACnDZ,gBAAYC,QAAQ9I,KAAKqJ,IAAIR,YAAYC,OAAOU,UAAUpD,aAAa;AACvEyC,gBAAYG,SAAShJ,KAAKqJ,IAAIR,YAAYG,QAAQS,UAAUpD,cAAc;SACrE;AAEL,QAAM8D,kBAAkBnK,KAAK6F,IAAI6D,eAAeF,UAAUpD,aAAa;AACvEyC,gBAAYC,QAAQ9I,KAAKqJ,IAAIR,YAAYC,OAAOqB,eAAe;AAE/D,QAAMC,mBAAmBpK,KAAK6F,IAAI+D,gBAAgBH,UAAUpD,cAAc;AAC1EwC,gBAAYG,SAAShJ,KAAKqJ,IAAIR,YAAYG,QAAQoB,gBAAgB;;AAGpE,SAAOvB,YAAY1I,OAAO,KACxB0I,YAAY3I,QAAQ,KACpB2I,YAAYG,SAASH,YAAY1I,OACjC0I,YAAYC,QAAQD,YAAY3I,OAC9B2I,cACA;AACN;AC3GA,SAASwB,kBAAkBC,aAAaC,UAAU1B,aAAaZ,UAAU;AACvE,MAAMnH,MAAM+G,MAAMG,MAAMsC,WAAW;AACnC,MAAME,OAAO;IACX/D,OAAO8D,SAAS9D;IAChBC,QAAQ6D,SAAS7D;;AAGnB,MAAIuB,SAASwC,WAAW3J,IAAIZ,OAAO2I,YAAY3I,MAAM;AACnDY,QAAIZ,OAAO2I,YAAY3I;;AAIzB,MACE+H,SAASyC,eACT5J,IAAIZ,QAAQ2I,YAAY3I,QACxBY,IAAIZ,OAAOsK,KAAK/D,QAAQoC,YAAYC,OACpC;AACA0B,SAAK/D,SAAS3F,IAAIZ,OAAOsK,KAAK/D,QAAQoC,YAAYC;;AAIpD,MAAIb,SAASwC,WAAW3J,IAAIZ,OAAOsK,KAAK/D,QAAQoC,YAAYC,OAAO;AAEjEhI,QAAIZ,OAAOF,KAAK6F,IAAIgD,YAAYC,QAAQ0B,KAAK/D,OAAOoC,YAAY3I,IAAI;;AAItE,MAAI+H,SAAS0C,WAAW7J,IAAIX,MAAM0I,YAAY1I,KAAK;AACjDW,QAAIX,MAAM0I,YAAY1I;;AAIxB,MACE8H,SAAS2C,gBACT9J,IAAIX,OAAO0I,YAAY1I,OACvBW,IAAIX,MAAMqK,KAAK9D,SAASmC,YAAYG,QACpC;AACAwB,SAAK9D,UAAU5F,IAAIX,MAAMqK,KAAK9D,SAASmC,YAAYG;;AAIrD,MAAIf,SAAS0C,WAAW7J,IAAIX,MAAMqK,KAAK9D,SAASmC,YAAYG,QAAQ;AAElElI,QAAIX,MAAMH,KAAK6F,IAAIgD,YAAYG,SAASwB,KAAK9D,QAAQmC,YAAY1I,GAAG;;AAGtE,SAAO0H,MAAMH,IAAI5G,KAAK0J,IAAI;AAC5B;AC/CA,SAASK,UAAU9N,MAAM;AACvB,MAAI4F;AACJ,MAAIpC;AACJ,MAAIuK;AACJ,MAAI,CAACjD,MAAM5G,SAASlE,IAAI,KAAKA,KAAKqE,aAAa,GAAG;AAChDuB,IAAAA,UAASkF,MAAMlF,OAAO5F,IAAI;AAC1BwD,QAAIsH,MAAMkD,WAAWhO,IAAI;AACzB+N,QAAIjD,MAAMmD,YAAYjO,IAAI;SACrB;AACL,QAAM+I,MAAM+B,MAAMC,UAAU/K,IAAI;AAChC4F,IAAAA,UAAS;MACPzC,MAAM2H,MAAMK,oBAAoBpC,GAAG;MACnC3F,KAAK0H,MAAMM,mBAAmBrC,GAAG;;AAEnCvF,QAAIsH,MAAMzB,cAAcN,GAAG;AAC3BgF,QAAIjD,MAAMxB,eAAeP,GAAG;;AAE9BnD,EAAAA,QAAO8D,QAAQlG;AACfoC,EAAAA,QAAO+D,SAASoE;AAChB,SAAOnI;AACT;AClBA,SAASsI,eAAeC,QAAQC,OAAO;AACrC,MAAMC,IAAID,MAAM5D,OAAO,CAAC;AACxB,MAAM8D,IAAIF,MAAM5D,OAAO,CAAC;AACxB,MAAMhH,IAAI2K,OAAOzE;AACjB,MAAMqE,IAAII,OAAOxE;AAEjB,MAAI7I,IAAIqN,OAAOhL;AACf,MAAInC,IAAImN,OAAO/K;AAEf,MAAIiL,MAAM,KAAK;AACbrN,SAAK+M,IAAI;aACAM,MAAM,KAAK;AACpBrN,SAAK+M;;AAGP,MAAIO,MAAM,KAAK;AACbxN,SAAK0C,IAAI;aACA8K,MAAM,KAAK;AACpBxN,SAAK0C;;AAGP,SAAO;IACLL,MAAMrC;IACNsC,KAAKpC;;AAET;AC3BA,SAASuN,eAAef,UAAUgB,eAAeC,QAAQ7I,SAAQ8I,cAAc;AAC7E,MAAMC,KAAKT,eAAeM,eAAeC,OAAO,CAAC,CAAC;AAClD,MAAMG,KAAKV,eAAeV,UAAUiB,OAAO,CAAC,CAAC;AAC7C,MAAMI,OAAO,CAACD,GAAGzL,OAAOwL,GAAGxL,MAAMyL,GAAGxL,MAAMuL,GAAGvL,GAAG;AAEhD,SAAO;IACLD,MAAMF,KAAK6L,MAAMtB,SAASrK,OAAO0L,KAAK,CAAC,IAAIjJ,QAAO,CAAC,IAAI8I,aAAa,CAAC,CAAC;IACtEtL,KAAKH,KAAK6L,MAAMtB,SAASpK,MAAMyL,KAAK,CAAC,IAAIjJ,QAAO,CAAC,IAAI8I,aAAa,CAAC,CAAC;;AAExE;ACEA,SAASK,QAAQxB,aAAaC,UAAU1B,aAAa;AACnD,SACEyB,YAAYpK,OAAO2I,YAAY3I,QAC/BoK,YAAYpK,OAAOqK,SAAS9D,QAAQoC,YAAYC;AAEpD;AAEA,SAASiD,QAAQzB,aAAaC,UAAU1B,aAAa;AACnD,SACEyB,YAAYnK,MAAM0I,YAAY1I,OAC9BmK,YAAYnK,MAAMoK,SAAS7D,SAASmC,YAAYG;AAEpD;AAEA,SAASgD,gBAAgB1B,aAAaC,UAAU1B,aAAa;AAC3D,SACEyB,YAAYpK,OAAO2I,YAAYC,SAC/BwB,YAAYpK,OAAOqK,SAAS9D,QAAQoC,YAAY3I;AAEpD;AAEA,SAAS+L,gBAAgB3B,aAAaC,UAAU1B,aAAa;AAC3D,SACEyB,YAAYnK,MAAM0I,YAAYG,UAC9BsB,YAAYnK,MAAMoK,SAAS7D,SAASmC,YAAY1I;AAEpD;AAEA,SAAS+L,KAAKV,QAAQW,KAAK5N,KAAK;AAC9B,MAAMiC,MAAM,CAAA;AACZqH,QAAM1D,KAAKqH,QAAQ,SAAAY,GAAK;AACtB5L,QAAI6L,KACFD,EAAEzO,QAAQwO,KAAK,SAAAG,GAAK;AAClB,aAAO/N,IAAI+N,CAAC;KACb,CAAC;GAEL;AACD,SAAO9L;AACT;AAEA,SAAS+L,WAAW5J,SAAQ6J,OAAO;AACjC7J,EAAAA,QAAO6J,KAAK,IAAI,CAAC7J,QAAO6J,KAAK;AAC7B,SAAO7J;AACT;AAEA,SAAS8J,cAAcC,KAAKC,WAAW;AACrC,MAAIC;AACJ,MAAI,KAAK3K,KAAKyK,GAAG,GAAG;AAClBE,QAAKC,SAASH,IAAII,UAAU,GAAGJ,IAAIrI,SAAS,CAAC,GAAG,EAAE,IAAI,MAAOsI;SACxD;AACLC,QAAIC,SAASH,KAAK,EAAE;;AAEtB,SAAOE,KAAK;AACd;AAEA,SAASG,gBAAgBpK,SAAQvD,IAAI;AACnCuD,EAAAA,QAAO,CAAC,IAAI8J,cAAc9J,QAAO,CAAC,GAAGvD,GAAGqH,KAAK;AAC7C9D,EAAAA,QAAO,CAAC,IAAI8J,cAAc9J,QAAO,CAAC,GAAGvD,GAAGsH,MAAM;AAChD;AAOA,SAASsG,QAAQ5N,IAAI6N,WAAW9B,OAAO+B,oBAAoB;AACzD,MAAI1B,SAASL,MAAMK;AACnB,MAAI7I,UAASwI,MAAMxI,UAAU,CAAC,GAAG,CAAC;AAClC,MAAI8I,eAAeN,MAAMM,gBAAgB,CAAC,GAAG,CAAC;AAC9C,MAAIxD,WAAWkD,MAAMlD;AACrB,MAAMrJ,SAASuM,MAAMvM,UAAUQ;AAC/BuD,EAAAA,UAAS,CAAA,EAAGwK,OAAOxK,OAAM;AACzB8I,iBAAe,CAAA,EAAG0B,OAAO1B,YAAY;AACrCxD,aAAWA,YAAY,CAAA;AACvB,MAAMmF,iBAAiB,CAAA;AACvB,MAAIC,OAAO;AACX,MAAMzE,mBAAmB,CAAC,EAAEX,YAAYA,SAASW;AAEjD,MAAMC,cAAcF,yBAAyB/J,QAAQgK,gBAAgB;AAErE,MAAM2B,WAAWM,UAAUjM,MAAM;AAEjCmO,kBAAgBpK,SAAQ4H,QAAQ;AAChCwC,kBAAgBtB,cAAcwB,SAAS;AAEvC,MAAI3C,cAAcgB,eAChBf,UACA0C,WACAzB,QACA7I,SACA8I,YAAY;AAGd,MAAI6B,cAAczF,MAAMO,MAAMmC,UAAUD,WAAW;AAGnD,MACEzB,gBACCZ,SAASwC,WAAWxC,SAAS0C,YAC9BuC,oBACA;AACA,QAAIjF,SAASwC,SAAS;AAEpB,UAAIqB,QAAQxB,aAAaC,UAAU1B,WAAW,GAAG;AAE/C,YAAM0E,YAAYrB,KAAKV,QAAQ,UAAU;UACvCgC,GAAG;UACHC,GAAG;SACJ;AAED,YAAMC,YAAYnB,WAAW5J,SAAQ,CAAC;AACtC,YAAMgL,kBAAkBpB,WAAWd,cAAc,CAAC;AAClD,YAAMmC,iBAAiBtC,eACrBf,UACA0C,WACAM,WACAG,WACAC,eAAe;AAGjB,YAAI,CAAC3B,gBAAgB4B,gBAAgBrD,UAAU1B,WAAW,GAAG;AAC3DwE,iBAAO;AACP7B,mBAAS+B;AACT5K,UAAAA,UAAS+K;AACTjC,yBAAekC;;;;AAKrB,QAAI1F,SAAS0C,SAAS;AAEpB,UAAIoB,QAAQzB,aAAaC,UAAU1B,WAAW,GAAG;AAE/C,YAAM0E,aAAYrB,KAAKV,QAAQ,UAAU;UACvCqC,GAAG;UACHC,GAAG;SACJ;AAED,YAAMJ,aAAYnB,WAAW5J,SAAQ,CAAC;AACtC,YAAMgL,mBAAkBpB,WAAWd,cAAc,CAAC;AAClD,YAAMmC,kBAAiBtC,eACrBf,UACA0C,WACAM,YACAG,YACAC,gBAAe;AAGjB,YAAI,CAAC1B,gBAAgB2B,iBAAgBrD,UAAU1B,WAAW,GAAG;AAC3DwE,iBAAO;AACP7B,mBAAS+B;AACT5K,UAAAA,UAAS+K;AACTjC,yBAAekC;;;;AAMrB,QAAIN,MAAM;AACR/C,oBAAcgB,eACZf,UACA0C,WACAzB,QACA7I,SACA8I,YAAY;AAEd5D,YAAMH,IAAI4F,aAAahD,WAAW;;AAEpC,QAAMyD,eAAejC,QAAQxB,aAAaC,UAAU1B,WAAW;AAC/D,QAAMmF,eAAejC,QAAQzB,aAAaC,UAAU1B,WAAW;AAG/D,QAAIkF,gBAAgBC,cAAc;AAChC,UAAIT,cAAY/B;AAGhB,UAAIuC,cAAc;AAChBR,sBAAYrB,KAAKV,QAAQ,UAAU;UACjCgC,GAAG;UACHC,GAAG;SACJ;;AAEH,UAAIO,cAAc;AAChBT,sBAAYrB,KAAKV,QAAQ,UAAU;UACjCqC,GAAG;UACHC,GAAG;SACJ;;AAGHtC,eAAS+B;AAET5K,MAAAA,UAASwI,MAAMxI,UAAU,CAAC,GAAG,CAAC;AAC9B8I,qBAAeN,MAAMM,gBAAgB,CAAC,GAAG,CAAC;;AAG5C2B,mBAAe3C,UAAUxC,SAASwC,WAAWsD;AAC7CX,mBAAezC,UAAU1C,SAAS0C,WAAWqD;AAG7C,QAAIZ,eAAe3C,WAAW2C,eAAezC,SAAS;AACpD2C,oBAAcjD,kBACZC,aACAC,UACA1B,aACAuE,cAAc;;;AAMpB,MAAIE,YAAY7G,UAAU8D,SAAS9D,OAAO;AACxCoB,UAAM1I,IACJP,QACA,SACAiJ,MAAMpB,MAAM7H,MAAM,IAAI0O,YAAY7G,QAAQ8D,SAAS9D,KAAK;;AAI5D,MAAI6G,YAAY5G,WAAW6D,SAAS7D,QAAQ;AAC1CmB,UAAM1I,IACJP,QACA,UACAiJ,MAAMnB,OAAO9H,MAAM,IAAI0O,YAAY5G,SAAS6D,SAAS7D,MAAM;;AAO/DmB,QAAMlF,OACJ/D,QACA;IACEsB,MAAMoN,YAAYpN;IAClBC,KAAKmN,YAAYnN;KAEnB;IACEoC,aAAa4I,MAAM5I;IACnBC,cAAc2I,MAAM3I;IACpB0B,iBAAiBiH,MAAMjH;IACvBP,aAAawH,MAAMxH;GACpB;AAGH,SAAO;IACL6H;IACA7I,QAAAA;IACA8I;IACAxD,UAAUmF;;AAEd;ACjQA,SAASa,mBAAmBC,QAAQtF,kBAAkB;AACpD,MAAMC,cAAcF,yBAAyBuF,QAAQtF,gBAAgB;AACrE,MAAMuF,eAAetD,UAAUqD,MAAM;AAErC,SACE,CAACrF,eACDsF,aAAajO,OAAOiO,aAAa1H,SAASoC,YAAY3I,QACtDiO,aAAahO,MAAMgO,aAAazH,UAAUmC,YAAY1I,OACtDgO,aAAajO,QAAQ2I,YAAYC,SACjCqF,aAAahO,OAAO0I,YAAYG;AAEpC;AAEA,SAASoF,aAAahP,IAAIiP,SAASlD,OAAO;AACxC,MAAM+C,SAAS/C,MAAM+C,UAAUG;AAC/B,MAAM9C,gBAAgBV,UAAUqD,MAAM;AAEtC,MAAMI,0BAA0B,CAACL,mBAC/BC,QACA/C,MAAMlD,YAAYkD,MAAMlD,SAASW,gBAAgB;AAGnD,SAAOoE,QAAQ5N,IAAImM,eAAeJ,OAAOmD,uBAAuB;AAClE;AAEAF,aAAaG,oBAAoBlG;AAEjC+F,aAAaI,6BAA6B7F;ACxB1C,SAAS8F,WAAWrP,IAAIsP,UAAUvD,OAAO;AACvC,MAAIwD;AACJ,MAAIC;AAEJ,MAAMlP,MAAMmI,MAAM1G,YAAY/B,EAAE;AAChC,MAAM0G,MAAMpG,IAAIqB,eAAerB,IAAIsB;AAEnC,MAAMwI,UAAU3B,MAAMK,oBAAoBpC,GAAG;AAC7C,MAAM2D,UAAU5B,MAAMM,mBAAmBrC,GAAG;AAC5C,MAAMM,gBAAgByB,MAAMzB,cAAcN,GAAG;AAC7C,MAAMO,iBAAiBwB,MAAMxB,eAAeP,GAAG;AAE/C,MAAI,WAAW4I,UAAU;AACvBC,YAAQD,SAASC;SACZ;AACLA,YAAQnF,UAAUkF,SAASG;;AAG7B,MAAI,WAAWH,UAAU;AACvBE,YAAQF,SAASE;SACZ;AACLA,YAAQnF,UAAUiF,SAASI;;AAG7B,MAAM7B,YAAY;IAChB/M,MAAMyO;IACNxO,KAAKyO;IACLnI,OAAO;IACPC,QAAQ;;AAGV,MAAMqI,cACJJ,SAAS,KACTA,SAASnF,UAAUpD,iBAClBwI,SAAS,KAAKA,SAASnF,UAAUpD;AAGpC,MAAMmF,SAAS,CAACL,MAAMK,OAAO,CAAC,GAAG,IAAI;AAErC,SAAOwB,QAAQ5N,IAAI6N,WAAS,eAAA,eAAA,CAAA,GAAO9B,KAAK,GAAA,CAAA,GAAA;IAAEK;MAAUuD,WAAW;AACjE;;", "names": ["vendorPrefix", "jsCssMap", "Webkit", "<PERSON><PERSON>", "ms", "O", "getVendorPrefix", "undefined", "style", "document", "createElement", "testProp", "key", "getTransitionName", "getTransformName", "setTransitionProperty", "node", "value", "name", "transitionProperty", "setTransform", "transform", "getTransitionProperty", "getTransformXY", "window", "getComputedStyle", "getPropertyValue", "matrix", "replace", "split", "x", "parseFloat", "y", "matrix2d", "matrix3d", "setTransformXY", "xy", "arr", "match2d", "match", "map", "item", "join", "match3d", "RE_NUM", "source", "getComputedStyleX", "forceRelayout", "elem", "originalStyle", "display", "offsetHeight", "css", "el", "v", "i", "hasOwnProperty", "getClientPosition", "box", "doc", "ownerDocument", "body", "doc<PERSON><PERSON>", "documentElement", "getBoundingClientRect", "Math", "floor", "left", "top", "clientLeft", "clientTop", "getScroll", "w", "ret", "method", "d", "getScrollLeft", "getScrollTop", "getOffset", "pos", "defaultView", "parentWindow", "isWindow", "obj", "getDocument", "nodeType", "_getComputedStyle", "cs", "computedStyle", "val", "_RE_NUM_NO_PX", "RegExp", "RE_POS", "CURRENT_STYLE", "RUNTIME_STYLE", "LEFT", "PX", "_getComputedStyleIE", "test", "rsLeft", "pixelLeft", "getOffsetDirection", "dir", "option", "useCssRight", "useCssBottom", "oppositeOffsetDirection", "setLeftTop", "offset", "position", "presetH", "presetV", "horizontalProperty", "verticalProperty", "oppositeHorizontalProperty", "oppositeVerticalProperty", "originalTransition", "originalOffset", "old", "preset", "off", "originalXY", "resultXY", "setOffset", "ignoreShake", "oriOffset", "oLeft", "toFixed", "oTop", "tLeft", "tTop", "useCssTransform", "each", "fn", "length", "isBorderBoxFn", "BOX_MODELS", "CONTENT_INDEX", "PADDING_INDEX", "BORDER_INDEX", "MARGIN_INDEX", "swap", "options", "callback", "call", "getPBMWidth", "props", "which", "prop", "j", "cssProp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getParent", "element", "parent", "host", "parentNode", "refWin", "max", "win", "documentElementProp", "compatMode", "getWH", "ex", "extra", "viewportWidth", "viewportHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doc<PERSON><PERSON>ght", "borderBoxValue", "width", "height", "isBorderBox", "cssBoxValue", "Number", "borderBoxValueOrIsBorderBox", "slice", "cssShow", "visibility", "getWHIgnoreDisplay", "args", "offsetWidth", "apply", "first", "char<PERSON>t", "toUpperCase", "<PERSON><PERSON><PERSON><PERSON>", "mix", "to", "from", "utils", "getWindow", "setTimeout", "clone", "overflow", "getWindowScrollLeft", "getWindowScrollTop", "merge", "getOffsetParent", "positionStyle", "skipStatic", "nodeName", "toLowerCase", "isAncestorFixed", "getVisibleRectForElement", "alwaysByViewport", "visibleRect", "right", "Infinity", "bottom", "navigator", "userAgent", "indexOf", "clientWidth", "min", "clientHeight", "originalPosition", "scrollX", "scrollY", "documentWidth", "scrollWidth", "documentHeight", "scrollHeight", "bodyStyle", "overflowX", "innerWidth", "overflowY", "innerHeight", "maxVisibleWidth", "maxVisibleHeight", "adjustForViewport", "elFuturePos", "elRegion", "size", "adjustX", "resizeWidth", "adjustY", "resizeHeight", "getRegion", "h", "outerWidth", "outerHeight", "getAlignOffset", "region", "align", "V", "H", "getElFuturePos", "refNodeRegion", "points", "targetOffset", "p1", "p2", "diff", "round", "isFailX", "isFailY", "isCompleteFailX", "isCompleteFailY", "flip", "reg", "p", "push", "m", "flipOffset", "index", "convertOffset", "str", "offsetLen", "n", "parseInt", "substring", "normalizeOffset", "doAlign", "tgtRegion", "isTgtRegionVisible", "concat", "newOverflowCfg", "fail", "newElRegion", "newPoints", "l", "r", "newOffset", "newTargetOffset", "newElFuturePos", "t", "b", "isStillFailX", "isStillFailY", "isOutOfVisibleRect", "target", "targetRegion", "alignElement", "refNode", "isTargetNotOutOfVisible", "__getOffsetParent", "__getVisibleRectForElement", "alignPoint", "tgtPoint", "pageX", "pageY", "clientX", "clientY", "pointInView"]}