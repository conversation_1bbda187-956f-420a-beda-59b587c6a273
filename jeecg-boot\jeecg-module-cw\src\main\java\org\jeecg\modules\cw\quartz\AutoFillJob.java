package org.jeecg.modules.cw.quartz;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;
import lombok.extern.log4j.Log4j2;
import org.jeecg.modules.cw.ckc.entity.CwCkcDay;
import org.jeecg.modules.cw.ckc.service.ICwCkcDayService;
import org.jeecg.modules.cw.ckc.service.ICwCkcZhcbService;
import org.jeecg.modules.cw.ds.entity.CwDsDay;
import org.jeecg.modules.cw.ds.service.ICwDsDayService;
import org.jeecg.modules.cw.ds.service.ICwDsZhcbService;
import org.jeecg.modules.cw.entity.BaseDayEntity;
import org.jeecg.modules.cw.frdw.entity.CwFrdwDay;
import org.jeecg.modules.cw.frdw.service.ICwFrdwDayService;
import org.jeecg.modules.cw.jg.entity.CwJgDay;
import org.jeecg.modules.cw.jg.service.ICwJgDayService;
import org.jeecg.modules.cw.jh.entity.CwJhDay;
import org.jeecg.modules.cw.jh.service.ICwJhDayService;
import org.jeecg.modules.cw.jw.entity.CwJwDay;
import org.jeecg.modules.cw.jw.service.ICwJwDayService;
import org.jeecg.modules.cw.jw.service.ICwJwZhcbService;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrStatisticsDayService;
import org.jeecg.modules.cw.qtfy.entity.CwQtfy;
import org.jeecg.modules.cw.qtfy.service.ICwQtfyService;
import org.jeecg.modules.cw.sx.entity.CwSxDay;
import org.jeecg.modules.cw.sx.service.ICwSxDayService;
import org.jeecg.modules.cw.sx.service.ICwSxZhcbService;
import org.jeecg.modules.cw.xjs.entity.CwXjsDay;
import org.jeecg.modules.cw.xjs.service.ICwXjsDayService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Log4j2
@Service
public class AutoFillJob implements Job {
    @Resource
    private ICwCkcDayService ckcDayService;
    @Resource
    private ICwDsDayService dsDayService;
    @Resource
    private ICwSxDayService sxDayService;
    @Resource
    private ICwJwDayService jwDayService;
    @Resource
    private ICwXjsDayService xjsDayService;
    @Resource
    private ICwJhDayService jhDayService;
    @Resource
    private ICwJgDayService jgDayService;
    @Resource
    private ICwQtfyService qtfyService;
    @Resource
    private ICwFrdwDayService frdwDayService;
    @Resource
    private ICwCkcZhcbService ckcZhcbService;
    @Resource
    private ICwDsZhcbService dsZhcbService;
    @Resource
    private ICwSxZhcbService sxZhcbService;
    @Resource
    private ICwJwZhcbService jwZhcbService;
    @Resource
    private ICwMnlrStatisticsDayService statisticsDayService;

    @Override
    public void execute(JobExecutionContext ctx) throws JobExecutionException {
        Date today = DateUtil.beginOfDay(new Date());
        log.info("开始执行自动填充数据任务，目标日期：{}", DateUtil.formatDate(today));

        // ckc, ds, sx, jw: 循环判断前一天填充后重新计算drs
        fillDataFromYesterday(ckcDayService, CwCkcDay::getRecordTime, today, "采矿场");
        ckcZhcbService.recalculateDrsByDate(today);
        log.info("采矿场: 已完成填充并调用recalculateDrsByDate");
        fillDataFromYesterday(dsDayService, CwDsDay::getRecordTime, today, "大山");
        dsZhcbService.recalculateDrsByDate(today);
        log.info("大山: 已完成填充并调用recalculateDrsByDate");
        fillDataFromYesterday(sxDayService, CwSxDay::getRecordTime, today, "泗选");
        sxZhcbService.recalculateDrsByDate(today);
        log.info("泗选: 已完成填充并调用recalculateDrsByDate");
        fillDataFromYesterday(jwDayService, CwJwDay::getRecordTime, today, "精尾");
        jwZhcbService.recalculateDrsByDate(today);
        log.info("精尾: 已完成填充并调用recalculateDrsByDate");

        // xjs, jh: 2-14号用1号填充，16-30用15号填充
        fillDataForXjsAndJh(xjsDayService, CwXjsDay::getRecordTime, today, "新技术");
        fillDataForXjsAndJh(jhDayService, CwJhDay::getRecordTime, today, "检化");

        // jg: 全部用1号填
        fillDataForJg(jgDayService, CwJgDay::getRecordTime, today, "机关");

        // qtfy, frdw: 循环判断前一天填充
        fillDataFromYesterday(qtfyService, CwQtfy::getRecordTime, today, "其他费用");
        fillDataFromYesterday(frdwDayService, CwFrdwDay::getRecordTime, today, "法人单位");

        log.info("自动填充数据任务执行完毕");

        log.info("开始执行自动计算利润任务，目标日期：{}", DateUtil.formatDate(today));
        statisticsDayService.recalcDay(today);
        log.info("利润计算任务执行完毕");
    }

    /**
     * xjs和jh填充方法
     * 2-14号用1号填充，16-30用15号填充
     */
    private <T extends BaseDayEntity> void fillDataForXjsAndJh(IService<T> service, SFunction<T, ?> recordTimeFunc, Date today, String logPrefix) {
        // 如果是最后一天，不填充
        if (DateUtil.isLastDayOfMonth(today)) {
            log.info("{}: 今天是本月最后一天，跳过填充。", logPrefix);
            return;
        }
        // 1. 为防止数据重复，先删除今天已存在的数据
        boolean removed = service.lambdaUpdate().eq(recordTimeFunc, today).remove();
        if (removed) {
            log.info("{}: 检测到今天已有数据，已执行删除操作以防止数据重复。", logPrefix);
        }

        int dayOfMonth = DateUtil.dayOfMonth(today);
        Date dateToCopyFrom = null;
        if (dayOfMonth >= 2 && dayOfMonth <= 14) {
            // 2-14号，用1号填充
            dateToCopyFrom = DateUtil.beginOfMonth(today);
        } else if (dayOfMonth >= 16 && dayOfMonth <= 30) {
            // 16-30号，用15号填充
            dateToCopyFrom = DateUtil.date(today).setField(DateField.DAY_OF_MONTH, 15).toJdkDate();
        }

        if (dateToCopyFrom != null) {
            copyData(service, recordTimeFunc, dateToCopyFrom, today, logPrefix);
        } else {
            log.info("{}: {} 无需填充", logPrefix, DateUtil.formatDate(today));
        }
    }

    /**
     * jg填充方法
     * 全部用1号填充
     */
    private <T extends BaseDayEntity> void fillDataForJg(IService<T> service, SFunction<T, ?> recordTimeFunc, Date today, String logPrefix) {
        // 1号不填充自己
        if (DateUtil.dayOfMonth(today) == 1) {
            log.info("{}: 今天是1号，跳过填充。", logPrefix);
            return;
        }
        // 1. 为防止数据重复，先删除今天已存在的数据
        boolean removed = service.lambdaUpdate().eq(recordTimeFunc, today).remove();
        if (removed) {
            log.info("{}: 检测到今天已有数据，已执行删除操作以防止数据重复。", logPrefix);
        }
        Date dateToCopyFrom = DateUtil.beginOfMonth(today);
        copyData(service, recordTimeFunc, dateToCopyFrom, today, logPrefix);
    }

    /**
     * 通用填充方法 - 从昨天往前找到最近的数据填充
     *
     * @param service        对应的服务
     * @param recordTimeFunc 获取记录时间的方法引用
     * @param today          填充的目标日期
     * @param logPrefix      日志前缀
     * @param <T>            实体类型，必须是 BaseDayEntity 的子类
     */
    private <T extends BaseDayEntity> void fillDataFromYesterday(IService<T> service, SFunction<T, ?> recordTimeFunc, Date today, String logPrefix) {
        // 1. 为防止数据重复，先删除今天已存在的数据
        boolean removed = service.lambdaUpdate().eq(recordTimeFunc, today).remove();
        if (removed) {
            log.info("{}: 检测到今天已有数据，已执行删除操作以防止数据重复。", logPrefix);
        }

        // 2. 从昨天开始，到本月1号为止，倒序查找可复制的数据源
        List<T> dataToCopy = null;
        Date copyDate = null;
        Date firstDayOfMonth = DateUtil.beginOfMonth(today);

        for (Date dateToTry = DateUtil.offsetDay(today, -1); !dateToTry.before(firstDayOfMonth); dateToTry = DateUtil.offsetDay(dateToTry, -1)) {
            List<T> potentialData = service.lambdaQuery()
                    .between(recordTimeFunc, DateUtil.beginOfDay(dateToTry), DateUtil.endOfDay(dateToTry))
                    .list();
            if (ObjectUtil.isNotEmpty(potentialData)) {
                dataToCopy = potentialData;
                copyDate = dateToTry;
                break; // 找到数据，中断循环
            }
        }

        // 3. 如果找到了数据，则进行复制
        if (ObjectUtil.isNotEmpty(dataToCopy)) {
            final Date finalCopyDate = copyDate;
            dataToCopy.forEach(item -> {
                item.resetForCopy(); // 重置ID、创建信息等
                item.setRecordTime(today);
            });
            service.saveBatch(dataToCopy);
            log.info("{}: 成功从 {} 复制 {} 条数据到今天。", logPrefix, DateUtil.formatDate(finalCopyDate), dataToCopy.size());
        } else {
            log.info("{}: 未能在本月（{}~{}）找到可供复制的数据。", logPrefix, DateUtil.formatDate(firstDayOfMonth), DateUtil.formatDate(DateUtil.offsetDay(today, -1)));
        }
    }

    private <T extends BaseDayEntity> void copyData(IService<T> service, SFunction<T, ?> recordTimeFunc, Date copyFrom, Date copyTo, String logPrefix) {
        List<T> dataToCopy = service.lambdaQuery()
                .between(recordTimeFunc, DateUtil.beginOfDay(copyFrom), DateUtil.endOfDay(copyFrom))
                .list();
        if (ObjectUtil.isNotEmpty(dataToCopy)) {
            dataToCopy.forEach(item -> {
                item.resetForCopy(); // 重置ID、创建信息等
                item.setRecordTime(copyTo);
            });
            service.saveBatch(dataToCopy);
            log.info("{}: 成功从 {} 复制 {} 条数据到 {}.", logPrefix, DateUtil.formatDate(copyFrom), dataToCopy.size(), DateUtil.formatDate(copyTo));
        } else {
            log.info("{}: 未能在 {} 找到可供复制的数据。", logPrefix, DateUtil.formatDate(copyFrom));
        }
    }
}
